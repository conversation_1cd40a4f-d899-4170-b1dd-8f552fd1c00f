#!/usr/bin/env python3
"""
Final Validation Test
Comprehensive validation of corrected billing implementation
"""

import sys
import os
from datetime import date, datetime
import psycopg2
from psycopg2.extras import RealDictCursor

# Add project root to path
sys.path.append('/home/<USER>/solar-prediction-project')

# Import standardized API
sys.path.append('/home/<USER>/solar-prediction-project/scripts/database')
from standardized_billing_api import get_daily_cost_standardized, get_roi_standardized

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 5433,
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

def get_db_connection():
    """Get database connection"""
    try:
        return psycopg2.connect(**DB_CONFIG)
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return None

def test_billing_fields_accuracy():
    """Test accuracy of corrected billing fields"""
    print("\n" + "="*60)
    print("🧪 TESTING BILLING FIELDS ACCURACY")
    print("="*60)
    
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        
        # Test 1: Check for unrealistic values
        print("\n📊 Test 1: Check for unrealistic values")
        
        cur.execute("""
            SELECT 
                'System 1' as system,
                COUNT(*) as total_records,
                COUNT(CASE WHEN billing_cost > 100 THEN 1 END) as high_cost_records,
                COUNT(CASE WHEN billing_benefit > 100 THEN 1 END) as high_benefit_records,
                ROUND(MAX(COALESCE(billing_cost, 0))::numeric, 4) as max_cost,
                ROUND(MAX(COALESCE(billing_benefit, 0))::numeric, 4) as max_benefit,
                ROUND(AVG(COALESCE(billing_cost, 0))::numeric, 6) as avg_cost,
                ROUND(AVG(COALESCE(billing_benefit, 0))::numeric, 6) as avg_benefit
            FROM solax_data 
            WHERE timestamp >= '2024-06-01'
            
            UNION ALL
            
            SELECT 
                'System 2' as system,
                COUNT(*) as total_records,
                COUNT(CASE WHEN billing_cost > 100 THEN 1 END) as high_cost_records,
                COUNT(CASE WHEN billing_benefit > 100 THEN 1 END) as high_benefit_records,
                ROUND(MAX(COALESCE(billing_cost, 0))::numeric, 4) as max_cost,
                ROUND(MAX(COALESCE(billing_benefit, 0))::numeric, 4) as max_benefit,
                ROUND(AVG(COALESCE(billing_cost, 0))::numeric, 6) as avg_cost,
                ROUND(AVG(COALESCE(billing_benefit, 0))::numeric, 6) as avg_benefit
            FROM solax_data2 
            WHERE timestamp >= '2024-06-01'
        """)
        
        results = cur.fetchall()
        for result in results:
            print(f"   {result['system']}:")
            print(f"      Total records: {result['total_records']:,}")
            print(f"      High cost records (>€100): {result['high_cost_records']:,}")
            print(f"      High benefit records (>€100): {result['high_benefit_records']:,}")
            print(f"      Max cost: €{result['max_cost']}")
            print(f"      Max benefit: €{result['max_benefit']}")
            print(f"      Avg cost: €{result['avg_cost']}")
            print(f"      Avg benefit: €{result['avg_benefit']}")
            
            # Validation
            if result['high_cost_records'] == 0 and result['high_benefit_records'] == 0:
                print(f"      ✅ No unrealistic values found")
            else:
                print(f"      ⚠️ Found unrealistic values - needs investigation")
        
        # Test 2: Coverage statistics
        print("\n📈 Test 2: Coverage statistics")
        
        cur.execute("""
            SELECT 
                'System 1' as system,
                COUNT(*) as total_records,
                COUNT(billing_cost) as has_cost,
                COUNT(billing_benefit) as has_benefit,
                COUNT(billing_schedule) as has_schedule,
                COUNT(billing_tariff) as has_tariff
            FROM solax_data 
            WHERE timestamp >= '2024-06-01'
            
            UNION ALL
            
            SELECT 
                'System 2' as system,
                COUNT(*) as total_records,
                COUNT(billing_cost) as has_cost,
                COUNT(billing_benefit) as has_benefit,
                COUNT(billing_schedule) as has_schedule,
                COUNT(billing_tariff) as has_tariff
            FROM solax_data2 
            WHERE timestamp >= '2024-06-01'
        """)
        
        coverage_results = cur.fetchall()
        for result in coverage_results:
            total = result['total_records']
            cost_pct = (result['has_cost'] / total * 100) if total > 0 else 0
            benefit_pct = (result['has_benefit'] / total * 100) if total > 0 else 0
            schedule_pct = (result['has_schedule'] / total * 100) if total > 0 else 0
            tariff_pct = (result['has_tariff'] / total * 100) if total > 0 else 0
            
            print(f"   {result['system']}:")
            print(f"      Total records: {total:,}")
            print(f"      Billing cost coverage: {cost_pct:.1f}%")
            print(f"      Billing benefit coverage: {benefit_pct:.1f}%")
            print(f"      Billing schedule coverage: {schedule_pct:.1f}%")
            print(f"      Billing tariff coverage: {tariff_pct:.1f}%")
            
            if cost_pct >= 95 and benefit_pct >= 95:
                print(f"      ✅ Excellent coverage")
            elif cost_pct >= 80 and benefit_pct >= 80:
                print(f"      ⚠️ Good coverage but could be improved")
            else:
                print(f"      ❌ Poor coverage - needs attention")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

def test_standardized_api():
    """Test the standardized API"""
    print("\n" + "="*60)
    print("🔧 TESTING STANDARDIZED API")
    print("="*60)
    
    try:
        # Test daily cost for both systems
        print("\n📊 Testing daily cost calculation...")
        
        today = date.today()
        
        # System 1
        system1_cost = get_daily_cost_standardized('system1', today)
        print(f"   System 1 daily cost:")
        print(f"      Status: {system1_cost.get('status', 'unknown')}")
        if system1_cost.get('status') == 'success':
            cost_data = system1_cost.get('cost_breakdown', {})
            energy_data = system1_cost.get('energy_data', {})
            print(f"      Total cost: €{cost_data.get('total_cost', 0)}")
            print(f"      Total benefit: €{cost_data.get('total_benefit', 0)}")
            print(f"      Net cost: €{cost_data.get('net_cost', 0)}")
            print(f"      Production: {energy_data.get('production', 0)} kWh")
            print(f"      Records processed: {system1_cost.get('records_processed', 0)}")
            print(f"      ✅ System 1 API working")
        else:
            print(f"      ❌ System 1 API failed: {system1_cost.get('error', 'unknown')}")
        
        # System 2
        system2_cost = get_daily_cost_standardized('system2', today)
        print(f"   System 2 daily cost:")
        print(f"      Status: {system2_cost.get('status', 'unknown')}")
        if system2_cost.get('status') == 'success':
            cost_data = system2_cost.get('cost_breakdown', {})
            energy_data = system2_cost.get('energy_data', {})
            print(f"      Total cost: €{cost_data.get('total_cost', 0)}")
            print(f"      Total benefit: €{cost_data.get('total_benefit', 0)}")
            print(f"      Net cost: €{cost_data.get('net_cost', 0)}")
            print(f"      Production: {energy_data.get('production', 0)} kWh")
            print(f"      Records processed: {system2_cost.get('records_processed', 0)}")
            print(f"      ✅ System 2 API working")
        else:
            print(f"      ❌ System 2 API failed: {system2_cost.get('error', 'unknown')}")
        
        # Test ROI calculation
        print("\n💰 Testing ROI calculation...")
        
        # System 1 ROI
        system1_roi = get_roi_standardized('system1')
        print(f"   System 1 ROI:")
        print(f"      Status: {system1_roi.get('status', 'unknown')}")
        if system1_roi.get('status') == 'success':
            financial = system1_roi.get('financial', {})
            production = system1_roi.get('production', {})
            print(f"      Annual ROI: {financial.get('annual_roi_percent', 0)}%")
            print(f"      Payback years: {financial.get('payback_years', 'N/A')}")
            print(f"      Annual benefit: €{financial.get('annual_benefit_eur', 0)}")
            print(f"      Annual production: {production.get('annual_production_kwh', 0)} kWh")
            print(f"      ✅ System 1 ROI calculation working")
        else:
            print(f"      ❌ System 1 ROI failed: {system1_roi.get('error', 'unknown')}")
        
        # System 2 ROI
        system2_roi = get_roi_standardized('system2')
        print(f"   System 2 ROI:")
        print(f"      Status: {system2_roi.get('status', 'unknown')}")
        if system2_roi.get('status') == 'success':
            financial = system2_roi.get('financial', {})
            production = system2_roi.get('production', {})
            print(f"      Annual ROI: {financial.get('annual_roi_percent', 0)}%")
            print(f"      Payback years: {financial.get('payback_years', 'N/A')}")
            print(f"      Annual benefit: €{financial.get('annual_benefit_eur', 0)}")
            print(f"      Annual production: {production.get('annual_production_kwh', 0)} kWh")
            print(f"      ✅ System 2 ROI calculation working")
        else:
            print(f"      ❌ System 2 ROI failed: {system2_roi.get('error', 'unknown')}")
        
    except Exception as e:
        print(f"❌ Error testing standardized API: {e}")

def test_triggers_functionality():
    """Test that triggers are working for new records"""
    print("\n" + "="*60)
    print("🔗 TESTING TRIGGERS FUNCTIONALITY")
    print("="*60)
    
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        
        # Check active triggers
        print("\n🔧 Active billing triggers:")
        cur.execute("""
            SELECT 
                trigger_name,
                event_object_table,
                action_timing,
                event_manipulation
            FROM information_schema.triggers 
            WHERE event_object_table IN ('solax_data', 'solax_data2')
                AND trigger_name LIKE '%billing%'
            ORDER BY event_object_table, trigger_name
        """)
        
        triggers = cur.fetchall()
        for trigger in triggers:
            print(f"   {trigger['trigger_name']} on {trigger['event_object_table']}")
            print(f"      Timing: {trigger['action_timing']} {trigger['event_manipulation']}")
            
            if 'corrected' in trigger['trigger_name']:
                print(f"      ✅ Using corrected function")
            else:
                print(f"      ⚠️ Using old function")
        
        # Check recent records have billing fields
        print("\n📊 Recent records billing field status:")
        
        cur.execute("""
            SELECT 
                'System 1' as system,
                COUNT(*) as recent_records,
                COUNT(billing_cost) as has_cost,
                COUNT(billing_benefit) as has_benefit,
                COUNT(billing_schedule) as has_schedule,
                AVG(COALESCE(billing_cost, 0)) as avg_cost,
                AVG(COALESCE(billing_benefit, 0)) as avg_benefit
            FROM solax_data 
            WHERE timestamp >= CURRENT_TIMESTAMP - INTERVAL '1 hour'
            
            UNION ALL
            
            SELECT 
                'System 2' as system,
                COUNT(*) as recent_records,
                COUNT(billing_cost) as has_cost,
                COUNT(billing_benefit) as has_benefit,
                COUNT(billing_schedule) as has_schedule,
                AVG(COALESCE(billing_cost, 0)) as avg_cost,
                AVG(COALESCE(billing_benefit, 0)) as avg_benefit
            FROM solax_data2 
            WHERE timestamp >= CURRENT_TIMESTAMP - INTERVAL '1 hour'
        """)
        
        recent_results = cur.fetchall()
        for result in recent_results:
            total = result['recent_records']
            if total > 0:
                cost_pct = (result['has_cost'] / total * 100)
                benefit_pct = (result['has_benefit'] / total * 100)
                schedule_pct = (result['has_schedule'] / total * 100)
                
                print(f"   {result['system']} (last hour):")
                print(f"      Records: {total}")
                print(f"      Cost coverage: {cost_pct:.1f}%")
                print(f"      Benefit coverage: {benefit_pct:.1f}%")
                print(f"      Schedule coverage: {schedule_pct:.1f}%")
                print(f"      Avg cost: €{result['avg_cost']:.6f}")
                print(f"      Avg benefit: €{result['avg_benefit']:.6f}")
                
                if cost_pct == 100 and benefit_pct == 100:
                    print(f"      ✅ Triggers working perfectly")
                else:
                    print(f"      ⚠️ Triggers may need attention")
            else:
                print(f"   {result['system']}: No recent records")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Main validation function"""
    print("🔍 FINAL VALIDATION TEST")
    print("=" * 60)
    print("Testing all aspects of the corrected billing implementation...")
    
    # Run all tests
    test_billing_fields_accuracy()
    test_standardized_api()
    test_triggers_functionality()
    
    print("\n" + "="*60)
    print("🎯 FINAL VALIDATION ΟΛΟΚΛΗΡΩΘΗΚΕ")
    print("="*60)
    print("✅ Billing fields accuracy tested")
    print("✅ Standardized API tested")
    print("✅ Triggers functionality verified")
    print("\n📝 Summary:")
    print("   - Check results above for any issues")
    print("   - All systems should show ✅ status")
    print("   - Coverage should be >95% for recent data")

if __name__ == "__main__":
    main()
