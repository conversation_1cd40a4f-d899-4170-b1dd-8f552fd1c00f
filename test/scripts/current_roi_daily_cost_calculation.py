#!/usr/bin/env python3
"""
Current ROI & Daily Cost Calculation
Calculate with current (partially corrected) billing fields
"""

import psycopg2
import pandas as pd
from datetime import datetime, date, timedelta
import sys
import os

# Add project root to path
sys.path.append('/home/<USER>/solar-prediction-project')

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 5433,
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

def get_db_connection():
    """Get database connection"""
    try:
        return psycopg2.connect(**DB_CONFIG)
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return None

def calculate_current_daily_cost():
    """Calculate daily cost with current billing fields"""
    print("\n" + "="*60)
    print("💰 DAILY COST CALCULATION (CURRENT STATE)")
    print("="*60)
    
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cur = conn.cursor()
        
        today = date.today()
        print(f"\n📅 Calculating for {today}")
        
        # System 1 daily cost
        print("\n🏠 SYSTEM 1 (Σπίτι Πάνω):")
        cur.execute("""
            SELECT 
                SUM(COALESCE(billing_cost, 0)) as total_cost,
                SUM(COALESCE(billing_benefit, 0)) as total_benefit,
                SUM(COALESCE(billing_net_metering_credit, 0)) as total_credit,
                MAX(yield_today) as daily_production,
                COUNT(*) as records,
                COUNT(CASE WHEN billing_cost > 100 THEN 1 END) as problematic_records
            FROM solax_data 
            WHERE DATE(timestamp) = %s
        """, (today,))
        
        result = cur.fetchone()
        if result:
            cost, benefit, credit, production, records, problematic = result
            net_cost = (cost or 0) - (benefit or 0) - (credit or 0)
            
            print(f"   📊 Raw billing fields:")
            print(f"      Total cost: €{cost:.4f}" if cost else "      Total cost: €0.0000")
            print(f"      Total benefit: €{benefit:.4f}" if benefit else "      Total benefit: €0.0000")
            print(f"      Total credit: €{credit:.4f}" if credit else "      Total credit: €0.0000")
            print(f"      Net cost: €{net_cost:.4f}")
            print(f"      Production: {production:.2f} kWh" if production else "      Production: 0.00 kWh")
            print(f"      Records: {records}")
            if problematic > 0:
                print(f"      ⚠️ Problematic records (>€100): {problematic}")
            
            # Manual calculation for comparison
            if production:
                # System 1: 40.5% self-consumption
                self_consumption = production * 0.405
                total_rate = 0.142 + 0.0069 + 0.017  # energy + network + etmear
                manual_benefit = self_consumption * total_rate
                manual_cost = 0.000  # Assume no grid import for today
                manual_net_cost = manual_cost - manual_benefit
                
                print(f"\n   🧮 Manual calculation:")
                print(f"      Self-consumption: {self_consumption:.2f} kWh")
                print(f"      Manual benefit: €{manual_benefit:.4f}")
                print(f"      Manual net cost: €{manual_net_cost:.4f}")
                
                # Comparison
                if benefit:
                    benefit_diff = abs(float(benefit) - manual_benefit)
                    print(f"      Difference: €{benefit_diff:.4f}")
        
        # System 2 daily cost
        print("\n🏠 SYSTEM 2 (Σπίτι Κάτω):")
        cur.execute("""
            SELECT 
                SUM(COALESCE(billing_cost, 0)) as total_cost,
                SUM(COALESCE(billing_benefit, 0)) as total_benefit,
                SUM(COALESCE(billing_net_metering_credit, 0)) as total_credit,
                MAX(COALESCE(yield_today, 0)) as daily_production,
                COUNT(*) as records,
                COUNT(billing_cost) as has_billing_fields,
                COUNT(CASE WHEN billing_cost > 100 THEN 1 END) as problematic_records
            FROM solax_data2 
            WHERE DATE(timestamp) = %s
        """, (today,))
        
        result = cur.fetchone()
        if result:
            cost, benefit, credit, production, records, has_billing, problematic = result
            net_cost = (cost or 0) - (benefit or 0) - (credit or 0)
            coverage = (has_billing / records * 100) if records > 0 else 0
            
            print(f"   📊 Raw billing fields:")
            print(f"      Total cost: €{cost:.4f}" if cost else "      Total cost: €0.0000")
            print(f"      Total benefit: €{benefit:.4f}" if benefit else "      Total benefit: €0.0000")
            print(f"      Total credit: €{credit:.4f}" if credit else "      Total credit: €0.0000")
            print(f"      Net cost: €{net_cost:.4f}")
            print(f"      Production: {production:.2f} kWh")
            print(f"      Records: {records}")
            print(f"      Billing coverage: {coverage:.1f}%")
            if problematic > 0:
                print(f"      ⚠️ Problematic records (>€100): {problematic}")
            
            # Manual calculation for comparison
            if production > 0:
                # System 2: 47% self-consumption
                self_consumption = production * 0.47
                total_rate = 0.142 + 0.0069 + 0.017  # energy + network + etmear
                manual_benefit = self_consumption * total_rate
                manual_cost = 0.000  # Assume no grid import for today
                manual_net_cost = manual_cost - manual_benefit
                
                print(f"\n   🧮 Manual calculation:")
                print(f"      Self-consumption: {self_consumption:.2f} kWh")
                print(f"      Manual benefit: €{manual_benefit:.4f}")
                print(f"      Manual net cost: €{manual_net_cost:.4f}")
                
                # Comparison
                if benefit:
                    benefit_diff = abs(float(benefit) - manual_benefit)
                    print(f"      Difference: €{benefit_diff:.4f}")
        
        # Combined totals
        print("\n🎯 COMBINED DAILY TOTALS:")
        cur.execute("""
            SELECT 
                (SELECT SUM(COALESCE(billing_cost, 0)) FROM solax_data WHERE DATE(timestamp) = %s) +
                (SELECT SUM(COALESCE(billing_cost, 0)) FROM solax_data2 WHERE DATE(timestamp) = %s) as total_cost,
                (SELECT SUM(COALESCE(billing_benefit, 0)) FROM solax_data WHERE DATE(timestamp) = %s) +
                (SELECT SUM(COALESCE(billing_benefit, 0)) FROM solax_data2 WHERE DATE(timestamp) = %s) as total_benefit,
                (SELECT MAX(yield_today) FROM solax_data WHERE DATE(timestamp) = %s) +
                (SELECT MAX(COALESCE(yield_today, 0)) FROM solax_data2 WHERE DATE(timestamp) = %s) as total_production
        """, (today, today, today, today, today, today))
        
        result = cur.fetchone()
        if result:
            total_cost, total_benefit, total_production = result
            total_net_cost = (total_cost or 0) - (total_benefit or 0)
            
            print(f"   Combined cost: €{total_cost:.4f}" if total_cost else "   Combined cost: €0.0000")
            print(f"   Combined benefit: €{total_benefit:.4f}" if total_benefit else "   Combined benefit: €0.0000")
            print(f"   Combined net cost: €{total_net_cost:.4f}")
            print(f"   Combined production: {total_production:.2f} kWh" if total_production else "   Combined production: 0.00 kWh")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

def calculate_current_roi():
    """Calculate ROI with current billing fields"""
    print("\n" + "="*60)
    print("📈 ROI CALCULATION (CURRENT STATE)")
    print("="*60)
    
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cur = conn.cursor()
        
        investment_cost = 12500.0  # €12,500 per system
        
        # System 1 ROI
        print("\n🏠 SYSTEM 1 ROI:")
        cur.execute("""
            SELECT 
                SUM(COALESCE(billing_benefit, 0)) as total_benefit,
                SUM(COALESCE(billing_cost, 0)) as total_cost,
                COUNT(DISTINCT DATE(timestamp)) as operational_days,
                MIN(timestamp) as start_date,
                MAX(timestamp) as end_date,
                COUNT(CASE WHEN billing_cost > 100 THEN 1 END) as problematic_records
            FROM solax_data 
            WHERE timestamp >= '2024-01-01'
        """)
        
        result = cur.fetchone()
        if result:
            benefit, cost, days, start_date, end_date, problematic = result
            
            if days > 0:
                # Calculate annual metrics
                annual_benefit = (float(benefit or 0) / days) * 365
                annual_cost = (float(cost or 0) / days) * 365
                net_annual_benefit = annual_benefit - annual_cost
                
                # ROI calculation
                annual_roi = (net_annual_benefit / investment_cost) * 100 if investment_cost > 0 else 0
                payback_years = investment_cost / net_annual_benefit if net_annual_benefit > 0 else None
                
                print(f"   📊 Financial metrics:")
                print(f"      Operational period: {start_date} to {end_date} ({days} days)")
                print(f"      Total benefit: €{benefit:.2f}" if benefit else "      Total benefit: €0.00")
                print(f"      Total cost: €{cost:.2f}" if cost else "      Total cost: €0.00")
                print(f"      Annual benefit: €{annual_benefit:.2f}")
                print(f"      Annual cost: €{annual_cost:.2f}")
                print(f"      Net annual benefit: €{net_annual_benefit:.2f}")
                print(f"      Annual ROI: {annual_roi:.2f}%")
                print(f"      Payback period: {payback_years:.1f} years" if payback_years else "      Payback period: N/A")
                if problematic > 0:
                    print(f"      ⚠️ Problematic records: {problematic}")
        
        # System 2 ROI
        print("\n🏠 SYSTEM 2 ROI:")
        cur.execute("""
            SELECT 
                SUM(COALESCE(billing_benefit, 0)) as total_benefit,
                SUM(COALESCE(billing_cost, 0)) as total_cost,
                COUNT(DISTINCT DATE(timestamp)) as operational_days,
                MIN(timestamp) as start_date,
                MAX(timestamp) as end_date,
                COUNT(billing_cost) as has_billing_fields,
                COUNT(*) as total_records,
                COUNT(CASE WHEN billing_cost > 100 THEN 1 END) as problematic_records
            FROM solax_data2 
            WHERE timestamp >= '2024-01-01'
        """)
        
        result = cur.fetchone()
        if result:
            benefit, cost, days, start_date, end_date, has_billing, total_records, problematic = result
            coverage = (has_billing / total_records * 100) if total_records > 0 else 0
            
            if days > 0:
                # Calculate annual metrics
                annual_benefit = (float(benefit or 0) / days) * 365
                annual_cost = (float(cost or 0) / days) * 365
                net_annual_benefit = annual_benefit - annual_cost
                
                # ROI calculation
                annual_roi = (net_annual_benefit / investment_cost) * 100 if investment_cost > 0 else 0
                payback_years = investment_cost / net_annual_benefit if net_annual_benefit > 0 else None
                
                print(f"   📊 Financial metrics:")
                print(f"      Operational period: {start_date} to {end_date} ({days} days)")
                print(f"      Billing coverage: {coverage:.1f}%")
                print(f"      Total benefit: €{benefit:.2f}" if benefit else "      Total benefit: €0.00")
                print(f"      Total cost: €{cost:.2f}" if cost else "      Total cost: €0.00")
                print(f"      Annual benefit: €{annual_benefit:.2f}")
                print(f"      Annual cost: €{annual_cost:.2f}")
                print(f"      Net annual benefit: €{net_annual_benefit:.2f}")
                print(f"      Annual ROI: {annual_roi:.2f}%")
                print(f"      Payback period: {payback_years:.1f} years" if payback_years else "      Payback period: N/A")
                if problematic > 0:
                    print(f"      ⚠️ Problematic records: {problematic}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Main calculation function"""
    print("📊 CURRENT ROI & DAILY COST CALCULATION")
    print("=" * 60)
    print("⚠️ Using current (partially corrected) billing fields")
    print("⚠️ Some unrealistic values may still exist")
    
    # Calculate current metrics
    calculate_current_daily_cost()
    calculate_current_roi()
    
    print("\n" + "="*60)
    print("🎯 SUMMARY")
    print("="*60)
    print("⚠️ Results may be inaccurate due to:")
    print("   - Unrealistic billing_cost values (>€100)")
    print("   - Incomplete System 2 billing coverage (54.2%)")
    print("   - Unfinished historical data correction")
    print("\n📝 To get accurate results:")
    print("   1. Complete the billing fields correction")
    print("   2. Fix remaining unrealistic values")
    print("   3. Fill missing System 2 billing fields")

if __name__ == "__main__":
    main()
