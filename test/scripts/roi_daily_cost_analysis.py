#!/usr/bin/env python3
"""
ROI & Daily Cost Analysis - Comprehensive Investigation
Απαντάει στα 4 κρίσιμα ερωτήματα του χρήστη
"""

import psycopg2
import pandas as pd
from datetime import datetime, date, timedelta
import sys
import os

# Add project root to path
sys.path.append('/home/<USER>/solar-prediction-project')

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 5433,
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

def get_db_connection():
    """Get database connection"""
    try:
        return psycopg2.connect(**DB_CONFIG)
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return None

def check_seasonal_tariffs():
    """Ερώτημα 1: Ελέγχει αν χρησιμοποιούνται εποχικά τιμολόγια"""
    print("\n" + "="*60)
    print("🌍 ΕΡΩΤΗΜΑ 1: ΕΠΟΧΙΚΑ ΤΙΜΟΛΟΓΙΑ")
    print("="*60)
    
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cur = conn.cursor()
        
        # Έλεγχος tariffs table
        print("\n📋 Έλεγχος tariffs table:")
        cur.execute("""
            SELECT category, value, created_at
            FROM tariffs
            ORDER BY category, created_at DESC
            LIMIT 20
        """)
        
        tariffs = cur.fetchall()
        if tariffs:
            print("✅ Βρέθηκαν versioned tariffs:")
            for category, value, created_at in tariffs:
                print(f"   {category}: €{value:.4f} (από {created_at})")
        else:
            print("❌ Δεν βρέθηκαν tariffs στη βάση")
        
        # Έλεγχος για seasonal patterns στα billing fields
        print("\n📊 Έλεγχος seasonal patterns στα billing data:")
        cur.execute("""
            SELECT 
                EXTRACT(MONTH FROM timestamp) as month,
                AVG(billing_tariff) as avg_tariff,
                COUNT(*) as records
            FROM solax_data 
            WHERE billing_tariff IS NOT NULL 
                AND timestamp >= '2024-01-01'
            GROUP BY EXTRACT(MONTH FROM timestamp)
            ORDER BY month
        """)
        
        seasonal_data = cur.fetchall()
        if seasonal_data:
            print("✅ Seasonal tariff patterns:")
            for month, avg_tariff, records in seasonal_data:
                season = "Χειμώνας" if month in [11,12,1,2,3] else "Καλοκαίρι"
                print(f"   Μήνας {month:2d} ({season}): €{avg_tariff:.4f}/kWh ({records:,} records)")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

def check_billing_fields_usage():
    """Ερώτημα 2: Ελέγχει αν χρησιμοποιούνται billing fields ή υπολογισμοί"""
    print("\n" + "="*60)
    print("💾 ΕΡΩΤΗΜΑ 2: BILLING FIELDS vs ΥΠΟΛΟΓΙΣΜΟΙ")
    print("="*60)
    
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cur = conn.cursor()
        
        # Έλεγχος ύπαρξης billing fields
        print("\n📋 Έλεγχος billing fields στη βάση:")
        cur.execute("""
            SELECT 
                COUNT(*) as total_records,
                COUNT(billing_cost) as has_billing_cost,
                COUNT(billing_benefit) as has_billing_benefit,
                COUNT(billing_tariff) as has_billing_tariff,
                AVG(billing_cost) as avg_cost,
                AVG(billing_benefit) as avg_benefit
            FROM solax_data 
            WHERE timestamp >= '2024-06-01'
        """)
        
        result = cur.fetchone()
        total, has_cost, has_benefit, has_tariff, avg_cost, avg_benefit = result
        
        print(f"✅ System 1 billing fields:")
        print(f"   Total records: {total:,}")
        print(f"   Has billing_cost: {has_cost:,} ({has_cost/total*100:.1f}%)")
        print(f"   Has billing_benefit: {has_benefit:,} ({has_benefit/total*100:.1f}%)")
        print(f"   Has billing_tariff: {has_tariff:,} ({has_tariff/total*100:.1f}%)")
        print(f"   Avg billing_cost: €{avg_cost:.4f}" if avg_cost else "   Avg billing_cost: NULL")
        print(f"   Avg billing_benefit: €{avg_benefit:.4f}" if avg_benefit else "   Avg billing_benefit: NULL")
        
        # Έλεγχος System 2
        cur.execute("""
            SELECT 
                COUNT(*) as total_records,
                COUNT(billing_cost) as has_billing_cost,
                COUNT(billing_benefit) as has_billing_benefit,
                COUNT(billing_tariff) as has_billing_tariff
            FROM solax_data2 
            WHERE timestamp >= '2024-06-01'
        """)
        
        result = cur.fetchone()
        total2, has_cost2, has_benefit2, has_tariff2 = result
        
        print(f"\n✅ System 2 billing fields:")
        print(f"   Total records: {total2:,}")
        print(f"   Has billing_cost: {has_cost2:,} ({has_cost2/total2*100:.1f}%)")
        print(f"   Has billing_benefit: {has_benefit2:,} ({has_benefit2/total2*100:.1f}%)")
        print(f"   Has billing_tariff: {has_tariff2:,} ({has_tariff2/total2*100:.1f}%)")
        
        # Έλεγχος triggers
        print("\n🔧 Έλεγχος database triggers:")
        cur.execute("""
            SELECT trigger_name, event_manipulation, action_statement
            FROM information_schema.triggers 
            WHERE event_object_table IN ('solax_data', 'solax_data2')
                AND trigger_name LIKE '%billing%'
        """)
        
        triggers = cur.fetchall()
        if triggers:
            print("✅ Βρέθηκαν billing triggers:")
            for name, event, action in triggers:
                print(f"   {name} ({event})")
        else:
            print("❌ Δεν βρέθηκαν billing triggers")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

def validate_billing_fields_accuracy():
    """Ερώτημα 3: Ελέγχει την ακρίβεια των billing fields"""
    print("\n" + "="*60)
    print("🎯 ΕΡΩΤΗΜΑ 3: ΑΚΡΙΒΕΙΑ BILLING FIELDS")
    print("="*60)
    
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cur = conn.cursor()
        
        # Σύγκριση billing fields με manual calculation για σήμερα
        today = date.today()
        print(f"\n📊 Σύγκριση για {today}:")
        
        # System 1 analysis
        cur.execute("""
            SELECT 
                SUM(billing_cost) as total_billing_cost,
                SUM(billing_benefit) as total_billing_benefit,
                MAX(yield_today) as daily_production,
                MAX(consume_energy) - MIN(consume_energy) as daily_consumption,
                COUNT(*) as records
            FROM solax_data 
            WHERE DATE(timestamp) = %s
        """, (today,))
        
        result = cur.fetchone()
        if result and result[0] is not None:
            billing_cost, billing_benefit, production, consumption, records = result
            
            print(f"✅ System 1 (από billing fields):")
            print(f"   Billing cost: €{billing_cost:.4f}")
            print(f"   Billing benefit: €{billing_benefit:.4f}")
            print(f"   Production: {production:.2f} kWh")
            print(f"   Consumption: {consumption:.2f} kWh" if consumption else "   Consumption: NULL")
            print(f"   Records: {records}")
            
            # Manual calculation
            if production and consumption:
                # Greek tariff rates
                energy_rate = 0.142  # €/kWh
                network_rate = 0.007  # €/kWh tier 1
                etmear_rate = 0.017  # €/kWh
                total_rate = energy_rate + network_rate + etmear_rate
                
                # Estimate self-consumption (40.5% for System 1)
                self_consumption = production * 0.405
                grid_import = max(0, consumption - self_consumption)
                
                manual_cost = grid_import * total_rate
                manual_benefit = self_consumption * total_rate
                
                print(f"\n🧮 Manual calculation:")
                print(f"   Self-consumption: {self_consumption:.2f} kWh")
                print(f"   Grid import: {grid_import:.2f} kWh")
                print(f"   Manual cost: €{manual_cost:.4f}")
                print(f"   Manual benefit: €{manual_benefit:.4f}")
                
                # Comparison
                cost_diff = abs(float(billing_cost) - manual_cost)
                benefit_diff = abs(float(billing_benefit) - manual_benefit)
                
                print(f"\n📈 Σύγκριση:")
                print(f"   Cost difference: €{cost_diff:.4f} ({cost_diff/max(manual_cost,0.001)*100:.1f}%)")
                print(f"   Benefit difference: €{benefit_diff:.4f} ({benefit_diff/max(manual_benefit,0.001)*100:.1f}%)")
                
                if cost_diff < 0.01 and benefit_diff < 0.01:
                    print("   ✅ Billing fields are ACCURATE")
                else:
                    print("   ⚠️ Billing fields may need validation")
        else:
            print("❌ Δεν βρέθηκαν δεδομένα για σήμερα")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

def check_interpolation_capabilities():
    """Ερώτημα 4: Ελέγχει δυνατότητες interpolation για missing data"""
    print("\n" + "="*60)
    print("🔄 ΕΡΩΤΗΜΑ 4: INTERPOLATION MISSING DATA")
    print("="*60)
    
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cur = conn.cursor()
        
        # Έλεγχος missing data patterns
        print("\n📊 Έλεγχος missing data patterns:")
        
        # Check for gaps in timestamps
        cur.execute("""
            WITH time_gaps AS (
                SELECT 
                    timestamp,
                    LAG(timestamp) OVER (ORDER BY timestamp) as prev_timestamp,
                    timestamp - LAG(timestamp) OVER (ORDER BY timestamp) as gap
                FROM solax_data 
                WHERE timestamp >= '2024-06-20'
                ORDER BY timestamp
            )
            SELECT 
                COUNT(*) as total_gaps,
                COUNT(CASE WHEN gap > INTERVAL '5 minutes' THEN 1 END) as significant_gaps,
                MAX(gap) as max_gap,
                AVG(gap) as avg_gap
            FROM time_gaps
            WHERE gap IS NOT NULL
        """)
        
        result = cur.fetchone()
        total_gaps, sig_gaps, max_gap, avg_gap = result
        
        print(f"✅ System 1 time gaps analysis:")
        print(f"   Total gaps: {total_gaps:,}")
        print(f"   Significant gaps (>5min): {sig_gaps:,}")
        print(f"   Max gap: {max_gap}")
        print(f"   Avg gap: {avg_gap}")
        
        # Check cumulative fields for interpolation
        print("\n🔄 Cumulative fields για interpolation:")
        cur.execute("""
            SELECT 
                COUNT(*) as total_records,
                COUNT(yield_today) as has_yield_today,
                COUNT(yield_total) as has_yield_total,
                COUNT(consume_energy) as has_consume_energy,
                COUNT(feedin_energy) as has_feedin_energy
            FROM solax_data 
            WHERE timestamp >= '2024-06-20'
        """)
        
        result = cur.fetchone()
        total, yield_today, yield_total, consume, feedin = result
        
        print(f"✅ Cumulative fields availability:")
        print(f"   yield_today: {yield_today:,}/{total:,} ({yield_today/total*100:.1f}%)")
        print(f"   yield_total: {yield_total:,}/{total:,} ({yield_total/total*100:.1f}%)")
        print(f"   consume_energy: {consume:,}/{total:,} ({consume/total*100:.1f}%)")
        print(f"   feedin_energy: {feedin:,}/{total:,} ({feedin/total*100:.1f}%)")
        
        # Test interpolation example
        print("\n🧪 Test interpolation example:")
        cur.execute("""
            SELECT 
                timestamp,
                yield_today,
                LAG(yield_today) OVER (ORDER BY timestamp) as prev_yield,
                LEAD(yield_today) OVER (ORDER BY timestamp) as next_yield
            FROM solax_data 
            WHERE DATE(timestamp) = '2024-06-24'
                AND yield_today IS NOT NULL
            ORDER BY timestamp
            LIMIT 5
        """)
        
        interpolation_data = cur.fetchall()
        if interpolation_data:
            print("✅ Sample data for interpolation:")
            for ts, current, prev, next_val in interpolation_data:
                prev_str = f"{prev:.2f}" if prev is not None else "NULL"
                next_str = f"{next_val:.2f}" if next_val is not None else "NULL"
                print(f"   {ts}: {current:.2f} kWh (prev: {prev_str}, next: {next_str})")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Main analysis function"""
    print("🔍 ROI & DAILY COST COMPREHENSIVE ANALYSIS")
    print("=" * 60)
    print("Απαντάει στα 4 κρίσιμα ερωτήματα:")
    print("1. Εποχικά τιμολόγια")
    print("2. Billing fields vs υπολογισμοί") 
    print("3. Ακρίβεια billing fields")
    print("4. Interpolation missing data")
    
    # Run all checks
    check_seasonal_tariffs()
    check_billing_fields_usage()
    validate_billing_fields_accuracy()
    check_interpolation_capabilities()
    
    print("\n" + "="*60)
    print("🎯 ΣΥΝΟΨΗ ΑΠΟΤΕΛΕΣΜΑΤΩΝ")
    print("="*60)
    print("Δες τα αποτελέσματα παραπάνω για λεπτομερείς απαντήσεις!")

if __name__ == "__main__":
    main()
