#!/bin/bash

# Script για ενημέρωση του Docker image με τρέχοντα δεδομένα
# Χρήση: ./update_docker_with_current_data.sh

set -e

echo "🔄 Ενημέρωση Docker image με τρέχοντα δεδομένα..."

# Έλεγχος αν η PostgreSQL τρέχει
if ! pg_isready -h localhost -p 5433 >/dev/null 2>&1; then
    echo "❌ Η PostgreSQL δεν τρέχει στο port 5433"
    echo "Παρακαλώ ξεκινήστε την PostgreSQL με: docker compose -f docker-compose.monolithic.yml up -d postgres"
    exit 1
fi

echo "✅ PostgreSQL βρέθηκε στο port 5433"

# Δημιουργία backup από την τρέχουσα βάση
echo "📦 Δημιουργία backup από την τρέχουσα βάση..."
BACKUP_FILE="current_database_backup_$(date +%Y%m%d_%H%M%S).sql"
PGPASSWORD=postgres pg_dump -h localhost -p 5433 -U postgres -d solar_prediction > "$BACKUP_FILE"

if [ ! -s "$BACKUP_FILE" ]; then
    echo "❌ Το backup αρχείο είναι κενό ή δεν δημιουργήθηκε"
    exit 1
fi

echo "✅ Backup δημιουργήθηκε: $BACKUP_FILE ($(du -h "$BACKUP_FILE" | cut -f1))"

# Ενημέρωση του Dockerfile να χρησιμοποιεί το νέο backup
echo "🔧 Ενημέρωση Dockerfile..."
sed -i "s|current_backup\.sql|$BACKUP_FILE|g" Dockerfile.complete

# Δημιουργία νέου Docker image
echo "🐳 Δημιουργία νέου Docker image..."
docker build -f Dockerfile.complete -t solar-prediction-complete:latest .

# Επαναφορά του Dockerfile
echo "🔄 Επαναφορά Dockerfile..."
sed -i "s|$BACKUP_FILE|current_backup.sql|g" Dockerfile.complete

# Καθαρισμός
echo "🧹 Καθαρισμός προσωρινών αρχείων..."
rm -f "$BACKUP_FILE"

echo "✅ Το Docker image ενημερώθηκε επιτυχώς με τα τρέχοντα δεδομένα!"
echo ""
echo "🚀 Για να ξεκινήσετε το container:"
echo "docker run -d --name solar-prediction-system -p 5432:5432 -p 8100:8100 -p 8109:8109 -p 8110:8110 solar-prediction-complete:latest"
echo ""
echo "📊 Για να ελέγξετε την κατάσταση:"
echo "docker logs solar-prediction-system"
