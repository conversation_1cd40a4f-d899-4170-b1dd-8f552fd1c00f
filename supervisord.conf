[supervisord]
nodaemon=true
user=solarapp
logfile=/app/logs/supervisord.log
pidfile=/app/logs/supervisord.pid
childlogdir=/app/logs

[unix_http_server]
file=/app/logs/supervisor.sock
chmod=0700
chown=solarapp:solarapp

[supervisorctl]
serverurl=unix:///app/logs/supervisor.sock

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

# Main API Server (Port 8100)
[program:api-server]
command=python3 scripts/production/production_api_server.py
directory=/app
user=solarapp
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/app/logs/api-server.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=5
environment=DATABASE_URL="postgresql://postgres:postgres@%(ENV_DATABASE_HOST)s:%(ENV_DATABASE_PORT)s/solar_prediction",PYTHONPATH="/app:/app/src"

# Telegram Bot Service (Port 8109)
[program:telegram-bot]
command=python3 telegram_bot_service.py
directory=/app
user=solarapp
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/app/logs/telegram-bot.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=5
environment=DATABASE_URL="postgresql://postgres:postgres@%(ENV_DATABASE_HOST)s:%(ENV_DATABASE_PORT)s/solar_prediction",PYTHONPATH="/app:/app/src"

# Enhanced Billing Service (Port 8110)
[program:enhanced-billing]
command=python3 enhanced_billing_service.py
directory=/app
user=solarapp
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/app/logs/enhanced-billing.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=5
environment=DATABASE_URL="postgresql://postgres:postgres@%(ENV_DATABASE_HOST)s:%(ENV_DATABASE_PORT)s/solar_prediction",PYTHONPATH="/app:/app/src"

[group:solar-services]
programs=api-server,telegram-bot,enhanced-billing
priority=999
