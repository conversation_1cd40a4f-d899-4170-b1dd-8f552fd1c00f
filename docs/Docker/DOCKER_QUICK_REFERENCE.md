# 🚀 SOLAR PREDICTION SYSTEM - DOCKER QUICK REFERENCE

## 📋 Essential Commands

### 🐳 Container Management
```bash
# Start all services
docker-compose up -d

# Stop all services
docker-compose down

# Restart specific service
docker-compose restart solar-prediction-main

# View all containers
docker ps

# Check container logs
docker logs solar-prediction-telegram --tail 50

# Execute command in container
docker exec -it solar-prediction-main bash
```

### 🔍 Health Checks
```bash
# Quick health check all services
curl http://localhost:8100/health  # Main API
curl http://localhost:8103/health  # Charts
curl http://localhost:8105/health  # GPU Prediction
curl http://localhost:8110/health  # Billing
curl http://localhost:8120/health  # Forecast

# Database connection test
docker exec solar-prediction-db psql -U postgres -d solar_prediction -c "SELECT 1;"

# Telegram bot status
docker logs solar-prediction-telegram | grep "Bot started"
```

## 🔧 Critical Configuration

### 🗄️ Database Connection (INTERNAL)
```bash
# ✅ CORRECT - Use inside containers
DATABASE_URL=********************************************/solar_prediction

# ❌ WRONG - External port
DATABASE_URL=postgresql://postgres:postgres@localhost:5433/solar_prediction
```

### 🤖 Telegram Bot Script
```bash
# ✅ CORRECT script location
scripts/frontend_system/greek_telegram_bot.py

# Check if correct script is running
docker exec solar-prediction-telegram ps aux | grep python
```

### 🌐 Service Communication
```bash
# ✅ CORRECT - Container names
http://solar-prediction:8100
http://enhanced-billing:8110
http://unified-forecast:8120

# ❌ WRONG - Localhost
http://localhost:8100
http://localhost:8110
```

## 🚨 Common Issues & Quick Fixes

### ❌ Issue: Database Connection Failed
```bash
# Check database is running
docker ps | grep postgres

# Verify internal connection
docker exec solar-prediction-main python -c "
import psycopg2
conn = psycopg2.connect('********************************************/solar_prediction')
print('OK')
"
```

### ❌ Issue: Telegram Bot Wrong Script
```bash
# Check SERVICE_TYPE
docker exec solar-prediction-telegram env | grep SERVICE_TYPE

# Should be: SERVICE_TYPE=telegram-bot
# If wrong, fix in docker-compose.yml and restart
```

### ❌ Issue: ROI Returns 0%
```bash
# Check database has data
docker exec solar-prediction-db psql -U postgres -d solar_prediction -c "
SELECT COUNT(*) FROM solax_data WHERE yield_today > 0;
"

# Check billing functions exist
docker exec solar-prediction-db psql -U postgres -d solar_prediction -c "\df calculate_system_roi"
```

### ❌ Issue: Service Can't Find Other Services
```bash
# Check network
docker network inspect solar-network

# Verify container aliases
docker inspect solar-prediction-billing | grep -A 5 "Aliases"

# Should show: ["enhanced-billing", "solar-prediction-billing"]
```

## 📊 Service Overview

| Service | Port | Container Name | Purpose |
|---------|------|----------------|---------|
| Main API | 8100 | solar-prediction-main | Core API & ML |
| Charts | 8103 | solar-prediction-charts | Visualization |
| GPU Prediction | 8105 | solar-prediction-gpu | ML Acceleration |
| Scheduler | 8106 | solar-prediction-scheduler | Automated tasks |
| Alerts | 8107 | solar-prediction-alerts | Monitoring |
| Config | 8108 | solar-prediction-config | Configuration |
| Billing | 8110 | solar-prediction-billing | ROI & billing |
| Forecast | 8120 | solar-prediction-forecast | Predictions |
| Web | 8080 | solar-prediction-web | Web interface |
| Telegram | - | solar-prediction-telegram | Bot interface |
| Database | 5433 | solar-prediction-db | PostgreSQL |
| Cache | 6380 | solar-prediction-cache | Redis |

## 🔑 Essential Environment Variables

```bash
# Database (ALL services)
DATABASE_URL=********************************************/solar_prediction

# Telegram Bot
TELEGRAM_BOT_TOKEN=**********************************************
TELEGRAM_CHAT_ID=1510889515

# SolaX API
SOLAX_TOKEN_ID=20250410220826567911082
SOLAX_WIFI_SN=SRFQDPDN9W

# Service Types
SERVICE_TYPE=main|charts-api|gpu-prediction|enhanced-billing|unified-forecast|telegram-bot|web-server|config-manager|alert-system|prediction-scheduler
```

## 🎯 Quick Deployment

### 1. Load Images
```bash
cd docker_images/
for img in *.tar.gz; do docker load < "$img"; done
```

### 2. Restore Volumes
```bash
cd docker_volumes/
for vol in *.tar.gz; do
    name=$(basename "$vol" .tar.gz)
    docker volume create "$name"
    docker run --rm -v "$name":/volume -v "$(pwd)":/backup alpine sh -c "cd /volume && tar xzf /backup/$vol"
done
```

### 3. Start System
```bash
docker-compose up -d
```

### 4. Verify
```bash
# Wait 60 seconds, then check
docker ps
curl http://localhost:8100/health
```

## 📱 Telegram Bot Menu

| # | Option | API Endpoint | Data |
|---|--------|--------------|------|
| 1 | System Data | PostgreSQL queries | Real-time |
| 2 | Weather | Open-Meteo API | Real-time |
| 3 | Statistics | PostgreSQL queries | Real-time |
| 4 | Health | /health | Real-time |
| 5 | Predictions | Cached ML | 77.7 kWh |
| 6 | ROI & Payback | /billing/enhanced/roi | 30.0% ROI |
| 7 | Daily Cost | /billing/enhanced/cost | Real-time |
| 8 | Tariffs | /billing/enhanced/tariffs | Real-time |
| 9 | English | Language switch | Static |
| 10 | Help | Static help | Static |

## 🔧 Maintenance Commands

```bash
# View resource usage
docker stats --no-stream

# Clean up unused resources
docker system prune -f

# Backup database
docker exec solar-prediction-db pg_dump -U postgres solar_prediction > backup.sql

# View logs
docker-compose logs -f solar-prediction-main

# Restart unhealthy service
docker-compose restart SERVICE_NAME
```

## 🚨 Emergency Recovery

```bash
# Complete restart
docker-compose down && docker-compose up -d

# Database recovery
docker exec solar-prediction-db pg_dump -U postgres solar_prediction > emergency.sql

# Check system resources
df -h && free -h && docker stats --no-stream
```

---

**📞 Quick Help**: Check container logs first, verify environment variables, ensure internal service communication uses container names not localhost.
