#!/bin/bash

# 🔍 Solar Prediction System - Docker Deployment Verification Script
# Created: June 19, 2025
# Purpose: Comprehensive verification of Docker deployment

echo "🔍 SOLAR PREDICTION SYSTEM - DEPLOYMENT VERIFICATION"
echo "=================================================="
echo "📅 $(date)"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Counters
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# Function to check and report
check_status() {
    local description="$1"
    local command="$2"
    local expected="$3"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    echo -n "🔍 $description... "
    
    if eval "$command" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ PASS${NC}"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        echo -e "${RED}❌ FAIL${NC}"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        if [ ! -z "$expected" ]; then
            echo "   Expected: $expected"
        fi
    fi
}

# Function to check API endpoint
check_api() {
    local name="$1"
    local port="$2"
    local endpoint="$3"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    echo -n "🌐 $name API ($port$endpoint)... "
    
    if curl -f -s "http://localhost:$port$endpoint" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ RESPONDING${NC}"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        echo -e "${RED}❌ NOT RESPONDING${NC}"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
    fi
}

# Function to check container
check_container() {
    local name="$1"
    local expected_status="$2"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    echo -n "🐳 Container $name... "
    
    if docker ps --format "{{.Names}}" | grep -q "^$name$"; then
        status=$(docker inspect --format='{{.State.Health.Status}}' "$name" 2>/dev/null || echo "no-healthcheck")
        if [ "$status" = "healthy" ] || [ "$status" = "no-healthcheck" ]; then
            echo -e "${GREEN}✅ RUNNING${NC}"
            PASSED_CHECKS=$((PASSED_CHECKS + 1))
        else
            echo -e "${YELLOW}⚠️  RUNNING (Health: $status)${NC}"
            PASSED_CHECKS=$((PASSED_CHECKS + 1))
        fi
    else
        echo -e "${RED}❌ NOT RUNNING${NC}"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
    fi
}

echo "📊 STEP 1: CONTAINER STATUS VERIFICATION"
echo "----------------------------------------"

# Check all containers
containers=(
    "solar-prediction-main"
    "solar-prediction-charts"
    "solar-prediction-gpu"
    "solar-prediction-scheduler"
    "solar-prediction-alerts"
    "solar-prediction-config"
    "solar-prediction-billing"
    "solar-prediction-forecast"
    "solar-prediction-telegram"
    "solar-prediction-web"
    "solar-prediction-db"
    "solar-prediction-cache"
)

for container in "${containers[@]}"; do
    check_container "$container" "running"
done

echo ""
echo "🌐 STEP 2: API ENDPOINT VERIFICATION"
echo "------------------------------------"

# Check API endpoints
check_api "Main" "8100" "/health"
check_api "Charts" "8103" "/health"
check_api "GPU Prediction" "8105" "/health"
check_api "Scheduler" "8106" "/health"
check_api "Alerts" "8107" "/health"
check_api "Config Manager" "8108" "/health"
check_api "Enhanced Billing" "8110" "/health"
check_api "Unified Forecast" "8120" "/health"
check_api "Web Server" "8080" "/"

echo ""
echo "🗄️ STEP 3: DATABASE VERIFICATION"
echo "--------------------------------"

# Database connection test
check_status "Database connection" "docker exec solar-prediction-db psql -U postgres -d solar_prediction -c 'SELECT 1;'"

# Check critical tables
check_status "SolaX data table" "docker exec solar-prediction-db psql -U postgres -d solar_prediction -c 'SELECT COUNT(*) FROM solax_data;'"
check_status "Weather data table" "docker exec solar-prediction-db psql -U postgres -d solar_prediction -c 'SELECT COUNT(*) FROM weather_data;'"
check_status "Predictions table" "docker exec solar-prediction-db psql -U postgres -d solar_prediction -c 'SELECT COUNT(*) FROM predictions;'"

# Check billing functions
check_status "ROI calculation function" "docker exec solar-prediction-db psql -U postgres -d solar_prediction -c '\df calculate_system_roi'"

echo ""
echo "🤖 STEP 4: TELEGRAM BOT VERIFICATION"
echo "------------------------------------"

# Check Telegram bot
check_status "Telegram bot process" "docker exec solar-prediction-telegram pgrep -f 'greek_telegram_bot.py'"
check_status "Telegram bot logs" "docker logs solar-prediction-telegram 2>&1 | grep -q 'Bot started'"

echo ""
echo "💰 STEP 5: BILLING SYSTEM VERIFICATION"
echo "--------------------------------------"

# Test ROI calculations
check_api "System 1 ROI" "8110" "/billing/enhanced/roi/system1"
check_api "System 2 ROI" "8110" "/billing/enhanced/roi/system2"
check_api "Daily Cost" "8110" "/billing/enhanced/cost/system1"
check_api "Tariffs" "8110" "/billing/enhanced/tariffs"

echo ""
echo "🔮 STEP 6: PREDICTION SYSTEM VERIFICATION"
echo "-----------------------------------------"

# Test prediction endpoints
check_api "Forecast Status" "8120" "/forecast/status"
check_api "GPU Prediction Status" "8105" "/gpu/status"

echo ""
echo "🌐 STEP 7: NETWORK VERIFICATION"
echo "-------------------------------"

# Check Docker network
check_status "Solar network exists" "docker network ls | grep -q solar-network"
check_status "Containers on solar network" "docker network inspect solar-network | grep -q solar-prediction"

echo ""
echo "💾 STEP 8: VOLUME VERIFICATION"
echo "------------------------------"

# Check volumes
check_status "PostgreSQL data volume" "docker volume ls | grep -q postgres_data"
check_status "Redis data volume" "docker volume ls | grep -q redis_data"
check_status "Static files volume" "docker volume ls | grep -q solar_prediction_static"

echo ""
echo "🔧 STEP 9: CONFIGURATION VERIFICATION"
echo "-------------------------------------"

# Check environment variables
check_status "Database URL configured" "docker exec solar-prediction-main env | grep -q 'DATABASE_URL.*postgres:5432'"
check_status "Telegram token configured" "docker exec solar-prediction-telegram env | grep -q 'TELEGRAM_BOT_TOKEN'"
check_status "SolaX token configured" "docker exec solar-prediction-main env | grep -q 'SOLAX_TOKEN_ID'"

echo ""
echo "📊 STEP 10: FUNCTIONAL VERIFICATION"
echo "-----------------------------------"

# Test actual functionality
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
echo -n "🔍 ROI calculation returns valid data... "
roi_response=$(curl -s "http://localhost:8110/billing/enhanced/roi/system1" 2>/dev/null)
if echo "$roi_response" | grep -q '"roi_percentage"' && ! echo "$roi_response" | grep -q '"roi_percentage": 0.0'; then
    echo -e "${GREEN}✅ PASS${NC}"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
else
    echo -e "${RED}❌ FAIL (Returns 0% or invalid data)${NC}"
    FAILED_CHECKS=$((FAILED_CHECKS + 1))
fi

TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
echo -n "🔍 Weather data collection working... "
if docker logs solar-prediction-main 2>&1 | grep -q "Weather data collected" || curl -s "http://localhost:8100/api/v1/data/weather/latest" | grep -q "temperature"; then
    echo -e "${GREEN}✅ PASS${NC}"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
else
    echo -e "${YELLOW}⚠️  WARNING (No recent weather data)${NC}"
    FAILED_CHECKS=$((FAILED_CHECKS + 1))
fi

echo ""
echo "🎯 VERIFICATION SUMMARY"
echo "======================"
echo -e "📊 Total Checks: ${BLUE}$TOTAL_CHECKS${NC}"
echo -e "✅ Passed: ${GREEN}$PASSED_CHECKS${NC}"
echo -e "❌ Failed: ${RED}$FAILED_CHECKS${NC}"

# Calculate success rate
success_rate=$((PASSED_CHECKS * 100 / TOTAL_CHECKS))
echo -e "📈 Success Rate: ${BLUE}$success_rate%${NC}"

echo ""
if [ $FAILED_CHECKS -eq 0 ]; then
    echo -e "${GREEN}🎉 DEPLOYMENT VERIFICATION SUCCESSFUL!${NC}"
    echo -e "${GREEN}✅ All systems operational and ready for production use.${NC}"
    exit 0
elif [ $success_rate -ge 80 ]; then
    echo -e "${YELLOW}⚠️  DEPLOYMENT MOSTLY SUCCESSFUL${NC}"
    echo -e "${YELLOW}🔧 Some issues detected but system is largely functional.${NC}"
    echo -e "${YELLOW}📋 Review failed checks and address issues as needed.${NC}"
    exit 1
else
    echo -e "${RED}❌ DEPLOYMENT VERIFICATION FAILED${NC}"
    echo -e "${RED}🚨 Critical issues detected. System may not be functional.${NC}"
    echo -e "${RED}📋 Address failed checks before using the system.${NC}"
    exit 2
fi
