#!/bin/bash
# 🔍 BILLING ACCURACY VERIFICATION SCRIPT
# Comprehensive verification of accurate billing implementation

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# Logging function
log() {
    local message="$1"
    local color="${2:-$NC}"
    echo -e "${color}[$(date '+%H:%M:%S')] $message${NC}"
}

# Header
echo -e "${PURPLE}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                                                              ║"
echo "║        🔍 BILLING ACCURACY VERIFICATION SCRIPT               ║"
echo "║                                                              ║"
echo "║  Comprehensive verification of accurate billing system       ║"
echo "║                                                              ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

log "🔍 Starting billing accuracy verification..." "$BLUE"

# Test 1: Daily Consumption Accuracy
log "📊 Test 1: Daily Consumption Accuracy" "$BLUE"
docker exec solar-prediction-db psql -U postgres -d solar_prediction -c "
SELECT 
    DATE(upload_time) as date,
    MAX(consume_energy) - MIN(consume_energy) as real_daily_consumption,
    SUM(grid_usage_kwh) as calculated_daily_consumption,
    ROUND(ABS((MAX(consume_energy) - MIN(consume_energy)) - SUM(grid_usage_kwh)), 4) as difference,
    CASE 
        WHEN ABS((MAX(consume_energy) - MIN(consume_energy)) - SUM(grid_usage_kwh)) < 0.01 
        THEN '✅ ACCURATE' 
        ELSE '❌ NEEDS CHECK' 
    END as status
FROM solax_data 
WHERE upload_time >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY DATE(upload_time)
ORDER BY date DESC
LIMIT 7;"

echo ""

# Test 2: Individual Record Verification
log "🔍 Test 2: Individual Record Verification" "$BLUE"
docker exec solar-prediction-db psql -U postgres -d solar_prediction -c "
SELECT 
    grid_usage_kwh,
    billing_cost,
    billing_benefit,
    billing_net_metering_credit,
    billing_tariff,
    upload_time
FROM solax_data 
WHERE upload_time::date = CURRENT_DATE - INTERVAL '1 day'
AND grid_usage_kwh > 0
ORDER BY upload_time ASC 
LIMIT 5;"

echo ""

# Test 3: Monthly Consumption Tiers
log "📈 Test 3: Monthly Consumption Tiers" "$BLUE"
docker exec solar-prediction-db psql -U postgres -d solar_prediction -c "
SELECT 
    DATE_TRUNC('month', upload_time) as month,
    SUM(grid_usage_kwh) as monthly_consumption,
    AVG(billing_tariff) as avg_energy_rate,
    CASE 
        WHEN SUM(grid_usage_kwh) <= 300 THEN 'Tier 1 (≤300 kWh)'
        WHEN SUM(grid_usage_kwh) <= 1600 THEN 'Tier 2 (301-1600 kWh)'
        ELSE 'Tier 3 (>1600 kWh)'
    END as expected_tier,
    CASE 
        WHEN AVG(billing_tariff) BETWEEN 0.074 AND 0.076 THEN 'Tier 1 Rate'
        WHEN AVG(billing_tariff) BETWEEN 0.094 AND 0.096 THEN 'Tier 2 Rate'
        WHEN AVG(billing_tariff) BETWEEN 0.114 AND 0.116 THEN 'Tier 3 Rate'
        ELSE 'Mixed/Other'
    END as actual_tier
FROM solax_data 
WHERE upload_time >= DATE_TRUNC('month', CURRENT_DATE) - INTERVAL '2 months'
GROUP BY DATE_TRUNC('month', upload_time)
ORDER BY month DESC;"

echo ""

# Test 4: Net Metering Balance Check
log "💰 Test 4: Net Metering Balance Check" "$BLUE"
docker exec solar-prediction-db psql -U postgres -d solar_prediction -c "
SELECT 
    'System 1' as system,
    MAX(feedin_energy) as total_feedin,
    MAX(consume_energy) as total_consumption,
    MAX(feedin_energy) - MAX(consume_energy) as net_metering_balance,
    CASE 
        WHEN MAX(feedin_energy) - MAX(consume_energy) > 0 
        THEN '✅ HAS CREDIT' 
        ELSE '❌ NO CREDIT' 
    END as credit_status
FROM solax_data 
WHERE upload_time <= CURRENT_DATE

UNION ALL

SELECT 
    'System 2' as system,
    MAX(feedin_energy) as total_feedin,
    MAX(consume_energy) as total_consumption,
    MAX(feedin_energy) - MAX(consume_energy) as net_metering_balance,
    CASE 
        WHEN MAX(feedin_energy) - MAX(consume_energy) > 0 
        THEN '✅ HAS CREDIT' 
        ELSE '❌ NO CREDIT' 
    END as credit_status
FROM solax_data2 
WHERE upload_time <= CURRENT_DATE;"

echo ""

# Test 5: Cost Calculation Verification
log "💶 Test 5: Cost Calculation Verification" "$BLUE"
docker exec solar-prediction-db psql -U postgres -d solar_prediction -c "
SELECT 
    DATE(upload_time) as date,
    SUM(grid_usage_kwh) as daily_consumption,
    SUM(billing_cost) as total_daily_cost,
    ROUND(SUM(billing_cost) / NULLIF(SUM(grid_usage_kwh), 0), 4) as avg_cost_per_kwh,
    CASE 
        WHEN SUM(billing_cost) / NULLIF(SUM(grid_usage_kwh), 0) BETWEEN 0.10 AND 0.20 
        THEN '✅ REASONABLE' 
        ELSE '⚠️ CHECK NEEDED' 
    END as cost_status
FROM solax_data 
WHERE upload_time >= CURRENT_DATE - INTERVAL '7 days'
AND grid_usage_kwh > 0
GROUP BY DATE(upload_time)
ORDER BY date DESC
LIMIT 7;"

echo ""

# Test 6: API Integration Test
log "🌐 Test 6: API Integration Test" "$BLUE"
api_result=$(curl -s "http://localhost:8110/billing/enhanced/cost/system1?date=$(date -d '1 day ago' +%Y-%m-%d)" | jq -r '.cost_breakdown.total_cost // "ERROR"')
if [[ "$api_result" != "ERROR" ]] && [[ "$api_result" != "null" ]]; then
    log "✅ Enhanced Billing API responding: €$api_result" "$GREEN"
else
    log "❌ Enhanced Billing API error or not responding" "$RED"
fi

echo ""

# Test 7: Database Function Status
log "🔧 Test 7: Database Function Status" "$BLUE"
docker exec solar-prediction-db psql -U postgres -d solar_prediction -c "
SELECT 
    proname as function_name,
    prosrc LIKE '%accurate%' as is_accurate_function,
    CASE 
        WHEN prosrc LIKE '%accurate%' THEN '✅ ACCURATE FUNCTION'
        ELSE '❌ OLD FUNCTION'
    END as function_status
FROM pg_proc 
WHERE proname IN ('calculate_billing_fields', 'calculate_accurate_billing_fields');"

echo ""

# Summary
log "📋 VERIFICATION SUMMARY" "$PURPLE"
echo "======================================"

# Count accurate days
accurate_days=$(docker exec solar-prediction-db psql -U postgres -d solar_prediction -t -c "
SELECT COUNT(*) 
FROM (
    SELECT DATE(upload_time) as date
    FROM solax_data 
    WHERE upload_time >= CURRENT_DATE - INTERVAL '7 days'
    GROUP BY DATE(upload_time)
    HAVING ABS((MAX(consume_energy) - MIN(consume_energy)) - SUM(grid_usage_kwh)) < 0.01
) accurate;" | tr -d ' ')

total_days=$(docker exec solar-prediction-db psql -U postgres -d solar_prediction -t -c "
SELECT COUNT(DISTINCT DATE(upload_time)) 
FROM solax_data 
WHERE upload_time >= CURRENT_DATE - INTERVAL '7 days';" | tr -d ' ')

if [[ -n "$accurate_days" ]] && [[ -n "$total_days" ]] && [[ "$total_days" -gt 0 ]]; then
    accuracy_percentage=$((accurate_days * 100 / total_days))
    log "📊 Daily Accuracy: $accurate_days/$total_days days ($accuracy_percentage%)" "$BLUE"
    
    if [[ $accuracy_percentage -ge 95 ]]; then
        log "🎉 BILLING SYSTEM VERIFICATION: PASSED" "$GREEN"
        log "✅ System is calculating billing accurately" "$GREEN"
    elif [[ $accuracy_percentage -ge 80 ]]; then
        log "⚠️ BILLING SYSTEM VERIFICATION: PARTIAL" "$YELLOW"
        log "⚠️ Most calculations accurate, some may need review" "$YELLOW"
    else
        log "❌ BILLING SYSTEM VERIFICATION: FAILED" "$RED"
        log "❌ Significant accuracy issues detected" "$RED"
    fi
else
    log "⚠️ Unable to calculate accuracy percentage" "$YELLOW"
fi

echo ""
log "🔍 Verification completed. Check results above for any issues." "$BLUE"
log "📋 For detailed analysis, review individual test results." "$BLUE"

echo ""
echo -e "${PURPLE}╔══════════════════════════════════════════════════════════════╗"
echo "║                                                              ║"
echo "║           🎯 BILLING VERIFICATION COMPLETED                  ║"
echo "║                                                              ║"
echo "╚══════════════════════════════════════════════════════════════╝${NC}"
