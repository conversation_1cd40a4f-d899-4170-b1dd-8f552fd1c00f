# 🐳 SOLAR PREDICTION SYSTEM - DOCKER ARCHITECTURE TECHNICAL GUIDE

## 📋 Overview
**Complete technical documentation for deploying the Solar Prediction System Docker infrastructure on any machine**

*Created: June 19, 2025*  
*Version: Production v2.0*  
*Target: Cross-platform deployment*

---

## 🏗️ SYSTEM ARCHITECTURE

### 📊 Service Overview
The system consists of **11 containerized services** running on a custom Docker network:

| Service | Container Name | Port | Image | Purpose |
|---------|---------------|------|-------|---------|
| **Main API** | solar-prediction-main | 8100 | solar-prediction-project_solar-prediction | Core API & ML Pipeline |
| **Charts API** | solar-prediction-charts | 8103 | solar-prediction-project_charts-api | Data visualization |
| **GPU Prediction** | solar-prediction-gpu | 8105 | solar-prediction-project_gpu-prediction | ML acceleration |
| **Prediction Scheduler** | solar-prediction-scheduler | 8106 | solar-prediction-project_prediction-scheduler | Automated predictions |
| **Alert System** | solar-prediction-alerts | 8107 | solar-prediction-project_alert-system | Monitoring & alerts |
| **Config Manager** | solar-prediction-config | 8108 | solar-prediction-project_config-manager | Configuration management |
| **Enhanced Billing** | solar-prediction-billing | 8110 | solar-prediction-project_enhanced-billing | ROI & billing calculations |
| **Unified Forecast** | solar-prediction-forecast | 8120 | solar-prediction-project_unified-forecast | Forecast aggregation |
| **Health Monitor** | solar-prediction-health | 8130 | solar-prediction-project_health-monitor | System health monitoring |
| **Telegram Bot** | solar-prediction-telegram | - | solar-prediction-project_telegram-bot | User interface |
| **Web Server** | solar-prediction-web | 8080 | solar-prediction-project_web-server | Web interface |

### 🗄️ Infrastructure Services
| Service | Container Name | Port | Image | Purpose |
|---------|---------------|------|-------|---------|
| **PostgreSQL** | solar-prediction-db | 5433:5432 | postgres:16-alpine | Primary database |
| **Redis** | solar-prediction-cache | 6380:6379 | redis:7-alpine | Caching & sessions |

---

## 🌐 NETWORK CONFIGURATION

### Custom Network: `solar-network`
```yaml
networks:
  solar-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
```

### 🔗 Internal Service Communication
All services communicate via **container names** on the `solar-network`:
- Database: `postgres:5432` (internal) / `localhost:5433` (external)
- Redis: `redis:6379` (internal) / `localhost:6380` (external)
- Main API: `solar-prediction:8100`
- Billing API: `enhanced-billing:8110`
- Forecast API: `unified-forecast:8120`

**⚠️ CRITICAL**: Services inside containers must use **internal ports** (5432, 6379), NOT external ports (5433, 6380)

---

## 💾 VOLUME CONFIGURATION

### Named Volumes (Persistent Data)
```yaml
volumes:
  postgres_data:          # Database data
  redis_data:             # Cache data  
  solar_prediction_static: # Static files
```

### Bind Mounts (Shared Code)
```yaml
volumes:
  - ./logs:/app/logs           # Application logs
  - ./models:/app/models       # ML models
  - ./data:/app/data           # Data files
  - ./scripts:/app/scripts     # Python scripts
  - ./docs:/app/docs           # Documentation
```

---

## 🔧 ENVIRONMENT VARIABLES

### 🗄️ Database Configuration (ALL SERVICES)
```bash
DATABASE_URL=********************************************/solar_prediction
DATABASE_HOST=postgres
DATABASE_PORT=5432
DATABASE_USER=postgres
DATABASE_PASSWORD=postgres
DATABASE_NAME=solar_prediction
```

### 🤖 Telegram Bot Configuration
```bash
TELEGRAM_ENABLED=true
TELEGRAM_BOT_TOKEN=**********************************************
TELEGRAM_CHAT_ID=1510889515
API_BASE_URL=http://solar-prediction:8100
MAIN_API_URL=http://solar-prediction:8100
```

### 🌤️ SolaX Cloud API Configuration
```bash
SOLAX_BASE_URL=https://www.solaxcloud.com:9443/proxy/api/getRealtimeInfo.do
SOLAX_TOKEN_ID=20250410220826567911082
SOLAX_WIFI_SN=SRFQDPDN9W
SOLAX_POLLING_INTERVAL=30
SOLAX_TIMEOUT=10
SOLAX_MAX_RETRIES=3
```

### 🌍 Weather API Configuration
```bash
WEATHER_BASE_URL=https://api.open-meteo.com/v1/forecast
WEATHER_LATITUDE=38.141348260997596
WEATHER_LONGITUDE=24.0071653937747
WEATHER_POLLING_INTERVAL=3600
WEATHER_TIMEZONE=Europe/Athens
WEATHER_FORECAST_DAYS=7
WEATHER_PAST_DAYS=1
```

### 🧠 ML Model Configuration
```bash
MODEL_PATH=/app/models/hybrid_ml_ensemble
MODEL_VERSION=hybrid_ml_ensemble
MODEL_TYPE=hybrid_ensemble
```

### 🔐 Security Configuration
```bash
SECRET_KEY=production-secret-key-change-me
ACCESS_TOKEN_EXPIRE_MINUTES=1440
```

### ⚙️ Service-Specific Configuration
```bash
CONTAINER_MODE=true
ENVIRONMENT=production
PYTHONRECURSIONLIMIT=10000
DEBUG=false
LOG_LEVEL=info
WORKERS=1
MAX_CONNECTIONS=100
KEEP_ALIVE=2
RUN_MIGRATIONS=true
```

---

## 🚀 SERVICE STARTUP CONFIGURATION

### 📋 Service Types (SERVICE_TYPE Environment Variable)
Each container determines its role via `SERVICE_TYPE`:

| SERVICE_TYPE | Script Executed | Port | Purpose |
|-------------|------------------|------|---------|
| `main` | `scripts/production/production_api_server.py` | 8100 | Main API |
| `charts-api` | `charts_api_service.py` | 8103 | Charts |
| `gpu-prediction` | `gpu_prediction_service.py` | 8105 | GPU ML |
| `enhanced-billing` | `enhanced_billing_service.py` | 8110 | Billing |
| `unified-forecast` | `unified_forecast_service.py` | 8120 | Forecast |
| `telegram-bot` | `telegram_bot_service.py` | - | Bot |
| `web-server` | `web_server_service.py` | 8080 | Web |
| `config-manager` | `config_manager_service.py` | 8108 | Config |
| `alert-system` | `alert_system_service.py` | 8107 | Alerts |
| `prediction-scheduler` | `unified_scheduler_service.py` | 8106 | Scheduler |
| `health-monitor` | `scripts/services/unified_health_service.py` | 8130 | Health |

### 🔄 Startup Dependencies
```yaml
Startup Order:
1. postgres (database)
2. redis (cache)
3. solar-prediction (main API)
4. enhanced-billing (depends on main)
5. unified-forecast (depends on main + billing)
6. All other services (depend on main)
```

---

## 🗄️ DATABASE SCHEMA

### 📊 Core Tables
```sql
-- Solar system data
solax_data          -- System 1 (Σπίτι Πάνω)
solax_data2         -- System 2 (Σπίτι Κάτω)

-- Weather & predictions
weather_data        -- Weather information
cams_radiation_data -- Historical radiation
yield_predictions   -- ML predictions
predictions         -- Legacy predictions

-- System management
system_logs         -- Application logs
model_metrics       -- ML performance
schedule_tasks      -- Automated tasks
api_configuration   -- API settings
port_registry       -- Port management
system_alerts       -- Alert history
```

### 🔧 Database Initialization
The database is automatically initialized with:
- All required tables and indexes
- Billing calculation functions
- Trigger functions for data validation
- Default configuration values
- Scheduled task definitions

---

## 🤖 TELEGRAM BOT CONFIGURATION

### 📱 Bot Script Location
```bash
# Telegram bot runs this script:
scripts/frontend_system/greek_telegram_bot.py
```

### 📋 Menu Structure (10 Options)
| # | Option | Script/API | Endpoint | Data Source |
|---|--------|------------|----------|-------------|
| 1 | System Data | `get_latest_system_data()` | PostgreSQL | Real-time |
| 2 | Weather | `get_weather_data()` | Open-Meteo API | Real-time |
| 3 | Statistics | `get_statistics()` | PostgreSQL | Real-time |
| 4 | Health | `show_main_api_health()` | `/health` | Real-time |
| 5 | Predictions | `get_cached_prediction()` | Cached ML | 77.7 kWh |
| 6 | ROI & Payback | Enhanced Billing API | `/billing/enhanced/roi` | 30.0% ROI |
| 7 | Daily Cost | `get_daily_cost()` | `/billing/enhanced/cost` | Real-time |
| 8 | Tariffs | Enhanced Billing API | `/billing/enhanced/tariffs` | Real-time |
| 9 | English | Language switch | UI change | Static |
| 10 | Help | Static help text | Static | Static |

### 🔧 ROI Submenu (Option 6)
| Sub-option | API Call | Endpoint | Data |
|------------|----------|----------|------|
| 1. Σπίτι Πάνω | `roi_command(system1)` | `/billing/enhanced/roi/system1` | 30.0% ROI, 3.3y |
| 2. Σπίτι Κάτω | `roi_command(system2)` | `/billing/enhanced/roi/system2` | 30.0% ROI, 3.3y |
| 3. Συνολικό ROI | `roi_command(combined)` | Both endpoints | Combined data |

---

## 🏥 HEALTH CHECK SYSTEM

### 🔍 Health Check Endpoints
All services expose health endpoints:
```bash
curl http://localhost:8100/health  # Main API
curl http://localhost:8103/health  # Charts API
curl http://localhost:8105/health  # GPU Prediction
curl http://localhost:8106/health  # Scheduler
curl http://localhost:8107/health  # Alert System
curl http://localhost:8108/health  # Config Manager
curl http://localhost:8109/health  # Telegram Bot
curl http://localhost:8110/health  # Enhanced Billing
curl http://localhost:8120/health  # Unified Forecast
curl http://localhost:8130/health  # Health Monitor
```

### 📊 Health Check Configuration
```yaml
healthcheck:
  test: ["CMD", "curl", "-f", "http://localhost:PORT/health"]
  interval: 30s
  timeout: 10s
  retries: 3
  start_period: 60s
```

---

## 🚨 TROUBLESHOOTING GUIDE

### ❌ Common Issues & Solutions

#### 1. **Database Connection Failed**
```bash
# Check if database is running
docker ps | grep postgres

# Check database logs
docker logs solar-prediction-db

# Verify connection inside container
docker exec -it solar-prediction-main psql -h postgres -U postgres -d solar_prediction
```

#### 2. **Telegram Bot Not Responding**
```bash
# Check bot logs
docker logs solar-prediction-telegram

# Verify bot script is running
docker exec -it solar-prediction-telegram ps aux | grep python

# Check if correct script is executed
# Should be: scripts/frontend_system/greek_telegram_bot.py
```

#### 3. **Service Health Check Failing**
```bash
# Check service logs
docker logs CONTAINER_NAME

# Verify port binding
docker port CONTAINER_NAME

# Test health endpoint manually
curl http://localhost:PORT/health
```

#### 4. **Wrong API Endpoints**
```bash
# Services should use INTERNAL container names:
# ✅ CORRECT: http://postgres:5432
# ❌ WRONG:   http://localhost:5433

# ✅ CORRECT: http://solar-prediction:8100
# ❌ WRONG:   http://localhost:8100
```

#### 5. **Environment Variables Missing**
```bash
# Check environment variables in container
docker exec -it CONTAINER_NAME env | grep DATABASE

# Verify SERVICE_TYPE is set correctly
docker exec -it CONTAINER_NAME env | grep SERVICE_TYPE
```

---

## 📋 DEPLOYMENT CHECKLIST

### ✅ Pre-Deployment Requirements
- [ ] Docker 20.10+ installed
- [ ] Docker Compose 2.0+ installed
- [ ] 50GB+ free disk space
- [ ] 8GB+ RAM available
- [ ] Internet connection for external APIs

### ✅ Deployment Steps
1. [ ] Copy backup to target machine
2. [ ] Extract backup files
3. [ ] Load Docker images: `docker load < image.tar.gz`
4. [ ] Restore Docker volumes
5. [ ] Restore database dump
6. [ ] Copy docker-compose.yml and .env
7. [ ] Run: `docker-compose up -d`
8. [ ] Verify all services healthy
9. [ ] Test Telegram bot functionality
10. [ ] Verify API endpoints respond

### ✅ Post-Deployment Verification
```bash
# Check all containers running
docker ps

# Verify health checks
curl http://localhost:8100/health

# Test Telegram bot
# Send message to bot: /start

# Check database connection
docker exec -it solar-prediction-db psql -U postgres -d solar_prediction -c "SELECT COUNT(*) FROM solax_data;"

# Verify API endpoints
curl http://localhost:8110/billing/enhanced/roi/system1
```

---

## 🎯 CRITICAL SUCCESS FACTORS

### 🔑 Essential Configuration
1. **Database Connection**: All services MUST use `postgres:5432` (internal)
2. **Service Communication**: Use container names, not localhost
3. **Environment Variables**: Ensure all required vars are set
4. **Volume Mounts**: Verify all paths exist and are accessible
5. **Network**: All containers on `solar-network`
6. **Startup Order**: Database first, then main API, then dependent services

### 🚀 Performance Optimization
- Use SSD storage for database volumes
- Allocate sufficient RAM (16GB+ recommended)
- Enable Docker BuildKit for faster builds
- Use multi-stage Dockerfile for smaller images

---

---

## 🔌 API ENDPOINTS REFERENCE

### 🌐 Main API (Port 8100)
```bash
# Health & Status
GET  /health                    # Service health check
GET  /api/v1/status            # System status
GET  /api/v1/data/solax/latest # Latest solar data

# Predictions
POST /api/v1/predict           # Generate predictions
GET  /api/v1/predict/history   # Prediction history
GET  /app/forecasts/*.json     # Cached predictions

# Data Collection
GET  /api/v1/data/weather/latest # Latest weather data
GET  /api/v1/collect/solax      # Trigger SolaX collection
GET  /api/v1/collect/weather    # Trigger weather collection
```

### 💰 Enhanced Billing API (Port 8110)
```bash
# ROI Calculations
GET  /billing/enhanced/roi/system1    # System 1 ROI
GET  /billing/enhanced/roi/system2    # System 2 ROI
GET  /billing/enhanced/roi/combined   # Combined ROI

# Daily Costs
GET  /billing/enhanced/cost/system1   # System 1 daily cost
GET  /billing/enhanced/cost/system2   # System 2 daily cost

# Tariffs
GET  /billing/enhanced/tariffs        # Current tariff rates
GET  /billing/enhanced/summary/{system_id} # Complete summary
```

### 🔮 Unified Forecast API (Port 8120)
```bash
# Forecast Generation
POST /forecast/generate/{system_id}   # Generate forecast
GET  /forecast/cached/{system_id}     # Get cached forecast
GET  /forecast/multi-horizon          # Multi-horizon predictions

# Forecast Management
GET  /forecast/status                 # Forecast system status
POST /forecast/refresh                # Refresh all forecasts
```

### 📊 Charts API (Port 8103)
```bash
# Data Visualization
GET  /charts/production/{system_id}   # Production charts
GET  /charts/consumption/{system_id}  # Consumption charts
GET  /charts/weather                  # Weather charts
GET  /charts/predictions              # Prediction charts
```

### ⚡ GPU Prediction API (Port 8105)
```bash
# GPU-Accelerated Predictions
POST /gpu/predict/batch               # Batch predictions
POST /gpu/predict/realtime            # Real-time prediction
GET  /gpu/status                      # GPU status
GET  /gpu/performance                 # Performance metrics
```

---

## 🗄️ DATABASE FUNCTIONS & TRIGGERS

### 📊 Billing Functions
```sql
-- ROI Calculation Functions
calculate_system_roi(system_id TEXT, investment_cost DECIMAL)
calculate_annual_savings(system_id TEXT)
calculate_payback_period(system_id TEXT, investment_cost DECIMAL)

-- Energy Balance Functions
get_daily_production(system_id TEXT, target_date DATE)
get_daily_consumption(system_id TEXT, target_date DATE)
calculate_surplus_energy(system_id TEXT, target_date DATE)
calculate_grid_usage(system_id TEXT, target_date DATE)

-- Tariff Functions
get_current_tariff_rates()
calculate_energy_cost(consumption_kwh DECIMAL, tariff_type TEXT)
calculate_network_charges(consumption_kwh DECIMAL, quarterly_usage DECIMAL)
```

### 🔧 Data Validation Triggers
```sql
-- Automatic data validation
trigger_validate_solax_data()        # Validates SolaX data on insert
trigger_update_total_yield()         # Updates total yield calculations
trigger_log_data_changes()           # Logs all data modifications

-- Billing calculation triggers
trigger_update_billing_cache()       # Updates billing cache on data change
trigger_recalculate_roi()           # Recalculates ROI when needed
```

### 📈 Performance Indexes
```sql
-- Core data indexes
idx_solax_data_timestamp             # Fast timestamp queries
idx_solax_data_yield                 # Yield-based queries
idx_weather_data_timestamp           # Weather data access
idx_predictions_timestamp_system     # Prediction lookups

-- Billing indexes
idx_billing_calculations_date        # Daily billing queries
idx_roi_calculations_system          # ROI lookups
idx_tariff_rates_effective_date      # Tariff rate queries
```

---

## 🔄 AUTOMATED TASKS & SCHEDULING

### 📅 Scheduled Tasks (Cron-like)
```bash
# Daily at 06:00 EEST
- Generate daily predictions for both systems
- Update billing calculations
- Refresh ROI calculations
- Clean old log entries

# Every 30 seconds
- Collect SolaX real-time data
- Update system status
- Check for alerts

# Every hour
- Collect weather data
- Update weather forecasts
- Validate data integrity

# Weekly
- Generate performance reports
- Update model metrics
- Archive old predictions
```

### 🤖 Prediction Scheduler (Port 8106)
```bash
# Scheduler Configuration
SCHEDULE_TIME=06:00                   # Daily prediction time
GPU_PREDICTION_URL=http://solar-prediction-gpu:8105
PRODUCTION_SCRIPTS_URL=http://solar-prediction-main:8100
TELEGRAM_BOT_URL=http://solar-prediction-telegram:8106
RETRY_ATTEMPTS=3
RETRY_DELAY=30
```

---

## 🚨 ALERT SYSTEM CONFIGURATION

### 📢 Alert Types
```bash
# System Alerts
- Low battery SOC (< 20%) - Suppressed 06:00-12:00
- Data collection failures
- API endpoint failures
- Database connection issues

# Performance Alerts
- High prediction errors
- Model performance degradation
- Unusual energy patterns
- System downtime

# Billing Alerts
- ROI calculation errors
- Tariff rate changes
- Cost anomalies
```

### 🕐 Timezone Configuration
```bash
# CRITICAL: All timestamps in EEST (Europe/Athens)
TZ=Europe/Athens
WEATHER_TIMEZONE=Europe/Athens

# Alert suppression times (local time)
SOC_ALERT_SUPPRESS_START=06:00
SOC_ALERT_SUPPRESS_END=12:00
```

---

## 🔐 SECURITY CONSIDERATIONS

### 🛡️ Container Security
```bash
# Non-root user in containers
USER solarapp                        # All containers run as non-root
WORKDIR /app                         # Secure working directory

# File permissions
chmod 600 /home/<USER>/.cdsapirc   # API credentials
chown -R solarapp:solarapp /app      # Proper ownership
```

### 🔑 API Security
```bash
# Secret management
SECRET_KEY=production-secret-key-change-me  # Change in production!
ACCESS_TOKEN_EXPIRE_MINUTES=1440            # 24-hour tokens

# Network security
- All services on isolated Docker network
- No direct external access to database
- Health checks only on localhost
```

### 🌐 External API Security
```bash
# Rate limiting for external APIs
SOLAX_POLLING_INTERVAL=30            # Don't exceed API limits
WEATHER_POLLING_INTERVAL=3600        # Respect weather API limits
SOLAX_MAX_RETRIES=3                  # Prevent API abuse
```

---

## 📊 MONITORING & LOGGING

### 📝 Log Locations
```bash
# Container logs
docker logs solar-prediction-main    # Main API logs
docker logs solar-prediction-telegram # Bot logs
docker logs solar-prediction-db      # Database logs

# Application logs (bind mounted)
./logs/main_api.log                  # Main API application logs
./logs/telegram_bot.log              # Telegram bot logs
./logs/billing_service.log           # Billing calculations
./logs/prediction_scheduler.log      # Scheduled tasks
```

### 📈 Performance Monitoring
```bash
# System metrics
docker stats                         # Container resource usage
docker system df                     # Disk usage
docker system events                 # System events

# Application metrics
curl http://localhost:8130/health    # Unified health monitoring
curl http://localhost:8100/api/v1/status # System status
```

---

## 🔧 MAINTENANCE PROCEDURES

### 🧹 Regular Maintenance
```bash
# Weekly cleanup
docker system prune -f              # Remove unused containers/images
docker volume prune -f              # Remove unused volumes

# Database maintenance
VACUUM ANALYZE predictions;          # Optimize prediction table
VACUUM ANALYZE weather_data;         # Optimize weather table
VACUUM ANALYZE solax_data;           # Optimize solar data table

# Log rotation
find ./logs -name "*.log" -mtime +30 -delete  # Remove old logs
```

### 🔄 Update Procedures
```bash
# Service updates
docker-compose pull                  # Pull latest images
docker-compose up -d --force-recreate # Recreate containers

# Database migrations
docker exec -it solar-prediction-main python -c "from src.core.database import init_database; init_database()"
```

---

## 🎯 PRODUCTION DEPLOYMENT NOTES

### ⚠️ Critical Configuration Changes
```bash
# MUST CHANGE in production:
SECRET_KEY=your-secure-secret-key-here
POSTGRES_PASSWORD=your-secure-db-password

# VERIFY these settings:
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=info
```

### 🚀 Performance Tuning
```bash
# Database optimization
shared_buffers = 256MB               # PostgreSQL memory
max_connections = 100                # Connection limit
work_mem = 4MB                       # Query memory

# Container resources
deploy:
  resources:
    limits:
      memory: 2G                     # Per container memory limit
      cpus: '1.0'                    # CPU limit
```

---

## 🚀 STEP-BY-STEP DEPLOYMENT EXAMPLE

### 📋 Complete Deployment Script
```bash
#!/bin/bash
# Solar Prediction System Deployment Script

echo "🚀 Starting Solar Prediction System deployment..."

# Step 1: Verify prerequisites
echo "📋 Checking prerequisites..."
docker --version || { echo "❌ Docker not installed"; exit 1; }
docker-compose --version || { echo "❌ Docker Compose not installed"; exit 1; }

# Step 2: Load Docker images
echo "📦 Loading Docker images..."
cd /path/to/backup/docker_images/
for image in *.tar.gz; do
    echo "Loading $image..."
    docker load < "$image"
done

# Step 3: Restore Docker volumes
echo "💾 Restoring Docker volumes..."
cd ../docker_volumes/
for volume in *.tar.gz; do
    volume_name=$(basename "$volume" .tar.gz)
    echo "Restoring volume: $volume_name"
    docker volume create "$volume_name"
    docker run --rm -v "$volume_name":/volume -v "$(pwd)":/backup alpine sh -c "cd /volume && tar xzf /backup/$volume"
done

# Step 4: Restore database
echo "🗄️ Restoring database..."
cd ../database_backup/
docker run --rm -v postgres_data:/var/lib/postgresql/data -v "$(pwd)":/backup postgres:16-alpine sh -c "
    initdb -D /var/lib/postgresql/data &&
    pg_ctl -D /var/lib/postgresql/data start &&
    createdb solar_prediction &&
    gunzip -c /backup/complete_database_dump_*.sql.gz | psql solar_prediction
"

# Step 5: Copy configuration files
echo "⚙️ Setting up configuration..."
cd ../project_files/
cp docker-compose.yml /target/deployment/
cp .env /target/deployment/

# Step 6: Start services
echo "🐳 Starting Docker services..."
cd /target/deployment/
docker-compose up -d

# Step 7: Wait for services to be healthy
echo "⏳ Waiting for services to be healthy..."
sleep 60

# Step 8: Verify deployment
echo "✅ Verifying deployment..."
./verify_deployment.sh

echo "🎉 Deployment completed successfully!"
```

### 🔍 Deployment Verification Script
```bash
#!/bin/bash
# verify_deployment.sh

echo "🔍 Verifying Solar Prediction System deployment..."

# Check all containers are running
echo "📊 Checking container status..."
containers=(
    "solar-prediction-main:8100"
    "solar-prediction-charts:8103"
    "solar-prediction-gpu:8105"
    "solar-prediction-scheduler:8106"
    "solar-prediction-alerts:8107"
    "solar-prediction-config:8108"
    "solar-prediction-billing:8110"
    "solar-prediction-forecast:8120"
    "solar-prediction-telegram"
    "solar-prediction-web:8080"
    "solar-prediction-db:5433"
    "solar-prediction-cache:6380"
)

for container in "${containers[@]}"; do
    name=$(echo $container | cut -d: -f1)
    port=$(echo $container | cut -d: -f2)

    if docker ps | grep -q "$name"; then
        echo "✅ $name is running"

        if [ "$port" != "$name" ]; then
            if curl -f -s "http://localhost:$port/health" > /dev/null 2>&1; then
                echo "✅ $name health check passed"
            else
                echo "❌ $name health check failed"
            fi
        fi
    else
        echo "❌ $name is not running"
    fi
done

# Test database connection
echo "🗄️ Testing database connection..."
if docker exec solar-prediction-db psql -U postgres -d solar_prediction -c "SELECT COUNT(*) FROM solax_data;" > /dev/null 2>&1; then
    echo "✅ Database connection successful"
else
    echo "❌ Database connection failed"
fi

# Test Telegram bot
echo "🤖 Testing Telegram bot..."
if docker logs solar-prediction-telegram 2>&1 | grep -q "Bot started successfully"; then
    echo "✅ Telegram bot started successfully"
else
    echo "❌ Telegram bot startup failed"
fi

# Test API endpoints
echo "🌐 Testing API endpoints..."
apis=(
    "8100:/health"
    "8103:/health"
    "8105:/health"
    "8110:/billing/enhanced/roi/system1"
    "8120:/forecast/status"
)

for api in "${apis[@]}"; do
    port=$(echo $api | cut -d: -f1)
    endpoint=$(echo $api | cut -d: -f2)

    if curl -f -s "http://localhost:$port$endpoint" > /dev/null 2>&1; then
        echo "✅ API $port$endpoint responding"
    else
        echo "❌ API $port$endpoint not responding"
    fi
done

echo "🎯 Verification completed!"
```

---

## 🚨 TROUBLESHOOTING SCENARIOS

### 🔧 Scenario 1: Telegram Bot Wrong Script
**Problem**: Telegram bot runs wrong script on startup

**Symptoms**:
```bash
docker logs solar-prediction-telegram
# Shows: Running telegram_bot_service.py instead of greek_telegram_bot.py
```

**Solution**:
```bash
# Check SERVICE_TYPE environment variable
docker exec solar-prediction-telegram env | grep SERVICE_TYPE

# Should be: SERVICE_TYPE=telegram-bot
# If wrong, recreate container with correct env var

# Verify correct script is executed
docker exec solar-prediction-telegram ps aux | grep python
# Should show: scripts/frontend_system/greek_telegram_bot.py
```

### 🔧 Scenario 2: Database Connection Issues
**Problem**: Services can't connect to database

**Symptoms**:
```bash
# Service logs show:
connection to server at "localhost:5433" failed
```

**Root Cause**: Using external port instead of internal

**Solution**:
```bash
# ❌ WRONG configuration:
DATABASE_URL=postgresql://postgres:postgres@localhost:5433/solar_prediction

# ✅ CORRECT configuration:
DATABASE_URL=********************************************/solar_prediction

# Update environment variables and restart containers
```

### 🔧 Scenario 3: API Aliases Not Working
**Problem**: Services can't find each other

**Symptoms**:
```bash
# Logs show:
Failed to connect to enhanced-billing:8110
Name or service not known
```

**Solution**:
```bash
# Verify all containers are on the same network
docker network ls
docker network inspect solar-network

# Check container network aliases
docker inspect solar-prediction-billing | grep -A 10 "Networks"

# Should show:
# "Aliases": ["enhanced-billing", "solar-prediction-billing"]

# If missing, recreate containers with proper network configuration
```

### 🔧 Scenario 4: ROI Calculations Return 0%
**Problem**: ROI API returns 0% instead of correct values

**Symptoms**:
```bash
curl http://localhost:8110/billing/enhanced/roi/system1
# Returns: {"roi_percentage": 0.0}
```

**Solution**:
```bash
# Check if database has data
docker exec solar-prediction-db psql -U postgres -d solar_prediction -c "
SELECT COUNT(*) FROM solax_data WHERE yield_today > 0;
"

# Check billing functions exist
docker exec solar-prediction-db psql -U postgres -d solar_prediction -c "
\df calculate_system_roi
"

# If functions missing, restore database properly
# If data missing, check SolaX data collection
```

### 🔧 Scenario 5: Health Checks Failing
**Problem**: All health checks return 503

**Symptoms**:
```bash
curl http://localhost:8100/health
# Returns: 503 Service Unavailable
```

**Solution**:
```bash
# Check if main dependencies are ready
docker exec solar-prediction-main python -c "
import psycopg2
conn = psycopg2.connect('********************************************/solar_prediction')
print('Database OK')
"

# Check if required files exist
docker exec solar-prediction-main ls -la /app/scripts/production/production_api_server.py

# Check container logs for startup errors
docker logs solar-prediction-main --tail 50
```

---

## 📋 DEPLOYMENT CHECKLIST TEMPLATE

### ✅ Pre-Deployment Verification
```bash
# Hardware Requirements
[ ] CPU: 4+ cores
[ ] RAM: 16GB+ available
[ ] Storage: 50GB+ free space
[ ] Network: Internet connectivity

# Software Requirements
[ ] Docker 20.10+ installed
[ ] Docker Compose 2.0+ installed
[ ] curl command available
[ ] User has docker group permissions

# Backup Files
[ ] Docker images directory exists (15 .tar.gz files)
[ ] Docker volumes directory exists (15 .tar.gz files)
[ ] Database backup exists (2 .sql.gz files)
[ ] Project files directory exists (32,000+ files)
[ ] Configuration files present (docker-compose.yml, .env)
```

### ✅ Deployment Execution
```bash
# Image Loading
[ ] All 15 Docker images loaded successfully
[ ] No image loading errors in logs
[ ] Images visible in: docker images

# Volume Restoration
[ ] All 15 volumes created successfully
[ ] postgres_data volume contains database files
[ ] redis_data volume restored
[ ] No volume restoration errors

# Database Restoration
[ ] PostgreSQL container starts successfully
[ ] Database dump restored without errors
[ ] All tables present and populated
[ ] Billing functions installed correctly

# Service Startup
[ ] All 11 services start successfully
[ ] No container restart loops
[ ] All containers show "healthy" status
[ ] Network connectivity between services
```

### ✅ Post-Deployment Testing
```bash
# Health Checks
[ ] Main API health check passes (8100)
[ ] All service health checks pass
[ ] Database connection test passes
[ ] Redis connection test passes

# API Functionality
[ ] ROI calculation returns correct values (not 0%)
[ ] Weather data API responds
[ ] Prediction endpoints work
[ ] Charts API generates data

# Telegram Bot
[ ] Bot responds to /start command
[ ] All 10 menu options work
[ ] Greek/English language switching works
[ ] ROI submenu functions correctly

# Data Collection
[ ] SolaX data collection working
[ ] Weather data collection working
[ ] Prediction scheduler running
[ ] Alert system operational
```

### ✅ Production Readiness
```bash
# Security
[ ] Default passwords changed
[ ] SECRET_KEY updated for production
[ ] File permissions set correctly
[ ] No sensitive data in logs

# Performance
[ ] All services respond within 2 seconds
[ ] Database queries optimized
[ ] Log rotation configured
[ ] Resource limits set appropriately

# Monitoring
[ ] Health monitoring active
[ ] Alert system configured
[ ] Log aggregation working
[ ] Performance metrics available
```

---

## 📞 SUPPORT & MAINTENANCE

### 🆘 Emergency Procedures
```bash
# Complete system restart
docker-compose down
docker-compose up -d

# Database emergency recovery
docker exec solar-prediction-db pg_dump -U postgres solar_prediction > emergency_backup.sql

# Service-specific restart
docker-compose restart solar-prediction-main
docker-compose restart solar-prediction-telegram

# Check system resources
docker stats --no-stream
df -h
free -h
```

### 📧 Contact Information
- **System Documentation**: This guide + RESTORE_INSTRUCTIONS.md
- **Log Locations**: ./logs/ directory + docker logs commands
- **Configuration Files**: docker-compose.yml, .env, start.sh
- **Database Schema**: Check database_backup/schema.sql

### 🔄 Regular Maintenance Schedule
```bash
# Daily (automated)
- Health check monitoring
- Log rotation
- Data collection verification

# Weekly (manual)
- Container resource usage review
- Database performance check
- Security update review

# Monthly (manual)
- Full system backup
- Performance optimization
- Documentation updates
```

**📞 Support**: For deployment issues, follow this troubleshooting guide and verify all environment variables are correctly set according to the specifications above.
