#!/usr/bin/env python3
"""
Enhanced Billing Service - FIXED VERSION
Calculates accurate daily costs using REAL consumption data from database
"""

import os
import logging
from datetime import datetime, date
from typing import Dict, Optional
from dataclasses import dataclass

from fastapi import FastAPI, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="Enhanced Solar Billing Service - FIXED",
    description="Accurate billing calculations using real consumption data",
    version="2.0.0"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@dataclass
class TariffRates:
    """Current tariff rates for Greece"""
    day_energy: float = 0.075      # €/kWh - Day energy rate
    night_energy: float = 0.055    # €/kWh - Night energy rate  
    network_tier1: float = 0.05    # €/kWh - Network charge tier 1
    network_tier2: float = 0.06    # €/kWh - Network charge tier 2
    etmear: float = 0.025          # €/kWh - ETMEAR charge
    surplus_rate: float = 0.075    # €/kWh - Surplus compensation
    feed_in_tariff: float = 0.0    # €/kWh - Net metering (no direct payment)

class EnhancedBillingService:
    """Enhanced billing service with REAL consumption data"""
    
    def __init__(self):
        self.rates = TariffRates()
        logger.info("🔧 Enhanced Billing Service (FIXED) initialized")
    
    def get_network_charge_per_kwh(self, quarterly_consumption: float) -> float:
        """Calculate network charge based on quarterly consumption tiers"""
        if quarterly_consumption <= 1200:
            return self.rates.network_tier1
        else:
            return self.rates.network_tier2
    
    async def get_system_balance_fixed(self, system_id: str, target_date: date) -> Dict:
        """Get energy balance using REAL consumption data from database"""
        try:
            import psycopg2
            from psycopg2.extras import RealDictCursor

            # Database config for Docker environment
            DB_CONFIG = {
                'host': 'postgres',
                'database': 'solar_prediction',
                'user': 'postgres',
                'password': 'postgres'
            }

            conn = psycopg2.connect(**DB_CONFIG)
            cur = conn.cursor(cursor_factory=RealDictCursor)

            # Get table name based on system
            table_name = 'solax_data' if system_id == 'system1' else 'solax_data2'

            # Get REAL daily data using PRE-CALCULATED billing fields
            cur.execute(f"""
                SELECT
                    MAX(yield_today) as production_today,
                    SUM(grid_usage_kwh) as daily_grid_consumption,
                    SUM(CASE WHEN billing_net_metering_credit > 0 THEN billing_net_metering_credit / 0.075 ELSE 0 END) as daily_feedin,
                    MAX(consume_energy) as total_consumption,
                    MAX(feedin_energy) as total_feedin,
                    COUNT(*) as records_count,
                    SUM(billing_cost) as total_daily_cost
                FROM {table_name}
                WHERE DATE(upload_time) = %s
            """, (target_date,))

            result = cur.fetchone()
            
            if not result or result['records_count'] == 0:
                conn.close()
                return {
                    "status": "no_data",
                    "date": str(target_date),
                    "system_id": system_id,
                    "error": "No data available for this date"
                }

            production = float(result['production_today'] or 0)
            daily_grid_consumption = float(result['daily_grid_consumption'] or 0)
            daily_feedin = float(result['daily_feedin'] or 0)
            total_consumption = float(result['total_consumption'] or 0)
            total_feedin = float(result['total_feedin'] or 0)

            # Calculate Net Metering balance (total feedin - total consumption)
            net_metering_balance = total_feedin - total_consumption

            # Calculate self consumption from solar (production that didn't go to grid)
            self_consumption_from_solar = max(0, production - daily_feedin)
            
            # Total daily consumption = grid consumption + self consumption from solar
            total_daily_consumption = daily_grid_consumption + self_consumption_from_solar

            # Calculate consumption rates
            if production > 0:
                self_consumption_rate = self_consumption_from_solar / production
            else:
                self_consumption_rate = 0.0

            conn.close()

            return {
                "status": "calculated",
                "date": str(target_date),
                "system_id": system_id,
                "production": round(production, 2),
                "consumption": round(total_daily_consumption, 2),
                "surplus": round(daily_feedin, 2),  # What actually went to grid
                "grid_usage": round(daily_grid_consumption, 2),  # What we consumed from grid
                "self_consumption": round(self_consumption_from_solar, 2),
                "net_metering_credit": round(net_metering_balance, 1),
                "has_net_credit": net_metering_balance > 0,
                "self_consumption_rate": round(self_consumption_rate, 4),
                "actual_grid_consumption": round(daily_grid_consumption, 2)
            }

        except Exception as e:
            logger.error(f"Error in get_system_balance_fixed: {e}")
            return {
                "status": "error",
                "date": str(target_date),
                "system_id": system_id,
                "error": str(e)
            }
    
    async def calculate_daily_cost_fixed(self, system_id: str, target_date: date) -> Dict:
        """Calculate daily electricity cost using REAL consumption data"""
        try:
            # Get energy balance with REAL data
            balance = await self.get_system_balance_fixed(system_id, target_date)
            
            if balance.get('status') in ['error', 'no_data']:
                raise Exception(balance.get('error', 'Unknown error'))
            
            # Get quarterly consumption for network charge calculation
            quarterly_consumption = 1200  # Estimated quarterly consumption
            
            # Extract values from balance
            production = balance['production']
            grid_usage = balance['grid_usage']  # REAL grid consumption
            surplus = balance['surplus']  # REAL feedin to grid
            has_net_credit = balance['has_net_credit']
            
            # Calculate costs using REAL data
            if has_net_credit:
                # We have net metering credit - NO energy cost
                energy_cost = 0.0
                actual_grid_consumption = grid_usage  # Still pay network charges
            else:
                # No net credit - pay for grid consumption
                actual_grid_consumption = grid_usage
                energy_cost = actual_grid_consumption * self.rates.day_energy
            
            # Network charges for all grid usage (always paid)
            network_charge_rate = self.get_network_charge_per_kwh(quarterly_consumption)
            network_cost = grid_usage * network_charge_rate
            
            # ETMEAR charge (always paid)
            etmear_cost = grid_usage * self.rates.etmear
            
            # Surplus compensation
            surplus_value = surplus * self.rates.surplus_rate
            
            # Total cost
            total_cost = energy_cost + network_cost + etmear_cost
            net_cost = total_cost - surplus_value
            
            return {
                "status": "success",
                "system_id": system_id,
                "date": str(target_date),
                "energy_cost": round(energy_cost, 4),
                "network_cost": round(network_cost, 4),
                "etmear_cost": round(etmear_cost, 4),
                "total_cost": round(total_cost, 4),
                "surplus_value": round(surplus_value, 4),
                "net_cost": round(net_cost, 4),
                "breakdown": {
                    "production": production,
                    "consumption": balance['consumption'],
                    "surplus": surplus,
                    "grid_usage": grid_usage,
                    "actual_grid_consumption": actual_grid_consumption
                },
                "tariff_info": {
                    "period": "summer",
                    "energy_rate": self.rates.day_energy,
                    "network_tier": network_charge_rate,
                    "etmear_rate": self.rates.etmear,
                    "quarterly_consumption": quarterly_consumption,
                    "has_net_credit": has_net_credit
                }
            }
            
        except Exception as e:
            logger.error(f"Error calculating daily cost: {e}")
            return {
                "status": "error",
                "message": str(e)
            }

# Initialize service
billing_service = EnhancedBillingService()

@app.get("/billing/enhanced/cost/{system_id}")
async def get_enhanced_daily_cost_fixed(system_id: str, date: str = Query(None)):
    """Get FIXED daily cost calculation using REAL consumption data"""
    try:
        if system_id not in ['system1', 'system2', '1', '2']:
            raise HTTPException(status_code=400, detail="Invalid system_id")

        # Normalize system_id
        if system_id in ['1', 'system1']:
            system_id = 'system1'
        else:
            system_id = 'system2'

        target_date = datetime.strptime(date, "%Y-%m-%d").date() if date else datetime.now().date()

        # Calculate daily cost using FIXED method
        result = await billing_service.calculate_daily_cost_fixed(system_id, target_date)
        
        if result.get('status') == 'error':
            raise HTTPException(status_code=500, detail=result.get('message'))

        return {
            "status": "success",
            "system_id": system_id,
            "date": str(target_date),
            "cost_breakdown": {
                "energy_cost": result["energy_cost"],
                "network_cost": result["network_cost"],
                "etmear_cost": result["etmear_cost"],
                "total_cost": result["total_cost"],
                "surplus_value": result["surplus_value"],
                "net_cost": result["net_cost"]
            },
            "energy_data": result["breakdown"],
            "tariff_info": result["tariff_info"]
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to calculate daily cost: {e}")

@app.get("/billing/enhanced/balance/{system_id}")
async def get_system_balance_fixed(system_id: str, date: str = Query(None)):
    """Get FIXED system balance using REAL consumption data"""
    try:
        if system_id not in ['system1', 'system2', '1', '2']:
            raise HTTPException(status_code=400, detail="Invalid system_id")

        # Normalize system_id
        if system_id in ['1', 'system1']:
            system_id = 'system1'
        else:
            system_id = 'system2'

        target_date = datetime.strptime(date, "%Y-%m-%d").date() if date else datetime.now().date()

        # Get balance using FIXED method
        result = await billing_service.get_system_balance_fixed(system_id, target_date)
        
        return result

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get system balance: {e}")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "enhanced_billing_service_fixed",
        "version": "2.0.0",
        "features": ["real_consumption_data", "accurate_grid_usage", "net_metering_balance"],
        "tariff_rates": {
            "day_energy": billing_service.rates.day_energy,
            "network_tier1": billing_service.rates.network_tier1,
            "etmear": billing_service.rates.etmear
        },
        "timestamp": datetime.now().isoformat()
    }

if __name__ == "__main__":
    port = int(os.getenv("BILLING_PORT", 8110))  # Use production port
    print(f"🚀 Starting Enhanced Billing Service (FIXED) on port {port}...")
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=port,
        reload=False,
        log_level="info"
    )
