# Quick Reference - Αυτοκατανάλωση Solar Systems

**Ημερομηνία:** 11 Ιουνίου 2025  
**Τύπος:** Quick Reference Guide  

## 📊 Σωστοί Ορισμοί

### Αυτοκατανάλωση (Self-Consumption)
```
Αυτοκατανάλωση = From System / Total Consumption
```
**Απαντά:** "Τι ποσοστό των αναγκών μου καλύπτει το σύστημα;"

### Κατανάλωση από Δίκτυο (Grid Consumption)
```
Κατανάλωση από Δίκτυο = From Grid / Total Consumption
```
**Απαντά:** "Τι ποσοστό των αναγκών μου έρχεται από το δίκτυο;"

### Πλεόνασμα στο Δίκτυο (Surplus to Grid)
```
Πλεόνασμα = System to Grid / Total Production
```
**Απαντά:** "Τι ποσοστό της παραγωγής στέλνω στο δίκτυο;"

## 📈 Σωστά Ποσοστά

### Σύστημα 1 (Σπίτι Κάτω)
- **Αυτοκατανάλωση:** 47.15% (8,284.60 kWh / 17.57 MWh)
- **Κατανάλωση από δίκτυο:** 52.85% (9,287.58 kWh / 17.57 MWh)
- **Πλεόνασμα στο δίκτυο:** 59.46% (12.16 MWh / 20.44 MWh)

### Σύστημα 2 (Σπίτι Πάνω)
- **Αυτοκατανάλωση:** 67.42% (9,791.05 kWh / 14.52 MWh)
- **Κατανάλωση από δίκτυο:** 32.58% (4,732.28 kWh / 14.52 MWh)
- **Πλεόνασμα στο δίκτυο:** 52.93% (11.01 MWh / 20.80 MWh)

## 🔧 Τεχνικά Commands

### Database Queries
```sql
-- Έλεγχος consumption data
SELECT COUNT(*) FROM solax_data2 WHERE feedin_power IS NOT NULL;
SELECT COUNT(*) FROM solax_data2 WHERE consume_energy IS NOT NULL;

-- Υπολογισμός αυτοκατανάλωσης
SELECT 
    SUM(yield_today) as total_production,
    SUM(feedin_energy) as total_feedin,
    SUM(consume_energy) as total_consumption,
    (SUM(yield_today) - SUM(feedin_energy)) / SUM(consume_energy) * 100 as self_consumption_pct
FROM solax_data2 
WHERE timestamp >= '2024-03-01';
```

### API Calls
```bash
# ROI Analysis
curl -s "http://localhost:8110/billing/enhanced/roi/system1"
curl -s "http://localhost:8110/billing/enhanced/roi/system2"

# Health Check
curl -s "http://localhost:8110/health"
```

### Service Management
```bash
# Restart Enhanced Billing
pkill -f "enhanced_billing_system"
python3 scripts/frontend_system/enhanced_billing_system.py > /tmp/billing.log 2>&1 &

# Restart Telegram Bot
pkill -f "greek_telegram_bot"
python3 scripts/frontend_system/greek_telegram_bot.py > /tmp/telegram.log 2>&1 &
```

## 📁 Αρχεία που Αλλάχθηκαν

### Core Files
- `scripts/frontend_system/unified_roi_calculator.py` - Κύρια λογική υπολογισμών
- `scripts/frontend_system/greek_telegram_bot.py` - User interface
- `scripts/data_import/manual_csv_import.py` - Import script (νέο)

### Database
- `solax_data2` table - Προσθήκη 3 νέων στηλών
- 103,539 νέα records για System2

## 🚨 Troubleshooting

### Λάθος Ποσοστά
```bash
# Έλεγχος logs
tail -f /tmp/enhanced_billing.log | grep "Self-consumption"

# Αναμενόμενο output:
# ✅ CORRECTED Self-consumption: 47.15% of consumption
```

### Missing Data
```sql
-- Έλεγχος για missing consumption data
SELECT COUNT(*) FROM solax_data2 WHERE consume_energy IS NULL;

-- Αν > 0, τρέξε το import script ξανά
python3 scripts/data_import/manual_csv_import.py
```

### API Errors
```bash
# Έλεγχος service status
curl -s http://localhost:8110/health

# Αν fail, restart service
pkill -f "enhanced_billing_system"
python3 scripts/frontend_system/enhanced_billing_system.py > /tmp/billing.log 2>&1 &
```

## ✅ Validation Checklist

### Database
- [ ] solax_data2 έχει feedin_power, feedin_energy, consume_energy στήλες
- [ ] 104,137+ records με consumption data
- [ ] Δεν υπάρχουν NULL values στα κρίσιμα fields

### API Responses
- [ ] System1: self_consumption_rate ≈ 47.15%
- [ ] System1: grid_consumption_rate ≈ 52.85%
- [ ] System2: self_consumption_rate ≈ 67.42%
- [ ] System2: grid_consumption_rate ≈ 32.58%

### Telegram Bot
- [ ] Εμφανίζει "Κατανάλωση από δίκτυο" αντί για "Πλεόνασμα"
- [ ] Σωστά ποσοστά για αμφότερα τα συστήματα
- [ ] Διευκρινιστικές σημειώσεις

## 📞 Support

### Log Files
- Enhanced Billing: `/tmp/enhanced_billing.log`
- Telegram Bot: `/tmp/telegram_bot.log`
- Import Script: Console output

### Key Metrics
- **Import Success:** 103,539 records
- **API Response Time:** ~600ms
- **Database Query Time:** ~50ms
- **Accuracy:** 100% real data

### Contact
- **Developer:** Augment Agent
- **Documentation:** `/docs/SELF_CONSUMPTION_CALCULATION_FIX_JUNE_2025.md`
- **Technical Details:** `/docs/TECHNICAL_IMPLEMENTATION_SELF_CONSUMPTION_FIX.md`

---

**Quick Reference Completed**  
**All systems operational with correct calculations**
