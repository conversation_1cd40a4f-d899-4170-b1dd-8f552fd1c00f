#!/bin/bash

# Copy backup to external drive
echo "🚀 Copying backup to external drive..."

SOURCE="/home/<USER>/solar-prediction-system-backup/20250619_223156"
DEST="/media/grlv/Backup2/solar-prediction-system-backup/"

echo "📂 Source: $SOURCE"
echo "💾 Destination: $DEST"

# Check if source exists
if [ ! -d "$SOURCE" ]; then
    echo "❌ Source directory does not exist: $SOURCE"
    exit 1
fi

# Check if destination is accessible
if [ ! -w "/media/grlv/Backup2" ]; then
    echo "❌ Cannot write to destination: /media/grlv/Backup2"
    echo "💡 Trying to create destination with current user permissions..."
    mkdir -p "$DEST" 2>/dev/null || {
        echo "❌ Failed to create destination directory"
        echo "📋 Please run: sudo mkdir -p '$DEST' && sudo chown -R grlv:grlv '$DEST'"
        exit 1
    }
fi

# Copy the backup
echo "📋 Copying backup (this may take a while)..."
rsync -av --progress "$SOURCE/" "$DEST/20250619_223156/"

if [ $? -eq 0 ]; then
    echo "✅ Backup copied successfully!"
    echo "📂 Backup location: $DEST/20250619_223156/"
    echo "💾 Total size: $(du -sh "$DEST/20250619_223156/" | cut -f1)"
    
    # Verify the copy
    echo "🔍 Verifying backup integrity..."
    cd "$DEST/20250619_223156/"
    ./verify_backup.sh
    
    echo ""
    echo "🎉 BACKUP COPY COMPLETED!"
    echo "================================"
    echo "📂 Your Solar Prediction System backup is now available at:"
    echo "   $DEST/20250619_223156/"
    echo ""
    echo "📋 To restore on another machine:"
    echo "1. Copy the entire backup folder to the target machine"
    echo "2. Follow the instructions in RESTORE_INSTRUCTIONS.md"
    echo ""
    echo "✅ Ready for migration to another machine!"
else
    echo "❌ Failed to copy backup"
    exit 1
fi
