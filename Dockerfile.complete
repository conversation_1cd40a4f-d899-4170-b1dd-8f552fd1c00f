# Solar Prediction Project - Complete Self-Contained Docker Image
# Includes PostgreSQL, all project files, and pre-loaded database data

FROM python:3.11-slim

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONPATH="/app:/app/src" \
    PYTHONRECURSIONLIMIT=10000 \
    TZ=Europe/Athens \
    PGDATA=/var/lib/postgresql/data \
    POSTGRES_DB=solar_prediction \
    POSTGRES_USER=postgres \
    POSTGRES_PASSWORD=postgres

# Install system dependencies
RUN apt-get update && apt-get install -y \
    # PostgreSQL
    postgresql \
    postgresql-client \
    postgresql-contrib \
    # Basic tools
    curl \
    wget \
    git \
    supervisor \
    netcat-openbsd \
    # Build dependencies
    gcc \
    g++ \
    make \
    pkg-config \
    libpq-dev \
    libgomp1 \
    # Timezone
    tzdata \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Set timezone
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Create application user
RUN groupadd -r solarapp && useradd -r -g solarapp solarapp

# Set working directory
WORKDIR /app

# Create necessary directories
RUN mkdir -p \
    /app/logs \
    /app/models \
    /app/data \
    /app/static \
    /app/templates \
    /app/scripts \
    /app/docs \
    /app/sql \
    /app/src \
    /app/backup_20250615_164048 \
    /var/lib/postgresql/data \
    /var/log/postgresql \
    /var/run/postgresql \
    && chown -R postgres:postgres /var/lib/postgresql \
    && chown -R postgres:postgres /var/log/postgresql \
    && chown -R postgres:postgres /var/run/postgresql \
    && chown -R solarapp:solarapp /app

# Copy ALL project files (complete copy)
COPY --chown=solarapp:solarapp . /app/

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip && \
    if [ -f /app/requirements.txt ]; then \
        pip install --no-cache-dir -r /app/requirements.txt; \
    elif [ -f /app/requirements.docker.txt ]; then \
        pip install --no-cache-dir -r /app/requirements.docker.txt; \
    fi

# Initialize PostgreSQL
USER postgres
RUN /usr/lib/postgresql/15/bin/initdb -D /var/lib/postgresql/data --encoding=UTF8 --lc-collate=C --lc-ctype=C

# Configure PostgreSQL
RUN echo "host all all 0.0.0.0/0 md5" >> /var/lib/postgresql/data/pg_hba.conf && \
    echo "listen_addresses='*'" >> /var/lib/postgresql/data/postgresql.conf && \
    echo "port = 5432" >> /var/lib/postgresql/data/postgresql.conf && \
    echo "max_connections = 100" >> /var/lib/postgresql/data/postgresql.conf && \
    echo "shared_buffers = 128MB" >> /var/lib/postgresql/data/postgresql.conf

# Start PostgreSQL temporarily to load data
RUN /usr/lib/postgresql/15/bin/pg_ctl -D /var/lib/postgresql/data start && \
    sleep 5 && \
    createdb solar_prediction && \
    # Create current backup from host database during build
    echo "Creating current backup from host database..." && \
    PGPASSWORD=postgres pg_dump -h host.docker.internal -p 5433 -U postgres -d solar_prediction > /tmp/current_backup.sql 2>/dev/null || \
    echo "Could not connect to host database, using fallback backup..." && \
    # Load data (prioritize current backup)
    if [ -f /tmp/current_backup.sql ] && [ -s /tmp/current_backup.sql ]; then \
        echo "Loading current database backup..." && \
        psql -d solar_prediction -f /tmp/current_backup.sql; \
    elif [ -f /app/current_backup.sql ]; then \
        echo "Loading current database backup..." && \
        psql -d solar_prediction -f /app/current_backup.sql; \
    elif [ -f /app/backup_20250615_164048/complete_database_backup.sql ]; then \
        echo "Loading fallback database backup..." && \
        psql -d solar_prediction -f /app/backup_20250615_164048/complete_database_backup.sql; \
    elif [ -f /app/scripts/final_backup_20250616_011741.sql ]; then \
        echo "Loading legacy database backup..." && \
        psql -d solar_prediction -f /app/scripts/final_backup_20250616_011741.sql; \
    else \
        echo "No database backup found!"; \
    fi && \
    rm -f /tmp/current_backup.sql && \
    /usr/lib/postgresql/15/bin/pg_ctl -D /var/lib/postgresql/data stop

# Switch back to root for supervisor setup
USER root

# Create supervisor configuration
RUN mkdir -p /etc/supervisor/conf.d && \
    echo '[supervisord]' > /etc/supervisor/conf.d/supervisord.conf && \
    echo 'nodaemon=true' >> /etc/supervisor/conf.d/supervisord.conf && \
    echo 'user=root' >> /etc/supervisor/conf.d/supervisord.conf && \
    echo 'logfile=/var/log/supervisor/supervisord.log' >> /etc/supervisor/conf.d/supervisord.conf && \
    echo 'pidfile=/var/run/supervisord.pid' >> /etc/supervisor/conf.d/supervisord.conf && \
    echo '' >> /etc/supervisor/conf.d/supervisord.conf && \
    echo '[program:postgresql]' >> /etc/supervisor/conf.d/supervisord.conf && \
    echo 'command=/usr/lib/postgresql/15/bin/postgres -D /var/lib/postgresql/data' >> /etc/supervisor/conf.d/supervisord.conf && \
    echo 'user=postgres' >> /etc/supervisor/conf.d/supervisord.conf && \
    echo 'autostart=true' >> /etc/supervisor/conf.d/supervisord.conf && \
    echo 'autorestart=true' >> /etc/supervisor/conf.d/supervisord.conf && \
    echo 'redirect_stderr=true' >> /etc/supervisor/conf.d/supervisord.conf && \
    echo 'stdout_logfile=/var/log/postgresql/postgresql.log' >> /etc/supervisor/conf.d/supervisord.conf && \
    echo '' >> /etc/supervisor/conf.d/supervisord.conf && \
    echo '[program:solar-prediction-app]' >> /etc/supervisor/conf.d/supervisord.conf && \
    echo 'command=python /app/scripts/production_app.py' >> /etc/supervisor/conf.d/supervisord.conf && \
    echo 'directory=/app' >> /etc/supervisor/conf.d/supervisord.conf && \
    echo 'user=solarapp' >> /etc/supervisor/conf.d/supervisord.conf && \
    echo 'autostart=true' >> /etc/supervisor/conf.d/supervisord.conf && \
    echo 'autorestart=true' >> /etc/supervisor/conf.d/supervisord.conf && \
    echo 'redirect_stderr=true' >> /etc/supervisor/conf.d/supervisord.conf && \
    echo 'stdout_logfile=/app/logs/production_app.log' >> /etc/supervisor/conf.d/supervisord.conf && \
    echo 'environment=DATABASE_URL="postgresql://postgres:postgres@localhost:5432/solar_prediction",PYTHONPATH="/app:/app/src"' >> /etc/supervisor/conf.d/supervisord.conf

# Create startup script
RUN echo '#!/bin/bash' > /app/start_complete_system.sh && \
    echo 'set -e' >> /app/start_complete_system.sh && \
    echo '' >> /app/start_complete_system.sh && \
    echo 'echo "🚀 Starting Solar Prediction Complete System..."' >> /app/start_complete_system.sh && \
    echo '' >> /app/start_complete_system.sh && \
    echo '# Ensure PostgreSQL data directory permissions' >> /app/start_complete_system.sh && \
    echo 'chown -R postgres:postgres /var/lib/postgresql/data' >> /app/start_complete_system.sh && \
    echo 'chmod 700 /var/lib/postgresql/data' >> /app/start_complete_system.sh && \
    echo '' >> /app/start_complete_system.sh && \
    echo '# Ensure application permissions' >> /app/start_complete_system.sh && \
    echo 'chown -R solarapp:solarapp /app/logs' >> /app/start_complete_system.sh && \
    echo 'chown -R solarapp:solarapp /app/models' >> /app/start_complete_system.sh && \
    echo 'chown -R solarapp:solarapp /app/data' >> /app/start_complete_system.sh && \
    echo '' >> /app/start_complete_system.sh && \
    echo '# Start supervisor (which starts PostgreSQL and the app)' >> /app/start_complete_system.sh && \
    echo 'echo "📊 Starting services with supervisor..."' >> /app/start_complete_system.sh && \
    echo 'exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf' >> /app/start_complete_system.sh

RUN chmod +x /app/start_complete_system.sh

# Expose ports
EXPOSE 5432 8100 8109 8110

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8100/health || exit 1

# Start the complete system
CMD ["/app/start_complete_system.sh"]
