#!/usr/bin/env python3
"""
Daily Cost Fix Test
"""

import requests

BOT_TOKEN = "8060881816:AAFBhGADTAAcUkbom_FEFSkOHoT5N6t5png"
CHAT_ID = "1510889515"

def send_daily_cost_fix():
    """Send daily cost fix test message"""
    url = f"https://api.telegram.org/bot{BOT_TOKEN}/sendMessage"
    
    payload = {
        "chat_id": CHAT_ID,
        "text": """💰 DAILY COST ΔΙΟΡΘΩΘΗΚΕ!

✅ **Τεχνικό πρόβλημα λύθηκε:**

🔧 **Πρόβλημα**: Λάθος υπολογισμός grid_usage
❌ **Πριν**: `grid_usage = max(0, consumption - production)` → 0 kWh
✅ **Μετά**: Χρήση πραγματικών δεδομένων

📊 **Πραγματικά δεδομένα σήμερα:**
• System 1: 5.45 kWh από δίκτυο
• System 2: 9.04 kWh από δίκτυο
• Συνολικά: 14.49 kWh grid consumption

💡 **Σωστός υπολογισμός τώρα:**
• **Κόστος ενέργειας**: 0€ (έχουμε net metering credit)
• **Κόστος δικτύου**: 14.49 × €0.0069 = €0.10
• **Έσοδα πλεονάσματος**: Surplus × €0.05

🎯 **Αναμενόμενα αποτελέσματα:**
• Network cost: €0.10 (όχι €0.00)
• Energy cost: €0.00 (με πλεόνασμα)
• Σωστά tariffs: €0.142/kWh energy, €0.0069/kWh network

Δοκιμάστε τώρα το Daily Cost!"""
    }
    
    try:
        response = requests.post(url, json=payload, timeout=10)
        if response.status_code == 200:
            print("✅ Daily cost fix test message sent!")
            return True
        else:
            print(f"❌ Failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    send_daily_cost_fix()
