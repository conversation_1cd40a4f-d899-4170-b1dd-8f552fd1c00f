#!/bin/bash
#
# Enhanced Solar Prediction System - Automated Scheduler
# =====================================================
#
# Updated version using the latest production models and APIs:
# - Hybrid ML Ensemble Model (94.31% R² accuracy)
# - GPU-accelerated predictions
# - Unified training pipeline
# - Production-ready APIs
#
# Created: June 15, 2025
#

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Project directory
PROJECT_DIR="/home/<USER>/solar-prediction-project"
cd "$PROJECT_DIR"

# Logging function
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if services are running
check_services() {
    log "🔍 Checking Docker services..."
    
    # Check if Docker containers are running
    if ! docker ps | grep -q "solar-prediction"; then
        error "Docker services not running. Please start with: docker-compose up -d"
        exit 1
    fi
    
    # Check specific services
    services=("solar-prediction-main:8100" "solar-prediction-gpu:8105" "solar-prediction-scheduler:8106")
    
    for service in "${services[@]}"; do
        name=$(echo $service | cut -d: -f1)
        port=$(echo $service | cut -d: -f2)
        
        if curl -s -f "http://localhost:$port/health" > /dev/null; then
            success "$name service is healthy"
        else
            warning "$name service may not be ready (port $port)"
        fi
    done
}

# Test GPU service
test_gpu_service() {
    log "🚀 Testing GPU Prediction Service..."
    
    response=$(curl -s -X POST http://localhost:8105/api/gpu/predict/batch \
        -H "Content-Type: application/json" \
        -d '{"systems": ["system1", "system2"], "hours_list": [24, 48]}' \
        --max-time 30)
    
    if echo "$response" | grep -q "predictions"; then
        success "GPU service test passed"
        echo "Response: $response"
        return 0
    else
        error "GPU service test failed"
        echo "Response: $response"
        return 1
    fi
}

# Enhanced Model Training
train_enhanced_models() {
    log "🤖 Starting Enhanced Model Training..."
    
    echo ""
    echo "🎯 Training Options:"
    echo "1. All 16 models (recommended for production)"
    echo "2. Seasonal models only (8 models)"
    echo "3. Multi-horizon models only (8 models)"
    echo "4. Skip training (use existing models)"
    echo ""
    
    read -p "Select training option (1-4): " choice
    
    case $choice in
        1)
            log "Training all 16 models with unified pipeline..."
            if python3 scripts/training/train_all_models_unified.py --output_dir models/unified; then
                success "All models trained successfully"
            else
                error "Model training failed"
                return 1
            fi
            ;;
        2)
            log "Training seasonal models..."
            if python3 scripts/training/seasonal_models_trainer_unified.py; then
                success "Seasonal models trained successfully"
            else
                error "Seasonal model training failed"
                return 1
            fi
            ;;
        3)
            log "Training multi-horizon models..."
            if python3 scripts/training/train_multi_horizon_suite.py; then
                success "Multi-horizon models trained successfully"
            else
                error "Multi-horizon model training failed"
                return 1
            fi
            ;;
        4)
            warning "Skipping model training"
            ;;
        *)
            error "Invalid choice. Skipping training."
            ;;
    esac
}

# Prediction Scheduler
run_prediction_scheduler() {
    log "📈 Running Prediction Scheduler..."
    
    # Manual trigger
    response=$(curl -s -X POST http://localhost:8106/schedule/run)
    
    if echo "$response" | grep -q "started"; then
        success "Prediction scheduler triggered successfully"
        echo "Response: $response"
        
        # Wait a moment and check status
        sleep 3
        status=$(curl -s http://localhost:8106/schedule/status)
        echo "Status: $status"
        
        return 0
    else
        error "Prediction scheduler failed"
        echo "Response: $response"
        return 1
    fi
}

# System Health Check
system_health_check() {
    log "🏥 System Health Check..."
    
    # Check all services
    services=(
        "solar-prediction-main:8100"
        "solar-prediction-gpu:8105" 
        "solar-prediction-scheduler:8106"
        "solar-prediction-charts:8103"
        "solar-prediction-config:8108"
    )
    
    healthy_count=0
    total_count=${#services[@]}
    
    for service in "${services[@]}"; do
        name=$(echo $service | cut -d: -f1)
        port=$(echo $service | cut -d: -f2)
        
        if curl -s -f "http://localhost:$port/health" > /dev/null; then
            success "$name (port $port)"
            ((healthy_count++))
        else
            error "$name (port $port) - NOT HEALTHY"
        fi
    done
    
    echo ""
    log "Health Summary: $healthy_count/$total_count services healthy"
    
    if [ $healthy_count -eq $total_count ]; then
        success "All services are healthy!"
        return 0
    else
        warning "Some services are not healthy"
        return 1
    fi
}

# Main execution
main() {
    echo ""
    echo "🌞 =================================="
    echo "🌞 ENHANCED SOLAR PREDICTION SYSTEM"
    echo "🌞 =================================="
    echo "🌞 Production-Ready Scheduler v2.0"
    echo "🌞 =================================="
    echo ""
    
    log "Starting enhanced solar prediction scheduler..."
    
    # Step 1: Check services
    check_services
    
    # Step 2: System health check
    system_health_check
    
    # Step 3: Test GPU service
    test_gpu_service
    
    # Step 4: Model training (optional)
    echo ""
    read -p "Do you want to run model training? (y/N): " train_choice
    if [[ $train_choice =~ ^[Yy]$ ]]; then
        train_enhanced_models
    else
        warning "Skipping model training"
    fi
    
    # Step 5: Run prediction scheduler
    echo ""
    run_prediction_scheduler
    
    # Step 6: Final health check
    echo ""
    system_health_check
    
    echo ""
    success "Enhanced solar prediction scheduler completed!"
    log "All systems operational. Next scheduled run: tomorrow at 06:00"
    echo ""
}

# Execute main function
main "$@"
