@echo off
setlocal enabledelayedexpansion

REM Solar Prediction System - COMPREHENSIVE DIAGNOSTICS
REM Collects ALL logs and performs complete system testing

echo.
echo ========================================
echo   SOLAR PREDICTION COMPREHENSIVE DIAG<PERSON><PERSON><PERSON>CS
echo   EXACT WORKING IMAGE VERIFICATION
echo ========================================
echo.

REM Create diagnostics directory with timestamp
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "timestamp=%YYYY%%MM%%DD%_%HH%%Min%%Sec%"

set "DIAG_DIR=solar-diagnostics-%timestamp%"
mkdir "%DIAG_DIR%"

echo 📁 Created diagnostics directory: %DIAG_DIR%
echo.

REM Start logging
set "LOG_FILE=%DIAG_DIR%\comprehensive-diagnostics.log"
echo SOLAR PREDICTION SYSTEM - COMPREHENSIVE DIAGNOSTICS > "%LOG_FILE%"
echo Timestamp: %timestamp% >> "%LOG_FILE%"
echo ================================================== >> "%LOG_FILE%"
echo. >> "%LOG_FILE%"

echo 🔍 Starting comprehensive system diagnostics...
echo 🔍 Starting comprehensive system diagnostics... >> "%LOG_FILE%"
echo.

REM ========================================
REM 1. SYSTEM INFORMATION
REM ========================================
echo 1️⃣ COLLECTING SYSTEM INFORMATION...
echo 1. SYSTEM INFORMATION >> "%LOG_FILE%"
echo ==================== >> "%LOG_FILE%"

echo    • Operating System...
systeminfo | findstr /B /C:"OS Name" /C:"OS Version" >> "%LOG_FILE%"
echo    • Docker Version...
docker --version >> "%LOG_FILE%" 2>&1
echo    • Docker Compose Version...
docker-compose --version >> "%LOG_FILE%" 2>&1
echo    • Available Memory...
wmic computersystem get TotalPhysicalMemory >> "%LOG_FILE%"
echo    • Available Disk Space...
wmic logicaldisk get size,freespace,caption >> "%LOG_FILE%"
echo. >> "%LOG_FILE%"

REM ========================================
REM 2. DOCKER STATUS
REM ========================================
echo 2️⃣ CHECKING DOCKER STATUS...
echo 2. DOCKER STATUS >> "%LOG_FILE%"
echo ================ >> "%LOG_FILE%"

echo    • Docker Service Status...
docker info >> "%LOG_FILE%" 2>&1
echo. >> "%LOG_FILE%"

echo    • Docker Images...
docker images >> "%LOG_FILE%" 2>&1
echo. >> "%LOG_FILE%"

echo    • Docker Containers...
docker ps -a >> "%LOG_FILE%" 2>&1
echo. >> "%LOG_FILE%"

echo    • Docker Networks...
docker network ls >> "%LOG_FILE%" 2>&1
echo. >> "%LOG_FILE%"

echo    • Docker Volumes...
docker volume ls >> "%LOG_FILE%" 2>&1
echo. >> "%LOG_FILE%"

REM ========================================
REM 3. CONTAINER LOGS
REM ========================================
echo 3️⃣ COLLECTING CONTAINER LOGS...
echo 3. CONTAINER LOGS >> "%LOG_FILE%"
echo ================= >> "%LOG_FILE%"

echo    • Docker Compose Status...
docker-compose ps >> "%LOG_FILE%" 2>&1
echo. >> "%LOG_FILE%"

echo    • Solar Prediction App Logs...
docker-compose logs solar-prediction > "%DIAG_DIR%\solar-prediction-app.log" 2>&1
echo Solar Prediction App logs saved to solar-prediction-app.log >> "%LOG_FILE%"

echo    • PostgreSQL Database Logs...
docker-compose logs postgres > "%DIAG_DIR%\postgres-database.log" 2>&1
echo PostgreSQL Database logs saved to postgres-database.log >> "%LOG_FILE%"

echo    • All Services Combined Logs...
docker-compose logs > "%DIAG_DIR%\all-services-combined.log" 2>&1
echo All services logs saved to all-services-combined.log >> "%LOG_FILE%"
echo. >> "%LOG_FILE%"

REM ========================================
REM 4. NETWORK CONNECTIVITY
REM ========================================
echo 4️⃣ TESTING NETWORK CONNECTIVITY...
echo 4. NETWORK CONNECTIVITY >> "%LOG_FILE%"
echo ======================= >> "%LOG_FILE%"

echo    • Testing localhost connectivity...
ping -n 4 localhost >> "%LOG_FILE%" 2>&1

echo    • Testing port 8100 (Main App)...
netstat -an | findstr :8100 >> "%LOG_FILE%" 2>&1

echo    • Testing port 8110 (Enhanced Billing)...
netstat -an | findstr :8110 >> "%LOG_FILE%" 2>&1

echo    • Testing port 5433 (Database)...
netstat -an | findstr :5433 >> "%LOG_FILE%" 2>&1
echo. >> "%LOG_FILE%"

REM ========================================
REM 5. API ENDPOINTS TESTING
REM ========================================
echo 5️⃣ TESTING API ENDPOINTS...
echo 5. API ENDPOINTS TESTING >> "%LOG_FILE%"
echo ======================== >> "%LOG_FILE%"

echo    • Testing Main Application Health...
curl -v -m 30 http://localhost:8100/health > "%DIAG_DIR%\health-endpoint.log" 2>&1
if %errorlevel% equ 0 (
    echo ✅ Health Endpoint: WORKING >> "%LOG_FILE%"
    set "HEALTH_STATUS=WORKING"
) else (
    echo ❌ Health Endpoint: FAILED >> "%LOG_FILE%"
    set "HEALTH_STATUS=FAILED"
)

echo    • Testing API Documentation...
curl -v -m 30 http://localhost:8100/docs > "%DIAG_DIR%\docs-endpoint.log" 2>&1
if %errorlevel% equ 0 (
    echo ✅ API Docs: WORKING >> "%LOG_FILE%"
    set "DOCS_STATUS=WORKING"
) else (
    echo ❌ API Docs: FAILED >> "%LOG_FILE%"
    set "DOCS_STATUS=FAILED"
)

echo    • Testing SolaX Data Endpoint...
curl -v -m 30 http://localhost:8100/api/v1/data/solax/latest > "%DIAG_DIR%\solax-data.log" 2>&1
if %errorlevel% equ 0 (
    echo ✅ SolaX Data: WORKING >> "%LOG_FILE%"
    set "SOLAX_STATUS=WORKING"
) else (
    echo ❌ SolaX Data: FAILED >> "%LOG_FILE%"
    set "SOLAX_STATUS=FAILED"
)

echo    • Testing Weather Data Endpoint...
curl -v -m 30 http://localhost:8100/api/v1/data/weather/latest > "%DIAG_DIR%\weather-data.log" 2>&1
if %errorlevel% equ 0 (
    echo ✅ Weather Data: WORKING >> "%LOG_FILE%"
    set "WEATHER_STATUS=WORKING"
) else (
    echo ❌ Weather Data: FAILED >> "%LOG_FILE%"
    set "WEATHER_STATUS=FAILED"
)

echo    • Testing Enhanced Billing ROI...
curl -v -m 30 http://localhost:8110/api/v1/roi/system1 > "%DIAG_DIR%\roi-endpoint.log" 2>&1
if %errorlevel% equ 0 (
    echo ✅ Enhanced Billing ROI: WORKING >> "%LOG_FILE%"
    set "ROI_STATUS=WORKING"
) else (
    echo ❌ Enhanced Billing ROI: FAILED >> "%LOG_FILE%"
    set "ROI_STATUS=FAILED"
)

echo    • Testing Daily Cost Endpoint...
curl -v -m 30 http://localhost:8110/api/v1/billing/system1/daily > "%DIAG_DIR%\daily-cost.log" 2>&1
if %errorlevel% equ 0 (
    echo ✅ Daily Cost: WORKING >> "%LOG_FILE%"
    set "DAILY_STATUS=WORKING"
) else (
    echo ❌ Daily Cost: FAILED >> "%LOG_FILE%"
    set "DAILY_STATUS=FAILED"
)

echo    • Testing Tariffs Endpoint...
curl -v -m 30 http://localhost:8110/api/v1/tariffs > "%DIAG_DIR%\tariffs.log" 2>&1
if %errorlevel% equ 0 (
    echo ✅ Tariffs: WORKING >> "%LOG_FILE%"
    set "TARIFFS_STATUS=WORKING"
) else (
    echo ❌ Tariffs: FAILED >> "%LOG_FILE%"
    set "TARIFFS_STATUS=FAILED"
)

echo    • Testing Data Collection Endpoints...
curl -v -m 30 -X POST http://localhost:8100/api/v1/data/collect/solax > "%DIAG_DIR%\collect-solax.log" 2>&1
if %errorlevel% equ 0 (
    echo ✅ SolaX Collection: WORKING >> "%LOG_FILE%"
    set "COLLECT_SOLAX_STATUS=WORKING"
) else (
    echo ❌ SolaX Collection: FAILED >> "%LOG_FILE%"
    set "COLLECT_SOLAX_STATUS=FAILED"
)

curl -v -m 30 -X POST http://localhost:8100/api/v1/data/collect/weather > "%DIAG_DIR%\collect-weather.log" 2>&1
if %errorlevel% equ 0 (
    echo ✅ Weather Collection: WORKING >> "%LOG_FILE%"
    set "COLLECT_WEATHER_STATUS=WORKING"
) else (
    echo ❌ Weather Collection: FAILED >> "%LOG_FILE%"
    set "COLLECT_WEATHER_STATUS=FAILED"
)
echo. >> "%LOG_FILE%"

REM ========================================
REM 6. DATABASE CONNECTIVITY
REM ========================================
echo 6️⃣ TESTING DATABASE CONNECTIVITY...
echo 6. DATABASE CONNECTIVITY >> "%LOG_FILE%"
echo ======================== >> "%LOG_FILE%"

echo    • Testing PostgreSQL Connection...
docker exec solar-prediction-db psql -U postgres -d solar_prediction -c "SELECT version();" > "%DIAG_DIR%\database-version.log" 2>&1
if %errorlevel% equ 0 (
    echo ✅ Database Connection: WORKING >> "%LOG_FILE%"
    set "DB_STATUS=WORKING"
) else (
    echo ❌ Database Connection: FAILED >> "%LOG_FILE%"
    set "DB_STATUS=FAILED"
)

echo    • Testing Database Tables...
docker exec solar-prediction-db psql -U postgres -d solar_prediction -c "\dt" > "%DIAG_DIR%\database-tables.log" 2>&1

echo    • Testing Data Availability...
docker exec solar-prediction-db psql -U postgres -d solar_prediction -c "SELECT COUNT(*) FROM solax_data;" > "%DIAG_DIR%\solax-data-count.log" 2>&1
docker exec solar-prediction-db psql -U postgres -d solar_prediction -c "SELECT COUNT(*) FROM weather_data;" > "%DIAG_DIR%\weather-data-count.log" 2>&1
echo. >> "%LOG_FILE%"

REM ========================================
REM 7. TELEGRAM BOT TESTING
REM ========================================
echo 7️⃣ TESTING TELEGRAM BOT ENDPOINTS...
echo 7. TELEGRAM BOT ENDPOINTS >> "%LOG_FILE%"
echo ========================= >> "%LOG_FILE%"

echo    • Testing Telegram Bot Token...
curl -v -m 30 "https://api.telegram.org/bot8060881816:AAFBhGADTAAcUkbom_FEFSkOHoT5N6t5png/getMe" > "%DIAG_DIR%\telegram-bot-token.log" 2>&1
if %errorlevel% equ 0 (
    echo ✅ Telegram Bot Token: VALID >> "%LOG_FILE%"
    set "TELEGRAM_TOKEN_STATUS=VALID"
) else (
    echo ❌ Telegram Bot Token: INVALID >> "%LOG_FILE%"
    set "TELEGRAM_TOKEN_STATUS=INVALID"
)

echo    • Testing Telegram Bot Webhook...
curl -v -m 30 "https://api.telegram.org/bot8060881816:AAFBhGADTAAcUkbom_FEFSkOHoT5N6t5png/getWebhookInfo" > "%DIAG_DIR%\telegram-webhook.log" 2>&1

echo    • Testing Telegram Bot Updates...
curl -v -m 30 "https://api.telegram.org/bot8060881816:AAFBhGADTAAcUkbom_FEFSkOHoT5N6t5png/getUpdates" > "%DIAG_DIR%\telegram-updates.log" 2>&1

echo    • Testing Internal Telegram Bot Service...
docker exec solar-prediction-app ps aux > "%DIAG_DIR%\telegram-process.log" 2>&1
if %errorlevel% equ 0 (
    echo ✅ Telegram Bot Process: RUNNING >> "%LOG_FILE%"
    set "TELEGRAM_PROCESS_STATUS=RUNNING"
) else (
    echo ❌ Telegram Bot Process: NOT RUNNING >> "%LOG_FILE%"
    set "TELEGRAM_PROCESS_STATUS=NOT_RUNNING"
)
echo. >> "%LOG_FILE%"

REM ========================================
REM 8. BACKGROUND TASKS TESTING
REM ========================================
echo 8️⃣ TESTING BACKGROUND TASKS...
echo 8. BACKGROUND TASKS >> "%LOG_FILE%"
echo =================== >> "%LOG_FILE%"

echo    • Checking Background Task Processes...
docker exec solar-prediction-app ps aux > "%DIAG_DIR%\container-processes.log" 2>&1

echo    • Testing Background Task Logs...
docker exec solar-prediction-app find /app -name "*.log" -type f > "%DIAG_DIR%\app-log-files.log" 2>&1

echo    • Checking Scheduled Tasks...
docker exec solar-prediction-app crontab -l > "%DIAG_DIR%\cron-tasks.log" 2>&1

echo    • Testing Data Collection Status...
docker exec solar-prediction-app ls -la /app/data/ > "%DIAG_DIR%\app-data-directory.log" 2>&1
echo. >> "%LOG_FILE%"

REM ========================================
REM 9. PERFORMANCE METRICS
REM ========================================
echo 9️⃣ COLLECTING PERFORMANCE METRICS...
echo 9. PERFORMANCE METRICS >> "%LOG_FILE%"
echo ====================== >> "%LOG_FILE%"

echo    • Container Resource Usage...
docker stats --no-stream > "%DIAG_DIR%\container-stats.log" 2>&1

echo    • Container Inspect Information...
docker inspect solar-prediction-app > "%DIAG_DIR%\app-container-inspect.log" 2>&1
docker inspect solar-prediction-db > "%DIAG_DIR%\db-container-inspect.log" 2>&1

echo    • System Resource Usage...
wmic process get name,processid,percentprocessortime > "%DIAG_DIR%\system-processes.log" 2>&1
echo. >> "%LOG_FILE%"

REM ========================================
REM 10. CONFIGURATION VERIFICATION
REM ========================================
echo 🔟 VERIFYING CONFIGURATION...
echo 10. CONFIGURATION VERIFICATION >> "%LOG_FILE%"
echo ============================== >> "%LOG_FILE%"

echo    • Environment Variables...
docker exec solar-prediction-app env > "%DIAG_DIR%\environment-variables.log" 2>&1

echo    • Application Configuration...
docker exec solar-prediction-app find /app -name "*.env" -o -name "*.conf" -o -name "*.ini" -o -name "*.yaml" -o -name "*.yml" > "%DIAG_DIR%\config-files.log" 2>&1

echo    • Python Path Configuration...
docker exec solar-prediction-app python -c "import sys; print('\n'.join(sys.path))" > "%DIAG_DIR%\python-path.log" 2>&1

echo    • Installed Python Packages...
docker exec solar-prediction-app pip list > "%DIAG_DIR%\python-packages.log" 2>&1
echo. >> "%LOG_FILE%"

REM ========================================
REM 11. FINAL SUMMARY REPORT
REM ========================================
echo 📊 GENERATING FINAL SUMMARY REPORT...
echo 11. FINAL SUMMARY REPORT >> "%LOG_FILE%"
echo ======================== >> "%LOG_FILE%"

echo.
echo ========================================
echo   COMPREHENSIVE DIAGNOSTICS SUMMARY
echo ========================================
echo.
echo SYSTEM STATUS:
echo   • Health Endpoint: %HEALTH_STATUS%
echo   • API Documentation: %DOCS_STATUS%
echo   • Database: %DB_STATUS%
echo.
echo DATA ENDPOINTS:
echo   • SolaX Data: %SOLAX_STATUS%
echo   • Weather Data: %WEATHER_STATUS%
echo.
echo ENHANCED BILLING:
echo   • ROI Endpoint: %ROI_STATUS%
echo   • Daily Cost: %DAILY_STATUS%
echo   • Tariffs: %TARIFFS_STATUS%
echo.
echo DATA COLLECTION:
echo   • SolaX Collection: %COLLECT_SOLAX_STATUS%
echo   • Weather Collection: %COLLECT_WEATHER_STATUS%
echo.
echo TELEGRAM BOT:
echo   • Token Validation: %TELEGRAM_TOKEN_STATUS%
echo   • Process Status: %TELEGRAM_PROCESS_STATUS%
echo.

REM Write summary to log file
echo SYSTEM STATUS: >> "%LOG_FILE%"
echo   Health Endpoint: %HEALTH_STATUS% >> "%LOG_FILE%"
echo   API Documentation: %DOCS_STATUS% >> "%LOG_FILE%"
echo   Database: %DB_STATUS% >> "%LOG_FILE%"
echo DATA ENDPOINTS: >> "%LOG_FILE%"
echo   SolaX Data: %SOLAX_STATUS% >> "%LOG_FILE%"
echo   Weather Data: %WEATHER_STATUS% >> "%LOG_FILE%"
echo ENHANCED BILLING: >> "%LOG_FILE%"
echo   ROI Endpoint: %ROI_STATUS% >> "%LOG_FILE%"
echo   Daily Cost: %DAILY_STATUS% >> "%LOG_FILE%"
echo   Tariffs: %TARIFFS_STATUS% >> "%LOG_FILE%"
echo DATA COLLECTION: >> "%LOG_FILE%"
echo   SolaX Collection: %COLLECT_SOLAX_STATUS% >> "%LOG_FILE%"
echo   Weather Collection: %COLLECT_WEATHER_STATUS% >> "%LOG_FILE%"
echo TELEGRAM BOT: >> "%LOG_FILE%"
echo   Token Validation: %TELEGRAM_TOKEN_STATUS% >> "%LOG_FILE%"
echo   Process Status: %TELEGRAM_PROCESS_STATUS% >> "%LOG_FILE%"
echo. >> "%LOG_FILE%"

REM ========================================
REM 12. RECOMMENDATIONS
REM ========================================
echo 📋 GENERATING RECOMMENDATIONS...
echo 12. RECOMMENDATIONS >> "%LOG_FILE%"
echo =================== >> "%LOG_FILE%"

echo RECOMMENDATIONS: >> "%LOG_FILE%"

if "%HEALTH_STATUS%"=="FAILED" (
    echo   • Main application is not responding - check container logs >> "%LOG_FILE%"
    echo   • Main application is not responding - check container logs
)

if "%DB_STATUS%"=="FAILED" (
    echo   • Database connection failed - verify PostgreSQL container >> "%LOG_FILE%"
    echo   • Database connection failed - verify PostgreSQL container
)

if "%ROI_STATUS%"=="FAILED" (
    echo   • Enhanced Billing service not working - check port 8110 >> "%LOG_FILE%"
    echo   • Enhanced Billing service not working - check port 8110
)

if "%TELEGRAM_TOKEN_STATUS%"=="INVALID" (
    echo   • Telegram bot token is invalid - check configuration >> "%LOG_FILE%"
    echo   • Telegram bot token is invalid - check configuration
)

echo   • Check all-services-combined.log for detailed error messages >> "%LOG_FILE%"
echo   • Verify all containers are running: docker-compose ps >> "%LOG_FILE%"
echo   • Check container resource usage in container-stats.log >> "%LOG_FILE%"
echo   • Review environment variables in environment-variables.log >> "%LOG_FILE%"
echo. >> "%LOG_FILE%"

echo.
echo ========================================
echo   DIAGNOSTICS COMPLETED
echo ========================================
echo.
echo 📁 All diagnostic files saved in: %DIAG_DIR%
echo 📄 Main log file: %LOG_FILE%
echo.
echo 📋 Key Files to Review:
echo   • comprehensive-diagnostics.log - Main summary
echo   • all-services-combined.log - All container logs
echo   • health-endpoint.log - Health check details
echo   • roi-endpoint.log - Enhanced billing details
echo   • telegram-bot-token.log - Telegram bot validation
echo   • database-version.log - Database connectivity
echo   • solax-data-count.log - Data availability
echo   • weather-data-count.log - Weather data availability
echo   • environment-variables.log - Container configuration
echo   • container-stats.log - Performance metrics
echo.
echo 🔍 If issues found, share the entire %DIAG_DIR% folder
echo    for detailed analysis and troubleshooting.
echo.

REM Create a summary file for easy sharing
echo SOLAR PREDICTION SYSTEM - DIAGNOSTIC SUMMARY > "%DIAG_DIR%\SUMMARY.txt"
echo ============================================== >> "%DIAG_DIR%\SUMMARY.txt"
echo Timestamp: %timestamp% >> "%DIAG_DIR%\SUMMARY.txt"
echo Package: solar-prediction-complete-image-20250627_052453.tar.gz >> "%DIAG_DIR%\SUMMARY.txt"
echo Source: EXACT working Docker image export >> "%DIAG_DIR%\SUMMARY.txt"
echo. >> "%DIAG_DIR%\SUMMARY.txt"
echo QUICK STATUS: >> "%DIAG_DIR%\SUMMARY.txt"
echo Health: %HEALTH_STATUS% >> "%DIAG_DIR%\SUMMARY.txt"
echo Database: %DB_STATUS% >> "%DIAG_DIR%\SUMMARY.txt"
echo Enhanced Billing: %ROI_STATUS% >> "%DIAG_DIR%\SUMMARY.txt"
echo Telegram Bot: %TELEGRAM_TOKEN_STATUS% >> "%DIAG_DIR%\SUMMARY.txt"
echo SolaX Data: %SOLAX_STATUS% >> "%DIAG_DIR%\SUMMARY.txt"
echo Weather Data: %WEATHER_STATUS% >> "%DIAG_DIR%\SUMMARY.txt"
echo. >> "%DIAG_DIR%\SUMMARY.txt"
echo EXPECTED WORKING ENDPOINTS: >> "%DIAG_DIR%\SUMMARY.txt"
echo • Web Interface: http://localhost:8100 >> "%DIAG_DIR%\SUMMARY.txt"
echo • Health Check: http://localhost:8100/health >> "%DIAG_DIR%\SUMMARY.txt"
echo • API Docs: http://localhost:8100/docs >> "%DIAG_DIR%\SUMMARY.txt"
echo • Enhanced Billing: http://localhost:8110 >> "%DIAG_DIR%\SUMMARY.txt"
echo • ROI Endpoint: http://localhost:8110/api/v1/roi/system1 >> "%DIAG_DIR%\SUMMARY.txt"
echo • Daily Cost: http://localhost:8110/api/v1/billing/system1/daily >> "%DIAG_DIR%\SUMMARY.txt"
echo • Tariffs: http://localhost:8110/api/v1/tariffs >> "%DIAG_DIR%\SUMMARY.txt"
echo • SolaX Data: http://localhost:8100/api/v1/data/solax/latest >> "%DIAG_DIR%\SUMMARY.txt"
echo • Weather Data: http://localhost:8100/api/v1/data/weather/latest >> "%DIAG_DIR%\SUMMARY.txt"
echo • Database: localhost:5433 >> "%DIAG_DIR%\SUMMARY.txt"
echo • Telegram Bot: @grlvSolarAI_bot >> "%DIAG_DIR%\SUMMARY.txt"
echo. >> "%DIAG_DIR%\SUMMARY.txt"
echo For detailed analysis, review all files in this directory. >> "%DIAG_DIR%\SUMMARY.txt"
echo If sharing for troubleshooting, include the entire folder. >> "%DIAG_DIR%\SUMMARY.txt"

echo 📄 Created SUMMARY.txt for quick reference
echo.
echo 🎯 NEXT STEPS:
echo   1. Review the summary above
echo   2. Check SUMMARY.txt for quick reference
echo   3. If issues found, review detailed log files
echo   4. Share entire %DIAG_DIR% folder if help needed
echo.

pause
