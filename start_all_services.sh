#!/bin/bash
set -e

echo "🌞 Starting Solar Prediction Project - All Services..."

# Wait for database to be ready
echo "⏳ Waiting for database..."
while ! pg_isready -h ${DATABASE_HOST:-postgres} -p ${DATABASE_PORT:-5432} -U ${DATABASE_USER:-postgres}; do
    echo "Database not ready, waiting..."
    sleep 2
done
echo "✅ Database is ready"

# Run database migrations if needed
if [ "${RUN_MIGRATIONS:-true}" = "true" ]; then
    echo "🔄 Running database migrations..."
    python -c "
from src.core.database import init_database
try:
    init_database()
    print('✅ Database initialized successfully')
except Exception as e:
    print(f'⚠️ Database initialization warning: {e}')
"
fi

# Update environment variables for container
export DATABASE_URL="postgresql://postgres:postgres@${DATABASE_HOST:-postgres}:${DATABASE_PORT:-5432}/solar_prediction"
export PYTHONPATH="/app:/app/src"

echo "🚀 Starting all services with supervisor..."

# Start supervisor to manage all services
exec supervisord -c /app/supervisord.conf -n
