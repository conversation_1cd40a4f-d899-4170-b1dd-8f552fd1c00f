#!/bin/bash

# =============================================================================
# CORRECTED HEALTH CHECK SCRIPT - DOCKER DATABASE ONLY
# Αφαιρέθηκε η σύγκριση με τοπική βάση που δεν υπάρχει
# =============================================================================

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Load environment variables
if [ -f .env ]; then
    export $(grep -v '^#' .env | xargs -d '\n')
else
    echo -e "${RED}✗ .env file not found!${NC}"
    exit 1
fi

# ΜΟΝΟ Docker Database Configuration
DB_HOST=${DB_HOST:-localhost}
DB_PORT=5433  # ΜΟΝΟ Docker port
DB_NAME=${DB_NAME}
DB_USER=${DB_USER}
DB_PASSWORD=${DB_PASSWORD}

LOG_FILE="/home/<USER>/solar-prediction-project/logs/health_check.log"
DATA_SCRIPTS_DIR="/home/<USER>/solar-prediction-project/scripts/data"
ML_SCRIPTS_DIR="/home/<USER>/solar-prediction-project/scripts/ml"

export PGPASSWORD=$DB_PASSWORD

# Create log directory
mkdir -p "/home/<USER>/solar-prediction-project/logs"

echo -e "${BLUE}=== DOCKER DATABASE HEALTH CHECK ===${NC}" | tee -a "$LOG_FILE"
echo "Checking ONLY Docker PostgreSQL database on port 5433" | tee -a "$LOG_FILE"
echo "Date: $(date)" | tee -a "$LOG_FILE"
echo "" | tee -a "$LOG_FILE"

# Test Docker database connection
if psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SELECT 1;" &>/dev/null; then
    echo -e "${GREEN}✓ Docker PostgreSQL connection successful${NC}" | tee -a "$LOG_FILE"
else
    echo -e "${RED}✗ Docker PostgreSQL connection failed!${NC}" | tee -a "$LOG_FILE"
    exit 1
fi

# Get database version and size
echo "Docker Database Information:" | tee -a "$LOG_FILE"
psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SELECT version();" | tee -a "$LOG_FILE"
psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "
SELECT 
    pg_database.datname as database_name,
    pg_size_pretty(pg_database_size(pg_database.datname)) as size
FROM pg_database 
WHERE pg_database.datname = '$DB_NAME';
" | tee -a "$LOG_FILE"
echo "" | tee -a "$LOG_FILE"

# ΚΡΙΣΙΜΟΣ ΕΛΕΓΧΟΣ: Πίνακες και δεδομένα
echo "=== CRITICAL DATA CHECK ===" | tee -a "$LOG_FILE"
echo "Tables and Record Counts in Docker Database:" | tee -a "$LOG_FILE"

# Έλεγχος κρίσιμων πινάκων
CRITICAL_TABLES=("nasa_power_data" "solax_data" "weather_data" "cams_radiation_data" "system_alerts")

for table in "${CRITICAL_TABLES[@]}"; do
    count=$(psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT COUNT(*) FROM $table;" 2>/dev/null | xargs)
    if [[ $? -eq 0 ]]; then
        if [[ $count -gt 0 ]]; then
            echo -e "${GREEN}✓ $table: $count records${NC}" | tee -a "$LOG_FILE"
        else
            echo -e "${RED}✗ $table: EMPTY (0 records)${NC}" | tee -a "$LOG_FILE"
        fi
    else
        echo -e "${RED}✗ $table: TABLE NOT FOUND${NC}" | tee -a "$LOG_FILE"
    fi
done

echo "" | tee -a "$LOG_FILE"

# Εκτέλεση ΜΟΝΟ των scheduler scripts
echo "=== RUNNING DATA UPDATE SCRIPTS ===" | tee -a "$LOG_FILE"

# Data scripts
if [ -d "$DATA_SCRIPTS_DIR" ]; then
    echo "Executing data update scripts..." | tee -a "$LOG_FILE"
    find "$DATA_SCRIPTS_DIR" -name "*.sh" -executable | while read script; do
        if [ -f "$script" ]; then
            echo "Running: $(basename $script)" | tee -a "$LOG_FILE"
            if timeout 300 bash "$script" >> "$LOG_FILE" 2>&1; then
                echo -e "${GREEN}✓ $(basename $script) completed${NC}" | tee -a "$LOG_FILE"
            else
                echo -e "${RED}✗ $(basename $script) failed${NC}" | tee -a "$LOG_FILE"
            fi
        fi
    done
fi

# ML scripts
if [ -d "$ML_SCRIPTS_DIR" ]; then
    echo "Executing ML update scripts..." | tee -a "$LOG_FILE"
    find "$ML_SCRIPTS_DIR" -name "*.py" | while read script; do
        if [ -f "$script" ]; then
            echo "Running: $(basename $script)" | tee -a "$LOG_FILE"
            if timeout 600 python3 "$script" >> "$LOG_FILE" 2>&1; then
                echo -e "${GREEN}✓ $(basename $script) completed${NC}" | tee -a "$LOG_FILE"
            else
                echo -e "${RED}✗ $(basename $script) failed${NC}" | tee -a "$LOG_FILE"
            fi
        fi
    done
fi

# Final status
echo "" | tee -a "$LOG_FILE"
echo -e "${BLUE}=== HEALTH CHECK COMPLETED ===${NC}" | tee -a "$LOG_FILE"
echo "Focus: Docker database only (port 5433)" | tee -a "$LOG_FILE"
echo "Local database comparison removed as requested" | tee -a "$LOG_FILE"

unset PGPASSWORD

