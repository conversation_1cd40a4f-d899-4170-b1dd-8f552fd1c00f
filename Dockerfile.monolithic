# Solar Prediction Project - Monolithic Dockerfile
# Single container with all services (API, Telegram Bot, Enhanced Billing)

FROM python:3.12-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONPATH="/app:/app/src" \
    PYTHONRECURSIONLIMIT=10000

# Install system dependencies
RUN apt-get update && apt-get install -y \
    postgresql-client \
    curl \
    libgomp1 \
    gcc \
    g++ \
    make \
    pkg-config \
    libpq-dev \
    netcat-openbsd \
    supervisor \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create non-root user
RUN groupadd -r solarapp && useradd -r -g solarapp solarapp

# Set working directory
WORKDIR /app

# Create necessary directories
RUN mkdir -p logs models data static templates scripts docs sql \
    && chown -R solarapp:solarapp /app

# Copy requirements and install Python dependencies
COPY requirements.docker.txt ./
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.docker.txt

# Copy application code
COPY --chown=solarapp:solarapp src/ ./src/
COPY --chown=solarapp:solarapp static/ ./static/
COPY --chown=solarapp:solarapp scripts/ ./scripts/
COPY --chown=solarapp:solarapp models/ ./models/
COPY --chown=solarapp:solarapp data/ ./data/
COPY --chown=solarapp:solarapp logs/ ./logs/
COPY --chown=solarapp:solarapp docs/ ./docs/
COPY --chown=solarapp:solarapp sql/ ./sql/

# Copy service files
COPY --chown=solarapp:solarapp telegram_bot_service.py enhanced_billing_service.py ./
COPY --chown=solarapp:solarapp run.py app.py ./

# Copy configuration files
COPY --chown=solarapp:solarapp .env ./
COPY --chown=solarapp:solarapp .cdsapirc /home/<USER>/.cdsapirc

# Copy startup scripts
COPY --chown=solarapp:solarapp start_all_services.sh ./start_all_services.sh
COPY --chown=solarapp:solarapp supervisord.conf ./supervisord.conf

# Set permissions
RUN chmod +x start_all_services.sh && \
    chmod 600 /home/<USER>/.cdsapirc && \
    chown -R solarapp:solarapp /app

# Switch to non-root user
USER solarapp

# Expose ports for all services
EXPOSE 8100 8109 8110

# Health check for main API
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8100/health || exit 1

# Start all services
CMD ["./start_all_services.sh"]
