#!/usr/bin/env python3
"""
Unified Health Monitoring Service
Aggregates health status from all solar prediction system services
"""

import os
import sys
import asyncio
import logging
import time
from datetime import datetime
from typing import Dict, List, Any
import requests
import aiohttp
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = FastAPI(title="Unified Health Monitoring Service", version="1.0.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class UnifiedHealthMonitor:
    """Unified health monitoring for all services"""
    
    def __init__(self):
        # Define all services to monitor
        self.services = [
            {"name": "Main API", "url": "http://solar-prediction-main:8100/health", "port": 8100},
            {"name": "Charts API", "url": "http://solar-prediction-charts:8103/health", "port": 8103},
            {"name": "GPU Prediction", "url": "http://solar-prediction-gpu:8105/health", "port": 8105},
            {"name": "Scheduler", "url": "http://solar-prediction-scheduler:8106/health", "port": 8106},
            {"name": "Alert System", "url": "http://solar-prediction-alerts:8107/health", "port": 8107},
            {"name": "Config Manager", "url": "http://solar-prediction-config:8108/health", "port": 8108},
            {"name": "Telegram Bot", "url": "http://solar-prediction-telegram:8109/health", "port": 8109},
            {"name": "Enhanced Billing", "url": "http://solar-prediction-billing:8110/health", "port": 8110},
            {"name": "Unified Forecast", "url": "http://solar-prediction-forecast:8120/health", "port": 8120},
            {"name": "Web Server", "url": "http://solar-prediction-web:8080/", "port": 8080},
        ]
        
        # Database services
        self.database_services = [
            {"name": "PostgreSQL", "url": "http://solar-prediction-db:5432", "port": 5432},
            {"name": "Redis Cache", "url": "http://solar-prediction-cache:6379", "port": 6379},
        ]
        
        self.cache = {}
        self.cache_ttl = 30  # 30 seconds cache
    
    async def check_service_health(self, service: Dict) -> Dict:
        """Check health of a single service"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(service["url"], timeout=5) as response:
                    if response.status == 200:
                        try:
                            data = await response.json()
                            return {
                                "status": "healthy",
                                "response_time_ms": 0,  # Could add timing
                                "details": data
                            }
                        except:
                            return {
                                "status": "healthy",
                                "response_time_ms": 0,
                                "details": {"message": "Service responding"}
                            }
                    else:
                        return {
                            "status": "unhealthy",
                            "response_time_ms": 0,
                            "error": f"HTTP {response.status}"
                        }
        except Exception as e:
            return {
                "status": "unhealthy",
                "response_time_ms": 0,
                "error": str(e)
            }
    
    async def get_system_health(self) -> Dict:
        """Get comprehensive system health"""
        # Check cache
        cache_key = "system_health"
        now = time.time()
        
        if cache_key in self.cache:
            cached_data, timestamp = self.cache[cache_key]
            if now - timestamp < self.cache_ttl:
                return cached_data
        
        # Check all services
        service_results = {}
        
        # Check application services
        for service in self.services:
            health = await self.check_service_health(service)
            service_results[service["name"]] = {
                "port": service["port"],
                "url": service["url"],
                **health
            }
        
        # Calculate overall status
        healthy_services = sum(1 for result in service_results.values() if result["status"] == "healthy")
        total_services = len(service_results)
        
        overall_status = "healthy" if healthy_services == total_services else "degraded"
        if healthy_services < total_services * 0.5:
            overall_status = "critical"
        
        # Build response
        health_data = {
            "overall_status": overall_status,
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_services": total_services,
                "healthy_services": healthy_services,
                "unhealthy_services": total_services - healthy_services,
                "health_percentage": round((healthy_services / total_services) * 100, 1)
            },
            "services": service_results,
            "system_info": {
                "monitoring_service": "unified_health_service",
                "version": "1.0.0",
                "uptime": "running"
            }
        }
        
        # Cache result
        self.cache[cache_key] = (health_data, now)
        
        return health_data

# Initialize monitor
health_monitor = UnifiedHealthMonitor()

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "service": "Unified Health Monitoring Service",
        "version": "1.0.0",
        "endpoints": [
            "/health",
            "/api/v2/system/health",
            "/services",
            "/status"
        ]
    }

@app.get("/health")
async def health_check():
    """Health check for this service"""
    return {
        "status": "healthy",
        "service": "unified_health_monitoring",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v2/system/health")
async def get_system_health():
    """Get comprehensive system health status"""
    try:
        health_data = await health_monitor.get_system_health()
        return health_data
    except Exception as e:
        logger.error(f"Failed to get system health: {e}")
        raise HTTPException(status_code=500, detail=f"Health check failed: {e}")

@app.get("/services")
async def list_services():
    """List all monitored services"""
    return {
        "services": health_monitor.services,
        "database_services": health_monitor.database_services,
        "total_services": len(health_monitor.services) + len(health_monitor.database_services)
    }

@app.get("/status")
async def get_status():
    """Get quick status summary"""
    try:
        health_data = await health_monitor.get_system_health()
        return {
            "status": health_data["overall_status"],
            "healthy_services": health_data["summary"]["healthy_services"],
            "total_services": health_data["summary"]["total_services"],
            "timestamp": health_data["timestamp"]
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

def main():
    """Main entry point"""
    port = int(os.getenv('HEALTH_PORT', 8130))
    logger.info(f"🚀 Starting Unified Health Monitoring Service on port {port}...")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=port,
        log_level="info"
    )

if __name__ == "__main__":
    main()
