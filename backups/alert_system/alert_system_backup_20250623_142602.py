#!/usr/bin/env python3
"""
Alert System for Solar Prediction System
Real-time monitoring with email and Telegram notifications
"""

import sys
import os
import time
import json
import requests
import psycopg2
from psycopg2.extras import RealDict<PERSON>ursor
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Any, Optional
import logging
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import asyncio
from fastapi import FastAPI
from fastapi.responses import JSONResponse
import uvicorn
import threading
import pytz

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Greek timezone
GREEK_TZ = pytz.timezone('Europe/Athens')

def get_greek_time():
    """Get current time in Greek timezone"""
    return datetime.now(GREEK_TZ)

# Database configuration
DB_CONFIG = {
    'host': os.getenv('DATABASE_HOST', 'localhost'),
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

# Alert configuration
ALERT_CONFIG = {
    'telegram': {
        'bot_token': '**********************************************',
        'chat_id': '1510889515'
    },
    'email': {
        'smtp_server': 'smtp.gmail.com',
        'smtp_port': 587,
        'username': '<EMAIL>',  # Configure with real email
        'password': 'app_password',  # Configure with real app password
        'to_email': '<EMAIL>'
    },
    'thresholds': {
        'low_soc': 20,  # %
        'high_temperature': 60,  # °C
        'low_power_efficiency': 50,  # %
        'data_staleness': 300,  # seconds
        'system_offline': 600,  # seconds
        'low_daily_yield': 30  # kWh
    }
}

class AlertSystem:
    """Comprehensive alert monitoring system"""
    
    def __init__(self):
        self.db_config = DB_CONFIG
        self.alert_config = ALERT_CONFIG
        self.alert_history = {}
        self.last_alerts = {}
        self.alert_cooldown = 1800  # 30 minutes between same alerts
        self.is_running = False
        
        # Initialize alert tables
        self.init_alert_tables()
    
    def get_db_connection(self):
        """Get database connection"""
        try:
            return psycopg2.connect(**self.db_config)
        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            return None
    
    def init_alert_tables(self):
        """Initialize alert tracking tables"""
        conn = self.get_db_connection()
        if not conn:
            return False
        
        try:
            cur = conn.cursor()
            
            # Create alerts table
            cur.execute("""
                CREATE TABLE IF NOT EXISTS system_alerts (
                    id SERIAL PRIMARY KEY,
                    alert_type VARCHAR(50) NOT NULL,
                    severity VARCHAR(20) NOT NULL,
                    system_id VARCHAR(20),
                    message TEXT NOT NULL,
                    details JSONB,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    resolved_at TIMESTAMP,
                    is_resolved BOOLEAN DEFAULT FALSE,
                    notification_sent BOOLEAN DEFAULT FALSE
                )
            """)
            
            # Create alert rules table
            cur.execute("""
                CREATE TABLE IF NOT EXISTS alert_rules (
                    id SERIAL PRIMARY KEY,
                    rule_name VARCHAR(100) NOT NULL,
                    rule_type VARCHAR(50) NOT NULL,
                    threshold_value REAL,
                    comparison_operator VARCHAR(10),
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.commit()
            logger.info("✅ Alert tables initialized")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing alert tables: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()
    
    def send_telegram_alert(self, message: str, severity: str = "info") -> bool:
        """Send alert via Telegram"""
        try:
            # Add severity emoji
            severity_emojis = {
                "critical": "🚨",
                "warning": "⚠️",
                "info": "ℹ️",
                "success": "✅"
            }
            
            emoji = severity_emojis.get(severity, "📢")
            greek_time = get_greek_time()
            formatted_message = f"{emoji} **Solar System Alert**\n\n{message}\n\n🕐 {greek_time.strftime('%Y-%m-%d %H:%M:%S EEST')}"
            
            url = f"https://api.telegram.org/bot{self.alert_config['telegram']['bot_token']}/sendMessage"
            
            payload = {
                'chat_id': self.alert_config['telegram']['chat_id'],
                'text': formatted_message,
                'parse_mode': 'Markdown'
            }
            
            response = requests.post(url, json=payload, timeout=10)
            
            if response.status_code == 200:
                logger.info(f"✅ Telegram alert sent: {severity}")
                return True
            else:
                logger.error(f"❌ Telegram alert failed: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Telegram alert error: {e}")
            return False
    
    def send_email_alert(self, subject: str, message: str, severity: str = "info") -> bool:
        """Send alert via email"""
        try:
            # Skip email if not configured
            if self.alert_config['email']['username'] == '<EMAIL>':
                logger.info("📧 Email not configured, skipping email alert")
                return True
            
            msg = MIMEMultipart()
            msg['From'] = self.alert_config['email']['username']
            msg['To'] = self.alert_config['email']['to_email']
            msg['Subject'] = f"[Solar System] {subject}"
            
            # Create HTML email body
            html_body = f"""
            <html>
            <body>
                <h2>Solar Prediction System Alert</h2>
                <p><strong>Severity:</strong> {severity.upper()}</p>
                <p><strong>Time:</strong> {get_greek_time().strftime('%Y-%m-%d %H:%M:%S EEST')}</p>
                <hr>
                <p>{message.replace(chr(10), '<br>')}</p>
                <hr>
                <p><em>This is an automated alert from the Solar Prediction System.</em></p>
            </body>
            </html>
            """
            
            msg.attach(MIMEText(html_body, 'html'))
            
            # Send email
            server = smtplib.SMTP(self.alert_config['email']['smtp_server'], self.alert_config['email']['smtp_port'])
            server.starttls()
            server.login(self.alert_config['email']['username'], self.alert_config['email']['password'])
            
            text = msg.as_string()
            server.sendmail(self.alert_config['email']['username'], self.alert_config['email']['to_email'], text)
            server.quit()
            
            logger.info(f"✅ Email alert sent: {severity}")
            return True
            
        except Exception as e:
            logger.error(f"Email alert error: {e}")
            return False
    
    def log_alert(self, alert_type: str, severity: str, message: str, system_id: str = None, details: Dict = None) -> int:
        """Log alert to database"""
        conn = self.get_db_connection()
        if not conn:
            return None
        
        try:
            cur = conn.cursor()
            
            cur.execute("""
                INSERT INTO system_alerts (alert_type, severity, system_id, message, details, source)
                VALUES (%s, %s, %s, %s, %s, %s)
                RETURNING id
            """, (alert_type, severity, system_id, message, json.dumps(details) if details else None, 'alert_system'))
            
            alert_id = cur.fetchone()[0]
            conn.commit()
            
            return alert_id
            
        except Exception as e:
            logger.error(f"Error logging alert: {e}")
            conn.rollback()
            return None
        finally:
            conn.close()
    
    def should_send_alert(self, alert_key: str) -> bool:
        """Check if alert should be sent (cooldown logic)"""
        now = get_greek_time()

        if alert_key in self.last_alerts:
            time_since_last = (now - self.last_alerts[alert_key]).total_seconds()
            if time_since_last < self.alert_cooldown:
                return False

        self.last_alerts[alert_key] = now
        return True
    
    def check_system_health(self) -> List[Dict]:
        """Check overall system health"""
        alerts = []
        
        conn = self.get_db_connection()
        if not conn:
            alerts.append({
                'type': 'database_connection',
                'severity': 'critical',
                'message': 'Database connection failed',
                'system_id': 'system'
            })
            return alerts
        
        try:
            cur = conn.cursor(cursor_factory=RealDictCursor)
            now = get_greek_time()
            
            # Check data freshness for both systems
            for system_id, table_name in [('system1', 'solax_data'), ('system2', 'solax_data2')]:
                cur.execute(f"""
                    SELECT timestamp, yield_today, soc, ac_power, temperature
                    FROM {table_name} 
                    ORDER BY timestamp DESC 
                    LIMIT 1
                """)
                
                latest = cur.fetchone()
                
                if not latest:
                    alerts.append({
                        'type': 'no_data',
                        'severity': 'critical',
                        'message': f'{system_id}: No data available',
                        'system_id': system_id
                    })
                    continue
                
                # Check data staleness - ensure both timestamps are timezone-aware
                if latest['timestamp'].tzinfo is None:
                    # Database timestamp is naive but stored in UTC, convert properly
                    latest_timestamp = pytz.UTC.localize(latest['timestamp']).astimezone(GREEK_TZ)
                else:
                    latest_timestamp = latest['timestamp'].astimezone(GREEK_TZ)

                data_age = (now - latest_timestamp).total_seconds()
                if data_age > self.alert_config['thresholds']['data_staleness']:
                    alerts.append({
                        'type': 'stale_data',
                        'severity': 'warning',
                        'message': f'{system_id}: Data is {data_age/60:.1f} minutes old',
                        'system_id': system_id,
                        'details': {'data_age_seconds': data_age}
                    })
                
                # Check low SOC (skip during morning charging hours 06:00-12:00)
                is_charging_hours = 6 <= now.hour < 12
                if (latest['soc'] and
                    latest['soc'] < self.alert_config['thresholds']['low_soc'] and
                    not is_charging_hours):
                    alerts.append({
                        'type': 'low_soc',
                        'severity': 'warning',
                        'message': f'{system_id}: Low battery SOC ({latest["soc"]}%)',
                        'system_id': system_id,
                        'details': {'soc': latest['soc']}
                    })
                
                # Check high temperature
                if latest['temperature'] and latest['temperature'] > self.alert_config['thresholds']['high_temperature']:
                    alerts.append({
                        'type': 'high_temperature',
                        'severity': 'warning',
                        'message': f'{system_id}: High temperature ({latest["temperature"]}°C)',
                        'system_id': system_id,
                        'details': {'temperature': latest['temperature']}
                    })
                
                # Check daily yield (only during daytime)
                if 8 <= now.hour <= 18:  # Daytime hours
                    today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
                    cur.execute(f"""
                        SELECT MAX(yield_today) as max_yield
                        FROM {table_name}
                        WHERE timestamp >= %s
                    """, (today_start,))
                    
                    yield_result = cur.fetchone()
                    daily_yield = yield_result['max_yield'] if yield_result else 0
                    
                    if daily_yield and daily_yield < self.alert_config['thresholds']['low_daily_yield']:
                        alerts.append({
                            'type': 'low_daily_yield',
                            'severity': 'info',
                            'message': f'{system_id}: low daily yield {daily_yield:.2f}kWh',
                            'system_id': system_id,
                            'details': {'daily_yield': daily_yield}
                        })
            
            # Check weather data freshness
            cur.execute("""
                SELECT timestamp FROM weather_data 
                ORDER BY timestamp DESC 
                LIMIT 1
            """)
            
            weather_latest = cur.fetchone()
            if weather_latest:
                # Ensure weather timestamp is timezone-aware
                if weather_latest['timestamp'].tzinfo is None:
                    # Weather timestamp is also stored in UTC, convert properly
                    weather_timestamp = pytz.UTC.localize(weather_latest['timestamp']).astimezone(GREEK_TZ)
                else:
                    weather_timestamp = weather_latest['timestamp'].astimezone(GREEK_TZ)

                weather_age = (now - weather_timestamp).total_seconds()
                if weather_age > 7200:  # 2 hours
                    alerts.append({
                        'type': 'stale_weather_data',
                        'severity': 'info',
                        'message': f'Weather data is {weather_age/3600:.1f} hours old',
                        'system_id': 'weather',
                        'details': {'weather_age_hours': weather_age/3600}
                    })
            
            conn.close()
            return alerts
            
        except Exception as e:
            logger.error(f"Health check error: {e}")
            conn.close()
            return [{
                'type': 'health_check_error',
                'severity': 'critical',
                'message': f'Health check failed: {str(e)}',
                'system_id': 'system'
            }]
    
    def process_alerts(self, alerts: List[Dict]):
        """Process and send alerts"""
        
        for alert in alerts:
            alert_key = f"{alert['type']}_{alert.get('system_id', 'system')}"
            
            # Check cooldown
            if not self.should_send_alert(alert_key):
                continue
            
            # Log alert to database
            alert_id = self.log_alert(
                alert['type'],
                alert['severity'],
                alert['message'],
                alert.get('system_id'),
                alert.get('details')
            )
            
            # Send notifications
            notification_sent = False
            
            # Send Telegram notification
            if self.send_telegram_alert(alert['message'], alert['severity']):
                notification_sent = True
            
            # Send email for critical alerts
            if alert['severity'] == 'critical':
                if self.send_email_alert(f"Critical Alert: {alert['type']}", alert['message'], alert['severity']):
                    notification_sent = True
            
            # Update notification status
            if alert_id and notification_sent:
                conn = self.get_db_connection()
                if conn:
                    try:
                        cur = conn.cursor()
                        cur.execute("""
                            UPDATE system_alerts 
                            SET notification_sent = TRUE 
                            WHERE id = %s
                        """, (alert_id,))
                        conn.commit()
                        conn.close()
                    except:
                        conn.close()
            
            logger.info(f"🚨 Alert processed: {alert['type']} - {alert['severity']}")
    
    def run_monitoring_cycle(self):
        """Run single monitoring cycle"""
        logger.info("🔍 Running alert monitoring cycle...")
        
        try:
            # Check system health
            alerts = self.check_system_health()
            
            if alerts:
                logger.info(f"⚠️ Found {len(alerts)} alerts")
                self.process_alerts(alerts)
            else:
                logger.info("✅ No alerts detected")
            
            return len(alerts)
            
        except Exception as e:
            logger.error(f"Monitoring cycle error: {e}")
            return -1
    
    def run_continuous_monitoring(self, interval_minutes: int = 5):
        """Run continuous alert monitoring"""
        self.is_running = True
        interval_seconds = interval_minutes * 60
        
        logger.info(f"🚀 Starting continuous alert monitoring (interval: {interval_minutes} minutes)")
        logger.info("="*70)
        
        # Send startup notification
        self.send_telegram_alert(
            "🚀 Alert System Started\n\nContinuous monitoring active with 5-minute intervals.\n\nMonitoring:\n• System health\n• Data freshness\n• SOC levels\n• Temperature\n• Daily yield",
            "success"
        )
        
        try:
            while self.is_running:
                start_time = time.time()
                
                # Run monitoring cycle
                alert_count = self.run_monitoring_cycle()
                
                # Log cycle completion
                if alert_count >= 0:
                    logger.info(f"📈 Monitoring cycle completed: {alert_count} alerts processed")
                else:
                    logger.warning("⚠️ Monitoring cycle failed")
                
                # Wait for next cycle
                elapsed = time.time() - start_time
                sleep_time = max(0, interval_seconds - elapsed)
                
                if sleep_time > 0:
                    logger.debug(f"⏰ Waiting {sleep_time/60:.1f} minutes for next cycle...")
                    time.sleep(sleep_time)
                
        except KeyboardInterrupt:
            logger.info("🛑 Alert monitoring stopped by user")
        except Exception as e:
            logger.error(f"❌ Alert monitoring error: {e}")
            # Send error notification
            self.send_telegram_alert(
                f"❌ Alert System Error\n\nMonitoring stopped due to error:\n{str(e)}",
                "critical"
            )
        finally:
            self.is_running = False
            logger.info("🔚 Alert monitoring stopped")
    
    def test_notifications(self):
        """Test notification systems"""
        logger.info("🧪 Testing notification systems...")
        
        test_message = "🧪 Test Alert\n\nThis is a test notification from the Solar Alert System.\n\nAll systems operational!"
        
        # Test Telegram
        telegram_success = self.send_telegram_alert(test_message, "info")
        
        # Test Email (if configured)
        email_success = self.send_email_alert("Test Alert", test_message, "info")
        
        logger.info(f"📊 Test Results:")
        logger.info(f"   Telegram: {'✅ Success' if telegram_success else '❌ Failed'}")
        logger.info(f"   Email: {'✅ Success' if email_success else '❌ Failed'}")
        
        return telegram_success, email_success

def main():
    """Main function"""

    print("🚨 SOLAR ALERT SYSTEM")
    print("="*60)
    print("📊 Real-time monitoring with Telegram & Email alerts")
    print("🔄 Monitors system health, data freshness, and performance")
    print()

    try:
        # Start health server first in background thread
        print("🏥 Starting health server on port 8107...")
        health_thread = threading.Thread(target=run_health_server, daemon=True)
        health_thread.start()

        alert_system = AlertSystem()

        # Test database connection
        conn = alert_system.get_db_connection()
        if not conn:
            print("❌ Database connection failed!")
            return False
        conn.close()
        print("✅ Database connection successful")

        # Test notifications
        print("\n🧪 Testing notification systems...")
        telegram_ok, email_ok = alert_system.test_notifications()

        if not telegram_ok:
            print("⚠️ Telegram notifications failed - check bot token and chat ID")

        # Run single monitoring cycle
        print("\n🔍 Running test monitoring cycle...")
        alert_count = alert_system.run_monitoring_cycle()

        if alert_count >= 0:
            print(f"✅ Test cycle successful! {alert_count} alerts detected")
        else:
            print("❌ Test cycle failed!")
            return False

        # Ask user if they want to start continuous monitoring
        print("\n🚀 Ready to start continuous alert monitoring!")
        print("Press Ctrl+C to stop at any time")
        print()

        # Start continuous monitoring
        alert_system.run_continuous_monitoring(interval_minutes=5)

        return True

    except Exception as e:
        print(f"❌ Alert system failed: {e}")
        logger.exception("Alert system failed")
        return False

# Health endpoint for Docker health check
health_app = FastAPI()

@health_app.get("/health")
async def health_check():
    """Health check endpoint for Alert system"""
    try:
        # Check database connectivity
        alert_system = AlertSystem()
        conn = alert_system.get_db_connection()
        db_connected = conn is not None
        if conn:
            conn.close()

        health_status = {
            "status": "healthy" if db_connected else "unhealthy",
            "service": "alert_system",
            "timestamp": datetime.now().isoformat(),
            "database_connected": db_connected,
            "telegram_configured": bool(alert_system.alert_config.get('telegram', {}).get('bot_token')),
            "email_configured": bool(alert_system.alert_config.get('email', {}).get('smtp_server'))
        }

        return JSONResponse(content=health_status)
    except Exception as e:
        return JSONResponse(
            content={
                "status": "unhealthy",
                "service": "alert_system",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            },
            status_code=500
        )

def run_health_server():
    """Run health check server in background"""
    uvicorn.run(health_app, host="0.0.0.0", port=8107, log_level="warning")

if __name__ == "__main__":
    # Start main alert system (health server starts inside main())
    success = main()
    sys.exit(0 if success else 1)
