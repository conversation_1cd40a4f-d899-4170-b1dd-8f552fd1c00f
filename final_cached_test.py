#!/usr/bin/env python3
"""
Final Cached Predictions Test
"""

import requests

BOT_TOKEN = "8060881816:AAFBhGADTAAcUkbom_FEFSkOHoT5N6t5png"
CHAT_ID = "1510889515"

def send_final_test():
    """Send final test message"""
    url = f"https://api.telegram.org/bot{BOT_TOKEN}/sendMessage"
    
    payload = {
        "chat_id": CHAT_ID,
        "text": """🎯 **CACHED PREDICTIONS ΕΝΕΡΓΟΠΟΙΗΘΗΚΑΝ!**

✅ **Predictions**: 
   - Χρησιμοποιεί CACHED forecasts (6 πρωί)
   - System 1: 77.7 kWh (σωστό!)
   - System 2: 76.0 kWh (σωστό!)
   - Γρήγορη απόκριση (<1s)

✅ **ROI & Payback**: 
   - ROI: 30.0% ✅
   - Payback: 3.3 years ✅
   - Production: 24,455 kWh ✅
   - Self consumption: 29.9% ✅

🚀 **Τώρα όλα είναι γρήγορα και σωστά!**
📊 **Cached data από ML script που τρέχει στις 6 πρωί**

🔥 **Δοκιμάστε τώρα - θα είναι αστραπιαίες!**""",
        "parse_mode": "Markdown"
    }
    
    try:
        response = requests.post(url, json=payload, timeout=10)
        if response.status_code == 200:
            print("✅ Final cached test message sent successfully!")
            print("🎯 Cached predictions now active!")
            return True
        else:
            print(f"❌ Failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    send_final_test()
