#!/bin/bash
# 🚀 CREATE AUTOMATED DEPLOYMENT SYSTEM
# This script creates the automated deployment system in the backup directory

set -euo pipefail

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# Configuration
BACKUP_DIR="/media/grlv/CE885018884FFD81/20250619_223156"
CURRENT_DIR="$(pwd)"

log() {
    echo -e "${2:-$NC}[$(date '+%H:%M:%S')] $1${NC}"
}

error_exit() {
    log "❌ ERROR: $1" "$RED"
    exit 1
}

success() {
    log "✅ $1" "$GREEN"
}

progress() {
    log "🔄 $1" "$BLUE"
}

# Check if backup directory exists
check_backup_dir() {
    if [[ ! -d "$BACKUP_DIR" ]]; then
        error_exit "Backup directory not found: $BACKUP_DIR"
    fi
    
    if [[ ! -d "$BACKUP_DIR/docker_images" ]]; then
        error_exit "Docker images directory not found in backup"
    fi
    
    if [[ ! -d "$BACKUP_DIR/docker_volumes" ]]; then
        error_exit "Docker volumes directory not found in backup"
    fi
    
    success "Backup directory structure verified"
}

# Create the main DEPLOY.sh script
create_deploy_script() {
    progress "Creating main DEPLOY.sh script..."
    
    # Copy the DEPLOY.sh we just created
    cp "$CURRENT_DIR/DEPLOY.sh" "$BACKUP_DIR/"
    chmod +x "$BACKUP_DIR/DEPLOY.sh"
    
    success "Main deployment script created"
}

# Create verification script
create_verification_script() {
    progress "Creating verification script..."
    
    cat > "$BACKUP_DIR/VERIFY.sh" << 'EOF'
#!/bin/bash
# 🔍 SOLAR PREDICTION SYSTEM - DEPLOYMENT VERIFICATION

set -euo pipefail

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${2:-$NC}[$(date '+%H:%M:%S')] $1${NC}"
}

# Verification counters
TOTAL_CHECKS=0
PASSED_CHECKS=0

verify() {
    local description="$1"
    local command="$2"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    echo -n "🔍 $description... "
    
    if eval "$command" &> /dev/null; then
        echo -e "${GREEN}✅ PASS${NC}"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        echo -e "${RED}❌ FAIL${NC}"
    fi
}

echo -e "${BLUE}🔍 SOLAR PREDICTION SYSTEM - VERIFICATION${NC}"
echo "=============================================="

# Container checks
log "📦 Checking containers..." "$BLUE"
verify "Database container running" "docker ps | grep -q solar-prediction-db"
verify "Cache container running" "docker ps | grep -q solar-prediction-cache"
verify "Main API container running" "docker ps | grep -q solar-prediction-main"
verify "Billing API container running" "docker ps | grep -q solar-prediction-billing"

# API checks
log "🌐 Checking APIs..." "$BLUE"
verify "Main API responding" "curl -f -s http://localhost:8100/health"
verify "Billing API responding" "curl -f -s http://localhost:8110/health"

# Database checks
log "🗄️ Checking database..." "$BLUE"
verify "Database connection" "docker exec solar-prediction-db psql -U postgres -d solar_prediction -c 'SELECT 1;'"
verify "SolaX data present" "docker exec solar-prediction-db psql -U postgres -d solar_prediction -c 'SELECT COUNT(*) FROM solax_data;' | grep -q '[1-9]'"

# Telegram bot checks
log "🤖 Checking Telegram bot..." "$BLUE"
verify "Telegram bot process" "docker exec solar-prediction-main ps aux | grep -q telegram"

# Calculate results
SUCCESS_RATE=$((PASSED_CHECKS * 100 / TOTAL_CHECKS))

echo ""
echo "📊 VERIFICATION RESULTS"
echo "======================"
echo -e "Total checks: ${BLUE}$TOTAL_CHECKS${NC}"
echo -e "Passed: ${GREEN}$PASSED_CHECKS${NC}"
echo -e "Failed: ${RED}$((TOTAL_CHECKS - PASSED_CHECKS))${NC}"
echo -e "Success rate: ${BLUE}$SUCCESS_RATE%${NC}"

if [[ $SUCCESS_RATE -ge 80 ]]; then
    echo -e "${GREEN}🎉 SYSTEM VERIFICATION SUCCESSFUL!${NC}"
    echo ""
    echo -e "${BLUE}🚀 Next steps:${NC}"
    echo "1. Test Telegram bot: Send /start to your bot"
    echo "2. Access web interface: http://localhost:8080"
    echo "3. Check API health: http://localhost:8100/health"
    exit 0
else
    echo -e "${RED}❌ SYSTEM VERIFICATION FAILED${NC}"
    echo ""
    echo -e "${YELLOW}🔧 Troubleshooting:${NC}"
    echo "1. Check container logs: docker logs CONTAINER_NAME"
    echo "2. Restart services: docker-compose restart"
    echo "3. Check system resources: docker stats"
    exit 1
fi
EOF
    
    chmod +x "$BACKUP_DIR/VERIFY.sh"
    success "Verification script created"
}

# Create quick start guide
create_quick_start() {
    progress "Creating quick start guide..."
    
    cat > "$BACKUP_DIR/QUICK_START.md" << 'EOF'
# 🚀 SOLAR PREDICTION SYSTEM - QUICK START

## One-Command Deployment

```bash
# Navigate to backup directory
cd /media/grlv/CE885018884FFD81/20250619_223156/

# Run automated deployment
./DEPLOY.sh

# Verify deployment
./VERIFY.sh
```

## What This Does

1. **Checks system requirements** (Docker, disk space, ports)
2. **Stops any existing containers** (clean slate)
3. **Loads all Docker images** (15 images, ~16GB)
4. **Restores all volumes** (database, cache, static files)
5. **Deploys all services** (11 containers)
6. **Verifies everything works** (APIs, database, bot)

## Expected Results

- ✅ All containers running healthy
- ✅ All APIs responding (8100, 8110, etc.)
- ✅ Database connected with data
- ✅ Telegram bot ready for testing
- ✅ Web interface at http://localhost:8080

## If Something Goes Wrong

```bash
# Check what's running
docker ps

# Check logs
docker logs solar-prediction-main

# Restart everything
docker-compose restart

# Complete restart
docker-compose down
./DEPLOY.sh --force
```

## Testing the System

1. **Telegram Bot**: Send `/start` to your bot
2. **Web Interface**: Open http://localhost:8080
3. **API Health**: Check http://localhost:8100/health
4. **Billing API**: Check http://localhost:8110/health

## Deployment Options

```bash
# Force deployment (no prompts)
./DEPLOY.sh --force

# Verbose output
./DEPLOY.sh --verbose

# Help
./DEPLOY.sh --help
```

## Success Criteria

- 🎯 **80%+ verification success rate**
- 📱 **Telegram bot responds to /start**
- 🌐 **All APIs return healthy status**
- 🗄️ **Database contains real data**

**Total deployment time: 5-10 minutes**
EOF
    
    success "Quick start guide created"
}

# Create troubleshooting script
create_troubleshooting_script() {
    progress "Creating troubleshooting script..."
    
    cat > "$BACKUP_DIR/TROUBLESHOOT.sh" << 'EOF'
#!/bin/bash
# 🔧 SOLAR PREDICTION SYSTEM - TROUBLESHOOTING

set -euo pipefail

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${2:-$NC}[$(date '+%H:%M:%S')] $1${NC}"
}

echo -e "${BLUE}🔧 SOLAR PREDICTION SYSTEM - TROUBLESHOOTING${NC}"
echo "================================================"

# Check Docker
log "🐳 Checking Docker..." "$BLUE"
if docker info &> /dev/null; then
    log "✅ Docker is running"
else
    log "❌ Docker is not running" "$RED"
    log "💡 Try: sudo systemctl start docker" "$YELLOW"
fi

# Check containers
log "📦 Checking containers..." "$BLUE"
running_containers=$(docker ps --format "{{.Names}}" | grep solar-prediction | wc -l)
log "Running containers: $running_containers"

if [[ $running_containers -eq 0 ]]; then
    log "❌ No Solar Prediction containers running" "$RED"
    log "💡 Try: ./DEPLOY.sh --force" "$YELLOW"
elif [[ $running_containers -lt 4 ]]; then
    log "⚠️  Only $running_containers containers running (expected 4+)" "$YELLOW"
    log "💡 Try: docker-compose up -d" "$YELLOW"
else
    log "✅ Containers are running"
fi

# Check ports
log "🌐 Checking ports..." "$BLUE"
ports=(8100 8110 5433 6380)
for port in "${ports[@]}"; do
    if netstat -tuln 2>/dev/null | grep -q ":$port "; then
        log "✅ Port $port is open"
    else
        log "❌ Port $port is not open" "$RED"
    fi
done

# Check logs for errors
log "📋 Checking recent errors..." "$BLUE"
if docker ps | grep -q solar-prediction-main; then
    error_count=$(docker logs solar-prediction-main --tail 50 2>&1 | grep -i error | wc -l)
    if [[ $error_count -gt 0 ]]; then
        log "⚠️  Found $error_count errors in main container logs" "$YELLOW"
        log "💡 Check: docker logs solar-prediction-main" "$YELLOW"
    else
        log "✅ No recent errors in main container"
    fi
fi

# Check disk space
log "💾 Checking disk space..." "$BLUE"
available_space=$(df / | awk 'NR==2 {print $4}')
available_gb=$((available_space / 1024 / 1024))
if [[ $available_gb -lt 10 ]]; then
    log "❌ Low disk space: ${available_gb}GB available" "$RED"
    log "💡 Need at least 10GB free space" "$YELLOW"
else
    log "✅ Sufficient disk space: ${available_gb}GB available"
fi

# Quick fixes
echo ""
log "🔧 QUICK FIXES:" "$BLUE"
echo "1. Restart all services: docker-compose restart"
echo "2. Complete redeployment: ./DEPLOY.sh --force"
echo "3. Check specific logs: docker logs CONTAINER_NAME"
echo "4. Check system resources: docker stats"
echo "5. Verify deployment: ./VERIFY.sh"

echo ""
log "📞 If problems persist:" "$YELLOW"
echo "1. Check the deployment log files"
echo "2. Ensure system meets requirements (Docker 20.10+, 30GB space)"
echo "3. Try deployment on a clean system"
EOF
    
    chmod +x "$BACKUP_DIR/TROUBLESHOOT.sh"
    success "Troubleshooting script created"
}

# Create README
create_readme() {
    progress "Creating README..."
    
    cat > "$BACKUP_DIR/README.md" << 'EOF'
# 🚀 SOLAR PREDICTION SYSTEM - AUTOMATED DEPLOYMENT

## Overview

This directory contains a **complete backup** of the Solar Prediction System with **fully automated deployment**.

**Zero manual configuration required** - just run one command!

## Quick Start

```bash
# 1. Navigate to this directory
cd /media/grlv/CE885018884FFD81/20250619_223156/

# 2. Run automated deployment
./DEPLOY.sh

# 3. Verify everything works
./VERIFY.sh
```

**That's it!** The system will be deployed automatically.

## What's Included

- ✅ **15 Docker images** (16GB) - All services pre-built
- ✅ **15 Docker volumes** (619MB) - Complete data backup
- ✅ **Database dumps** (146MB) - All 268,395+ records
- ✅ **Project files** (7.4GB) - Complete source code
- ✅ **Automated scripts** - Zero-error deployment

## System Requirements

- **Docker 20.10+**
- **Docker Compose 2.0+**
- **30GB+ free disk space**
- **8GB+ RAM** (recommended)
- **Internet connection** (for external APIs)

## Deployment Features

### ✅ Fully Automated
- No manual configuration
- No editing of files
- No complex commands
- One-click deployment

### ✅ Error-Proof
- Automatic error detection
- Built-in recovery
- Comprehensive validation
- Clear error messages

### ✅ Complete System
- All 11 services
- Database with real data
- Telegram bot ready
- Web interface working
- All APIs functional

## Available Scripts

| Script | Purpose |
|--------|---------|
| `./DEPLOY.sh` | **Main deployment** - Deploys entire system |
| `./VERIFY.sh` | **Verification** - Checks everything works |
| `./TROUBLESHOOT.sh` | **Diagnostics** - Identifies problems |
| `QUICK_START.md` | **Quick guide** - Essential commands |

## Expected Results

After successful deployment:

- 🐳 **11 containers running** (all healthy)
- 🌐 **APIs responding** (8100, 8110, 8103, etc.)
- 🗄️ **Database connected** (268,395+ records)
- 🤖 **Telegram bot ready** (responds to /start)
- 🌍 **Web interface** (http://localhost:8080)

## Deployment Options

```bash
# Standard deployment
./DEPLOY.sh

# Force deployment (skip prompts)
./DEPLOY.sh --force

# Verbose output
./DEPLOY.sh --verbose

# Get help
./DEPLOY.sh --help
```

## Troubleshooting

If deployment fails:

```bash
# 1. Run diagnostics
./TROUBLESHOOT.sh

# 2. Check what's running
docker ps

# 3. Check logs
docker logs solar-prediction-main

# 4. Try force redeployment
./DEPLOY.sh --force
```

## Success Rate

**Target: 95%+ verification success rate**

The automated deployment system is designed to achieve consistent, reliable results with minimal user intervention.

## Support

- 📋 Check deployment logs for detailed information
- 🔧 Use `./TROUBLESHOOT.sh` for automatic diagnosis
- 📚 Refer to technical documentation in the backup
- 🔄 Try `./DEPLOY.sh --force` for clean redeployment

---

**🎯 Goal: Zero-error deployment of production-ready Solar Prediction System**
EOF
    
    success "README created"
}

# Main function
main() {
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                                                              ║"
    echo "║     🚀 CREATING AUTOMATED DEPLOYMENT SYSTEM                  ║"
    echo "║                                                              ║"
    echo "║  This will add automated deployment scripts to the backup    ║"
    echo "║  directory for zero-error deployment on any machine.         ║"
    echo "║                                                              ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    
    progress "Starting automated deployment system creation..."
    
    # Execute steps
    check_backup_dir
    create_deploy_script
    create_verification_script
    create_quick_start
    create_troubleshooting_script
    create_readme
    
    # Final message
    echo -e "${GREEN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                                                              ║"
    echo "║        🎉 AUTOMATED DEPLOYMENT SYSTEM CREATED! 🎉            ║"
    echo "║                                                              ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    
    log "🎯 Automated deployment system ready!" "$GREEN"
    log "📂 Location: $BACKUP_DIR" "$BLUE"
    log "🚀 To deploy: cd $BACKUP_DIR && ./DEPLOY.sh" "$CYAN"
    log "🔍 To verify: cd $BACKUP_DIR && ./VERIFY.sh" "$CYAN"
    
    echo ""
    log "📋 Available files:" "$BLUE"
    log "   DEPLOY.sh - Main automated deployment script" "$NC"
    log "   VERIFY.sh - Deployment verification script" "$NC"
    log "   TROUBLESHOOT.sh - Automatic troubleshooting" "$NC"
    log "   QUICK_START.md - Quick start guide" "$NC"
    log "   README.md - Complete documentation" "$NC"
    
    echo ""
    log "🎉 Ready for zero-error deployment on any machine!" "$GREEN"
}

# Run main function
main "$@"
