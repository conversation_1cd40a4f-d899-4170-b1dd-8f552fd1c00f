version: '3.8'

services:
  # Solar Prediction Application (All Services in One Container)
  solar-prediction-app:
    build:
      context: .
      dockerfile: Dockerfile.monolithic
    container_name: solar-prediction-app
    ports:
      - "8100:8100"  # Main API Server
      - "8109:8109"  # Telegram Bot Health
      - "8110:8110"  # Enhanced Billing API
    environment:
      # Database Configuration
      - DATABASE_URL=********************************************/solar_prediction
      - DATABASE_HOST=postgres
      - DATABASE_PORT=5432
      - DATABASE_USER=postgres
      - DATABASE_PASSWORD=postgres

      # Service Configuration
      - SERVICE_PORT=8100
      - DEBUG=false
      - LOG_LEVEL=info
      - CONTAINER_MODE=true
      - ENVIRONMENT=production
      - PYTHONRECURSIONLIMIT=10000

      # Telegram Bot Configuration
      - TELEGRAM_ENABLED=true
      - TELEGRAM_BOT_TOKEN=**********************************************
      - TELEGRAM_CHAT_ID=**********
      - API_BASE_URL=http://localhost:8100

      # ML Model Configuration - Using Hybrid ML Ensemble (94.31% R²)
      - MODEL_PATH=/app/models/hybrid_ml_ensemble
      - MODEL_VERSION=hybrid_ml_ensemble
      - MODEL_TYPE=hybrid_ensemble

      # SolaX Cloud API Configuration
      - SOLAX_BASE_URL=https://www.solaxcloud.com:9443/proxy/api/getRealtimeInfo.do
      - SOLAX_TOKEN_ID=20250410220826567911082
      - SOLAX_WIFI_SN=SRFQDPDN9W
      - SOLAX_POLLING_INTERVAL=30
      - SOLAX_TIMEOUT=10
      - SOLAX_MAX_RETRIES=3

      # Weather API Configuration
      - WEATHER_BASE_URL=https://api.open-meteo.com/v1/forecast
      - WEATHER_LATITUDE=38.141348260997596
      - WEATHER_LONGITUDE=24.0071653937747
      - WEATHER_POLLING_INTERVAL=3600
      - WEATHER_TIMEZONE=Europe/Athens
      - WEATHER_FORECAST_DAYS=7
      - WEATHER_PAST_DAYS=1

      # Security
      - SECRET_KEY=production-secret-key-change-me
      - ACCESS_TOKEN_EXPIRE_MINUTES=1440

      # Performance
      - WORKERS=1
      - MAX_CONNECTIONS=100
      - KEEP_ALIVE=2

      # Startup Configuration
      - RUN_MIGRATIONS=true

    volumes:
      # Persistent data volumes
      - ./logs:/app/logs
      - ./models:/app/models
      - ./data:/app/data
      - ./scripts:/app/scripts
      - ./docs:/app/docs

    depends_on:
      postgres:
        condition: service_healthy

    networks:
      - solar-network

    restart: unless-stopped

    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8100/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # PostgreSQL Database
  postgres:
    image: postgres:16-alpine
    container_name: solar-prediction-db
    environment:
      - POSTGRES_DB=solar_prediction
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C

    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backup_20250615_164048/complete_database_backup.sql:/docker-entrypoint-initdb.d/01-restore.sql:ro

    ports:
      - "5433:5432"

    networks:
      - solar-network

    restart: unless-stopped

    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d solar_prediction"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

# Named volumes for persistent data
volumes:
  postgres_data:
    driver: local

# Custom network
networks:
  solar-network:
    driver: bridge
