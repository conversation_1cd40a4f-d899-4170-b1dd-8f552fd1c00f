#!/usr/bin/env python3
"""
Configuration Manager Service for Docker Container
Wrapper for the Configuration Manager
"""

import os
import sys
import asyncio
import logging
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """Main entry point for Configuration Manager service"""
    try:
        port = int(os.getenv('SERVICE_PORT', 8108))
        logger.info(f"⚙️ Starting Configuration Manager Service on port {port}...")

        # Run the configuration manager directly
        import subprocess
        result = subprocess.run([
            sys.executable,
            "scripts/frontend_system/configuration_manager.py"
        ], check=True)

    except Exception as e:
        logger.error(f"❌ Failed to start Configuration Manager: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
