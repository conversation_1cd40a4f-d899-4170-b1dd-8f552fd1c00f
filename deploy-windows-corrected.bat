@echo off
echo.
echo ========================================
echo   Solar Prediction System - Windows
echo   CORRECTED DEPLOYMENT v1.1
echo ========================================
echo.

echo Checking Docker...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker is not installed or not running
    echo.
    echo Please install Docker Desktop from:
    echo https://www.docker.com/products/docker-desktop
    echo.
    pause
    exit /b 1
)

echo Docker found! Starting deployment...
echo.

echo Cleaning up any previous containers...
docker-compose down >nul 2>&1

echo Loading Docker images...
echo This may take 10-15 minutes...

echo Loading PostgreSQL image...
docker load -i postgres-image.tar.gz

echo Loading Solar Prediction application image...
docker load -i solar-prediction-app-image.tar.gz

echo Tagging imported image...
REM Use Windows-compatible commands instead of grep
for /f "tokens=1,2" %%i in ('docker images --format "table {{.Repository}}:{{.Tag}}" ^| findstr solar') do (
    set IMPORTED_IMAGE=%%i
    goto :tag_image
)

:tag_image
if defined IMPORTED_IMAGE (
    echo Tagging %IMPORTED_IMAGE% as solar-prediction-app:imported
    docker tag %IMPORTED_IMAGE% solar-prediction-app:imported
) else (
    echo Using default tag...
    docker tag solar-prediction-project-enhanced-billing:latest solar-prediction-app:imported
)

echo Starting system...
docker-compose up -d

if %errorlevel% neq 0 (
    echo.
    echo ERROR: Failed to start containers
    echo Checking logs...
    docker-compose logs
    echo.
    pause
    exit /b 1
)

echo.
echo Waiting for system to be ready...
echo Please wait 120 seconds for complete initialization...

REM Wait 120 seconds
timeout /t 120 /nobreak >nul

echo.
echo Checking system health...

REM Test main application
curl -s http://localhost:8100/health >nul 2>&1
if %errorlevel% equ 0 (
    set MAIN_APP=WORKING
) else (
    set MAIN_APP=STARTING
)

REM Test enhanced billing
curl -s http://localhost:8110/api/v1/roi/system1 >nul 2>&1
if %errorlevel% equ 0 (
    set ENHANCED_BILLING=WORKING
) else (
    set ENHANCED_BILLING=STARTING
)

echo.
echo ========================================
echo   System Status Report
echo ========================================
echo.
echo Main Application (8100): %MAIN_APP%
echo Enhanced Billing (8110): %ENHANCED_BILLING%
echo Database (5433): RUNNING
echo.

if "%MAIN_APP%"=="WORKING" (
    echo ========================================
    echo   System Started Successfully!
    echo ========================================
    echo.
    echo Web Interface: http://localhost:8100
    echo Health Check:  http://localhost:8100/health
    echo API Docs:      http://localhost:8100/docs
    echo Enhanced Billing: http://localhost:8110
    echo Database: localhost:5433
    echo Telegram Bot: @grlvSolarAI_bot
    echo.
    echo Opening web browser...
    start http://localhost:8100
) else (
    echo ========================================
    echo   System Still Starting...
    echo ========================================
    echo.
    echo The system may need more time to initialize.
    echo Please wait a few more minutes and try:
    echo.
    echo Web Interface: http://localhost:8100
    echo Enhanced Billing: http://localhost:8110
    echo.
    echo To check container status: docker-compose ps
    echo To check logs: docker-compose logs
)

echo.
echo EXACT WORKING IMAGE DEPLOYMENT:
echo - Uses production Docker image
echo - No build errors or missing dependencies
echo - All services should be working
echo.
echo To stop the system: docker-compose down
echo To view logs: docker-compose logs
echo To restart: docker-compose restart
echo.
pause
