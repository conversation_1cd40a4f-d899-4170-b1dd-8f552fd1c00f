#!/bin/bash

# Complete Solar Prediction System Stop Script
# Stops Telegram Bot, Production API, and PostgreSQL

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🛑 COMPLETE SOLAR PREDICTION SYSTEM SHUTDOWN${NC}"
echo "============================================================"
echo

# Function to check if a process is running
check_process() {
    local process_name=$1
    if pgrep -f "$process_name" >/dev/null; then
        return 0  # Process is running
    else
        return 1  # Process is not running
    fi
}

# Function to check if a port is in use
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0  # Port is in use
    else
        return 1  # Port is free
    fi
}

# Step 1: Stop Telegram Bot
echo -e "${YELLOW}📋 STEP 1: Stopping Telegram Bot${NC}"
echo "------------------------------------------------------------"

if check_process "greek_telegram_bot.py"; then
    echo "🤖 Stopping Telegram Bot..."
    pkill -f "greek_telegram_bot.py"
    sleep 3
    
    if check_process "greek_telegram_bot.py"; then
        echo -e "${RED}❌ Telegram Bot still running, force killing...${NC}"
        pkill -9 -f "greek_telegram_bot.py" || true
        sleep 2
    fi
    
    if ! check_process "greek_telegram_bot.py"; then
        echo -e "${GREEN}✅ Telegram Bot stopped successfully${NC}"
    else
        echo -e "${RED}❌ Failed to stop Telegram Bot${NC}"
    fi
else
    echo -e "${GREEN}✅ Telegram Bot was not running${NC}"
fi

echo

# Step 2: Stop Production API
echo -e "${YELLOW}📋 STEP 2: Stopping Production API${NC}"
echo "------------------------------------------------------------"

if check_process "scripts/production_app.py"; then
    echo "🚀 Stopping Production API..."
    pkill -f "scripts/production_app.py"
    sleep 3

    if check_process "scripts/production_app.py"; then
        echo -e "${RED}❌ Production API still running, force killing...${NC}"
        pkill -9 -f "scripts/production_app.py" || true
        sleep 2
    fi

    if ! check_process "scripts/production_app.py"; then
        echo -e "${GREEN}✅ Production API stopped successfully${NC}"
    else
        echo -e "${RED}❌ Failed to stop Production API${NC}"
    fi
else
    echo -e "${GREEN}✅ Production API was not running${NC}"
fi

echo

# Step 3: Stop Enhanced Billing System
echo -e "${YELLOW}📋 STEP 3: Stopping Enhanced Billing System${NC}"
echo "------------------------------------------------------------"

if check_process "enhanced_billing_system.py"; then
    echo "💰 Stopping Enhanced Billing System..."
    pkill -f "enhanced_billing_system.py"
    sleep 3

    if check_process "enhanced_billing_system.py"; then
        echo -e "${RED}❌ Enhanced Billing still running, force killing...${NC}"
        pkill -9 -f "enhanced_billing_system.py" || true
        sleep 2
    fi

    if ! check_process "enhanced_billing_system.py"; then
        echo -e "${GREEN}✅ Enhanced Billing System stopped successfully${NC}"
    else
        echo -e "${RED}❌ Failed to stop Enhanced Billing System${NC}"
    fi
else
    echo -e "${GREEN}✅ Enhanced Billing System was not running${NC}"
fi

echo

# Step 4: Stop PostgreSQL (optional)
echo -e "${YELLOW}📋 STEP 3: PostgreSQL Database${NC}"
echo "------------------------------------------------------------"

read -p "Do you want to stop PostgreSQL database? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    if check_port 5433; then
        echo "🐘 Stopping PostgreSQL container..."
        cd /home/<USER>/solar-prediction-project
        docker compose down postgres
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ PostgreSQL stopped successfully${NC}"
        else
            echo -e "${RED}❌ Failed to stop PostgreSQL${NC}"
        fi
    else
        echo -e "${GREEN}✅ PostgreSQL was not running${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  PostgreSQL left running (as requested)${NC}"
fi

echo

# Step 5: Final Status Check
echo -e "${YELLOW}📋 STEP 5: Final Status Check${NC}"
echo "------------------------------------------------------------"

echo "🔍 Checking remaining services..."
echo

# Check Telegram Bot
if check_process "greek_telegram_bot.py"; then
    echo -e "🤖 Telegram Bot: ${RED}❌ Still running${NC}"
else
    echo -e "🤖 Telegram Bot: ${GREEN}✅ Stopped${NC}"
fi

# Check Production API
if check_process "scripts/production_app.py"; then
    echo -e "🚀 Production API: ${RED}❌ Still running${NC}"
else
    echo -e "🚀 Production API: ${GREEN}✅ Stopped${NC}"
fi

# Check Enhanced Billing System
if check_process "enhanced_billing_system.py"; then
    echo -e "💰 Enhanced Billing: ${RED}❌ Still running${NC}"
else
    echo -e "💰 Enhanced Billing: ${GREEN}✅ Stopped${NC}"
fi

# Check PostgreSQL
if check_port 5433; then
    echo -e "🐘 PostgreSQL: ${YELLOW}⚠️  Still running${NC}"
else
    echo -e "🐘 PostgreSQL: ${GREEN}✅ Stopped${NC}"
fi

echo

# Step 6: Cleanup Information
echo -e "${YELLOW}📋 STEP 6: Cleanup Information${NC}"
echo "------------------------------------------------------------"

echo "🧹 Additional cleanup commands (if needed):"
echo "   • Kill all Python processes: pkill python3"
echo "   • Stop all Docker containers: docker stop \$(docker ps -q)"
echo "   • Remove Docker containers: docker compose down"
echo "   • Check running processes: ps aux | grep -E '(python|docker)'"
echo

echo -e "${GREEN}🎉 SOLAR SYSTEM SHUTDOWN COMPLETED!${NC}"
echo "============================================================"
echo
echo "💡 To restart the system, run:"
echo "   ./start_complete_solar_system.sh"
echo
