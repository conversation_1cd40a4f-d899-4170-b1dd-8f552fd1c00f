#!/usr/bin/env python3
import sys
sys.path.append('/home/<USER>/solar-prediction-project')
from scripts.ml.enhanced_feature_engineering import EnhancedFeatureEngineer
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from sklearn.ensemble import Grad<PERSON><PERSON>oostingRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import joblib
import json

def train_enhanced_model(system_id=1):
    print(f"🚀 Training enhanced model for System {system_id}")
    
    # Create features
    engineer = EnhancedFeatureEngineer()
    end_date = datetime.now()
    start_date = end_date - timedelta(days=90)  # 3 months of data
    
    df = engineer.create_enhanced_features(start_date, end_date, system_id)
    
    # Prepare features and target
    feature_columns = [
        'hour_sin', 'hour_cos', 'day_sin', 'day_cos',
        'solar_elevation_factor', 'ghi_primary', 'temperature_primary',
        'combined_efficiency'
    ]
    
    # Filter available features
    available_features = [col for col in feature_columns if col in df.columns]
    
    if len(available_features) < 4:
        print(f"❌ Insufficient features: {len(available_features)}")
        return False
    
    X = df[available_features].fillna(0)
    y = df['ac_power'].fillna(0)
    
    # Remove zero power records for training
    mask = y > 0
    X = X[mask]
    y = y[mask]
    
    if len(X) < 100:
        print(f"❌ Insufficient training data: {len(X)} records")
        return False
    
    # Train-test split
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42
    )
    
    # Train model
    model = GradientBoostingRegressor(
        n_estimators=100,
        learning_rate=0.1,
        max_depth=6,
        random_state=42
    )
    
    model.fit(X_train, y_train)
    
    # Evaluate
    y_pred = model.predict(X_test)
    mae = mean_absolute_error(y_test, y_pred)
    rmse = np.sqrt(mean_squared_error(y_test, y_pred))
    r2 = r2_score(y_test, y_pred)
    
    print(f"📊 Model Performance:")
    print(f"   MAE: {mae:.2f} W")
    print(f"   RMSE: {rmse:.2f} W")
    print(f"   R²: {r2:.4f}")
    
    # Save model
    model_path = f"/home/<USER>/solar-prediction-project/models/enhanced_model_system_{system_id}.joblib"
    joblib.dump(model, model_path)
    
    # Save metadata
    metadata = {
        'model_type': 'enhanced_gradient_boosting',
        'system_id': system_id,
        'training_date': datetime.now().isoformat(),
        'features': available_features,
        'performance': {'mae': mae, 'rmse': rmse, 'r2': r2},
        'training_samples': len(X_train),
        'test_samples': len(X_test)
    }
    
    with open(f"/home/<USER>/solar-prediction-project/models/enhanced_model_system_{system_id}_metadata.json", 'w') as f:
        json.dump(metadata, f, indent=2)
    
    print(f"✅ Enhanced model saved: {model_path}")
    return True

if __name__ == "__main__":
    for system_id in [1, 2]:
        try:
            train_enhanced_model(system_id)
        except Exception as e:
            print(f"❌ Training failed for System {system_id}: {e}")
