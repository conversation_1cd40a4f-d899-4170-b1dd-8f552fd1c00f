#!/usr/bin/env python3
"""
Multi-Source Model Training Pipeline
Implements advanced ML training with integrated data sources
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import logging
import joblib
import json

from sklearn.model_selection import TimeSeriesSplit, cross_val_score
from sklearn.ensemble import VotingRegressor, GradientBoostingRegressor
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.preprocessing import StandardScaler
import xgboost as xgb

from src.data_integration.multi_source_manager import MultiSourceDataManager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MultiSourceDataLoader:
    """Data loader for multi-source training"""
    
    def __init__(self, manager: MultiSourceDataManager):
        self.manager = manager
        self.scaler = StandardScaler()
    
    def load_training_data(self, 
                          start_date: str, 
                          end_date: str,
                          target_variable: str = 'yield_today') -> Tuple[pd.DataFrame, pd.Series]:
        """
        Load training data with multi-source features
        
        Args:
            start_date: Start date (YYYY-MM-DD)
            end_date: End date (YYYY-MM-DD)
            target_variable: Target variable name
            
        Returns:
            Features DataFrame and target Series
        """
        
        logger.info(f"Loading training data from {start_date} to {end_date}")
        
        start_time = datetime.strptime(start_date, '%Y-%m-%d')
        end_time = datetime.strptime(end_date, '%Y-%m-%d')
        
        # Define feature variables
        feature_variables = ['ghi', 'temperature', 'cloud_cover', 'humidity', 'wind_speed']
        
        # Get integrated data
        integrated_df = self.manager.get_integrated_data(
            variables=feature_variables,
            start_time=start_time,
            end_time=end_time,
            time_resolution=timedelta(hours=1)
        )
        
        logger.info(f"Loaded {len(integrated_df)} integrated records")
        
        # Load target data (SolaX yield data)
        target_df = self._load_target_data(start_time, end_time, target_variable)
        
        # Merge features with targets
        merged_df = self._merge_features_targets(integrated_df, target_df)
        
        # Feature engineering
        engineered_df = self._engineer_features(merged_df)
        
        # Prepare X and y
        feature_columns = [col for col in engineered_df.columns 
                          if col not in ['timestamp', target_variable]]
        
        X = engineered_df[feature_columns].copy()
        y = engineered_df[target_variable].copy()
        
        # Remove rows with missing targets
        valid_mask = y.notna()
        X = X[valid_mask]
        y = y[valid_mask]
        
        logger.info(f"Final dataset: {len(X)} samples, {len(X.columns)} features")
        
        return X, y
    
    def _load_target_data(self, start_time: datetime, end_time: datetime, target_variable: str) -> pd.DataFrame:
        """Load target variable data from SolaX systems"""
        
        import psycopg2
        
        conn = psycopg2.connect(
            host='localhost',
            database='solar_prediction',
            user='postgres',
            password='postgres'
        )
        
        # Load from both SolaX systems
        query = """
            SELECT 
                DATE_TRUNC('hour', timestamp) as hour_timestamp,
                MAX(yield_today) as max_yield_system1
            FROM solax_data 
            WHERE timestamp BETWEEN %s AND %s
            GROUP BY DATE_TRUNC('hour', timestamp)
            
            UNION ALL
            
            SELECT 
                DATE_TRUNC('hour', timestamp) as hour_timestamp,
                MAX(yield_today) as max_yield_system2
            FROM solax_data2 
            WHERE timestamp BETWEEN %s AND %s
            GROUP BY DATE_TRUNC('hour', timestamp)
        """
        
        df = pd.read_sql(query, conn, params=[start_time, end_time, start_time, end_time])
        conn.close()
        
        # Aggregate both systems
        df_grouped = df.groupby('hour_timestamp').agg({
            'max_yield_system1': 'sum',
            'max_yield_system2': 'sum'
        }).reset_index()
        
        df_grouped['total_yield'] = df_grouped['max_yield_system1'].fillna(0) + df_grouped['max_yield_system2'].fillna(0)
        df_grouped = df_grouped.rename(columns={'hour_timestamp': 'timestamp', 'total_yield': target_variable})
        
        return df_grouped[['timestamp', target_variable]]
    
    def _merge_features_targets(self, features_df: pd.DataFrame, targets_df: pd.DataFrame) -> pd.DataFrame:
        """Merge features with targets on timestamp"""
        
        # Ensure timestamp columns are datetime
        features_df['timestamp'] = pd.to_datetime(features_df['timestamp'])
        targets_df['timestamp'] = pd.to_datetime(targets_df['timestamp'])
        
        # Merge on timestamp
        merged_df = pd.merge(features_df, targets_df, on='timestamp', how='inner')
        
        logger.info(f"Merged dataset: {len(merged_df)} records")
        
        return merged_df
    
    def _engineer_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Engineer additional features"""
        
        df = df.copy()
        
        # Time-based features
        df['hour'] = df['timestamp'].dt.hour
        df['day_of_year'] = df['timestamp'].dt.dayofyear
        df['month'] = df['timestamp'].dt.month
        
        # Cyclical encoding
        df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
        df['day_sin'] = np.sin(2 * np.pi * df['day_of_year'] / 365)
        df['day_cos'] = np.cos(2 * np.pi * df['day_of_year'] / 365)
        
        # Solar geometry (simplified)
        df['solar_elevation'] = self._calculate_solar_elevation(df['timestamp'])
        
        # Weather interactions
        if 'ghi' in df.columns and 'cloud_cover' in df.columns:
            df['clear_sky_index'] = df['ghi'] / (df['ghi'].max() * (1 - df['cloud_cover'] / 100))
            df['clear_sky_index'] = df['clear_sky_index'].clip(0, 2)  # Reasonable bounds
        
        # Data quality features
        quality_features = []
        for col in df.columns:
            if col.endswith('_confidence'):
                quality_features.append(col)
        
        if quality_features:
            df['avg_confidence'] = df[quality_features].mean(axis=1)
        
        logger.info(f"Engineered {len(df.columns)} features")
        
        return df
    
    def _calculate_solar_elevation(self, timestamps: pd.Series) -> pd.Series:
        """Calculate simplified solar elevation angle"""
        
        # Simplified solar elevation calculation
        # In production, use a proper solar position library like pvlib
        
        hour = timestamps.dt.hour
        day_of_year = timestamps.dt.dayofyear
        
        # Solar declination (simplified)
        declination = 23.45 * np.sin(np.radians(360 * (284 + day_of_year) / 365))
        
        # Hour angle
        hour_angle = 15 * (hour - 12)
        
        # Solar elevation (simplified for latitude ~38°)
        latitude = 38.14
        elevation = np.arcsin(
            np.sin(np.radians(declination)) * np.sin(np.radians(latitude)) +
            np.cos(np.radians(declination)) * np.cos(np.radians(latitude)) * np.cos(np.radians(hour_angle))
        )
        
        return np.degrees(elevation).clip(0, 90)

class MultiSourceModelTrainer:
    """Advanced model trainer for multi-source data"""
    
    def __init__(self, data_loader: MultiSourceDataLoader):
        self.data_loader = data_loader
        self.models = {}
        self.scalers = {}
        self.feature_importance = {}
        self.training_history = []
    
    def create_models(self) -> Dict:
        """Create ensemble of models"""
        
        models = {
            'xgboost': xgb.XGBRegressor(
                n_estimators=200,
                max_depth=8,
                learning_rate=0.1,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=42
            ),
            'gradient_boosting': GradientBoostingRegressor(
                n_estimators=200,
                max_depth=6,
                learning_rate=0.1,
                subsample=0.8,
                random_state=42
            ),
            'ensemble': VotingRegressor([
                ('xgb', xgb.XGBRegressor(n_estimators=100, random_state=42)),
                ('gb', GradientBoostingRegressor(n_estimators=100, random_state=42))
            ])
        }
        
        return models
    
    def train_models(self, 
                    start_date: str, 
                    end_date: str,
                    target_variable: str = 'yield_today') -> Dict:
        """
        Train all models with time series cross-validation
        
        Args:
            start_date: Training start date
            end_date: Training end date
            target_variable: Target variable name
            
        Returns:
            Training results dictionary
        """
        
        logger.info(f"Starting model training: {start_date} to {end_date}")
        
        # Load data
        X, y = self.data_loader.load_training_data(start_date, end_date, target_variable)
        
        if len(X) == 0:
            logger.error("No training data available")
            return {}
        
        # Scale features
        scaler = StandardScaler()
        X_scaled = pd.DataFrame(
            scaler.fit_transform(X),
            columns=X.columns,
            index=X.index
        )
        
        self.scalers['feature_scaler'] = scaler
        
        # Create models
        models = self.create_models()
        
        # Time series cross-validation
        tscv = TimeSeriesSplit(n_splits=5)
        
        results = {}
        
        for model_name, model in models.items():
            logger.info(f"Training {model_name}...")
            
            try:
                # Cross-validation scores
                cv_scores = cross_val_score(
                    model, X_scaled, y, 
                    cv=tscv, 
                    scoring='neg_mean_absolute_error',
                    n_jobs=-1
                )
                
                # Train on full dataset
                model.fit(X_scaled, y)
                
                # Make predictions
                y_pred = model.predict(X_scaled)
                
                # Calculate metrics
                mae = mean_absolute_error(y, y_pred)
                rmse = np.sqrt(mean_squared_error(y, y_pred))
                r2 = r2_score(y, y_pred)
                
                # Store results
                results[model_name] = {
                    'model': model,
                    'cv_scores': cv_scores,
                    'cv_mean': -cv_scores.mean(),
                    'cv_std': cv_scores.std(),
                    'mae': mae,
                    'rmse': rmse,
                    'r2': r2,
                    'feature_importance': self._get_feature_importance(model, X.columns)
                }
                
                logger.info(f"{model_name} - MAE: {mae:.3f}, RMSE: {rmse:.3f}, R²: {r2:.3f}")
                
            except Exception as e:
                logger.error(f"Training failed for {model_name}: {e}")
                results[model_name] = {'error': str(e)}
        
        # Store models and results
        self.models = {name: result.get('model') for name, result in results.items() if 'model' in result}
        
        # Training history
        training_record = {
            'timestamp': datetime.now().isoformat(),
            'start_date': start_date,
            'end_date': end_date,
            'target_variable': target_variable,
            'n_samples': len(X),
            'n_features': len(X.columns),
            'results': {name: {k: v for k, v in result.items() if k != 'model'} 
                       for name, result in results.items()}
        }
        
        self.training_history.append(training_record)
        
        return results
    
    def _get_feature_importance(self, model, feature_names) -> Dict:
        """Extract feature importance from model"""
        
        try:
            if hasattr(model, 'feature_importances_'):
                importance = model.feature_importances_
            elif hasattr(model, 'coef_'):
                importance = np.abs(model.coef_)
            else:
                return {}
            
            return dict(zip(feature_names, importance))
            
        except Exception as e:
            logger.warning(f"Could not extract feature importance: {e}")
            return {}
    
    def save_models(self, model_dir: str = 'models/multi_source'):
        """Save trained models and metadata"""
        
        os.makedirs(model_dir, exist_ok=True)
        
        # Save models
        for name, model in self.models.items():
            if model is not None:
                model_path = os.path.join(model_dir, f'{name}_model.joblib')
                joblib.dump(model, model_path)
                logger.info(f"Saved {name} model to {model_path}")
        
        # Save scalers
        for name, scaler in self.scalers.items():
            scaler_path = os.path.join(model_dir, f'{name}.joblib')
            joblib.dump(scaler, scaler_path)
        
        # Save training history
        history_path = os.path.join(model_dir, 'training_history.json')
        with open(history_path, 'w') as f:
            json.dump(self.training_history, f, indent=2)
        
        logger.info(f"Models and metadata saved to {model_dir}")

def main():
    """Main training function"""
    
    print("🤖 Multi-Source Model Training Pipeline")
    print("=" * 60)
    print(f"📅 Training Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        # Initialize components
        manager = MultiSourceDataManager()
        data_loader = MultiSourceDataLoader(manager)
        trainer = MultiSourceModelTrainer(data_loader)
        
        # Training period - last 30 days
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        
        print(f"📊 Training period: {start_date} to {end_date}")
        
        # Train models
        results = trainer.train_models(start_date, end_date)
        
        if results:
            print("\n🎯 Training Results:")
            for model_name, result in results.items():
                if 'error' in result:
                    print(f"   ❌ {model_name}: {result['error']}")
                else:
                    print(f"   ✅ {model_name}: MAE={result['mae']:.3f}, R²={result['r2']:.3f}")
            
            # Save models
            trainer.save_models()
            print("\n💾 Models saved successfully")
            
            print("\n🎉 Multi-source model training completed!")
            return True
        else:
            print("\n❌ No models were trained successfully")
            return False
            
    except Exception as e:
        print(f"\n❌ Training failed: {e}")
        logger.exception("Training failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
