#!/usr/bin/env python3
"""
Simple Greek Test
"""

import requests

BOT_TOKEN = "8060881816:AAFBhGADTAAcUkbom_FEFSkOHoT5N6t5png"
CHAT_ID = "1510889515"

def send_simple_test():
    """Send simple test message"""
    url = f"https://api.telegram.org/bot{BOT_TOKEN}/sendMessage"
    
    payload = {
        "chat_id": CHAT_ID,
        "text": "🇬🇷 ΕΛΛΗΝΙΚΑ ΜΕΝΟΥ ΔΙΟΡΘΩΘΗΚΑΝ!\n\n✅ ROI επιλογές 1 & 2 τώρα στα ελληνικά\n🚀 Δοκιμάστε τα ROI μενού!"
    }
    
    try:
        response = requests.post(url, json=payload, timeout=10)
        if response.status_code == 200:
            print("✅ Simple Greek test message sent successfully!")
            return True
        else:
            print(f"❌ Failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    send_simple_test()
