# Solar Prediction System - COMPREHENSIVE DIAGNOSTIC SCRIPTS

## 🎯 Purpose

These diagnostic scripts perform **COMPLETE SYSTEM VERIFICATION** for the Solar Prediction System deployed from the EXACT working Docker image export.

## 📦 Scripts Included

### **Windows: `comprehensive-diagnostics.bat`**
- Complete diagnostic testing for Windows systems
- Collects ALL logs and performs connectivity tests
- Tests ALL API endpoints and Telegram bot functionality

### **Unix/Linux: `comprehensive-diagnostics.sh`**
- Complete diagnostic testing for Unix/Linux/macOS systems
- Same functionality as Windows version
- Bash-compatible script

### **Windows: `deploy-windows-corrected.bat`**
- CORRECTED Windows deployment script
- Fixes the `grep` command issue from original deployment
- Includes proper error handling and status verification

### **Windows: `test-system-windows.bat`**
- Quick system verification script
- Tests key endpoints without full diagnostics
- Faster alternative for basic health checks

## 🔍 What the Comprehensive Diagnostics Test

### **1. System Information**
- Operating System details
- Docker and Docker Compose versions
- Available memory and disk space
- System resource usage

### **2. Docker Status**
- Docker service status
- All Docker images, containers, networks, volumes
- Container resource usage and performance metrics

### **3. Container Logs**
- Solar Prediction application logs
- PostgreSQL database logs
- Combined logs from all services
- Background task status and processes

### **4. Network Connectivity**
- Localhost connectivity
- Port availability (8100, 8110, 5433)
- Network configuration

### **5. API Endpoints Testing**
- **Main Application:** http://localhost:8100/health
- **API Documentation:** http://localhost:8100/docs
- **SolaX Data:** http://localhost:8100/api/v1/data/solax/latest
- **Weather Data:** http://localhost:8100/api/v1/data/weather/latest
- **Enhanced Billing ROI:** http://localhost:8110/api/v1/roi/system1
- **Daily Cost:** http://localhost:8110/api/v1/billing/system1/daily
- **Tariffs:** http://localhost:8110/api/v1/tariffs
- **Data Collection:** POST endpoints for manual triggers

### **6. Database Connectivity**
- PostgreSQL connection testing
- Database version verification
- Table structure verification
- Data availability checks (record counts)

### **7. Telegram Bot Testing**
- Telegram bot token validation
- Webhook information
- Recent updates check
- Internal bot process verification

### **8. Configuration Verification**
- Environment variables
- Python path configuration
- Installed packages
- Application configuration files

## 🚀 How to Use

### **Windows Systems**

1. **Deploy the system:**
   ```cmd
   deploy-windows-corrected.bat
   ```

2. **Run comprehensive diagnostics:**
   ```cmd
   comprehensive-diagnostics.bat
   ```

3. **Or run quick test:**
   ```cmd
   test-system-windows.bat
   ```

### **Unix/Linux/macOS Systems**

1. **Deploy the system:**
   ```bash
   ./deploy-unix.sh
   ```

2. **Run comprehensive diagnostics:**
   ```bash
   ./comprehensive-diagnostics.sh
   ```

## 📊 Output Files

The diagnostic scripts create a timestamped directory with all results:

### **Main Files**
- `comprehensive-diagnostics.log` - Complete summary and status
- `SUMMARY.txt` - Quick reference with key status information
- `all-services-combined.log` - All container logs combined

### **Endpoint Testing**
- `health-endpoint.log` - Health check detailed response
- `docs-endpoint.log` - API documentation response
- `solax-data.log` - SolaX data endpoint response
- `weather-data.log` - Weather data endpoint response
- `roi-endpoint.log` - ROI calculation response
- `daily-cost.log` - Daily cost calculation response
- `tariffs.log` - Tariff information response

### **System Information**
- `solar-prediction-app.log` - Application container logs
- `postgres-database.log` - Database container logs
- `container-stats.log` - Resource usage statistics
- `environment-variables.log` - Container environment
- `python-packages.log` - Installed Python packages

### **Database Testing**
- `database-version.log` - PostgreSQL version and connection
- `database-tables.log` - Available database tables
- `solax-data-count.log` - SolaX data record count
- `weather-data-count.log` - Weather data record count

### **Telegram Bot Testing**
- `telegram-bot-token.log` - Token validation response
- `telegram-webhook.log` - Webhook configuration
- `telegram-updates.log` - Recent bot updates
- `telegram-process.log` - Internal bot process status

## 🎯 Expected Results

### **✅ If Everything Works Correctly:**
```
SYSTEM STATUS:
  • Health Endpoint: WORKING
  • API Documentation: WORKING
  • Database: WORKING

DATA ENDPOINTS:
  • SolaX Data: WORKING
  • Weather Data: WORKING

ENHANCED BILLING:
  • ROI Endpoint: WORKING
  • Daily Cost: WORKING
  • Tariffs: WORKING

DATA COLLECTION:
  • SolaX Collection: WORKING
  • Weather Collection: WORKING

TELEGRAM BOT:
  • Token Validation: VALID
  • Process Status: RUNNING
```

### **❌ If Issues Found:**
The scripts will clearly mark failed components and provide:
- Detailed error logs for each failed endpoint
- Container status and logs
- Recommendations for troubleshooting
- Specific files to review for each issue

## 🔧 Troubleshooting

### **Common Issues and Solutions:**

1. **Health Endpoint FAILED**
   - Check `health-endpoint.log` for detailed error
   - Review `solar-prediction-app.log` for application errors
   - Verify container is running: `docker-compose ps`

2. **Database FAILED**
   - Check `database-version.log` for connection errors
   - Review `postgres-database.log` for database issues
   - Verify database container: `docker-compose logs postgres`

3. **Enhanced Billing FAILED**
   - Check `roi-endpoint.log` for specific errors
   - Verify port 8110 is accessible
   - Review application logs for billing service status

4. **Telegram Bot INVALID**
   - Check `telegram-bot-token.log` for token validation
   - Verify bot configuration in environment variables
   - Check `telegram-process.log` for internal bot status

## 📞 Getting Help

### **If You Need Support:**

1. **Run the comprehensive diagnostics**
2. **Review the SUMMARY.txt file**
3. **Share the ENTIRE diagnostics folder** (not just individual files)
4. **Include information about:**
   - Your operating system
   - Docker version
   - Any error messages during deployment

### **The diagnostics folder contains ALL necessary information for troubleshooting:**
- Complete system status
- All error logs
- Configuration details
- Performance metrics
- Network connectivity results

## 🎯 Key Advantages

### **Comprehensive Coverage**
- Tests ALL system components
- Verifies ALL API endpoints
- Checks ALL configuration aspects
- Collects ALL relevant logs

### **Detailed Reporting**
- Clear WORKING/FAILED status for each component
- Detailed logs for troubleshooting
- Quick summary for overview
- Specific recommendations for issues

### **Easy Sharing**
- Single folder contains everything
- Structured file organization
- Clear naming conventions
- Summary file for quick reference

---

**These diagnostic scripts ensure complete verification of the EXACT working Docker image deployment!**
