#!/bin/bash

# Solar Prediction System - AppImage Creator
# Creates a portable Linux AppImage

set -e

echo "🐧 Solar Prediction System - AppImage Creator"
echo "============================================="

# Check if we're on Linux
if [[ "$OSTYPE" != "linux-gnu"* ]]; then
    echo "❌ AppImage can only be created on Linux"
    exit 1
fi

# Download AppImage tools if needed
if [ ! -f "appimagetool-x86_64.AppImage" ]; then
    echo "📥 Downloading AppImage tools..."
    wget -q "https://github.com/AppImage/AppImageKit/releases/download/continuous/appimagetool-x86_64.AppImage"
    chmod +x appimagetool-x86_64.AppImage
fi

# Create AppDir structure
APP_DIR="SolarPrediction.AppDir"
rm -rf "$APP_DIR"
mkdir -p "$APP_DIR"

echo "📁 Creating AppDir structure..."

# Create directory structure
mkdir -p "$APP_DIR/usr/bin"
mkdir -p "$APP_DIR/usr/lib"
mkdir -p "$APP_DIR/usr/share/applications"
mkdir -p "$APP_DIR/usr/share/icons/hicolor/256x256/apps"

# Copy application files
echo "📋 Copying application files..."
cp -r scripts/ "$APP_DIR/usr/bin/"
cp -r static/ "$APP_DIR/usr/bin/"
cp -r models/ "$APP_DIR/usr/bin/" 2>/dev/null || echo "Models not found"
cp requirements.txt "$APP_DIR/usr/bin/"
cp docker-compose.yml "$APP_DIR/usr/bin/"

# Create launcher script
cat > "$APP_DIR/usr/bin/solar-prediction" << 'EOF'
#!/bin/bash

# Solar Prediction System Launcher
APP_DIR="$(dirname "$(readlink -f "$0")")"
cd "$APP_DIR"

echo "🌞 Solar Prediction System"
echo "========================="

# Check for Docker
if command -v docker &> /dev/null && docker info &> /dev/null; then
    echo "✅ Docker found - starting with full system"
    docker-compose up -d
    echo "🌐 Opening web interface..."
    sleep 3
    xdg-open "http://localhost:8100" 2>/dev/null &
    
    echo "💡 System is running at http://localhost:8100"
    echo "Press Ctrl+C to stop..."
    
    # Wait for interrupt
    trap 'echo "Stopping..."; docker-compose down; exit' INT
    while true; do sleep 1; done
    
else
    echo "⚠️ Docker not found - starting minimal version"
    # Start Python application directly
    if [ -f "scripts/production_app.py" ]; then
        python3 scripts/production_app.py &
        PID=$!
        echo "🌐 Opening web interface..."
        sleep 3
        xdg-open "http://localhost:8100" 2>/dev/null &
        
        echo "💡 System is running at http://localhost:8100"
        echo "Press Ctrl+C to stop..."
        
        trap "echo 'Stopping...'; kill $PID 2>/dev/null; exit" INT
        wait $PID
    else
        echo "❌ Application not found"
        exit 1
    fi
fi
EOF

chmod +x "$APP_DIR/usr/bin/solar-prediction"

# Create desktop file
cat > "$APP_DIR/usr/share/applications/solar-prediction.desktop" << 'EOF'
[Desktop Entry]
Type=Application
Name=Solar Prediction System
Comment=Solar Energy Prediction and Monitoring System
Exec=solar-prediction
Icon=solar-prediction
Categories=Science;Engineering;
Terminal=true
EOF

# Create AppRun
cat > "$APP_DIR/AppRun" << 'EOF'
#!/bin/bash

# AppImage entry point
HERE="$(dirname "$(readlink -f "${0}")")"
export PATH="${HERE}/usr/bin:${PATH}"
export LD_LIBRARY_PATH="${HERE}/usr/lib:${LD_LIBRARY_PATH}"

exec "${HERE}/usr/bin/solar-prediction" "$@"
EOF

chmod +x "$APP_DIR/AppRun"

# Create desktop file in root
cp "$APP_DIR/usr/share/applications/solar-prediction.desktop" "$APP_DIR/"

# Create simple icon (text-based)
cat > "$APP_DIR/solar-prediction.svg" << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<svg width="256" height="256" xmlns="http://www.w3.org/2000/svg">
  <rect width="256" height="256" fill="#FFA500"/>
  <circle cx="128" cy="128" r="80" fill="#FFD700"/>
  <text x="128" y="140" text-anchor="middle" font-family="Arial" font-size="24" fill="#FF4500">☀️</text>
  <text x="128" y="200" text-anchor="middle" font-family="Arial" font-size="16" fill="#000">Solar</text>
  <text x="128" y="220" text-anchor="middle" font-family="Arial" font-size="16" fill="#000">Prediction</text>
</svg>
EOF

cp "$APP_DIR/solar-prediction.svg" "$APP_DIR/usr/share/icons/hicolor/256x256/apps/"

# Build AppImage
echo "🔨 Building AppImage..."
./appimagetool-x86_64.AppImage "$APP_DIR" SolarPrediction-x86_64.AppImage

if [ -f "SolarPrediction-x86_64.AppImage" ]; then
    chmod +x SolarPrediction-x86_64.AppImage
    echo "✅ AppImage created successfully!"
    echo "📦 File: SolarPrediction-x86_64.AppImage"
    echo "📏 Size: $(du -h SolarPrediction-x86_64.AppImage | cut -f1)"
    echo
    echo "🚀 Usage:"
    echo "1. Transfer SolarPrediction-x86_64.AppImage to target Linux system"
    echo "2. Make executable: chmod +x SolarPrediction-x86_64.AppImage"
    echo "3. Run: ./SolarPrediction-x86_64.AppImage"
    echo
    echo "💡 The AppImage will work on most Linux distributions without installation"
else
    echo "❌ AppImage creation failed"
    exit 1
fi

# Cleanup
echo "🧹 Cleaning up..."
rm -rf "$APP_DIR"
rm -f appimagetool-x86_64.AppImage

echo "✨ AppImage package ready!"
