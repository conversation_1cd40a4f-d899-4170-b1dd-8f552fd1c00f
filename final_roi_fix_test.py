#!/usr/bin/env python3
"""
Final ROI Fix Test
"""

import requests

BOT_TOKEN = "8060881816:AAFBhGADTAAcUkbom_FEFSkOHoT5N6t5png"
CHAT_ID = "1510889515"

def send_final_test():
    """Send final test message"""
    url = f"https://api.telegram.org/bot{BOT_TOKEN}/sendMessage"
    
    payload = {
        "chat_id": CHAT_ID,
        "text": """🎯 ROI MARKDOWN PARSING ΔΙΟΡΘΩΘΗΚΕ!

✅ Διορθώσεις:
1. Απλοποιημένο conditional expression
2. Αφαιρέθηκε Markdown parsing για ROI
3. Χρησιμοποιεί /roi endpoint (όχι /summary)

📊 Αναμενόμενα αποτελέσματα:
• ROI επιλογή 1 (Σπίτι Πάνω): ✅ Λειτουργεί
• ROI επιλογή 2 (Σπίτι Κάτω): ✅ Λειτουργεί  
• ROI επιλογή 3 (Συνολικό): ✅ Ήδη λειτουργούσε

🚀 Δοκιμάστε τώρα τις επιλογές 1 & 2 του ROI μενού!""",
        "parse_mode": "Markdown"
    }
    
    try:
        response = requests.post(url, json=payload, timeout=10)
        if response.status_code == 200:
            print("✅ Final ROI fix test message sent successfully!")
            print("🎯 ROI Markdown parsing fixed!")
            return True
        else:
            print(f"❌ Failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    send_final_test()
