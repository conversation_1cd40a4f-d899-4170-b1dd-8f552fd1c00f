#!/usr/bin/env python3
"""
Web Server Service for Docker Container
Simple HTTP server for static files
"""

import os
import sys
import logging
import http.server
import socketserver
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """Custom HTTP request handler with CORS support"""
    
    def end_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()

def main():
    """Main entry point for Web Server service"""
    try:
        port = int(os.getenv('SERVICE_PORT', 8080))
        
        # Change to the scripts/frontend_system directory to serve static files
        static_dir = Path(__file__).parent / 'scripts' / 'frontend_system'
        if static_dir.exists():
            os.chdir(static_dir)
            logger.info(f"📁 Serving files from: {static_dir}")
        else:
            logger.warning(f"⚠️ Static directory not found: {static_dir}")
            # Fallback to current directory
            logger.info("📁 Serving files from current directory")
        
        logger.info(f"🌐 Starting Web Server on port {port}...")
        
        with socketserver.TCPServer(("", port), CustomHTTPRequestHandler) as httpd:
            logger.info(f"✅ Web Server running at http://localhost:{port}/")
            httpd.serve_forever()
            
    except Exception as e:
        logger.error(f"❌ Failed to start Web Server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
