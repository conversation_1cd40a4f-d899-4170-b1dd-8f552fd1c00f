#!/usr/bin/env python3
"""
Solar Prediction System - PyInstaller EXE Creator
Creates standalone Windows executables
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def create_exe_package():
    """Create standalone EXE package using PyInstaller"""
    
    print("🚀 Solar Prediction System - EXE Package Creator")
    print("=" * 60)
    
    # Check if PyInstaller is installed
    try:
        import PyInstaller
        print("✅ PyInstaller found")
    except ImportError:
        print("❌ PyInstaller not found. Installing...")
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✅ PyInstaller installed")
    
    # Create main launcher script
    launcher_script = """
import os
import sys
import subprocess
import webbrowser
import time
from pathlib import Path

def main():
    print("🌞 Solar Prediction System")
    print("=" * 40)
    
    # Get the directory where the exe is located
    if getattr(sys, 'frozen', False):
        app_dir = Path(sys.executable).parent
    else:
        app_dir = Path(__file__).parent
    
    # Change to app directory
    os.chdir(app_dir)
    
    print(f"📁 Application directory: {app_dir}")
    
    # Check if PostgreSQL is available
    postgres_available = False
    try:
        result = subprocess.run(['docker', '--version'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("✅ Docker found - will use containerized PostgreSQL")
            postgres_available = True
        else:
            print("⚠️ Docker not found - will use embedded SQLite")
    except:
        print("⚠️ Docker not found - will use embedded SQLite")
    
    # Start the application
    try:
        if postgres_available:
            print("🚀 Starting with Docker...")
            subprocess.run(['docker-compose', 'up', '-d'])
            print("✅ System started with Docker")
            print("🌐 Opening web interface...")
            time.sleep(5)
            webbrowser.open('http://localhost:8100')
        else:
            print("🚀 Starting with embedded database...")
            # Start the Python application directly
            python_script = app_dir / "scripts" / "production_app.py"
            if python_script.exists():
                subprocess.Popen([sys.executable, str(python_script)])
                print("✅ System started")
                print("🌐 Opening web interface...")
                time.sleep(3)
                webbrowser.open('http://localhost:8100')
            else:
                print("❌ Application script not found")
                return
        
        print("\\n💡 System is running!")
        print("   • Web Interface: http://localhost:8100")
        print("   • Close this window to stop the system")
        
        # Keep the window open
        input("\\nPress Enter to stop the system...")
        
        # Cleanup
        if postgres_available:
            subprocess.run(['docker-compose', 'down'])
        
    except KeyboardInterrupt:
        print("\\n🛑 Stopping system...")
        if postgres_available:
            subprocess.run(['docker-compose', 'down'])
    except Exception as e:
        print(f"❌ Error: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
"""
    
    # Write launcher script
    with open("solar_launcher.py", "w") as f:
        f.write(launcher_script)
    
    print("✅ Launcher script created")
    
    # Create PyInstaller spec file
    spec_content = """
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['solar_launcher.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('scripts', 'scripts'),
        ('static', 'static'),
        ('models', 'models'),
        ('docker-compose.yml', '.'),
        ('requirements.txt', '.'),
        ('.env', '.'),
    ],
    hiddenimports=[
        'fastapi',
        'uvicorn',
        'sqlalchemy',
        'psycopg2',
        'lightgbm',
        'pandas',
        'numpy',
        'sklearn',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='SolarPredictionSystem',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='solar_icon.ico'  # Add icon if available
)
"""
    
    with open("solar_prediction.spec", "w") as f:
        f.write(spec_content)
    
    print("✅ PyInstaller spec file created")
    
    # Build the executable
    print("🔨 Building executable...")
    try:
        subprocess.run([
            "pyinstaller", 
            "--onefile", 
            "--name=SolarPredictionSystem",
            "--add-data=scripts;scripts",
            "--add-data=static;static", 
            "--add-data=docker-compose.yml;.",
            "--hidden-import=fastapi",
            "--hidden-import=uvicorn",
            "--hidden-import=sqlalchemy",
            "--hidden-import=psycopg2",
            "--hidden-import=lightgbm",
            "solar_launcher.py"
        ], check=True)
        
        print("✅ Executable built successfully!")
        print("📁 Location: dist/SolarPredictionSystem.exe")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Build failed: {e}")
        return False
    
    # Create installer package
    print("📦 Creating installer package...")
    
    package_dir = "SolarPredictionPortable"
    if os.path.exists(package_dir):
        shutil.rmtree(package_dir)
    
    os.makedirs(package_dir)
    
    # Copy executable
    shutil.copy("dist/SolarPredictionSystem.exe", package_dir)
    
    # Copy essential files
    files_to_copy = [
        "docker-compose.yml",
        "requirements.txt", 
        ".env",
        "README.md"
    ]
    
    for file in files_to_copy:
        if os.path.exists(file):
            shutil.copy(file, package_dir)
    
    # Copy directories
    dirs_to_copy = ["scripts", "static", "models", "docs"]
    for dir_name in dirs_to_copy:
        if os.path.exists(dir_name):
            shutil.copytree(dir_name, os.path.join(package_dir, dir_name))
    
    # Create installer README
    installer_readme = """
# Solar Prediction System - Portable Windows Package

## 🚀 Quick Start

1. **Option A: With Docker (Recommended)**
   - Install Docker Desktop from: https://www.docker.com/products/docker-desktop
   - Double-click `SolarPredictionSystem.exe`
   - System will start automatically with full database

2. **Option B: Standalone (Limited)**
   - Just double-click `SolarPredictionSystem.exe`
   - System will start with embedded database
   - Some features may be limited

## 🌐 Access

- Web Interface: http://localhost:8100
- The application will open automatically in your browser

## 🛑 Stopping

- Close the console window or press Ctrl+C

## 📋 Requirements

- Windows 10/11
- 4GB RAM minimum
- 2GB free disk space
- Internet connection for weather data

## 🔧 Troubleshooting

### Port Already in Use
If you get a port error, another application is using port 8100.
Either close that application or edit `docker-compose.yml` to change the port.

### Docker Issues
Make sure Docker Desktop is running before starting the application.

## 📞 Support

Check the documentation in the `docs/` folder for detailed information.
"""
    
    with open(os.path.join(package_dir, "README.txt"), "w") as f:
        f.write(installer_readme)
    
    # Create ZIP package
    print("🗜️ Creating ZIP package...")
    shutil.make_archive("SolarPredictionPortable", "zip", package_dir)
    
    print("✅ Package created successfully!")
    print(f"📦 Package: SolarPredictionPortable.zip")
    print(f"📁 Directory: {package_dir}/")
    
    # Cleanup
    cleanup_files = ["solar_launcher.py", "solar_prediction.spec"]
    for file in cleanup_files:
        if os.path.exists(file):
            os.remove(file)
    
    if os.path.exists("build"):
        shutil.rmtree("build")
    
    if os.path.exists("dist"):
        shutil.rmtree("dist")
    
    print("🧹 Cleanup completed")
    
    return True

if __name__ == "__main__":
    success = create_exe_package()
    if success:
        print("\n🎉 EXE package created successfully!")
        print("📋 Next steps:")
        print("1. Transfer SolarPredictionPortable.zip to target Windows PC")
        print("2. Extract the ZIP file")
        print("3. Double-click SolarPredictionSystem.exe")
    else:
        print("\n❌ Package creation failed")
