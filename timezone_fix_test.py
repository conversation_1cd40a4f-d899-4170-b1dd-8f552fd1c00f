#!/usr/bin/env python3
"""
Timezone Fix Test
"""

import requests

BOT_TOKEN = "8060881816:AAFBhGADTAAcUkbom_FEFSkOHoT5N6t5png"
CHAT_ID = "1510889515"

def send_timezone_test():
    """Send timezone fix test message"""
    url = f"https://api.telegram.org/bot{BOT_TOKEN}/sendMessage"
    
    payload = {
        "chat_id": CHAT_ID,
        "text": """🕐 TIMEZONE ΔΙΟΡΘΩΘΗΚΕ!

✅ Διορθώσεις:
• Alert System: Χρησιμοποιεί Greek timezone (EEST)
• Telegram Bot: Διορθωμένα timestamps
• Όλα τα containers: Timezone-aware

⏰ Αναμενόμενα αποτελέσματα:
• Alerts: Σωστό timestamp (EEST)
• Health checks: Σωστή ώρα
• Όλα τα logs: Greek time

🇬🇷 Τώρα όλα τα timestamps είναι στην ελληνική ώρα!

Το επόμενο alert θα έχει σωστό timestamp."""
    }
    
    try:
        response = requests.post(url, json=payload, timeout=10)
        if response.status_code == 200:
            print("✅ Timezone fix test message sent successfully!")
            print("🕐 Timezone fix applied to Docker system!")
            return True
        else:
            print(f"❌ Failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    send_timezone_test()
