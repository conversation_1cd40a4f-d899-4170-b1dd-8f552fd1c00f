"""
JSON Utilities - Enhanced JSON encoder for all project scripts
"""

import json
import numpy as np
from decimal import Decimal
from datetime import datetime, date

class EnhancedJSONEncoder(json.JSONEncoder):
    """
    Enhanced JSON encoder that handles:
    - Decimal objects
    - NumPy types (bool_, int64, float64, ndarray)
    - Sets
    - Datetime objects
    - Complex objects with __dict__
    """
    
    def default(self, obj):
        # Handle Decimal
        if isinstance(obj, Decimal):
            return float(obj)
        
        # Handle numpy boolean
        elif isinstance(obj, np.bool_):
            return bool(obj)
        
        # Handle numpy integers
        elif isinstance(obj, np.integer):
            return int(obj)
        
        # Handle numpy floats
        elif isinstance(obj, np.floating):
            return float(obj)
        
        # Handle numpy arrays
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        
        # <PERSON>le sets
        elif isinstance(obj, set):
            return list(obj)
        
        # Handle datetime objects
        elif isinstance(obj, (datetime, date)):
            return obj.isoformat()
        
        # Handle any object with __dict__ (fallback)
        elif hasattr(obj, '__dict__'):
            return str(obj)
        
        return super().default(obj)

def safe_json_dump(data, file_path, **kwargs):
    """Safe JSON dump using enhanced encoder"""
    with open(file_path, 'w') as f:
        json.dump(data, f, cls=EnhancedJSONEncoder, **kwargs)

def safe_json_dumps(data, **kwargs):
    """Safe JSON dumps using enhanced encoder"""
    return json.dumps(data, cls=EnhancedJSONEncoder, **kwargs)
