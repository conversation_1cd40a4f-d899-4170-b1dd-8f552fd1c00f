@echo off
echo.
echo ========================================
echo   Solar Prediction System - Test Script
echo   EXACT WORKING IMAGE VERIFICATION
echo ========================================
echo.

echo Testing system endpoints...
echo.

echo 1. Testing Main Application Health...
curl -s http://localhost:8100/health
if %errorlevel% equ 0 (
    echo ✅ Main Application: WORKING
) else (
    echo ❌ Main Application: NOT RESPONDING
)
echo.

echo 2. Testing Enhanced Billing Service...
curl -s http://localhost:8110/api/v1/roi/system1
if %errorlevel% equ 0 (
    echo ✅ Enhanced Billing: WORKING
) else (
    echo ❌ Enhanced Billing: NOT RESPONDING
)
echo.

echo 3. Testing Latest Data Endpoints...
curl -s http://localhost:8100/api/v1/data/solax/latest
if %errorlevel% equ 0 (
    echo ✅ SolaX Data: WORKING
) else (
    echo ❌ SolaX Data: NOT RESPONDING
)
echo.

echo 4. Testing Weather Data...
curl -s http://localhost:8100/api/v1/data/weather/latest
if %errorlevel% equ 0 (
    echo ✅ Weather Data: WORKING
) else (
    echo ❌ Weather Data: NOT RESPONDING
)
echo.

echo 5. Testing Tariffs Endpoint...
curl -s http://localhost:8110/api/v1/tariffs
if %errorlevel% equ 0 (
    echo ✅ Tariffs: WORKING
) else (
    echo ❌ Tariffs: NOT RESPONDING
)
echo.

echo ========================================
echo   Container Status
echo ========================================
docker-compose ps

echo.
echo ========================================
echo   Recent Logs (Last 20 lines)
echo ========================================
docker-compose logs --tail=20

echo.
echo ========================================
echo   Access Points
echo ========================================
echo.
echo 🌐 Web Interface: http://localhost:8100
echo 💰 Enhanced Billing: http://localhost:8110
echo 📖 API Documentation: http://localhost:8100/docs
echo 🔍 Health Check: http://localhost:8100/health
echo 🗄️ Database: localhost:5433
echo 🤖 Telegram Bot: @grlvSolarAI_bot
echo.

echo If services are not responding, wait a few more minutes
echo for complete initialization, then run this test again.
echo.
pause
