{"training_date": "2025-06-17T06:20:31.607093", "pipeline_version": "v2.0.0", "total_models": 8, "successful_models": 8, "target_achieved": 0, "systems": [1, 2], "horizons": ["hourly", "daily", "monthly", "yearly"], "models": {"system_1_hourly": {"algorithm": "GradientBoosting", "metrics": {"r2": 0.13557351863588107, "mae": 0.14522661252118962, "rmse": 0.7363331066194363, "cv_r2": 0.06132845100456361, "cv_std": 0.05345090781117273}, "features": ["feature_0", "feature_1", "feature_2", "feature_3", "feature_4", "feature_5", "feature_6", "feature_7"], "target_achieved": false, "training_samples": 135422, "benchmark_results": {"RandomForest": {"r2": 0.1340715761958886, "mae": 0.13961683976245495, "rmse": 0.7369725189898026, "cv_r2": 0.09343144964322095, "cv_std": 0.019757842862289564}, "GradientBoosting": {"r2": 0.13557351863588107, "mae": 0.14522661252118962, "rmse": 0.7363331066194363, "cv_r2": 0.06132845100456361, "cv_std": 0.05345090781117273}, "Ridge": {"r2": 0.0029284941452493163, "mae": 0.24755788100274173, "rmse": 0.790812360229418, "cv_r2": 0.002536313951822922, "cv_std": 0.00027609077420843596}, "LinearRegression": {"r2": 0.002920731293609702, "mae": 0.24763822058867485, "rmse": 0.7908154387182939, "cv_r2": 0.002537248772929446, "cv_std": 0.00026844397315306165}}}, "system_1_daily": {"algorithm": "RandomForest", "metrics": {"r2": 0.878472272684014, "mae": 4.96697894132754, "rmse": 8.33258120019372, "cv_r2": 0.8743609633269556, "cv_std": 0.004031340754304483}, "features": ["feature_0", "feature_1", "feature_2", "feature_3", "feature_4", "feature_5", "feature_6", "feature_7"], "target_achieved": false, "training_samples": 135422, "benchmark_results": {"RandomForest": {"r2": 0.878472272684014, "mae": 4.96697894132754, "rmse": 8.33258120019372, "cv_r2": 0.8743609633269556, "cv_std": 0.004031340754304483}, "GradientBoosting": {"r2": 0.8776210157078066, "mae": 5.061863696000572, "rmse": 8.36171360515715, "cv_r2": 0.8734084434284595, "cv_std": 0.0037550759425215648}, "Ridge": {"r2": 0.7115456872100274, "mae": 9.859732728226998, "rmse": 12.837497874611197, "cv_r2": 0.7104599092189268, "cv_std": 0.0051523494824366484}, "LinearRegression": {"r2": 0.7115528763773756, "mae": 9.860257692754422, "rmse": 12.837337898678248, "cv_r2": 0.7104553320689321, "cv_std": 0.005152993774489038}}}, "system_1_monthly": {"algorithm": "RandomForest", "metrics": {"r2": 0.5482947898697407, "mae": 38769.743595191685, "rmse": 47813.19476309184, "cv_r2": 0.5426637077810839, "cv_std": 0.00717138925269605}, "features": ["feature_0", "feature_1", "feature_2", "feature_3", "feature_4", "feature_5", "feature_6", "feature_7"], "target_achieved": false, "training_samples": 135422, "benchmark_results": {"RandomForest": {"r2": 0.5482947898697407, "mae": 38769.743595191685, "rmse": 47813.19476309184, "cv_r2": 0.5426637077810839, "cv_std": 0.00717138925269605}, "GradientBoosting": {"r2": 0.5455528103695452, "mae": 39057.740562931904, "rmse": 47958.09506607448, "cv_r2": 0.540117847192416, "cv_std": 0.007386036825209213}, "Ridge": {"r2": 0.4521949131925155, "mae": 45107.29283396026, "rmse": 52654.22726370386, "cv_r2": 0.44996137310218476, "cv_std": 0.006662059609392156}, "LinearRegression": {"r2": 0.45215684134223866, "mae": 45142.33029163007, "rmse": 52656.05693731969, "cv_r2": 0.449952904694974, "cv_std": 0.006623630086047892}}}, "system_1_yearly": {"algorithm": "RandomForest", "metrics": {"r2": 0.4726054398070012, "mae": 199873.85149478726, "rmse": 271862.5786274655, "cv_r2": 0.4730265862929154, "cv_std": 0.005424014914539368}, "features": ["feature_0", "feature_1", "feature_2", "feature_3", "feature_4", "feature_5", "feature_6", "feature_7"], "target_achieved": false, "training_samples": 135422, "benchmark_results": {"RandomForest": {"r2": 0.4726054398070012, "mae": 199873.85149478726, "rmse": 271862.5786274655, "cv_r2": 0.4730265862929154, "cv_std": 0.005424014914539368}, "GradientBoosting": {"r2": 0.46274824734655085, "mae": 204171.3666755214, "rmse": 274391.42137288075, "cv_r2": 0.4641219697955437, "cv_std": 0.007085897026359015}, "Ridge": {"r2": 0.3220643301525644, "mae": 263250.3348455841, "rmse": 308230.6708069582, "cv_r2": 0.3298946973288562, "cv_std": 0.005755159412343545}, "LinearRegression": {"r2": 0.3220672687837711, "mae": 263275.26117952046, "rmse": 308230.0027662502, "cv_r2": 0.32989559627165344, "cv_std": 0.005753137460914874}}}, "system_2_hourly": {"algorithm": "GradientBoosting", "metrics": {"r2": 0.637988418263844, "mae": 0.07830873698584356, "rmse": 0.1301988007530838, "cv_r2": 0.30606455907743413, "cv_std": 0.2391101744777217}, "features": ["feature_0", "feature_1", "feature_2", "feature_3", "feature_4", "feature_5", "feature_6", "feature_7"], "target_achieved": false, "training_samples": 129591, "benchmark_results": {"RandomForest": {"r2": 0.6204683842255347, "mae": 0.07820353493853849, "rmse": 0.13331214956978252, "cv_r2": 0.4514464420590068, "cv_std": 0.16458485931661618}, "GradientBoosting": {"r2": 0.637988418263844, "mae": 0.07830873698584356, "rmse": 0.1301988007530838, "cv_r2": 0.30606455907743413, "cv_std": 0.2391101744777217}, "Ridge": {"r2": 0.045927209101683464, "mae": 0.16132239803625642, "rmse": 0.21136667068115192, "cv_r2": 0.04012869771243936, "cv_std": 0.01300520149924744}, "LinearRegression": {"r2": 0.0459547879051162, "mae": 0.16134837063319862, "rmse": 0.21136361573500695, "cv_r2": 0.0401686658155449, "cv_std": 0.013016092168723702}}}, "system_2_daily": {"algorithm": "GradientBoosting", "metrics": {"r2": 0.812659660017023, "mae": 6.228357414013428, "rmse": 9.417282489453132, "cv_r2": 0.812522068920752, "cv_std": 0.003205689203276007}, "features": ["feature_0", "feature_1", "feature_2", "feature_3", "feature_4", "feature_5", "feature_6", "feature_7"], "target_achieved": false, "training_samples": 129591, "benchmark_results": {"RandomForest": {"r2": 0.8096023881755404, "mae": 6.251668784988598, "rmse": 9.493813465160285, "cv_r2": 0.8098605477347224, "cv_std": 0.0027755588597695455}, "GradientBoosting": {"r2": 0.812659660017023, "mae": 6.228357414013428, "rmse": 9.417282489453132, "cv_r2": 0.812522068920752, "cv_std": 0.003205689203276007}, "Ridge": {"r2": 0.6829977148543223, "mae": 9.636778764224967, "rmse": 12.25014255256647, "cv_r2": 0.6829622577027327, "cv_std": 0.0032922287568674796}, "LinearRegression": {"r2": 0.6829977124705, "mae": 9.63677890417215, "rmse": 12.250142598626331, "cv_r2": 0.6829617999578149, "cv_std": 0.003292705195226867}}}, "system_2_monthly": {"algorithm": "GradientBoosting", "metrics": {"r2": 0.18432448272248703, "mae": 49791.22183943848, "rmse": 58013.92542133068, "cv_r2": 0.18087483458922798, "cv_std": 0.003918746582044892}, "features": ["feature_0", "feature_1", "feature_2", "feature_3", "feature_4", "feature_5", "feature_6", "feature_7"], "target_achieved": false, "training_samples": 129591, "benchmark_results": {"RandomForest": {"r2": 0.1782201747425196, "mae": 49883.811469451604, "rmse": 58230.60151635822, "cv_r2": 0.17698310683530896, "cv_std": 0.004941743666274117}, "GradientBoosting": {"r2": 0.18432448272248703, "mae": 49791.22183943848, "rmse": 58013.92542133068, "cv_r2": 0.18087483458922798, "cv_std": 0.003918746582044892}, "Ridge": {"r2": 0.08720417525140334, "mae": 53532.5180234089, "rmse": 61370.5988678243, "cv_r2": 0.09101319144426841, "cv_std": 0.005384252336559522}, "LinearRegression": {"r2": 0.08720805064889314, "mae": 53549.26003755139, "rmse": 61370.4685891186, "cv_r2": 0.0909922262547405, "cv_std": 0.005413144259127383}}}, "system_2_yearly": {"algorithm": "GradientBoosting", "metrics": {"r2": 0.18480021270316005, "mae": 319163.6411063093, "rmse": 355843.55845035834, "cv_r2": 0.1791593256962417, "cv_std": 0.004320403287603175}, "features": ["feature_0", "feature_1", "feature_2", "feature_3", "feature_4", "feature_5", "feature_6", "feature_7"], "target_achieved": false, "training_samples": 129591, "benchmark_results": {"RandomForest": {"r2": 0.1831396396424957, "mae": 318764.1507654094, "rmse": 356205.80315545294, "cv_r2": 0.1791228944267346, "cv_std": 0.003256642242577791}, "GradientBoosting": {"r2": 0.18480021270316005, "mae": 319163.6411063093, "rmse": 355843.55845035834, "cv_r2": 0.1791593256962417, "cv_std": 0.004320403287603175}, "Ridge": {"r2": 0.11885445918637016, "mae": 338285.15052911, "rmse": 369956.70569139684, "cv_r2": 0.11399201742947267, "cv_std": 0.002823494740333181}, "LinearRegression": {"r2": 0.11884371493035062, "mae": 338422.4847726433, "rmse": 369958.9612197182, "cv_r2": 0.11398388124422554, "cv_std": 0.0028255764750424093}}}}}