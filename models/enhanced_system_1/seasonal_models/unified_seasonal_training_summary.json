{"training_date": "2025-06-17T06:25:15.742138", "pipeline_version": "v2.0.0", "total_models": 8, "successful_models": 8, "target_achieved": 3, "systems": [1, 2], "seasons": ["spring", "summer", "autumn", "winter"], "models": {"spring_system1": {"algorithm": "GradientBoosting", "metrics": {"r2": 0.9109453957339255, "mae": 4.145991325655052, "rmse": 7.409846251409722, "cv_r2": 0.9160983944465123, "cv_std": 0.00187026576041489}, "features": ["hour_sin", "hour_cos", "temperature", "cloud_cover", "ghi", "soc"], "target_achieved": false, "training_samples": 48341, "benchmark_results": {"RandomForest": {"r2": 0.9096366997899661, "mae": 4.05640724428562, "rmse": 7.46409313138478, "cv_r2": 0.9147763554350942, "cv_std": 0.001862422164097893}, "GradientBoosting": {"r2": 0.9109453957339255, "mae": 4.145991325655052, "rmse": 7.409846251409722, "cv_r2": 0.9160983944465123, "cv_std": 0.00187026576041489}, "Ridge": {"r2": 0.7591952910686975, "mae": 8.893735489322035, "rmse": 12.184662250880946, "cv_r2": 0.7584892517081665, "cv_std": 0.004086180955176324}, "LinearRegression": {"r2": 0.7591953376508336, "mae": 8.893745737377158, "rmse": 12.184661072358265, "cv_r2": 0.758489247631552, "cv_std": 0.004086120239168693}}}, "summer_system1": {"algorithm": "GradientBoosting", "metrics": {"r2": 0.9752907378323596, "mae": 1.8249076861017803, "rmse": 4.298059010484055, "cv_r2": 0.9787091913878221, "cv_std": 0.0016126006064545843}, "features": ["hour_sin", "hour_cos", "temperature", "cloud_cover", "ghi", "soc"], "target_achieved": true, "training_samples": 37382, "benchmark_results": {"RandomForest": {"r2": 0.9743012856061158, "mae": 1.7962564326293937, "rmse": 4.383269606763925, "cv_r2": 0.9776394023340063, "cv_std": 0.0017896358010276127}, "GradientBoosting": {"r2": 0.9752907378323596, "mae": 1.8249076861017803, "rmse": 4.298059010484055, "cv_r2": 0.9787091913878221, "cv_std": 0.0016126006064545843}, "Ridge": {"r2": 0.8193369842273945, "mae": 7.949315329587291, "rmse": 11.621899430814382, "cv_r2": 0.8265358633734341, "cv_std": 0.002800111579732394}, "LinearRegression": {"r2": 0.8193369738025249, "mae": 7.949468114860591, "rmse": 11.621899766125912, "cv_r2": 0.8265358563527855, "cv_std": 0.002800000809773476}}}, "autumn_system1": {"algorithm": "GradientBoosting", "metrics": {"r2": 0.9359246952619726, "mae": 2.7978315570056207, "rmse": 5.113401965557791, "cv_r2": 0.9382247539793713, "cv_std": 0.005452916006884586}, "features": ["hour_sin", "hour_cos", "temperature", "cloud_cover", "ghi", "soc"], "target_achieved": true, "training_samples": 24767, "benchmark_results": {"RandomForest": {"r2": 0.9327952933762645, "mae": 2.817896041326461, "rmse": 5.236781318198867, "cv_r2": 0.9351111577887732, "cv_std": 0.004855100347009235}, "GradientBoosting": {"r2": 0.9359246952619726, "mae": 2.7978315570056207, "rmse": 5.113401965557791, "cv_r2": 0.9382247539793713, "cv_std": 0.005452916006884586}, "Ridge": {"r2": 0.7668025073347369, "mae": 7.274206966315619, "rmse": 9.754983343279735, "cv_r2": 0.7713730940055513, "cv_std": 0.005925995424323741}, "LinearRegression": {"r2": 0.7668027558104382, "mae": 7.274185546574024, "rmse": 9.754978146232062, "cv_r2": 0.7713730750978225, "cv_std": 0.00592665095422628}}}, "winter_system1": {"algorithm": "GradientBoosting", "metrics": {"r2": 0.8438646228488749, "mae": 2.9946449091950815, "rmse": 5.242187629394828, "cv_r2": 0.8394394330647377, "cv_std": 0.002724080279463824}, "features": ["hour_sin", "hour_cos", "temperature", "cloud_cover", "ghi", "soc"], "target_achieved": false, "training_samples": 24932, "benchmark_results": {"RandomForest": {"r2": 0.8299636820178202, "mae": 3.110478350751371, "rmse": 5.470572134068946, "cv_r2": 0.8247340873616558, "cv_std": 0.003982545151737071}, "GradientBoosting": {"r2": 0.8438646228488749, "mae": 2.9946449091950815, "rmse": 5.242187629394828, "cv_r2": 0.8394394330647377, "cv_std": 0.002724080279463824}, "Ridge": {"r2": 0.6789355611799055, "mae": 5.639471284472056, "rmse": 7.5172328985867924, "cv_r2": 0.6727580279462508, "cv_std": 0.007318427793347283}, "LinearRegression": {"r2": 0.6789355610507819, "mae": 5.639486698007966, "rmse": 7.517232900098408, "cv_r2": 0.6727580128455685, "cv_std": 0.007318214829853431}}}, "spring_system2": {"algorithm": "GradientBoosting", "metrics": {"r2": 0.8717538367247474, "mae": 5.179169882455267, "rmse": 8.118848395753002, "cv_r2": 0.8685993058075393, "cv_std": 0.002930077283839217}, "features": ["hour_sin", "hour_cos", "temperature", "cloud_cover", "ghi", "soc"], "target_achieved": false, "training_samples": 48217, "benchmark_results": {"RandomForest": {"r2": 0.8708653503177811, "mae": 5.138020245363863, "rmse": 8.146923449350632, "cv_r2": 0.8668009168850153, "cv_std": 0.004516726715659866}, "GradientBoosting": {"r2": 0.8717538367247474, "mae": 5.179169882455267, "rmse": 8.118848395753002, "cv_r2": 0.8685993058075393, "cv_std": 0.002930077283839217}, "Ridge": {"r2": 0.7015036526547634, "mae": 9.270382813948835, "rmse": 12.386304484265326, "cv_r2": 0.6980922135652028, "cv_std": 0.003140967269785261}, "LinearRegression": {"r2": 0.7015035122652942, "mae": 9.270409471341736, "rmse": 12.386307397042188, "cv_r2": 0.6980922125543687, "cv_std": 0.003140949348067335}}}, "summer_system2": {"algorithm": "GradientBoosting", "metrics": {"r2": 0.9700175611556412, "mae": 2.3877945526913495, "rmse": 4.4355158484167045, "cv_r2": 0.9702819296271376, "cv_std": 0.004696111362617254}, "features": ["hour_sin", "hour_cos", "temperature", "cloud_cover", "ghi", "soc"], "target_achieved": true, "training_samples": 31420, "benchmark_results": {"RandomForest": {"r2": 0.9690648114569286, "mae": 2.398383781057605, "rmse": 4.505438239244337, "cv_r2": 0.9694103690928306, "cv_std": 0.005204729477970698}, "GradientBoosting": {"r2": 0.9700175611556412, "mae": 2.3877945526913495, "rmse": 4.4355158484167045, "cv_r2": 0.9702819296271376, "cv_std": 0.004696111362617254}, "Ridge": {"r2": 0.8014042017386251, "mae": 8.026705263887722, "rmse": 11.41551962454466, "cv_r2": 0.797967131476233, "cv_std": 0.0019268251102871911}, "LinearRegression": {"r2": 0.8014040973939339, "mae": 8.026878366071562, "rmse": 11.415522623471938, "cv_r2": 0.7979671181644594, "cv_std": 0.0019266183750079053}}}, "autumn_system2": {"algorithm": "GradientBoosting", "metrics": {"r2": 0.8804471902807209, "mae": 3.65282917220834, "rmse": 6.107857980873652, "cv_r2": 0.8816220951747817, "cv_std": 0.007372055851117926}, "features": ["hour_sin", "hour_cos", "temperature", "cloud_cover", "ghi", "soc"], "target_achieved": false, "training_samples": 24809, "benchmark_results": {"RandomForest": {"r2": 0.8757557615638197, "mae": 3.681164335586978, "rmse": 6.22654549791785, "cv_r2": 0.8739557598041557, "cv_std": 0.005792136810125522}, "GradientBoosting": {"r2": 0.8804471902807209, "mae": 3.65282917220834, "rmse": 6.107857980873652, "cv_r2": 0.8816220951747817, "cv_std": 0.007372055851117926}, "Ridge": {"r2": 0.6883264526625782, "mae": 7.339701316411448, "rmse": 9.861863990215292, "cv_r2": 0.6794911361270535, "cv_std": 0.004321377366281608}, "LinearRegression": {"r2": 0.6883273967951241, "mae": 7.339775501868652, "rmse": 9.861849053250179, "cv_r2": 0.6794911235976386, "cv_std": 0.004321877976359288}}}, "winter_system2": {"algorithm": "GradientBoosting", "metrics": {"r2": 0.8380001988536906, "mae": 2.5503364760919265, "rmse": 4.526166589651275, "cv_r2": 0.8369280238480139, "cv_std": 0.0039052134175788745}, "features": ["hour_sin", "hour_cos", "temperature", "cloud_cover", "ghi", "soc"], "target_achieved": false, "training_samples": 25145, "benchmark_results": {"RandomForest": {"r2": 0.8324584553883583, "mae": 2.5462328252505118, "rmse": 4.602931918032886, "cv_r2": 0.8332707271953181, "cv_std": 0.0038695071507813283}, "GradientBoosting": {"r2": 0.8380001988536906, "mae": 2.5503364760919265, "rmse": 4.526166589651275, "cv_r2": 0.8369280238480139, "cv_std": 0.0039052134175788745}, "Ridge": {"r2": 0.6336886613917887, "mae": 5.013916108273311, "rmse": 6.806102333827115, "cv_r2": 0.6364802056117085, "cv_std": 0.004493296690033576}, "LinearRegression": {"r2": 0.633688558857814, "mae": 5.013968759414422, "rmse": 6.806103286372945, "cv_r2": 0.63648019727032, "cv_std": 0.0044932226800381185}}}}}