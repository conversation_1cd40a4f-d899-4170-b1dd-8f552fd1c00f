{"training_start": "2025-06-16T14:11:39.389671", "pipeline_version": "v2.0.0", "total_target_models": 16, "multi_horizon_results": {"training_date": "2025-06-16T14:11:41.065042", "pipeline_version": "v2.0.0", "total_models": 8, "successful_models": 8, "target_achieved": 0, "systems": [1, 2], "horizons": ["hourly", "daily", "monthly", "yearly"], "models": {"system_1_hourly": {"model": "GradientBoostingRegressor(max_depth=6, random_state=42)", "algorithm": "GradientBoosting", "metrics": {"r2": 0.10912538461331822, "mae": 0.14688982739837952, "rmse": 0.8347110435161373, "cv_r2": 0.07741628126320813, "cv_std": 0.04874301242705216}, "features": ["feature_0", "feature_1", "feature_2", "feature_3", "feature_4", "feature_5", "feature_6", "feature_7"], "target_achieved": "False", "training_samples": 135530, "benchmark_results": {"RandomForest": {"r2": 0.09996809869096834, "mae": 0.14195530346827948, "rmse": 0.8389900663596315, "cv_r2": 0.11977348024786494, "cv_std": 0.02766928171903758}, "GradientBoosting": {"r2": 0.10912538461331822, "mae": 0.14688982739837952, "rmse": 0.8347110435161373, "cv_r2": 0.07741628126320813, "cv_std": 0.04874301242705216}, "Ridge": {"r2": 0.0029948618396089532, "mae": 0.24936469534950198, "rmse": 0.8830322507715722, "cv_r2": 0.0024773747935448176, "cv_std": 0.0006307209001568749}, "LinearRegression": {"r2": 0.002986537631269881, "mae": 0.24956264130428168, "rmse": 0.8830359370760863, "cv_r2": 0.002469345032733772, "cv_std": 0.0006157920269670961}}}, "system_1_daily": {"model": "RandomForestRegressor(max_depth=10, min_samples_leaf=2, min_samples_split=5,\n                      random_state=42)", "algorithm": "RandomForest", "metrics": {"r2": 0.876730363914491, "mae": 4.950326093463333, "rmse": 8.403419883095124, "cv_r2": 0.8746761095867435, "cv_std": 0.0031371575969491522}, "features": ["feature_0", "feature_1", "feature_2", "feature_3", "feature_4", "feature_5", "feature_6", "feature_7"], "target_achieved": "False", "training_samples": 135530, "benchmark_results": {"RandomForest": {"r2": 0.876730363914491, "mae": 4.950326093463333, "rmse": 8.403419883095124, "cv_r2": 0.8746761095867435, "cv_std": 0.0031371575969491522}, "GradientBoosting": {"r2": 0.8755214794207725, "mae": 5.059874334066285, "rmse": 8.444524811181113, "cv_r2": 0.8745685489372376, "cv_std": 0.0031015739749092283}, "Ridge": {"r2": 0.7100452450277157, "mae": 9.86670539335924, "rmse": 12.88822545308679, "cv_r2": 0.7105124287082838, "cv_std": 0.004054153966847396}, "LinearRegression": {"r2": 0.7100440747132475, "mae": 9.866316207646735, "rmse": 12.88825146276802, "cv_r2": 0.7105150404478336, "cv_std": 0.00405370254396794}}}, "system_1_monthly": {"model": "RandomForestRegressor(max_depth=10, min_samples_leaf=2, min_samples_split=5,\n                      random_state=42)", "algorithm": "RandomForest", "metrics": {"r2": 0.5584163430466755, "mae": 38671.27030523005, "rmse": 47653.86159714862, "cv_r2": 0.5518266613314808, "cv_std": 0.006521204353459727}, "features": ["feature_0", "feature_1", "feature_2", "feature_3", "feature_4", "feature_5", "feature_6", "feature_7"], "target_achieved": "False", "training_samples": 135530, "benchmark_results": {"RandomForest": {"r2": 0.5584163430466755, "mae": 38671.27030523005, "rmse": 47653.86159714862, "cv_r2": 0.5518266613314808, "cv_std": 0.006521204353459727}, "GradientBoosting": {"r2": 0.5534335922049585, "mae": 39041.04633338375, "rmse": 47921.96622429696, "cv_r2": 0.5499132764334321, "cv_std": 0.007005915383802134}, "Ridge": {"r2": 0.4615399205736669, "mae": 44944.186254529945, "rmse": 52622.122194347336, "cv_r2": 0.4558575861011772, "cv_std": 0.006052653518423938}, "LinearRegression": {"r2": 0.4615240212788654, "mae": 44954.095274811625, "rmse": 52622.899084306184, "cv_r2": 0.45585129031810967, "cv_std": 0.006040437097695884}}}, "system_1_yearly": {"model": "RandomForestRegressor(max_depth=10, min_samples_leaf=2, min_samples_split=5,\n                      random_state=42)", "algorithm": "RandomForest", "metrics": {"r2": 0.47156167769585444, "mae": 198563.71842163283, "rmse": 269879.28952636494, "cv_r2": 0.47286125979625926, "cv_std": 0.002262852687090821}, "features": ["feature_0", "feature_1", "feature_2", "feature_3", "feature_4", "feature_5", "feature_6", "feature_7"], "target_achieved": "False", "training_samples": 135530, "benchmark_results": {"RandomForest": {"r2": 0.47156167769585444, "mae": 198563.71842163283, "rmse": 269879.28952636494, "cv_r2": 0.47286125979625926, "cv_std": 0.002262852687090821}, "GradientBoosting": {"r2": 0.46289944182412757, "mae": 202396.20583232175, "rmse": 272082.2482009422, "cv_r2": 0.4654568556119201, "cv_std": 0.0030457777362880574}, "Ridge": {"r2": 0.3251447854718532, "mae": 260392.70704665472, "rmse": 304984.45894739224, "cv_r2": 0.3293146592225426, "cv_std": 0.0030413089088811235}, "LinearRegression": {"r2": 0.3251414482716184, "mae": 260326.0817145332, "rmse": 304985.21302984416, "cv_r2": 0.3293070684696928, "cv_std": 0.003041537972359269}}}, "system_2_hourly": {"model": "GradientBoostingRegressor(max_depth=6, random_state=42)", "algorithm": "GradientBoosting", "metrics": {"r2": 0.3399522385138263, "mae": 0.0795145587854486, "rmse": 0.24172322572036972, "cv_r2": 0.4230527186010235, "cv_std": 0.2966350693644157}, "features": ["feature_0", "feature_1", "feature_2", "feature_3", "feature_4", "feature_5", "feature_6", "feature_7"], "target_achieved": "False", "training_samples": 129697, "benchmark_results": {"RandomForest": {"r2": 0.3358453980135768, "mae": 0.07946904975728784, "rmse": 0.24247406482148706, "cv_r2": 0.5190335098511534, "cv_std": 0.16104114675334677}, "GradientBoosting": {"r2": 0.3399522385138263, "mae": 0.0795145587854486, "rmse": 0.24172322572036972, "cv_r2": 0.4230527186010235, "cv_std": 0.2966350693644157}, "Ridge": {"r2": 0.025987521162319838, "mae": 0.16254616603647362, "rmse": 0.29363847531770126, "cv_r2": 0.04082260327984688, "cv_std": 0.010779980790656223}, "LinearRegression": {"r2": 0.0259386724780436, "mae": 0.162575285350879, "rmse": 0.29364583850536313, "cv_r2": 0.040863620571751455, "cv_std": 0.010803832529706231}}}, "system_2_daily": {"model": "GradientBoostingRegressor(max_depth=6, random_state=42)", "algorithm": "GradientBoosting", "metrics": {"r2": 0.8094080299666757, "mae": 6.287559506034999, "rmse": 9.502885355512026, "cv_r2": 0.8127935405878656, "cv_std": 0.0036642003887960147}, "features": ["feature_0", "feature_1", "feature_2", "feature_3", "feature_4", "feature_5", "feature_6", "feature_7"], "target_achieved": "False", "training_samples": 129697, "benchmark_results": {"RandomForest": {"r2": 0.8067799610616788, "mae": 6.313444055922113, "rmse": 9.568178593431657, "cv_r2": 0.8096916713870608, "cv_std": 0.003509579388791326}, "GradientBoosting": {"r2": 0.8094080299666757, "mae": 6.287559506034999, "rmse": 9.502885355512026, "cv_r2": 0.8127935405878656, "cv_std": 0.0036642003887960147}, "Ridge": {"r2": 0.6760369310948036, "mae": 9.723920600023924, "rmse": 12.389419052786222, "cv_r2": 0.6806547425212484, "cv_std": 0.004096391528175952}, "LinearRegression": {"r2": 0.6760326764597067, "mae": 9.723999603578303, "rmse": 12.389500408176888, "cv_r2": 0.6806516482998293, "cv_std": 0.004092027113384288}}}, "system_2_monthly": {"model": "GradientBoostingRegressor(max_depth=6, random_state=42)", "algorithm": "GradientBoosting", "metrics": {"r2": 0.17869235879888357, "mae": 49981.099894240775, "rmse": 58215.97167519921, "cv_r2": 0.18029733763327738, "cv_std": 0.004831681096365399}, "features": ["feature_0", "feature_1", "feature_2", "feature_3", "feature_4", "feature_5", "feature_6", "feature_7"], "target_achieved": "False", "training_samples": 129697, "benchmark_results": {"RandomForest": {"r2": 0.17313234149743917, "mae": 50055.44260612027, "rmse": 58412.69202193734, "cv_r2": 0.17659166744149693, "cv_std": 0.00526036197702851}, "GradientBoosting": {"r2": 0.17869235879888357, "mae": 49981.099894240775, "rmse": 58215.97167519921, "cv_r2": 0.18029733763327738, "cv_std": 0.004831681096365399}, "Ridge": {"r2": 0.08874424372194523, "mae": 53473.20537472717, "rmse": 61321.0187392069, "cv_r2": 0.0905243431509876, "cv_std": 0.0035489363368908762}, "LinearRegression": {"r2": 0.08874199885631506, "mae": 53480.38527686332, "rmse": 61321.09427089032, "cv_r2": 0.0905224579518139, "cv_std": 0.003549994463476874}}}, "system_2_yearly": {"model": "RandomForestRegressor(max_depth=10, min_samples_leaf=2, min_samples_split=5,\n                      random_state=42)", "algorithm": "RandomForest", "metrics": {"r2": 0.1813882829796376, "mae": 318449.43861754594, "rmse": 356384.6034754361, "cv_r2": 0.18101122097261696, "cv_std": 0.004278607885928568}, "features": ["feature_0", "feature_1", "feature_2", "feature_3", "feature_4", "feature_5", "feature_6", "feature_7"], "target_achieved": "False", "training_samples": 129697, "benchmark_results": {"RandomForest": {"r2": 0.1813882829796376, "mae": 318449.43861754594, "rmse": 356384.6034754361, "cv_r2": 0.18101122097261696, "cv_std": 0.004278607885928568}, "GradientBoosting": {"r2": 0.1806531901510231, "mae": 319211.72580739745, "rmse": 356544.5797958752, "cv_r2": 0.17913626423289633, "cv_std": 0.004105389707690476}, "Ridge": {"r2": 0.11328404031899308, "mae": 338831.7419483635, "rmse": 370913.13896290935, "cv_r2": 0.11689559355901329, "cv_std": 0.00417861596861253}, "LinearRegression": {"r2": 0.1132772534701535, "mae": 338908.758710719, "rmse": 370914.5584290656, "cv_r2": 0.11689109677333805, "cv_std": 0.004179060218179362}}}}}, "seasonal_results": {"training_date": "2025-06-16T14:16:32.263053", "pipeline_version": "v2.0.0", "total_models": 8, "successful_models": 8, "target_achieved": 3, "systems": [1, 2], "seasons": ["spring", "summer", "autumn", "winter"], "models": {"spring_system1": {"model": "GradientBoostingRegressor(max_depth=6, random_state=42)", "algorithm": "GradientBoosting", "metrics": {"r2": 0.9109453957339255, "mae": 4.145991325655052, "rmse": 7.409846251409722, "cv_r2": 0.9160983944465123, "cv_std": 0.00187026576041489}, "features": ["hour_sin", "hour_cos", "temperature", "cloud_cover", "ghi", "soc"], "target_achieved": "False", "training_samples": 48341, "benchmark_results": {"RandomForest": {"r2": 0.9096369779992692, "mae": 4.056396901954458, "rmse": 7.464081641201581, "cv_r2": 0.9147582515164198, "cv_std": 0.0018285128104998717}, "GradientBoosting": {"r2": 0.9109453957339255, "mae": 4.145991325655052, "rmse": 7.409846251409722, "cv_r2": 0.9160983944465123, "cv_std": 0.00187026576041489}, "Ridge": {"r2": 0.7591952909751946, "mae": 8.893735485790508, "rmse": 12.184662253246563, "cv_r2": 0.7584892517118939, "cv_std": 0.004086181044673875}, "LinearRegression": {"r2": 0.7591953376508336, "mae": 8.893745737377158, "rmse": 12.184661072358265, "cv_r2": 0.758489247631552, "cv_std": 0.004086120239168668}}}, "summer_system1": {"model": "GradientBoostingRegressor(max_depth=6, random_state=42)", "algorithm": "GradientBoosting", "metrics": {"r2": 0.9775082731030215, "mae": 1.7956448449190703, "rmse": 4.114137079415139, "cv_r2": 0.9783272086874385, "cv_std": 0.0019284286715150068}, "features": ["hour_sin", "hour_cos", "temperature", "cloud_cover", "ghi", "soc"], "target_achieved": "True", "training_samples": 37490, "benchmark_results": {"RandomForest": {"r2": 0.9773665780068524, "mae": 1.7542209278543939, "rmse": 4.127076010238311, "cv_r2": 0.9770194678700326, "cv_std": 0.002091625205087337}, "GradientBoosting": {"r2": 0.9775082731030215, "mae": 1.7956448449190703, "rmse": 4.114137079415139, "cv_r2": 0.9783272086874385, "cv_std": 0.0019284286715150068}, "Ridge": {"r2": 0.821368008300989, "mae": 7.919080376623994, "rmse": 11.594365246316912, "cv_r2": 0.82531471952363, "cv_std": 0.0023926709580771614}, "LinearRegression": {"r2": 0.8213683427959262, "mae": 7.9192265227156495, "rmse": 11.59435439087536, "cv_r2": 0.8253147031552635, "cv_std": 0.002392507578933073}}}, "autumn_system1": {"model": "GradientBoostingRegressor(max_depth=6, random_state=42)", "algorithm": "GradientBoosting", "metrics": {"r2": 0.9359246952619726, "mae": 2.7978315570056207, "rmse": 5.113401965557791, "cv_r2": 0.9382247539793713, "cv_std": 0.005452916006884586}, "features": ["hour_sin", "hour_cos", "temperature", "cloud_cover", "ghi", "soc"], "target_achieved": "True", "training_samples": 24767, "benchmark_results": {"RandomForest": {"r2": 0.9327952933182223, "mae": 2.8178961668336755, "rmse": 5.236781320460276, "cv_r2": 0.9351129727236493, "cv_std": 0.004854686489901464}, "GradientBoosting": {"r2": 0.9359246952619726, "mae": 2.7978315570056207, "rmse": 5.113401965557791, "cv_r2": 0.9382247539793713, "cv_std": 0.005452916006884586}, "Ridge": {"r2": 0.7668025072396394, "mae": 7.274206947851232, "rmse": 9.754983345268768, "cv_r2": 0.7713730940071256, "cv_std": 0.005925995403950463}, "LinearRegression": {"r2": 0.7668027558104382, "mae": 7.274185546574025, "rmse": 9.754978146232062, "cv_r2": 0.7713730750978225, "cv_std": 0.00592665095422628}}}, "winter_system1": {"model": "GradientBoostingRegressor(max_depth=6, random_state=42)", "algorithm": "GradientBoosting", "metrics": {"r2": 0.8438646228488749, "mae": 2.9946449091950815, "rmse": 5.242187629394828, "cv_r2": 0.8394394330647377, "cv_std": 0.002724080279463824}, "features": ["hour_sin", "hour_cos", "temperature", "cloud_cover", "ghi", "soc"], "target_achieved": "False", "training_samples": 24932, "benchmark_results": {"RandomForest": {"r2": 0.8299604172033659, "mae": 3.110547943457112, "rmse": 5.47062465319385, "cv_r2": 0.8247334588645918, "cv_std": 0.003982859014801489}, "GradientBoosting": {"r2": 0.8438646228488749, "mae": 2.9946449091950815, "rmse": 5.242187629394828, "cv_r2": 0.8394394330647377, "cv_std": 0.002724080279463824}, "Ridge": {"r2": 0.6789355611309937, "mae": 5.639471284081312, "rmse": 7.51723289915939, "cv_r2": 0.6727580279482902, "cv_std": 0.007318427725983333}, "LinearRegression": {"r2": 0.6789355610507819, "mae": 5.639486698007967, "rmse": 7.517232900098409, "cv_r2": 0.6727580128455685, "cv_std": 0.007318214829853431}}}, "spring_system2": {"model": "GradientBoostingRegressor(max_depth=6, random_state=42)", "algorithm": "GradientBoosting", "metrics": {"r2": 0.8717538367247474, "mae": 5.179169882455267, "rmse": 8.118848395753002, "cv_r2": 0.8685993058075393, "cv_std": 0.002930077283839217}, "features": ["hour_sin", "hour_cos", "temperature", "cloud_cover", "ghi", "soc"], "target_achieved": "False", "training_samples": 48217, "benchmark_results": {"RandomForest": {"r2": 0.8708653503177811, "mae": 5.138020245363863, "rmse": 8.146923449350632, "cv_r2": 0.866799367877074, "cv_std": 0.0045187897424378565}, "GradientBoosting": {"r2": 0.8717538367247474, "mae": 5.179169882455267, "rmse": 8.118848395753002, "cv_r2": 0.8685993058075393, "cv_std": 0.002930077283839217}, "Ridge": {"r2": 0.7015036526704641, "mae": 9.270382812956111, "rmse": 12.386304483939572, "cv_r2": 0.6980922135647946, "cv_std": 0.003140967276312914}, "LinearRegression": {"r2": 0.7015035122652942, "mae": 9.270409471341738, "rmse": 12.386307397042186, "cv_r2": 0.6980922125543687, "cv_std": 0.003140949348067335}}}, "summer_system2": {"model": "GradientBoostingRegressor(max_depth=6, random_state=42)", "algorithm": "GradientBoosting", "metrics": {"r2": 0.9710598887919784, "mae": 2.3972749303207284, "rmse": 4.35443929648585, "cv_r2": 0.9670675187071482, "cv_std": 0.004523565744726354}, "features": ["hour_sin", "hour_cos", "temperature", "cloud_cover", "ghi", "soc"], "target_achieved": "True", "training_samples": 31526, "benchmark_results": {"RandomForest": {"r2": 0.9701496358820553, "mae": 2.426285130649251, "rmse": 4.422389185254638, "cv_r2": 0.9650036574363121, "cv_std": 0.004517143381879799}, "GradientBoosting": {"r2": 0.9710598887919784, "mae": 2.3972749303207284, "rmse": 4.35443929648585, "cv_r2": 0.9670675187071482, "cv_std": 0.004523565744726354}, "Ridge": {"r2": 0.788474365333876, "mae": 8.149479319471201, "rmse": 11.772363029804701, "cv_r2": 0.7882823867710715, "cv_std": 0.0007904062589880167}, "LinearRegression": {"r2": 0.7884749369638046, "mae": 8.149621828688847, "rmse": 11.772347122892063, "cv_r2": 0.7882823714952224, "cv_std": 0.0007906407078770389}}}, "autumn_system2": {"model": "GradientBoostingRegressor(max_depth=6, random_state=42)", "algorithm": "GradientBoosting", "metrics": {"r2": 0.8804471902807209, "mae": 3.65282917220834, "rmse": 6.107857980873652, "cv_r2": 0.8816220951747817, "cv_std": 0.007372055851117926}, "features": ["hour_sin", "hour_cos", "temperature", "cloud_cover", "ghi", "soc"], "target_achieved": "False", "training_samples": 24809, "benchmark_results": {"RandomForest": {"r2": 0.8757559850669699, "mae": 3.681164335586978, "rmse": 6.22653989744423, "cv_r2": 0.8739587085007343, "cv_std": 0.005790210771640481}, "GradientBoosting": {"r2": 0.8804471902807209, "mae": 3.65282917220834, "rmse": 6.107857980873652, "cv_r2": 0.8816220951747817, "cv_std": 0.007372055851117926}, "Ridge": {"r2": 0.6883264525675066, "mae": 7.339701313902055, "rmse": 9.861863991719403, "cv_r2": 0.6794911361268776, "cv_std": 0.00432137729643462}, "LinearRegression": {"r2": 0.688327396795124, "mae": 7.339775501868649, "rmse": 9.861849053250179, "cv_r2": 0.679491123597636, "cv_std": 0.004321877976354123}}}, "winter_system2": {"model": "GradientBoostingRegressor(max_depth=6, random_state=42)", "algorithm": "GradientBoosting", "metrics": {"r2": 0.8380001988536906, "mae": 2.5503364760919265, "rmse": 4.526166589651275, "cv_r2": 0.8369280238480139, "cv_std": 0.0039052134175788745}, "features": ["hour_sin", "hour_cos", "temperature", "cloud_cover", "ghi", "soc"], "target_achieved": "False", "training_samples": 25145, "benchmark_results": {"RandomForest": {"r2": 0.8324584553883583, "mae": 2.5462328252505118, "rmse": 4.602931918032886, "cv_r2": 0.8332742226214688, "cv_std": 0.003875164340425729}, "GradientBoosting": {"r2": 0.8380001988536906, "mae": 2.5503364760919265, "rmse": 4.526166589651275, "cv_r2": 0.8369280238480139, "cv_std": 0.0039052134175788745}, "Ridge": {"r2": 0.6336886613555809, "mae": 5.0139161066617115, "rmse": 6.806102334163488, "cv_r2": 0.6364802056118316, "cv_std": 0.004493296700653112}, "LinearRegression": {"r2": 0.633688558857814, "mae": 5.013968759414421, "rmse": 6.806103286372945, "cv_r2": 0.63648019727032, "cv_std": 0.0044932226800381185}}}}}, "overall_summary": {"total_successful": 16, "total_attempted": 16, "total_target_achieved": 3, "success_rate": 1.0, "target_achievement_rate": 0.1875, "training_duration_seconds": 366.231629}, "training_end": "2025-06-16T14:17:45.621300"}