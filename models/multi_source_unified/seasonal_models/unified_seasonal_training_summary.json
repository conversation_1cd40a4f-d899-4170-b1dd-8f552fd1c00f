{"training_date": "2025-06-16T14:16:32.263053", "pipeline_version": "v2.0.0", "total_models": 8, "successful_models": 8, "target_achieved": 3, "systems": [1, 2], "seasons": ["spring", "summer", "autumn", "winter"], "models": {"spring_system1": {"algorithm": "GradientBoosting", "metrics": {"r2": 0.9109453957339255, "mae": 4.145991325655052, "rmse": 7.409846251409722, "cv_r2": 0.9160983944465123, "cv_std": 0.00187026576041489}, "features": ["hour_sin", "hour_cos", "temperature", "cloud_cover", "ghi", "soc"], "target_achieved": false, "training_samples": 48341, "benchmark_results": {"RandomForest": {"r2": 0.9096369779992692, "mae": 4.056396901954458, "rmse": 7.464081641201581, "cv_r2": 0.9147582515164198, "cv_std": 0.0018285128104998717}, "GradientBoosting": {"r2": 0.9109453957339255, "mae": 4.145991325655052, "rmse": 7.409846251409722, "cv_r2": 0.9160983944465123, "cv_std": 0.00187026576041489}, "Ridge": {"r2": 0.7591952909751946, "mae": 8.893735485790508, "rmse": 12.184662253246563, "cv_r2": 0.7584892517118939, "cv_std": 0.004086181044673875}, "LinearRegression": {"r2": 0.7591953376508336, "mae": 8.893745737377158, "rmse": 12.184661072358265, "cv_r2": 0.758489247631552, "cv_std": 0.004086120239168668}}}, "summer_system1": {"algorithm": "GradientBoosting", "metrics": {"r2": 0.9775082731030215, "mae": 1.7956448449190703, "rmse": 4.114137079415139, "cv_r2": 0.9783272086874385, "cv_std": 0.0019284286715150068}, "features": ["hour_sin", "hour_cos", "temperature", "cloud_cover", "ghi", "soc"], "target_achieved": true, "training_samples": 37490, "benchmark_results": {"RandomForest": {"r2": 0.9773665780068524, "mae": 1.7542209278543939, "rmse": 4.127076010238311, "cv_r2": 0.9770194678700326, "cv_std": 0.002091625205087337}, "GradientBoosting": {"r2": 0.9775082731030215, "mae": 1.7956448449190703, "rmse": 4.114137079415139, "cv_r2": 0.9783272086874385, "cv_std": 0.0019284286715150068}, "Ridge": {"r2": 0.821368008300989, "mae": 7.919080376623994, "rmse": 11.594365246316912, "cv_r2": 0.82531471952363, "cv_std": 0.0023926709580771614}, "LinearRegression": {"r2": 0.8213683427959262, "mae": 7.9192265227156495, "rmse": 11.59435439087536, "cv_r2": 0.8253147031552635, "cv_std": 0.002392507578933073}}}, "autumn_system1": {"algorithm": "GradientBoosting", "metrics": {"r2": 0.9359246952619726, "mae": 2.7978315570056207, "rmse": 5.113401965557791, "cv_r2": 0.9382247539793713, "cv_std": 0.005452916006884586}, "features": ["hour_sin", "hour_cos", "temperature", "cloud_cover", "ghi", "soc"], "target_achieved": true, "training_samples": 24767, "benchmark_results": {"RandomForest": {"r2": 0.9327952933182223, "mae": 2.8178961668336755, "rmse": 5.236781320460276, "cv_r2": 0.9351129727236493, "cv_std": 0.004854686489901464}, "GradientBoosting": {"r2": 0.9359246952619726, "mae": 2.7978315570056207, "rmse": 5.113401965557791, "cv_r2": 0.9382247539793713, "cv_std": 0.005452916006884586}, "Ridge": {"r2": 0.7668025072396394, "mae": 7.274206947851232, "rmse": 9.754983345268768, "cv_r2": 0.7713730940071256, "cv_std": 0.005925995403950463}, "LinearRegression": {"r2": 0.7668027558104382, "mae": 7.274185546574025, "rmse": 9.754978146232062, "cv_r2": 0.7713730750978225, "cv_std": 0.00592665095422628}}}, "winter_system1": {"algorithm": "GradientBoosting", "metrics": {"r2": 0.8438646228488749, "mae": 2.9946449091950815, "rmse": 5.242187629394828, "cv_r2": 0.8394394330647377, "cv_std": 0.002724080279463824}, "features": ["hour_sin", "hour_cos", "temperature", "cloud_cover", "ghi", "soc"], "target_achieved": false, "training_samples": 24932, "benchmark_results": {"RandomForest": {"r2": 0.8299604172033659, "mae": 3.110547943457112, "rmse": 5.47062465319385, "cv_r2": 0.8247334588645918, "cv_std": 0.003982859014801489}, "GradientBoosting": {"r2": 0.8438646228488749, "mae": 2.9946449091950815, "rmse": 5.242187629394828, "cv_r2": 0.8394394330647377, "cv_std": 0.002724080279463824}, "Ridge": {"r2": 0.6789355611309937, "mae": 5.639471284081312, "rmse": 7.51723289915939, "cv_r2": 0.6727580279482902, "cv_std": 0.007318427725983333}, "LinearRegression": {"r2": 0.6789355610507819, "mae": 5.639486698007967, "rmse": 7.517232900098409, "cv_r2": 0.6727580128455685, "cv_std": 0.007318214829853431}}}, "spring_system2": {"algorithm": "GradientBoosting", "metrics": {"r2": 0.8717538367247474, "mae": 5.179169882455267, "rmse": 8.118848395753002, "cv_r2": 0.8685993058075393, "cv_std": 0.002930077283839217}, "features": ["hour_sin", "hour_cos", "temperature", "cloud_cover", "ghi", "soc"], "target_achieved": false, "training_samples": 48217, "benchmark_results": {"RandomForest": {"r2": 0.8708653503177811, "mae": 5.138020245363863, "rmse": 8.146923449350632, "cv_r2": 0.866799367877074, "cv_std": 0.0045187897424378565}, "GradientBoosting": {"r2": 0.8717538367247474, "mae": 5.179169882455267, "rmse": 8.118848395753002, "cv_r2": 0.8685993058075393, "cv_std": 0.002930077283839217}, "Ridge": {"r2": 0.7015036526704641, "mae": 9.270382812956111, "rmse": 12.386304483939572, "cv_r2": 0.6980922135647946, "cv_std": 0.003140967276312914}, "LinearRegression": {"r2": 0.7015035122652942, "mae": 9.270409471341738, "rmse": 12.386307397042186, "cv_r2": 0.6980922125543687, "cv_std": 0.003140949348067335}}}, "summer_system2": {"algorithm": "GradientBoosting", "metrics": {"r2": 0.9710598887919784, "mae": 2.3972749303207284, "rmse": 4.35443929648585, "cv_r2": 0.9670675187071482, "cv_std": 0.004523565744726354}, "features": ["hour_sin", "hour_cos", "temperature", "cloud_cover", "ghi", "soc"], "target_achieved": true, "training_samples": 31526, "benchmark_results": {"RandomForest": {"r2": 0.9701496358820553, "mae": 2.426285130649251, "rmse": 4.422389185254638, "cv_r2": 0.9650036574363121, "cv_std": 0.004517143381879799}, "GradientBoosting": {"r2": 0.9710598887919784, "mae": 2.3972749303207284, "rmse": 4.35443929648585, "cv_r2": 0.9670675187071482, "cv_std": 0.004523565744726354}, "Ridge": {"r2": 0.788474365333876, "mae": 8.149479319471201, "rmse": 11.772363029804701, "cv_r2": 0.7882823867710715, "cv_std": 0.0007904062589880167}, "LinearRegression": {"r2": 0.7884749369638046, "mae": 8.149621828688847, "rmse": 11.772347122892063, "cv_r2": 0.7882823714952224, "cv_std": 0.0007906407078770389}}}, "autumn_system2": {"algorithm": "GradientBoosting", "metrics": {"r2": 0.8804471902807209, "mae": 3.65282917220834, "rmse": 6.107857980873652, "cv_r2": 0.8816220951747817, "cv_std": 0.007372055851117926}, "features": ["hour_sin", "hour_cos", "temperature", "cloud_cover", "ghi", "soc"], "target_achieved": false, "training_samples": 24809, "benchmark_results": {"RandomForest": {"r2": 0.8757559850669699, "mae": 3.681164335586978, "rmse": 6.22653989744423, "cv_r2": 0.8739587085007343, "cv_std": 0.005790210771640481}, "GradientBoosting": {"r2": 0.8804471902807209, "mae": 3.65282917220834, "rmse": 6.107857980873652, "cv_r2": 0.8816220951747817, "cv_std": 0.007372055851117926}, "Ridge": {"r2": 0.6883264525675066, "mae": 7.339701313902055, "rmse": 9.861863991719403, "cv_r2": 0.6794911361268776, "cv_std": 0.00432137729643462}, "LinearRegression": {"r2": 0.688327396795124, "mae": 7.339775501868649, "rmse": 9.861849053250179, "cv_r2": 0.679491123597636, "cv_std": 0.004321877976354123}}}, "winter_system2": {"algorithm": "GradientBoosting", "metrics": {"r2": 0.8380001988536906, "mae": 2.5503364760919265, "rmse": 4.526166589651275, "cv_r2": 0.8369280238480139, "cv_std": 0.0039052134175788745}, "features": ["hour_sin", "hour_cos", "temperature", "cloud_cover", "ghi", "soc"], "target_achieved": false, "training_samples": 25145, "benchmark_results": {"RandomForest": {"r2": 0.8324584553883583, "mae": 2.5462328252505118, "rmse": 4.602931918032886, "cv_r2": 0.8332742226214688, "cv_std": 0.003875164340425729}, "GradientBoosting": {"r2": 0.8380001988536906, "mae": 2.5503364760919265, "rmse": 4.526166589651275, "cv_r2": 0.8369280238480139, "cv_std": 0.0039052134175788745}, "Ridge": {"r2": 0.6336886613555809, "mae": 5.0139161066617115, "rmse": 6.806102334163488, "cv_r2": 0.6364802056118316, "cv_std": 0.004493296700653112}, "LinearRegression": {"r2": 0.633688558857814, "mae": 5.013968759414421, "rmse": 6.806103286372945, "cv_r2": 0.63648019727032, "cv_std": 0.0044932226800381185}}}}}