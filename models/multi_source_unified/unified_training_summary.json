{"training_date": "2025-06-16T14:11:41.065042", "pipeline_version": "v2.0.0", "total_models": 8, "successful_models": 8, "target_achieved": 0, "systems": [1, 2], "horizons": ["hourly", "daily", "monthly", "yearly"], "models": {"system_1_hourly": {"algorithm": "GradientBoosting", "metrics": {"r2": 0.10912538461331822, "mae": 0.14688982739837952, "rmse": 0.8347110435161373, "cv_r2": 0.07741628126320813, "cv_std": 0.04874301242705216}, "features": ["feature_0", "feature_1", "feature_2", "feature_3", "feature_4", "feature_5", "feature_6", "feature_7"], "target_achieved": false, "training_samples": 135530, "benchmark_results": {"RandomForest": {"r2": 0.09996809869096834, "mae": 0.14195530346827948, "rmse": 0.8389900663596315, "cv_r2": 0.11977348024786494, "cv_std": 0.02766928171903758}, "GradientBoosting": {"r2": 0.10912538461331822, "mae": 0.14688982739837952, "rmse": 0.8347110435161373, "cv_r2": 0.07741628126320813, "cv_std": 0.04874301242705216}, "Ridge": {"r2": 0.0029948618396089532, "mae": 0.24936469534950198, "rmse": 0.8830322507715722, "cv_r2": 0.0024773747935448176, "cv_std": 0.0006307209001568749}, "LinearRegression": {"r2": 0.002986537631269881, "mae": 0.24956264130428168, "rmse": 0.8830359370760863, "cv_r2": 0.002469345032733772, "cv_std": 0.0006157920269670961}}}, "system_1_daily": {"algorithm": "RandomForest", "metrics": {"r2": 0.876730363914491, "mae": 4.950326093463333, "rmse": 8.403419883095124, "cv_r2": 0.8746761095867435, "cv_std": 0.0031371575969491522}, "features": ["feature_0", "feature_1", "feature_2", "feature_3", "feature_4", "feature_5", "feature_6", "feature_7"], "target_achieved": false, "training_samples": 135530, "benchmark_results": {"RandomForest": {"r2": 0.876730363914491, "mae": 4.950326093463333, "rmse": 8.403419883095124, "cv_r2": 0.8746761095867435, "cv_std": 0.0031371575969491522}, "GradientBoosting": {"r2": 0.8755214794207725, "mae": 5.059874334066285, "rmse": 8.444524811181113, "cv_r2": 0.8745685489372376, "cv_std": 0.0031015739749092283}, "Ridge": {"r2": 0.7100452450277157, "mae": 9.86670539335924, "rmse": 12.88822545308679, "cv_r2": 0.7105124287082838, "cv_std": 0.004054153966847396}, "LinearRegression": {"r2": 0.7100440747132475, "mae": 9.866316207646735, "rmse": 12.88825146276802, "cv_r2": 0.7105150404478336, "cv_std": 0.00405370254396794}}}, "system_1_monthly": {"algorithm": "RandomForest", "metrics": {"r2": 0.5584163430466755, "mae": 38671.27030523005, "rmse": 47653.86159714862, "cv_r2": 0.5518266613314808, "cv_std": 0.006521204353459727}, "features": ["feature_0", "feature_1", "feature_2", "feature_3", "feature_4", "feature_5", "feature_6", "feature_7"], "target_achieved": false, "training_samples": 135530, "benchmark_results": {"RandomForest": {"r2": 0.5584163430466755, "mae": 38671.27030523005, "rmse": 47653.86159714862, "cv_r2": 0.5518266613314808, "cv_std": 0.006521204353459727}, "GradientBoosting": {"r2": 0.5534335922049585, "mae": 39041.04633338375, "rmse": 47921.96622429696, "cv_r2": 0.5499132764334321, "cv_std": 0.007005915383802134}, "Ridge": {"r2": 0.4615399205736669, "mae": 44944.186254529945, "rmse": 52622.122194347336, "cv_r2": 0.4558575861011772, "cv_std": 0.006052653518423938}, "LinearRegression": {"r2": 0.4615240212788654, "mae": 44954.095274811625, "rmse": 52622.899084306184, "cv_r2": 0.45585129031810967, "cv_std": 0.006040437097695884}}}, "system_1_yearly": {"algorithm": "RandomForest", "metrics": {"r2": 0.47156167769585444, "mae": 198563.71842163283, "rmse": 269879.28952636494, "cv_r2": 0.47286125979625926, "cv_std": 0.002262852687090821}, "features": ["feature_0", "feature_1", "feature_2", "feature_3", "feature_4", "feature_5", "feature_6", "feature_7"], "target_achieved": false, "training_samples": 135530, "benchmark_results": {"RandomForest": {"r2": 0.47156167769585444, "mae": 198563.71842163283, "rmse": 269879.28952636494, "cv_r2": 0.47286125979625926, "cv_std": 0.002262852687090821}, "GradientBoosting": {"r2": 0.46289944182412757, "mae": 202396.20583232175, "rmse": 272082.2482009422, "cv_r2": 0.4654568556119201, "cv_std": 0.0030457777362880574}, "Ridge": {"r2": 0.3251447854718532, "mae": 260392.70704665472, "rmse": 304984.45894739224, "cv_r2": 0.3293146592225426, "cv_std": 0.0030413089088811235}, "LinearRegression": {"r2": 0.3251414482716184, "mae": 260326.0817145332, "rmse": 304985.21302984416, "cv_r2": 0.3293070684696928, "cv_std": 0.003041537972359269}}}, "system_2_hourly": {"algorithm": "GradientBoosting", "metrics": {"r2": 0.3399522385138263, "mae": 0.0795145587854486, "rmse": 0.24172322572036972, "cv_r2": 0.4230527186010235, "cv_std": 0.2966350693644157}, "features": ["feature_0", "feature_1", "feature_2", "feature_3", "feature_4", "feature_5", "feature_6", "feature_7"], "target_achieved": false, "training_samples": 129697, "benchmark_results": {"RandomForest": {"r2": 0.3358453980135768, "mae": 0.07946904975728784, "rmse": 0.24247406482148706, "cv_r2": 0.5190335098511534, "cv_std": 0.16104114675334677}, "GradientBoosting": {"r2": 0.3399522385138263, "mae": 0.0795145587854486, "rmse": 0.24172322572036972, "cv_r2": 0.4230527186010235, "cv_std": 0.2966350693644157}, "Ridge": {"r2": 0.025987521162319838, "mae": 0.16254616603647362, "rmse": 0.29363847531770126, "cv_r2": 0.04082260327984688, "cv_std": 0.010779980790656223}, "LinearRegression": {"r2": 0.0259386724780436, "mae": 0.162575285350879, "rmse": 0.29364583850536313, "cv_r2": 0.040863620571751455, "cv_std": 0.010803832529706231}}}, "system_2_daily": {"algorithm": "GradientBoosting", "metrics": {"r2": 0.8094080299666757, "mae": 6.287559506034999, "rmse": 9.502885355512026, "cv_r2": 0.8127935405878656, "cv_std": 0.0036642003887960147}, "features": ["feature_0", "feature_1", "feature_2", "feature_3", "feature_4", "feature_5", "feature_6", "feature_7"], "target_achieved": false, "training_samples": 129697, "benchmark_results": {"RandomForest": {"r2": 0.8067799610616788, "mae": 6.313444055922113, "rmse": 9.568178593431657, "cv_r2": 0.8096916713870608, "cv_std": 0.003509579388791326}, "GradientBoosting": {"r2": 0.8094080299666757, "mae": 6.287559506034999, "rmse": 9.502885355512026, "cv_r2": 0.8127935405878656, "cv_std": 0.0036642003887960147}, "Ridge": {"r2": 0.6760369310948036, "mae": 9.723920600023924, "rmse": 12.389419052786222, "cv_r2": 0.6806547425212484, "cv_std": 0.004096391528175952}, "LinearRegression": {"r2": 0.6760326764597067, "mae": 9.723999603578303, "rmse": 12.389500408176888, "cv_r2": 0.6806516482998293, "cv_std": 0.004092027113384288}}}, "system_2_monthly": {"algorithm": "GradientBoosting", "metrics": {"r2": 0.17869235879888357, "mae": 49981.099894240775, "rmse": 58215.97167519921, "cv_r2": 0.18029733763327738, "cv_std": 0.004831681096365399}, "features": ["feature_0", "feature_1", "feature_2", "feature_3", "feature_4", "feature_5", "feature_6", "feature_7"], "target_achieved": false, "training_samples": 129697, "benchmark_results": {"RandomForest": {"r2": 0.17313234149743917, "mae": 50055.44260612027, "rmse": 58412.69202193734, "cv_r2": 0.17659166744149693, "cv_std": 0.00526036197702851}, "GradientBoosting": {"r2": 0.17869235879888357, "mae": 49981.099894240775, "rmse": 58215.97167519921, "cv_r2": 0.18029733763327738, "cv_std": 0.004831681096365399}, "Ridge": {"r2": 0.08874424372194523, "mae": 53473.20537472717, "rmse": 61321.0187392069, "cv_r2": 0.0905243431509876, "cv_std": 0.0035489363368908762}, "LinearRegression": {"r2": 0.08874199885631506, "mae": 53480.38527686332, "rmse": 61321.09427089032, "cv_r2": 0.0905224579518139, "cv_std": 0.003549994463476874}}}, "system_2_yearly": {"algorithm": "RandomForest", "metrics": {"r2": 0.1813882829796376, "mae": 318449.43861754594, "rmse": 356384.6034754361, "cv_r2": 0.18101122097261696, "cv_std": 0.004278607885928568}, "features": ["feature_0", "feature_1", "feature_2", "feature_3", "feature_4", "feature_5", "feature_6", "feature_7"], "target_achieved": false, "training_samples": 129697, "benchmark_results": {"RandomForest": {"r2": 0.1813882829796376, "mae": 318449.43861754594, "rmse": 356384.6034754361, "cv_r2": 0.18101122097261696, "cv_std": 0.004278607885928568}, "GradientBoosting": {"r2": 0.1806531901510231, "mae": 319211.72580739745, "rmse": 356544.5797958752, "cv_r2": 0.17913626423289633, "cv_std": 0.004105389707690476}, "Ridge": {"r2": 0.11328404031899308, "mae": 338831.7419483635, "rmse": 370913.13896290935, "cv_r2": 0.11689559355901329, "cv_std": 0.00417861596861253}, "LinearRegression": {"r2": 0.1132772534701535, "mae": 338908.758710719, "rmse": 370914.5584290656, "cv_r2": 0.11689109677333805, "cv_std": 0.004179060218179362}}}}}