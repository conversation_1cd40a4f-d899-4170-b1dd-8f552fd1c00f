{"version": "v2.0.0", "created_at": "2025-06-16T14:11:39.389494", "scaler_type": "StandardScaler", "feature_mappings": {"multi_horizon": {"feature_0": "soc", "feature_1": "bat_power", "feature_2": "temperature", "feature_3": "ghi", "feature_4": "air_temp", "feature_5": "humidity", "feature_6": "cloud_cover", "feature_7": "hour"}, "seasonal": {"hour_sin": "hour_sin", "hour_cos": "hour_cos", "temperature": "temperature", "cloud_cover": "cloud_cover", "ghi": "ghi", "soc": "soc"}}, "model_groups": {"multi_horizon": {"models": ["multi_horizon_daily_system1", "multi_horizon_daily_system2", "multi_horizon_hourly_system1", "multi_horizon_hourly_system2", "multi_horizon_monthly_system1", "multi_horizon_monthly_system2", "multi_horizon_yearly_system1", "multi_horizon_yearly_system2"], "features": ["feature_0", "feature_1", "feature_2", "feature_3", "feature_4", "feature_5", "feature_6", "feature_7"], "real_features": ["soc", "bat_power", "temperature", "ghi", "air_temp", "humidity", "cloud_cover", "hour"]}, "seasonal": {"models": ["spring_system1", "spring_system2", "summer_system1", "summer_system2", "autumn_system1", "autumn_system2", "winter_system1", "winter_system2"], "features": ["hour_sin", "hour_cos", "temperature", "cloud_cover", "ghi", "soc"], "real_features": ["hour_sin", "hour_cos", "temperature", "cloud_cover", "ghi", "soc"]}}, "fitted_groups": ["seasonal"], "scaler_files": {"seasonal_v2.0.0": "models/multi_source_unified/preprocessing/scaler_seasonal_v2.0.0.joblib"}}