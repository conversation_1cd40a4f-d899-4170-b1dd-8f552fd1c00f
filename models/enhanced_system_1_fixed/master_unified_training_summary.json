{"training_start": "2025-06-17T06:30:30.068300", "pipeline_version": "v2.0.0", "total_target_models": 16, "multi_horizon_results": {"training_date": "2025-06-17T06:30:31.633464", "pipeline_version": "v2.0.0", "total_models": 8, "successful_models": 8, "target_achieved": 0, "systems": [1, 2], "horizons": ["hourly", "daily", "monthly", "yearly"], "models": {"system_1_hourly": {"model": "GradientBoostingRegressor(max_depth=6, random_state=42)", "algorithm": "GradientBoosting", "metrics": {"r2": 0.13557351863588107, "mae": 0.14522661252118962, "rmse": 0.7363331066194363, "cv_r2": 0.06132845100456361, "cv_std": 0.05345090781117273}, "features": ["feature_0", "feature_1", "feature_2", "feature_3", "feature_4", "feature_5", "feature_6", "feature_7"], "target_achieved": "False", "training_samples": 135422, "benchmark_results": {"RandomForest": {"r2": 0.1340715761958886, "mae": 0.13961683976245495, "rmse": 0.7369725189898026, "cv_r2": 0.09343144964322095, "cv_std": 0.019757842862289564}, "GradientBoosting": {"r2": 0.13557351863588107, "mae": 0.14522661252118962, "rmse": 0.7363331066194363, "cv_r2": 0.06132845100456361, "cv_std": 0.05345090781117273}, "Ridge": {"r2": 0.0029284941452493163, "mae": 0.24755788100274173, "rmse": 0.790812360229418, "cv_r2": 0.002536313951822922, "cv_std": 0.00027609077420843596}, "LinearRegression": {"r2": 0.002920731293609702, "mae": 0.24763822058867485, "rmse": 0.7908154387182939, "cv_r2": 0.002537248772929446, "cv_std": 0.00026844397315306165}}}, "system_1_daily": {"model": "RandomForestRegressor(max_depth=10, min_samples_leaf=2, min_samples_split=5,\n                      random_state=42)", "algorithm": "RandomForest", "metrics": {"r2": 0.878472272684014, "mae": 4.96697894132754, "rmse": 8.33258120019372, "cv_r2": 0.8743609633269556, "cv_std": 0.004031340754304483}, "features": ["feature_0", "feature_1", "feature_2", "feature_3", "feature_4", "feature_5", "feature_6", "feature_7"], "target_achieved": "False", "training_samples": 135422, "benchmark_results": {"RandomForest": {"r2": 0.878472272684014, "mae": 4.96697894132754, "rmse": 8.33258120019372, "cv_r2": 0.8743609633269556, "cv_std": 0.004031340754304483}, "GradientBoosting": {"r2": 0.8776210157078066, "mae": 5.061863696000572, "rmse": 8.36171360515715, "cv_r2": 0.8734084434284595, "cv_std": 0.0037550759425215648}, "Ridge": {"r2": 0.7115456872100274, "mae": 9.859732728226998, "rmse": 12.837497874611197, "cv_r2": 0.7104599092189268, "cv_std": 0.0051523494824366484}, "LinearRegression": {"r2": 0.7115528763773756, "mae": 9.860257692754422, "rmse": 12.837337898678248, "cv_r2": 0.7104553320689321, "cv_std": 0.005152993774489038}}}, "system_1_monthly": {"model": "RandomForestRegressor(max_depth=10, min_samples_leaf=2, min_samples_split=5,\n                      random_state=42)", "algorithm": "RandomForest", "metrics": {"r2": 0.5482947898697407, "mae": 38769.743595191685, "rmse": 47813.19476309184, "cv_r2": 0.5426637077810839, "cv_std": 0.00717138925269605}, "features": ["feature_0", "feature_1", "feature_2", "feature_3", "feature_4", "feature_5", "feature_6", "feature_7"], "target_achieved": "False", "training_samples": 135422, "benchmark_results": {"RandomForest": {"r2": 0.5482947898697407, "mae": 38769.743595191685, "rmse": 47813.19476309184, "cv_r2": 0.5426637077810839, "cv_std": 0.00717138925269605}, "GradientBoosting": {"r2": 0.5455528103695452, "mae": 39057.740562931904, "rmse": 47958.09506607448, "cv_r2": 0.540117847192416, "cv_std": 0.007386036825209213}, "Ridge": {"r2": 0.4521949131925155, "mae": 45107.29283396026, "rmse": 52654.22726370386, "cv_r2": 0.44996137310218476, "cv_std": 0.006662059609392156}, "LinearRegression": {"r2": 0.45215684134223866, "mae": 45142.33029163007, "rmse": 52656.05693731969, "cv_r2": 0.449952904694974, "cv_std": 0.006623630086047892}}}, "system_1_yearly": {"model": "RandomForestRegressor(max_depth=10, min_samples_leaf=2, min_samples_split=5,\n                      random_state=42)", "algorithm": "RandomForest", "metrics": {"r2": 0.4726054398070012, "mae": 199873.85149478726, "rmse": 271862.5786274655, "cv_r2": 0.4730265862929154, "cv_std": 0.005424014914539368}, "features": ["feature_0", "feature_1", "feature_2", "feature_3", "feature_4", "feature_5", "feature_6", "feature_7"], "target_achieved": "False", "training_samples": 135422, "benchmark_results": {"RandomForest": {"r2": 0.4726054398070012, "mae": 199873.85149478726, "rmse": 271862.5786274655, "cv_r2": 0.4730265862929154, "cv_std": 0.005424014914539368}, "GradientBoosting": {"r2": 0.46274824734655085, "mae": 204171.3666755214, "rmse": 274391.42137288075, "cv_r2": 0.4641219697955437, "cv_std": 0.007085897026359015}, "Ridge": {"r2": 0.3220643301525644, "mae": 263250.3348455841, "rmse": 308230.6708069582, "cv_r2": 0.3298946973288562, "cv_std": 0.005755159412343545}, "LinearRegression": {"r2": 0.3220672687837711, "mae": 263275.26117952046, "rmse": 308230.0027662502, "cv_r2": 0.32989559627165344, "cv_std": 0.005753137460914874}}}, "system_2_hourly": {"model": "GradientBoostingRegressor(max_depth=6, random_state=42)", "algorithm": "GradientBoosting", "metrics": {"r2": 0.637988418263844, "mae": 0.07830873698584356, "rmse": 0.1301988007530838, "cv_r2": 0.30606455907743413, "cv_std": 0.2391101744777217}, "features": ["feature_0", "feature_1", "feature_2", "feature_3", "feature_4", "feature_5", "feature_6", "feature_7"], "target_achieved": "False", "training_samples": 129591, "benchmark_results": {"RandomForest": {"r2": 0.6204683842255347, "mae": 0.07820353493853849, "rmse": 0.13331214956978252, "cv_r2": 0.4514464420590068, "cv_std": 0.16458485931661618}, "GradientBoosting": {"r2": 0.637988418263844, "mae": 0.07830873698584356, "rmse": 0.1301988007530838, "cv_r2": 0.30606455907743413, "cv_std": 0.2391101744777217}, "Ridge": {"r2": 0.045927209101683464, "mae": 0.16132239803625642, "rmse": 0.21136667068115192, "cv_r2": 0.04012869771243936, "cv_std": 0.01300520149924744}, "LinearRegression": {"r2": 0.0459547879051162, "mae": 0.16134837063319862, "rmse": 0.21136361573500695, "cv_r2": 0.0401686658155449, "cv_std": 0.013016092168723702}}}, "system_2_daily": {"model": "GradientBoostingRegressor(max_depth=6, random_state=42)", "algorithm": "GradientBoosting", "metrics": {"r2": 0.812659660017023, "mae": 6.228357414013428, "rmse": 9.417282489453132, "cv_r2": 0.812522068920752, "cv_std": 0.003205689203276007}, "features": ["feature_0", "feature_1", "feature_2", "feature_3", "feature_4", "feature_5", "feature_6", "feature_7"], "target_achieved": "False", "training_samples": 129591, "benchmark_results": {"RandomForest": {"r2": 0.8096023881755404, "mae": 6.251668784988598, "rmse": 9.493813465160285, "cv_r2": 0.8098605477347224, "cv_std": 0.0027755588597695455}, "GradientBoosting": {"r2": 0.812659660017023, "mae": 6.228357414013428, "rmse": 9.417282489453132, "cv_r2": 0.812522068920752, "cv_std": 0.003205689203276007}, "Ridge": {"r2": 0.6829977148543223, "mae": 9.636778764224967, "rmse": 12.25014255256647, "cv_r2": 0.6829622577027327, "cv_std": 0.0032922287568674796}, "LinearRegression": {"r2": 0.6829977124705, "mae": 9.63677890417215, "rmse": 12.250142598626331, "cv_r2": 0.6829617999578149, "cv_std": 0.003292705195226867}}}, "system_2_monthly": {"model": "GradientBoostingRegressor(max_depth=6, random_state=42)", "algorithm": "GradientBoosting", "metrics": {"r2": 0.18432448272248703, "mae": 49791.22183943848, "rmse": 58013.92542133068, "cv_r2": 0.18087483458922798, "cv_std": 0.003918746582044892}, "features": ["feature_0", "feature_1", "feature_2", "feature_3", "feature_4", "feature_5", "feature_6", "feature_7"], "target_achieved": "False", "training_samples": 129591, "benchmark_results": {"RandomForest": {"r2": 0.1782201747425196, "mae": 49883.811469451604, "rmse": 58230.60151635822, "cv_r2": 0.17698310683530896, "cv_std": 0.004941743666274117}, "GradientBoosting": {"r2": 0.18432448272248703, "mae": 49791.22183943848, "rmse": 58013.92542133068, "cv_r2": 0.18087483458922798, "cv_std": 0.003918746582044892}, "Ridge": {"r2": 0.08720417525140334, "mae": 53532.5180234089, "rmse": 61370.5988678243, "cv_r2": 0.09101319144426841, "cv_std": 0.005384252336559522}, "LinearRegression": {"r2": 0.08720805064889314, "mae": 53549.26003755139, "rmse": 61370.4685891186, "cv_r2": 0.0909922262547405, "cv_std": 0.005413144259127383}}}, "system_2_yearly": {"model": "GradientBoostingRegressor(max_depth=6, random_state=42)", "algorithm": "GradientBoosting", "metrics": {"r2": 0.18480021270316005, "mae": 319163.6411063093, "rmse": 355843.55845035834, "cv_r2": 0.1791593256962417, "cv_std": 0.004320403287603175}, "features": ["feature_0", "feature_1", "feature_2", "feature_3", "feature_4", "feature_5", "feature_6", "feature_7"], "target_achieved": "False", "training_samples": 129591, "benchmark_results": {"RandomForest": {"r2": 0.1831396396424957, "mae": 318764.1507654094, "rmse": 356205.80315545294, "cv_r2": 0.1791228944267346, "cv_std": 0.003256642242577791}, "GradientBoosting": {"r2": 0.18480021270316005, "mae": 319163.6411063093, "rmse": 355843.55845035834, "cv_r2": 0.1791593256962417, "cv_std": 0.004320403287603175}, "Ridge": {"r2": 0.11885445918637016, "mae": 338285.15052911, "rmse": 369956.70569139684, "cv_r2": 0.11399201742947267, "cv_std": 0.002823494740333181}, "LinearRegression": {"r2": 0.11884371493035062, "mae": 338422.4847726433, "rmse": 369958.9612197182, "cv_r2": 0.11398388124422554, "cv_std": 0.0028255764750424093}}}}}, "seasonal_results": {"training_date": "2025-06-17T06:35:13.571509", "pipeline_version": "v2.0.0", "total_models": 8, "successful_models": 8, "target_achieved": 3, "systems": [1, 2], "seasons": ["spring", "summer", "autumn", "winter"], "models": {"spring_system1": {"model": "GradientBoostingRegressor(max_depth=6, random_state=42)", "algorithm": "GradientBoosting", "metrics": {"r2": 0.9109453957339255, "mae": 4.145991325655052, "rmse": 7.409846251409722, "cv_r2": 0.9160983944465123, "cv_std": 0.00187026576041489}, "features": ["hour_sin", "hour_cos", "temperature", "cloud_cover", "ghi", "soc"], "target_achieved": "False", "training_samples": 48341, "benchmark_results": {"RandomForest": {"r2": 0.9096366997899661, "mae": 4.05640724428562, "rmse": 7.46409313138478, "cv_r2": 0.9147763554350942, "cv_std": 0.001862422164097893}, "GradientBoosting": {"r2": 0.9109453957339255, "mae": 4.145991325655052, "rmse": 7.409846251409722, "cv_r2": 0.9160983944465123, "cv_std": 0.00187026576041489}, "Ridge": {"r2": 0.7591952910686975, "mae": 8.893735489322035, "rmse": 12.184662250880946, "cv_r2": 0.7584892517081665, "cv_std": 0.004086180955176324}, "LinearRegression": {"r2": 0.7591953376508336, "mae": 8.893745737377158, "rmse": 12.184661072358265, "cv_r2": 0.758489247631552, "cv_std": 0.004086120239168693}}}, "summer_system1": {"model": "GradientBoostingRegressor(max_depth=6, random_state=42)", "algorithm": "GradientBoosting", "metrics": {"r2": 0.9752907378323596, "mae": 1.8249076861017803, "rmse": 4.298059010484055, "cv_r2": 0.9787091913878221, "cv_std": 0.0016126006064545843}, "features": ["hour_sin", "hour_cos", "temperature", "cloud_cover", "ghi", "soc"], "target_achieved": "True", "training_samples": 37382, "benchmark_results": {"RandomForest": {"r2": 0.9743012856061158, "mae": 1.7962564326293937, "rmse": 4.383269606763925, "cv_r2": 0.9776394023340063, "cv_std": 0.0017896358010276127}, "GradientBoosting": {"r2": 0.9752907378323596, "mae": 1.8249076861017803, "rmse": 4.298059010484055, "cv_r2": 0.9787091913878221, "cv_std": 0.0016126006064545843}, "Ridge": {"r2": 0.8193369842273945, "mae": 7.949315329587291, "rmse": 11.621899430814382, "cv_r2": 0.8265358633734341, "cv_std": 0.002800111579732394}, "LinearRegression": {"r2": 0.8193369738025249, "mae": 7.949468114860591, "rmse": 11.621899766125912, "cv_r2": 0.8265358563527855, "cv_std": 0.002800000809773476}}}, "autumn_system1": {"model": "GradientBoostingRegressor(max_depth=6, random_state=42)", "algorithm": "GradientBoosting", "metrics": {"r2": 0.9359246952619726, "mae": 2.7978315570056207, "rmse": 5.113401965557791, "cv_r2": 0.9382247539793713, "cv_std": 0.005452916006884586}, "features": ["hour_sin", "hour_cos", "temperature", "cloud_cover", "ghi", "soc"], "target_achieved": "True", "training_samples": 24767, "benchmark_results": {"RandomForest": {"r2": 0.9327952933762645, "mae": 2.817896041326461, "rmse": 5.236781318198867, "cv_r2": 0.9351111577887732, "cv_std": 0.004855100347009235}, "GradientBoosting": {"r2": 0.9359246952619726, "mae": 2.7978315570056207, "rmse": 5.113401965557791, "cv_r2": 0.9382247539793713, "cv_std": 0.005452916006884586}, "Ridge": {"r2": 0.7668025073347369, "mae": 7.274206966315619, "rmse": 9.754983343279735, "cv_r2": 0.7713730940055513, "cv_std": 0.005925995424323741}, "LinearRegression": {"r2": 0.7668027558104382, "mae": 7.274185546574024, "rmse": 9.754978146232062, "cv_r2": 0.7713730750978225, "cv_std": 0.00592665095422628}}}, "winter_system1": {"model": "GradientBoostingRegressor(max_depth=6, random_state=42)", "algorithm": "GradientBoosting", "metrics": {"r2": 0.8438646228488749, "mae": 2.9946449091950815, "rmse": 5.242187629394828, "cv_r2": 0.8394394330647377, "cv_std": 0.002724080279463824}, "features": ["hour_sin", "hour_cos", "temperature", "cloud_cover", "ghi", "soc"], "target_achieved": "False", "training_samples": 24932, "benchmark_results": {"RandomForest": {"r2": 0.8299636820178202, "mae": 3.110478350751371, "rmse": 5.470572134068946, "cv_r2": 0.8247340873616558, "cv_std": 0.003982545151737071}, "GradientBoosting": {"r2": 0.8438646228488749, "mae": 2.9946449091950815, "rmse": 5.242187629394828, "cv_r2": 0.8394394330647377, "cv_std": 0.002724080279463824}, "Ridge": {"r2": 0.6789355611799055, "mae": 5.639471284472056, "rmse": 7.5172328985867924, "cv_r2": 0.6727580279462508, "cv_std": 0.007318427793347283}, "LinearRegression": {"r2": 0.6789355610507819, "mae": 5.639486698007966, "rmse": 7.517232900098408, "cv_r2": 0.6727580128455685, "cv_std": 0.007318214829853431}}}, "spring_system2": {"model": "GradientBoostingRegressor(max_depth=6, random_state=42)", "algorithm": "GradientBoosting", "metrics": {"r2": 0.8717538367247474, "mae": 5.179169882455267, "rmse": 8.118848395753002, "cv_r2": 0.8685993058075393, "cv_std": 0.002930077283839217}, "features": ["hour_sin", "hour_cos", "temperature", "cloud_cover", "ghi", "soc"], "target_achieved": "False", "training_samples": 48217, "benchmark_results": {"RandomForest": {"r2": 0.8708653503177811, "mae": 5.138020245363863, "rmse": 8.146923449350632, "cv_r2": 0.8668009168850153, "cv_std": 0.004516726715659866}, "GradientBoosting": {"r2": 0.8717538367247474, "mae": 5.179169882455267, "rmse": 8.118848395753002, "cv_r2": 0.8685993058075393, "cv_std": 0.002930077283839217}, "Ridge": {"r2": 0.7015036526547634, "mae": 9.270382813948835, "rmse": 12.386304484265326, "cv_r2": 0.6980922135652028, "cv_std": 0.003140967269785261}, "LinearRegression": {"r2": 0.7015035122652942, "mae": 9.270409471341736, "rmse": 12.386307397042188, "cv_r2": 0.6980922125543687, "cv_std": 0.003140949348067335}}}, "summer_system2": {"model": "GradientBoostingRegressor(max_depth=6, random_state=42)", "algorithm": "GradientBoosting", "metrics": {"r2": 0.9700175611556412, "mae": 2.3877945526913495, "rmse": 4.4355158484167045, "cv_r2": 0.9702819296271376, "cv_std": 0.004696111362617254}, "features": ["hour_sin", "hour_cos", "temperature", "cloud_cover", "ghi", "soc"], "target_achieved": "True", "training_samples": 31420, "benchmark_results": {"RandomForest": {"r2": 0.9690648114569286, "mae": 2.398383781057605, "rmse": 4.505438239244337, "cv_r2": 0.9694103690928306, "cv_std": 0.005204729477970698}, "GradientBoosting": {"r2": 0.9700175611556412, "mae": 2.3877945526913495, "rmse": 4.4355158484167045, "cv_r2": 0.9702819296271376, "cv_std": 0.004696111362617254}, "Ridge": {"r2": 0.8014042017386251, "mae": 8.026705263887722, "rmse": 11.41551962454466, "cv_r2": 0.797967131476233, "cv_std": 0.0019268251102871911}, "LinearRegression": {"r2": 0.8014040973939339, "mae": 8.026878366071562, "rmse": 11.415522623471938, "cv_r2": 0.7979671181644594, "cv_std": 0.0019266183750079053}}}, "autumn_system2": {"model": "GradientBoostingRegressor(max_depth=6, random_state=42)", "algorithm": "GradientBoosting", "metrics": {"r2": 0.8804471902807209, "mae": 3.65282917220834, "rmse": 6.107857980873652, "cv_r2": 0.8816220951747817, "cv_std": 0.007372055851117926}, "features": ["hour_sin", "hour_cos", "temperature", "cloud_cover", "ghi", "soc"], "target_achieved": "False", "training_samples": 24809, "benchmark_results": {"RandomForest": {"r2": 0.8757557615638197, "mae": 3.681164335586978, "rmse": 6.22654549791785, "cv_r2": 0.8739557598041557, "cv_std": 0.005792136810125522}, "GradientBoosting": {"r2": 0.8804471902807209, "mae": 3.65282917220834, "rmse": 6.107857980873652, "cv_r2": 0.8816220951747817, "cv_std": 0.007372055851117926}, "Ridge": {"r2": 0.6883264526625782, "mae": 7.339701316411448, "rmse": 9.861863990215292, "cv_r2": 0.6794911361270535, "cv_std": 0.004321377366281608}, "LinearRegression": {"r2": 0.6883273967951241, "mae": 7.339775501868652, "rmse": 9.861849053250179, "cv_r2": 0.6794911235976386, "cv_std": 0.004321877976359288}}}, "winter_system2": {"model": "GradientBoostingRegressor(max_depth=6, random_state=42)", "algorithm": "GradientBoosting", "metrics": {"r2": 0.8380001988536906, "mae": 2.5503364760919265, "rmse": 4.526166589651275, "cv_r2": 0.8369280238480139, "cv_std": 0.0039052134175788745}, "features": ["hour_sin", "hour_cos", "temperature", "cloud_cover", "ghi", "soc"], "target_achieved": "False", "training_samples": 25145, "benchmark_results": {"RandomForest": {"r2": 0.8324584553883583, "mae": 2.5462328252505118, "rmse": 4.602931918032886, "cv_r2": 0.8332707271953181, "cv_std": 0.0038695071507813283}, "GradientBoosting": {"r2": 0.8380001988536906, "mae": 2.5503364760919265, "rmse": 4.526166589651275, "cv_r2": 0.8369280238480139, "cv_std": 0.0039052134175788745}, "Ridge": {"r2": 0.6336886613917887, "mae": 5.013916108273311, "rmse": 6.806102333827115, "cv_r2": 0.6364802056117085, "cv_std": 0.004493296690033576}, "LinearRegression": {"r2": 0.633688558857814, "mae": 5.013968759414422, "rmse": 6.806103286372945, "cv_r2": 0.63648019727032, "cv_std": 0.0044932226800381185}}}}}, "overall_summary": {"total_successful": 16, "total_attempted": 16, "total_target_achieved": 3, "success_rate": 1.0, "target_achievement_rate": 0.1875, "training_duration_seconds": 354.987656}, "training_end": "2025-06-17T06:36:25.055956"}