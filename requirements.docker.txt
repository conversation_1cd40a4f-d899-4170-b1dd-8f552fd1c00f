# Solar Prediction Project - Docker Requirements
# Simplified requirements for Docker build

# Core Web Framework
fastapi>=0.104.0
uvicorn[standard]>=0.24.0

# Machine Learning
lightgbm>=4.6.0
xgboost>=3.0.0
scikit-learn>=1.3.0
joblib>=1.3.0

# Data Processing
pandas>=2.0.0
numpy>=1.24.0

# Database
asyncpg>=0.29.0
psycopg2-binary>=2.9.0
SQLAlchemy>=2.0.0

# Async & HTTP
aiohttp>=3.8.0
httpx>=0.25.0
redis>=4.5.0

# Telegram Bot
python-telegram-bot>=20.0

# Configuration & Environment
pydantic>=2.0.0
pydantic-settings>=2.0.0
python-dotenv>=1.0.0

# Utilities
loguru>=0.7.0
tenacity>=8.2.0
tqdm>=4.65.0

# Weather & API
requests>=2.31.0
cdsapi>=0.7.0

# Scientific Computing
scipy>=1.10.0
netCDF4>=1.6.0
xarray>=2023.1.0

# Development
autopep8>=2.0.0
