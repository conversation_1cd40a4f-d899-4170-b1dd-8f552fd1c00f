#!/usr/bin/env python3
"""
Docker Style Telegram Bot - EXACT COPY of Docker Container Bot
Simple 7-button menu with real production data integration
"""

import asyncio
import logging
import json
import requests
import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional, Any
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import (
    Application, CommandHandler, CallbackQueryHandler, 
    MessageHandler, filters, ContextTypes
)

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Configuration
BOT_TOKEN = "8060881816:AAFBhGADTAAcUkbom_FEFSkOHoT5N6t5png"
CHAT_ID = "1510889515"
API_BASE_URL = "http://localhost:8100"
BILLING_API_URL = "http://localhost:8110"

# Database configuration - Monolithic app compatible
DB_CONFIG = {
    'host': 'localhost',
    'port': 5433,
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

class ProductionDataService:
    """Production data service integration with PostgreSQL and APIs"""
    
    @staticmethod
    def get_db_connection():
        """Get database connection"""
        try:
            return psycopg2.connect(**DB_CONFIG)
        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            return None
    
    @staticmethod
    async def get_health() -> Dict:
        """Get system health from production API"""
        try:
            response = requests.get(f"{API_BASE_URL}/health", timeout=10)
            if response.status_code == 200:
                return response.json()
        except Exception as e:
            logger.error(f"Health check failed: {e}")
        return {"status": "error", "message": "API unavailable"}
    
    @staticmethod
    async def get_latest_solax_data() -> Dict:
        """Get latest solar data from production API"""
        try:
            response = requests.get(f"{API_BASE_URL}/api/v1/data/solax/latest", timeout=10)
            if response.status_code == 200:
                return response.json()
        except Exception as e:
            logger.error(f"Failed to get solar data: {e}")
        return {}
    
    @staticmethod
    async def get_weather_data() -> Dict:
        """Get latest weather data from production API"""
        try:
            response = requests.get(f"{API_BASE_URL}/api/v1/data/weather/latest", timeout=10)
            if response.status_code == 200:
                return response.json()
        except Exception as e:
            logger.error(f"Failed to get weather data: {e}")
        return {}
    
    @staticmethod
    async def get_model_info() -> Dict:
        """Get model information from production API"""
        try:
            response = requests.get(f"{API_BASE_URL}/api/v1/model/info", timeout=10)
            if response.status_code == 200:
                return response.json()
        except Exception as e:
            logger.error(f"Failed to get model info: {e}")
        return {}
    
    @staticmethod
    async def get_database_stats() -> Dict:
        """Get database statistics directly from PostgreSQL"""
        conn = ProductionDataService.get_db_connection()
        if not conn:
            return {"error": "Database connection failed"}
        
        try:
            cur = conn.cursor(cursor_factory=RealDictCursor)
            
            stats = {}
            
            # Check both systems
            systems = [
                ('solax_data', 'System 1 (Σπίτι Πάνω)'),
                ('solax_data2', 'System 2 (Σπίτι Κάτω)')
            ]
            
            for table_name, system_name in systems:
                try:
                    # Get latest record
                    cur.execute(f'''
                        SELECT timestamp, yield_today, ac_power, soc, bat_power, temperature
                        FROM {table_name} 
                        ORDER BY timestamp DESC 
                        LIMIT 1
                    ''')
                    latest = cur.fetchone()
                    
                    # Get record count
                    cur.execute(f'SELECT COUNT(*) as count FROM {table_name}')
                    count = cur.fetchone()["count"]
                    
                    if latest:
                        stats[table_name] = {
                            'system_name': system_name,
                            'latest_timestamp': str(latest["timestamp"]),
                            'yield_today': float(latest["yield_today"] or 0),
                            'ac_power': float(latest["ac_power"] or 0),
                            'soc': float(latest["soc"] or 0),
                            'bat_power': float(latest["bat_power"] or 0),
                            'temperature': float(latest["temperature"] or 0),
                            'total_records': count
                        }
                    else:
                        stats[table_name] = {
                            'system_name': system_name,
                            'error': 'No data found',
                            'total_records': count
                        }
                        
                except Exception as e:
                    stats[table_name] = {
                        'system_name': system_name,
                        'error': str(e)
                    }
            
            # Check weather data
            try:
                cur.execute('''
                    SELECT timestamp, temperature_2m, global_horizontal_irradiance, cloud_cover
                    FROM weather_data 
                    ORDER BY timestamp DESC 
                    LIMIT 1
                ''')
                weather = cur.fetchone()
                
                cur.execute('SELECT COUNT(*) as count FROM weather_data')
                weather_count = cur.fetchone()["count"]
                
                if weather:
                    stats['weather_data'] = {
                        'latest_timestamp': str(weather["timestamp"]),
                        'temperature_2m': float(weather["temperature_2m"] or 0),
                        'global_horizontal_irradiance': float(weather["global_horizontal_irradiance"] or 0),
                        'cloud_cover': float(weather["cloud_cover"] or 0),
                        'total_records': weather_count
                    }
                else:
                    stats['weather_data'] = {
                        'error': 'No weather data found',
                        'total_records': weather_count
                    }
                    
            except Exception as e:
                stats['weather_data'] = {'error': str(e)}
            
            conn.close()
            return stats
            
        except Exception as e:
            conn.close()
            return {"error": f"Database query failed: {e}"}
    
    @staticmethod
    async def make_prediction(data: Dict) -> Dict:
        """Make prediction using production API"""
        try:
            response = requests.post(f"{API_BASE_URL}/api/v1/predict", json=data, timeout=30)
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"Prediction failed: {response.status_code}"}
        except Exception as e:
            logger.error(f"Prediction failed: {e}")
            return {"error": str(e)}
    
    @staticmethod
    async def get_roi_data(system_id: str) -> Dict:
        """Get ROI data from Enhanced Billing API"""
        try:
            response = requests.get(f"{BILLING_API_URL}/api/v1/billing/{system_id}/roi", timeout=10)
            if response.status_code == 200:
                return response.json()
        except Exception as e:
            logger.error(f"Failed to get ROI data: {e}")
        return {}
    
    @staticmethod
    async def get_daily_cost(system_id: str) -> Dict:
        """Get daily cost from Enhanced Billing API"""
        try:
            response = requests.get(f"{BILLING_API_URL}/api/v1/billing/{system_id}/daily", timeout=10)
            if response.status_code == 200:
                return response.json()
        except Exception as e:
            logger.error(f"Failed to get daily cost: {e}")
        return {}
    
    @staticmethod
    async def get_tariffs() -> Dict:
        """Get tariffs from Enhanced Billing API"""
        try:
            response = requests.get(f"{BILLING_API_URL}/api/v1/tariffs/system1", timeout=10)
            if response.status_code == 200:
                return response.json()
        except Exception as e:
            logger.error(f"Failed to get tariffs: {e}")
        return {}

class DockerStyleTelegramBot:
    """EXACT COPY of Docker Container Telegram Bot - Simple 7-button menu"""
    
    def __init__(self):
        self.application = Application.builder().token(BOT_TOKEN).build()
        self.setup_handlers()
    
    def setup_handlers(self):
        """Setup command and callback handlers"""
        
        # Command handlers
        self.application.add_handler(CommandHandler("start", self.start_command))
        self.application.add_handler(CommandHandler("help", self.help_command))
        self.application.add_handler(CommandHandler("data", self.data_command))
        self.application.add_handler(CommandHandler("weather", self.weather_command))
        self.application.add_handler(CommandHandler("database", self.database_command))
        self.application.add_handler(CommandHandler("health", self.health_command))
        self.application.add_handler(CommandHandler("predict", self.predict_command))
        self.application.add_handler(CommandHandler("systems", self.systems_command))
        self.application.add_handler(CommandHandler("roi", self.roi_command))
        self.application.add_handler(CommandHandler("cost", self.cost_command))
        self.application.add_handler(CommandHandler("tariffs", self.tariffs_command))
        
        # Callback query handler for inline buttons
        self.application.add_handler(CallbackQueryHandler(self.button_callback))
        
        # Message handler for text messages
        self.application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_message))
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Start command handler - EXACT COPY of Docker bot"""
        
        keyboard = [
            [
                InlineKeyboardButton("📊 System Data", callback_data="data"),
                InlineKeyboardButton("🌤️ Weather", callback_data="weather")
            ],
            [
                InlineKeyboardButton("📈 Statistics", callback_data="database"),
                InlineKeyboardButton("🔧 Health", callback_data="health")
            ],
            [
                InlineKeyboardButton("🔮 Predictions", callback_data="predict"),
                InlineKeyboardButton("📈 ROI & Payback", callback_data="roi")
            ],
            [
                InlineKeyboardButton("💡 Daily Cost", callback_data="cost"),
                InlineKeyboardButton("⚙️ Tariffs", callback_data="tariffs")
            ],
            [
                InlineKeyboardButton("🌐 English", callback_data="english"),
                InlineKeyboardButton("ℹ️ Help", callback_data="help")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        welcome_message = """🌞 Καλώς ήρθατε στο Solar Bot!

Το πλήρες σύστημα διαχείρισης ηλιακής ενέργειας με ΠΡΑΓΜΑΤΙΚΑ ΔΕΔΟΜΕΝΑ!

🔥 Χαρακτηριστικά:
• Δεδομένα σε πραγματικό χρόνο από PostgreSQL
• Παρακολούθηση συστήματος (268,395+ εγγραφές)
• Καιρικές συνθήκες από APIs παραγωγής
• ML προβλέψεις και κατάσταση μοντέλων
• Οικονομική ανάλυση ROI & κόστους

📊 Πηγές Δεδομένων:
• PostgreSQL Database (solax_data, solax_data2, weather_data)
• Production API (localhost:8100)
• Enhanced Billing API (localhost:8110)

🏠 Συστήματα:
• Σύστημα 1: Σπίτι Πάνω (solax_data)
• Σύστημα 2: Σπίτι Κάτω (solax_data2)

Χρησιμοποιήστε τα κουμπιά παρακάτω:"""
        
        await update.message.reply_text(
            welcome_message,
            reply_markup=reply_markup
        )

    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Help command handler"""

        help_text = """🔧 **Εντολές Solar Bot**

**📊 Εντολές Δεδομένων:**
• `/data` - Τρέχοντα δεδομένα ηλιακού συστήματος
• `/weather` - Καιρικές συνθήκες
• `/database` - Στατιστικά βάσης δεδομένων
• `/systems` - Επισκόπηση ηλιακών συστημάτων
• `/health` - Κατάσταση Production API

**🤖 AI Εντολές:**
• `/predict` - ML πρόβλεψη με production μοντέλα

**💰 Οικονομικές Εντολές:**
• `/roi` - Ανάλυση ROI & Payback
• `/cost` - Ημερήσιο κόστος
• `/tariffs` - Τιμολόγια ενέργειας

**💡 Παραδείγματα:**
• `/data` - Τρέχουσα παραγωγή, ισχύς, SOC από DB
• `/weather` - Θερμοκρασία, GHI, νεφοκάλυψη
• `/database` - PostgreSQL stats και αριθμός εγγραφών

**🔥 Production Features:**
• 131,176+ εγγραφές (Σύστημα 1)
• 126,310+ εγγραφές (Σύστημα 2)
• Πραγματική PostgreSQL ενσωμάτωση
• Χωρίς mock δεδομένα - 100% παραγωγή
• Live API monitoring

Όλα τα δεδομένα προέρχονται από πραγματική βάση δεδομένων παραγωγής!"""

        await update.message.reply_text(help_text)

    async def data_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Data command handler"""

        try:
            solax_data = await ProductionDataService.get_latest_solax_data()

            if not solax_data:
                await update.message.reply_text("❌ Δεν υπάρχουν διαθέσιμα δεδομένα παραγωγής")
                return

            # Calculate data age
            data_timestamp = solax_data.get('timestamp', '')
            try:
                data_time = datetime.fromisoformat(data_timestamp.replace('Z', '+00:00'))
                age = datetime.now() - data_time.replace(tzinfo=None)
                age_str = f"{age.days} ημέρες, {age.seconds//3600} ώρες πριν"
            except:
                age_str = "Άγνωστο"

            data_message = f"""📊 **Πραγματικά Δεδομένα Ηλιακής Παραγωγής**

**🏠 Σύστημα:** {solax_data.get('system', 'Άγνωστο')}

**⚡ Δεδομένα Παραγωγής:**
• Παραγωγή Σήμερα: **{solax_data.get('yield_today', 0)} kWh**
• AC Ισχύς: {solax_data.get('ac_power', 0)} W

**🔋 Κατάσταση Μπαταρίας:**
• SOC: **{solax_data.get('soc', 0)}%**
• Ισχύς Μπαταρίας: {solax_data.get('bat_power', 0)} W

**🌡️ Περιβάλλον:**
• Θερμοκρασία: {solax_data.get('temperature', 0)}°C

**📅 Πληροφορίες Δεδομένων:**
• Χρονική Σήμανση: {data_timestamp}
• Ηλικία Δεδομένων: {age_str}
• Πηγή: PostgreSQL Database

**🔥 Αυτά είναι 100% ΠΡΑΓΜΑΤΙΚΑ δεδομένα παραγωγής!**"""

            keyboard = [
                [
                    InlineKeyboardButton("🔄 Ανανέωση", callback_data="data"),
                    InlineKeyboardButton("🗄️ Βάση Δεδομένων", callback_data="database")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                data_message,
                reply_markup=reply_markup
            )

        except Exception as e:
            await update.message.reply_text(f"❌ Σφάλμα λήψης δεδομένων παραγωγής: {e}")

    async def weather_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Weather command handler"""

        try:
            weather_data = await ProductionDataService.get_weather_data()

            if not weather_data:
                await update.message.reply_text("❌ Δεν υπάρχουν διαθέσιμα καιρικά δεδομένα")
                return

            weather_message = f"""🌤️ **Πραγματικά Καιρικά Δεδομένα**

**🌡️ Θερμοκρασία:** {weather_data.get('temperature', 'N/A')}°C
**☁️ Νεφοκάλυψη:** {weather_data.get('cloud_cover', 'N/A')}%
**💨 Ταχύτητα Ανέμου:** {weather_data.get('wind_speed', 'N/A')} km/h
**💧 Υγρασία:** {weather_data.get('humidity', 'N/A')}%

**📍 Τοποθεσία:** Μαραθώνας, Αττική, Ελλάδα
**📅 Χρόνος Δεδομένων:** {weather_data.get('timestamp', 'Άγνωστο')}

**📊 Πηγή:** Production Database & APIs
**✅ Κατάσταση:** {weather_data.get('status', 'Άγνωστο')}"""

            keyboard = [
                [
                    InlineKeyboardButton("🔄 Ανανέωση", callback_data="weather"),
                    InlineKeyboardButton("📊 Ηλιακά Δεδομένα", callback_data="data")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                weather_message,
                reply_markup=reply_markup
            )

        except Exception as e:
            await update.message.reply_text(f"❌ Σφάλμα λήψης καιρικών δεδομένων: {e}")

    async def database_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Database command handler"""

        try:
            db_stats = await ProductionDataService.get_database_stats()

            if "error" in db_stats:
                await update.message.reply_text(f"❌ Σφάλμα βάσης δεδομένων: {db_stats['error']}")
                return

            db_message = "🗄️ **Στατιστικά PostgreSQL Database**\n\n"

            # System data
            for table_name, stats in db_stats.items():
                if table_name.startswith('solax_data'):
                    if 'error' in stats:
                        db_message += f"**{stats['system_name']}:**\n❌ {stats['error']}\n\n"
                    else:
                        db_message += f"""**{stats['system_name']}:**
• Εγγραφές: {stats['total_records']:,}
• Τελευταία: {stats['latest_timestamp']}
• Παραγωγή: {stats['yield_today']} kWh
• SOC: {stats['soc']}%
• AC Ισχύς: {stats['ac_power']} W

"""

                elif table_name == 'weather_data':
                    if 'error' in stats:
                        db_message += f"**Καιρικά Δεδομένα:**\n❌ {stats['error']}\n\n"
                    else:
                        db_message += f"""**Καιρικά Δεδομένα:**
• Εγγραφές: {stats['total_records']:,}
• Τελευταία: {stats['latest_timestamp']}
• Θερμοκρασία: {stats['temperature_2m']}°C
• GHI: {stats['global_horizontal_irradiance']} W/m²

"""

            db_message += "**🔥 Όλα τα δεδομένα είναι ΠΡΑΓΜΑΤΙΚΑ δεδομένα παραγωγής από PostgreSQL!**"

            keyboard = [
                [
                    InlineKeyboardButton("🔄 Ανανέωση", callback_data="database"),
                    InlineKeyboardButton("📊 Live Δεδομένα", callback_data="data")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                db_message,
                reply_markup=reply_markup
            )

        except Exception as e:
            await update.message.reply_text(f"❌ Σφάλμα λήψης στατιστικών βάσης δεδομένων: {e}")

    async def health_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Health command handler"""

        try:
            health = await ProductionDataService.get_health()
            model_info = await ProductionDataService.get_model_info()

            health_message = f"""🔧 **Κατάσταση Production System**

**🔌 API Status:** {health.get('status', 'Άγνωστο')}
**📅 Timestamp:** {health.get('timestamp', 'Άγνωστο')}

**🗄️ Κατάσταση Υπηρεσιών:**
• Database: {health.get('services', {}).get('database', 'Άγνωστο')}
• Weather API: {health.get('services', {}).get('weather_api', 'Άγνωστο')}
• Background Tasks: {health.get('services', {}).get('background_tasks', False)}

**🤖 ML Model:**
• Model: {model_info.get('model_type', 'Άγνωστο')}
• Accuracy: {model_info.get('accuracy', 0):.1f}%
• Algorithm: {model_info.get('algorithm', 'Άγνωστο')}
• Features: {model_info.get('features_used', 0)}
• Loaded: {model_info.get('model_loaded', False)}

**🔥 Πηγή Δεδομένων:** Production PostgreSQL Database
**✅ Κατάσταση:** Όλα τα συστήματα λειτουργικά"""

            await update.message.reply_text(health_message)

        except Exception as e:
            await update.message.reply_text(f"❌ Σφάλμα λήψης κατάστασης υγείας: {e}")

    async def systems_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Systems command handler"""

        try:
            db_stats = await ProductionDataService.get_database_stats()

            systems_message = "🏠 **Επισκόπηση Ηλιακών Συστημάτων**\n\n"

            total_yield = 0
            total_records = 0

            for table_name, stats in db_stats.items():
                if table_name.startswith('solax_data'):
                    if 'error' not in stats:
                        total_yield += float(stats['yield_today']) if stats['yield_today'] is not None else 0.0
                        total_records += stats['total_records']

                        systems_message += f"""**{stats['system_name']}:**
📊 Πίνακας: {table_name}
📈 Εγγραφές: {stats['total_records']:,}
⚡ Σήμερα: {stats['yield_today']} kWh
🔋 SOC: {stats['soc']}%
🔌 Ισχύς: {stats['ac_power']} W
📅 Ενημέρωση: {stats['latest_timestamp']}

"""

            systems_message += f"""**📊 Συνδυασμένα Στατιστικά:**
• Συνολική Παραγωγή Σήμερα: {total_yield:.1f} kWh
• Συνολικές Εγγραφές: {total_records:,}
• Πηγή Δεδομένων: PostgreSQL Production DB
• Κατάσταση: Ενεργή παρακολούθηση σε πραγματικό χρόνο

**🎯 Όλα τα δεδομένα είναι 100% ΠΡΑΓΜΑΤΙΚΑ δεδομένα παραγωγής!**"""

            await update.message.reply_text(systems_message)

        except Exception as e:
            await update.message.reply_text(f"❌ Σφάλμα λήψης δεδομένων συστημάτων: {e}")

    async def predict_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Prediction command handler"""

        try:
            # Get current data for prediction context
            solax_data = await ProductionDataService.get_latest_solax_data()

            if not solax_data:
                await update.message.reply_text("❌ Δεν υπάρχουν διαθέσιμα δεδομένα για πρόβλεψη")
                return

            # Make prediction using production API
            prediction_data = {"system": solax_data.get('system', 'System 1')}
            prediction = await ProductionDataService.make_prediction(prediction_data)

            if "error" in prediction:
                predict_message = f"""🤖 **Κατάσταση ML Πρόβλεψης**

❌ **Σφάλμα Πρόβλεψης:** {prediction['error']}

**📊 Διαθέσιμα Δεδομένα:**
• Τρέχουσα Παραγωγή: {solax_data.get('yield_today', 0)} kWh
• Τρέχον SOC: {solax_data.get('soc', 0)}%
• Σύστημα: {solax_data.get('system', 'Άγνωστο')}

**🔧 Σημείωση:** Το μοντέλο μπορεί να χρειάζεται φόρτωση ή επανεκπαίδευση.
**📅 Πηγή Δεδομένων:** Production Database"""
            else:
                predict_message = f"""🤖 **ML Πρόβλεψη (Production Model)**

**📊 Δεδομένα Εισόδου:**
• Τρέχουσα Παραγωγή: {solax_data.get('yield_today', 0)} kWh
• Τρέχον SOC: {solax_data.get('soc', 0)}%
• Σύστημα: {solax_data.get('system', 'Άγνωστο')}

**🎯 Αποτελέσματα Πρόβλεψης:**
• Προβλεπόμενη Τιμή: {prediction.get('prediction', 'N/A')}
• Μοντέλο που Χρησιμοποιήθηκε: {prediction.get('model_used', 'Άγνωστο')}
• Κατάσταση: {prediction.get('status', 'Άγνωστο')}

**🔥 Πρόβλεψη βασισμένη σε 100% δεδομένα παραγωγής!**
**📅 Δημιουργήθηκε:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""

            await update.message.reply_text(predict_message)

        except Exception as e:
            await update.message.reply_text(f"❌ Σφάλμα δημιουργίας πρόβλεψης: {e}")

    async def roi_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """ROI command handler"""

        try:
            # Get ROI data for both systems
            system1_roi = await ProductionDataService.get_roi_data('system1')
            system2_roi = await ProductionDataService.get_roi_data('system2')

            roi_message = "📈 **Ανάλυση ROI & Payback**\n\n"

            if system1_roi:
                roi_message += f"""**🏠 Σπίτι Πάνω (System 1):**
• Επένδυση: €{system1_roi.get('investment_cost', 12500):,}
• Ετήσια Παραγωγή: {system1_roi.get('annual_production', 0):,.0f} kWh
• Ετήσια Εξοικονόμηση: €{system1_roi.get('annual_savings', 0):,.2f}
• Payback: {system1_roi.get('payback_years', 0):.1f} έτη
• ROI: {system1_roi.get('annual_roi', 0):.1f}%

"""

            if system2_roi:
                roi_message += f"""**🏠 Σπίτι Κάτω (System 2):**
• Επένδυση: €{system2_roi.get('investment_cost', 12500):,}
• Ετήσια Παραγωγή: {system2_roi.get('annual_production', 0):,.0f} kWh
• Ετήσια Εξοικονόμηση: €{system2_roi.get('annual_savings', 0):,.2f}
• Payback: {system2_roi.get('payback_years', 0):.1f} έτη
• ROI: {system2_roi.get('annual_roi', 0):.1f}%

"""

            if not system1_roi and not system2_roi:
                roi_message += "❌ Δεν υπάρχουν διαθέσιμα δεδομένα ROI"
            else:
                roi_message += "**📊 Πηγή:** Enhanced Billing API\n**🔥 Βασισμένο σε πραγματικά δεδομένα παραγωγής!**"

            await update.message.reply_text(roi_message)

        except Exception as e:
            await update.message.reply_text(f"❌ Σφάλμα λήψης δεδομένων ROI: {e}")

    async def cost_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Daily cost command handler"""

        try:
            # Get daily cost data for both systems
            system1_cost = await ProductionDataService.get_daily_cost('system1')
            system2_cost = await ProductionDataService.get_daily_cost('system2')

            cost_message = "💡 **Ημερήσιο Κόστος**\n\n"

            if system1_cost:
                cost_message += f"""**🏠 Σπίτι Πάνω (System 1):**
• Παραγωγή: {system1_cost.get('production_kwh', 0):.2f} kWh
• Κατανάλωση από Δίκτυο: {system1_cost.get('grid_consumption_kwh', 0):.2f} kWh
• Κόστος Δικτύου: €{system1_cost.get('grid_cost', 0):.2f}
• Εξοικονόμηση Παραγωγής: €{system1_cost.get('production_savings', 0):.2f}
• Καθαρή Εξοικονόμηση: €{system1_cost.get('net_savings', 0):.2f}

"""

            if system2_cost:
                cost_message += f"""**🏠 Σπίτι Κάτω (System 2):**
• Παραγωγή: {system2_cost.get('production_kwh', 0):.2f} kWh
• Κατανάλωση από Δίκτυο: {system2_cost.get('grid_consumption_kwh', 0):.2f} kWh
• Κόστος Δικτύου: €{system2_cost.get('grid_cost', 0):.2f}
• Εξοικονόμηση Παραγωγής: €{system2_cost.get('production_savings', 0):.2f}
• Καθαρή Εξοικονόμηση: €{system2_cost.get('net_savings', 0):.2f}

"""

            if not system1_cost and not system2_cost:
                cost_message += "❌ Δεν υπάρχουν διαθέσιμα δεδομένα κόστους"
            else:
                cost_message += "**📊 Πηγή:** Enhanced Billing API\n**🔥 Βασισμένο σε πραγματικά δεδομένα παραγωγής!"

            await update.message.reply_text(cost_message)

        except Exception as e:
            await update.message.reply_text(f"❌ Σφάλμα λήψης δεδομένων κόστους: {e}")

    async def tariffs_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Tariffs command handler"""

        try:
            tariffs = await ProductionDataService.get_tariffs()

            if not tariffs:
                await update.message.reply_text("❌ Δεν υπάρχουν διαθέσιμα δεδομένα τιμολογίων")
                return

            tariffs_message = f"""⚙️ **Τιμολόγια Ενέργειας**

**💡 Τιμές Ενέργειας:**
• Ημερήσια Τιμή: €{tariffs.get('day_rate', 0):.3f}/kWh
• Νυχτερινή Τιμή: €{tariffs.get('night_rate', 0):.3f}/kWh

**🔌 Χρεώσεις Δικτύου:**
• Βαθμίδα 1 (0-1,600 kWh): €{tariffs.get('network_tier1', 0):.4f}/kWh
• Βαθμίδα 2 (1,601-2,000 kWh): €{tariffs.get('network_tier2', 0):.4f}/kWh
• Βαθμίδα 3 (2,001+ kWh): €{tariffs.get('network_tier3', 0):.4f}/kWh

**📊 Πρόσθετες Χρεώσεις:**
• ETMEAR: €{tariffs.get('etmear', 0):.3f}/kWh
• ΦΠΑ: {tariffs.get('vat', 24)}%

**🔄 Net Metering:**
• Ποσοστό Συμψηφισμού: {tariffs.get('surplus_rate', 90)}%

**📊 Πηγή:** Enhanced Billing API
**🔥 Πραγματικά τιμολόγια παραγωγής!**"""

            await update.message.reply_text(tariffs_message)

        except Exception as e:
            await update.message.reply_text(f"❌ Σφάλμα λήψης δεδομένων τιμολογίων: {e}")

    async def button_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle inline button callbacks"""

        query = update.callback_query
        await query.answer()

        data = query.data

        # Create a fake update object for callback handlers
        # This is needed because callback queries don't have update.message
        class FakeMessage:
            def __init__(self, query):
                self.query = query

            async def reply_text(self, text, reply_markup=None, parse_mode=None):
                if reply_markup:
                    await self.query.edit_message_text(text, reply_markup=reply_markup, parse_mode=parse_mode)
                else:
                    await self.query.edit_message_text(text, parse_mode=parse_mode)

        # Create fake update for handlers
        fake_update = type('FakeUpdate', (), {})()
        fake_update.message = FakeMessage(query)
        fake_update.callback_query = query

        # Route callback to appropriate handler
        if data == "data":
            await self.data_command(fake_update, context)
        elif data == "weather":
            await self.weather_command(fake_update, context)
        elif data == "database":
            await self.database_command(fake_update, context)
        elif data == "health":
            await self.health_command(fake_update, context)
        elif data == "systems":
            await self.systems_command(fake_update, context)
        elif data == "predict":
            await self.predict_command(fake_update, context)
        elif data == "roi":
            await self.roi_command(fake_update, context)
        elif data == "cost":
            await self.cost_command(fake_update, context)
        elif data == "tariffs":
            await self.tariffs_command(fake_update, context)
        elif data == "help":
            await self.help_command(fake_update, context)
        elif data == "english":
            await query.edit_message_text("🌐 English language support coming soon! Currently supporting Greek.")
        else:
            await query.edit_message_text(f"🔧 Feature '{data}' - Production data integration ready")

    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle text messages"""

        text = update.message.text.lower()

        if "data" in text or "δεδομένα" in text:
            await self.data_command(update, context)
        elif "weather" in text or "καιρός" in text:
            await self.weather_command(update, context)
        elif "database" in text or "βάση" in text:
            await self.database_command(update, context)
        elif "health" in text or "υγεία" in text:
            await self.health_command(update, context)
        elif "systems" in text or "συστήματα" in text:
            await self.systems_command(update, context)
        elif "predict" in text or "πρόβλεψη" in text:
            await self.predict_command(update, context)
        elif "roi" in text or "επένδυση" in text:
            await self.roi_command(update, context)
        elif "cost" in text or "κόστος" in text:
            await self.cost_command(update, context)
        elif "tariffs" in text or "τιμολόγια" in text:
            await self.tariffs_command(update, context)
        else:
            await update.message.reply_text(
                "🤖 Καταλαβαίνω! Δοκιμάστε:\n• 'δεδομένα' για ηλιακά δεδομένα παραγωγής\n• 'καιρός' για συνθήκες\n• 'βάση' για PostgreSQL stats\n• 'συστήματα' για επισκόπηση\n• 'πρόβλεψη' για ML forecast\n• 'roi' για οικονομική ανάλυση\n• 'κόστος' για ημερήσιο κόστος\n• 'τιμολόγια' για τιμές ενέργειας\n\nΌλα βασισμένα σε ΠΡΑΓΜΑΤΙΚΑ δεδομένα παραγωγής! 🔥"
            )

    def run(self):
        """Run the bot"""
        logger.info("Starting Docker Style Telegram bot...")
        self.application.run_polling()

def main():
    """Main function"""

    print("🤖 DOCKER STYLE TELEGRAM BOT")
    print("="*60)
    print("🔄 EXACT COPY of Docker Container Bot - Simple 10-button menu")
    print()

    try:
        # Test database connection
        conn = ProductionDataService.get_db_connection()
        if conn:
            print("✅ Database connection successful")
            conn.close()
        else:
            print("❌ Database connection failed")
            return False

        # Test API connection
        import requests
        try:
            response = requests.get(f"{API_BASE_URL}/health", timeout=5)
            if response.status_code == 200:
                print("✅ Production API connection successful")
            else:
                print(f"⚠️ Production API returned status {response.status_code}")
        except:
            print("❌ Production API connection failed")

        # Test Enhanced Billing API connection
        try:
            response = requests.get(f"{BILLING_API_URL}/health", timeout=5)
            if response.status_code == 200:
                print("✅ Enhanced Billing API connection successful")
            else:
                print(f"⚠️ Enhanced Billing API returned status {response.status_code}")
        except:
            print("❌ Enhanced Billing API connection failed")

        bot_service = DockerStyleTelegramBot()

        print("✅ Bot initialized successfully")
        print(f"📱 Bot Token: {BOT_TOKEN[:20]}...")
        print(f"💬 Chat ID: {CHAT_ID}")
        print(f"🌐 API URL: {API_BASE_URL}")
        print(f"💰 Billing API URL: {BILLING_API_URL}")
        print(f"🗄️ Database: {DB_CONFIG['database']}@{DB_CONFIG['host']}:{DB_CONFIG['port']}")
        print("🔥 Data Source: PostgreSQL production database")
        print()
        print("🚀 Starting bot service...")

        bot_service.run()

    except Exception as e:
        print(f"❌ Bot service failed: {e}")
        logger.exception("Telegram bot service failed")
        return False

if __name__ == "__main__":
    main()
