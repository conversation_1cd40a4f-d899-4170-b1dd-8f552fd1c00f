#!/usr/bin/env python3
"""
Final Greek Translations Test
"""

import requests

BOT_TOKEN = "8060881816:AAFBhGADTAAcUkbom_FEFSkOHoT5N6t5png"
CHAT_ID = "1510889515"

def send_final_test():
    """Send final test message"""
    url = f"https://api.telegram.org/bot{BOT_TOKEN}/sendMessage"
    
    payload = {
        "chat_id": CHAT_ID,
        "text": """🎯 ΕΛΛΗΝΙΚΑ ΜΕΝΟΥ ΔΙΟΡΘΩΘΗΚΑΝ!

✅ Διορθώσεις:
• Προστέθηκαν ελληνικά translations για ROI
• Διορθώθηκε το ROI message να χρησιμοποιεί get_text()
• Προστέθηκαν όλα τα λείποντα translations

📊 Αναμενόμενα αποτελέσματα:
• ROI επιλογή 1 (Σπίτι Πάνω): ✅ Ελληνικά
• ROI επιλογή 2 (Σπίτ<PERSON> Κάτω): ✅ Ελληνικά  
• ROI επιλογή 3 (Συνολικό): ✅ Ελληνικά

🇬🇷 Νέα ελληνικά translations:
• Οικονομική Απόδοση
• Ανάλυση Παραγωγής
• Ετήσια εξοικονόμηση
• Κόστος επένδυσης
• Οικονομικό Όφελος
• Σύνοψη Επένδυσης

🚀 Δοκιμάστε τώρα τα ROI μενού στα ελληνικά!""",
        "parse_mode": "Markdown"
    }
    
    try:
        response = requests.post(url, json=payload, timeout=10)
        if response.status_code == 200:
            print("✅ Final Greek translations test message sent successfully!")
            print("🇬🇷 Greek translations added for ROI menus!")
            return True
        else:
            print(f"❌ Failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    send_final_test()
