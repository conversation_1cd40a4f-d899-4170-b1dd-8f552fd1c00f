#!/usr/bin/env python3
"""
Enhanced Billing Service - CORRECTED VERSION
Uses UnifiedROICalculator with correct consumption rates from documentation
"""
import asyncio
import json
import os
import time
import logging
from datetime import datetime, timedelta, date
from typing import Dict, List, Any, Optional

import uvicorn
import requests
from fastapi import FastAPI, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import UnifiedROICalculator (CORRECTED)
UNIFIED_ROI_AVAILABLE = False
try:
    import sys
    sys.path.append('/app/scripts/frontend_system')
    from unified_roi_calculator import UnifiedROICalculator
    UNIFIED_ROI_AVAILABLE = True
    logger.info("✅ UnifiedROICalculator available")
except ImportError as e:
    logger.warning(f"⚠️ UnifiedROICalculator not available: {e}")
    logger.info("   Using fallback calculations")

# FastAPI app
app = FastAPI(
    title="Enhanced Billing Service - Corrected",
    description="Advanced billing calculations using UnifiedROICalculator",
    version="2.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# CORRECT consumption rates from documentation
SYSTEM_RATES = {
    'system1': {
        'self_consumption_rate': 0.2992,  # 29.92%
        'surplus_rate': 0.7008,           # 70.08%
        'grid_import_rate': 0.0548        # 5.48%
    },
    'system2': {
        'self_consumption_rate': 0.3203,  # 32.03%
        'surplus_rate': 0.6797,           # 67.97%
        'grid_import_rate': 0.2161        # 21.61%
    }
}

# CORRECT Greek tariff rates from documentation
class TariffRates:
    """Greek electricity tariff rates - CORRECTED"""
    def __init__(self):
        self.day_energy = 0.1420  # €/kWh day rate (from docs)
        self.night_energy = 0.1320  # €/kWh night rate (from docs)
        self.network_tier1 = 0.0500  # €/kWh (0-1600 kWh/year)
        self.network_tier2 = 0.0600  # €/kWh (1601-2000 kWh/year)
        self.network_tier3 = 0.0700  # €/kWh (2001+ kWh/year)
        self.etmear = 0.0250  # €/kWh ETMEAR charge
        self.feed_in_tariff = 0.0000  # €/kWh (Net Metering Greece - no payment)
        self.monthly_fixed = 8.50  # €/month network connection fee

class BillingService:
    def __init__(self):
        self.rates = TariffRates()
        
    async def get_system_balance_unified(self, system_id: str, target_date: date) -> Dict:
        """Get energy balance using UnifiedROICalculator approach"""
        try:
            import psycopg2
            from psycopg2.extras import RealDictCursor
            
            # Database config for Docker environment
            DB_CONFIG = {
                'host': 'postgres',
                'database': 'solar_prediction',
                'user': 'postgres',
                'password': 'postgres'
            }
            
            conn = psycopg2.connect(**DB_CONFIG)
            cur = conn.cursor(cursor_factory=RealDictCursor)
            
            # Get table name based on system
            table_name = 'solax_data' if system_id == 'system1' else 'solax_data2'
            
            # Get today's production
            cur.execute(f"""
                SELECT 
                    MAX(yield_today) as production_today
                FROM {table_name}
                WHERE DATE(timestamp) = %s
            """, (target_date,))
            
            result = cur.fetchone()
            production = float(result['production_today'] or 0)
            
            # Use CORRECT consumption rates from documentation
            rates = SYSTEM_RATES[system_id]
            
            # Calculate consumption components using CORRECT rates
            self_consumption = production * rates['self_consumption_rate']
            surplus = production * rates['surplus_rate']
            grid_import = production * rates['grid_import_rate']
            
            conn.close()
            
            return {
                "status": "calculated",
                "date": str(target_date),
                "system_id": system_id,
                "production": round(production, 2),
                "self_consumption": round(self_consumption, 2),
                "surplus": round(surplus, 2),
                "grid_import": round(grid_import, 2),
                "consumption_rates": rates
            }
                
        except Exception as e:
            logger.error(f"Database error in get_system_balance_unified: {e}")
            return {
                "status": "error",
                "date": str(target_date),
                "system_id": system_id,
                "error": str(e)
            }

    async def calculate_daily_cost_unified(self, system_id: str, target_date: date) -> Dict:
        """Calculate daily cost using CORRECT Net Metering logic"""
        try:
            # Get energy balance
            balance = await self.get_system_balance_unified(system_id, target_date)
            
            if balance.get('status') == 'error':
                raise Exception(balance.get('error', 'Unknown error'))
            
            production = balance['production']
            self_consumption = balance['self_consumption']
            surplus = balance['surplus']
            grid_import = balance['grid_import']
            
            # CORRECT Net Metering Logic for Greece
            # Energy cost: Only for grid import (what we buy from grid)
            energy_cost = grid_import * self.rates.day_energy
            
            # Network charges: For grid import (tiered system)
            network_cost = grid_import * self.rates.network_tier1  # Use tier 1 for daily calc
            
            # ETMEAR charge: For grid import
            etmear_cost = grid_import * self.rates.etmear
            
            # Surplus value: €0.00 in Greece (Net Metering - no direct payment)
            surplus_value = surplus * self.rates.feed_in_tariff  # = 0.00
            
            # Total cost calculation
            total_cost = energy_cost + network_cost + etmear_cost
            net_cost = total_cost - surplus_value  # surplus_value = 0, so net_cost = total_cost
            
            return {
                "status": "success",
                "system_id": system_id,
                "date": str(target_date),
                "cost_breakdown": {
                    "energy_cost": round(energy_cost, 3),
                    "network_cost": round(network_cost, 3),
                    "etmear_cost": round(etmear_cost, 3),
                    "total_cost": round(total_cost, 3),
                    "surplus_value": round(surplus_value, 3),  # Always 0.00
                    "net_cost": round(net_cost, 3)
                },
                "energy_data": {
                    "production": production,
                    "self_consumption": self_consumption,
                    "surplus": surplus,
                    "grid_import": grid_import
                },
                "tariff_info": {
                    "period": "summer" if 4 <= target_date.month <= 9 else "winter",
                    "network_tier": self.rates.network_tier1,
                    "feed_in_tariff": self.rates.feed_in_tariff
                }
            }
            
        except Exception as e:
            logger.error(f"Error calculating daily cost: {e}")
            raise HTTPException(status_code=500, detail=str(e))

# Initialize service
billing_service = BillingService()

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "service": "Enhanced Billing Service - Corrected",
        "version": "2.0.0",
        "unified_roi_available": UNIFIED_ROI_AVAILABLE,
        "correct_rates": SYSTEM_RATES,
        "endpoints": [
            "/billing/enhanced/cost/{system_id}",
            "/billing/enhanced/roi/{system_id}",
            "/billing/enhanced/tariffs",
            "/health"
        ]
    }

@app.get("/billing/enhanced/cost/{system_id}")
async def get_enhanced_daily_cost(system_id: str, date: str = Query(None)):
    """Get enhanced daily cost calculation using CORRECT rates"""
    try:
        if system_id not in ['system1', 'system2', '1', '2']:
            raise HTTPException(status_code=400, detail="Invalid system_id")

        # Normalize system_id
        if system_id in ['1', 'system1']:
            system_id = 'system1'
        else:
            system_id = 'system2'

        target_date = datetime.strptime(date, "%Y-%m-%d").date() if date else datetime.now().date()

        # Calculate daily cost using CORRECTED method
        result = await billing_service.calculate_daily_cost_unified(system_id, target_date)
        return result

    except Exception as e:
        logger.error(f"Failed to calculate daily cost: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to calculate daily cost: {e}")

    async def calculate_roi_corrected(self, system_id: str) -> Dict:
        """Calculate ROI using CORRECT rates from documentation"""
        try:
            import psycopg2
            from psycopg2.extras import RealDictCursor

            # Database config for Docker environment
            DB_CONFIG = {
                'host': 'postgres',
                'database': 'solar_prediction',
                'user': 'postgres',
                'password': 'postgres'
            }

            conn = psycopg2.connect(**DB_CONFIG)
            cur = conn.cursor(cursor_factory=RealDictCursor)

            # Get table name based on system
            table_name = 'solax_data' if system_id == 'system1' else 'solax_data2'

            # Get total production from database
            cur.execute(f"""
                SELECT
                    SUM(CASE WHEN yield_today > 0 THEN yield_today ELSE 0 END) as total_production,
                    COUNT(DISTINCT DATE(timestamp)) as total_days,
                    MIN(timestamp) as start_date,
                    MAX(timestamp) as end_date
                FROM {table_name}
                WHERE timestamp >= '2024-01-01'
            """)

            result = cur.fetchone()
            total_production = float(result['total_production'] or 0)
            total_days = int(result['total_days'] or 1)

            conn.close()

            # Use CORRECT consumption rates from documentation
            rates = SYSTEM_RATES[system_id]
            investment_cost = 12500  # €

            # Calculate annual production
            annual_production = (total_production / total_days) * 365 if total_days > 0 else 0

            # Calculate consumption components using CORRECT rates
            annual_self_consumption = annual_production * rates['self_consumption_rate']
            annual_surplus = annual_production * rates['surplus_rate']
            annual_grid_import = annual_production * rates['grid_import_rate']

            # Financial calculations using CORRECT Greek tariffs
            # Energy savings from self-consumption
            energy_savings = annual_self_consumption * self.rates.day_energy

            # Grid costs (what we pay for grid import)
            grid_energy_cost = annual_grid_import * self.rates.day_energy
            grid_network_cost = annual_grid_import * self.rates.network_tier1
            grid_etmear_cost = annual_grid_import * self.rates.etmear
            total_grid_cost = grid_energy_cost + grid_network_cost + grid_etmear_cost

            # Surplus income: €0.00 in Greece (Net Metering)
            surplus_income = annual_surplus * self.rates.feed_in_tariff  # = 0.00

            # Net annual benefit
            annual_benefit = energy_savings + surplus_income - total_grid_cost

            # ROI calculations
            annual_roi_percent = (annual_benefit / investment_cost) * 100 if investment_cost > 0 else 0
            payback_years = investment_cost / annual_benefit if annual_benefit > 0 else None

            return {
                "system_id": system_id,
                "investment_cost_eur": investment_cost,
                "production": {
                    "total_production_kwh": total_production,
                    "annual_production_kwh": annual_production,
                    "total_days": total_days
                },
                "consumption_analysis": {
                    "self_consumption_rate": rates['self_consumption_rate'] * 100,
                    "surplus_rate": rates['surplus_rate'] * 100,
                    "grid_import_rate": rates['grid_import_rate'] * 100,
                    "annual_self_consumption_kwh": annual_self_consumption,
                    "annual_surplus_kwh": annual_surplus,
                    "annual_grid_import_kwh": annual_grid_import
                },
                "financial": {
                    "energy_savings_eur": round(energy_savings, 2),
                    "grid_costs_eur": round(total_grid_cost, 2),
                    "surplus_income_eur": round(surplus_income, 2),
                    "annual_benefit_eur": round(annual_benefit, 2),
                    "annual_roi_percent": round(annual_roi_percent, 1),
                    "payback_years": round(payback_years, 1) if payback_years else None,
                    "annual_savings_eur": round(annual_benefit, 2)
                },
                "status": "calculated",
                "calculation_method": "corrected_documentation_rates",
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error calculating ROI: {e}")
            raise Exception(f"ROI calculation failed: {e}")

@app.get("/billing/enhanced/roi/{system_id}")
async def get_enhanced_roi_analysis(system_id: str):
    """Get ROI analysis using CORRECTED rates from documentation"""
    try:
        if system_id not in ['system1', 'system2']:
            raise HTTPException(status_code=400, detail="Invalid system_id")

        # Use CORRECTED calculation method
        roi_summary = await billing_service.calculate_roi_corrected(system_id)
        return roi_summary

    except Exception as e:
        logger.error(f"Enhanced ROI calculation failed: {e}")
        raise HTTPException(status_code=500, detail=f"Enhanced ROI calculation failed: {e}")

@app.get("/billing/enhanced/tariffs")
async def get_enhanced_tariffs():
    """Get current tariff configuration"""
    try:
        return {
            "status": "success",
            "tariffs": {
                "energy_rates": {
                    "day_rate": billing_service.rates.day_energy,
                    "night_rate": billing_service.rates.night_energy,
                    "unit": "€/kWh"
                },
                "network_charges": {
                    "tier1_0_1600": billing_service.rates.network_tier1,
                    "tier2_1601_2000": billing_service.rates.network_tier2,
                    "tier3_2001_plus": billing_service.rates.network_tier3,
                    "unit": "€/kWh"
                },
                "additional_charges": {
                    "etmear": billing_service.rates.etmear,
                    "monthly_fixed": billing_service.rates.monthly_fixed,
                    "unit": "€/kWh or €/month"
                },
                "net_metering": {
                    "feed_in_tariff": billing_service.rates.feed_in_tariff,
                    "description": "Net Metering Greece - no direct payment"
                }
            },
            "consumption_rates": SYSTEM_RATES,
            "last_updated": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get tariffs: {e}")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "enhanced_billing_service_corrected",
        "unified_roi_available": UNIFIED_ROI_AVAILABLE,
        "correct_consumption_rates": SYSTEM_RATES,
        "timestamp": datetime.now().isoformat()
    }

if __name__ == "__main__":
    port = int(os.getenv("BILLING_PORT", 8110))
    print(f"🚀 Starting Enhanced Billing Service (Corrected) on port {port}...")
    uvicorn.run(
        "enhanced_billing_service:app",
        host="0.0.0.0",
        port=port,
        reload=False,
        log_level="info"
    )
