#!/usr/bin/env python3
"""
Prediction Scheduler Service for Docker Container
Wrapper for the Prediction Scheduler
"""

import os
import sys
import asyncio
import logging
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """Main entry point for Prediction Scheduler service"""
    try:
        port = int(os.getenv('SERVICE_PORT', 8106))
        logger.info(f"📅 Starting Prediction Scheduler Service on port {port}...")

        # Import and run the Prediction Scheduler
        from scripts.production.prediction_scheduler import main as scheduler_main

        # Run the scheduler
        scheduler_main()

    except Exception as e:
        logger.error(f"❌ Failed to start Prediction Scheduler: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
