#!/usr/bin/env python3
"""
Final Fix Test - All Issues Resolved
"""

import requests

BOT_TOKEN = "8060881816:AAFBhGADTAAcUkbom_FEFSkOHoT5N6t5png"
CHAT_ID = "1510889515"

def send_final_test():
    """Send final test message"""
    url = f"https://api.telegram.org/bot{BOT_TOKEN}/sendMessage"
    
    payload = {
        "chat_id": CHAT_ID,
        "text": """🎉 ΌΛΕΣ ΟΙ ΔΙΟΡΘΏΣΕΙΣ ΟΛΟΚΛΗΡΏΘΗΚΑΝ!

✅ **Διορθώσεις που ολοκληρώθηκαν:**

1. 🕐 **Timezone Mixing**: Διορθώθηκε offset-naïve vs offset-aware
2. 📱 **ROI Messages**: Αφαιρέθηκε "Εξαιρετική επένδυση"
3. 🌤️ **Data Timestamps**: Τώρα σε EEST (όχι 3 ώρες πίσω)
4. 🔧 **Health Status**: Αφαιρέθηκαν περιττά μηνύματα
5. 💰 **Daily Cost**: ΣΩΣΤΟΣ υπολογισμός με Net Metering

🔥 **Net Metering Logic:**
• Αν έχετε πλεόνασμα → Κόστος ενέργειας = 0€
• Μόνο network charges για grid usage
• Surplus value από feed-in tariff

📊 **Τώρα τα Daily Cost θα δείχνουν:**
• Κόστος ενέργειας: 0€ (με πλεόνασμα)
• Κόστος δικτύου: Μόνο για grid usage
• Έσοδα πλεονάσματος: Από feed-in tariff

🇬🇷 Όλα τα timestamps τώρα σε ελληνική ώρα!"""
    }
    
    try:
        response = requests.post(url, json=payload, timeout=10)
        if response.status_code == 200:
            print("✅ Final fix test message sent successfully!")
            return True
        else:
            print(f"❌ Failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    send_final_test()
