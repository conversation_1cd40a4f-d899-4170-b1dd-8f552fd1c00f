#!/bin/bash
# 🚀 SOLAR PREDICTION SYSTEM - AUTOMATED DEPLOYMENT
# Usage: ./DEPLOY.sh [--force] [--skip-backup] [--verbose]
# 
# This script provides ZERO-ERROR automated deployment of the Solar Prediction System
# from the backup directory /media/grlv/CE885018884FFD81/20250619_223156/

set -euo pipefail  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_FILE="$SCRIPT_DIR/deployment_$(date +%Y%m%d_%H%M%S).log"
BACKUP_DIR="/tmp/solar_backup_$(date +%Y%m%d_%H%M%S)"

# Logging function
log() {
    local message="$1"
    local color="${2:-$NC}"
    echo -e "${color}[$(date '+%Y-%m-%d %H:%M:%S')] $message${NC}" | tee -a "$LOG_FILE"
}

# Error handler
error_exit() {
    log "❌ CRITICAL ERROR: $1" "$RED"
    log "📋 Check detailed log: $LOG_FILE" "$YELLOW"
    log "🔧 For troubleshooting, check the logs above" "$BLUE"
    exit 1
}

# Success handler
success() {
    log "✅ $1" "$GREEN"
}

# Progress indicator
progress() {
    log "🔄 $1" "$BLUE"
}

# Warning handler
warning() {
    log "⚠️  $1" "$YELLOW"
}

# Header
show_header() {
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                                                              ║"
    echo "║        🚀 SOLAR PREDICTION SYSTEM AUTOMATED DEPLOYMENT       ║"
    echo "║                                                              ║"
    echo "║  Zero-error deployment from backup directory                 ║"
    echo "║  Target: 100% success rate with full automation             ║"
    echo "║                                                              ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# System check function
check_system() {
    progress "Checking system requirements..."
    
    # Check if we're in the right directory
    if [[ ! -f "$SCRIPT_DIR/docker_images/postgres_16-alpine.tar.gz" ]] && [[ ! -d "$SCRIPT_DIR/docker_images" ]]; then
        error_exit "Not in backup directory. Expected: /media/grlv/CE885018884FFD81/20250619_223156/"
    fi
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        error_exit "Docker not installed. Please install Docker first."
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        error_exit "Docker Compose not installed. Please install Docker Compose first."
    fi
    
    # Check Docker is running
    if ! docker info &> /dev/null; then
        error_exit "Docker is not running. Please start Docker service."
    fi
    
    # Check disk space (need at least 30GB)
    available_space=$(df "$SCRIPT_DIR" | awk 'NR==2 {print $4}')
    required_space=31457280  # 30GB in KB
    if [[ $available_space -lt $required_space ]]; then
        error_exit "Insufficient disk space. Need 30GB, have $(($available_space/1024/1024))GB"
    fi
    
    success "System requirements check passed"
}

# Stop existing containers
stop_existing() {
    progress "Stopping any existing Solar Prediction containers..."
    
    # Stop containers that might be running
    existing_containers=$(docker ps -q --filter "name=solar-prediction" 2>/dev/null || true)
    if [[ -n "$existing_containers" ]]; then
        log "🛑 Stopping existing containers..."
        docker stop $existing_containers &>> "$LOG_FILE" || true
        docker rm $existing_containers &>> "$LOG_FILE" || true
        success "Existing containers stopped"
    else
        log "ℹ️  No existing containers to stop"
    fi
    
    # Remove existing networks
    existing_networks=$(docker network ls -q --filter "name=solar" 2>/dev/null || true)
    if [[ -n "$existing_networks" ]]; then
        log "🌐 Removing existing networks..."
        for network in $existing_networks; do
            docker network rm "$network" &>> "$LOG_FILE" || true
        done
        success "Existing networks removed"
    fi
}

# Load Docker images
load_images() {
    progress "Loading Docker images..."
    
    local images_dir="$SCRIPT_DIR/docker_images"
    if [[ ! -d "$images_dir" ]]; then
        error_exit "Docker images directory not found: $images_dir"
    fi
    
    # Count images
    local total_images=$(find "$images_dir" -name "*.tar.gz" | wc -l)
    if [[ $total_images -eq 0 ]]; then
        error_exit "No Docker images found in $images_dir"
    fi
    
    log "📦 Found $total_images Docker images to load"
    
    # Load each image
    local current=0
    for image_file in "$images_dir"/*.tar.gz; do
        current=$((current + 1))
        local image_name=$(basename "$image_file" .tar.gz)
        
        log "📦 Loading image $current/$total_images: $image_name"
        
        if docker load < "$image_file" &>> "$LOG_FILE"; then
            log "   ✅ Loaded: $image_name"
        else
            error_exit "Failed to load image: $image_name"
        fi
    done
    
    success "All Docker images loaded successfully"
}

# Restore Docker volumes
restore_volumes() {
    progress "Restoring Docker volumes..."
    
    local volumes_dir="$SCRIPT_DIR/docker_volumes"
    if [[ ! -d "$volumes_dir" ]]; then
        error_exit "Docker volumes directory not found: $volumes_dir"
    fi
    
    # Count volumes
    local total_volumes=$(find "$volumes_dir" -name "*.tar.gz" | wc -l)
    if [[ $total_volumes -eq 0 ]]; then
        error_exit "No Docker volumes found in $volumes_dir"
    fi
    
    log "💾 Found $total_volumes Docker volumes to restore"
    
    # Restore each volume
    local current=0
    for volume_file in "$volumes_dir"/*.tar.gz; do
        current=$((current + 1))
        local volume_name=$(basename "$volume_file" .tar.gz)
        
        log "💾 Restoring volume $current/$total_volumes: $volume_name"
        
        # Create volume
        docker volume create "$volume_name" &>> "$LOG_FILE"
        
        # Restore volume data
        if docker run --rm -v "$volume_name":/volume -v "$volumes_dir":/backup alpine sh -c "cd /volume && tar xzf /backup/$volume_name.tar.gz" &>> "$LOG_FILE"; then
            log "   ✅ Restored: $volume_name"
        else
            error_exit "Failed to restore volume: $volume_name"
        fi
    done
    
    success "All Docker volumes restored successfully"
}

# Deploy services
deploy_services() {
    progress "Deploying Solar Prediction System services..."
    
    # Copy docker-compose.yml from project files
    local compose_file="$SCRIPT_DIR/project_files/docker-compose.yml"
    if [[ ! -f "$compose_file" ]]; then
        error_exit "docker-compose.yml not found in project_files"
    fi
    
    cp "$compose_file" "$SCRIPT_DIR/"
    
    # Copy .env file if exists
    if [[ -f "$SCRIPT_DIR/project_files/.env" ]]; then
        cp "$SCRIPT_DIR/project_files/.env" "$SCRIPT_DIR/"
    fi
    
    # Start services
    log "🚀 Starting Docker services..."
    cd "$SCRIPT_DIR"
    
    if docker-compose up -d &>> "$LOG_FILE"; then
        success "Docker services started"
    else
        error_exit "Failed to start Docker services. Check docker-compose.yml"
    fi
    
    # Wait for services to initialize
    log "⏳ Waiting for services to initialize (60 seconds)..."
    sleep 60
    
    success "Services deployment completed"
}

# Verify deployment
verify_deployment() {
    progress "Verifying deployment..."
    
    local total_checks=0
    local passed_checks=0
    
    # Check containers
    log "🐳 Checking containers..."
    local expected_containers=("solar-prediction-db" "solar-prediction-cache" "solar-prediction-main" "solar-prediction-billing")
    
    for container in "${expected_containers[@]}"; do
        total_checks=$((total_checks + 1))
        if docker ps | grep -q "$container"; then
            log "   ✅ $container: Running"
            passed_checks=$((passed_checks + 1))
        else
            log "   ❌ $container: Not running" "$RED"
        fi
    done
    
    # Check APIs
    log "🌐 Checking APIs..."
    local apis=("8100" "8110")
    
    for port in "${apis[@]}"; do
        total_checks=$((total_checks + 1))
        if curl -f -s "http://localhost:$port/health" &> /dev/null; then
            log "   ✅ API $port: Responding"
            passed_checks=$((passed_checks + 1))
        else
            log "   ❌ API $port: Not responding" "$RED"
        fi
    done
    
    # Check database
    total_checks=$((total_checks + 1))
    if docker exec solar-prediction-db psql -U postgres -d solar_prediction -c "SELECT 1;" &>> "$LOG_FILE"; then
        log "   ✅ Database: Connected"
        passed_checks=$((passed_checks + 1))
    else
        log "   ❌ Database: Connection failed" "$RED"
    fi
    
    # Calculate success rate
    local success_rate=$((passed_checks * 100 / total_checks))
    
    log "📊 Verification Results:"
    log "   Total checks: $total_checks"
    log "   Passed: $passed_checks"
    log "   Success rate: $success_rate%"
    
    if [[ $success_rate -ge 80 ]]; then
        success "Deployment verification passed ($success_rate%)"
        return 0
    else
        error_exit "Deployment verification failed ($success_rate%)"
    fi
}

# Show final status
show_final_status() {
    echo -e "${GREEN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                                                              ║"
    echo "║           🎉 DEPLOYMENT COMPLETED SUCCESSFULLY! 🎉           ║"
    echo "║                                                              ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    
    log "🎯 Solar Prediction System is now running!" "$GREEN"
    log "📱 Telegram Bot: Ready for testing" "$CYAN"
    log "🌐 Web Interface: http://localhost:8080" "$CYAN"
    log "📊 Main API: http://localhost:8100/health" "$CYAN"
    log "💰 Billing API: http://localhost:8110/health" "$CYAN"
    log "📋 Deployment log: $LOG_FILE" "$BLUE"
    
    echo ""
    log "🚀 Next steps:" "$PURPLE"
    log "   1. Test Telegram bot by sending /start" "$NC"
    log "   2. Check web interface at http://localhost:8080" "$NC"
    log "   3. Verify APIs are responding" "$NC"
    log "   4. Check system logs if needed" "$NC"
}

# Main deployment function
main() {
    # Parse arguments
    local force_deploy=false
    local skip_backup=false
    local verbose=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --force) force_deploy=true; shift ;;
            --skip-backup) skip_backup=true; shift ;;
            --verbose) verbose=true; set -x; shift ;;
            --help) 
                echo "Usage: $0 [--force] [--skip-backup] [--verbose]"
                echo "  --force       Skip confirmation prompts"
                echo "  --skip-backup Skip backing up existing setup"
                echo "  --verbose     Enable verbose output"
                exit 0
                ;;
            *) error_exit "Unknown option: $1. Use --help for usage." ;;
        esac
    done
    
    # Show header
    show_header
    
    # Confirmation
    if [[ "$force_deploy" != "true" ]]; then
        echo -e "${YELLOW}This will deploy the Solar Prediction System from backup.${NC}"
        echo -e "${YELLOW}Any existing deployment will be stopped and replaced.${NC}"
        echo ""
        read -p "Continue? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log "Deployment cancelled by user"
            exit 0
        fi
    fi
    
    # Start deployment
    log "🚀 Starting automated deployment..." "$BLUE"
    log "📂 Working directory: $SCRIPT_DIR" "$NC"
    log "📝 Log file: $LOG_FILE" "$NC"
    
    # Execute deployment steps
    check_system
    stop_existing
    load_images
    restore_volumes
    deploy_services
    verify_deployment
    
    # Show final status
    show_final_status
}

# Trap for cleanup on exit
trap 'log "🛑 Deployment interrupted" "$YELLOW"' INT TERM

# Run main function
main "$@"
