# Solar Prediction System - Windows Installation Guide

## 🚀 Γρήγορη Εγκατάσταση

### Βήμα 1: Εγ<PERSON><PERSON>τάσταση Docker Desktop
1. Κατεβάστε το Docker Desktop από: https://docs.docker.com/desktop/windows/
2. Εκτελέστε το installer
3. Επανεκκινήστε τον υπολογιστή αν χρειάζεται
4. Ξεκινήστε το Docker Desktop

### Βήμα 2: Εκτέλεση Script Εγκατάστασης

#### Επιλογή A: PowerShell (Συνιστάται)
```powershell
# Κλικ δεξί στο PowerShell -> "Run as Administrator"
.\install_solar_docker.ps1
```

#### Επιλογή B: Command Prompt
```cmd
install_solar_docker.bat
```

### Βήμα 3: Πρόσβαση στο Σύστημα
- **Web Interface**: http://localhost:8100
- **API Docs**: http://localhost:8100/docs

## 📋 Λεπτομερείς Οδηγίες

### Απαιτήσεις Συστήματος

#### Ελάχιστες Απαιτήσεις
- **Windows 10** (έκδοση 1903 ή νεότερη) ή **Windows 11**
- **RAM**: 4GB ελεύθερη μνήμη
- **Αποθηκευτικός χώρος**: 10GB ελεύθερος χώρος
- **Docker Desktop**: Τελευταία έκδοση

#### Συνιστώμενες Απαιτήσεις
- **RAM**: 8GB+ για βέλτιστη απόδοση
- **CPU**: 4+ cores
- **SSD**: Για καλύτερη απόδοση

### Εγκατάσταση Docker Desktop

#### Αυτόματη Εγκατάσταση
```powershell
# Με Chocolatey
choco install docker-desktop

# Με winget (Windows 10 1709+)
winget install Docker.DockerDesktop
```

#### Χειροκίνητη Εγκατάσταση
1. Πηγαίνετε στο https://docs.docker.com/desktop/windows/
2. Κατεβάστε το "Docker Desktop for Windows"
3. Εκτελέστε το `Docker Desktop Installer.exe`
4. Ακολουθήστε τις οδηγίες εγκατάστασης
5. Επανεκκινήστε τον υπολογιστή

#### Ρύθμιση Docker Desktop
1. Ξεκινήστε το Docker Desktop
2. Περιμένετε να ολοκληρωθεί η εκκίνηση (πράσινο εικονίδιο)
3. Ανοίξτε Settings (γρανάζι)
4. Στο "Resources" -> "Advanced":
   - **Memory**: Ορίστε τουλάχιστον 4GB
   - **CPUs**: Ορίστε τουλάχιστον 2 cores
5. Κάντε "Apply & Restart"

### Εκτέλεση Scripts Εγκατάστασης

#### PowerShell Script (install_solar_docker.ps1)

**Χαρακτηριστικά:**
- Έλεγχος προαπαιτούμενων
- Αυτόματη αποσυμπίεση
- Έλεγχος ports και μνήμης
- Χρωματιστό output
- Λεπτομερή μηνύματα σφαλμάτων

**Εκτέλεση:**
```powershell
# Άνοιγμα PowerShell ως Administrator
# Windows Key + X -> "Windows PowerShell (Admin)"

# Μετάβαση στον φάκελο
cd "C:\path\to\solar-prediction-package"

# Εκτέλεση script
.\install_solar_docker.ps1

# Αν έχετε πρόβλημα με execution policy:
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
.\install_solar_docker.ps1

# Για force εγκατάσταση (παράκαμψη ελέγχων):
.\install_solar_docker.ps1 -Force
```

#### Batch Script (install_solar_docker.bat)

**Χαρακτηριστικά:**
- Συμβατότητα με παλιότερα Windows
- Απλή εκτέλεση
- Βασικοί έλεγχοι

**Εκτέλεση:**
```cmd
# Άνοιγμα Command Prompt ως Administrator
# Windows Key + X -> "Command Prompt (Admin)"

# Μετάβαση στον φάκελο
cd "C:\path\to\solar-prediction-package"

# Εκτέλεση script
install_solar_docker.bat
```

### Αντιμετώπιση Προβλημάτων

#### Docker Desktop δεν ξεκινάει
```powershell
# Έλεγχος κατάστασης Hyper-V
Get-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V

# Ενεργοποίηση Hyper-V (απαιτεί επανεκκίνηση)
Enable-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V -All

# Έλεγχος WSL2
wsl --status

# Ενημέρωση WSL2
wsl --update
```

#### Execution Policy Error (PowerShell)
```powershell
# Προσωρινή αλλαγή για το τρέχον session
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process

# Μόνιμη αλλαγή για τον τρέχοντα χρήστη
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# Έλεγχος τρέχουσας policy
Get-ExecutionPolicy -List
```

#### Ports σε χρήση
```powershell
# Εύρεση διεργασιών που χρησιμοποιούν ports
netstat -ano | findstr :8100
netstat -ano | findstr :5432

# Τερματισμός διεργασίας (αντικαταστήστε PID)
taskkill /PID 1234 /F

# Εκκίνηση με διαφορετικά ports
docker run -d --name solar-prediction-system `
  -p 5433:5432 -p 8101:8100 -p 8111:8109 -p 8112:8110 `
  solar-prediction-complete:latest
```

#### Πρόβλημα με μνήμη
```powershell
# Έλεγχος διαθέσιμης μνήμης
Get-WmiObject -Class Win32_OperatingSystem | Select-Object TotalVisibleMemorySize,FreePhysicalMemory

# Αύξηση memory limit για Docker
# Docker Desktop -> Settings -> Resources -> Advanced -> Memory
```

#### Αποσυμπίεση αποτυγχάνει
```powershell
# Εγκατάσταση 7-Zip
choco install 7zip

# Ή χειροκίνητα από: https://www.7-zip.org/

# Χειροκίνητη αποσυμπίεση
7z x solar-prediction-complete.tar.gz
docker load -i solar-prediction-complete.tar
```

### Χρήσιμες Εντολές Windows

#### Διαχείριση Docker
```powershell
# Κατάσταση containers
docker ps -a

# Logs
docker logs solar-prediction-system

# Στατιστικά χρήσης
docker stats solar-prediction-system

# Πρόσβαση στο container
docker exec -it solar-prediction-system bash

# Επανεκκίνηση
docker restart solar-prediction-system

# Διακοπή και αφαίρεση
docker stop solar-prediction-system
docker rm solar-prediction-system
```

#### Διαχείριση Υπηρεσιών
```powershell
# Άνοιγμα web interface
Start-Process "http://localhost:8100"

# Έλεγχος συνδεσιμότητας
Test-NetConnection -ComputerName localhost -Port 8100

# Έλεγχος PostgreSQL
Test-NetConnection -ComputerName localhost -Port 5432
```

#### Firewall Configuration
```powershell
# Άνοιγμα ports στο Windows Firewall
New-NetFirewallRule -DisplayName "Solar Prediction Web" -Direction Inbound -Protocol TCP -LocalPort 8100
New-NetFirewallRule -DisplayName "Solar Prediction DB" -Direction Inbound -Protocol TCP -LocalPort 5432

# Ή μέσω GUI: Windows Security -> Firewall & network protection -> Advanced settings
```

### Performance Optimization

#### Docker Desktop Settings
1. **Resources -> Advanced**:
   - Memory: 6-8GB (αν διαθέσιμο)
   - CPUs: 4+ cores
   - Swap: 2GB
   - Disk image size: 64GB+

2. **Docker Engine**:
```json
{
  "experimental": false,
  "debug": false,
  "log-level": "info",
  "storage-driver": "windowsfilter"
}
```

#### Windows Performance
```powershell
# Απενεργοποίηση Windows Search για Docker volumes
# Services -> Windows Search -> Stop & Disable

# Εξαίρεση από Windows Defender
Add-MpPreference -ExclusionPath "C:\ProgramData\Docker"
Add-MpPreference -ExclusionPath "C:\Users\<USER>\.docker"
```

### Backup & Restore

#### Backup Container
```powershell
# Backup ολόκληρου container
docker commit solar-prediction-system solar-backup:$(Get-Date -Format "yyyyMMdd")

# Export σε αρχείο
docker save solar-backup:$(Get-Date -Format "yyyyMMdd") -o solar-backup.tar

# Backup μόνο δεδομένων
docker exec solar-prediction-system pg_dump -U postgres solar_prediction > backup.sql
```

#### Restore Container
```powershell
# Restore από αρχείο
docker load -i solar-backup.tar

# Restore δεδομένων
Get-Content backup.sql | docker exec -i solar-prediction-system psql -U postgres solar_prediction
```

## 🎯 Επόμενα Βήματα

1. **Πρόσβαση στο σύστημα**: http://localhost:8100
2. **Έλεγχος API**: http://localhost:8100/docs
3. **Ρύθμιση Telegram Bot** (αν χρειάζεται)
4. **Backup δεδομένων** (συνιστάται)

---

**Υποστήριξη**: Για προβλήματα, ελέγξτε τα logs με `docker logs solar-prediction-system`
