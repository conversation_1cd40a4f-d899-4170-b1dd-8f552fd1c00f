#!/bin/bash

# Complete Solar Prediction System Backup Script
# Backs up everything needed to transfer Docker infrastructure to another machine

set -e  # Exit on any error

# Configuration
BACKUP_DIR="/home/<USER>/solar-prediction-system-backup"
PROJECT_DIR="/home/<USER>/solar-prediction-project"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_SESSION_DIR="${BACKUP_DIR}/${TIMESTAMP}"

echo "🚀 COMPLETE SOLAR PREDICTION SYSTEM BACKUP"
echo "=========================================="
echo "📅 Timestamp: ${TIMESTAMP}"
echo "📂 Source: ${PROJECT_DIR}"
echo "💾 Destination: ${BACKUP_SESSION_DIR}"
echo ""

# Create backup directory structure
echo "📁 Creating backup directory structure..."
mkdir -p "${BACKUP_SESSION_DIR}"
mkdir -p "${BACKUP_SESSION_DIR}/docker_images"
mkdir -p "${BACKUP_SESSION_DIR}/docker_volumes"
mkdir -p "${BACKUP_SESSION_DIR}/project_files"
mkdir -p "${BACKUP_SESSION_DIR}/database_backup"
mkdir -p "${BACKUP_SESSION_DIR}/scripts"

# Function to log progress
log_progress() {
    echo "✅ $1"
}

# Function to handle errors
handle_error() {
    echo "❌ Error: $1"
    exit 1
}

echo "🔍 Step 1: Backing up project files..."
# Copy entire project directory (excluding .git and temporary files)
rsync -av --progress \
    --exclude='.git' \
    --exclude='__pycache__' \
    --exclude='*.pyc' \
    --exclude='.pytest_cache' \
    --exclude='node_modules' \
    --exclude='*.log' \
    --exclude='test_*.py' \
    "${PROJECT_DIR}/" "${BACKUP_SESSION_DIR}/project_files/" || handle_error "Failed to backup project files"

log_progress "Project files backed up"

echo ""
echo "🐳 Step 2: Backing up Docker images..."
# Get list of solar prediction related images
IMAGES=$(docker images --format "table {{.Repository}}:{{.Tag}}" | grep -E "(solar-prediction|postgres|redis)" | grep -v "REPOSITORY" || true)

if [ -n "$IMAGES" ]; then
    echo "Found images to backup:"
    echo "$IMAGES"
    echo ""
    
    # Save each image
    while IFS= read -r image; do
        if [ -n "$image" ]; then
            echo "💾 Saving image: $image"
            image_filename=$(echo "$image" | sed 's/[\/:]/_/g')
            docker save "$image" | gzip > "${BACKUP_SESSION_DIR}/docker_images/${image_filename}.tar.gz" || handle_error "Failed to save image $image"
            log_progress "Saved image: $image"
        fi
    done <<< "$IMAGES"
else
    echo "⚠️ No solar prediction images found"
fi

echo ""
echo "💽 Step 3: Backing up Docker volumes..."
# Get list of solar prediction related volumes
VOLUMES=$(docker volume ls --format "{{.Name}}" | grep -E "(solar|postgres|redis)" || true)

if [ -n "$VOLUMES" ]; then
    echo "Found volumes to backup:"
    echo "$VOLUMES"
    echo ""
    
    # Backup each volume
    while IFS= read -r volume; do
        if [ -n "$volume" ]; then
            echo "💾 Backing up volume: $volume"
            docker run --rm \
                -v "$volume":/source:ro \
                -v "${BACKUP_SESSION_DIR}/docker_volumes":/backup \
                alpine:latest \
                tar czf "/backup/${volume}.tar.gz" -C /source . || handle_error "Failed to backup volume $volume"
            log_progress "Backed up volume: $volume"
        fi
    done <<< "$VOLUMES"
else
    echo "⚠️ No solar prediction volumes found"
fi

echo ""
echo "🗄️ Step 4: Backing up database content..."
# Create database dump
echo "💾 Creating PostgreSQL database dump..."
docker exec solar-prediction-db pg_dumpall -U postgres | gzip > "${BACKUP_SESSION_DIR}/database_backup/complete_database_dump_${TIMESTAMP}.sql.gz" || handle_error "Failed to create database dump"
log_progress "Database dump created"

# Also backup individual databases
echo "💾 Creating individual database backups..."
docker exec solar-prediction-db pg_dump -U postgres -d solar_prediction | gzip > "${BACKUP_SESSION_DIR}/database_backup/solar_prediction_db_${TIMESTAMP}.sql.gz" || handle_error "Failed to backup solar_prediction database"
log_progress "Individual database backup created"

echo ""
echo "⚙️ Step 5: Backing up Docker configuration..."
# Copy docker-compose files and related configs
cp "${PROJECT_DIR}/docker-compose.yml" "${BACKUP_SESSION_DIR}/" 2>/dev/null || echo "⚠️ docker-compose.yml not found"
cp "${PROJECT_DIR}/Dockerfile" "${BACKUP_SESSION_DIR}/" 2>/dev/null || echo "⚠️ Dockerfile not found"
cp "${PROJECT_DIR}/.env" "${BACKUP_SESSION_DIR}/" 2>/dev/null || echo "⚠️ .env file not found"
cp "${PROJECT_DIR}/requirements.txt" "${BACKUP_SESSION_DIR}/" 2>/dev/null || echo "⚠️ requirements.txt not found"

log_progress "Docker configuration files backed up"

echo ""
echo "📋 Step 6: Creating system information..."
# Create system info file
cat > "${BACKUP_SESSION_DIR}/SYSTEM_INFO.txt" << EOF
SOLAR PREDICTION SYSTEM BACKUP
==============================
Backup Date: $(date)
Source System: $(hostname)
Docker Version: $(docker --version)
Docker Compose Version: $(docker-compose --version 2>/dev/null || echo "Not available")
Operating System: $(lsb_release -d 2>/dev/null | cut -f2 || uname -a)
Python Version: $(python3 --version)

DOCKER CONTAINERS STATUS:
$(docker ps -a --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep solar || echo "No solar containers found")

DOCKER IMAGES:
$(docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}" | grep -E "(solar|postgres|redis)" || echo "No related images found")

DOCKER VOLUMES:
$(docker volume ls --format "table {{.Name}}\t{{.Driver}}" | grep -E "(solar|postgres|redis)" || echo "No related volumes found")

BACKUP CONTENTS:
- Project files (complete source code)
- Docker images (compressed tar.gz files)
- Docker volumes (compressed tar.gz files)  
- Database dumps (PostgreSQL complete dump)
- Configuration files (docker-compose.yml, Dockerfile, etc.)
- This system information file

RESTORATION INSTRUCTIONS:
See RESTORE_INSTRUCTIONS.md for complete restoration guide.
EOF

log_progress "System information file created"

echo ""
echo "📖 Step 7: Creating restoration instructions..."
# Create detailed restoration guide
cat > "${BACKUP_SESSION_DIR}/RESTORE_INSTRUCTIONS.md" << 'EOF'
# Solar Prediction System Restoration Guide

## Prerequisites on Target Machine

1. **Install Docker and Docker Compose**
   ```bash
   # Ubuntu/Debian
   sudo apt update
   sudo apt install docker.io docker-compose
   sudo systemctl enable docker
   sudo systemctl start docker
   sudo usermod -aG docker $USER
   # Log out and back in for group changes
   ```

2. **Install Python 3.11+ and required tools**
   ```bash
   sudo apt install python3 python3-pip python3-venv git rsync
   ```

## Restoration Steps

### Step 1: Copy Project Files
```bash
# Copy the entire project_files directory to target location
sudo mkdir -p /home/<USER>/solar-prediction-project
sudo cp -r project_files/* /home/<USER>/solar-prediction-project/
sudo chown -R grlv:grlv /home/<USER>/solar-prediction-project
```

### Step 2: Restore Docker Images
```bash
cd docker_images
# Load each image
for image_file in *.tar.gz; do
    echo "Loading $image_file..."
    gunzip -c "$image_file" | docker load
done
```

### Step 3: Restore Docker Volumes
```bash
cd ../docker_volumes
# Restore each volume
for volume_file in *.tar.gz; do
    volume_name=$(basename "$volume_file" .tar.gz)
    echo "Restoring volume: $volume_name"

    # Create volume
    docker volume create "$volume_name"

    # Restore data
    docker run --rm \
        -v "$volume_name":/target \
        -v "$(pwd)":/backup \
        alpine:latest \
        tar xzf "/backup/$volume_file" -C /target
done
```

### Step 4: Restore Database
```bash
cd ../database_backup

# Method 1: Complete restore (recommended)
gunzip -c complete_database_dump_*.sql.gz | docker exec -i solar-prediction-db psql -U postgres

# Method 2: Individual database restore (if needed)
# First start containers, then:
# gunzip -c solar_prediction_db_*.sql.gz | docker exec -i solar-prediction-db psql -U postgres -d solar_prediction
```

### Step 5: Start the System
```bash
cd /home/<USER>/solar-prediction-project

# Copy configuration files
cp docker-compose.yml .
cp Dockerfile .
cp .env . 2>/dev/null || echo "No .env file to copy"

# Start the system
docker-compose up -d

# Check status
docker-compose ps
```

### Step 6: Verify System
```bash
# Check all containers are running
docker ps

# Check logs
docker-compose logs

# Test main API
curl http://localhost:8100/health

# Test Telegram bot (if configured)
# Send a test message to verify functionality
```

## Troubleshooting

### If containers fail to start:
1. Check Docker logs: `docker-compose logs [service-name]`
2. Verify volumes are restored: `docker volume ls`
3. Check database connectivity: `docker exec -it solar-prediction-db psql -U postgres -d solar_prediction`

### If database is empty:
1. Stop containers: `docker-compose down`
2. Remove database volume: `docker volume rm solar-prediction-project_postgres_data`
3. Restore volume from backup (Step 3)
4. Start containers: `docker-compose up -d`

### If images are missing:
1. Rebuild images: `docker-compose build`
2. Or restore from backup images (Step 2)

## Post-Restoration Checklist

- [ ] All containers running (`docker ps`)
- [ ] Database accessible and populated
- [ ] Main API responding (`curl http://localhost:8100/health`)
- [ ] Telegram bot responding (if configured)
- [ ] All services healthy (`docker-compose ps`)
- [ ] Logs show no critical errors (`docker-compose logs`)

## Important Notes

- Ensure target machine has same or compatible architecture (x86_64)
- Update any hardcoded paths in configuration files if needed
- Verify timezone settings match original system
- Update any external API keys or tokens if changed
- Test all functionality before considering migration complete

EOF

log_progress "Restoration instructions created"

echo ""
echo "🔐 Step 8: Creating backup verification..."
# Create verification script
cat > "${BACKUP_SESSION_DIR}/verify_backup.sh" << 'EOF'
#!/bin/bash
# Backup Verification Script

echo "🔍 BACKUP VERIFICATION"
echo "====================="

BACKUP_DIR="$(dirname "$0")"
cd "$BACKUP_DIR"

echo "📁 Checking backup structure..."
required_dirs=("project_files" "docker_images" "docker_volumes" "database_backup")
for dir in "${required_dirs[@]}"; do
    if [ -d "$dir" ]; then
        echo "✅ $dir directory exists"
        echo "   Files: $(find "$dir" -type f | wc -l)"
        echo "   Size: $(du -sh "$dir" | cut -f1)"
    else
        echo "❌ $dir directory missing"
    fi
done

echo ""
echo "🐳 Checking Docker images..."
if [ -d "docker_images" ]; then
    image_count=$(find docker_images -name "*.tar.gz" | wc -l)
    echo "✅ Found $image_count Docker image backups"
    ls -la docker_images/
else
    echo "❌ No Docker images found"
fi

echo ""
echo "💽 Checking Docker volumes..."
if [ -d "docker_volumes" ]; then
    volume_count=$(find docker_volumes -name "*.tar.gz" | wc -l)
    echo "✅ Found $volume_count Docker volume backups"
    ls -la docker_volumes/
else
    echo "❌ No Docker volumes found"
fi

echo ""
echo "🗄️ Checking database backups..."
if [ -d "database_backup" ]; then
    db_count=$(find database_backup -name "*.sql.gz" | wc -l)
    echo "✅ Found $db_count database backups"
    ls -la database_backup/
else
    echo "❌ No database backups found"
fi

echo ""
echo "📋 Checking configuration files..."
config_files=("docker-compose.yml" "Dockerfile" "SYSTEM_INFO.txt" "RESTORE_INSTRUCTIONS.md")
for file in "${config_files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file exists ($(du -sh "$file" | cut -f1))"
    else
        echo "❌ $file missing"
    fi
done

echo ""
echo "📊 BACKUP SUMMARY"
echo "================="
echo "Total backup size: $(du -sh . | cut -f1)"
echo "Total files: $(find . -type f | wc -l)"
echo "Backup date: $(cat SYSTEM_INFO.txt | grep "Backup Date:" | cut -d: -f2-)"

EOF

chmod +x "${BACKUP_SESSION_DIR}/verify_backup.sh"
log_progress "Backup verification script created"

echo ""
echo "📊 Step 9: Final backup summary..."
# Run verification
cd "${BACKUP_SESSION_DIR}"
./verify_backup.sh

echo ""
echo "🎉 BACKUP COMPLETED SUCCESSFULLY!"
echo "================================="
echo "📂 Backup location: ${BACKUP_SESSION_DIR}"
echo "💾 Total size: $(du -sh "${BACKUP_SESSION_DIR}" | cut -f1)"
echo ""
echo "📋 Next steps:"
echo "1. Verify backup integrity: cd '${BACKUP_SESSION_DIR}' && ./verify_backup.sh"
echo "2. Copy backup to target machine"
echo "3. Follow RESTORE_INSTRUCTIONS.md on target machine"
echo ""
echo "✅ Your complete Solar Prediction System is now backed up!"
