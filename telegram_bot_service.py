#!/usr/bin/env python3
"""
Telegram Bot Service for Docker Container
Wrapper for the Greek Telegram Bot
"""

import os
import sys
import asyncio
import logging
import threading
from pathlib import Path
from datetime import datetime
from fastapi import FastAPI
from fastapi.responses import JSONResponse
import uvicorn

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Health endpoint for Docker health check
health_app = FastAPI()

@health_app.get("/health")
async def health_check():
    """Health check endpoint for Telegram bot service"""
    try:
        health_status = {
            "status": "healthy",
            "service": "telegram_bot_service",
            "timestamp": datetime.now().isoformat(),
            "bot_running": True,
            "port": 8109
        }

        return JSONResponse(content=health_status)
    except Exception as e:
        return JSONResponse(
            content={
                "status": "unhealthy",
                "service": "telegram_bot_service",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            },
            status_code=500
        )

def run_health_server():
    """Run health check server in background"""
    uvicorn.run(health_app, host="0.0.0.0", port=8109, log_level="warning")

def main():
    """Main entry point for Telegram Bot service"""
    try:
        logger.info("🤖 Starting Telegram Bot Service...")

        # Start health server in background thread
        logger.info("🏥 Starting health server on port 8109...")
        health_thread = threading.Thread(target=run_health_server, daemon=True)
        health_thread.start()

        # Import and run the Greek Telegram Bot
        from scripts.frontend_system.greek_telegram_bot import main as bot_main

        # Run the bot
        bot_main()

    except Exception as e:
        logger.error(f"❌ Failed to start Telegram Bot: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
