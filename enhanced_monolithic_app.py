#!/usr/bin/env python3
"""
Production Solar Prediction API
Complete FastAPI application with background tasks
"""
import os
import json
import asyncio
import subprocess
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, Optional, List
from contextlib import asynccontextmanager

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from pydantic import BaseModel
import psycopg2
from psycopg2.extras import RealDictCursor

# Configuration
DATABASE_URL = "postgresql://postgres:postgres@localhost:5433/solar_prediction"
WEATHER_API_URL = "https://api.open-meteo.com/v1/forecast"
SOLAX_API_URL = "https://www.solaxcloud.com:9443/proxy/api/getRealtimeInfo.do"
SOLAX_TOKEN_ID = "20250410220826567911082"

# System configurations
SYSTEMS = {
    "system1": {
        "wifi_sn": "SRFQDPDN9W",
        "table": "solax_data",
        "name": "Σπίτι Πάνω"
    },
    "system2": {
        "wifi_sn": "SRCV9TUD6S",
        "table": "solax_data2",
        "name": "Σπίτι Κάτω"
    }
}

# Background task state
background_tasks_running = False
background_task_handle = None

class PredictionRequest(BaseModel):
    temperature: Optional[float] = None
    cloud_cover: Optional[float] = None
    soc: Optional[float] = 75.0
    hour: Optional[int] = None

class PredictionResponse(BaseModel):
    predicted_power: float
    confidence: float
    timestamp: str
    model_version: str
    inputs: Dict[str, Any]

class WeatherResponse(BaseModel):
    temperature: float
    cloud_cover: float
    humidity: Optional[float] = None
    timestamp: str
    source: str

def get_db_connection():
    """Get database connection"""
    try:
        return psycopg2.connect(DATABASE_URL)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database connection failed: {e}")

def call_weather_api() -> Dict[str, Any]:
    """Call Weather API using curl"""
    try:
        cmd = [
            "curl", "--tlsv1.2", "-s", "--connect-timeout", "10", "--max-time", "15",
            f"{WEATHER_API_URL}?latitude=37.9755&longitude=23.7348&current=temperature_2m,cloud_cover,relative_humidity_2m&timezone=Europe/Athens"
        ]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=20)

        if result.returncode == 0:
            data = json.loads(result.stdout)
            current = data.get("current", {})
            return {
                "success": True,
                "temperature": current.get("temperature_2m"),
                "cloud_cover": current.get("cloud_cover"),
                "humidity": current.get("relative_humidity_2m"),
                "timestamp": current.get("time")
            }
        else:
            return {"success": False, "error": "API call failed"}

    except Exception as e:
        return {"success": False, "error": str(e)}

def call_solax_api(system_id: str = "system1") -> Dict[str, Any]:
    """Call SolaX Cloud API using curl for specified system"""
    try:
        if system_id not in SYSTEMS:
            return {"success": False, "error": f"Unknown system: {system_id}"}

        wifi_sn = SYSTEMS[system_id]["wifi_sn"]
        # Prepare form data (not JSON!)
        form_data = f"tokenId={SOLAX_TOKEN_ID}&sn={wifi_sn}"

        cmd = [
            "curl", "--tlsv1.2", "-s", "--connect-timeout", "10", "--max-time", "15",
            "-H", "Content-Type: application/x-www-form-urlencoded",
            "-X", "POST",
            "-d", form_data,
            SOLAX_API_URL
        ]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=20)

        if result.returncode == 0:
            data = json.loads(result.stdout)

            if data.get("success"):
                result_data = data.get("result", {})
                return {
                    "success": True,
                    "data": result_data,
                    "timestamp": datetime.now().isoformat()
                }
            else:
                error_msg = data.get("exception", "Unknown SolaX API error")
                return {"success": False, "error": error_msg}
        else:
            return {"success": False, "error": "SolaX API call failed"}

    except Exception as e:
        return {"success": False, "error": str(e)}

def make_prediction(inputs: Dict[str, Any]) -> Dict[str, Any]:
    """Generate solar power prediction - System has battery and can produce power without sun"""
    try:
        temp = inputs.get("temperature", 25)
        clouds = inputs.get("cloud_cover", 50)
        soc = inputs.get("soc", 75)
        hour = inputs.get("hour", datetime.now().hour)

        # Solar power estimation with battery consideration
        if 6 <= hour <= 18:  # Daylight hours - panels + battery
            # Calculate GHI estimate
            base_ghi = 800 * max(0.1, 1.0 - clouds / 100.0)

            # Temperature coefficient (panels lose efficiency in heat)
            temp_factor = max(0.8, 1.0 - (temp - 25) * 0.004)

            # Battery factor (reduce output if battery is full)
            soc_factor = 0.8 if soc > 90 else 1.0

            # System efficiency and panel rating
            system_efficiency = 0.85
            panel_rating = 10500  # 10.5kW system (as per documentation)

            # Panel production
            panel_power = (base_ghi / 1000) * panel_rating * temp_factor * soc_factor * system_efficiency

            # Battery can also provide power during day
            battery_power = max(0, (soc - 20) * 120) if soc > 20 else 0  # 12kWh battery

            predicted_power = panel_power + (battery_power * 0.3)  # Conservative battery contribution
            confidence = 0.85 if base_ghi > 200 else 0.65

        else:  # Night hours - battery only (if available)
            # Battery can provide power even at night
            if soc > 20:  # Minimum SOC for operation
                # Battery power output based on SOC
                battery_power = (soc - 20) * 150  # Conservative night output
                predicted_power = min(battery_power, 3000)  # Max 3kW at night
                confidence = 0.75
            else:
                predicted_power = 0.0
                confidence = 0.95

        return {
            "predicted_power": max(0, predicted_power),
            "confidence": confidence,
            "model_version": "production_battery_aware_v1.1",
            "timestamp": datetime.now().isoformat(),
            "inputs": inputs
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Prediction failed: {e}")

async def background_data_collection():
    """Background task for periodic data collection"""
    global background_tasks_running

    weather_counter = 0  # Counter for weather collection frequency (every 30 iterations = 15 minutes)

    while background_tasks_running:
        try:
            # Get current weather for temperature data
            current_weather = call_weather_api()
            ambient_temperature = current_weather.get("temperature", 25) if current_weather.get("success") else 25

            # Collect SolaX data from both systems every 30 seconds
            for system_id, system_config in SYSTEMS.items():
                solax_result = call_solax_api(system_id)

                if solax_result["success"]:
                    try:
                        conn = get_db_connection()
                        with conn.cursor() as cur:
                            timestamp = datetime.now()
                            solax_data = solax_result["data"]
                            table_name = system_config["table"]

                            # Use weather temperature if SolaX doesn't provide it
                            system_temperature = solax_data.get("temperature") or ambient_temperature

                            cur.execute(f"""
                                INSERT INTO {table_name} (timestamp, inverter_sn, wifi_sn,
                                                      ac_power, soc, bat_power, temperature,
                                                      powerdc1, powerdc2, yield_today, yield_total,
                                                      feedin_power, feedin_energy, consume_energy,
                                                      created_at, raw_data)
                                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                            """, (
                                timestamp,
                                solax_data.get("inverterSN"),
                                solax_data.get("wifiSN"),
                                solax_data.get("acpower"),
                                solax_data.get("soc"),
                                solax_data.get("batPower"),
                                system_temperature,
                                solax_data.get("powerdc1"),
                                solax_data.get("powerdc2"),
                                solax_data.get("yieldtoday"),
                                solax_data.get("yieldtotal"),
                                solax_data.get("feedinpower"),
                                solax_data.get("feedinenergy"),
                                solax_data.get("consumeenergy"),
                                timestamp,
                                json.dumps(solax_result)
                            ))
                            conn.commit()
                        conn.close()

                        print(f"Background: {system_config['name']} data saved - {solax_data.get('acpower', 0)}W, SOC: {solax_data.get('soc', 0)}%")

                    except Exception as db_error:
                        print(f"Background: {system_config['name']} database save failed - {db_error}")
                else:
                    print(f"Background: {system_config['name']} API failed - {solax_result.get('error', 'Unknown error')}")

            # Collect weather data every 15 minutes (30 iterations * 30 seconds = 15 minutes)
            if weather_counter % 30 == 0:
                weather_result = call_weather_api()

                if weather_result["success"]:
                    # Save to database
                    try:
                        conn = get_db_connection()
                        with conn.cursor() as cur:
                            # Use current timestamp for database record
                            current_timestamp = datetime.now()

                            # Parse the weather API timestamp if available, otherwise use current
                            weather_timestamp = weather_result.get("timestamp")
                            if weather_timestamp:
                                try:
                                    # Convert weather API timestamp to datetime
                                    from datetime import datetime as dt
                                    weather_dt = dt.fromisoformat(weather_timestamp.replace('Z', '+00:00'))
                                    # Use current time but keep the weather data
                                    record_timestamp = current_timestamp
                                except:
                                    record_timestamp = current_timestamp
                            else:
                                record_timestamp = current_timestamp

                            cur.execute("""
                                INSERT INTO weather_data (timestamp, temperature_2m, relative_humidity_2m,
                                                        cloud_cover, is_forecast, created_at, raw_data)
                                VALUES (%s, %s, %s, %s, %s, %s, %s)
                            """, (
                                record_timestamp,
                                weather_result["temperature"],
                                weather_result["humidity"],
                                weather_result["cloud_cover"],
                                False,
                                current_timestamp,
                                json.dumps(weather_result)
                            ))
                            conn.commit()
                        conn.close()

                        print(f"Background: Weather data saved - {weather_result['temperature']}°C")

                    except Exception as db_error:
                        print(f"Background: Weather database save failed - {db_error}")

                # Generate prediction with latest data
                try:
                    # Use real SOC from SolaX if available, otherwise default
                    current_soc = 75  # Default
                    if solax_result.get("success"):
                        current_soc = solax_result["data"].get("soc", 75)

                    prediction_inputs = {
                        "temperature": weather_result.get("temperature", 25),
                        "cloud_cover": weather_result.get("cloud_cover", 50),
                        "soc": current_soc,
                        "hour": datetime.now().hour
                    }

                    prediction = make_prediction(prediction_inputs)

                    # Save prediction
                    conn = get_db_connection()
                    with conn.cursor() as cur:
                        cur.execute("""
                            INSERT INTO predictions (timestamp, predicted_ac_power, confidence_score,
                                                   model_version, input_features, prediction_time_ms)
                            VALUES (%s, %s, %s, %s, %s, %s)
                        """, (
                            datetime.now(),
                            prediction["predicted_power"],
                            prediction["confidence"],
                            prediction["model_version"],
                            json.dumps(prediction["inputs"]),
                            5.0
                        ))
                        conn.commit()
                    conn.close()

                    print(f"Background: Prediction saved - {prediction['predicted_power']:.1f}W")

                except Exception as pred_error:
                    print(f"Background: Prediction failed - {pred_error}")

        except Exception as e:
            print(f"Background task error: {e}")

        # Increment counter and wait
        weather_counter += 1

        # Wait 30 seconds before next iteration
        await asyncio.sleep(30)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    global background_tasks_running, background_task_handle

    # Startup
    print("🚀 Starting Solar Prediction API")
    background_tasks_running = True
    background_task_handle = asyncio.create_task(background_data_collection())
    print("✅ Background tasks started")

    yield

    # Shutdown
    print("🛑 Shutting down Solar Prediction API")
    background_tasks_running = False
    if background_task_handle:
        background_task_handle.cancel()
        try:
            await background_task_handle
        except asyncio.CancelledError:
            pass
    print("✅ Background tasks stopped")

# Initialize FastAPI app
app = FastAPI(
    title="Solar Prediction API",
    description="Production solar power prediction system",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
import os
static_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "static")
app.mount("/static", StaticFiles(directory=static_dir), name="static")

@app.get("/")
async def root():
    """Serve the main web interface"""
    static_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), "static", "index.html")
    return FileResponse(static_file)

@app.get("/admin")
async def admin_interface():
    """Admin interface"""
    admin_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), "static", "admin", "index.html")
    if os.path.exists(admin_file):
        return FileResponse(admin_file)
    else:
        return {"error": "Admin interface not found", "path": admin_file}

@app.get("/api")
async def api_root():
    """API root endpoint"""
    return {
        "message": "Solar Prediction API",
        "version": "1.0.0",
        "status": "running",
        "timestamp": datetime.now().isoformat(),
        "background_tasks": background_tasks_running,
        "endpoints": {
            "health": "/health",
            "predict": "/api/v1/predict",
            "weather": "/api/v1/weather/current",
            "predictions": "/api/v1/predictions/recent"
        }
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Test database
        conn = get_db_connection()
        with conn.cursor() as cur:
            cur.execute("SELECT 1")
        conn.close()
        db_status = "healthy"

        # Test weather API
        weather_result = call_weather_api()
        weather_status = "healthy" if weather_result["success"] else "unhealthy"

        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "services": {
                "database": db_status,
                "weather_api": weather_status,
                "background_tasks": background_tasks_running
            },
            "current_weather": weather_result if weather_result["success"] else None
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Health check failed: {e}")

@app.post("/api/v1/predict", response_model=PredictionResponse)
async def predict_power(request: PredictionRequest):
    """Generate solar power prediction"""
    try:
        # Get current weather if not provided
        if request.temperature is None or request.cloud_cover is None:
            weather_result = call_weather_api()
            if weather_result["success"]:
                temp = request.temperature or weather_result["temperature"]
                clouds = request.cloud_cover or weather_result["cloud_cover"]
            else:
                temp = request.temperature or 25
                clouds = request.cloud_cover or 50
        else:
            temp = request.temperature
            clouds = request.cloud_cover

        # Prepare inputs
        inputs = {
            "temperature": temp,
            "cloud_cover": clouds,
            "soc": request.soc,
            "hour": request.hour or datetime.now().hour
        }

        # Generate prediction
        prediction = make_prediction(inputs)

        # Save to database
        try:
            conn = get_db_connection()
            with conn.cursor() as cur:
                cur.execute("""
                    INSERT INTO predictions (timestamp, predicted_ac_power, confidence_score,
                                           model_version, input_features, prediction_time_ms)
                    VALUES (%s, %s, %s, %s, %s, %s)
                """, (
                    datetime.now(),
                    prediction["predicted_power"],
                    prediction["confidence"],
                    prediction["model_version"],
                    json.dumps(inputs),
                    5.0
                ))
                conn.commit()
            conn.close()
        except Exception as db_error:
            print(f"Database save failed: {db_error}")

        return PredictionResponse(
            predicted_power=prediction["predicted_power"],
            confidence=prediction["confidence"],
            timestamp=prediction["timestamp"],
            model_version=prediction["model_version"],
            inputs=inputs
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Prediction failed: {e}")

@app.get("/api/v1/weather/current", response_model=WeatherResponse)
async def get_current_weather():
    """Get current weather data"""
    weather_result = call_weather_api()

    if weather_result["success"]:
        return WeatherResponse(
            temperature=weather_result["temperature"],
            cloud_cover=weather_result["cloud_cover"],
            humidity=weather_result["humidity"],
            timestamp=weather_result["timestamp"],
            source="open_meteo"
        )
    else:
        raise HTTPException(status_code=503, detail="Weather API unavailable")

@app.get("/api/v1/solax/current")
async def get_current_solax_data():
    """Get current SolaX data"""
    solax_result = call_solax_api()

    if solax_result["success"]:
        return {
            "status": "success",
            "data": solax_result["data"],
            "timestamp": solax_result["timestamp"],
            "source": "solax_cloud"
        }
    else:
        raise HTTPException(status_code=503, detail=f"SolaX API unavailable: {solax_result.get('error', 'Unknown error')}")

@app.get("/api/v1/data/solax/latest")
async def get_latest_solax_data(system: str = "system1"):
    """Get latest SolaX data from database for specified system"""
    try:
        if system not in SYSTEMS:
            raise HTTPException(status_code=400, detail=f"Invalid system: {system}")

        table_name = SYSTEMS[system]["table"]
        system_name = SYSTEMS[system]["name"]

        conn = get_db_connection()
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute(f"""
                SELECT yield_today, soc, ac_power, timestamp, bat_power, temperature
                FROM {table_name}
                ORDER BY timestamp DESC
                LIMIT 1
            """)

            row = cur.fetchone()
            if row:
                return {
                    'yield_today': float(row['yield_today'] or 0),
                    'soc': float(row['soc'] or 0),
                    'ac_power': float(row['ac_power'] or 0),
                    'timestamp': row['timestamp'].isoformat() if row['timestamp'] else None,
                    'bat_power': float(row['bat_power'] or 0),
                    'temperature': float(row['temperature'] or 0),
                    'system': system,
                    'system_name': system_name
                }
            else:
                raise HTTPException(status_code=404, detail=f"No SolaX data found for {system}")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database query failed: {e}")

@app.get("/api/v1/data/solax/both")
async def get_both_systems_data():
    """Get latest data from both solar systems"""
    try:
        systems_data = {}

        for system_id, system_config in SYSTEMS.items():
            table_name = system_config["table"]
            system_name = system_config["name"]

            conn = get_db_connection()
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                cur.execute(f"""
                    SELECT yield_today, soc, ac_power, timestamp, bat_power, temperature
                    FROM {table_name}
                    ORDER BY timestamp DESC
                    LIMIT 1
                """)

                row = cur.fetchone()
                if row:
                    systems_data[system_id] = {
                        'yield_today': float(row['yield_today'] or 0),
                        'soc': float(row['soc'] or 0),
                        'ac_power': float(row['ac_power'] or 0),
                        'timestamp': row['timestamp'].isoformat() if row['timestamp'] else None,
                        'bat_power': float(row['bat_power'] or 0),
                        'temperature': float(row['temperature'] or 0),
                        'system': system_id,
                        'system_name': system_name
                    }
                else:
                    systems_data[system_id] = {
                        'error': f'No data found for {system_name}',
                        'system': system_id,
                        'system_name': system_name
                    }
            conn.close()

        return {
            'status': 'success',
            'systems': systems_data,
            'timestamp': datetime.now().isoformat()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database query failed: {e}")

@app.get("/api/v1/data/weather/latest")
async def get_latest_weather_data():
    """Get latest weather data from database"""
    try:
        conn = get_db_connection()
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute("""
                SELECT temperature_2m, cloud_cover, timestamp
                FROM weather_data
                ORDER BY timestamp DESC
                LIMIT 1
            """)

            row = cur.fetchone()
            if row:
                return {
                    'temperature_2m': float(row['temperature_2m'] or 0),
                    'cloud_cover': float(row['cloud_cover'] or 0),
                    'timestamp': row['timestamp'].isoformat() if row['timestamp'] else None
                }
            else:
                raise HTTPException(status_code=404, detail="No weather data found")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database query failed: {e}")

@app.get("/api/v1/predictions/recent")
async def get_recent_predictions():
    """Get recent predictions"""
    try:
        conn = get_db_connection()
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute("""
                SELECT * FROM predictions
                ORDER BY timestamp DESC
                LIMIT 20
            """)
            predictions = cur.fetchall()
        conn.close()

        return {
            "status": "success",
            "count": len(predictions),
            "data": [dict(pred) for pred in predictions],
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database query failed: {e}")

# Admin API Endpoints
@app.get("/api/v1/admin/database/info")
async def get_admin_database_info():
    """Get database information for admin interface"""
    try:
        conn = get_db_connection()
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            # Get table information
            table_names = [
                "solax_data", "weather_data", "predictions",
                "system_logs", "model_metrics", "schedule_tasks",
                "api_sources"
            ]

            tables_info = []
            total_records = 0

            for table_name in table_names:
                try:
                    cur.execute(f"SELECT COUNT(*) as count FROM {table_name}")
                    count_result = cur.fetchone()
                    count = count_result['count'] if count_result else 0

                    # Get last updated (approximate)
                    last_updated = None
                    if table_name in ["solax_data", "weather_data", "predictions"]:
                        try:
                            cur.execute(f"SELECT MAX(timestamp) as last_updated FROM {table_name}")
                            last_result = cur.fetchone()
                            last_updated = last_result['last_updated'] if last_result else None
                        except:
                            pass

                    tables_info.append({
                        "table_name": table_name,
                        "record_count": count,
                        "size_mb": 0.0,  # Would need actual size calculation
                        "health_status": "healthy" if count >= 0 else "error",
                        "last_updated": last_updated.isoformat() if last_updated else None
                    })
                    total_records += count

                except Exception as e:
                    tables_info.append({
                        "table_name": table_name,
                        "record_count": 0,
                        "size_mb": 0.0,
                        "health_status": "error",
                        "last_updated": None
                    })

        conn.close()

        return {
            "status": "success",
            "data": {
                "total_tables": len(tables_info),
                "total_records": total_records,
                "total_size_mb": 0.0,
                "health_status": "healthy",
                "tables": tables_info
            },
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get database info: {e}")

@app.get("/api/v1/admin/database/tables")
async def get_admin_database_tables():
    """Get database tables for admin interface"""
    try:
        conn = get_db_connection()
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            # Get table information
            table_names = [
                "solax_data", "weather_data", "predictions",
                "system_logs", "model_metrics", "schedule_tasks",
                "api_sources"
            ]

            tables_data = []

            for table_name in table_names:
                try:
                    cur.execute(f"SELECT COUNT(*) as count FROM {table_name}")
                    count_result = cur.fetchone()
                    count = count_result['count'] if count_result else 0

                    # Get last updated (approximate)
                    last_updated = None
                    if table_name in ["solax_data", "weather_data", "predictions"]:
                        try:
                            cur.execute(f"SELECT MAX(timestamp) as last_updated FROM {table_name}")
                            last_result = cur.fetchone()
                            last_updated = last_result['last_updated'] if last_result else None
                        except:
                            pass

                    tables_data.append({
                        "table_name": table_name,
                        "record_count": count,
                        "size_mb": 0.0,
                        "health_status": "healthy" if count >= 0 else "error",
                        "last_updated": last_updated.isoformat() if last_updated else None,
                        "description": get_table_description(table_name)
                    })

                except Exception as e:
                    tables_data.append({
                        "table_name": table_name,
                        "record_count": 0,
                        "size_mb": 0.0,
                        "health_status": "error",
                        "last_updated": None,
                        "description": get_table_description(table_name)
                    })

        conn.close()

        return {
            "status": "success",
            "data": tables_data,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get database tables: {e}")

def get_table_description(table_name: str) -> str:
    """Get description for database table"""
    descriptions = {
        "solax_data": "Real-time solar system data from SolaX Cloud API",
        "weather_data": "Weather and radiation data from Open-Meteo API",
        "predictions": "ML model predictions for solar power output",
        "system_logs": "Application logs and system events",
        "model_metrics": "ML model performance metrics and training history",
        "schedule_tasks": "Background task scheduler configuration",
        "api_sources": "External API configuration and status"
    }
    return descriptions.get(table_name, "Database table")

@app.get("/api/v1/admin/schedules")
async def get_admin_schedules():
    """Get schedule tasks for admin interface"""
    try:
        conn = get_db_connection()
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute("""
                SELECT * FROM schedule_tasks
                ORDER BY task_name
            """)
            schedules = cur.fetchall()
        conn.close()

        return {
            "status": "success",
            "data": [dict(schedule) for schedule in schedules],
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get schedules: {e}")

@app.get("/api/v1/admin/status")
async def get_admin_status():
    """Get system status for admin interface"""
    try:
        # Test database
        conn = get_db_connection()
        with conn.cursor() as cur:
            cur.execute("SELECT 1")
        conn.close()
        db_status = "healthy"

        # Test weather API
        weather_result = call_weather_api()
        weather_status = "healthy" if weather_result["success"] else "unhealthy"

        # Check background tasks
        bg_status = "healthy" if background_tasks_running else "unhealthy"

        # Overall status
        overall_status = "healthy"
        if weather_status != "healthy" or bg_status != "healthy":
            overall_status = "warning"
        if db_status != "healthy":
            overall_status = "unhealthy"

        return {
            "status": "success",
            "data": {
                "overall_status": overall_status,
                "services": {
                    "database": db_status,
                    "weather_api": weather_status,
                    "background_tasks": bg_status
                },
                "uptime": "Running",
                "version": "1.0.0"
            },
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get system status: {e}")

if __name__ == "__main__":
    import uvicorn
    print("🌞 Starting Production Solar Prediction API")
    uvicorn.run(app, host="0.0.0.0", port=8100, log_level="info")
