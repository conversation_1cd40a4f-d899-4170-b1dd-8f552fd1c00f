#!/usr/bin/env python3
"""
Comprehensive Model Retraining - FINAL VERSION
Complete retraining with ALL correct data and comprehensive evaluation
"""

import os
import sys
import json
import joblib
import numpy as np
import pandas as pd
import subprocess
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, List, Tuple
import warnings
warnings.filterwarnings('ignore')

# Try to import ML libraries
try:
    from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
    from sklearn.linear_model import LinearRegression, Ridge, Lasso
    from sklearn.tree import DecisionTreeRegressor
    from sklearn.preprocessing import StandardScaler, MinMaxScaler
    from sklearn.model_selection import train_test_split, cross_val_score
    from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
    SKLEARN_AVAILABLE = True
    print("✅ Sklearn available")
except ImportError:
    SKLEARN_AVAILABLE = False
    print("⚠️  Sklearn not available - using basic models")

class ComprehensiveModelTrainer:
    """Complete model trainer with all algorithms"""
    
    def __init__(self):
        self.algorithms = {}
        self.scalers = {}
        self.results = {}
        
        if SKLEARN_AVAILABLE:
            self.algorithms = {
                'RandomForest': RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1),
                'GradientBoosting': GradientBoostingRegressor(n_estimators=100, random_state=42),
                'LinearRegression': LinearRegression(),
                'Ridge': Ridge(alpha=1.0),
                'Lasso': Lasso(alpha=1.0),
                'DecisionTree': DecisionTreeRegressor(random_state=42)
            }
            
            self.scalers = {
                'StandardScaler': StandardScaler(),
                'MinMaxScaler': MinMaxScaler()
            }
        else:
            # Fallback to basic statistical models
            self.algorithms = {
                'SeasonalAverage': self.seasonal_average_model,
                'LinearTrend': self.linear_trend_model
            }
            self.scalers = {'NoScaling': None}
    
    def get_data_from_db(self) -> Dict[str, pd.DataFrame]:
        """Get all data from database"""
        print("📊 LOADING ALL DATA FROM DATABASE")
        print("=" * 40)
        
        all_data = {}
        
        # System 1 data
        try:
            cmd = ['psql', '-h', 'localhost', '-U', 'postgres', '-d', 'solar_prediction', '-c', 
                   """COPY (
                       SELECT timestamp, yield_today, 1 as system_id
                       FROM solax_data 
                       WHERE yield_today >= 0 AND yield_today < 100
                       ORDER BY timestamp
                   ) TO STDOUT WITH CSV HEADER;"""]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                # Save to temp file and read with pandas
                with open('temp_system1.csv', 'w') as f:
                    f.write(result.stdout)
                
                df1 = pd.read_csv('temp_system1.csv')
                df1['timestamp'] = pd.to_datetime(df1['timestamp'])
                all_data['system_1'] = df1
                
                os.remove('temp_system1.csv')
                
                print(f"✅ System 1: {len(df1)} records from {df1['timestamp'].min()} to {df1['timestamp'].max()}")
            else:
                print("❌ Failed to load System 1 data")
                all_data['system_1'] = pd.DataFrame()
        
        except Exception as e:
            print(f"❌ Error loading System 1: {e}")
            all_data['system_1'] = pd.DataFrame()
        
        # System 2 data
        try:
            cmd = ['psql', '-h', 'localhost', '-U', 'postgres', '-d', 'solar_prediction', '-c', 
                   """COPY (
                       SELECT timestamp, yield_today, 2 as system_id
                       FROM solax_data2 
                       WHERE yield_today >= 0 AND yield_today < 100
                       ORDER BY timestamp
                   ) TO STDOUT WITH CSV HEADER;"""]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                # Save to temp file and read with pandas
                with open('temp_system2.csv', 'w') as f:
                    f.write(result.stdout)
                
                df2 = pd.read_csv('temp_system2.csv')
                df2['timestamp'] = pd.to_datetime(df2['timestamp'])
                all_data['system_2'] = df2
                
                os.remove('temp_system2.csv')
                
                print(f"✅ System 2: {len(df2)} records from {df2['timestamp'].min()} to {df2['timestamp'].max()}")
            else:
                print("❌ Failed to load System 2 data")
                all_data['system_2'] = pd.DataFrame()
        
        except Exception as e:
            print(f"❌ Error loading System 2: {e}")
            all_data['system_2'] = pd.DataFrame()
        
        return all_data
    
    def create_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create comprehensive features"""
        print(f"🔧 Creating features for {len(df)} records...")
        
        # Temporal features
        df['month'] = df['timestamp'].dt.month
        df['day'] = df['timestamp'].dt.day
        df['hour'] = df['timestamp'].dt.hour
        df['day_of_week'] = df['timestamp'].dt.dayofweek
        df['day_of_year'] = df['timestamp'].dt.dayofyear
        df['week_of_year'] = df['timestamp'].dt.isocalendar().week
        df['quarter'] = df['timestamp'].dt.quarter
        
        # Season
        df['season'] = (df['month'] - 1) // 3
        
        # Cyclical encoding
        df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
        df['day_sin'] = np.sin(2 * np.pi * df['day_of_year'] / 365)
        df['day_cos'] = np.cos(2 * np.pi * df['day_of_year'] / 365)
        df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
        
        # Solar position features
        df['solar_declination'] = 23.45 * np.sin(np.radians(360 * (284 + df['day_of_year']) / 365))
        df['day_length'] = 12 + 4 * np.sin(np.radians(df['solar_declination']))
        
        # Binary features
        df['is_weekend'] = (df['day_of_week'] >= 5).astype(int)
        df['is_peak_season'] = df['month'].isin([5, 6, 7]).astype(int)
        df['is_low_season'] = df['month'].isin([12, 1, 2]).astype(int)
        
        # Lag features (if enough data)
        if len(df) > 24:
            df = df.sort_values('timestamp')
            df['yield_lag_1h'] = df['yield_today'].shift(1)
            df['yield_lag_24h'] = df['yield_today'].shift(24)
            
            # Rolling statistics
            df['yield_rolling_24h_mean'] = df['yield_today'].rolling(window=24, min_periods=1).mean()
            df['yield_rolling_24h_std'] = df['yield_today'].rolling(window=24, min_periods=1).std()
        
        # Fill NaN values
        df = df.fillna(method='bfill').fillna(0)
        
        print(f"✅ Created {len(df.columns)} features")
        return df
    
    def seasonal_average_model(self, X_train, y_train):
        """Simple seasonal average model"""
        class SeasonalModel:
            def __init__(self):
                self.seasonal_averages = {}
            
            def fit(self, X, y):
                # Assume first column is month
                for month in range(1, 13):
                    mask = X[:, 0] == month
                    if mask.any():
                        self.seasonal_averages[month] = np.mean(y[mask])
                    else:
                        self.seasonal_averages[month] = np.mean(y)
                return self
            
            def predict(self, X):
                predictions = []
                for row in X:
                    month = int(row[0])
                    predictions.append(self.seasonal_averages.get(month, np.mean(list(self.seasonal_averages.values()))))
                return np.array(predictions)
        
        return SeasonalModel()
    
    def linear_trend_model(self, X_train, y_train):
        """Simple linear trend model"""
        class LinearModel:
            def __init__(self):
                self.coefficients = None
                self.intercept = None
            
            def fit(self, X, y):
                # Simple linear regression using normal equation
                X_with_bias = np.column_stack([np.ones(len(X)), X])
                try:
                    theta = np.linalg.lstsq(X_with_bias, y, rcond=None)[0]
                    self.intercept = theta[0]
                    self.coefficients = theta[1:]
                except:
                    self.intercept = np.mean(y)
                    self.coefficients = np.zeros(X.shape[1])
                return self
            
            def predict(self, X):
                return self.intercept + np.dot(X, self.coefficients)
        
        return LinearModel()
    
    def train_and_evaluate_models(self, all_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """Train and evaluate all models"""
        print(f"\n🚀 TRAINING & EVALUATING ALL MODELS")
        print("=" * 45)
        
        results = {}
        
        for system_key, df in all_data.items():
            if df.empty:
                continue
            
            print(f"\n📊 Training models for {system_key}...")
            
            # Create features
            df_features = self.create_features(df)
            
            # Define feature columns
            feature_cols = [col for col in df_features.columns if col not in 
                           ['timestamp', 'yield_today', 'system_id']]
            
            X = df_features[feature_cols].values
            y = df_features['yield_today'].values
            
            # Split data (80% train, 20% test)
            split_idx = int(len(X) * 0.8)
            X_train, X_test = X[:split_idx], X[split_idx:]
            y_train, y_test = y[:split_idx], y[split_idx:]
            
            print(f"   Training samples: {len(X_train)}")
            print(f"   Test samples: {len(X_test)}")
            
            system_results = {}
            
            # Try different scalers
            for scaler_name, scaler in self.scalers.items():
                print(f"\n🔧 Using {scaler_name}...")
                
                # Scale features
                if scaler is not None:
                    X_train_scaled = scaler.fit_transform(X_train)
                    X_test_scaled = scaler.transform(X_test)
                else:
                    X_train_scaled = X_train
                    X_test_scaled = X_test
                
                scaler_results = {}
                
                # Train each algorithm
                for algo_name, algorithm in self.algorithms.items():
                    try:
                        print(f"   Training {algo_name}...")
                        
                        # Train model
                        if callable(algorithm):
                            # Custom model
                            model = algorithm(X_train_scaled, y_train)
                            model.fit(X_train_scaled, y_train)
                        else:
                            # Sklearn model
                            model = algorithm.fit(X_train_scaled, y_train)
                        
                        # Predictions
                        y_pred_train = model.predict(X_train_scaled)
                        y_pred_test = model.predict(X_test_scaled)
                        
                        # Metrics
                        train_mae = np.mean(np.abs(y_pred_train - y_train))
                        test_mae = np.mean(np.abs(y_pred_test - y_test))
                        train_rmse = np.sqrt(np.mean((y_pred_train - y_train) ** 2))
                        test_rmse = np.sqrt(np.mean((y_pred_test - y_test) ** 2))
                        
                        # R-squared
                        ss_res = np.sum((y_test - y_pred_test) ** 2)
                        ss_tot = np.sum((y_test - np.mean(y_test)) ** 2)
                        test_r2 = 1 - (ss_res / (ss_tot + 1e-8))
                        
                        # Accuracy percentage
                        mape = np.mean(np.abs((y_test - y_pred_test) / (y_test + 1e-8))) * 100
                        accuracy_pct = max(0, 100 - mape)
                        
                        scaler_results[algo_name] = {
                            'train_mae': train_mae,
                            'test_mae': test_mae,
                            'train_rmse': train_rmse,
                            'test_rmse': test_rmse,
                            'test_r2': test_r2,
                            'accuracy_percent': accuracy_pct,
                            'model': model,
                            'scaler': scaler,
                            'feature_names': feature_cols
                        }
                        
                        print(f"      Test MAE: {test_mae:.3f}, R²: {test_r2:.3f}, Accuracy: {accuracy_pct:.1f}%")
                        
                    except Exception as e:
                        print(f"      ❌ Failed: {e}")
                        continue
                
                system_results[scaler_name] = scaler_results
            
            results[system_key] = system_results
        
        return results
    
    def find_best_models(self, results: Dict[str, Any]) -> Dict[str, Dict]:
        """Find best performing models"""
        print(f"\n🏆 FINDING BEST MODELS")
        print("=" * 25)
        
        best_models = {}
        
        for system_key, system_results in results.items():
            print(f"\n📊 Best models for {system_key}:")
            
            best_mae = float('inf')
            best_config = None
            
            for scaler_name, scaler_results in system_results.items():
                for algo_name, metrics in scaler_results.items():
                    test_mae = metrics['test_mae']
                    
                    if test_mae < best_mae:
                        best_mae = test_mae
                        best_config = {
                            'system': system_key,
                            'scaler': scaler_name,
                            'algorithm': algo_name,
                            'metrics': metrics
                        }
            
            if best_config:
                best_models[system_key] = best_config
                
                print(f"   🥇 Best: {best_config['algorithm']} + {best_config['scaler']}")
                print(f"      Test MAE: {best_config['metrics']['test_mae']:.3f}")
                print(f"      Test R²: {best_config['metrics']['test_r2']:.3f}")
                print(f"      Accuracy: {best_config['metrics']['accuracy_percent']:.1f}%")
        
        return best_models
    
    def save_best_models(self, best_models: Dict[str, Dict]):
        """Save best models to disk"""
        print(f"\n💾 SAVING BEST MODELS")
        print("=" * 22)
        
        for system_key, config in best_models.items():
            system_id = int(system_key.split('_')[1])
            model_dir = f"models/final_retrained_system{system_id}"
            
            os.makedirs(model_dir, exist_ok=True)
            
            # Save model and scaler
            joblib.dump(config['metrics']['model'], f"{model_dir}/model.joblib")
            if config['metrics']['scaler'] is not None:
                joblib.dump(config['metrics']['scaler'], f"{model_dir}/scaler.joblib")
            
            # Save metadata
            metadata = {
                'system_id': system_id,
                'algorithm': config['algorithm'],
                'scaler': config['scaler'],
                'feature_names': config['metrics']['feature_names'],
                'performance': {
                    'test_mae': config['metrics']['test_mae'],
                    'test_rmse': config['metrics']['test_rmse'],
                    'test_r2': config['metrics']['test_r2'],
                    'accuracy_percent': config['metrics']['accuracy_percent']
                },
                'training_date': datetime.now().isoformat(),
                'data_source': 'complete_database_march2024_june2025'
            }
            
            with open(f"{model_dir}/metadata.json", 'w') as f:
                json.dump(metadata, f, indent=2)
            
            print(f"✅ Saved {system_key} model: {config['algorithm']} ({config['metrics']['accuracy_percent']:.1f}% accuracy)")
        
        return True

def main():
    """Main retraining function"""
    print("🚀 COMPREHENSIVE MODEL RETRAINING - FINAL VERSION")
    print("=" * 60)
    print(f"Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🎯 Using ALL correct data from March 2024 to June 2025")
    
    try:
        trainer = ComprehensiveModelTrainer()
        
        # Step 1: Get all data
        all_data = trainer.get_data_from_db()
        
        if not any(not df.empty for df in all_data.values()):
            print("❌ No data available for training")
            return False
        
        # Step 2: Train and evaluate all models
        results = trainer.train_and_evaluate_models(all_data)
        
        # Step 3: Find best models
        best_models = trainer.find_best_models(results)
        
        # Step 4: Save best models
        trainer.save_best_models(best_models)
        
        print(f"\n🎉 COMPREHENSIVE RETRAINING COMPLETED!")
        print("✅ All models retrained with correct data")
        print("🔄 Ready for comprehensive evaluation and predictions")
        
        return True
        
    except Exception as e:
        print(f"❌ Retraining failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
