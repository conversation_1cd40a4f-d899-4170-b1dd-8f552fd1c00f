#!/usr/bin/env python3
"""
Simple Enhanced Feature Engineering
Creates enhanced features using available CAMS + Open-Meteo data
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import psycopg2

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class SimpleEnhancedFeatures:
    """Simple enhanced feature engineering using available data"""
    
    def __init__(self):
        self.latitude = 38.141348260997596
        self.longitude = 24.0071653937747
        
        # Physics constants
        self.NOCT = 45.0  # Nominal Operating Cell Temperature
        self.TEMP_COEFF = -0.004  # -0.4%/°C
    
    def load_available_data(self, days_back: int = 30) -> pd.DataFrame:
        """Load available data from all sources"""
        
        logger.info(f"Loading data for last {days_back} days...")
        
        try:
            conn = psycopg2.connect(
                host='localhost',
                database='solar_prediction',
                user='postgres',
                password='postgres'
            )
            
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days_back)
            
            # Simple query combining available data
            query = """
            WITH hourly_cams AS (
                SELECT 
                    DATE_TRUNC('hour', timestamp) as hour_timestamp,
                    AVG(ghi) as cams_ghi,
                    AVG(dni) as cams_dni,
                    AVG(dhi) as cams_dhi,
                    AVG(temperature) as cams_temperature,
                    AVG(cloud_cover) as cams_cloud_cover,
                    AVG(aod) as cams_aod
                FROM cams_radiation_data
                WHERE timestamp >= %s AND timestamp <= %s
                AND ghi IS NOT NULL
                GROUP BY DATE_TRUNC('hour', timestamp)
            ),
            hourly_weather AS (
                SELECT 
                    DATE_TRUNC('hour', timestamp) as hour_timestamp,
                    AVG(shortwave_radiation) as om_ghi,
                    AVG(direct_radiation) as om_direct,
                    AVG(diffuse_radiation) as om_diffuse,
                    AVG(temperature_2m) as om_temperature,
                    AVG(relative_humidity_2m) as om_humidity,
                    AVG(cloud_cover) as om_cloud_cover
                FROM weather_data
                WHERE timestamp >= %s AND timestamp <= %s
                AND shortwave_radiation IS NOT NULL
                GROUP BY DATE_TRUNC('hour', timestamp)
            ),
            hourly_solar1 AS (
                SELECT 
                    DATE_TRUNC('hour', timestamp) as hour_timestamp,
                    AVG(ac_power) as system1_power,
                    MAX(yield_today) as system1_yield_max,
                    AVG(soc) as system1_soc
                FROM solax_data
                WHERE timestamp >= %s AND timestamp <= %s
                AND ac_power >= 0
                GROUP BY DATE_TRUNC('hour', timestamp)
            ),
            hourly_solar2 AS (
                SELECT 
                    DATE_TRUNC('hour', timestamp) as hour_timestamp,
                    MAX(yield_today) as system2_yield_max,
                    AVG(soc) as system2_soc,
                    AVG(bat_power) as system2_bat_power
                FROM solax_data2
                WHERE timestamp >= %s AND timestamp <= %s
                AND yield_today IS NOT NULL
                GROUP BY DATE_TRUNC('hour', timestamp)
            )
            SELECT 
                COALESCE(c.hour_timestamp, w.hour_timestamp, s1.hour_timestamp, s2.hour_timestamp) as timestamp,
                
                -- CAMS data (highest quality)
                c.cams_ghi,
                c.cams_dni,
                c.cams_dhi,
                c.cams_temperature,
                c.cams_cloud_cover,
                c.cams_aod,
                
                -- Open-Meteo data
                w.om_ghi,
                w.om_direct,
                w.om_diffuse,
                w.om_temperature,
                w.om_humidity,
                w.om_cloud_cover,
                
                -- Solar production
                s1.system1_power,
                s1.system1_yield_max,
                s1.system1_soc,
                s2.system2_yield_max,
                s2.system2_soc,
                s2.system2_bat_power
                
            FROM hourly_cams c
            FULL OUTER JOIN hourly_weather w ON c.hour_timestamp = w.hour_timestamp
            FULL OUTER JOIN hourly_solar1 s1 ON COALESCE(c.hour_timestamp, w.hour_timestamp) = s1.hour_timestamp
            FULL OUTER JOIN hourly_solar2 s2 ON COALESCE(c.hour_timestamp, w.hour_timestamp) = s2.hour_timestamp
            
            WHERE COALESCE(c.hour_timestamp, w.hour_timestamp, s1.hour_timestamp, s2.hour_timestamp) >= %s
            AND COALESCE(c.hour_timestamp, w.hour_timestamp, s1.hour_timestamp, s2.hour_timestamp) <= %s
            
            ORDER BY timestamp
            """
            
            # Execute with parameters (8 times for each table)
            params = [start_date, end_date] * 4
            df = pd.read_sql(query, conn, params=params)
            conn.close()
            
            logger.info(f"Loaded {len(df)} hourly records")
            return df
            
        except Exception as e:
            logger.error(f"Error loading data: {e}")
            return pd.DataFrame()
    
    def create_hybrid_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create hybrid features from available data sources"""
        
        logger.info("Creating hybrid features...")
        
        # Best available GHI (prefer CAMS, fallback to Open-Meteo)
        df['ghi_best'] = df['cams_ghi'].fillna(df['om_ghi'])
        
        # Best available temperature
        df['temperature_best'] = df['cams_temperature'].fillna(df['om_temperature'])
        
        # Best available cloud cover
        df['cloud_cover_best'] = df['cams_cloud_cover'].fillna(df['om_cloud_cover'])
        
        # Data quality indicator
        df['data_quality'] = np.where(
            df['cams_ghi'].notna(), 3,  # High quality (CAMS)
            np.where(df['om_ghi'].notna(), 2, 1)  # Medium (Open-Meteo) or Low
        )
        
        # Fill missing values with reasonable defaults
        df['ghi_best'] = df['ghi_best'].fillna(0)
        df['temperature_best'] = df['temperature_best'].fillna(20)
        df['cloud_cover_best'] = df['cloud_cover_best'].fillna(50)
        
        return df
    
    def add_solar_geometry(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add solar geometry features"""
        
        logger.info("Adding solar geometry features...")
        
        # Extract time components
        df['hour'] = df['timestamp'].dt.hour
        df['day_of_year'] = df['timestamp'].dt.dayofyear
        df['month'] = df['timestamp'].dt.month
        
        # Solar declination (simplified)
        df['solar_declination'] = 23.45 * np.sin(np.radians(360 * (284 + df['day_of_year']) / 365))
        
        # Hour angle
        df['hour_angle'] = 15 * (df['hour'] - 12)
        
        # Solar elevation (simplified)
        lat_rad = np.radians(self.latitude)
        df['solar_elevation'] = np.degrees(np.arcsin(
            np.sin(lat_rad) * np.sin(np.radians(df['solar_declination'])) +
            np.cos(lat_rad) * np.cos(np.radians(df['solar_declination'])) * 
            np.cos(np.radians(df['hour_angle']))
        ))
        
        # Solar elevation factor (0 when sun below horizon)
        df['solar_elevation_factor'] = np.maximum(0, np.sin(np.radians(df['solar_elevation'])))
        
        return df
    
    def add_physics_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add physics-based features"""
        
        logger.info("Adding physics-based features...")
        
        # Module temperature (NOCT model)
        df['module_temperature'] = np.where(
            df['ghi_best'] > 0,
            df['temperature_best'] + (df['ghi_best'] / 800.0) * (self.NOCT - 20.0),
            df['temperature_best']
        )
        
        # Temperature efficiency factor
        df['temp_efficiency_factor'] = np.maximum(
            0.5, 1 + self.TEMP_COEFF * (df['module_temperature'] - 25.0)
        )
        
        # Clear sky estimation (simplified)
        df['clear_sky_ghi_est'] = 900 * df['solar_elevation_factor'] * 0.85  # 85% atmospheric clarity
        
        # Clearness index
        df['clearness_index'] = np.where(
            df['clear_sky_ghi_est'] > 0,
            np.minimum(1.2, df['ghi_best'] / df['clear_sky_ghi_est']),
            0
        )
        
        # Atmospheric clarity (using AOD when available)
        df['atmospheric_clarity'] = np.where(
            df['cams_aod'].notna(),
            np.maximum(0.5, 1.0 - df['cams_aod']),
            0.85  # Default
        )
        
        return df
    
    def add_temporal_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add temporal features"""
        
        logger.info("Adding temporal features...")
        
        # Cyclical encoding
        df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
        df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
        
        # Rolling features (3-hour window)
        df['ghi_rolling_3h'] = df['ghi_best'].rolling(window=3, min_periods=1).mean()
        df['temp_rolling_3h'] = df['temperature_best'].rolling(window=3, min_periods=1).mean()
        df['cloud_rolling_3h'] = df['cloud_cover_best'].rolling(window=3, min_periods=1).mean()
        
        # Lag features
        df['ghi_lag_1h'] = df['ghi_best'].shift(1)
        df['ghi_lag_3h'] = df['ghi_best'].shift(3)
        df['temp_lag_1h'] = df['temperature_best'].shift(1)
        
        return df
    
    def add_interaction_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add interaction features"""
        
        logger.info("Adding interaction features...")
        
        # GHI × Solar Elevation
        df['ghi_elevation_interaction'] = df['ghi_best'] * df['solar_elevation_factor']
        
        # Temperature × Solar Elevation
        df['temp_elevation_interaction'] = df['temperature_best'] * df['solar_elevation_factor']
        
        # Cloud impact on radiation
        df['cloud_radiation_factor'] = (100 - df['cloud_cover_best']) / 100 * df['ghi_best']
        
        # Weather quality score
        df['weather_quality_score'] = (
            (df['ghi_best'] / 1000) * 0.4 +
            df['clearness_index'] * 0.3 +
            df['atmospheric_clarity'] * 0.2 +
            ((100 - df['cloud_cover_best']) / 100) * 0.1
        )
        
        return df
    
    def process_enhanced_features(self, days_back: int = 30) -> pd.DataFrame:
        """Complete enhanced feature engineering pipeline"""
        
        logger.info("🚀 Starting simple enhanced feature engineering")
        
        # Load data
        df = self.load_available_data(days_back)
        
        if df.empty:
            logger.error("No data loaded")
            return pd.DataFrame()
        
        # Apply feature engineering steps
        df = self.create_hybrid_features(df)
        df = self.add_solar_geometry(df)
        df = self.add_physics_features(df)
        df = self.add_temporal_features(df)
        df = self.add_interaction_features(df)
        
        # Remove rows with all NaN values
        df = df.dropna(how='all')
        
        logger.info(f"✅ Enhanced features created: {len(df)} records, {len(df.columns)} features")
        
        return df


def main():
    """Main execution function"""
    
    print("🌟 Simple Enhanced Feature Engineering")
    print("=" * 45)
    
    # Initialize feature engine
    engine = SimpleEnhancedFeatures()
    
    # Process features for last 30 days
    df = engine.process_enhanced_features(days_back=30)
    
    if df.empty:
        print("❌ No enhanced features created")
        return False
    
    # Save results
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_file = f"/home/<USER>/solar-prediction-project/data/enhanced_features_{timestamp}.csv"
    
    try:
        df.to_csv(output_file, index=False)
        print(f"✅ Enhanced features saved to: {output_file}")
        
        # Display summary
        print(f"\n📊 Feature Engineering Summary:")
        print(f"   Records: {len(df):,}")
        print(f"   Features: {len(df.columns)}")
        print(f"   Date range: {df['timestamp'].min()} to {df['timestamp'].max()}")
        
        # Key feature statistics
        key_features = ['ghi_best', 'temperature_best', 'clearness_index', 'weather_quality_score']
        print(f"\n📈 Key Feature Statistics:")
        for feature in key_features:
            if feature in df.columns:
                values = df[feature].dropna()
                if len(values) > 0:
                    print(f"   {feature}: {values.min():.2f} - {values.max():.2f} (mean: {values.mean():.2f})")
        
        print(f"\n🎯 Next steps:")
        print(f"   1. Train models with enhanced features")
        print(f"   2. Compare with baseline accuracy")
        print(f"   3. Implement ensemble methods")
        
        return True
        
    except Exception as e:
        print(f"❌ Error saving features: {e}")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
