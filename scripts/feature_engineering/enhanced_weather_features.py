#!/usr/bin/env python3
"""
Enhanced Weather Feature Engineering
Combines CAMS + Open-Meteo data with physics-based features for improved solar prediction
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Optional, Tuple
import psycopg2
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class EnhancedWeatherFeatureEngine:
    """Enhanced feature engineering combining multiple weather data sources"""
    
    def __init__(self):
        load_dotenv()
        self.db_config = {
            'host': os.getenv('DB_HOST', 'localhost'),
            'database': os.getenv('DB_NAME', 'solar_prediction'),
            'user': os.getenv('DB_USER', 'postgres'),
            'password': os.getenv('DB_PASSWORD', 'postgres')
        }
        
        # Solar system location (Marathon, Attica)
        self.latitude = 38.141348260997596
        self.longitude = 24.0071653937747
        
        # Physics constants
        self.NOCT = 45.0  # Nominal Operating Cell Temperature (°C)
        self.TEMP_COEFF = -0.004  # Temperature coefficient (-0.4%/°C)
        self.WIND_COOLING_FACTOR = 0.02  # 2% cooling per m/s wind
        
    def load_combined_weather_data(self, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        """Load and combine weather data from all available sources"""
        
        logger.info(f"Loading combined weather data: {start_date.date()} to {end_date.date()}")
        
        try:
            conn = psycopg2.connect(**self.db_config)
            
            # Query to combine CAMS, Open-Meteo, and solar data
            query = """
            WITH hourly_weather AS (
                -- CAMS radiation data (highest quality)
                SELECT 
                    DATE_TRUNC('hour', timestamp) as hour_timestamp,
                    AVG(ghi) as cams_ghi,
                    AVG(dni) as cams_dni,
                    AVG(dhi) as cams_dhi,
                    AVG(temperature) as cams_temperature,
                    AVG(cloud_cover) as cams_cloud_cover,
                    AVG(aod) as cams_aod
                FROM cams_radiation_data
                WHERE timestamp >= %s AND timestamp <= %s
                GROUP BY DATE_TRUNC('hour', timestamp)
            ),
            openmeteo_weather AS (
                -- Open-Meteo data (real-time and forecast)
                SELECT 
                    DATE_TRUNC('hour', timestamp) as hour_timestamp,
                    AVG(shortwave_radiation) as om_ghi,
                    AVG(direct_radiation) as om_direct,
                    AVG(diffuse_radiation) as om_diffuse,
                    AVG(temperature_2m) as om_temperature,
                    AVG(relative_humidity_2m) as om_humidity,
                    AVG(cloud_cover) as om_cloud_cover
                FROM weather_data
                WHERE timestamp >= %s AND timestamp <= %s
                GROUP BY DATE_TRUNC('hour', timestamp)
            ),
            solar_production AS (
                -- Solar production data for both systems
                SELECT 
                    DATE_TRUNC('hour', timestamp) as hour_timestamp,
                    AVG(ac_power) as system1_power,
                    AVG(yield_today) as system1_yield,
                    AVG(soc) as system1_soc
                FROM solax_data
                WHERE timestamp >= %s AND timestamp <= %s
                AND ac_power >= 0
                GROUP BY DATE_TRUNC('hour', timestamp)
            ),
            solar_production2 AS (
                -- System 2 data
                SELECT 
                    DATE_TRUNC('hour', timestamp) as hour_timestamp,
                    AVG(ac_power) as system2_power,
                    AVG(yield_today) as system2_yield,
                    AVG(soc) as system2_soc
                FROM solax_data2
                WHERE timestamp >= %s AND timestamp <= %s
                AND ac_power >= 0
                GROUP BY DATE_TRUNC('hour', timestamp)
            )
            SELECT 
                COALESCE(hw.hour_timestamp, om.hour_timestamp, sp1.hour_timestamp, sp2.hour_timestamp) as timestamp,
                
                -- CAMS radiation data (preferred)
                hw.cams_ghi,
                hw.cams_dni,
                hw.cams_dhi,
                hw.cams_temperature,
                hw.cams_cloud_cover,
                hw.cams_aod,
                
                -- Open-Meteo data (fallback/supplement)
                om.om_ghi,
                om.om_direct,
                om.om_diffuse,
                om.om_temperature,
                om.om_humidity,
                om.om_cloud_cover,
                
                -- Solar production data
                sp1.system1_power,
                sp1.system1_yield,
                sp1.system1_soc,
                sp2.system2_power,
                sp2.system2_yield,
                sp2.system2_soc
                
            FROM hourly_weather hw
            FULL OUTER JOIN openmeteo_weather om ON hw.hour_timestamp = om.hour_timestamp
            FULL OUTER JOIN solar_production sp1 ON COALESCE(hw.hour_timestamp, om.hour_timestamp) = sp1.hour_timestamp
            FULL OUTER JOIN solar_production2 sp2 ON COALESCE(hw.hour_timestamp, om.hour_timestamp) = sp2.hour_timestamp
            
            WHERE COALESCE(hw.hour_timestamp, om.hour_timestamp, sp1.hour_timestamp, sp2.hour_timestamp) >= %s
            AND COALESCE(hw.hour_timestamp, om.hour_timestamp, sp1.hour_timestamp, sp2.hour_timestamp) <= %s
            
            ORDER BY timestamp
            """
            
            # Execute query with date parameters (5 times for each table)
            params = [start_date, end_date] * 5
            df = pd.read_sql(query, conn, params=params)
            conn.close()
            
            logger.info(f"Loaded {len(df)} hourly records with combined weather data")
            return df
            
        except Exception as e:
            logger.error(f"Error loading combined weather data: {e}")
            return pd.DataFrame()
    
    def create_hybrid_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create hybrid features combining CAMS and Open-Meteo data"""
        
        logger.info("Creating hybrid weather features...")
        
        # Best available GHI (prefer CAMS, fallback to Open-Meteo)
        df['ghi_best'] = df['cams_ghi'].fillna(df['om_ghi'])
        
        # Best available temperature
        df['temperature_best'] = df['cams_temperature'].fillna(df['om_temperature'])
        
        # Best available cloud cover
        df['cloud_cover_best'] = df['cams_cloud_cover'].fillna(df['om_cloud_cover'])
        
        # Data source quality indicator
        df['data_source_quality'] = np.where(
            df['cams_ghi'].notna(), 'high',  # CAMS data available
            np.where(df['om_ghi'].notna(), 'medium', 'low')  # Open-Meteo only or missing
        )
        
        # DNI estimation when missing (using empirical relationships)
        df['dni_estimated'] = np.where(
            df['cams_dni'].notna(), 
            df['cams_dni'],
            # Estimate DNI from GHI and cloud cover
            df['ghi_best'] * (1 - df['cloud_cover_best'] / 100) * 0.8
        )
        
        # DHI estimation when missing
        df['dhi_estimated'] = np.where(
            df['cams_dhi'].notna(),
            df['cams_dhi'],
            # Estimate DHI as complement to DNI
            df['ghi_best'] - df['dni_estimated']
        )
        
        logger.info("Hybrid features created successfully")
        return df
    
    def calculate_physics_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate physics-based features for solar prediction"""
        
        logger.info("Calculating physics-based features...")
        
        # Solar geometry features
        df = self._add_solar_geometry(df)
        
        # Module temperature (NOCT model)
        df['module_temperature'] = np.where(
            (df['ghi_best'] > 0) & (df['temperature_best'].notna()),
            df['temperature_best'] + (df['ghi_best'] / 800.0) * (self.NOCT - 20.0),
            df['temperature_best']
        )
        
        # Temperature efficiency factor
        df['temp_efficiency_factor'] = np.where(
            df['module_temperature'].notna(),
            np.maximum(0.5, 1 + self.TEMP_COEFF * (df['module_temperature'] - 25.0)),
            1.0
        )
        
        # Clear sky index (when possible)
        df['clear_sky_ghi'] = self._estimate_clear_sky_ghi(df)
        df['clearness_index'] = np.where(
            (df['clear_sky_ghi'] > 0) & (df['ghi_best'] > 0),
            np.minimum(1.2, df['ghi_best'] / df['clear_sky_ghi']),
            np.nan
        )
        
        # Wind cooling effect (if humidity data available as proxy)
        df['wind_cooling_factor'] = np.where(
            df['om_humidity'].notna(),
            1.0 + ((100 - df['om_humidity']) / 100) * 0.1,  # Dry air = more cooling
            1.0
        )
        
        # Atmospheric clarity (using AOD when available)
        df['atmospheric_clarity'] = np.where(
            df['cams_aod'].notna(),
            np.maximum(0.5, 1.0 - df['cams_aod']),  # Lower AOD = clearer atmosphere
            0.85  # Default value
        )
        
        logger.info("Physics-based features calculated")
        return df
    
    def _add_solar_geometry(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add solar geometry features"""
        
        # Extract time components
        df['hour'] = df['timestamp'].dt.hour
        df['day_of_year'] = df['timestamp'].dt.dayofyear
        df['month'] = df['timestamp'].dt.month
        
        # Solar declination angle (simplified)
        df['solar_declination'] = 23.45 * np.sin(np.radians(360 * (284 + df['day_of_year']) / 365))
        
        # Hour angle
        df['hour_angle'] = 15 * (df['hour'] - 12)
        
        # Solar elevation angle (simplified)
        lat_rad = np.radians(self.latitude)
        df['solar_elevation'] = np.degrees(np.arcsin(
            np.sin(lat_rad) * np.sin(np.radians(df['solar_declination'])) +
            np.cos(lat_rad) * np.cos(np.radians(df['solar_declination'])) * 
            np.cos(np.radians(df['hour_angle']))
        ))
        
        # Solar elevation factor (0 when sun is below horizon)
        df['solar_elevation_factor'] = np.maximum(0, np.sin(np.radians(df['solar_elevation'])))
        
        # Air mass (simplified Kasten-Young formula)
        df['air_mass'] = np.where(
            df['solar_elevation'] > 0,
            1 / (np.sin(np.radians(df['solar_elevation'])) + 
                 0.50572 * (df['solar_elevation'] + 6.07995) ** -1.6364),
            np.nan
        )
        
        return df
    
    def _estimate_clear_sky_ghi(self, df: pd.DataFrame) -> pd.Series:
        """Estimate clear sky GHI using simplified model"""
        
        # Simplified clear sky model
        # GHI_clear = DNI_clear * sin(elevation) + DHI_clear
        
        # Estimate clear sky DNI (simplified)
        dni_clear = 900 * df['solar_elevation_factor'] * df['atmospheric_clarity']
        
        # Estimate clear sky DHI (typically 10-15% of clear sky GHI)
        dhi_clear = dni_clear * 0.12
        
        # Clear sky GHI
        ghi_clear = dni_clear * df['solar_elevation_factor'] + dhi_clear
        
        return np.maximum(0, ghi_clear)
    
    def add_temporal_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add advanced temporal features"""
        
        logger.info("Adding temporal features...")
        
        # Cyclical encoding for time features
        df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
        df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
        df['day_of_year_sin'] = np.sin(2 * np.pi * df['day_of_year'] / 365)
        df['day_of_year_cos'] = np.cos(2 * np.pi * df['day_of_year'] / 365)
        
        # Rolling window features (3-hour and 24-hour)
        for window in [3, 24]:
            df[f'ghi_rolling_mean_{window}h'] = df['ghi_best'].rolling(window=window, min_periods=1).mean()
            df[f'ghi_rolling_std_{window}h'] = df['ghi_best'].rolling(window=window, min_periods=1).std()
            df[f'temp_rolling_mean_{window}h'] = df['temperature_best'].rolling(window=window, min_periods=1).mean()
            df[f'cloud_rolling_mean_{window}h'] = df['cloud_cover_best'].rolling(window=window, min_periods=1).mean()
        
        # Lag features (previous hours)
        for lag in [1, 2, 3, 6, 12, 24]:
            df[f'ghi_lag_{lag}h'] = df['ghi_best'].shift(lag)
            df[f'temp_lag_{lag}h'] = df['temperature_best'].shift(lag)
        
        # Weather volatility (standard deviation over last 6 hours)
        df['weather_volatility'] = df['ghi_best'].rolling(window=6, min_periods=1).std()
        
        logger.info("Temporal features added")
        return df

    def create_interaction_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create interaction features between weather variables"""

        logger.info("Creating interaction features...")

        # GHI × Clearness Index (actual vs theoretical radiation)
        df['ghi_clearness_interaction'] = df['ghi_best'] * df['clearness_index'].fillna(0.7)

        # Temperature × Solar Elevation (heating effect)
        df['temp_elevation_interaction'] = df['temperature_best'] * df['solar_elevation_factor']

        # Cloud Cover × GHI (cloud impact on radiation)
        df['cloud_ghi_interaction'] = (100 - df['cloud_cover_best'].fillna(30)) * df['ghi_best'] / 100

        # Module Temperature × Efficiency Factor
        df['module_temp_efficiency'] = df['module_temperature'] * df['temp_efficiency_factor']

        # Atmospheric Clarity × Solar Elevation
        df['clarity_elevation_interaction'] = df['atmospheric_clarity'] * df['solar_elevation_factor']

        # Weather Quality Score (composite feature)
        df['weather_quality_score'] = (
            (df['ghi_best'] / 1000) * 0.4 +  # Normalized GHI
            (df['clearness_index'].fillna(0.7)) * 0.3 +  # Clearness
            (df['atmospheric_clarity']) * 0.2 +  # Clarity
            ((100 - df['cloud_cover_best'].fillna(30)) / 100) * 0.1  # Clear sky
        )

        logger.info("Interaction features created")
        return df

    def process_enhanced_features(self, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        """Complete enhanced feature engineering pipeline"""

        logger.info("🚀 Starting enhanced feature engineering pipeline")
        logger.info(f"📅 Period: {start_date.date()} to {end_date.date()}")

        # Step 1: Load combined weather data
        df = self.load_combined_weather_data(start_date, end_date)

        if df.empty:
            logger.error("No data loaded - aborting feature engineering")
            return pd.DataFrame()

        logger.info(f"📊 Loaded {len(df)} records")

        # Step 2: Create hybrid features
        df = self.create_hybrid_features(df)

        # Step 3: Calculate physics-based features
        df = self.calculate_physics_features(df)

        # Step 4: Add temporal features
        df = self.add_temporal_features(df)

        # Step 5: Create interaction features
        df = self.create_interaction_features(df)

        # Step 6: Data quality assessment
        quality_report = self._assess_data_quality(df)

        logger.info("✅ Enhanced feature engineering completed")
        logger.info(f"📊 Final dataset: {len(df)} records, {len(df.columns)} features")

        return df

    def _assess_data_quality(self, df: pd.DataFrame) -> Dict:
        """Assess the quality of the enhanced dataset"""

        key_features = [
            'ghi_best', 'temperature_best', 'cloud_cover_best',
            'module_temperature', 'clearness_index', 'solar_elevation',
            'weather_quality_score'
        ]

        quality_report = {
            'total_records': len(df),
            'feature_count': len(df.columns),
            'missing_data': {},
            'data_ranges': {},
            'quality_score': 0
        }

        for feature in key_features:
            if feature in df.columns:
                missing_pct = (df[feature].isna().sum() / len(df)) * 100
                quality_report['missing_data'][feature] = missing_pct

                if missing_pct < 100:
                    values = df[feature].dropna()
                    quality_report['data_ranges'][feature] = {
                        'min': float(values.min()),
                        'max': float(values.max()),
                        'mean': float(values.mean())
                    }

        # Calculate overall quality score
        avg_missing = np.mean(list(quality_report['missing_data'].values()))
        quality_report['quality_score'] = max(0, 100 - avg_missing)

        logger.info(f"📊 Data Quality Assessment:")
        logger.info(f"   Overall quality score: {quality_report['quality_score']:.1f}%")
        logger.info(f"   Average missing data: {avg_missing:.1f}%")

        return quality_report

    def save_enhanced_dataset(self, df: pd.DataFrame, output_path: str) -> bool:
        """Save the enhanced dataset to file"""

        try:
            # Save as parquet for efficiency
            df.to_parquet(output_path, index=False)
            logger.info(f"✅ Enhanced dataset saved to: {output_path}")

            # Also save a CSV sample for inspection
            csv_path = output_path.replace('.parquet', '_sample.csv')
            df.head(100).to_csv(csv_path, index=False)
            logger.info(f"📋 Sample CSV saved to: {csv_path}")

            return True

        except Exception as e:
            logger.error(f"❌ Error saving dataset: {e}")
            return False


def main():
    """Main execution function"""

    print("🌟 Enhanced Weather Feature Engineering")
    print("=" * 50)
    print("Combining CAMS + Open-Meteo data with physics-based features")
    print()

    # Initialize feature engine
    engine = EnhancedWeatherFeatureEngine()

    # Define date range (last 6 months for testing)
    end_date = datetime.now()
    start_date = end_date - timedelta(days=180)

    print(f"📅 Processing period: {start_date.date()} to {end_date.date()}")

    # Process enhanced features
    df = engine.process_enhanced_features(start_date, end_date)

    if df.empty:
        print("❌ No data processed - check database connections")
        return False

    # Save enhanced dataset
    output_path = f"/home/<USER>/solar-prediction-project/data/enhanced_weather_features_{datetime.now().strftime('%Y%m%d_%H%M%S')}.parquet"
    success = engine.save_enhanced_dataset(df, output_path)

    if success:
        print("\n✅ Enhanced feature engineering completed successfully!")
        print("\n🎯 Next steps:")
        print("   1. Train models with enhanced features")
        print("   2. Compare accuracy with baseline models")
        print("   3. Implement ensemble methods")
        print("   4. Deploy enhanced prediction system")

        return True
    else:
        print("\n❌ Failed to save enhanced dataset")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
