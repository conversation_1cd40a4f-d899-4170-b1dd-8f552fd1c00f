#!/usr/bin/env python3
"""
Phase 1: Production Database Analysis
Analyzes real PostgreSQL database with actual solar and weather data
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import psycopg2
from psycopg2.extras import RealDictCursor
import pandas as pd
from datetime import datetime, timedelta
import logging
import json

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ProductionDatabaseAnalyzer:
    """Production database analyzer for real PostgreSQL data"""
    
    def __init__(self):
        self.db_config = {
            'host': 'localhost',
            'database': 'solar_prediction',
            'user': 'postgres',
            'password': ''  # No password needed based on successful connection
        }
        
        self.analysis_results = {}
        self.schema_mapping = {
            'solax_data': {
                'timestamp': 'timestamp',
                'yield_today': 'yield_today',
                'total_yield': 'yield_total',  # Different column name
                'ac_power': 'ac_power',
                'battery_soc': 'soc',  # Different column name
                'battery_power': 'bat_power'  # Different column name
            },
            'solax_data2': {
                'timestamp': 'timestamp',
                'yield_today': 'yield_today',
                'total_yield': 'total_yield',  # Now available after enhancement
                'ac_power': 'ac_power',  # Now available after enhancement
                'battery_soc': 'soc',
                'battery_power': 'bat_power'
            },
            'weather_data': {
                'timestamp': 'timestamp',
                'ghi': 'global_horizontal_irradiance',
                'dni': 'direct_normal_irradiance',
                'dhi': 'diffuse_radiation',
                'temperature': 'temperature_2m',
                'humidity': 'relative_humidity_2m',
                'cloud_cover': 'cloud_cover',
                'solar_elevation': 'solar_elevation_angle'
            }
        }
    
    def connect_database(self):
        """Connect to production database"""
        try:
            conn = psycopg2.connect(**self.db_config)
            logger.info("✅ Connected to production database")
            return conn
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            return None
    
    def analyze_table_schema(self, cur, table_name):
        """Analyze table schema and data"""
        
        logger.info(f"📊 Analyzing {table_name}...")
        
        # Get table columns
        cur.execute(f"""
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = '{table_name}'
            ORDER BY ordinal_position
        """)
        
        columns = cur.fetchall()
        column_names = [col['column_name'] for col in columns]
        
        # Get record count
        cur.execute(f"SELECT COUNT(*) as count FROM {table_name}")
        record_count = cur.fetchone()['count']
        
        # Get date range if timestamp exists
        date_range = None
        if 'timestamp' in column_names and record_count > 0:
            cur.execute(f"SELECT MIN(timestamp) as earliest, MAX(timestamp) as latest FROM {table_name}")
            date_range = cur.fetchone()
        
        # Get recent data count (last 7 days)
        recent_count = 0
        if 'timestamp' in column_names and record_count > 0:
            cur.execute(f"""
                SELECT COUNT(*) as recent_count 
                FROM {table_name} 
                WHERE timestamp >= NOW() - INTERVAL '7 days'
            """)
            recent_count = cur.fetchone()['recent_count']
        
        # Sample recent data
        sample_data = []
        if record_count > 0:
            # Get available important columns
            important_cols = []
            schema_map = self.schema_mapping.get(table_name, {})
            
            for standard_name, actual_name in schema_map.items():
                if actual_name and actual_name in column_names:
                    important_cols.append(actual_name)
            
            if important_cols:
                cols_str = ', '.join(important_cols)
                cur.execute(f"""
                    SELECT {cols_str}
                    FROM {table_name} 
                    ORDER BY timestamp DESC 
                    LIMIT 5
                """)
                sample_data = cur.fetchall()
        
        # Store results
        self.analysis_results[table_name] = {
            'columns': column_names,
            'record_count': record_count,
            'date_range': date_range,
            'recent_count': recent_count,
            'sample_data': sample_data,
            'schema_mapping': self.schema_mapping.get(table_name, {})
        }
        
        logger.info(f"   📊 {record_count:,} records")
        if date_range:
            logger.info(f"   📅 Range: {date_range['earliest']} to {date_range['latest']}")
        logger.info(f"   ⏰ Recent (7d): {recent_count:,} records")
        
        return True
    
    def check_schema_compatibility(self):
        """Check schema compatibility for ML pipeline"""
        
        logger.info("🔧 Checking schema compatibility...")
        
        compatibility_issues = []
        recommendations = []
        
        # Check solax_data
        if 'solax_data' in self.analysis_results:
            solax1_cols = self.analysis_results['solax_data']['columns']
            
            # Check for total_yield equivalent
            if 'yield_total' in solax1_cols:
                logger.info("   ✅ solax_data: yield_total available (equivalent to total_yield)")
            else:
                compatibility_issues.append("solax_data missing total_yield equivalent")
                recommendations.append("Add total_yield column or use yield_total")
            
            # Check for battery data
            if 'soc' in solax1_cols and 'bat_power' in solax1_cols:
                logger.info("   ✅ solax_data: battery data available (soc, bat_power)")
            else:
                compatibility_issues.append("solax_data missing battery data")
        
        # Check solax_data2
        if 'solax_data2' in self.analysis_results:
            solax2_cols = self.analysis_results['solax_data2']['columns']
            
            # Note limitations
            if 'yield_total' not in solax2_cols:
                compatibility_issues.append("solax_data2 missing total yield data")
                recommendations.append("Consider using yield_today for solax_data2")
            
            if 'ac_power' not in solax2_cols:
                compatibility_issues.append("solax_data2 missing ac_power data")
                recommendations.append("Use yield_today for power estimation in solax_data2")
        
        # Check weather data
        if 'weather_data' in self.analysis_results:
            weather_cols = self.analysis_results['weather_data']['columns']
            
            required_weather = ['global_horizontal_irradiance', 'temperature_2m', 'cloud_cover']
            missing_weather = [col for col in required_weather if col not in weather_cols]
            
            if not missing_weather:
                logger.info("   ✅ weather_data: all required columns available")
            else:
                compatibility_issues.append(f"weather_data missing: {', '.join(missing_weather)}")
        
        return compatibility_issues, recommendations
    
    def analyze_data_quality(self):
        """Analyze data quality and completeness"""
        
        logger.info("📈 Analyzing data quality...")
        
        quality_report = {}
        
        for table_name, table_info in self.analysis_results.items():
            if table_info['record_count'] == 0:
                continue
            
            conn = self.connect_database()
            if not conn:
                continue
            
            try:
                cur = conn.cursor(cursor_factory=RealDictCursor)
                
                # Check for NULL values in important columns
                schema_map = table_info['schema_mapping']
                null_counts = {}
                
                for standard_name, actual_name in schema_map.items():
                    if actual_name and actual_name in table_info['columns']:
                        cur.execute(f"""
                            SELECT COUNT(*) as null_count 
                            FROM {table_name} 
                            WHERE {actual_name} IS NULL
                        """)
                        null_count = cur.fetchone()['null_count']
                        null_percentage = (null_count / table_info['record_count']) * 100
                        null_counts[standard_name] = {
                            'null_count': null_count,
                            'null_percentage': null_percentage
                        }
                
                # Check data freshness (last 24 hours)
                if 'timestamp' in table_info['columns']:
                    cur.execute(f"""
                        SELECT COUNT(*) as fresh_count 
                        FROM {table_name} 
                        WHERE timestamp >= NOW() - INTERVAL '24 hours'
                    """)
                    fresh_count = cur.fetchone()['fresh_count']
                else:
                    fresh_count = 0
                
                quality_report[table_name] = {
                    'null_counts': null_counts,
                    'data_freshness_24h': fresh_count,
                    'completeness_score': 100 - (sum(nc['null_percentage'] for nc in null_counts.values()) / len(null_counts) if null_counts else 0)
                }
                
                conn.close()
                
            except Exception as e:
                logger.error(f"❌ Quality analysis failed for {table_name}: {e}")
                conn.close()
        
        return quality_report
    
    def generate_production_report(self):
        """Generate comprehensive production database report"""
        
        logger.info("📋 Generating production database report...")
        
        # Check schema compatibility
        compatibility_issues, recommendations = self.check_schema_compatibility()
        
        # Analyze data quality
        quality_report = self.analyze_data_quality()
        
        # Determine readiness status
        has_solar_data = any(
            self.analysis_results.get(table, {}).get('record_count', 0) > 0 
            for table in ['solax_data', 'solax_data2']
        )
        
        has_weather_data = self.analysis_results.get('weather_data', {}).get('record_count', 0) > 0
        
        schema_ready = len(compatibility_issues) == 0
        
        # Calculate total records
        total_records = sum(
            table_info.get('record_count', 0) 
            for table_info in self.analysis_results.values()
        )
        
        report = {
            'analysis_date': datetime.now().isoformat(),
            'database_config': {
                'host': self.db_config['host'],
                'database': self.db_config['database'],
                'user': self.db_config['user']
            },
            'tables_analyzed': len(self.analysis_results),
            'total_records': total_records,
            'has_solar_data': has_solar_data,
            'has_weather_data': has_weather_data,
            'schema_ready': schema_ready,
            'compatibility_issues': compatibility_issues,
            'recommendations': recommendations,
            'table_details': self.analysis_results,
            'quality_report': quality_report,
            'status': 'ready_for_phase2' if (has_solar_data and has_weather_data and schema_ready) else 'needs_attention',
            'next_steps': []
        }
        
        # Add next steps
        if not has_solar_data:
            report['next_steps'].append("Import solar data")
        
        if not has_weather_data:
            report['next_steps'].append("Import weather data")
        
        if compatibility_issues:
            report['next_steps'].append("Resolve schema compatibility issues")
        
        if has_solar_data and has_weather_data and schema_ready:
            report['next_steps'].append("Proceed to Phase 2: Feature Engineering")
        
        # Save report
        os.makedirs('reports/phase1', exist_ok=True)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_path = f'reports/phase1/production_database_analysis_{timestamp}.json'
        
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        logger.info(f"📋 Report saved to: {report_path}")
        
        return report
    
    def run_full_analysis(self):
        """Run complete production database analysis"""
        
        logger.info("🚀 Starting production database analysis...")
        
        conn = self.connect_database()
        if not conn:
            return None
        
        try:
            cur = conn.cursor(cursor_factory=RealDictCursor)
            
            # Get all tables
            cur.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public'
                ORDER BY table_name
            """)
            
            tables = [row['table_name'] for row in cur.fetchall()]
            logger.info(f"📋 Found {len(tables)} tables: {', '.join(tables)}")
            
            # Analyze each important table
            important_tables = ['solax_data', 'solax_data2', 'weather_data']
            
            for table_name in important_tables:
                if table_name in tables:
                    self.analyze_table_schema(cur, table_name)
                else:
                    logger.warning(f"⚠️ Important table {table_name} not found")
            
            conn.close()
            
            # Generate comprehensive report
            report = self.generate_production_report()
            
            return report
            
        except Exception as e:
            logger.error(f"❌ Analysis failed: {e}")
            conn.close()
            return None

def print_analysis_summary(report):
    """Print analysis summary"""
    
    print("\n" + "="*80)
    print("📊 PRODUCTION DATABASE ANALYSIS SUMMARY")
    print("="*80)
    print(f"📅 Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🗄️ Database: {report['database_config']['database']}@{report['database_config']['host']}")
    print(f"📋 Tables Analyzed: {report['tables_analyzed']}")
    print(f"📊 Total Records: {report['total_records']:,}")
    print()
    
    # Table details
    print("📊 TABLE DETAILS:")
    for table_name, table_info in report['table_details'].items():
        print(f"   📋 {table_name}:")
        print(f"      Records: {table_info['record_count']:,}")
        print(f"      Recent (7d): {table_info['recent_count']:,}")
        
        if table_info['date_range']:
            print(f"      Range: {table_info['date_range']['earliest']} to {table_info['date_range']['latest']}")
        
        # Show sample data
        if table_info['sample_data']:
            print(f"      Sample (latest):")
            sample = table_info['sample_data'][0]
            for key, value in sample.items():
                if key != 'timestamp':
                    print(f"        {key}: {value}")
    print()
    
    # Data quality
    if report['quality_report']:
        print("📈 DATA QUALITY:")
        for table_name, quality in report['quality_report'].items():
            print(f"   📊 {table_name}:")
            print(f"      Completeness: {quality['completeness_score']:.1f}%")
            print(f"      Fresh data (24h): {quality['data_freshness_24h']:,} records")
    print()
    
    # Status
    print("🎯 OVERALL STATUS:")
    print(f"   Solar Data: {'✅ Available' if report['has_solar_data'] else '❌ Missing'}")
    print(f"   Weather Data: {'✅ Available' if report['has_weather_data'] else '❌ Missing'}")
    print(f"   Schema Ready: {'✅ Compatible' if report['schema_ready'] else '❌ Issues Found'}")
    print(f"   Status: {report['status']}")
    print()
    
    # Issues and recommendations
    if report['compatibility_issues']:
        print("⚠️ COMPATIBILITY ISSUES:")
        for issue in report['compatibility_issues']:
            print(f"   • {issue}")
        print()
    
    if report['recommendations']:
        print("💡 RECOMMENDATIONS:")
        for rec in report['recommendations']:
            print(f"   • {rec}")
        print()
    
    # Next steps
    print("📋 NEXT STEPS:")
    for step in report['next_steps']:
        print(f"   🚀 {step}")
    
    print("="*80)

def main():
    """Main production database analysis function"""
    
    print("📊 PHASE 1: PRODUCTION DATABASE ANALYSIS")
    print("="*60)
    print("🔗 Analyzing real PostgreSQL database")
    print("📊 Processing actual solar and weather data")
    print()
    
    try:
        # Initialize analyzer
        analyzer = ProductionDatabaseAnalyzer()
        
        # Run full analysis
        report = analyzer.run_full_analysis()
        
        if report:
            # Print summary
            print_analysis_summary(report)
            
            print(f"\n🎉 Production database analysis completed!")
            
            if report['status'] == 'ready_for_phase2':
                print("🚀 Ready to proceed to Phase 2: Feature Engineering")
                return True
            else:
                print("🔧 Issues found - address before proceeding")
                return False
        else:
            print("❌ Analysis failed")
            return False
            
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        logger.exception("Production database analysis failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
