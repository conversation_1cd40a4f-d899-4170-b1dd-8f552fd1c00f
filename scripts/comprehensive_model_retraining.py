#!/usr/bin/env python3
"""
Comprehensive Model Retraining & Evaluation
Retrain ALL models with correct data and evaluate across all time scales
"""

import os
import sys
import json
import joblib
import numpy as np
import pandas as pd
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any, List, <PERSON>ple
import psycopg2
from psycopg2.extras import RealDictCursor
import warnings
warnings.filterwarnings('ignore')

# ML Libraries (basic sklearn only)
try:
    from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
    from sklearn.linear_model import LinearRegression, Ridge
    from sklearn.tree import DecisionTreeRegressor
    from sklearn.preprocessing import StandardScaler, MinMaxScaler
    from sklearn.model_selection import train_test_split, cross_val_score
    from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
    SKLEARN_AVAILABLE = True
except ImportError:
    print("⚠️  Sklearn not available, using basic models only")
    SKLEARN_AVAILABLE = False

def get_db_connection():
    """Get PostgreSQL database connection"""
    try:
        return psycopg2.connect(
            host=os.getenv("DB_HOST", "localhost"),
            database=os.getenv("DB_NAME", "solar_prediction"),
            user=os.getenv("DB_USER", "postgres"),
            password=os.getenv("DB_PASSWORD", "postgres"),
            port=os.getenv("DB_PORT", "5432")
        )
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return None

class ComprehensiveModelTrainer:
    """Comprehensive model trainer and evaluator"""
    
    def __init__(self):
        self.conn = get_db_connection()
        if not self.conn:
            raise Exception("Cannot proceed without database connection")
        
        if SKLEARN_AVAILABLE:
            self.algorithms = {
                'RandomForest': RandomForestRegressor(n_estimators=100, random_state=42),
                'GradientBoosting': GradientBoostingRegressor(n_estimators=100, random_state=42),
                'LinearRegression': LinearRegression(),
                'Ridge': Ridge(alpha=1.0),
                'DecisionTree': DecisionTreeRegressor(random_state=42)
            }

            self.scalers = {
                'StandardScaler': StandardScaler(),
                'MinMaxScaler': MinMaxScaler()
            }
        else:
            # Fallback to basic implementations
            self.algorithms = {
                'LinearRegression': self.basic_linear_regression
            }
            self.scalers = {
                'StandardScaler': self.basic_standard_scaler
            }
        
        self.results = {}
    
    def get_all_data(self) -> Dict[str, pd.DataFrame]:
        """Get all available data for both systems"""
        print("📊 COLLECTING ALL AVAILABLE DATA")
        print("=" * 40)
        
        all_data = {}
        
        # System 1 data (solax_data)
        try:
            with self.conn.cursor(cursor_factory=RealDictCursor) as cur:
                cur.execute("""
                    SELECT 
                        timestamp,
                        yield_today,
                        EXTRACT(year FROM timestamp) as year,
                        EXTRACT(month FROM timestamp) as month,
                        EXTRACT(day FROM timestamp) as day,
                        EXTRACT(hour FROM timestamp) as hour,
                        EXTRACT(dow FROM timestamp) as day_of_week,
                        EXTRACT(doy FROM timestamp) as day_of_year
                    FROM solax_data
                    WHERE yield_today >= 0 AND yield_today < 100
                    ORDER BY timestamp
                """)
                
                results = cur.fetchall()
                if results:
                    df = pd.DataFrame([dict(row) for row in results])
                    df['system_id'] = 1
                    all_data['system_1'] = df
                    print(f"✅ System 1: {len(df)} records from {df['timestamp'].min()} to {df['timestamp'].max()}")
                else:
                    print("❌ No data for System 1")
                    all_data['system_1'] = pd.DataFrame()
        
        except Exception as e:
            print(f"❌ Error getting System 1 data: {e}")
            all_data['system_1'] = pd.DataFrame()
        
        # System 2 data (solax_data2)
        try:
            with self.conn.cursor(cursor_factory=RealDictCursor) as cur:
                cur.execute("""
                    SELECT 
                        timestamp,
                        yield_today,
                        EXTRACT(year FROM timestamp) as year,
                        EXTRACT(month FROM timestamp) as month,
                        EXTRACT(day FROM timestamp) as day,
                        EXTRACT(hour FROM timestamp) as hour,
                        EXTRACT(dow FROM timestamp) as day_of_week,
                        EXTRACT(doy FROM timestamp) as day_of_year
                    FROM solax_data2
                    WHERE yield_today >= 0 AND yield_today < 100
                    ORDER BY timestamp
                """)
                
                results = cur.fetchall()
                if results:
                    df = pd.DataFrame([dict(row) for row in results])
                    df['system_id'] = 2
                    all_data['system_2'] = df
                    print(f"✅ System 2: {len(df)} records from {df['timestamp'].min()} to {df['timestamp'].max()}")
                else:
                    print("❌ No data for System 2")
                    all_data['system_2'] = pd.DataFrame()
        
        except Exception as e:
            print(f"❌ Error getting System 2 data: {e}")
            all_data['system_2'] = pd.DataFrame()
        
        return all_data
    
    def create_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create comprehensive features for ML"""
        print(f"🔧 Creating features for {len(df)} records...")
        
        # Convert timestamp to datetime if needed
        if 'timestamp' in df.columns:
            df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        # Temporal features
        df['month'] = df['timestamp'].dt.month
        df['day'] = df['timestamp'].dt.day
        df['hour'] = df['timestamp'].dt.hour
        df['day_of_week'] = df['timestamp'].dt.dayofweek
        df['day_of_year'] = df['timestamp'].dt.dayofyear
        df['week_of_year'] = df['timestamp'].dt.isocalendar().week
        df['quarter'] = df['timestamp'].dt.quarter
        
        # Season
        df['season'] = (df['month'] - 1) // 3
        
        # Cyclical encoding
        df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
        df['day_sin'] = np.sin(2 * np.pi * df['day_of_year'] / 365)
        df['day_cos'] = np.cos(2 * np.pi * df['day_of_year'] / 365)
        df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
        
        # Solar position features
        df['solar_declination'] = 23.45 * np.sin(np.radians(360 * (284 + df['day_of_year']) / 365))
        df['day_length'] = 12 + 4 * np.sin(np.radians(df['solar_declination']))
        
        # Binary features
        df['is_weekend'] = (df['day_of_week'] >= 5).astype(int)
        df['is_peak_season'] = df['month'].isin([5, 6, 7]).astype(int)
        df['is_low_season'] = df['month'].isin([12, 1, 2]).astype(int)
        df['is_spring'] = df['month'].isin([3, 4, 5]).astype(int)
        df['is_summer'] = df['month'].isin([6, 7, 8]).astype(int)
        df['is_autumn'] = df['month'].isin([9, 10, 11]).astype(int)
        df['is_winter'] = df['month'].isin([12, 1, 2]).astype(int)
        
        # Lag features (previous values)
        df = df.sort_values('timestamp')
        df['yield_lag_1h'] = df['yield_today'].shift(1)
        df['yield_lag_24h'] = df['yield_today'].shift(24)
        df['yield_lag_7d'] = df['yield_today'].shift(24*7)
        
        # Rolling statistics
        df['yield_rolling_24h_mean'] = df['yield_today'].rolling(window=24, min_periods=1).mean()
        df['yield_rolling_24h_std'] = df['yield_today'].rolling(window=24, min_periods=1).std()
        df['yield_rolling_7d_mean'] = df['yield_today'].rolling(window=24*7, min_periods=1).mean()
        
        # Fill NaN values
        df = df.fillna(method='bfill').fillna(0)
        
        print(f"✅ Created {len(df.columns)} features")
        
        return df
    
    def prepare_datasets(self, all_data: Dict[str, pd.DataFrame]) -> Dict[str, Dict]:
        """Prepare datasets for training"""
        print(f"\n🔧 PREPARING DATASETS")
        print("=" * 25)
        
        datasets = {}
        
        for system_key, df in all_data.items():
            if df.empty:
                print(f"❌ No data for {system_key}")
                continue
            
            print(f"\n📊 Processing {system_key}...")
            
            # Create features
            df_features = self.create_features(df)
            
            # Define feature columns (exclude target and metadata)
            feature_cols = [col for col in df_features.columns if col not in 
                           ['timestamp', 'yield_today', 'system_id']]
            
            X = df_features[feature_cols]
            y = df_features['yield_today']
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, shuffle=False
            )
            
            datasets[system_key] = {
                'X_train': X_train,
                'X_test': X_test,
                'y_train': y_train,
                'y_test': y_test,
                'feature_names': feature_cols,
                'full_data': df_features
            }
            
            print(f"✅ {system_key}: {len(X_train)} train, {len(X_test)} test samples")
        
        return datasets
    
    def train_and_evaluate_models(self, datasets: Dict[str, Dict]) -> Dict[str, Any]:
        """Train and evaluate all models"""
        print(f"\n🚀 TRAINING & EVALUATING ALL MODELS")
        print("=" * 45)
        
        results = {}
        
        for system_key, data in datasets.items():
            print(f"\n📊 Training models for {system_key}...")
            
            system_results = {}
            
            X_train, X_test = data['X_train'], data['X_test']
            y_train, y_test = data['y_train'], data['y_test']
            
            # Try different scalers
            for scaler_name, scaler in self.scalers.items():
                print(f"\n🔧 Using {scaler_name}...")
                
                # Scale features
                X_train_scaled = scaler.fit_transform(X_train)
                X_test_scaled = scaler.transform(X_test)
                
                scaler_results = {}
                
                # Train each algorithm
                for algo_name, algorithm in self.algorithms.items():
                    try:
                        print(f"   Training {algo_name}...")
                        
                        # Train model
                        model = algorithm.fit(X_train_scaled, y_train)
                        
                        # Predictions
                        y_pred_train = model.predict(X_train_scaled)
                        y_pred_test = model.predict(X_test_scaled)
                        
                        # Metrics
                        train_mae = mean_absolute_error(y_train, y_pred_train)
                        test_mae = mean_absolute_error(y_test, y_pred_test)
                        train_rmse = np.sqrt(mean_squared_error(y_train, y_pred_train))
                        test_rmse = np.sqrt(mean_squared_error(y_test, y_pred_test))
                        train_r2 = r2_score(y_train, y_pred_train)
                        test_r2 = r2_score(y_test, y_pred_test)
                        
                        # Cross validation
                        cv_scores = cross_val_score(algorithm, X_train_scaled, y_train, 
                                                  cv=5, scoring='neg_mean_absolute_error')
                        cv_mae = -cv_scores.mean()
                        
                        scaler_results[algo_name] = {
                            'train_mae': train_mae,
                            'test_mae': test_mae,
                            'train_rmse': train_rmse,
                            'test_rmse': test_rmse,
                            'train_r2': train_r2,
                            'test_r2': test_r2,
                            'cv_mae': cv_mae,
                            'model': model,
                            'scaler': scaler,
                            'predictions_test': y_pred_test
                        }
                        
                        print(f"      Test MAE: {test_mae:.3f}, R²: {test_r2:.3f}")
                        
                    except Exception as e:
                        print(f"      ❌ Failed: {e}")
                        continue
                
                system_results[scaler_name] = scaler_results
            
            results[system_key] = system_results
        
        return results
    
    def find_best_models(self, results: Dict[str, Any]) -> Dict[str, Dict]:
        """Find best performing models"""
        print(f"\n🏆 FINDING BEST MODELS")
        print("=" * 25)
        
        best_models = {}
        
        for system_key, system_results in results.items():
            print(f"\n📊 Best models for {system_key}:")
            
            best_mae = float('inf')
            best_config = None
            
            for scaler_name, scaler_results in system_results.items():
                for algo_name, metrics in scaler_results.items():
                    test_mae = metrics['test_mae']
                    
                    if test_mae < best_mae:
                        best_mae = test_mae
                        best_config = {
                            'system': system_key,
                            'scaler': scaler_name,
                            'algorithm': algo_name,
                            'metrics': metrics
                        }
            
            if best_config:
                best_models[system_key] = best_config
                
                print(f"   🥇 Best: {best_config['algorithm']} + {best_config['scaler']}")
                print(f"      Test MAE: {best_config['metrics']['test_mae']:.3f}")
                print(f"      Test R²: {best_config['metrics']['test_r2']:.3f}")
                print(f"      CV MAE: {best_config['metrics']['cv_mae']:.3f}")
        
        return best_models
    
    def save_best_models(self, best_models: Dict[str, Dict], datasets: Dict[str, Dict]):
        """Save best models to disk"""
        print(f"\n💾 SAVING BEST MODELS")
        print("=" * 22)
        
        for system_key, config in best_models.items():
            system_id = int(system_key.split('_')[1])
            model_dir = f"models/retrained_system{system_id}"
            
            os.makedirs(model_dir, exist_ok=True)
            
            # Save model and scaler
            joblib.dump(config['metrics']['model'], f"{model_dir}/model.joblib")
            joblib.dump(config['metrics']['scaler'], f"{model_dir}/scaler.joblib")
            
            # Save metadata
            metadata = {
                'system_id': system_id,
                'algorithm': config['algorithm'],
                'scaler': config['scaler'],
                'feature_names': datasets[system_key]['feature_names'],
                'performance': {
                    'test_mae': config['metrics']['test_mae'],
                    'test_rmse': config['metrics']['test_rmse'],
                    'test_r2': config['metrics']['test_r2'],
                    'cv_mae': config['metrics']['cv_mae']
                },
                'training_date': datetime.now().isoformat(),
                'data_range': {
                    'start': str(datasets[system_key]['full_data']['timestamp'].min()),
                    'end': str(datasets[system_key]['full_data']['timestamp'].max()),
                    'total_records': len(datasets[system_key]['full_data'])
                }
            }
            
            with open(f"{model_dir}/metadata.json", 'w') as f:
                json.dump(metadata, f, indent=2)
            
            print(f"✅ Saved {system_key} model: {config['algorithm']} + {config['scaler']}")
        
        return True

def main():
    """Main retraining function"""
    print("🚀 COMPREHENSIVE MODEL RETRAINING & EVALUATION")
    print("=" * 60)
    print(f"Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        trainer = ComprehensiveModelTrainer()
        
        # Step 1: Get all data
        all_data = trainer.get_all_data()
        
        if not any(not df.empty for df in all_data.values()):
            print("❌ No data available for training")
            return False
        
        # Step 2: Prepare datasets
        datasets = trainer.prepare_datasets(all_data)
        
        # Step 3: Train and evaluate all models
        results = trainer.train_and_evaluate_models(datasets)
        
        # Step 4: Find best models
        best_models = trainer.find_best_models(results)
        
        # Step 5: Save best models
        trainer.save_best_models(best_models, datasets)
        
        print(f"\n🎉 COMPREHENSIVE RETRAINING COMPLETED!")
        print("✅ All models retrained with correct data")
        print("🔄 Ready for comprehensive evaluation")
        
        return True
        
    except Exception as e:
        print(f"❌ Retraining failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
