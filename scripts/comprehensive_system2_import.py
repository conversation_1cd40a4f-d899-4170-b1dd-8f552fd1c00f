#!/usr/bin/env python3
"""
Comprehensive System 2 Import
Import ALL System 2 data from all Excel/CSV files
"""

import os
import pandas as pd
import psycopg2
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def get_db_connection():
    """Get database connection"""
    try:
        return psycopg2.connect(
            host="localhost",
            database="solar_prediction",
            user="postgres",
            password="postgres",
            port="5432"
        )
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return None

def process_csv_file(file_path):
    """Process CSV file"""
    print(f"📊 Processing CSV: {os.path.basename(file_path)}")
    
    try:
        # Read CSV
        df = pd.read_csv(file_path)
        
        # Clean column names
        df.columns = df.columns.str.replace('\ufeff', '').str.replace('"', '').str.strip()
        
        print(f"   Columns: {list(df.columns)}")
        
        # Find the right columns
        time_col = None
        yield_col = None
        
        for col in df.columns:
            if 'Update time' in col or 'time' in col.lower():
                time_col = col
            elif 'Daily inverter output' in col and 'kWh' in col:
                yield_col = col
        
        if not time_col or not yield_col:
            print(f"   ❌ Could not find required columns")
            return pd.DataFrame()
        
        # Process data
        df_clean = df[[time_col, yield_col]].copy()
        df_clean.columns = ['timestamp', 'yield_today']
        
        # Convert types
        df_clean['timestamp'] = pd.to_datetime(df_clean['timestamp'], errors='coerce')
        df_clean['yield_today'] = pd.to_numeric(df_clean['yield_today'], errors='coerce')
        
        # Clean data
        df_clean = df_clean.dropna()
        df_clean = df_clean[df_clean['yield_today'] >= 0]
        df_clean = df_clean[df_clean['yield_today'] < 100]
        
        print(f"   ✅ Processed {len(df_clean)} valid records")
        print(f"   Date range: {df_clean['timestamp'].min()} to {df_clean['timestamp'].max()}")
        
        return df_clean
        
    except Exception as e:
        print(f"   ❌ Error processing CSV: {e}")
        return pd.DataFrame()

def process_excel_file(file_path):
    """Process Excel file"""
    print(f"📊 Processing Excel: {os.path.basename(file_path)}")
    
    try:
        # Try different approaches to read Excel
        approaches = [
            {"skiprows": 0},
            {"skiprows": 1},
            {"header": 0},
            {"header": 1}
        ]
        
        df = None
        for approach in approaches:
            try:
                df_temp = pd.read_excel(file_path, **approach)
                if len(df_temp) > 0 and len(df_temp.columns) > 3:
                    df = df_temp
                    break
            except:
                continue
        
        if df is None:
            print(f"   ❌ Could not read Excel file")
            return pd.DataFrame()
        
        # Clean column names
        df.columns = df.columns.astype(str).str.replace('\ufeff', '').str.replace('"', '').str.strip()
        
        print(f"   Columns: {list(df.columns)}")
        
        # Find the right columns
        time_col = None
        yield_col = None
        
        for col in df.columns:
            if 'Update time' in str(col) or 'time' in str(col).lower():
                time_col = col
            elif 'Daily inverter output' in str(col) and 'kWh' in str(col):
                yield_col = col
            elif 'Daily PV Yield' in str(col) and 'kWh' in str(col):
                if yield_col is None:  # Use as fallback
                    yield_col = col
        
        if not time_col or not yield_col:
            print(f"   ❌ Could not find required columns")
            print(f"   Available columns: {list(df.columns)}")
            return pd.DataFrame()
        
        # Process data
        df_clean = df[[time_col, yield_col]].copy()
        df_clean.columns = ['timestamp', 'yield_today']
        
        # Convert types
        df_clean['timestamp'] = pd.to_datetime(df_clean['timestamp'], errors='coerce')
        df_clean['yield_today'] = pd.to_numeric(df_clean['yield_today'], errors='coerce')
        
        # Clean data
        df_clean = df_clean.dropna()
        df_clean = df_clean[df_clean['yield_today'] >= 0]
        df_clean = df_clean[df_clean['yield_today'] < 100]
        
        print(f"   ✅ Processed {len(df_clean)} valid records")
        if len(df_clean) > 0:
            print(f"   Date range: {df_clean['timestamp'].min()} to {df_clean['timestamp'].max()}")
        
        return df_clean
        
    except Exception as e:
        print(f"   ❌ Error processing Excel: {e}")
        return pd.DataFrame()

def import_system2_data():
    """Import all System 2 data"""
    print("🚀 COMPREHENSIVE SYSTEM 2 IMPORT")
    print("=" * 40)
    
    system2_dir = "data/raw/System2"
    
    if not os.path.exists(system2_dir):
        print(f"❌ Directory not found: {system2_dir}")
        return False
    
    # Get all files
    files = os.listdir(system2_dir)
    excel_files = [f for f in files if f.endswith('.xlsx')]
    csv_files = [f for f in files if f.endswith('.csv')]
    
    print(f"📁 Found files:")
    print(f"   Excel files: {excel_files}")
    print(f"   CSV files: {csv_files}")
    
    # Process all files
    all_data = []
    
    # Process CSV files first
    for csv_file in csv_files:
        file_path = os.path.join(system2_dir, csv_file)
        df = process_csv_file(file_path)
        if not df.empty:
            all_data.append(df)
    
    # Process Excel files
    for excel_file in excel_files:
        file_path = os.path.join(system2_dir, excel_file)
        df = process_excel_file(file_path)
        if not df.empty:
            all_data.append(df)
    
    if not all_data:
        print("❌ No valid data found in any file")
        return False
    
    # Combine all data
    print(f"\n🔄 Combining data from {len(all_data)} files...")
    combined_df = pd.concat(all_data, ignore_index=True)
    
    # Remove duplicates
    print(f"   Before dedup: {len(combined_df)} records")
    combined_df = combined_df.drop_duplicates(subset=['timestamp'])
    combined_df = combined_df.sort_values('timestamp')
    print(f"   After dedup: {len(combined_df)} records")
    
    print(f"   Final date range: {combined_df['timestamp'].min()} to {combined_df['timestamp'].max()}")
    
    # Import to database
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        with conn.cursor() as cur:
            # Recreate table
            print(f"\n💾 Importing to database...")
            
            cur.execute("DROP TABLE IF EXISTS solax_data2")
            
            cur.execute("""
                CREATE TABLE solax_data2 (
                    id SERIAL PRIMARY KEY,
                    timestamp TIMESTAMP NOT NULL,
                    inverter_sn VARCHAR(50),
                    wifi_sn VARCHAR(50),
                    yield_today DECIMAL(10,2),
                    soc DECIMAL(5,2),
                    bat_power DECIMAL(10,2),
                    temperature DECIMAL(5,2),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            cur.execute("CREATE INDEX idx_solax_data2_timestamp ON solax_data2(timestamp)")
            
            # Insert data in batches
            batch_size = 1000
            total_inserted = 0
            
            for i in range(0, len(combined_df), batch_size):
                batch = combined_df.iloc[i:i + batch_size]
                
                # Prepare batch data
                batch_data = []
                for _, row in batch.iterrows():
                    batch_data.append((
                        row['timestamp'],
                        'SYSTEM2_COMPREHENSIVE',
                        'SYSTEM2_COMPREHENSIVE',
                        row['yield_today'],
                        50.0,  # soc
                        0.0,   # bat_power
                        20.0   # temperature
                    ))
                
                # Insert batch
                insert_query = """
                    INSERT INTO solax_data2 (
                        timestamp, inverter_sn, wifi_sn, yield_today, 
                        soc, bat_power, temperature
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s)
                """
                
                cur.executemany(insert_query, batch_data)
                total_inserted += len(batch_data)
                
                if total_inserted % 5000 == 0:
                    print(f"   Inserted {total_inserted} records...")
            
            conn.commit()
            
            print(f"✅ Import completed: {total_inserted} records")
            
            # Verify import
            cur.execute("""
                SELECT 
                    COUNT(*) as total,
                    MIN(timestamp) as earliest,
                    MAX(timestamp) as latest,
                    ROUND(AVG(yield_today), 2) as avg_yield
                FROM solax_data2
            """)
            
            result = cur.fetchone()
            print(f"\n📊 Import Verification:")
            print(f"   Total records: {result[0]:,}")
            print(f"   Date range: {result[1]} to {result[2]}")
            print(f"   Average yield: {result[3]} kWh")
            
            return True
            
    except Exception as e:
        print(f"❌ Database import failed: {e}")
        conn.rollback()
        return False
    
    finally:
        conn.close()

def main():
    """Main import function"""
    print("🚀 COMPREHENSIVE SYSTEM 2 DATA IMPORT")
    print("=" * 45)
    print(f"Started: {datetime.now()}")
    
    success = import_system2_data()
    
    if success:
        print(f"\n🎉 SYSTEM 2 IMPORT COMPLETED!")
        print("✅ All available data imported successfully")
    else:
        print(f"\n❌ SYSTEM 2 IMPORT FAILED!")
    
    return success

if __name__ == "__main__":
    main()
