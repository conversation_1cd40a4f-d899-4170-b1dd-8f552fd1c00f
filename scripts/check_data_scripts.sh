#!/bin/bash
echo "🔍 Database Configuration Analysis για Data Collection Scripts"
echo "============================================================="

# Έλεγχος ύπαρξης directory
if [ ! -d "/home/<USER>/solar-prediction-project/scripts/data" ]; then
    echo "❌ Directory δεν υπάρχει: /home/<USER>/solar-prediction-project/scripts/data"
    exit 1
fi

cd /home/<USER>/solar-prediction-project/scripts/data

echo ""
echo "📋 Λίστα Python Scripts:"
ls -la *.py 2>/dev/null || echo "Κανένα Python script βρέθηκε"

echo ""
echo "🔍 Database Configuration Analysis:"
echo "=================================="

for script in *.py; do
    if [ -f "$script" ]; then
        echo ""
        echo "📄 Script: $script"
        echo "-------------------"
        
        # Έλεγχος για database host
        echo "🏠 Host Configuration:"
        grep -n "host.*=" "$script" | head -5
        
        # Έλεγχος για port configuration
        echo "🔌 Port Configuration:"
        grep -n "port.*=" "$script" | head -5
        grep -n "5432\|5433" "$script" | head -5
        
        # Έλεγχος για database name
        echo "🗄️ Database Name:"
        grep -n "database.*=" "$script" | head -5
        
        # Έλεγχος για connection strings
        echo "🔗 Connection Strings:"
        grep -n "postgresql://\|connect\|engine" "$script" | head -5
        
        # Έλεγχος για environment variables
        echo "🌍 Environment Variables:"
        grep -n "DATABASE_URL\|getenv\|environ" "$script" | head -5
        
        echo "----------------------------------------"
    fi
done

echo ""
echo "🎯 Συνοπτικός Έλεγχος Ports:"
echo "=========================="
echo "Scripts που χρησιμοποιούν port 5432 (τοπική database):"
grep -l "5432" *.py 2>/dev/null || echo "Κανένα"

echo ""
echo "Scripts που χρησιμοποιούν port 5433 (Docker database):"
grep -l "5433" *.py 2>/dev/null || echo "Κανένα"

echo ""
echo "📊 Database Targets Summary:"
echo "=========================="
for script in *.py; do
    if [ -f "$script" ]; then
        echo -n "$script: "
        if grep -q "5433" "$script"; then
            echo "✅ Docker Database (5433)"
        elif grep -q "5432" "$script"; then
            echo "⚠️ Local Database (5432)"
        elif grep -q "localhost" "$script"; then
            echo "❓ Localhost (port unclear)"
        else
            echo "❓ Database target unclear"
        fi
    fi
done

