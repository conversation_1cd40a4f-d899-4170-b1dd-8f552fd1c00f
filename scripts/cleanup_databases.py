#!/usr/bin/env python3
"""
Database Cleanup Script
Remove unnecessary databases and keep only solar_prediction
"""

import psycopg2
import sys
from datetime import datetime

def check_databases():
    """Check current database status"""
    print("🔍 CHECKING CURRENT DATABASE STATUS")
    print("=" * 50)
    
    try:
        # Connect to postgres to list databases
        conn = psycopg2.connect(
            host='localhost',
            user='postgres',
            password='postgres',
            database='postgres'
        )
        cursor = conn.cursor()
        
        # List solar-related databases
        cursor.execute("""
            SELECT datname, pg_size_pretty(pg_database_size(datname)) as size
            FROM pg_database 
            WHERE datname IN ('solar_prediction', 'solarpredict_db')
            ORDER BY datname;
        """)
        
        databases = cursor.fetchall()
        
        print("🗄️  Solar-related databases:")
        for db_name, size in databases:
            print(f"   {db_name}: {size}")
        
        conn.close()
        
        # Check main database content
        if any(db[0] == 'solar_prediction' for db in databases):
            print()
            print("📊 Main database (solar_prediction) content:")
            
            conn = psycopg2.connect(
                host='localhost',
                user='postgres',
                password='postgres',
                database='solar_prediction'
            )
            cursor = conn.cursor()
            
            key_tables = ['solax_data', 'weather_data', 'predictions', 'normalized_training_data']
            total_records = 0
            
            for table in key_tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table};")
                    count = cursor.fetchone()[0]
                    total_records += count
                    
                    # Get date range
                    date_info = ""
                    try:
                        cursor.execute(f"SELECT MIN(timestamp), MAX(timestamp) FROM {table};")
                        min_date, max_date = cursor.fetchone()
                        if min_date and max_date:
                            days = (max_date - min_date).days
                            date_info = f" ({days} days coverage)"
                    except:
                        pass
                    
                    print(f"   {table}: {count:,} records{date_info}")
                    
                except Exception as e:
                    print(f"   {table}: Error - {e}")
            
            print(f"   📈 Total records: {total_records:,}")
            conn.close()
        
        return databases
        
    except Exception as e:
        print(f"❌ Error checking databases: {e}")
        return None

def check_old_database():
    """Check if old database has any important data"""
    print()
    print("🔍 CHECKING OLD DATABASE (solarpredict_db)")
    print("-" * 50)
    
    try:
        conn = psycopg2.connect(
            host='localhost',
            user='mining_user',
            password='postgres',
            database='solarpredict_db'
        )
        cursor = conn.cursor()
        
        # List tables
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
            ORDER BY table_name;
        """)
        
        tables = [row[0] for row in cursor.fetchall()]
        
        print(f"📋 Tables in solarpredict_db: {len(tables)}")
        
        total_records = 0
        for table in tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table};")
                count = cursor.fetchone()[0]
                total_records += count
                
                if count > 0:
                    print(f"   {table}: {count:,} records")
            except:
                pass
        
        print(f"📊 Total records in old database: {total_records:,}")
        
        conn.close()
        
        return total_records > 0
        
    except Exception as e:
        print(f"⚠️  Cannot access old database: {e}")
        return False

def drop_old_database():
    """Drop the old solarpredict_db database"""
    print()
    print("🗑️  DROPPING OLD DATABASE")
    print("-" * 40)
    
    try:
        # Connect to postgres database to drop solarpredict_db
        conn = psycopg2.connect(
            host='localhost',
            user='postgres',
            password='postgres',
            database='postgres'
        )
        conn.autocommit = True
        cursor = conn.cursor()
        
        # Check if database exists
        cursor.execute("""
            SELECT 1 FROM pg_database WHERE datname = 'solarpredict_db';
        """)
        
        if not cursor.fetchone():
            print("ℹ️  Database solarpredict_db does not exist")
            conn.close()
            return True
        
        # Terminate connections to the database
        print("🔌 Terminating connections to solarpredict_db...")
        cursor.execute("""
            SELECT pg_terminate_backend(pid)
            FROM pg_stat_activity
            WHERE datname = 'solarpredict_db' AND pid <> pg_backend_pid();
        """)
        
        # Drop the database
        print("🗑️  Dropping solarpredict_db database...")
        cursor.execute("DROP DATABASE solarpredict_db;")
        
        conn.close()
        
        print("✅ Old database dropped successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error dropping old database: {e}")
        return False

def verify_cleanup():
    """Verify cleanup was successful"""
    print()
    print("✅ VERIFYING CLEANUP")
    print("-" * 40)
    
    try:
        conn = psycopg2.connect(
            host='localhost',
            user='postgres',
            password='postgres',
            database='postgres'
        )
        cursor = conn.cursor()
        
        # Check remaining databases
        cursor.execute("""
            SELECT datname 
            FROM pg_database 
            WHERE datname IN ('solar_prediction', 'solarpredict_db')
            ORDER BY datname;
        """)
        
        remaining_dbs = [row[0] for row in cursor.fetchall()]
        
        print(f"🗄️  Remaining solar databases: {remaining_dbs}")
        
        if remaining_dbs == ['solar_prediction']:
            print("✅ Cleanup successful - only main database remains")
            
            # Check main database is still functional
            conn.close()
            
            conn = psycopg2.connect(
                host='localhost',
                user='postgres',
                password='postgres',
                database='solar_prediction'
            )
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM solax_data;")
            solax_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM weather_data;")
            weather_count = cursor.fetchone()[0]
            
            print(f"📊 Main database status:")
            print(f"   SolaX data: {solax_count:,} records")
            print(f"   Weather data: {weather_count:,} records")
            print("✅ Main database is functional")
            
            conn.close()
            return True
        else:
            print("⚠️  Cleanup may not be complete")
            return False
            
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        return False

def main():
    """Main cleanup function"""
    print("🧹 SOLAR PREDICTION DATABASE CLEANUP")
    print("=" * 50)
    print(f"🕐 Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Step 1: Check current status
    databases = check_databases()
    if not databases:
        print("❌ Failed to check database status")
        return
    
    # Step 2: Check old database
    has_old_data = check_old_database()
    
    print()
    print("📋 CLEANUP PLAN")
    print("-" * 30)
    print("This will:")
    print("✅ Keep main database (solar_prediction)")
    print("🗑️  Remove old database (solarpredict_db)")
    print("🎯 Result: Single, clean database setup")
    print()
    
    if has_old_data:
        print("⚠️  Note: Old database contains some data")
        print("   This data appears to be redundant/outdated")
    else:
        print("ℹ️  Old database contains no significant data")
    
    print()
    
    # Step 3: Ask for confirmation
    try:
        confirm = input("Proceed with cleanup? (yes/no): ").strip().lower()
        if confirm not in ['yes', 'y']:
            print("⏹️  Cleanup cancelled")
            return
        
        print()
        
        # Step 4: Drop old database
        if 'solarpredict_db' in [db[0] for db in databases]:
            if not drop_old_database():
                print("❌ Failed to drop old database")
                return
        else:
            print("ℹ️  Old database already removed")
        
        # Step 5: Verify cleanup
        if verify_cleanup():
            print()
            print("🎉 DATABASE CLEANUP COMPLETED SUCCESSFULLY!")
            print()
            print("📊 Final Status:")
            print("   ✅ Single database: solar_prediction")
            print("   ✅ Production system unaffected")
            print("   ✅ Data collection continues normally")
            print("   🎯 Clean, optimized setup")
        else:
            print("⚠️  Cleanup completed with warnings")
            
    except KeyboardInterrupt:
        print("\n⏹️  Cleanup cancelled by user")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"\n❌ Cleanup failed: {e}")
        sys.exit(1)
