#!/usr/bin/env python3
"""
Quick System Check
Check for performance issues without using terminals
"""

import psutil
import subprocess
import os
import sys
from datetime import datetime

def check_system_resources():
    """Check system resources"""
    print("\n" + "="*60)
    print("📊 SYSTEM RESOURCES CHECK")
    print("="*60)
    
    # CPU Usage
    cpu_percent = psutil.cpu_percent(interval=1)
    print(f"   CPU Usage: {cpu_percent}%")
    
    # Memory Usage
    memory = psutil.virtual_memory()
    print(f"   Memory Usage: {memory.percent}% ({memory.used // (1024**3):.1f}GB / {memory.total // (1024**3):.1f}GB)")
    
    # Disk Usage
    disk = psutil.disk_usage('/')
    print(f"   Disk Usage: {disk.percent}% ({disk.used // (1024**3):.1f}GB / {disk.total // (1024**3):.1f}GB)")
    
    # Load Average
    load_avg = os.getloadavg()
    print(f"   Load Average: {load_avg[0]:.2f}, {load_avg[1]:.2f}, {load_avg[2]:.2f}")

def check_processes():
    """Check for problematic processes"""
    print("\n" + "="*60)
    print("🔍 PROCESS ANALYSIS")
    print("="*60)
    
    python_processes = []
    postgres_processes = []
    zombie_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'status', 'cpu_percent', 'memory_percent', 'cmdline']):
        try:
            info = proc.info
            
            # Check for Python processes
            if 'python' in info['name'].lower():
                python_processes.append({
                    'pid': info['pid'],
                    'name': info['name'],
                    'cpu': info['cpu_percent'],
                    'memory': info['memory_percent'],
                    'cmdline': ' '.join(info['cmdline'][:3]) if info['cmdline'] else ''
                })
            
            # Check for PostgreSQL processes
            if 'postgres' in info['name'].lower():
                postgres_processes.append({
                    'pid': info['pid'],
                    'name': info['name'],
                    'cpu': info['cpu_percent'],
                    'memory': info['memory_percent']
                })
            
            # Check for zombie processes
            if info['status'] == psutil.STATUS_ZOMBIE:
                zombie_processes.append({
                    'pid': info['pid'],
                    'name': info['name']
                })
                
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    print(f"   Python processes: {len(python_processes)}")
    if python_processes:
        print("   Top Python processes:")
        for proc in sorted(python_processes, key=lambda x: x['cpu'], reverse=True)[:5]:
            print(f"      PID {proc['pid']}: {proc['name']} - CPU {proc['cpu']:.1f}% - {proc['cmdline']}")
    
    print(f"   PostgreSQL processes: {len(postgres_processes)}")
    if postgres_processes:
        for proc in postgres_processes[:3]:
            print(f"      PID {proc['pid']}: {proc['name']} - CPU {proc['cpu']:.1f}%")
    
    print(f"   Zombie processes: {len(zombie_processes)}")
    if zombie_processes:
        for proc in zombie_processes:
            print(f"      PID {proc['pid']}: {proc['name']}")

def check_database_status():
    """Check database status"""
    print("\n" + "="*60)
    print("🐘 DATABASE STATUS")
    print("="*60)
    
    try:
        # Check if PostgreSQL container is running
        result = subprocess.run(['docker', 'ps'], capture_output=True, text=True, timeout=10)
        if 'postgres' in result.stdout:
            print("   ✅ PostgreSQL container is running")
        else:
            print("   ❌ PostgreSQL container not found")
            return
        
        # Try to connect to database
        import psycopg2
        try:
            conn = psycopg2.connect(
                host='localhost',
                port=5433,
                database='solar_prediction',
                user='postgres',
                password='postgres',
                connect_timeout=5
            )
            
            cur = conn.cursor()
            
            # Check active connections
            cur.execute("SELECT count(*) FROM pg_stat_activity WHERE state = 'active'")
            active_connections = cur.fetchone()[0]
            print(f"   Active connections: {active_connections}")
            
            # Check for locks
            cur.execute("SELECT count(*) FROM pg_locks")
            locks = cur.fetchone()[0]
            print(f"   Database locks: {locks}")
            
            # Check for long-running queries
            cur.execute("""
                SELECT count(*) 
                FROM pg_stat_activity 
                WHERE state = 'active' 
                AND now() - query_start > interval '1 minute'
            """)
            long_queries = cur.fetchone()[0]
            print(f"   Long-running queries (>1min): {long_queries}")
            
            if long_queries > 0:
                print("   ⚠️ Found long-running queries!")
                cur.execute("""
                    SELECT pid, now() - query_start as duration, query 
                    FROM pg_stat_activity 
                    WHERE state = 'active' 
                    AND now() - query_start > interval '1 minute'
                    LIMIT 3
                """)
                for pid, duration, query in cur.fetchall():
                    print(f"      PID {pid}: {duration} - {query[:50]}...")
            
            conn.close()
            print("   ✅ Database connection successful")
            
        except psycopg2.Error as e:
            print(f"   ❌ Database connection failed: {e}")
            
    except subprocess.TimeoutExpired:
        print("   ❌ Docker command timed out")
    except Exception as e:
        print(f"   ❌ Error checking database: {e}")

def check_file_handles():
    """Check for file handle leaks"""
    print("\n" + "="*60)
    print("📁 FILE HANDLES CHECK")
    print("="*60)
    
    try:
        # Check open file descriptors
        current_process = psutil.Process()
        open_files = current_process.open_files()
        print(f"   Current process open files: {len(open_files)}")
        
        # Check system-wide file descriptors
        result = subprocess.run(['lsof', '|', 'wc', '-l'], shell=True, capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            total_files = result.stdout.strip()
            print(f"   System-wide open files: {total_files}")
        
    except Exception as e:
        print(f"   ⚠️ Could not check file handles: {e}")

def cleanup_recommendations():
    """Provide cleanup recommendations"""
    print("\n" + "="*60)
    print("🔧 CLEANUP RECOMMENDATIONS")
    print("="*60)
    
    print("   1. Kill any stuck Python processes:")
    print("      pkill -f 'python.*scripts/database'")
    print("")
    print("   2. Restart PostgreSQL container:")
    print("      docker restart postgres_solar")
    print("")
    print("   3. Clear temporary files:")
    print("      rm -rf /tmp/python* /tmp/postgres*")
    print("")
    print("   4. Check PostgreSQL configuration:")
    print("      - max_connections")
    print("      - shared_buffers")
    print("      - work_mem")
    print("")
    print("   5. Optimize database queries:")
    print("      - Use smaller batch sizes")
    print("      - Add LIMIT clauses")
    print("      - Use indexes")

def main():
    """Main diagnostic function"""
    print("🔍 QUICK SYSTEM CHECK")
    print(f"Time: {datetime.now()}")
    print("=" * 60)
    
    check_system_resources()
    check_processes()
    check_database_status()
    check_file_handles()
    cleanup_recommendations()
    
    print("\n" + "="*60)
    print("✅ SYSTEM CHECK COMPLETED")
    print("="*60)

if __name__ == "__main__":
    main()
