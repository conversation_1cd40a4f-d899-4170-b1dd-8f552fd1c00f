#!/bin/bash
# System Diagnostics Script
# Check for performance issues, memory leaks, zombie processes

echo "🔍 SYSTEM DIAGNOSTICS - $(date)"
echo "=================================================="

echo ""
echo "📊 SYSTEM RESOURCES:"
echo "CPU Usage:"
top -bn1 | grep "Cpu(s)" | head -1

echo ""
echo "Memory Usage:"
free -h

echo ""
echo "Disk Usage:"
df -h | grep -E "(/$|/home)"

echo ""
echo "🔍 PROCESS ANALYSIS:"
echo "Python processes:"
ps aux | grep python | grep -v grep | wc -l
echo "PostgreSQL processes:"
ps aux | grep postgres | grep -v grep | wc -l

echo ""
echo "Top CPU consumers:"
ps aux --sort=-%cpu | head -10

echo ""
echo "Top Memory consumers:"
ps aux --sort=-%mem | head -10

echo ""
echo "🧟 ZOMBIE PROCESSES:"
ps aux | awk '$8 ~ /^Z/ { print $2, $11 }' | wc -l
ps aux | awk '$8 ~ /^Z/ { print $2, $11 }'

echo ""
echo "🐘 POSTGRESQL STATUS:"
echo "PostgreSQL container status:"
docker ps | grep postgres || echo "No PostgreSQL container running"

echo ""
echo "PostgreSQL connections:"
docker exec -it postgres_solar psql -U postgres -d solar_prediction -c "SELECT count(*) as active_connections FROM pg_stat_activity WHERE state = 'active';" 2>/dev/null || echo "Cannot connect to PostgreSQL"

echo ""
echo "PostgreSQL locks:"
docker exec -it postgres_solar psql -U postgres -d solar_prediction -c "SELECT count(*) as locks FROM pg_locks;" 2>/dev/null || echo "Cannot check locks"

echo ""
echo "🔧 SYSTEM LOAD:"
uptime

echo ""
echo "📁 TEMP FILES:"
ls -la /tmp/ | grep -E "(python|postgres|psql)" | wc -l

echo ""
echo "🔍 NETWORK CONNECTIONS:"
netstat -tulpn | grep -E "(5432|5433)" | head -5

echo ""
echo "=================================================="
echo "✅ Diagnostics completed at $(date)"
