#!/usr/bin/env python3
"""
April Comparison from Excel Files
REAL DATA ANALYSIS: April 2024 vs 2025 vs 2026 prediction
Using the actual Excel files from data/raw/
"""

import os
import sys
import json
import joblib
import numpy as np
import pandas as pd
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, List, Tuple
import warnings
warnings.filterwarnings('ignore')

class AprilComparisonAnalyzer:
    """Analyzer using real Excel data files"""
    
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.load_trained_models()
    
    def load_trained_models(self):
        """Load the corrected yield-based models"""
        print("📊 Loading trained models...")
        
        for system_id in [1, 2]:
            model_dir = f"models/corrected_yield_system{system_id}"
            
            if os.path.exists(f"{model_dir}/model.joblib"):
                self.models[system_id] = joblib.load(f"{model_dir}/model.joblib")
                self.scalers[system_id] = joblib.load(f"{model_dir}/scaler.joblib")
                
                with open(f"{model_dir}/metadata.json", 'r') as f:
                    metadata = json.load(f)
                
                accuracy = metadata['performance']['accuracy_percent']
                print(f"✅ System {system_id}: {accuracy:.1f}% accuracy model loaded")
            else:
                print(f"❌ Model not found for System {system_id}")
    
    def read_excel_data(self, system_id: int, target_month: int, target_year: int) -> pd.DataFrame:
        """Read Excel data for specific system, month and year"""
        print(f"\n📊 Reading Excel data for System {system_id}, {target_month}/{target_year}...")
        
        system_dir = f"data/raw/System{system_id}"
        excel_files = [f for f in os.listdir(system_dir) if f.endswith('.xlsx')]
        
        print(f"   Found Excel files: {excel_files}")
        
        all_data = []
        
        for excel_file in excel_files:
            file_path = os.path.join(system_dir, excel_file)
            
            try:
                # Read Excel file
                df = pd.read_excel(file_path)
                
                # Check if this file contains our target date
                if 'Date' in df.columns or 'date' in df.columns:
                    date_col = 'Date' if 'Date' in df.columns else 'date'
                    
                    # Convert date column
                    df[date_col] = pd.to_datetime(df[date_col], errors='coerce')
                    
                    # Filter for target month/year
                    target_data = df[
                        (df[date_col].dt.month == target_month) & 
                        (df[date_col].dt.year == target_year)
                    ].copy()
                    
                    if len(target_data) > 0:
                        print(f"   ✅ Found {len(target_data)} records in {excel_file}")
                        all_data.append(target_data)
                    else:
                        print(f"   ⚪ No {target_month}/{target_year} data in {excel_file}")
                else:
                    print(f"   ❌ No date column found in {excel_file}")
                    
            except Exception as e:
                print(f"   ❌ Error reading {excel_file}: {e}")
        
        if all_data:
            combined_df = pd.concat(all_data, ignore_index=True)
            print(f"✅ Total records found: {len(combined_df)}")
            return combined_df
        else:
            print(f"❌ No data found for System {system_id} in {target_month}/{target_year}")
            return pd.DataFrame()
    
    def process_excel_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Process Excel data to extract daily yields"""
        if df.empty:
            return pd.DataFrame()
        
        print("🔧 Processing Excel data...")
        
        # Find date and yield columns
        date_col = None
        yield_col = None
        
        for col in df.columns:
            col_lower = col.lower()
            if 'date' in col_lower:
                date_col = col
            elif 'yield' in col_lower and 'today' in col_lower:
                yield_col = col
            elif 'yield' in col_lower and 'daily' in col_lower:
                yield_col = col
            elif col_lower in ['yield_today', 'daily_yield', 'yield']:
                yield_col = col
        
        if not date_col or not yield_col:
            print(f"❌ Could not find date or yield columns")
            print(f"   Available columns: {list(df.columns)}")
            return pd.DataFrame()
        
        print(f"   Using date column: {date_col}")
        print(f"   Using yield column: {yield_col}")
        
        # Process data
        processed_df = df[[date_col, yield_col]].copy()
        processed_df.columns = ['date', 'daily_yield']
        
        # Convert to numeric
        processed_df['daily_yield'] = pd.to_numeric(processed_df['daily_yield'], errors='coerce')
        processed_df['date'] = pd.to_datetime(processed_df['date'], errors='coerce')
        
        # Remove invalid data
        processed_df = processed_df.dropna()
        processed_df = processed_df[processed_df['daily_yield'] > 0]
        processed_df = processed_df[processed_df['daily_yield'] < 100]  # Reasonable upper limit
        
        # Group by date and take max (in case of multiple readings per day)
        daily_data = processed_df.groupby('date')['daily_yield'].max().reset_index()
        daily_data = daily_data.sort_values('date')
        
        print(f"✅ Processed to {len(daily_data)} daily records")
        print(f"   Date range: {daily_data['date'].min()} to {daily_data['date'].max()}")
        print(f"   Yield range: {daily_data['daily_yield'].min():.1f} - {daily_data['daily_yield'].max():.1f} kWh")
        
        return daily_data
    
    def get_april_data(self, year: int) -> Dict[str, pd.DataFrame]:
        """Get April data for both systems"""
        print(f"\n📊 GETTING APRIL {year} DATA FROM EXCEL FILES")
        print("=" * 50)
        
        april_data = {}
        
        for system_id in [1, 2]:
            # Read Excel data
            raw_df = self.read_excel_data(system_id, 4, year)  # April = month 4
            
            # Process data
            processed_df = self.process_excel_data(raw_df)
            
            april_data[f"system_{system_id}"] = processed_df
            
            if not processed_df.empty:
                avg_yield = processed_df['daily_yield'].mean()
                total_yield = processed_df['daily_yield'].sum()
                days = len(processed_df)
                
                print(f"✅ System {system_id}: {days} days, avg {avg_yield:.1f} kWh/day, total {total_yield:.1f} kWh")
            else:
                print(f"❌ No valid data for System {system_id}")
        
        return april_data
    
    def create_prediction_features(self, date: datetime, system_id: int) -> np.array:
        """Create features for prediction"""
        month = date.month
        day_of_year = date.timetuple().tm_yday
        day_of_week = date.weekday()
        
        # Temporal features
        season = (month - 1) // 3
        is_peak_season = 1 if month in [5, 6, 7] else 0
        is_low_season = 1 if month in [12, 1, 2] else 0
        is_weekend = 1 if day_of_week >= 5 else 0
        
        # Cyclical encoding
        month_sin = np.sin(2 * np.pi * month / 12)
        month_cos = np.cos(2 * np.pi * month / 12)
        day_sin = np.sin(2 * np.pi * day_of_year / 365)
        day_cos = np.cos(2 * np.pi * day_of_year / 365)
        
        # Solar position
        solar_declination = 23.45 * np.sin(np.radians(360 * (284 + day_of_year) / 365))
        day_length = 12 + 4 * np.sin(np.radians(solar_declination))
        
        # Use April patterns for seasonal factors
        if system_id == 1:
            seasonal_yield_factor = 59.3  # Spring average for System 1
            monthly_yield_factor = 58.7   # April average for System 1
        else:
            seasonal_yield_factor = 53.5  # Spring average for System 2 (lower)
            monthly_yield_factor = 49.1   # April average for System 2 (lower)
        
        # Calculate yield efficiency
        estimated_daily_yield = monthly_yield_factor
        yield_efficiency = estimated_daily_yield / (day_length + 1)
        
        # Feature vector
        features = [
            month,                    # month
            day_of_year,             # day_of_year
            season,                  # season
            day_of_week,             # day_of_week
            is_weekend,              # is_weekend
            month_sin,               # month_sin
            month_cos,               # month_cos
            day_sin,                 # day_sin
            day_cos,                 # day_cos
            is_peak_season,          # is_peak_season
            is_low_season,           # is_low_season
            solar_declination,       # solar_declination
            day_length,              # day_length
            yield_efficiency,        # yield_efficiency
            seasonal_yield_factor,   # seasonal_yield_factor
            monthly_yield_factor     # monthly_yield_factor
        ]
        
        return np.array([features])
    
    def predict_april_2026(self) -> Dict[str, Any]:
        """Predict April 2026 using trained models"""
        print(f"\n🔮 PREDICTING APRIL 2026")
        print("=" * 50)
        
        predictions = {}
        april_2026_dates = pd.date_range(start='2026-04-01', end='2026-04-30', freq='D')
        
        for system_id in [1, 2]:
            if system_id not in self.models:
                print(f"❌ No model available for System {system_id}")
                continue
            
            daily_predictions = []
            
            for date in april_2026_dates:
                # Create features
                features = self.create_prediction_features(date, system_id)
                
                # Scale and predict
                features_scaled = self.scalers[system_id].transform(features)
                prediction = self.models[system_id].predict(features_scaled)[0]
                
                daily_predictions.append({
                    'date': date.date(),
                    'day': date.day,
                    'predicted_yield': prediction
                })
            
            predictions[f"system_{system_id}"] = daily_predictions
            
            # Calculate statistics
            total_predicted = sum(p['predicted_yield'] for p in daily_predictions)
            avg_predicted = total_predicted / len(daily_predictions)
            
            print(f"✅ System {system_id}: {len(daily_predictions)} days predicted")
            print(f"   Average: {avg_predicted:.1f} kWh/day")
            print(f"   Total: {total_predicted:.1f} kWh")
        
        return predictions
    
    def compare_april_years(self, data_2024: Dict, data_2025: Dict, predictions_2026: Dict) -> Dict[str, Any]:
        """Compare April data across years"""
        print(f"\n📊 APRIL COMPARISON: 2024 vs 2025 vs 2026")
        print("=" * 50)
        
        comparison = {}
        
        for system_key in ['system_1', 'system_2']:
            system_id = int(system_key.split('_')[1])
            
            # Calculate averages
            avg_2024 = data_2024[system_key]['daily_yield'].mean() if not data_2024[system_key].empty else 0
            avg_2025 = data_2025[system_key]['daily_yield'].mean() if not data_2025[system_key].empty else 0
            avg_2026 = np.mean([p['predicted_yield'] for p in predictions_2026[system_key]]) if system_key in predictions_2026 else 0
            
            # Calculate totals
            total_2024 = data_2024[system_key]['daily_yield'].sum() if not data_2024[system_key].empty else 0
            total_2025 = data_2025[system_key]['daily_yield'].sum() if not data_2025[system_key].empty else 0
            total_2026 = sum(p['predicted_yield'] for p in predictions_2026[system_key]) if system_key in predictions_2026 else 0
            
            # Calculate year-over-year changes
            change_2024_to_2025 = avg_2025 - avg_2024 if avg_2024 > 0 else 0
            change_2025_to_2026 = avg_2026 - avg_2025 if avg_2025 > 0 else 0
            
            change_2024_to_2025_pct = (change_2024_to_2025 / avg_2024 * 100) if avg_2024 > 0 else 0
            change_2025_to_2026_pct = (change_2025_to_2026 / avg_2025 * 100) if avg_2025 > 0 else 0
            
            comparison[system_key] = {
                'avg_2024': avg_2024,
                'avg_2025': avg_2025,
                'avg_2026': avg_2026,
                'total_2024': total_2024,
                'total_2025': total_2025,
                'total_2026': total_2026,
                'change_2024_to_2025_kwh': change_2024_to_2025,
                'change_2025_to_2026_kwh': change_2025_to_2026,
                'change_2024_to_2025_pct': change_2024_to_2025_pct,
                'change_2025_to_2026_pct': change_2025_to_2026_pct
            }
            
            print(f"\n📊 System {system_id} April Comparison:")
            print(f"   2024 Actual:    {avg_2024:.1f} kWh/day (total: {total_2024:.1f} kWh)")
            print(f"   2025 Actual:    {avg_2025:.1f} kWh/day (total: {total_2025:.1f} kWh)")
            print(f"   2026 Predicted: {avg_2026:.1f} kWh/day (total: {total_2026:.1f} kWh)")
            print(f"   ")
            print(f"   2024→2025: {change_2024_to_2025:+.1f} kWh/day ({change_2024_to_2025_pct:+.1f}%)")
            print(f"   2025→2026: {change_2025_to_2026:+.1f} kWh/day ({change_2025_to_2026_pct:+.1f}%)")
        
        return comparison

def main():
    """Main April comparison analysis"""
    print("📊 APRIL COMPARISON: 2024 vs 2025 vs 2026 PREDICTION")
    print("Using REAL Excel data from data/raw/")
    print("=" * 70)
    print(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        analyzer = AprilComparisonAnalyzer()
        
        # 1. Get April 2024 data from Excel
        april_2024 = analyzer.get_april_data(2024)
        
        # 2. Get April 2025 data from Excel
        april_2025 = analyzer.get_april_data(2025)
        
        # 3. Predict April 2026
        predictions_2026 = analyzer.predict_april_2026()
        
        # 4. Compare across years
        comparison = analyzer.compare_april_years(april_2024, april_2025, predictions_2026)
        
        print(f"\n✅ April comparison analysis completed!")
        print("🎯 All data sourced from actual Excel files")
        
        return {
            'april_2024': april_2024,
            'april_2025': april_2025,
            'predictions_2026': predictions_2026,
            'comparison': comparison
        }
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()
