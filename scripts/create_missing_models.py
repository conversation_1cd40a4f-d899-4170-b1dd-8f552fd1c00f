#!/usr/bin/env python3
"""
Create Missing Production Models
Δημιουργεί τα 4 μοντέλα που λείπουν (hourly + yearly για κάθε σύστημα)
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project/src')

import psycopg2
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import logging
from pathlib import Path
import joblib
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import Ridge, LinearRegression
from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler
from sklearn.metrics import mean_absolute_error, r2_score, mean_absolute_percentage_error
from sklearn.model_selection import train_test_split

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MissingModelCreator:
    """Δημιουργεί τα μοντέλα που λείπουν"""
    
    def __init__(self):
        self.models_dir = Path("/home/<USER>/solar-prediction-project/models")
        self.db_connection_string = "postgresql://postgres:postgres@localhost:5433/solar_prediction"
        
        # Model configurations
        self.model_configs = {
            'hourly': {
                'algorithms': [RandomForestRegressor, GradientBoostingRegressor, LinearRegression],
                'scalers': [StandardScaler, MinMaxScaler, RobustScaler],
                'hyperparameters': {
                    'RandomForestRegressor': {'n_estimators': 100, 'max_depth': 10, 'random_state': 42},
                    'GradientBoostingRegressor': {'n_estimators': 100, 'max_depth': 6, 'random_state': 42},
                    'LinearRegression': {},
                    'Ridge': {'alpha': 1.0, 'random_state': 42}
                }
            },
            'yearly': {
                'algorithms': [Ridge, LinearRegression, RandomForestRegressor],
                'scalers': [StandardScaler, MinMaxScaler, RobustScaler],
                'hyperparameters': {
                    'Ridge': {'alpha': 1.0, 'random_state': 42},
                    'LinearRegression': {},
                    'RandomForestRegressor': {'n_estimators': 50, 'max_depth': 5, 'random_state': 42}
                }
            }
        }
    
    def connect_database(self):
        """Σύνδεση στη βάση δεδομένων"""
        try:
            conn = psycopg2.connect(self.db_connection_string)
            return conn
        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            return None
    
    def load_hourly_data(self, system_id: int):
        """Φορτώνει ωριαία δεδομένα"""
        conn = self.connect_database()
        if not conn:
            return None
        
        try:
            table = 'solax_data' if system_id == 1 else 'solax_data2'
            
            query = f"""
            WITH hourly_data AS (
                SELECT 
                    DATE_TRUNC('hour', timestamp) as hour_timestamp,
                    EXTRACT(hour FROM timestamp) as hour,
                    EXTRACT(month FROM timestamp) as month,
                    EXTRACT(doy FROM timestamp) as day_of_year,
                    EXTRACT(dow FROM timestamp) as day_of_week,
                    AVG(ac_power) as avg_ac_power,
                    MAX(yield_today) as max_yield,
                    AVG(soc) as avg_soc,
                    COUNT(*) as records_count
                FROM {table}
                WHERE timestamp >= CURRENT_DATE - INTERVAL '12 months'
                AND yield_today IS NOT NULL
                GROUP BY DATE_TRUNC('hour', timestamp), EXTRACT(hour FROM timestamp),
                         EXTRACT(month FROM timestamp), EXTRACT(doy FROM timestamp),
                         EXTRACT(dow FROM timestamp)
                HAVING COUNT(*) >= 3
            ),
            hourly_yield AS (
                SELECT *,
                    max_yield - LAG(max_yield) OVER (ORDER BY hour_timestamp) as hourly_yield_diff
                FROM hourly_data
            )
            SELECT 
                hour_timestamp,
                hour,
                month,
                day_of_year,
                day_of_week,
                avg_ac_power,
                avg_soc,
                COALESCE(hourly_yield_diff, 0) as target_yield
            FROM hourly_yield
            WHERE hourly_yield_diff IS NOT NULL
            AND hourly_yield_diff >= 0
            AND hourly_yield_diff <= 15
            ORDER BY hour_timestamp
            """
            
            df = pd.read_sql(query, conn)
            logger.info(f"Loaded {len(df)} hourly records for System {system_id}")
            return df
            
        except Exception as e:
            logger.error(f"Failed to load hourly data: {e}")
            return None
        finally:
            conn.close()
    
    def load_yearly_data(self, system_id: int):
        """Φορτώνει ετήσια δεδομένα"""
        conn = self.connect_database()
        if not conn:
            return None
        
        try:
            table = 'solax_data' if system_id == 1 else 'solax_data2'
            
            query = f"""
            SELECT 
                EXTRACT(year FROM timestamp) as year,
                AVG(yield_today) as target_yield,
                COUNT(*) as days_count,
                MIN(yield_today) as min_yield,
                MAX(yield_today) as max_yield,
                STDDEV(yield_today) as yield_std
            FROM {table}
            WHERE timestamp >= CURRENT_DATE - INTERVAL '3 years'
            AND yield_today IS NOT NULL
            AND yield_today > 5
            GROUP BY EXTRACT(year FROM timestamp)
            HAVING COUNT(*) >= 300
            ORDER BY year
            """
            
            df = pd.read_sql(query, conn)
            logger.info(f"Loaded {len(df)} yearly records for System {system_id}")
            return df
            
        except Exception as e:
            logger.error(f"Failed to load yearly data: {e}")
            return None
        finally:
            conn.close()
    
    def create_hourly_features(self, df):
        """Δημιουργεί features για ωριαία πρόβλεψη"""
        # Cyclical encoding
        df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
        df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
        df['doy_sin'] = np.sin(2 * np.pi * df['day_of_year'] / 365)
        df['doy_cos'] = np.cos(2 * np.pi * df['day_of_year'] / 365)
        
        # Solar position features
        df['seasonal_intensity'] = np.cos(2 * np.pi * (df['day_of_year'] - 172) / 365)
        df['solar_declination'] = 23.45 * np.sin(np.radians(360 * (284 + df['day_of_year']) / 365))
        
        # Time-based features
        df['is_weekend'] = (df['day_of_week'] >= 5).astype(int)
        df['is_summer'] = df['month'].isin([6, 7, 8]).astype(int)
        df['is_peak_hour'] = df['hour'].isin([11, 12, 13, 14]).astype(int)
        
        return df
    
    def create_yearly_features(self, df):
        """Δημιουργεί features για ετήσια πρόβλεψη"""
        # Normalize year
        base_year = df['year'].min()
        df['year_normalized'] = (df['year'] - base_year) / 10.0
        
        # Trend features
        df['yield_trend'] = df['target_yield'].diff().fillna(0)
        df['is_leap_year'] = ((df['year'] % 4 == 0) & (df['year'] % 100 != 0)) | (df['year'] % 400 == 0)
        
        # Stability features
        df['yield_stability'] = 1 / (1 + df['yield_std'])
        df['yield_range'] = df['max_yield'] - df['min_yield']
        
        return df
    
    def train_model(self, df, horizon: str, system_id: int):
        """Εκπαιδεύει ένα μοντέλο"""
        
        if len(df) < 10:
            logger.error(f"Insufficient data for {horizon} System {system_id}: {len(df)} records")
            return None
        
        # Prepare features
        if horizon == 'hourly':
            df = self.create_hourly_features(df)
            feature_cols = ['hour_sin', 'hour_cos', 'month_sin', 'month_cos', 
                          'seasonal_intensity', 'solar_declination', 'is_peak_hour', 'avg_soc']
        elif horizon == 'yearly':
            df = self.create_yearly_features(df)
            feature_cols = ['year_normalized', 'yield_trend', 'is_leap_year', 'yield_stability']
        
        # Filter valid features
        available_features = [col for col in feature_cols if col in df.columns]
        if not available_features:
            logger.error(f"No valid features found for {horizon} System {system_id}")
            return None
        
        X = df[available_features].fillna(0)
        y = df['target_yield']
        
        # Remove invalid values
        valid_mask = ~(X.isnull().any(axis=1) | y.isnull() | (y < 0))
        X = X[valid_mask]
        y = y[valid_mask]
        
        if len(X) < 5:
            logger.error(f"Insufficient valid data after cleaning: {len(X)} records")
            return None
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        best_model = None
        best_score = -np.inf
        best_config = None
        
        # Test different algorithms and scalers
        algorithms = self.model_configs[horizon]['algorithms']
        scalers = self.model_configs[horizon]['scalers']
        
        for algorithm in algorithms:
            for scaler_class in scalers:
                try:
                    # Scale features
                    scaler = scaler_class()
                    X_train_scaled = scaler.fit_transform(X_train)
                    X_test_scaled = scaler.transform(X_test)
                    
                    # Train model
                    hyperparams = self.model_configs[horizon]['hyperparameters'].get(
                        algorithm.__name__, {}
                    )
                    model = algorithm(**hyperparams)
                    model.fit(X_train_scaled, y_train)
                    
                    # Evaluate
                    y_pred = model.predict(X_test_scaled)
                    r2 = r2_score(y_test, y_pred)
                    mae = mean_absolute_error(y_test, y_pred)
                    
                    if r2 > best_score:
                        best_score = r2
                        best_model = model
                        best_config = {
                            'algorithm': algorithm.__name__,
                            'scaler': scaler_class.__name__,
                            'features': available_features,
                            'performance': {
                                'r2': r2,
                                'mae': mae,
                                'mape': mean_absolute_percentage_error(y_test, y_pred) * 100,
                                'samples': len(X_test)
                            },
                            'scaler_object': scaler
                        }
                    
                    logger.info(f"  {algorithm.__name__} + {scaler_class.__name__}: R²={r2:.4f}, MAE={mae:.2f}")
                    
                except Exception as e:
                    logger.warning(f"  Failed {algorithm.__name__} + {scaler_class.__name__}: {e}")
                    continue
        
        if best_model is None:
            logger.error(f"No successful model for {horizon} System {system_id}")
            return None
        
        logger.info(f"✅ Best model: {best_config['algorithm']} + {best_config['scaler']} (R²={best_score:.4f})")
        return best_model, best_config
    
    def save_model(self, model, config, horizon: str, system_id: int):
        """Αποθηκεύει το μοντέλο"""
        
        model_dir = self.models_dir / f"multi_horizon_{horizon}_system{system_id}"
        model_dir.mkdir(exist_ok=True)
        
        # Save model and scaler
        joblib.dump(model, model_dir / "model.joblib")
        joblib.dump(config['scaler_object'], model_dir / "scaler.joblib")
        
        # Save metadata
        metadata = {
            'system_id': system_id,
            'aggregation': horizon,
            'best_model': f"{config['algorithm']}_{config['scaler']}",
            'performance': config['performance'],
            'features': config['features'],
            'training_date': datetime.now().isoformat(),
            'model_type': f'multi_horizon_{horizon}_prediction'
        }
        
        with open(model_dir / "metadata.json", 'w') as f:
            json.dump(metadata, f, indent=2)
        
        logger.info(f"Model saved to {model_dir}")
        return True
    
    def create_missing_models(self):
        """Δημιουργεί όλα τα μοντέλα που λείπουν"""
        
        logger.info("🚀 Creating missing production models")
        
        missing_models = [
            ('hourly', 1), ('hourly', 2),
            ('yearly', 1), ('yearly', 2)
        ]
        
        created_models = []
        failed_models = []
        
        for horizon, system_id in missing_models:
            try:
                logger.info(f"\n📊 Creating {horizon} model for System {system_id}")
                
                # Load data
                if horizon == 'hourly':
                    df = self.load_hourly_data(system_id)
                elif horizon == 'yearly':
                    df = self.load_yearly_data(system_id)
                
                if df is None or len(df) == 0:
                    logger.error(f"No data available for {horizon} System {system_id}")
                    failed_models.append((horizon, system_id, "No data"))
                    continue
                
                # Train model
                result = self.train_model(df, horizon, system_id)
                if result is None:
                    failed_models.append((horizon, system_id, "Training failed"))
                    continue
                
                model, config = result
                
                # Save model
                success = self.save_model(model, config, horizon, system_id)
                if success:
                    created_models.append((horizon, system_id, config['performance']['r2']))
                else:
                    failed_models.append((horizon, system_id, "Save failed"))
                
            except Exception as e:
                logger.error(f"Failed to create {horizon} System {system_id}: {e}")
                failed_models.append((horizon, system_id, str(e)))
        
        # Summary
        logger.info(f"\n📋 CREATION SUMMARY:")
        logger.info(f"✅ Created models: {len(created_models)}")
        for horizon, system_id, r2 in created_models:
            logger.info(f"   - {horizon}_system{system_id}: R²={r2:.4f}")
        
        logger.info(f"❌ Failed models: {len(failed_models)}")
        for horizon, system_id, error in failed_models:
            logger.info(f"   - {horizon}_system{system_id}: {error}")
        
        return len(created_models) == 4

def main():
    """Main function"""
    print("🔧 MISSING MODELS CREATOR")
    print("=" * 50)
    
    creator = MissingModelCreator()
    success = creator.create_missing_models()
    
    if success:
        print("\n🎉 ALL MISSING MODELS CREATED SUCCESSFULLY!")
        print("✅ Hourly models: System 1 & 2")
        print("✅ Yearly models: System 1 & 2")
        print("🚀 Ready for ensemble implementation")
    else:
        print("\n⚠️ SOME MODELS FAILED TO CREATE")
        print("Check logs for details")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
