#!/usr/bin/env python3
"""
Database Migration Script: AC Power to Yield Predictions
MANDATORY: Migrates all prediction tables to use yield instead of ac_power
"""

import os
import sys
import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime

# Database connection
def get_db_connection():
    """Get PostgreSQL database connection"""
    return psycopg2.connect(
        host=os.getenv("DB_HOST", "localhost"),
        database=os.getenv("DB_NAME", "solar_prediction"),
        user=os.getenv("DB_USER", "postgres"),
        password=os.getenv("DB_PASSWORD", "postgres"),
        port=os.getenv("DB_PORT", "5432")
    )

def backup_predictions_table():
    """Backup existing predictions table before migration"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cur:
            # Create backup table
            backup_table_name = f"predictions_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            cur.execute(f"""
                CREATE TABLE {backup_table_name} AS 
                SELECT * FROM predictions;
            """)
            
            # Get count of backed up records
            cur.execute(f"SELECT COUNT(*) FROM {backup_table_name}")
            count = cur.fetchone()[0]
            
            conn.commit()
            print(f"✅ Backup created: {backup_table_name} with {count} records")
            return backup_table_name
            
    except Exception as e:
        print(f"❌ Backup failed: {e}")
        return None
    finally:
        conn.close()

def migrate_predictions_table():
    """Migrate predictions table from ac_power to yield"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cur:
            print("🔄 Starting predictions table migration...")
            
            # Step 1: Add new yield columns
            print("  Adding new yield columns...")
            cur.execute("""
                ALTER TABLE predictions 
                ADD COLUMN IF NOT EXISTS predicted_yield_kwh FLOAT,
                ADD COLUMN IF NOT EXISTS prediction_type VARCHAR(20) DEFAULT 'daily',
                ADD COLUMN IF NOT EXISTS system_id INTEGER DEFAULT 1;
            """)
            
            # Step 2: Migrate existing data (convert AC power to approximate yield)
            print("  Migrating existing data...")
            cur.execute("""
                UPDATE predictions 
                SET predicted_yield_kwh = CASE 
                    WHEN predicted_ac_power IS NOT NULL THEN predicted_ac_power / 1000.0
                    ELSE 0
                END,
                prediction_type = 'daily'
                WHERE predicted_yield_kwh IS NULL;
            """)
            
            # Step 3: Create new yield-based predictions table structure
            print("  Creating new yield predictions table...")
            cur.execute("""
                CREATE TABLE IF NOT EXISTS yield_predictions (
                    id SERIAL PRIMARY KEY,
                    timestamp TIMESTAMP NOT NULL,
                    system_id INTEGER NOT NULL DEFAULT 1,
                    prediction_type VARCHAR(20) NOT NULL DEFAULT 'daily',
                    predicted_yield_kwh FLOAT NOT NULL,
                    confidence_score FLOAT,
                    model_version VARCHAR(50),
                    features_used INTEGER,
                    input_features JSONB,
                    normalized_features JSONB,
                    prediction_time_ms FLOAT,
                    created_at TIMESTAMP DEFAULT NOW()
                );
            """)
            
            # Step 4: Copy migrated data to new table
            print("  Copying data to yield_predictions table...")
            cur.execute("""
                INSERT INTO yield_predictions (
                    timestamp, system_id, prediction_type, predicted_yield_kwh,
                    confidence_score, model_version, features_used,
                    input_features, prediction_time_ms, created_at
                )
                SELECT 
                    timestamp, 
                    COALESCE(system_id, 1),
                    COALESCE(prediction_type, 'daily'),
                    COALESCE(predicted_yield_kwh, 0),
                    confidence_score,
                    model_version,
                    features_used,
                    input_features,
                    prediction_time_ms,
                    created_at
                FROM predictions
                WHERE predicted_yield_kwh IS NOT NULL;
            """)
            
            # Step 5: Create indexes for performance
            print("  Creating indexes...")
            cur.execute("""
                CREATE INDEX IF NOT EXISTS idx_yield_predictions_timestamp 
                ON yield_predictions(timestamp);
                
                CREATE INDEX IF NOT EXISTS idx_yield_predictions_system_type 
                ON yield_predictions(system_id, prediction_type);
                
                CREATE INDEX IF NOT EXISTS idx_yield_predictions_model 
                ON yield_predictions(model_version);
            """)
            
            conn.commit()
            
            # Get counts
            cur.execute("SELECT COUNT(*) FROM predictions")
            old_count = cur.fetchone()[0]
            
            cur.execute("SELECT COUNT(*) FROM yield_predictions")
            new_count = cur.fetchone()[0]
            
            print(f"✅ Migration completed:")
            print(f"  Original predictions: {old_count}")
            print(f"  Migrated yield_predictions: {new_count}")
            
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()
    
    return True

def update_database_models():
    """Update database model files to reflect yield-based schema"""
    try:
        # Update src/models/database.py
        database_model_path = "src/models/database.py"
        
        if os.path.exists(database_model_path):
            print("🔄 Updating database models...")
            
            with open(database_model_path, 'r') as f:
                content = f.read()
            
            # Replace ac_power references with yield
            updated_content = content.replace(
                'predicted_ac_power = Column(Float, nullable=False)  # Predicted AC power (Watt)',
                'predicted_yield_kwh = Column(Float, nullable=False)  # Predicted yield (kWh)'
            )
            
            updated_content = updated_content.replace(
                'predicted_power={self.predicted_ac_power}',
                'predicted_yield={self.predicted_yield_kwh}'
            )
            
            with open(database_model_path, 'w') as f:
                f.write(updated_content)
            
            print("✅ Database models updated")
        
        # Update schemas
        schema_path = "src/models/schemas.py"
        if os.path.exists(schema_path):
            print("🔄 Updating API schemas...")
            
            with open(schema_path, 'r') as f:
                content = f.read()
            
            # Replace ac_power references with yield
            updated_content = content.replace(
                'predicted_ac_power: float = Field(description="Predicted AC power in Watts")',
                'predicted_yield_kwh: float = Field(description="Predicted yield in kWh")'
            )
            
            with open(schema_path, 'w') as f:
                f.write(updated_content)
            
            print("✅ API schemas updated")
            
    except Exception as e:
        print(f"❌ Model update failed: {e}")

def verify_migration():
    """Verify the migration was successful"""
    try:
        conn = get_db_connection()
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            print("🔍 Verifying migration...")
            
            # Check if yield_predictions table exists and has data
            cur.execute("""
                SELECT COUNT(*) as count,
                       MIN(predicted_yield_kwh) as min_yield,
                       MAX(predicted_yield_kwh) as max_yield,
                       AVG(predicted_yield_kwh) as avg_yield
                FROM yield_predictions
            """)
            
            result = cur.fetchone()
            
            if result and result['count'] > 0:
                print(f"✅ Verification successful:")
                print(f"  Records: {result['count']}")
                print(f"  Yield range: {result['min_yield']:.2f} - {result['max_yield']:.2f} kWh")
                print(f"  Average yield: {result['avg_yield']:.2f} kWh")
                return True
            else:
                print("❌ Verification failed: No data in yield_predictions table")
                return False
                
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        return False
    finally:
        conn.close()

def main():
    """Main migration process"""
    print("🚀 Starting AC Power to Yield Migration")
    print("=" * 50)
    
    # Step 1: Backup existing data
    backup_name = backup_predictions_table()
    if not backup_name:
        print("❌ Migration aborted: Backup failed")
        return False
    
    # Step 2: Migrate database schema and data
    if not migrate_predictions_table():
        print("❌ Migration aborted: Database migration failed")
        return False
    
    # Step 3: Update model files
    update_database_models()
    
    # Step 4: Verify migration
    if not verify_migration():
        print("❌ Migration completed with warnings")
        return False
    
    print("\n✅ Migration completed successfully!")
    print(f"📁 Backup available: {backup_name}")
    print("🎯 System now uses yield-based predictions")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
