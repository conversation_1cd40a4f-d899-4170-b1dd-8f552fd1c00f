#!/usr/bin/env python3
"""
Phase 4: Production Deployment
Deploys trained ML models to production environment with API endpoints
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import json
import joblib
from flask import Flask, request, jsonify
import threading
import time

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ProductionMLPipeline:
    """Production ML pipeline for solar prediction"""
    
    def __init__(self):
        self.models = {}
        self.scaler = None
        self.model_metadata = {}
        self.prediction_cache = {}
        self.cache_ttl = 300  # 5 minutes
        
        # Load latest models
        self.load_production_models()
        
    def load_production_models(self):
        """Load latest trained models"""
        
        logger.info("📦 Loading production models...")
        
        models_dir = 'models/phase3'
        
        if not os.path.exists(models_dir):
            logger.error(f"❌ Models directory not found: {models_dir}")
            return False
        
        try:
            # Find latest model files
            model_files = [f for f in os.listdir(models_dir) if f.endswith('.joblib')]
            
            if not model_files:
                logger.error("❌ No model files found")
                return False
            
            # Get latest timestamp
            timestamps = set()
            for f in model_files:
                if '_20' in f:  # Extract timestamp
                    timestamp = f.split('_')[-1].replace('.joblib', '')
                    timestamps.add(timestamp)
            
            if not timestamps:
                logger.error("❌ No valid timestamps found in model files")
                return False
            
            latest_timestamp = max(timestamps)
            logger.info(f"📅 Loading models with timestamp: {latest_timestamp}")
            
            # Load models
            model_types = ['random_forest', 'gradient_boosting']
            
            for model_type in model_types:
                model_file = f'{model_type}_model_{latest_timestamp}.joblib'
                model_path = os.path.join(models_dir, model_file)
                
                if os.path.exists(model_path):
                    self.models[model_type] = joblib.load(model_path)
                    logger.info(f"   ✅ Loaded {model_type}")
                else:
                    logger.warning(f"   ⚠️ Model file not found: {model_file}")
            
            # Load scaler
            scaler_file = f'feature_scaler_{latest_timestamp}.joblib'
            scaler_path = os.path.join(models_dir, scaler_file)
            
            if os.path.exists(scaler_path):
                self.scaler = joblib.load(scaler_path)
                logger.info(f"   ✅ Loaded feature scaler")
            else:
                logger.warning(f"   ⚠️ Scaler file not found: {scaler_file}")
            
            # Load validation results
            results_file = f'validation_results_{latest_timestamp}.json'
            results_path = os.path.join(models_dir, results_file)
            
            if os.path.exists(results_path):
                with open(results_path, 'r') as f:
                    self.model_metadata = json.load(f)
                logger.info(f"   ✅ Loaded model metadata")
            
            logger.info(f"🎉 Production models loaded successfully!")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to load models: {e}")
            return False
    
    def create_features_from_input(self, input_data):
        """Create features from input data"""
        
        # Extract basic inputs
        timestamp = pd.to_datetime(input_data.get('timestamp', datetime.now()))
        system_id = input_data.get('system_id', 'system_1')
        ghi = input_data.get('ghi', 500)
        temperature = input_data.get('temperature', 25)
        cloud_cover = input_data.get('cloud_cover', 20)
        battery_soc = input_data.get('battery_soc', 50)
        
        # Create feature vector (matching training features)
        hour = timestamp.hour
        day_of_year = timestamp.timetuple().tm_yday
        month = timestamp.month
        
        # Solar simulation
        if 6 <= hour <= 18:
            solar_elevation = 60 * np.sin(np.pi * (hour - 6) / 12)
            is_daytime = 1
        else:
            solar_elevation = 0
            is_daytime = 0
        
        features = {
            'hour': hour,
            'day_of_year': day_of_year,
            'month': month,
            'hour_sin': np.sin(2 * np.pi * hour / 24),
            'hour_cos': np.cos(2 * np.pi * hour / 24),
            'day_of_year_sin': np.sin(2 * np.pi * day_of_year / 365),
            'day_of_year_cos': np.cos(2 * np.pi * day_of_year / 365),
            'is_summer': 1 if month in [6, 7, 8] else 0,
            'is_winter': 1 if month in [12, 1, 2] else 0,
            'is_daytime': is_daytime,
            'is_peak_sun': 1 if 10 <= hour <= 14 else 0,
            'solar_elevation_approx': solar_elevation,
            'solar_elevation_normalized': solar_elevation / 90,
            'time_from_solar_noon': abs(hour - 12),
            'ghi_normalized': ghi / 1000,
            'cloud_cover_normalized': cloud_cover / 100,
            'clear_sky_factor': 1 - cloud_cover / 100,
            'temp_normalized': (temperature - 0) / 40,
            'temp_optimality': max(0, 1 - abs(temperature - 25) / 25),
            'battery_soc_normalized': battery_soc / 100,
            'ac_power_normalized': 0.5,  # Default value
            'is_system_1': 1 if system_id == 'system_1' else 0,
            'is_system_2': 1 if system_id == 'system_2' else 0,
            'solar_weather_score': (solar_elevation / 90) * (1 - cloud_cover / 100),
            'optimal_conditions': (solar_elevation / 90) * (1 - cloud_cover / 100) * max(0, 1 - abs(temperature - 25) / 25),
            'battery_solar_potential': (1 - battery_soc / 100) * (solar_elevation / 90)
        }
        
        return features
    
    def predict_yield(self, input_data):
        """Make yield prediction using ensemble of models"""
        
        try:
            # Create cache key
            cache_key = f"{input_data.get('timestamp')}_{input_data.get('system_id')}"
            
            # Check cache
            if cache_key in self.prediction_cache:
                cache_entry = self.prediction_cache[cache_key]
                if time.time() - cache_entry['timestamp'] < self.cache_ttl:
                    logger.info("📋 Returning cached prediction")
                    return cache_entry['prediction']
            
            # Create features
            features = self.create_features_from_input(input_data)
            
            # Convert to DataFrame
            feature_df = pd.DataFrame([features])
            
            # Scale features
            if self.scaler is None:
                logger.error("❌ Feature scaler not loaded")
                return None
            
            feature_scaled = self.scaler.transform(feature_df)
            
            # Make predictions with all models
            predictions = {}
            
            for model_name, model in self.models.items():
                pred = model.predict(feature_scaled)[0]
                predictions[model_name] = max(0, pred)  # Ensure non-negative
            
            # Create ensemble prediction (weighted average)
            if len(predictions) > 1:
                # Use weights from training if available
                weights = self.model_metadata.get('ensemble', {}).get('weights', {})
                
                if weights:
                    ensemble_pred = sum(weights.get(name, 1/len(predictions)) * pred 
                                      for name, pred in predictions.items())
                else:
                    ensemble_pred = np.mean(list(predictions.values()))
            else:
                ensemble_pred = list(predictions.values())[0] if predictions else 0
            
            # Calculate confidence based on model agreement
            if len(predictions) > 1:
                pred_std = np.std(list(predictions.values()))
                confidence = max(0.5, 1 - pred_std / max(predictions.values()))
            else:
                confidence = 0.8
            
            # Prepare result
            result = {
                'prediction': round(ensemble_pred, 2),
                'confidence': round(confidence, 3),
                'individual_predictions': {k: round(v, 2) for k, v in predictions.items()},
                'model_metadata': {
                    'models_used': list(predictions.keys()),
                    'feature_count': len(features),
                    'prediction_timestamp': datetime.now().isoformat()
                }
            }
            
            # Cache result
            self.prediction_cache[cache_key] = {
                'prediction': result,
                'timestamp': time.time()
            }
            
            logger.info(f"🎯 Prediction: {result['prediction']} kWh (confidence: {result['confidence']})")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Prediction failed: {e}")
            return None
    
    def get_model_info(self):
        """Get information about loaded models"""
        
        info = {
            'models_loaded': list(self.models.keys()),
            'model_count': len(self.models),
            'scaler_loaded': self.scaler is not None,
            'metadata_available': bool(self.model_metadata),
            'cache_size': len(self.prediction_cache),
            'model_performance': {}
        }
        
        # Add performance metrics if available
        for model_name in self.models.keys():
            if model_name in self.model_metadata:
                model_perf = self.model_metadata[model_name]
                info['model_performance'][model_name] = {
                    'validation_mae': model_perf.get('val_mae', 'N/A'),
                    'validation_r2': model_perf.get('val_r2', 'N/A'),
                    'cv_mae': model_perf.get('cv_mae_mean', 'N/A'),
                    'target_met': model_perf.get('target_met', False)
                }
        
        return info

# Initialize global pipeline
ml_pipeline = ProductionMLPipeline()

# Flask API
app = Flask(__name__)

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'models_loaded': len(ml_pipeline.models),
        'scaler_loaded': ml_pipeline.scaler is not None
    })

@app.route('/api/v1/predict', methods=['POST'])
def predict_endpoint():
    """Prediction endpoint"""
    
    try:
        # Get input data
        input_data = request.get_json()
        
        if not input_data:
            return jsonify({'error': 'No input data provided'}), 400
        
        # Make prediction
        result = ml_pipeline.predict_yield(input_data)
        
        if result is None:
            return jsonify({'error': 'Prediction failed'}), 500
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"❌ API prediction failed: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/v1/model/info', methods=['GET'])
def model_info_endpoint():
    """Model information endpoint"""
    
    try:
        info = ml_pipeline.get_model_info()
        return jsonify(info)
        
    except Exception as e:
        logger.error(f"❌ Model info failed: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/v1/predict/batch', methods=['POST'])
def batch_predict_endpoint():
    """Batch prediction endpoint"""
    
    try:
        # Get input data
        input_data = request.get_json()
        
        if not input_data or 'predictions' not in input_data:
            return jsonify({'error': 'No predictions array provided'}), 400
        
        predictions = input_data['predictions']
        results = []
        
        for pred_input in predictions:
            result = ml_pipeline.predict_yield(pred_input)
            if result:
                results.append(result)
            else:
                results.append({'error': 'Prediction failed'})
        
        return jsonify({
            'batch_results': results,
            'total_predictions': len(results),
            'successful_predictions': len([r for r in results if 'error' not in r])
        })
        
    except Exception as e:
        logger.error(f"❌ Batch prediction failed: {e}")
        return jsonify({'error': str(e)}), 500

def create_deployment_summary():
    """Create deployment summary"""
    
    summary = {
        'deployment_date': datetime.now().isoformat(),
        'deployment_status': 'active',
        'models_deployed': list(ml_pipeline.models.keys()),
        'api_endpoints': [
            '/health',
            '/api/v1/predict',
            '/api/v1/model/info',
            '/api/v1/predict/batch'
        ],
        'model_performance': ml_pipeline.model_metadata,
        'deployment_config': {
            'cache_ttl': ml_pipeline.cache_ttl,
            'prediction_timeout': 30,
            'max_batch_size': 100
        }
    }
    
    # Save deployment summary
    os.makedirs('deployment', exist_ok=True)
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    with open(f'deployment/deployment_summary_{timestamp}.json', 'w') as f:
        json.dump(summary, f, indent=2, default=str)
    
    return summary, timestamp

def print_deployment_summary(summary, timestamp):
    """Print deployment summary"""
    
    print("\n" + "="*80)
    print("📦 PHASE 4: PRODUCTION DEPLOYMENT SUMMARY")
    print("="*80)
    print(f"📅 Deployment Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🚀 Status: {summary['deployment_status'].upper()}")
    print()
    
    # Models deployed
    print("🤖 MODELS DEPLOYED:")
    for model in summary['models_deployed']:
        perf = summary['model_performance'].get(model, {})
        mae = perf.get('val_mae', 'N/A')
        r2 = perf.get('val_r2', 'N/A')
        print(f"   ✅ {model.upper()}: MAE {mae} kWh, R² {r2}")
    print()
    
    # API endpoints
    print("🌐 API ENDPOINTS:")
    for endpoint in summary['api_endpoints']:
        print(f"   📡 {endpoint}")
    print()
    
    # Configuration
    print("⚙️ DEPLOYMENT CONFIG:")
    config = summary['deployment_config']
    print(f"   🕒 Cache TTL: {config['cache_ttl']} seconds")
    print(f"   ⏱️ Timeout: {config['prediction_timeout']} seconds")
    print(f"   📊 Max Batch: {config['max_batch_size']} predictions")
    print()
    
    # Files created
    print("📄 DEPLOYMENT FILES:")
    print(f"   📋 Summary: deployment/deployment_summary_{timestamp}.json")
    print(f"   🤖 Models: models/phase3/ (loaded)")
    print(f"   📡 API: Flask application ready")
    print()
    
    print("🎯 DEPLOYMENT STATUS: ✅ SUCCESS")
    print("🚀 Production ML API is ready for use!")
    print("📡 Start API server with: python scripts/phase4_production_deployment.py")
    
    print("="*80)

def main():
    """Main deployment function"""
    
    print("📦 PHASE 4: PRODUCTION DEPLOYMENT")
    print("="*60)
    print("🚀 Deploying trained ML models to production")
    print("📡 Creating REST API for solar predictions")
    print()
    
    try:
        # Create deployment summary
        summary, timestamp = create_deployment_summary()
        
        # Print summary
        print_deployment_summary(summary, timestamp)
        
        print(f"\n🎉 Phase 4 production deployment completed!")
        print("📡 API server ready to start")
        
        # Ask if user wants to start API server
        print("\n🚀 Start API server? (y/n): ", end="")
        
        return True
        
    except Exception as e:
        print(f"❌ Deployment failed: {e}")
        logger.exception("Deployment failed")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🚀 Starting Flask API server...")
        print("📡 API will be available at: http://localhost:5000")
        print("🔍 Health check: http://localhost:5000/health")
        print("📊 Model info: http://localhost:5000/api/v1/model/info")
        print("\n⚠️ Press Ctrl+C to stop the server")
        
        try:
            app.run(host='0.0.0.0', port=5000, debug=False)
        except KeyboardInterrupt:
            print("\n🛑 API server stopped")
    
    sys.exit(0 if success else 1)
