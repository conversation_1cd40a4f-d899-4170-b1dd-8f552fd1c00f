#!/usr/bin/env python3
"""
Phase 2: Simple Feature Engineering
Creates essential features from real solar and weather data
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import psycopg2
from psycopg2.extras import RealDictCursor
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SimpleFeatureEngineer:
    """Simple feature engineering for solar prediction"""
    
    def __init__(self):
        self.db_config = {
            'host': 'localhost',
            'database': 'solar_prediction',
            'user': 'postgres',
            'password': ''
        }
        
        self.features_created = []
    
    def connect_database(self):
        """Connect to database"""
        try:
            conn = psycopg2.connect(**self.db_config)
            logger.info("✅ Connected to database")
            return conn
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            return None
    
    def load_sample_data(self, days=7):
        """Load sample data for feature engineering"""
        
        logger.info(f"📊 Loading {days} days of data...")
        
        conn = self.connect_database()
        if not conn:
            return None
        
        try:
            query = f"""
            SELECT 
                s1.timestamp,
                'system_1' as system_id,
                s1.yield_today,
                s1.yield_total as total_yield,
                s1.ac_power,
                s1.soc as battery_soc,
                s1.bat_power as battery_power,
                w.temperature_2m as ambient_temperature,
                w.cloud_cover,
                w.global_horizontal_irradiance as ghi
            FROM solax_data s1
            LEFT JOIN weather_data w ON 
                DATE_TRUNC('hour', s1.timestamp) = DATE_TRUNC('hour', w.timestamp)
            WHERE s1.timestamp >= NOW() - INTERVAL '{days} days'
            
            UNION ALL
            
            SELECT 
                s2.timestamp,
                'system_2' as system_id,
                s2.yield_today,
                s2.total_yield,
                s2.ac_power,
                s2.soc as battery_soc,
                s2.bat_power as battery_power,
                w.temperature_2m as ambient_temperature,
                w.cloud_cover,
                w.global_horizontal_irradiance as ghi
            FROM solax_data2 s2
            LEFT JOIN weather_data w ON 
                DATE_TRUNC('hour', s2.timestamp) = DATE_TRUNC('hour', w.timestamp)
            WHERE s2.timestamp >= NOW() - INTERVAL '{days} days'
            
            ORDER BY timestamp, system_id
            """
            
            df = pd.read_sql(query, conn)
            conn.close()
            
            logger.info(f"📊 Loaded {len(df):,} records")
            
            # Convert timestamp
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            
            # Basic cleaning
            df = df.dropna(subset=['timestamp', 'system_id'])
            
            logger.info(f"📊 After cleaning: {len(df):,} records")
            
            return df
            
        except Exception as e:
            logger.error(f"❌ Failed to load data: {e}")
            conn.close()
            return None
    
    def add_temporal_features(self, df):
        """Add basic temporal features"""
        
        logger.info("⏰ Adding temporal features...")
        
        # Basic time components
        df['hour'] = df['timestamp'].dt.hour
        df['day_of_year'] = df['timestamp'].dt.dayofyear
        df['month'] = df['timestamp'].dt.month
        df['weekday'] = df['timestamp'].dt.weekday
        
        # Cyclical encoding
        df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
        df['day_of_year_sin'] = np.sin(2 * np.pi * df['day_of_year'] / 365)
        df['day_of_year_cos'] = np.cos(2 * np.pi * df['day_of_year'] / 365)
        
        # Season indicators
        df['is_summer'] = ((df['month'] >= 6) & (df['month'] <= 8)).astype(int)
        df['is_winter'] = ((df['month'] >= 12) | (df['month'] <= 2)).astype(int)
        
        # Time of day
        df['is_daytime'] = ((df['hour'] >= 6) & (df['hour'] <= 18)).astype(int)
        df['is_peak_sun'] = ((df['hour'] >= 10) & (df['hour'] <= 14)).astype(int)
        
        temporal_features = [
            'hour', 'day_of_year', 'month', 'weekday',
            'hour_sin', 'hour_cos', 'day_of_year_sin', 'day_of_year_cos',
            'is_summer', 'is_winter', 'is_daytime', 'is_peak_sun'
        ]
        
        self.features_created.extend(temporal_features)
        logger.info(f"   ✅ Added {len(temporal_features)} temporal features")
        
        return df
    
    def add_solar_features(self, df):
        """Add simple solar features"""
        
        logger.info("☀️ Adding solar features...")
        
        # Simple solar elevation approximation
        df['solar_elevation_approx'] = np.where(
            df['is_daytime'] == 1,
            60 * np.sin(np.pi * (df['hour'] - 6) / 12),  # Peak at noon
            0
        )
        df['solar_elevation_approx'] = np.clip(df['solar_elevation_approx'], 0, 90)
        
        # Normalize solar elevation
        df['solar_elevation_normalized'] = df['solar_elevation_approx'] / 90
        
        # Distance from solar noon
        df['time_from_solar_noon'] = np.abs(df['hour'] - 12)
        
        # Day length approximation (varies by season)
        df['day_length_approx'] = 12 + 2 * np.sin(2 * np.pi * (df['day_of_year'] - 80) / 365)
        
        solar_features = [
            'solar_elevation_approx', 'solar_elevation_normalized',
            'time_from_solar_noon', 'day_length_approx'
        ]
        
        self.features_created.extend(solar_features)
        logger.info(f"   ✅ Added {len(solar_features)} solar features")
        
        return df
    
    def add_weather_features(self, df):
        """Add weather features"""
        
        logger.info("🌤️ Adding weather features...")
        
        # Normalize weather features
        df['ghi_normalized'] = np.where(df['ghi'].notna(), df['ghi'] / 1000, 0)
        df['cloud_cover_normalized'] = np.where(df['cloud_cover'].notna(), df['cloud_cover'] / 100, 0)
        df['temp_normalized'] = np.where(df['ambient_temperature'].notna(), 
                                        (df['ambient_temperature'] - 0) / 40, 0.5)
        
        # Clear sky factor
        df['clear_sky_factor'] = 1 - df['cloud_cover_normalized']
        
        # Temperature optimality (25°C is optimal)
        df['temp_optimality'] = np.where(df['ambient_temperature'].notna(),
                                        1 - np.abs(df['ambient_temperature'] - 25) / 25, 0.5)
        df['temp_optimality'] = np.clip(df['temp_optimality'], 0, 1)
        
        # Weather quality score
        df['weather_quality'] = (df['clear_sky_factor'] + df['temp_optimality']) / 2
        
        weather_features = [
            'ghi_normalized', 'cloud_cover_normalized', 'temp_normalized',
            'clear_sky_factor', 'temp_optimality', 'weather_quality'
        ]
        
        self.features_created.extend(weather_features)
        logger.info(f"   ✅ Added {len(weather_features)} weather features")
        
        return df
    
    def add_system_features(self, df):
        """Add system features"""
        
        logger.info("🔋 Adding system features...")
        
        # Normalize system features
        df['battery_soc_normalized'] = df['battery_soc'] / 100
        df['ac_power_normalized'] = df['ac_power'] / 10500  # 10.5kW system
        df['battery_power_normalized'] = df['battery_power'] / 12000  # Max battery power
        
        # Battery state
        df['battery_charging'] = (df['battery_power'] > 100).astype(int)
        df['battery_discharging'] = (df['battery_power'] < -100).astype(int)
        
        # System efficiency approximation
        df['system_efficiency'] = np.where(
            (df['ghi'] > 100) & (df['ac_power'] > 0),
            df['ac_power'] / (df['ghi'] * 10.5),
            0
        )
        df['system_efficiency'] = np.clip(df['system_efficiency'], 0, 1.5)
        
        # System ID encoding
        df['is_system_1'] = (df['system_id'] == 'system_1').astype(int)
        df['is_system_2'] = (df['system_id'] == 'system_2').astype(int)
        
        system_features = [
            'battery_soc_normalized', 'ac_power_normalized', 'battery_power_normalized',
            'battery_charging', 'battery_discharging', 'system_efficiency',
            'is_system_1', 'is_system_2'
        ]
        
        self.features_created.extend(system_features)
        logger.info(f"   ✅ Added {len(system_features)} system features")
        
        return df
    
    def add_lag_features(self, df):
        """Add simple lag features"""
        
        logger.info("📈 Adding lag features...")
        
        # Sort by system and timestamp
        df = df.sort_values(['system_id', 'timestamp'])
        
        # Add lag features for each system
        for system_id in df['system_id'].unique():
            mask = df['system_id'] == system_id
            
            # Previous hour values (12 periods back for 5-min data)
            df.loc[mask, 'yield_today_lag1h'] = df.loc[mask, 'yield_today'].shift(12)
            df.loc[mask, 'ac_power_lag1h'] = df.loc[mask, 'ac_power'].shift(12)
            df.loc[mask, 'battery_soc_lag1h'] = df.loc[mask, 'battery_soc'].shift(12)
            
            # Rolling averages (30 minutes = 6 periods)
            df.loc[mask, 'ac_power_rolling_30min'] = df.loc[mask, 'ac_power'].rolling(6, min_periods=1).mean()
            df.loc[mask, 'yield_rolling_30min'] = df.loc[mask, 'yield_today'].rolling(6, min_periods=1).mean()
        
        lag_features = [
            'yield_today_lag1h', 'ac_power_lag1h', 'battery_soc_lag1h',
            'ac_power_rolling_30min', 'yield_rolling_30min'
        ]
        
        self.features_created.extend(lag_features)
        logger.info(f"   ✅ Added {len(lag_features)} lag features")
        
        return df
    
    def add_interaction_features(self, df):
        """Add interaction features"""
        
        logger.info("🔬 Adding interaction features...")
        
        # Solar-weather interactions
        df['solar_weather_score'] = df['solar_elevation_normalized'] * df['weather_quality']
        df['optimal_conditions'] = df['solar_elevation_normalized'] * df['clear_sky_factor'] * df['temp_optimality']
        
        # Battery-solar interactions
        df['battery_solar_potential'] = (1 - df['battery_soc_normalized']) * df['solar_elevation_normalized']
        
        # Time-based interactions
        df['peak_sun_weather'] = df['is_peak_sun'] * df['weather_quality']
        df['summer_efficiency'] = df['is_summer'] * df['system_efficiency']
        
        interaction_features = [
            'solar_weather_score', 'optimal_conditions', 'battery_solar_potential',
            'peak_sun_weather', 'summer_efficiency'
        ]
        
        self.features_created.extend(interaction_features)
        logger.info(f"   ✅ Added {len(interaction_features)} interaction features")
        
        return df
    
    def run_feature_engineering(self, days=7):
        """Run complete simple feature engineering"""
        
        logger.info("🚀 Starting simple feature engineering...")
        
        # Load data
        df = self.load_sample_data(days=days)
        if df is None:
            return None
        
        # Add features
        df = self.add_temporal_features(df)
        df = self.add_solar_features(df)
        df = self.add_weather_features(df)
        df = self.add_system_features(df)
        df = self.add_lag_features(df)
        df = self.add_interaction_features(df)
        
        # Clean up
        df = df.replace([np.inf, -np.inf], np.nan)
        
        logger.info(f"🎉 Feature engineering completed!")
        logger.info(f"📊 Final dataset: {len(df):,} records with {len(self.features_created)} features")
        
        return df

def main():
    """Main feature engineering function"""
    
    print("🔧 PHASE 2: SIMPLE FEATURE ENGINEERING")
    print("="*60)
    print("📊 Processing real solar and weather data")
    print("🔬 Creating essential features for ML pipeline")
    print()
    
    try:
        # Initialize feature engineer
        engineer = SimpleFeatureEngineer()
        
        # Run feature engineering
        df = engineer.run_feature_engineering(days=7)
        
        if df is not None:
            print(f"\n🎉 Feature engineering completed successfully!")
            print(f"📊 Created {len(engineer.features_created)} features")
            print(f"📈 Final dataset: {len(df):,} records")
            
            # Show feature summary
            print(f"\n📋 Features created:")
            for i, feature in enumerate(engineer.features_created, 1):
                print(f"   {i:2d}. {feature}")
            
            # Save dataset
            output_path = 'data/simple_features_dataset.csv'
            os.makedirs('data', exist_ok=True)
            df.to_csv(output_path, index=False)
            print(f"\n💾 Dataset saved to: {output_path}")
            
            # Show sample
            print(f"\n📊 Sample data:")
            print(df[['timestamp', 'system_id', 'yield_today', 'ac_power', 'hour', 'solar_elevation_normalized']].head())
            
            return True
        else:
            print("❌ Feature engineering failed")
            return False
            
    except Exception as e:
        print(f"❌ Feature engineering failed: {e}")
        logger.exception("Feature engineering failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
