#!/bin/bash
"""
Solar Prediction Project - Setup Script
Sets up the complete development environment
"""

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Project paths
PROJECT_ROOT="/home/<USER>/solar-prediction-project"
ORIGINAL_PROJECT="/home/<USER>/mining-ai-project"

echo -e "${BLUE}🌞 Solar Prediction Project Setup${NC}"
echo -e "${BLUE}=================================${NC}"
echo

# Function to print status
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're in the right directory
if [ "$(pwd)" != "$PROJECT_ROOT" ]; then
    echo -e "${YELLOW}📁 Changing to project directory...${NC}"
    cd "$PROJECT_ROOT" || {
        print_error "Failed to change to project directory: $PROJECT_ROOT"
        exit 1
    }
fi

print_status "In project directory: $(pwd)"

# Check if original project exists
if [ ! -d "$ORIGINAL_PROJECT" ]; then
    print_error "Original project not found: $ORIGINAL_PROJECT"
    print_error "Please ensure the mining-ai-project exists"
    exit 1
fi

print_status "Original project found: $ORIGINAL_PROJECT"

# Create symlinks if they don't exist
echo -e "${BLUE}🔗 Setting up symlinks...${NC}"

if [ ! -L "models" ]; then
    if [ -d "$ORIGINAL_PROJECT/models/enhanced_v2_all" ]; then
        ln -s "$ORIGINAL_PROJECT/models/enhanced_v2_all" ./models
        print_status "Created models symlink"
    else
        print_error "Enhanced Model v2 not found in original project"
        exit 1
    fi
else
    print_status "Models symlink already exists"
fi

if [ ! -L "data" ]; then
    if [ -d "$ORIGINAL_PROJECT/data" ]; then
        ln -s "$ORIGINAL_PROJECT/data" ./data
        print_status "Created data symlink"
    else
        print_warning "Data directory not found in original project"
    fi
else
    print_status "Data symlink already exists"
fi

# Verify symlinks work
echo -e "${BLUE}🔍 Verifying symlinks...${NC}"

if [ -f "models/lightgbm_model.txt" ]; then
    print_status "LightGBM model accessible"
else
    print_error "LightGBM model not accessible"
    exit 1
fi

if [ -f "models/feature_columns.json" ]; then
    print_status "Feature columns accessible"
else
    print_warning "Feature columns file not found (will use defaults)"
fi

# Check Python version
echo -e "${BLUE}🐍 Checking Python...${NC}"

if command -v python3 &> /dev/null; then
    PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
    print_status "Python version: $PYTHON_VERSION"
else
    print_error "Python 3 not found"
    exit 1
fi

# Create virtual environment
echo -e "${BLUE}📦 Setting up virtual environment...${NC}"

if [ ! -d "venv" ]; then
    python3 -m venv venv
    print_status "Created virtual environment"
else
    print_status "Virtual environment already exists"
fi

# Activate virtual environment
source venv/bin/activate
print_status "Activated virtual environment"

# Upgrade pip
echo -e "${BLUE}⬆️  Upgrading pip...${NC}"
pip install --upgrade pip
print_status "Pip upgraded"

# Install dependencies
echo -e "${BLUE}📚 Installing dependencies...${NC}"
if [ -f "requirements.txt" ]; then
    pip install -r requirements.txt
    print_status "Dependencies installed"
else
    print_error "requirements.txt not found"
    exit 1
fi

# Verify key packages
echo -e "${BLUE}🔍 Verifying key packages...${NC}"

packages=("fastapi" "lightgbm" "sqlalchemy" "psycopg2" "httpx" "pydantic")
for package in "${packages[@]}"; do
    if python -c "import $package" 2>/dev/null; then
        print_status "$package installed"
    else
        print_error "$package not installed properly"
        exit 1
    fi
done

# Check PostgreSQL
echo -e "${BLUE}🗄️  Checking PostgreSQL...${NC}"

if command -v psql &> /dev/null; then
    print_status "PostgreSQL client available"
    
    # Check if PostgreSQL service is running
    if systemctl is-active --quiet postgresql; then
        print_status "PostgreSQL service is running"
    else
        print_warning "PostgreSQL service is not running"
        echo "  To start: sudo systemctl start postgresql"
    fi
else
    print_warning "PostgreSQL client not found"
    echo "  To install: sudo apt install postgresql postgresql-contrib"
fi

# Create database if it doesn't exist
echo -e "${BLUE}🏗️  Setting up database...${NC}"

if command -v createdb &> /dev/null; then
    if psql -lqt | cut -d \| -f 1 | grep -qw solar_prediction; then
        print_status "Database 'solar_prediction' already exists"
    else
        if createdb solar_prediction 2>/dev/null; then
            print_status "Created database 'solar_prediction'"
        else
            print_warning "Could not create database (may need to configure PostgreSQL)"
        fi
    fi
else
    print_warning "Cannot create database - PostgreSQL tools not available"
fi

# Create directories
echo -e "${BLUE}📁 Creating directories...${NC}"

directories=("logs" "static" "templates" "tests")
for dir in "${directories[@]}"; do
    if [ ! -d "$dir" ]; then
        mkdir -p "$dir"
        print_status "Created directory: $dir"
    else
        print_status "Directory exists: $dir"
    fi
done

# Copy environment file
echo -e "${BLUE}⚙️  Setting up environment...${NC}"

if [ ! -f ".env" ]; then
    if [ -f ".env.example" ]; then
        cp .env.example .env
        print_status "Created .env from .env.example"
        print_warning "Please review and update .env file with your settings"
    else
        print_error ".env.example not found"
    fi
else
    print_status ".env file already exists"
fi

# Test model loading
echo -e "${BLUE}🤖 Testing model loading...${NC}"

if python -c "
import sys
sys.path.insert(0, 'src')
from services.ml_service import MLService
ml_service = MLService()
if ml_service.model_loaded:
    print('✅ Model loaded successfully')
    info = ml_service.get_model_info()
    print(f'   Model: {info[\"model_type\"]} v{info[\"version\"]}')
    print(f'   Accuracy: {info[\"accuracy\"]}')
    print(f'   Features: {info[\"features\"]}')
else:
    print('❌ Model loading failed')
    sys.exit(1)
" 2>/dev/null; then
    print_status "Model loading test passed"
else
    print_error "Model loading test failed"
    print_error "Check that symlinks are working and model files exist"
    exit 1
fi

# Final status
echo
echo -e "${GREEN}🎉 Setup completed successfully!${NC}"
echo
echo -e "${BLUE}Next steps:${NC}"
echo "1. Review and update .env file if needed"
echo "2. Start the application: python run.py"
echo "3. Test the API: curl http://localhost:8100/health"
echo "4. View API docs: http://localhost:8100/docs"
echo
echo -e "${BLUE}Useful commands:${NC}"
echo "  Activate venv: source venv/bin/activate"
echo "  Start app: python run.py"
echo "  Run tests: pytest"
echo "  Check health: curl http://localhost:8100/health"
echo
echo -e "${GREEN}Happy coding! 🚀${NC}"
