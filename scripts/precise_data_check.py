#!/usr/bin/env python3
"""
Ακριβής έλεγχος δεδομένων - Και τα δύο συστήματα
"""

import psycopg2
from datetime import datetime, timed<PERSON>ta

def precise_data_check():
    """Ακριβής έλεγχος όλων των δεδομένων"""
    print("🔍 ΑΚΡΙΒΗΣ ΕΛΕΓΧΟΣ ΔΕΔΟΜΕΝΩΝ - ΚΑΙ ΤΑ ΔΥΟ ΣΥΣΤΗΜΑΤΑ")
    print("=" * 70)
    
    try:
        # Σύνδεση στη βάση
        conn = psycopg2.connect(
            host="localhost",
            database="solar_prediction",
            user="postgres", 
            password="postgres"
        )
        cursor = conn.cursor()
        
        # Ελέγχω όλους τους πίνακες που υπάρχουν
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_type = 'BASE TABLE'
            ORDER BY table_name
        """)
        all_tables = [row[0] for row in cursor.fetchall()]
        
        print(f"📋 ΟΛΟΙ ΟΙ ΠΙΝΑΚΕΣ ΣΤΗ ΒΑΣΗ:")
        for table in all_tables:
            print(f"   📊 {table}")
        
        # Εστιάζω στους κύριους πίνακες δεδομένων
        main_tables = ['solax_data', 'solax_data2', 'weather_data']
        
        print(f"\n🎯 ΑΝΑΛΥΣΗ ΚΥΡΙΩΝ ΠΙΝΑΚΩΝ:")
        
        for table in main_tables:
            if table in all_tables:
                print(f"\n📊 {table.upper()}:")
                
                # Συνολικά records
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                total = cursor.fetchone()[0]
                
                if total > 0:
                    # Εύρος ημερομηνιών
                    cursor.execute(f"SELECT MIN(timestamp), MAX(timestamp) FROM {table}")
                    min_date, max_date = cursor.fetchone()
                    
                    # Ελέγχω για δεδομένα από March 2024
                    cursor.execute(f"""
                        SELECT COUNT(*) FROM {table} 
                        WHERE timestamp >= '2024-03-01' AND timestamp < '2024-04-01'
                    """)
                    march_2024 = cursor.fetchone()[0]
                    
                    # Ελέγχω για δεδομένα από April 2024
                    cursor.execute(f"""
                        SELECT COUNT(*) FROM {table} 
                        WHERE timestamp >= '2024-04-01' AND timestamp < '2024-05-01'
                    """)
                    april_2024 = cursor.fetchone()[0]
                    
                    # Ελέγχω για πρόσφατα δεδομένα (τελευταίες 7 ημέρες)
                    cursor.execute(f"""
                        SELECT COUNT(*) FROM {table} 
                        WHERE timestamp >= NOW() - INTERVAL '7 days'
                    """)
                    recent = cursor.fetchone()[0]
                    
                    print(f"   📈 Συνολικά: {total:,} records")
                    print(f"   📅 Εύρος: {min_date} έως {max_date}")
                    print(f"   🎯 March 2024: {march_2024:,} records")
                    print(f"   🎯 April 2024: {april_2024:,} records")
                    print(f"   🆕 Τελευταίες 7 ημέρες: {recent:,} records")
                    
                    # Ελέγχω system_id αν υπάρχει
                    if table.startswith('solax'):
                        try:
                            cursor.execute(f"""
                                SELECT system_id, COUNT(*) 
                                FROM {table} 
                                WHERE system_id IS NOT NULL
                                GROUP BY system_id 
                                ORDER BY system_id
                            """)
                            system_data = cursor.fetchall()
                            
                            if system_data:
                                print(f"   🏠 System ID breakdown:")
                                for system_id, count in system_data:
                                    print(f"      System {system_id}: {count:,} records")
                            else:
                                print(f"   ⚠️  Δεν υπάρχουν system_id")
                        except:
                            print(f"   ❌ Δεν υπάρχει στήλη system_id")
                else:
                    print(f"   ❌ ΑΔΕΙΟΣ ΠΙΝΑΚΑΣ")
            else:
                print(f"\n❌ {table.upper()}: ΔΕΝ ΥΠΑΡΧΕΙ")
        
        # Ελέγχω για άλλους πίνακες με δεδομένα
        other_data_tables = [t for t in all_tables if 'data' in t and t not in main_tables]
        
        if other_data_tables:
            print(f"\n🔍 ΑΛΛΟΙ ΠΙΝΑΚΕΣ ΜΕ ΔΕΔΟΜΕΝΑ:")
            for table in other_data_tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                if count > 0:
                    cursor.execute(f"SELECT MIN(timestamp), MAX(timestamp) FROM {table}")
                    min_date, max_date = cursor.fetchone()
                    print(f"   📊 {table}: {count:,} records ({min_date} έως {max_date})")
        
        # ΣΥΜΠΕΡΑΣΜΑΤΑ
        print(f"\n📋 ΣΥΜΠΕΡΑΣΜΑΤΑ:")
        
        # Ελέγχω αν έχουμε δεδομένα από March 2024
        cursor.execute("""
            SELECT 
                (SELECT COUNT(*) FROM solax_data WHERE timestamp >= '2024-03-01') as solax1_march,
                (SELECT COUNT(*) FROM solax_data2 WHERE timestamp >= '2024-03-01') as solax2_march
        """)
        solax1_march, solax2_march = cursor.fetchone()
        
        if solax1_march > 0 or solax2_march > 0:
            print(f"   ✅ ΕΧΟΥΜΕ δεδομένα από March 2024")
            print(f"      System 1: {solax1_march:,} records")
            print(f"      System 2: {solax2_march:,} records")
        else:
            print(f"   ❌ ΔΕΝ ΕΧΟΥΜΕ δεδομένα από March 2024")
            print(f"   🔧 Χρειάζεται import από backup files")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Σφάλμα: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    precise_data_check()
