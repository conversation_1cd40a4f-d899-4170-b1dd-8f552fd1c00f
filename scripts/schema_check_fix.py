#!/usr/bin/env python3
"""
Database Schema Check & Fix για Historical Data Import
"""

import psycopg2
from datetime import datetime

def check_and_fix_schema():
    """Έλεγχος και διόρθωση database schema"""
    print("🔧 DATABASE SCHEMA CHECK & FIX")
    print("=" * 50)
    
    try:
        conn = psycopg2.connect(
            host="localhost",
            database="solar_prediction",
            user="postgres", 
            password="postgres"
        )
        cursor = conn.cursor()
        
        print("✅ Database connection successful")
        
        tables = ['solax_data', 'solax_data2']
        
        for table_name in tables:
            print(f"\n🔍 CHECKING: {table_name}")
            
            # 1. Ελέγχω αν υπάρχει ο πίνακας
            cursor.execute(f"""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = '{table_name}'
                )
            """)
            
            if not cursor.fetchone()[0]:
                print(f"   ❌ Table {table_name} does not exist!")
                continue
            
            # 2. Ελέγχω columns
            cursor.execute(f"""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = '{table_name}'
            """)
            
            existing_columns = [row[0] for row in cursor.fetchall()]
            print(f"   📋 Existing columns: {len(existing_columns)}")
            
            # 3. Ελέγχω constraints
            cursor.execute(f"""
                SELECT constraint_name, constraint_type 
                FROM information_schema.table_constraints 
                WHERE table_name = '{table_name}' AND constraint_type = 'UNIQUE'
            """)
            
            unique_constraints = cursor.fetchall()
            has_timestamp_unique = any('timestamp' in name.lower() for name, _ in unique_constraints)
            
            print(f"   🔒 UNIQUE constraints: {len(unique_constraints)}")
            print(f"   📅 Timestamp UNIQUE: {'✅' if has_timestamp_unique else '❌'}")
            
            # 4. Διόρθωση: Προσθήκη UNIQUE constraint αν λείπει
            if not has_timestamp_unique:
                print(f"   🔧 Adding UNIQUE constraint on timestamp...")
                
                try:
                    # Πρώτα αφαιρώ duplicates αν υπάρχουν
                    cursor.execute(f"""
                        SELECT COUNT(*) FROM (
                            SELECT timestamp, COUNT(*) 
                            FROM {table_name} 
                            GROUP BY timestamp 
                            HAVING COUNT(*) > 1
                        ) duplicates
                    """)
                    
                    duplicate_count = cursor.fetchone()[0]
                    
                    if duplicate_count > 0:
                        print(f"      ⚠️  Found {duplicate_count} duplicate timestamps")
                        print(f"      🧹 Removing duplicates...")
                        
                        cursor.execute(f"""
                            DELETE FROM {table_name} 
                            WHERE ctid NOT IN (
                                SELECT MIN(ctid) 
                                FROM {table_name} 
                                GROUP BY timestamp
                            )
                        """)
                        
                        removed = cursor.rowcount
                        print(f"      ✅ Removed {removed} duplicate records")
                    
                    # Προσθήκη UNIQUE constraint
                    constraint_name = f"{table_name}_timestamp_unique"
                    cursor.execute(f"""
                        ALTER TABLE {table_name} 
                        ADD CONSTRAINT {constraint_name} UNIQUE (timestamp)
                    """)
                    
                    print(f"      ✅ Added UNIQUE constraint: {constraint_name}")
                    
                except Exception as e:
                    if "already exists" in str(e):
                        print(f"      ⚠️  UNIQUE constraint already exists")
                    else:
                        print(f"      ❌ Error: {e}")
            
            # 5. Test insert
            print(f"   🧪 Testing insert capability...")
            
            try:
                test_timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
                
                cursor.execute(f"""
                    INSERT INTO {table_name} (
                        timestamp, ac_power, system_id, inverter_sn, wifi_sn
                    ) VALUES (%s, %s, %s, %s, %s)
                """, (test_timestamp, 1000.0, 1, 'TEST', 'TEST'))
                
                print(f"      ✅ Insert test successful")
                
                # Cleanup test record
                cursor.execute(f"DELETE FROM {table_name} WHERE inverter_sn = 'TEST'")
                
            except Exception as e:
                print(f"      ❌ Insert test failed: {e}")
                return False
        
        # Commit changes
        conn.commit()
        cursor.close()
        conn.close()
        
        print(f"\n✅ SCHEMA FIX COMPLETED!")
        print(f"   🎯 Tables ready for historical data import")
        
        return True
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Κύρια συνάρτηση"""
    success = check_and_fix_schema()
    
    if success:
        print(f"\n🚀 READY FOR IMPORT!")
        print(f"   📥 Database schema is now ready")
        print(f"   🔧 UNIQUE constraints added")
        print(f"   ✅ Insert capability verified")
    else:
        print(f"\n❌ SCHEMA FIX FAILED!")
        print(f"   🔧 Manual intervention required")
        print(f"   📋 Check database permissions and structure")
    
    return success

if __name__ == "__main__":
    main()
