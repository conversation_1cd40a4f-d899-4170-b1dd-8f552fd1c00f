#!/usr/bin/env python3
"""
Simple Excel Reader
Read the specific Excel file and extract April 2024 data
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def read_excel_file():
    """Read the Excel file and extract data"""
    print("📊 READING EXCEL FILE")
    print("=" * 30)
    
    file_path = "data/raw/System2/Plant Reports 2024-03-01-2024-06-28.xlsx"
    
    try:
        print(f"Reading: {file_path}")
        
        # Try different approaches to read the Excel file
        approaches = [
            {"skiprows": 0},
            {"skiprows": 1}, 
            {"skiprows": 2},
            {"header": 0},
            {"header": 1},
            {"header": None}
        ]
        
        for i, approach in enumerate(approaches):
            try:
                print(f"\nApproach {i+1}: {approach}")
                df = pd.read_excel(file_path, **approach)
                
                print(f"Shape: {df.shape}")
                print(f"Columns: {list(df.columns)}")
                
                if len(df) > 0:
                    print("First few rows:")
                    print(df.head(3))
                    
                    # Look for date/time columns
                    date_cols = [col for col in df.columns if any(word in str(col).lower() for word in ['date', 'time', 'timestamp'])]
                    print(f"Potential date columns: {date_cols}")
                    
                    # Look for yield columns
                    yield_cols = [col for col in df.columns if any(word in str(col).lower() for word in ['yield', 'energy', 'kwh'])]
                    print(f"Potential yield columns: {yield_cols}")
                    
                    if date_cols and yield_cols:
                        print(f"✅ Found usable data with approach {i+1}")
                        return df, approach
                
            except Exception as e:
                print(f"Approach {i+1} failed: {e}")
                continue
        
        print("❌ Could not read Excel file with any approach")
        return None, None
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None, None

def extract_april_2024_data(df):
    """Extract April 2024 data from the dataframe"""
    if df is None:
        return None
    
    print("\n🔍 EXTRACTING APRIL 2024 DATA")
    print("=" * 40)
    
    # Find date column
    date_col = None
    for col in df.columns:
        if any(word in str(col).lower() for word in ['date', 'time', 'timestamp']):
            date_col = col
            break
    
    if not date_col:
        print("❌ No date column found")
        return None
    
    print(f"Using date column: {date_col}")
    
    # Convert to datetime
    try:
        df[date_col] = pd.to_datetime(df[date_col], errors='coerce')
        
        # Filter for April 2024
        april_2024 = df[
            (df[date_col].dt.year == 2024) & 
            (df[date_col].dt.month == 4)
        ].copy()
        
        print(f"Found {len(april_2024)} records for April 2024")
        
        if len(april_2024) > 0:
            print(f"Date range: {april_2024[date_col].min()} to {april_2024[date_col].max()}")
            
            # Find yield column - try different options
            yield_col = None
            yield_options = ['Daily inverter output (kWh)', 'Daily PV Yield(kWh)', 'Daily exported energy(kWh)']

            for option in yield_options:
                if option in df.columns:
                    yield_col = option
                    print(f"Found yield column: {yield_col}")
                    break

            if not yield_col:
                # Fallback to any column with yield/energy and kwh
                for col in df.columns:
                    if any(word in str(col).lower() for word in ['yield', 'energy']) and 'kwh' in str(col).lower():
                        yield_col = col
                        break
            
            if yield_col:
                print(f"Using yield column: {yield_col}")
                
                # Clean and process data
                april_2024[yield_col] = pd.to_numeric(april_2024[yield_col], errors='coerce')
                april_2024 = april_2024.dropna(subset=[date_col, yield_col])
                april_2024 = april_2024[april_2024[yield_col] > 0]
                
                # Debug: Check data before grouping
                print(f"Valid yield values: {april_2024[yield_col].count()}")
                print(f"Yield sample: {april_2024[yield_col].head()}")

                # Group by date to get daily totals (use max as it's cumulative)
                daily_data = april_2024.groupby(april_2024[date_col].dt.date)[yield_col].max().reset_index()
                daily_data.columns = ['date', 'daily_yield']

                # Remove NaN values
                daily_data = daily_data.dropna()
                daily_data = daily_data[daily_data['daily_yield'] > 0]

                print(f"Processed to {len(daily_data)} daily records")
                if len(daily_data) > 0:
                    print(f"Yield range: {daily_data['daily_yield'].min():.1f} - {daily_data['daily_yield'].max():.1f} kWh")
                    print(f"Average: {daily_data['daily_yield'].mean():.1f} kWh/day")
                else:
                    print("❌ No valid daily data after processing")
                
                return daily_data
            else:
                print("❌ No yield column found")
                return None
        else:
            print("❌ No April 2024 data found")
            return None
            
    except Exception as e:
        print(f"❌ Error processing dates: {e}")
        return None

def main():
    """Main function"""
    print("📊 SIMPLE EXCEL READER FOR APRIL 2024")
    print("=" * 50)
    
    # Read Excel file
    df, approach = read_excel_file()
    
    if df is not None:
        # Extract April 2024 data
        april_data = extract_april_2024_data(df)
        
        if april_data is not None:
            print("\n✅ SUCCESS!")
            print("April 2024 data extracted successfully")
            print("\nSample data:")
            print(april_data.head(10))
            
            # Save to CSV for easy access
            output_file = "april_2024_system2_data.csv"
            april_data.to_csv(output_file, index=False)
            print(f"\n💾 Data saved to: {output_file}")
            
            return april_data
        else:
            print("\n❌ Failed to extract April 2024 data")
            return None
    else:
        print("\n❌ Failed to read Excel file")
        return None

if __name__ == "__main__":
    main()
