#!/usr/bin/env python3
"""
7-Day Solar Production Forecast
Comprehensive prediction for both solar systems with daily totals
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import json
import requests
import psycopg2
from psycopg2.extras import RealDictCursor
import joblib
from typing import Dict, List, Tuple

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SevenDayForecastSystem:
    """Comprehensive 7-day solar production forecast system"""
    
    def __init__(self):
        self.db_config = {
            'host': 'localhost',
            'database': 'solar_prediction',
            'user': 'postgres',
            'password': 'postgres'
        }
        
        # Solar systems configuration
        self.systems = {
            'system_1': {
                'name': 'Σπίτι Πάνω',
                'table': 'solax_data',
                'wifi_sn': 'SRFQDPDN9W',
                'capacity_kw': 10.5,
                'typical_daily_production': 65  # kWh
            },
            'system_2': {
                'name': 'Σπίτι Κάτω', 
                'table': 'solax_data2',
                'wifi_sn': 'SRCV9TUD6S',
                'capacity_kw': 10.5,
                'typical_daily_production': 72  # kWh
            }
        }
        
        # Weather API configuration
        self.weather_api_url = "https://api.open-meteo.com/v1/forecast"
        self.coordinates = {
            'latitude': 38.141348,
            'longitude': 24.007165
        }
        
        # Model paths
        self.model_paths = {
            'system_1': 'models/system_1_best_model.joblib',
            'system_2': 'models/system_2_best_model.joblib'
        }
        
        # Load models
        self.models = {}
        self.load_models()
    
    def load_models(self):
        """Load trained models for each system"""
        
        logger.info("📊 Loading trained models...")
        
        for system_id, system_config in self.systems.items():
            model_path = self.model_paths.get(system_id)
            
            if model_path and os.path.exists(model_path):
                try:
                    self.models[system_id] = joblib.load(model_path)
                    logger.info(f"✅ Loaded model for {system_config['name']}")
                except Exception as e:
                    logger.warning(f"Failed to load model for {system_id}: {e}")
                    self.models[system_id] = None
            else:
                logger.warning(f"Model not found for {system_id}, will use statistical model")
                self.models[system_id] = None
    
    def get_latest_system_data(self, system_id: str, days: int = 30) -> pd.DataFrame:
        """Get latest data for a specific system"""
        
        try:
            conn = psycopg2.connect(**self.db_config)
            
            system_config = self.systems[system_id]
            table_name = system_config['table']
            
            query = f"""
                SELECT 
                    timestamp,
                    total_yield,
                    ac_power,
                    battery_soc,
                    battery_power,
                    grid_power,
                    load_power
                FROM {table_name}
                WHERE timestamp >= NOW() - INTERVAL '{days} days'
                ORDER BY timestamp DESC
                LIMIT 1000
            """
            
            df = pd.read_sql(query, conn)
            conn.close()
            
            if not df.empty:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df = df.sort_values('timestamp')
                logger.info(f"✅ Retrieved {len(df)} records for {system_config['name']}")
            
            return df
            
        except Exception as e:
            logger.error(f"Failed to get system data for {system_id}: {e}")
            return pd.DataFrame()
    
    def get_weather_forecast(self, days: int = 7) -> pd.DataFrame:
        """Get weather forecast for the next 7 days"""
        
        logger.info("🌤️ Fetching weather forecast...")
        
        try:
            # Calculate date range
            start_date = datetime.now().date()
            end_date = start_date + timedelta(days=days)
            
            # API parameters
            params = {
                'latitude': self.coordinates['latitude'],
                'longitude': self.coordinates['longitude'],
                'hourly': [
                    'temperature_2m',
                    'relative_humidity_2m',
                    'cloud_cover',
                    'wind_speed_10m',
                    'shortwave_radiation',
                    'direct_radiation',
                    'diffuse_radiation'
                ],
                'start_date': start_date.strftime('%Y-%m-%d'),
                'end_date': end_date.strftime('%Y-%m-%d'),
                'timezone': 'Europe/Athens'
            }
            
            # Make API request
            response = requests.get(self.weather_api_url, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            # Parse hourly data
            hourly_data = data['hourly']
            
            weather_df = pd.DataFrame({
                'timestamp': pd.to_datetime(hourly_data['time']),
                'temperature': hourly_data['temperature_2m'],
                'humidity': hourly_data['relative_humidity_2m'],
                'cloud_cover': hourly_data['cloud_cover'],
                'wind_speed': hourly_data['wind_speed_10m'],
                'ghi': hourly_data['shortwave_radiation'],
                'dni': hourly_data['direct_radiation'],
                'dhi': hourly_data['diffuse_radiation']
            })
            
            logger.info(f"✅ Retrieved weather forecast: {len(weather_df)} hourly records")
            
            return weather_df
            
        except Exception as e:
            logger.error(f"Failed to get weather forecast: {e}")
            return self._generate_fallback_weather(days)
    
    def _generate_fallback_weather(self, days: int = 7) -> pd.DataFrame:
        """Generate fallback weather data based on seasonal patterns"""
        
        logger.info("🔄 Generating fallback weather data...")
        
        # Create hourly timestamps
        start_time = datetime.now().replace(minute=0, second=0, microsecond=0)
        timestamps = [start_time + timedelta(hours=h) for h in range(days * 24)]
        
        weather_data = []
        
        for ts in timestamps:
            hour = ts.hour
            day_of_year = ts.timetuple().tm_yday
            
            # Seasonal temperature (June patterns)
            base_temp = 25 + 5 * np.sin(2 * np.pi * (day_of_year - 80) / 365)
            daily_temp_variation = 8 * np.sin(2 * np.pi * (hour - 6) / 24)
            temperature = base_temp + daily_temp_variation + np.random.normal(0, 2)
            
            # Solar radiation (clear sky model)
            solar_elevation = max(0, 60 * np.sin(2 * np.pi * (hour - 6) / 24))
            ghi = 1000 * np.sin(np.radians(solar_elevation)) if solar_elevation > 0 else 0
            ghi = max(0, ghi + np.random.normal(0, 50))
            
            # Cloud cover (random with some persistence)
            cloud_cover = max(0, min(100, 30 + np.random.normal(0, 20)))
            
            # Adjust GHI for clouds
            ghi = ghi * (1 - cloud_cover / 100 * 0.8)
            
            weather_data.append({
                'timestamp': ts,
                'temperature': temperature,
                'humidity': 60 + np.random.normal(0, 15),
                'cloud_cover': cloud_cover,
                'wind_speed': 5 + np.random.normal(0, 3),
                'ghi': ghi,
                'dni': ghi * 0.8,
                'dhi': ghi * 0.2
            })
        
        return pd.DataFrame(weather_data)
    
    def create_prediction_features(self, weather_df: pd.DataFrame, system_id: str) -> pd.DataFrame:
        """Create features for prediction"""
        
        features_df = weather_df.copy()
        
        # Time-based features
        features_df['hour'] = features_df['timestamp'].dt.hour
        features_df['day_of_year'] = features_df['timestamp'].dt.dayofyear
        features_df['month'] = features_df['timestamp'].dt.month
        features_df['weekday'] = features_df['timestamp'].dt.weekday
        
        # Solar geometry features
        features_df['solar_elevation'] = self._calculate_solar_elevation(
            features_df['timestamp'], 
            self.coordinates['latitude'], 
            self.coordinates['longitude']
        )
        
        # Derived features
        features_df['temp_optimal'] = 1 - np.abs(features_df['temperature'] - 25) / 25
        features_df['cloud_factor'] = 1 - features_df['cloud_cover'] / 100
        features_df['ghi_normalized'] = features_df['ghi'] / 1000
        
        # System-specific adjustments
        system_config = self.systems[system_id]
        features_df['system_capacity'] = system_config['capacity_kw']
        
        return features_df
    
    def _calculate_solar_elevation(self, timestamps: pd.Series, lat: float, lon: float) -> pd.Series:
        """Calculate solar elevation angle"""
        
        # Simplified solar elevation calculation
        elevations = []
        
        for ts in timestamps:
            hour = ts.hour
            day_of_year = ts.timetuple().tm_yday
            
            # Solar declination
            declination = 23.45 * np.sin(np.radians(360 * (284 + day_of_year) / 365))
            
            # Hour angle
            hour_angle = 15 * (hour - 12)
            
            # Solar elevation
            elevation = np.arcsin(
                np.sin(np.radians(lat)) * np.sin(np.radians(declination)) +
                np.cos(np.radians(lat)) * np.cos(np.radians(declination)) * np.cos(np.radians(hour_angle))
            )
            
            elevation_degrees = max(0, np.degrees(elevation))
            elevations.append(elevation_degrees)
        
        return pd.Series(elevations)
    
    def predict_system_production(self, system_id: str, weather_df: pd.DataFrame) -> pd.DataFrame:
        """Predict production for a specific system"""
        
        logger.info(f"🔮 Predicting production for {self.systems[system_id]['name']}...")
        
        # Create features
        features_df = self.create_prediction_features(weather_df, system_id)
        
        # Get model
        model = self.models.get(system_id)
        
        if model is not None:
            # Use trained model
            try:
                # Select features that the model expects
                feature_columns = [
                    'ghi', 'temperature', 'cloud_cover', 'humidity', 'wind_speed',
                    'hour', 'day_of_year', 'solar_elevation', 'temp_optimal', 
                    'cloud_factor', 'ghi_normalized'
                ]
                
                # Ensure all features exist
                available_features = [col for col in feature_columns if col in features_df.columns]
                X = features_df[available_features]
                
                # Make predictions
                predictions = model.predict(X)
                
                logger.info(f"✅ Used trained model for {system_id}")
                
            except Exception as e:
                logger.warning(f"Model prediction failed for {system_id}: {e}, using statistical model")
                predictions = self._statistical_prediction(features_df, system_id)
        else:
            # Use statistical model
            predictions = self._statistical_prediction(features_df, system_id)
        
        # Create results dataframe
        results_df = features_df[['timestamp']].copy()
        results_df['predicted_yield_hourly'] = np.maximum(0, predictions)
        
        return results_df
    
    def _statistical_prediction(self, features_df: pd.DataFrame, system_id: str) -> np.ndarray:
        """Statistical prediction model as fallback"""
        
        logger.info(f"📊 Using statistical model for {system_id}")
        
        system_config = self.systems[system_id]
        base_production = system_config['typical_daily_production']
        
        predictions = []
        
        for _, row in features_df.iterrows():
            # Base hourly production (assuming 10 hours of daylight)
            hourly_base = base_production / 10
            
            # Solar elevation factor
            elevation_factor = max(0, np.sin(np.radians(row['solar_elevation'])))
            
            # Weather factors
            ghi_factor = row['ghi'] / 800  # Normalize to typical peak GHI
            cloud_factor = 1 - (row['cloud_cover'] / 100) * 0.7
            temp_factor = 1 - abs(row['temperature'] - 25) * 0.005  # Optimal at 25°C
            
            # Combined prediction
            hourly_prediction = (
                hourly_base * 
                elevation_factor * 
                ghi_factor * 
                cloud_factor * 
                temp_factor
            )
            
            predictions.append(max(0, hourly_prediction))
        
        return np.array(predictions)
    
    def generate_7_day_forecast(self) -> Dict:
        """Generate comprehensive 7-day forecast for both systems"""
        
        logger.info("🚀 Generating 7-day solar production forecast...")
        
        # Get weather forecast
        weather_df = self.get_weather_forecast(days=7)
        
        if weather_df.empty:
            logger.error("No weather data available")
            return {}
        
        # Generate predictions for both systems
        forecast_results = {}
        
        for system_id, system_config in self.systems.items():
            # Get system predictions
            predictions_df = self.predict_system_production(system_id, weather_df)
            
            # Calculate daily totals
            predictions_df['date'] = predictions_df['timestamp'].dt.date
            daily_totals = predictions_df.groupby('date')['predicted_yield_hourly'].sum().reset_index()
            daily_totals.columns = ['date', 'daily_total_kwh']
            
            # Add day names
            daily_totals['day_name'] = pd.to_datetime(daily_totals['date']).dt.strftime('%A')
            daily_totals['date_formatted'] = pd.to_datetime(daily_totals['date']).dt.strftime('%d/%m/%Y')
            
            forecast_results[system_id] = {
                'system_name': system_config['name'],
                'system_capacity_kw': system_config['capacity_kw'],
                'hourly_predictions': predictions_df.to_dict('records'),
                'daily_totals': daily_totals.to_dict('records'),
                'total_7_days_kwh': daily_totals['daily_total_kwh'].sum(),
                'average_daily_kwh': daily_totals['daily_total_kwh'].mean(),
                'peak_day_kwh': daily_totals['daily_total_kwh'].max(),
                'min_day_kwh': daily_totals['daily_total_kwh'].min()
            }
        
        # Calculate combined totals
        combined_daily = {}
        for system_id, results in forecast_results.items():
            for day_data in results['daily_totals']:
                date = day_data['date']
                if date not in combined_daily:
                    combined_daily[date] = {
                        'date': date,
                        'date_formatted': day_data['date_formatted'],
                        'day_name': day_data['day_name'],
                        'total_kwh': 0
                    }
                combined_daily[date]['total_kwh'] += day_data['daily_total_kwh']
        
        # Summary statistics
        total_combined_7_days = sum(day['total_kwh'] for day in combined_daily.values())
        
        forecast_summary = {
            'forecast_date': datetime.now().isoformat(),
            'forecast_period': '7 days',
            'systems': forecast_results,
            'combined_daily_totals': list(combined_daily.values()),
            'combined_7_day_total_kwh': total_combined_7_days,
            'combined_average_daily_kwh': total_combined_7_days / 7,
            'weather_source': 'Open-Meteo API' if not weather_df.empty else 'Statistical Model'
        }
        
        logger.info("✅ 7-day forecast generation completed")
        
        return forecast_summary
    
    def display_forecast_results(self, forecast: Dict):
        """Display forecast results in a formatted way"""
        
        print("\n" + "="*80)
        print("🌞 ΠΡΟΒΛΕΨΗ ΠΑΡΑΓΩΓΗΣ ΦΩΤΟΒΟΛΤΑΙΚΩΝ - ΕΠΟΜΕΝΕΣ 7 ΗΜΕΡΕΣ")
        print("="*80)
        print(f"📅 Ημερομηνία Πρόβλεψης: {datetime.now().strftime('%d/%m/%Y %H:%M')}")
        print(f"🌤️ Πηγή Καιρού: {forecast['weather_source']}")
        print()
        
        # Individual system results
        for system_id, results in forecast['systems'].items():
            print(f"🏠 {results['system_name']} (Ισχύς: {results['system_capacity_kw']} kW)")
            print("-" * 60)
            
            for day_data in results['daily_totals']:
                print(f"  📅 {day_data['day_name']:<10} {day_data['date_formatted']:<12} "
                      f"⚡ {day_data['daily_total_kwh']:.1f} kWh")
            
            print(f"\n  📊 Στατιστικά 7 Ημερών:")
            print(f"     • Συνολική Παραγωγή: {results['total_7_days_kwh']:.1f} kWh")
            print(f"     • Μέσος Όρος/Ημέρα: {results['average_daily_kwh']:.1f} kWh")
            print(f"     • Μέγιστη Ημέρα: {results['peak_day_kwh']:.1f} kWh")
            print(f"     • Ελάχιστη Ημέρα: {results['min_day_kwh']:.1f} kWh")
            print()
        
        # Combined results
        print("🏠 ΣΥΝΟΛΙΚΗ ΠΑΡΑΓΩΓΗ ΑΜΦΟΤΕΡΩΝ ΣΥΣΤΗΜΑΤΩΝ")
        print("-" * 60)
        
        for day_data in forecast['combined_daily_totals']:
            print(f"  📅 {day_data['day_name']:<10} {day_data['date_formatted']:<12} "
                  f"⚡ {day_data['total_kwh']:.1f} kWh")
        
        print(f"\n  📊 Συνολικά Στατιστικά 7 Ημερών:")
        print(f"     • Συνολική Παραγωγή: {forecast['combined_7_day_total_kwh']:.1f} kWh")
        print(f"     • Μέσος Όρος/Ημέρα: {forecast['combined_average_daily_kwh']:.1f} kWh")
        
        # Performance indicators
        print(f"\n  🎯 Δείκτες Απόδοσης:")
        total_capacity = sum(sys['system_capacity_kw'] for sys in forecast['systems'].values())
        capacity_factor = (forecast['combined_average_daily_kwh'] / (total_capacity * 24)) * 100
        print(f"     • Συντελεστής Χρησιμοποίησης: {capacity_factor:.1f}%")
        
        # Weather summary
        print(f"\n  🌤️ Περίληψη Καιρικών Συνθηκών:")
        print(f"     • Πηγή Δεδομένων: {forecast['weather_source']}")
        print(f"     • Περίοδος Πρόβλεψης: {forecast['forecast_period']}")
        
        print("\n" + "="*80)
    
    def save_forecast_results(self, forecast: Dict):
        """Save forecast results to file"""
        
        # Create output directory
        os.makedirs('reports/forecasts', exist_ok=True)
        
        # Save detailed forecast
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        forecast_path = f'reports/forecasts/7_day_forecast_{timestamp}.json'
        
        with open(forecast_path, 'w', encoding='utf-8') as f:
            json.dump(forecast, f, indent=2, ensure_ascii=False, default=str)
        
        logger.info(f"💾 Forecast saved to: {forecast_path}")
        
        return forecast_path

def main():
    """Main forecast function"""
    
    try:
        # Initialize forecast system
        forecast_system = SevenDayForecastSystem()
        
        # Generate 7-day forecast
        forecast = forecast_system.generate_7_day_forecast()
        
        if forecast:
            # Display results
            forecast_system.display_forecast_results(forecast)
            
            # Save results
            forecast_path = forecast_system.save_forecast_results(forecast)
            
            print(f"\n💾 Αναλυτικά αποτελέσματα αποθηκεύτηκαν στο: {forecast_path}")
            print("🎉 Πρόβλεψη 7 ημερών ολοκληρώθηκε επιτυχώς!")
            
            return True
        else:
            print("❌ Αποτυχία δημιουργίας πρόβλεψης")
            return False
            
    except Exception as e:
        print(f"❌ Σφάλμα στην πρόβλεψη: {e}")
        logger.exception("Forecast generation failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
