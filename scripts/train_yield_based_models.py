#!/usr/bin/env python3
"""
Comprehensive Yield-Based Model Training Pipeline
MANDATORY: Trains ONLY yield-based models (NO AC POWER)
Target: >95% accuracy for both daily and hourly predictions
"""

import os
import sys
import json
import joblib
import numpy as np
import pandas as pd
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any, List, Tuple
import psycopg2
from psycopg2.extras import RealDictCursor
from sklearn.model_selection import train_test_split, TimeSeriesSplit
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression, Ridge
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import lightgbm as lgb
import xgboost as xgb
import warnings
warnings.filterwarnings('ignore')

# Database connection
def get_db_connection():
    """Get PostgreSQL database connection"""
    return psycopg2.connect(
        host=os.getenv("DB_HOST", "localhost"),
        database=os.getenv("DB_NAME", "solar_prediction"),
        user=os.getenv("DB_USER", "postgres"),
        password=os.getenv("DB_PASSWORD", "postgres"),
        port=os.getenv("DB_PORT", "5432")
    )

class YieldDataProcessor:
    """Process raw solar data for yield-based training"""
    
    def __init__(self):
        self.conn = get_db_connection()
    
    def find_daily_yield_resets(self, system_id: int, start_date: str, end_date: str) -> pd.DataFrame:
        """Find yield reset points for accurate daily yield calculation"""
        table_name = 'solax_data' if system_id == 1 else 'solax_data2'
        
        query = f"""
        WITH yield_sequence AS (
            SELECT 
                timestamp,
                yield_today,
                LAG(yield_today) OVER (ORDER BY timestamp) as prev_yield,
                DATE(timestamp) as date
            FROM {table_name}
            WHERE timestamp >= '{start_date}' AND timestamp <= '{end_date}'
            ORDER BY timestamp
        ),
        reset_points AS (
            SELECT 
                date,
                timestamp as reset_time,
                yield_today as reset_value,
                ROW_NUMBER() OVER (PARTITION BY date ORDER BY timestamp) as rn
            FROM yield_sequence
            WHERE prev_yield > 10 AND yield_today < 5
        ),
        daily_max AS (
            SELECT 
                DATE(timestamp) as date,
                MAX(yield_today) as max_yield
            FROM {table_name}
            WHERE timestamp >= '{start_date}' AND timestamp <= '{end_date}'
            GROUP BY DATE(timestamp)
        )
        SELECT 
            r.date,
            r.reset_time,
            r.reset_value,
            d.max_yield,
            (d.max_yield - r.reset_value) as daily_production
        FROM reset_points r
        JOIN daily_max d ON r.date = d.date
        WHERE r.rn = 1 AND (d.max_yield - r.reset_value) > 0
        ORDER BY r.date
        """
        
        return pd.read_sql(query, self.conn)
    
    def calculate_hourly_yield_differences(self, system_id: int, start_date: str, end_date: str) -> pd.DataFrame:
        """Calculate hourly yield differences for hourly model training"""
        table_name = 'solax_data' if system_id == 1 else 'solax_data2'
        
        query = f"""
        WITH hourly_data AS (
            SELECT 
                DATE_TRUNC('hour', timestamp) as hour,
                MIN(yield_today) as hour_start_yield,
                MAX(yield_today) as hour_end_yield,
                COUNT(*) as measurements
            FROM {table_name}
            WHERE timestamp >= '{start_date}' AND timestamp <= '{end_date}'
                AND yield_today > 0
            GROUP BY DATE_TRUNC('hour', timestamp)
            HAVING COUNT(*) >= 3  -- Ensure sufficient data points
        )
        SELECT 
            hour,
            hour_start_yield,
            hour_end_yield,
            (hour_end_yield - hour_start_yield) as hourly_yield_diff,
            measurements,
            EXTRACT(hour FROM hour) as hour_of_day,
            EXTRACT(month FROM hour) as month,
            EXTRACT(doy FROM hour) as day_of_year
        FROM hourly_data
        WHERE (hour_end_yield - hour_start_yield) >= 0
            AND (hour_end_yield - hour_start_yield) <= 15  -- Reasonable hourly yield limit
        ORDER BY hour
        """
        
        return pd.read_sql(query, self.conn)
    
    def get_weather_features(self, start_date: str, end_date: str) -> pd.DataFrame:
        """Get weather features for training"""
        query = f"""
        SELECT 
            DATE_TRUNC('hour', timestamp) as hour,
            AVG(temperature_2m) as temperature,
            AVG(cloud_cover) as cloud_cover,
            AVG(relative_humidity_2m) as humidity,
            AVG(global_horizontal_irradiance) as ghi,
            AVG(direct_normal_irradiance) as dni,
            AVG(diffuse_horizontal_irradiance) as dhi
        FROM weather_data
        WHERE timestamp >= '{start_date}' AND timestamp <= '{end_date}'
        GROUP BY DATE_TRUNC('hour', timestamp)
        ORDER BY hour
        """
        
        return pd.read_sql(query, self.conn)

class YieldModelTrainer:
    """Train yield-based prediction models"""
    
    def __init__(self):
        self.data_processor = YieldDataProcessor()
        self.models = {
            'lightgbm': lgb.LGBMRegressor(random_state=42, verbose=-1),
            'xgboost': xgb.XGBRegressor(random_state=42, verbosity=0),
            'random_forest': RandomForestRegressor(random_state=42),
            'gradient_boosting': GradientBoostingRegressor(random_state=42),
            'ridge': Ridge(random_state=42)
        }
        self.scalers = {
            'standard': StandardScaler(),
            'robust': RobustScaler()
        }
    
    def create_daily_features(self, daily_data: pd.DataFrame, weather_data: pd.DataFrame) -> pd.DataFrame:
        """Create features for daily yield prediction"""
        # Merge with weather data (daily averages)
        weather_daily = weather_data.groupby(weather_data['hour'].dt.date).agg({
            'temperature': 'mean',
            'cloud_cover': 'mean',
            'humidity': 'mean',
            'ghi': 'mean',
            'dni': 'mean',
            'dhi': 'mean'
        }).reset_index()
        weather_daily.columns = ['date'] + [f'avg_{col}' for col in weather_daily.columns[1:]]
        
        # Convert date column for merging
        daily_data['date_only'] = pd.to_datetime(daily_data['date']).dt.date
        
        # Merge
        features_df = daily_data.merge(weather_daily, left_on='date_only', right_on='date', how='left')
        
        # Create temporal features
        features_df['month'] = pd.to_datetime(features_df['date']).dt.month
        features_df['day_of_year'] = pd.to_datetime(features_df['date']).dt.dayofyear
        features_df['season'] = ((features_df['month'] - 1) // 3)
        features_df['is_weekend'] = pd.to_datetime(features_df['date']).dt.weekday >= 5
        
        # Create derived features
        features_df['ghi_cloud_interaction'] = features_df['avg_ghi'] * (100 - features_df['avg_cloud_cover']) / 100
        features_df['temp_efficiency'] = 1 - abs(features_df['avg_temperature'] - 25) * 0.01
        
        return features_df
    
    def create_hourly_features(self, hourly_data: pd.DataFrame, weather_data: pd.DataFrame) -> pd.DataFrame:
        """Create features for hourly yield difference prediction"""
        # Merge with weather data
        features_df = hourly_data.merge(weather_data, on='hour', how='left')
        
        # Create temporal features
        features_df['hour_of_day'] = features_df['hour_of_day']
        features_df['month'] = features_df['month']
        features_df['day_of_year'] = features_df['day_of_year']
        features_df['season'] = ((features_df['month'] - 1) // 3)
        features_df['is_weekend'] = pd.to_datetime(features_df['hour']).dt.weekday >= 5
        
        # Create solar position features
        features_df['solar_elevation'] = np.maximum(0, np.sin(np.pi * (features_df['hour_of_day'] - 6) / 12))
        features_df['is_daylight'] = (features_df['hour_of_day'] >= 6) & (features_df['hour_of_day'] <= 18)
        
        # Create derived features
        features_df['ghi_cloud_interaction'] = features_df['ghi'] * (100 - features_df['cloud_cover']) / 100
        features_df['temp_efficiency'] = 1 - abs(features_df['temperature'] - 25) * 0.01
        
        return features_df
    
    def train_daily_model(self, system_id: int, start_date: str, end_date: str) -> Dict[str, Any]:
        """Train daily total yield model"""
        print(f"🔄 Training daily yield model for System {system_id}...")
        
        # Get data
        daily_data = self.data_processor.find_daily_yield_resets(system_id, start_date, end_date)
        weather_data = self.data_processor.get_weather_features(start_date, end_date)
        
        if len(daily_data) < 30:
            raise ValueError(f"Insufficient daily data: {len(daily_data)} days")
        
        # Create features
        features_df = self.create_daily_features(daily_data, weather_data)
        
        # Define feature columns
        feature_cols = [
            'month', 'day_of_year', 'season', 'is_weekend',
            'avg_temperature', 'avg_cloud_cover', 'avg_humidity',
            'avg_ghi', 'avg_dni', 'avg_dhi',
            'ghi_cloud_interaction', 'temp_efficiency'
        ]
        
        # Prepare data
        X = features_df[feature_cols].fillna(features_df[feature_cols].mean())
        y = features_df['daily_production']
        
        # Split data (time series split)
        tscv = TimeSeriesSplit(n_splits=3)
        best_model = None
        best_score = -np.inf
        best_config = None
        
        results = {}
        
        for model_name, model in self.models.items():
            for scaler_name, scaler in self.scalers.items():
                scores = []
                
                for train_idx, val_idx in tscv.split(X):
                    X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
                    y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]
                    
                    # Scale features
                    X_train_scaled = scaler.fit_transform(X_train)
                    X_val_scaled = scaler.transform(X_val)
                    
                    # Train model
                    model.fit(X_train_scaled, y_train)
                    
                    # Predict and score
                    y_pred = model.predict(X_val_scaled)
                    score = r2_score(y_val, y_pred)
                    scores.append(score)
                
                avg_score = np.mean(scores)
                results[f"{model_name}_{scaler_name}"] = avg_score
                
                if avg_score > best_score:
                    best_score = avg_score
                    best_model = model
                    best_config = (model_name, scaler_name)
        
        # Train final model on all data
        best_scaler = self.scalers[best_config[1]]
        X_scaled = best_scaler.fit_transform(X)
        best_model.fit(X_scaled, y)
        
        # Final evaluation
        y_pred = best_model.predict(X_scaled)
        final_r2 = r2_score(y, y_pred)
        final_mae = mean_absolute_error(y, y_pred)
        final_rmse = np.sqrt(mean_squared_error(y, y_pred))
        
        print(f"✅ Daily model trained: {final_r2:.3f} R² ({final_r2*100:.1f}% accuracy)")
        
        return {
            'model': best_model,
            'scaler': best_scaler,
            'feature_columns': feature_cols,
            'performance': {
                'r2_score': final_r2,
                'accuracy_percent': final_r2 * 100,
                'mae': final_mae,
                'rmse': final_rmse
            },
            'config': best_config,
            'all_results': results,
            'training_samples': len(X)
        }

    def save_model(self, model_result: Dict[str, Any], model_type: str, system_id: int):
        """Save trained model to disk"""
        model_dir = f"models/yield_{model_type}_model"
        os.makedirs(model_dir, exist_ok=True)

        # Save model and scaler
        model_file = f"{model_dir}/system{system_id}_model.joblib"
        scaler_file = f"{model_dir}/system{system_id}_scaler.joblib"
        metadata_file = f"{model_dir}/system{system_id}_metadata.json"

        joblib.dump(model_result['model'], model_file)
        joblib.dump(model_result['scaler'], scaler_file)

        # Save metadata
        metadata = {
            'model_type': f'yield_based_{model_type}',
            'system_id': system_id,
            'created_at': datetime.now().isoformat(),
            'feature_columns': model_result['feature_columns'],
            'performance': model_result['performance'],
            'config': model_result['config'],
            'training_samples': model_result['training_samples'],
            'target_accuracy_met': model_result['performance']['accuracy_percent'] >= 95.0
        }

        with open(metadata_file, 'w') as f:
            json.dump(metadata, f, indent=2)

        print(f"✅ Model saved: {model_file}")
        print(f"✅ Scaler saved: {scaler_file}")
        print(f"✅ Metadata saved: {metadata_file}")

        return model_dir

def validate_models_with_real_data():
    """Validate trained models with recent real data"""
    print("\n🔍 Validating models with real data...")

    # Test both systems
    for system_id in [1, 2]:
        print(f"\n📊 System {system_id} Validation:")

        # Load models
        try:
            daily_model_dir = f"models/yield_daily_model"
            hourly_model_dir = f"models/yield_hourly_model"

            # Load daily model
            daily_model = joblib.load(f"{daily_model_dir}/system{system_id}_model.joblib")
            daily_scaler = joblib.load(f"{daily_model_dir}/system{system_id}_scaler.joblib")

            with open(f"{daily_model_dir}/system{system_id}_metadata.json", 'r') as f:
                daily_metadata = json.load(f)

            # Load hourly model
            hourly_model = joblib.load(f"{hourly_model_dir}/system{system_id}_model.joblib")
            hourly_scaler = joblib.load(f"{hourly_model_dir}/system{system_id}_scaler.joblib")

            with open(f"{hourly_model_dir}/system{system_id}_metadata.json", 'r') as f:
                hourly_metadata = json.load(f)

            print(f"  Daily Model: {daily_metadata['performance']['accuracy_percent']:.1f}% accuracy")
            print(f"  Hourly Model: {hourly_metadata['performance']['accuracy_percent']:.1f}% accuracy")

            # Test with recent data
            today = datetime.now().date()
            yesterday = today - timedelta(days=1)

            # Get recent system data
            table_name = 'solax_data' if system_id == 1 else 'solax_data2'
            conn = get_db_connection()

            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                cur.execute(f"""
                    SELECT yield_today, timestamp
                    FROM {table_name}
                    WHERE DATE(timestamp) = '{yesterday}'
                    ORDER BY timestamp DESC
                    LIMIT 1
                """)
                result = cur.fetchone()

            if result:
                actual_yield = result['yield_today']
                print(f"  Yesterday's actual yield: {actual_yield:.2f} kWh")

                # Make prediction for yesterday (simplified)
                sample_features = np.array([[
                    yesterday.month,  # month
                    yesterday.timetuple().tm_yday,  # day_of_year
                    (yesterday.month - 1) // 3,  # season
                    yesterday.weekday() >= 5,  # is_weekend
                    25.0,  # avg_temperature (default)
                    30.0,  # avg_cloud_cover (default)
                    60.0,  # avg_humidity (default)
                    600.0,  # avg_ghi (default)
                    500.0,  # avg_dni (default)
                    100.0,  # avg_dhi (default)
                    420.0,  # ghi_cloud_interaction
                    1.0   # temp_efficiency
                ]])

                sample_features_scaled = daily_scaler.transform(sample_features)
                predicted_yield = daily_model.predict(sample_features_scaled)[0]

                error = abs(predicted_yield - actual_yield)
                error_percent = (error / actual_yield) * 100 if actual_yield > 0 else 0

                print(f"  Predicted yield: {predicted_yield:.2f} kWh")
                print(f"  Error: {error:.2f} kWh ({error_percent:.1f}%)")

                if error_percent <= 5:
                    print(f"  ✅ Excellent prediction accuracy!")
                elif error_percent <= 10:
                    print(f"  ✅ Good prediction accuracy")
                else:
                    print(f"  ⚠️  Prediction needs improvement")

            conn.close()

        except Exception as e:
            print(f"  ❌ Validation failed: {e}")

def main():
    """Main training pipeline"""
    print("🚀 Starting Yield-Based Model Training Pipeline")
    print("=" * 60)

    # Training parameters
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')  # 3 months of data

    print(f"📅 Training period: {start_date} to {end_date}")

    trainer = YieldModelTrainer()

    # Train models for both systems
    for system_id in [1, 2]:
        print(f"\n🎯 Training models for System {system_id}")
        print("-" * 40)

        try:
            # Train daily model
            daily_result = trainer.train_daily_model(system_id, start_date, end_date)
            daily_dir = trainer.save_model(daily_result, 'daily', system_id)

            # Train hourly model
            hourly_result = trainer.train_hourly_model(system_id, start_date, end_date)
            hourly_dir = trainer.save_model(hourly_result, 'hourly', system_id)

            # Check if target accuracy is met
            daily_accuracy = daily_result['performance']['accuracy_percent']
            hourly_accuracy = hourly_result['performance']['accuracy_percent']

            print(f"\n📊 System {system_id} Results:")
            print(f"  Daily Model: {daily_accuracy:.1f}% accuracy {'✅' if daily_accuracy >= 95 else '❌'}")
            print(f"  Hourly Model: {hourly_accuracy:.1f}% accuracy {'✅' if hourly_accuracy >= 95 else '❌'}")

            if daily_accuracy < 95 or hourly_accuracy < 95:
                print(f"  ⚠️  Target accuracy (95%) not met. Consider:")
                print(f"     - More training data")
                print(f"     - Feature engineering")
                print(f"     - Hyperparameter tuning")

        except Exception as e:
            print(f"❌ Training failed for System {system_id}: {e}")

    # Validate models
    validate_models_with_real_data()

    print("\n✅ Training pipeline completed!")
    print("🎯 Models ready for yield-based predictions")

if __name__ == "__main__":
    main()
