#!/usr/bin/env python3
"""
Real Database Connection and Analysis
Connects to actual PostgreSQL database and analyzes real data
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import psycopg2
from psycopg2.extras import RealDictCursor
import pandas as pd
from datetime import datetime, timedelta
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RealDatabaseAnalyzer:
    """Real database analyzer using actual PostgreSQL data"""
    
    def __init__(self):
        # Try different connection configurations
        self.connection_configs = [
            {'host': 'localhost', 'database': 'solar_prediction', 'user': 'postgres', 'password': 'postgres'},
            {'host': 'localhost', 'database': 'solar_prediction', 'user': 'postgres', 'password': ''},
            {'host': 'localhost', 'database': 'solar_prediction', 'user': 'grlv', 'password': ''},
            {'host': 'localhost', 'database': 'postgres', 'user': 'postgres', 'password': 'postgres'},
            {'host': 'localhost', 'database': 'postgres', 'user': 'postgres', 'password': ''},
        ]
        
        self.working_config = None
        self.analysis_results = {}
    
    def find_working_connection(self):
        """Find working database connection"""
        
        logger.info("🔍 Finding working database connection...")
        
        for i, config in enumerate(self.connection_configs, 1):
            try:
                logger.info(f"🔄 Trying config {i}: {config['database']}@{config['host']} as {config['user']}")
                
                conn = psycopg2.connect(**config)
                cur = conn.cursor()
                
                # Test connection
                cur.execute('SELECT version()')
                version = cur.fetchone()[0]
                
                logger.info(f"✅ Connected successfully!")
                logger.info(f"📊 PostgreSQL: {version[:50]}...")
                
                # Check if solar_prediction database exists
                if config['database'] != 'solar_prediction':
                    cur.execute("SELECT datname FROM pg_database WHERE datname = 'solar_prediction'")
                    solar_db_exists = cur.fetchone() is not None
                    
                    if solar_db_exists:
                        logger.info("🗄️ solar_prediction database found, switching...")
                        conn.close()
                        
                        # Connect to solar_prediction
                        solar_config = config.copy()
                        solar_config['database'] = 'solar_prediction'
                        conn = psycopg2.connect(**solar_config)
                        cur = conn.cursor()
                        
                        self.working_config = solar_config
                    else:
                        logger.warning("⚠️ solar_prediction database not found")
                        self.working_config = config
                else:
                    self.working_config = config
                
                conn.close()
                return True
                
            except Exception as e:
                logger.warning(f"❌ Config {i} failed: {e}")
                continue
        
        logger.error("❌ No working database connection found")
        return False
    
    def connect_to_database(self):
        """Connect to database using working config"""
        
        if not self.working_config:
            if not self.find_working_connection():
                return None
        
        try:
            conn = psycopg2.connect(**self.working_config)
            return conn
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            return None
    
    def analyze_database_schema(self):
        """Analyze real database schema"""
        
        logger.info("📊 Analyzing real database schema...")
        
        conn = self.connect_to_database()
        if not conn:
            return False
        
        try:
            cur = conn.cursor(cursor_factory=RealDictCursor)
            
            # Get all tables
            cur.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public'
                ORDER BY table_name
            """)
            
            tables = [row['table_name'] for row in cur.fetchall()]
            self.analysis_results['tables'] = tables
            
            logger.info(f"📋 Found {len(tables)} tables: {', '.join(tables)}")
            
            # Analyze solar data tables
            for table_name in ['solax_data', 'solax_data2']:
                if table_name in tables:
                    self.analyze_solar_table(cur, table_name)
                else:
                    logger.warning(f"⚠️ Table {table_name} not found")
            
            # Analyze weather data
            if 'weather_data' in tables:
                self.analyze_weather_table(cur)
            else:
                logger.warning("⚠️ weather_data table not found")
            
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"❌ Schema analysis failed: {e}")
            conn.close()
            return False
    
    def analyze_solar_table(self, cur, table_name):
        """Analyze solar data table"""
        
        logger.info(f"📊 Analyzing {table_name}...")
        
        # Get table schema
        cur.execute(f"""
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = '{table_name}'
            ORDER BY ordinal_position
        """)
        
        columns = cur.fetchall()
        column_names = [col['column_name'] for col in columns]
        
        # Check for important columns
        important_columns = ['timestamp', 'yield_today', 'total_yield', 'ac_power', 'battery_soc']
        missing_columns = []
        
        for col in important_columns:
            if col in column_names:
                logger.info(f"   ✅ {col}")
            else:
                logger.warning(f"   ❌ {col} - MISSING")
                missing_columns.append(col)
        
        # Get data statistics
        cur.execute(f"SELECT COUNT(*) as count FROM {table_name}")
        record_count = cur.fetchone()['count']
        
        if record_count > 0:
            # Get date range
            cur.execute(f"SELECT MIN(timestamp) as earliest, MAX(timestamp) as latest FROM {table_name}")
            date_range = cur.fetchone()
            
            # Get recent data count
            cur.execute(f"""
                SELECT COUNT(*) as recent_count 
                FROM {table_name} 
                WHERE timestamp >= NOW() - INTERVAL '7 days'
            """)
            recent_count = cur.fetchone()['recent_count']
            
            # Sample recent data
            available_columns = [col for col in ['timestamp', 'yield_today', 'ac_power', 'battery_soc'] if col in column_names]
            if available_columns:
                columns_str = ', '.join(available_columns)
                cur.execute(f"""
                    SELECT {columns_str}
                    FROM {table_name} 
                    ORDER BY timestamp DESC 
                    LIMIT 5
                """)
                sample_data = cur.fetchall()
            else:
                sample_data = []
            
            logger.info(f"   📊 Records: {record_count:,}")
            logger.info(f"   📅 Range: {date_range['earliest']} to {date_range['latest']}")
            logger.info(f"   ⏰ Recent (7d): {recent_count:,} records")
            
            if sample_data:
                logger.info(f"   📋 Sample data (latest 5):")
                for row in sample_data[:3]:  # Show first 3
                    row_str = ', '.join([f"{k}: {v}" for k, v in row.items() if k != 'timestamp'])
                    logger.info(f"      {row['timestamp']}: {row_str}")
        else:
            logger.warning(f"   ⚠️ No data in {table_name}")
        
        # Store results
        self.analysis_results[table_name] = {
            'columns': column_names,
            'missing_columns': missing_columns,
            'record_count': record_count,
            'has_data': record_count > 0
        }
    
    def analyze_weather_table(self, cur):
        """Analyze weather data table"""
        
        logger.info("🌤️ Analyzing weather_data...")
        
        # Get record count
        cur.execute("SELECT COUNT(*) as count FROM weather_data")
        record_count = cur.fetchone()['count']
        
        if record_count > 0:
            # Get date range
            cur.execute("SELECT MIN(timestamp) as earliest, MAX(timestamp) as latest FROM weather_data")
            date_range = cur.fetchone()
            
            logger.info(f"   📊 Records: {record_count:,}")
            logger.info(f"   📅 Range: {date_range['earliest']} to {date_range['latest']}")
        else:
            logger.warning("   ⚠️ No weather data")
        
        self.analysis_results['weather_data'] = {
            'record_count': record_count,
            'has_data': record_count > 0
        }
    
    def check_total_yield_columns(self):
        """Check if total_yield columns need to be added"""
        
        logger.info("🔧 Checking total_yield column requirements...")
        
        conn = self.connect_to_database()
        if not conn:
            return False
        
        try:
            cur = conn.cursor()
            
            needs_fix = False
            
            for table_name in ['solax_data', 'solax_data2']:
                if table_name in self.analysis_results:
                    table_info = self.analysis_results[table_name]
                    
                    if 'total_yield' in table_info['missing_columns']:
                        logger.warning(f"❌ {table_name} missing total_yield column")
                        needs_fix = True
                    else:
                        logger.info(f"✅ {table_name} has total_yield column")
            
            conn.close()
            
            if needs_fix:
                logger.info("🔧 Schema fixes needed - total_yield columns missing")
                return False
            else:
                logger.info("✅ Schema is ready - all required columns present")
                return True
                
        except Exception as e:
            logger.error(f"❌ Column check failed: {e}")
            conn.close()
            return False
    
    def generate_analysis_report(self):
        """Generate comprehensive analysis report"""
        
        logger.info("📋 Generating analysis report...")
        
        # Determine overall status
        has_solar_data = any(
            self.analysis_results.get(table, {}).get('has_data', False) 
            for table in ['solax_data', 'solax_data2']
        )
        
        schema_ready = self.check_total_yield_columns()
        
        report = {
            'analysis_date': datetime.now().isoformat(),
            'database_config': {
                'host': self.working_config['host'],
                'database': self.working_config['database'],
                'user': self.working_config['user']
            },
            'tables_found': len(self.analysis_results.get('tables', [])),
            'has_solar_data': has_solar_data,
            'schema_ready': schema_ready,
            'analysis_results': self.analysis_results,
            'status': 'ready_for_phase2' if (has_solar_data and schema_ready) else 'needs_schema_fixes',
            'next_steps': []
        }
        
        # Add recommendations
        if not has_solar_data:
            report['next_steps'].append("Import solar data into solax_data/solax_data2 tables")
        
        if not schema_ready:
            report['next_steps'].append("Run schema fixes to add missing total_yield columns")
        
        if has_solar_data and schema_ready:
            report['next_steps'].append("Proceed to Phase 2: Feature Engineering")
        
        # Save report
        os.makedirs('reports/phase1', exist_ok=True)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_path = f'reports/phase1/real_database_analysis_{timestamp}.json'
        
        import json
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        logger.info(f"📋 Report saved to: {report_path}")
        
        return report
    
    def print_summary(self, report):
        """Print analysis summary"""
        
        print("\n" + "="*80)
        print("📊 REAL DATABASE ANALYSIS SUMMARY")
        print("="*80)
        print(f"📅 Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🗄️ Database: {report['database_config']['database']}@{report['database_config']['host']}")
        print(f"👤 User: {report['database_config']['user']}")
        print(f"📋 Tables Found: {report['tables_found']}")
        print()
        
        # Data status
        print("📊 DATA STATUS:")
        for table in ['solax_data', 'solax_data2', 'weather_data']:
            if table in self.analysis_results:
                table_info = self.analysis_results[table]
                status = "✅ HAS DATA" if table_info.get('has_data', False) else "❌ NO DATA"
                count = table_info.get('record_count', 0)
                print(f"   📋 {table}: {status} ({count:,} records)")
                
                if table in ['solax_data', 'solax_data2'] and 'missing_columns' in table_info:
                    missing = table_info['missing_columns']
                    if missing:
                        print(f"      ⚠️ Missing columns: {', '.join(missing)}")
            else:
                print(f"   ❌ {table}: NOT FOUND")
        print()
        
        # Overall status
        print("🎯 OVERALL STATUS:")
        print(f"   Solar Data: {'✅ Available' if report['has_solar_data'] else '❌ Missing'}")
        print(f"   Schema: {'✅ Ready' if report['schema_ready'] else '❌ Needs Fixes'}")
        print(f"   Status: {report['status']}")
        print()
        
        # Next steps
        if report['next_steps']:
            print("📋 NEXT STEPS:")
            for step in report['next_steps']:
                print(f"   • {step}")
        else:
            print("🚀 READY TO PROCEED TO PHASE 2!")
        
        print("="*80)

def main():
    """Main real database analysis function"""
    
    print("📊 PHASE 1: REAL DATABASE ANALYSIS")
    print("="*60)
    print("🔗 Connecting to actual PostgreSQL database")
    print("📊 Analyzing real solar and weather data")
    print()
    
    try:
        # Initialize analyzer
        analyzer = RealDatabaseAnalyzer()
        
        # Find working connection
        if not analyzer.find_working_connection():
            print("❌ Cannot connect to database")
            return False
        
        # Analyze schema
        success = analyzer.analyze_database_schema()
        
        if success:
            # Generate report
            report = analyzer.generate_analysis_report()
            
            # Print summary
            analyzer.print_summary(report)
            
            print(f"\n📋 Detailed report saved")
            print("🎉 Real database analysis completed!")
            
            return report['status'] == 'ready_for_phase2'
        else:
            print("❌ Database analysis failed")
            return False
            
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        logger.exception("Real database analysis failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
