#!/usr/bin/env python3
"""
Simple CSV Reader without pandas
Read CSV manually and extract April 2024 data
"""

import csv
from datetime import datetime

def read_csv_manually():
    """Read CSV file manually without pandas"""
    print("📊 READING CSV FILE MANUALLY")
    print("=" * 40)
    
    file_path = "data/raw/System2/Plant Reports 2024-03-01-2024-06-28.csv"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            
            # Read header
            header = next(reader)
            print(f"Header: {header}")
            
            # Find column indices
            time_col_idx = None
            yield_col_idx = None
            
            for i, col in enumerate(header):
                if 'Update time' in col or 'time' in col.lower():
                    time_col_idx = i
                elif 'Daily inverter output' in col and 'kWh' in col:
                    yield_col_idx = i
                elif 'Daily PV Yield' in col and 'kWh' in col:
                    if yield_col_idx is None:  # Use as fallback
                        yield_col_idx = i
            
            print(f"Time column index: {time_col_idx}")
            print(f"Yield column index: {yield_col_idx}")
            
            if time_col_idx is None or yield_col_idx is None:
                print("❌ Required columns not found")
                return None
            
            # Read data
            april_data = []
            total_rows = 0
            
            for row in reader:
                total_rows += 1
                
                if len(row) > max(time_col_idx, yield_col_idx):
                    try:
                        # Parse date
                        time_str = row[time_col_idx]
                        dt = datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
                        
                        # Check if April 2024
                        if dt.year == 2024 and dt.month == 4:
                            yield_str = row[yield_col_idx]
                            try:
                                yield_val = float(yield_str)
                                if yield_val > 0:
                                    april_data.append({
                                        'date': dt.date(),
                                        'datetime': dt,
                                        'yield': yield_val
                                    })
                            except ValueError:
                                continue
                                
                    except ValueError:
                        continue
            
            print(f"Total rows processed: {total_rows}")
            print(f"April 2024 records found: {len(april_data)}")
            
            if april_data:
                # Group by date and get max yield per day
                daily_yields = {}
                for record in april_data:
                    date = record['date']
                    yield_val = record['yield']
                    
                    if date not in daily_yields or yield_val > daily_yields[date]:
                        daily_yields[date] = yield_val
                
                # Convert to list
                daily_data = []
                for date, yield_val in sorted(daily_yields.items()):
                    daily_data.append({'date': date, 'daily_yield': yield_val})
                
                print(f"\nDaily data extracted: {len(daily_data)} days")
                
                if daily_data:
                    yields = [d['daily_yield'] for d in daily_data]
                    print(f"Yield range: {min(yields):.1f} - {max(yields):.1f} kWh")
                    print(f"Average: {sum(yields)/len(yields):.1f} kWh/day")
                    print(f"Total: {sum(yields):.1f} kWh")
                    
                    print(f"\nSample data:")
                    for i, record in enumerate(daily_data[:10]):
                        print(f"  {record['date']}: {record['daily_yield']:.1f} kWh")
                    
                    # Save to simple CSV
                    output_file = "april_2024_system2_manual.csv"
                    with open(output_file, 'w', newline='') as outf:
                        writer = csv.writer(outf)
                        writer.writerow(['date', 'daily_yield'])
                        for record in daily_data:
                            writer.writerow([record['date'], record['daily_yield']])
                    
                    print(f"\n💾 Data saved to: {output_file}")
                    return daily_data
                
            else:
                print("❌ No valid April 2024 data found")
                return None
                
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def main():
    """Main function"""
    print("🚀 SIMPLE CSV READER - NO PANDAS")
    print("=" * 40)
    
    april_data = read_csv_manually()
    
    if april_data:
        print("\n🎉 SUCCESS! April 2024 System 2 data extracted")
        print("✅ Ready to proceed with comparison")
        return april_data
    else:
        print("\n❌ FAILED to extract data")
        return None

if __name__ == "__main__":
    main()
