#!/usr/bin/env python3
"""
Check Available Data
Find what data is actually available in the database
"""

import os
import sys
import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime

def get_db_connection():
    """Get PostgreSQL database connection"""
    try:
        return psycopg2.connect(
            host=os.getenv("DB_HOST", "localhost"),
            database=os.getenv("DB_NAME", "solar_prediction"),
            user=os.getenv("DB_USER", "postgres"),
            password=os.getenv("DB_PASSWORD", "postgres"),
            port=os.getenv("DB_PORT", "5432")
        )
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return None

def check_data_availability():
    """Check what data is available"""
    print("🔍 CHECKING AVAILABLE DATA IN DATABASE")
    print("=" * 50)
    
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            # Check System 1 data availability
            print("\n📊 System 1 (solax_data) Data Availability:")
            cur.execute("""
                SELECT 
                    EXTRACT(year FROM timestamp) as year,
                    EXTRACT(month FROM timestamp) as month,
                    COUNT(*) as records,
                    MIN(DATE(timestamp)) as first_date,
                    MAX(DATE(timestamp)) as last_date,
                    AVG(yield_today) as avg_yield
                FROM solax_data 
                WHERE yield_today > 0
                GROUP BY EXTRACT(year FROM timestamp), EXTRACT(month FROM timestamp)
                ORDER BY year, month
            """)
            
            results = cur.fetchall()
            
            print("Year | Month | Records | First Date | Last Date  | Avg Yield")
            print("-" * 65)
            
            april_data = []
            for row in results:
                year = int(row['year'])
                month = int(row['month'])
                records = row['records']
                first_date = row['first_date']
                last_date = row['last_date']
                avg_yield = row['avg_yield'] or 0
                
                print(f"{year} | {month:5d} | {records:7d} | {first_date} | {last_date} | {avg_yield:8.1f}")
                
                if month == 4:  # April data
                    april_data.append({
                        'year': year,
                        'records': records,
                        'avg_yield': avg_yield
                    })
            
            print(f"\n📊 System 1 April Data Found:")
            for data in april_data:
                print(f"   April {data['year']}: {data['records']} records, {data['avg_yield']:.1f} kWh avg")
            
            # Check System 2 data availability
            print("\n📊 System 2 (solax_data2) Data Availability:")
            cur.execute("""
                SELECT 
                    EXTRACT(year FROM timestamp) as year,
                    EXTRACT(month FROM timestamp) as month,
                    COUNT(*) as records,
                    MIN(DATE(timestamp)) as first_date,
                    MAX(DATE(timestamp)) as last_date,
                    AVG(yield_today) as avg_yield
                FROM solax_data2 
                WHERE yield_today > 0
                GROUP BY EXTRACT(year FROM timestamp), EXTRACT(month FROM timestamp)
                ORDER BY year, month
            """)
            
            results = cur.fetchall()
            
            print("Year | Month | Records | First Date | Last Date  | Avg Yield")
            print("-" * 65)
            
            april_data_s2 = []
            for row in results:
                year = int(row['year'])
                month = int(row['month'])
                records = row['records']
                first_date = row['first_date']
                last_date = row['last_date']
                avg_yield = row['avg_yield'] or 0
                
                print(f"{year} | {month:5d} | {records:7d} | {first_date} | {last_date} | {avg_yield:8.1f}")
                
                if month == 4:  # April data
                    april_data_s2.append({
                        'year': year,
                        'records': records,
                        'avg_yield': avg_yield
                    })
            
            print(f"\n📊 System 2 April Data Found:")
            for data in april_data_s2:
                print(f"   April {data['year']}: {data['records']} records, {data['avg_yield']:.1f} kWh avg")
            
            # Check earliest available data
            print(f"\n📅 EARLIEST AVAILABLE DATA:")
            cur.execute("SELECT MIN(timestamp) as earliest FROM solax_data WHERE yield_today > 0")
            earliest_s1 = cur.fetchone()['earliest']
            
            cur.execute("SELECT MIN(timestamp) as earliest FROM solax_data2 WHERE yield_today > 0")
            earliest_s2 = cur.fetchone()['earliest']
            
            print(f"   System 1: {earliest_s1}")
            print(f"   System 2: {earliest_s2}")
            
            # Suggest alternative comparison periods
            print(f"\n💡 SUGGESTED COMPARISON PERIODS:")
            
            # Find months with data in both years
            cur.execute("""
                WITH monthly_data AS (
                    SELECT 
                        EXTRACT(year FROM timestamp) as year,
                        EXTRACT(month FROM timestamp) as month,
                        COUNT(*) as records
                    FROM solax_data 
                    WHERE yield_today > 0
                    GROUP BY EXTRACT(year FROM timestamp), EXTRACT(month FROM timestamp)
                )
                SELECT month, COUNT(*) as year_count, STRING_AGG(year::text, ', ') as years
                FROM monthly_data
                GROUP BY month
                HAVING COUNT(*) >= 2
                ORDER BY month
            """)
            
            multi_year_months = cur.fetchall()
            
            for row in multi_year_months:
                month = int(row['month'])
                year_count = row['year_count']
                years = row['years']
                month_name = ['', 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 
                             'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'][month]
                
                print(f"   {month_name} ({month:2d}): Available in {year_count} years ({years})")
    
    except Exception as e:
        print(f"❌ Error checking data: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        conn.close()

def main():
    """Main data check"""
    print("🔍 DATABASE DATA AVAILABILITY CHECK")
    print("=" * 50)
    print(f"Check Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    check_data_availability()
    
    print(f"\n✅ Data availability check completed!")

if __name__ == "__main__":
    main()
