#!/usr/bin/env python3
"""
ΠΡΑΓΜΑΤΙΚΟ ML ΜΟΝΤΕΛΟ ΒΑΣΙΣΜΕΝΟ ΣΕ ΙΣΤΟΡΙΚΑ ΔΕΔΟΜΕΝΑ
Αντί για hardcoded συντελεστές, μαθαίνει από πραγματικά δεδομένα
"""

import psycopg2
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Tuple, Optional
import math

class HistoricalPredictionModel:
    """
    Μοντέλο πρόβλεψης που βασίζεται σε ιστορικά δεδομένα
    Χρησιμοποιεί similarity matching για παρόμοιες συνθήκες
    """
    
    def __init__(self, db_connection_string: str):
        self.db_connection_string = db_connection_string
        self.historical_data = None
        self.weather_data = None
        
    def load_historical_data(self, days_back: int = 365):
        """Φόρτωση ιστορικών δεδομένων από τη βάση"""
        print(f"📊 Φόρτωση ιστορικών δεδομένων ({days_back} ημέρες)...")
        
        conn = psycopg2.connect(self.db_connection_string)
        
        # Φόρτωση δεδομένων παραγωγής
        query = """
        SELECT 
            s.timestamp,
            s.ac_power as system1_power,
            s2.ac_power as system2_power,
            EXTRACT(hour FROM s.timestamp) as hour,
            EXTRACT(month FROM s.timestamp) as month,
            EXTRACT(dow FROM s.timestamp) as day_of_week,
            EXTRACT(doy FROM s.timestamp) as day_of_year
        FROM solax_data s
        LEFT JOIN solax_data2 s2 ON s.timestamp = s2.timestamp
        WHERE s.timestamp >= %s
            AND s.ac_power >= 0
        ORDER BY s.timestamp
        """
        
        start_date = datetime.now() - timedelta(days=days_back)
        self.historical_data = pd.read_sql(query, conn, params=[start_date])
        
        # Φόρτωση καιρικών δεδομένων
        weather_query = """
        SELECT 
            timestamp,
            temperature_2m,
            cloud_cover,
            shortwave_radiation as ghi
        FROM weather_data
        WHERE timestamp >= %s
        ORDER BY timestamp
        """
        
        self.weather_data = pd.read_sql(weather_query, conn, params=[start_date])
        
        conn.close()
        
        # Συνδυασμός δεδομένων
        self.combined_data = pd.merge(
            self.historical_data, 
            self.weather_data, 
            on='timestamp', 
            how='left'
        )
        
        print(f"✅ Φορτώθηκαν {len(self.combined_data)} records")
        
    def find_similar_conditions(self, 
                               target_hour: int,
                               target_month: int, 
                               target_ghi: float,
                               target_temp: float,
                               target_clouds: float,
                               system_id: int = 1) -> List[float]:
        """
        Βρίσκει παρόμοιες ιστορικές συνθήκες και επιστρέφει τις παραγωγές
        """
        if self.combined_data is None:
            raise ValueError("Δεν έχουν φορτωθεί ιστορικά δεδομένα")
        
        # Φιλτράρισμα για παρόμοιες συνθήκες
        similar_data = self.combined_data[
            (abs(self.combined_data['hour'] - target_hour) <= 1) &  # ±1 ώρα
            (abs(self.combined_data['month'] - target_month) <= 1) &  # ±1 μήνας
            (abs(self.combined_data['ghi'] - target_ghi) <= target_ghi * 0.3) &  # ±30% GHI
            (abs(self.combined_data['temperature_2m'] - target_temp) <= 10) &  # ±10°C
            (abs(self.combined_data['cloud_cover'] - target_clouds) <= 30)  # ±30% clouds
        ].copy()
        
        if len(similar_data) == 0:
            # Αν δεν βρέθηκαν παρόμοια δεδομένα, χαλάρωσε τα κριτήρια
            similar_data = self.combined_data[
                (abs(self.combined_data['hour'] - target_hour) <= 2) &  # ±2 ώρες
                (abs(self.combined_data['month'] - target_month) <= 2)   # ±2 μήνες
            ].copy()
        
        # Επιλογή στήλης ανάλογα με το σύστημα
        power_column = 'system1_power' if system_id == 1 else 'system2_power'
        
        # Φιλτράρισμα για θετικές τιμές
        valid_powers = similar_data[similar_data[power_column] > 0][power_column].tolist()
        
        return valid_powers
    
    def predict_power(self, 
                     hour: int,
                     month: int,
                     ghi: float, 
                     temperature: float,
                     cloud_cover: float,
                     system_id: int = 1) -> float:
        """
        Πρόβλεψη ισχύος βασισμένη σε ιστορικά δεδομένα
        """
        # Βρες παρόμοιες συνθήκες
        similar_powers = self.find_similar_conditions(
            hour, month, ghi, temperature, cloud_cover, system_id
        )
        
        if not similar_powers:
            # Fallback: χρησιμοποίησε μόνο την ώρα και τον μήνα
            fallback_data = self.combined_data[
                (self.combined_data['hour'] == hour) &
                (self.combined_data['month'] == month)
            ]
            
            power_column = 'system1_power' if system_id == 1 else 'system2_power'
            similar_powers = fallback_data[fallback_data[power_column] > 0][power_column].tolist()
        
        if not similar_powers:
            # Τελικό fallback: μηδενική παραγωγή
            return 0.0
        
        # Υπολογισμός weighted average
        # Δώσε περισσότερο βάρος σε πρόσφατα δεδομένα
        weights = np.exp(-np.arange(len(similar_powers)) * 0.01)  # Exponential decay
        
        if len(weights) != len(similar_powers):
            weights = np.ones(len(similar_powers))
        
        predicted_power = np.average(similar_powers, weights=weights)
        
        # Εφαρμογή διόρθωσης για GHI
        if ghi > 0:
            # Βρες μέσο GHI για παρόμοιες συνθήκες
            avg_ghi = self.combined_data[
                (abs(self.combined_data['hour'] - hour) <= 1) &
                (abs(self.combined_data['month'] - month) <= 1)
            ]['ghi'].mean()
            
            if avg_ghi > 0:
                ghi_factor = ghi / avg_ghi
                predicted_power *= ghi_factor
        
        return max(0.0, predicted_power)
    
    def analyze_historical_patterns(self, system_id: int = 1) -> Dict:
        """Ανάλυση ιστορικών patterns"""
        if self.combined_data is None:
            raise ValueError("Δεν έχουν φορτωθεί ιστορικά δεδομένα")
        
        power_column = 'system1_power' if system_id == 1 else 'system2_power'
        
        analysis = {
            'max_power': self.combined_data[power_column].max(),
            'avg_power': self.combined_data[self.combined_data[power_column] > 0][power_column].mean(),
            'p95_power': self.combined_data[power_column].quantile(0.95),
            'p90_power': self.combined_data[power_column].quantile(0.90),
            'hourly_avg': self.combined_data.groupby('hour')[power_column].mean().to_dict(),
            'monthly_avg': self.combined_data.groupby('month')[power_column].mean().to_dict(),
            'total_records': len(self.combined_data),
            'valid_records': len(self.combined_data[self.combined_data[power_column] > 0])
        }
        
        return analysis

def test_historical_model():
    """Δοκιμή του ιστορικού μοντέλου"""
    print("🧪 ΔΟΚΙΜΗ ΙΣΤΟΡΙΚΟΥ ΜΟΝΤΕΛΟΥ")
    
    model = HistoricalPredictionModel('postgresql://postgres:postgres@localhost/solar_prediction')
    
    try:
        # Φόρτωση δεδομένων
        model.load_historical_data(days_back=180)  # 6 μήνες
        
        # Ανάλυση patterns
        for system_id in [1, 2]:
            print(f"\n📊 ΑΝΑΛΥΣΗ SYSTEM {system_id}:")
            analysis = model.analyze_historical_patterns(system_id)
            
            print(f"   Μέγιστη ισχύς: {analysis['max_power']:.0f}W")
            print(f"   Μέση ισχύς: {analysis['avg_power']:.0f}W")
            print(f"   95th percentile: {analysis['p95_power']:.0f}W")
            print(f"   Έγκυρα records: {analysis['valid_records']:,}")
        
        # Δοκιμή πρόβλεψης για τρέχουσες συνθήκες
        print(f"\n🔮 ΔΟΚΙΜΗ ΠΡΟΒΛΕΨΗΣ:")
        current_hour = datetime.now().hour
        current_month = datetime.now().month
        
        # Παράδειγμα συνθηκών
        test_conditions = [
            (12, 6, 900, 25, 20),  # Μεσημέρι Ιουνίου, καλός καιρός
            (14, 6, 700, 30, 40),  # Απόγευμα Ιουνίου, λίγα σύννεφα
            (10, 6, 600, 22, 60),  # Πρωί Ιουνίου, πολλά σύννεφα
        ]
        
        for hour, month, ghi, temp, clouds in test_conditions:
            for system_id in [1, 2]:
                prediction = model.predict_power(hour, month, ghi, temp, clouds, system_id)
                print(f"   System {system_id}, {hour}:00, {ghi}W/m², {temp}°C, {clouds}% clouds → {prediction:.0f}W")
        
    except Exception as e:
        print(f"❌ Σφάλμα: {e}")

if __name__ == "__main__":
    test_historical_model()
