#!/usr/bin/env python3
"""
Script για την αντιγραφή του Ενισχυμένου Μοντέλου v2 στον κατάλληλο φάκελο.

Αυτό το script αντιγράφει το μοντέλο LightGBM, τα χαρακτηριστικά, και τα μεταδεδομένα
από τον φάκελο models/enhanced_v2_all στον φάκελο app/models/enhanced_v2.
"""

import os
import sys
import shutil
import json
import logging
from pathlib import Path

# Ρύθμιση του logger
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Προσθήκη του project directory στο path
project_dir = Path(__file__).parent.parent
sys.path.append(str(project_dir))

def create_directory(directory: str):
    """
    Δημιουργία φακέλου αν δεν υπάρχει.
    
    Args:
        directory: Διαδρομή του φακέλου
    """
    os.makedirs(directory, exist_ok=True)
    logger.info(f"Φάκελος δημιουργήθηκε: {directory}")

def copy_file(source: str, destination: str):
    """
    Αντιγραφή αρχείου.
    
    Args:
        source: Διαδρομή του αρχείου πηγής
        destination: Διαδρομή του αρχείου προορισμού
    """
    try:
        shutil.copy2(source, destination)
        logger.info(f"Αρχείο αντιγράφηκε: {source} -> {destination}")
    except FileNotFoundError:
        logger.error(f"Το αρχείο δεν βρέθηκε: {source}")
        raise
    except PermissionError:
        logger.error(f"Δεν υπάρχουν δικαιώματα για την αντιγραφή του αρχείου: {source}")
        raise
    except Exception as e:
        logger.error(f"Σφάλμα κατά την αντιγραφή του αρχείου: {e}")
        raise

def update_metadata(metadata_path: str):
    """
    Ενημέρωση των μεταδεδομένων του μοντέλου.
    
    Args:
        metadata_path: Διαδρομή του αρχείου μεταδεδομένων
    """
    try:
        # Φόρτωση των μεταδεδομένων
        with open(metadata_path, "r") as f:
            metadata = json.load(f)
        
        # Προσθήκη πληροφοριών για το μοντέλο
        metadata["model_type"] = "lightgbm"
        metadata["model_file"] = "lightgbm_model.txt"
        metadata["feature_columns_file"] = "feature_columns.json"
        metadata["normalization_params_file"] = "normalization_params.json"
        metadata["integration_date"] = "2025-04-17"
        metadata["integration_version"] = "2.0.0"
        
        # Αποθήκευση των ενημερωμένων μεταδεδομένων
        with open(metadata_path, "w") as f:
            json.dump(metadata, f, indent=4)
        
        logger.info(f"Μεταδεδομένα ενημερώθηκαν: {metadata_path}")
    except FileNotFoundError:
        logger.error(f"Το αρχείο μεταδεδομένων δεν βρέθηκε: {metadata_path}")
        raise
    except json.JSONDecodeError:
        logger.error(f"Το αρχείο μεταδεδομένων δεν είναι έγκυρο JSON: {metadata_path}")
        raise
    except Exception as e:
        logger.error(f"Σφάλμα κατά την ενημέρωση των μεταδεδομένων: {e}")
        raise

def create_normalization_params(source_dir: str, destination_dir: str):
    """
    Δημιουργία αρχείου παραμέτρων κανονικοποίησης αν δεν υπάρχει.
    
    Args:
        source_dir: Διαδρομή του φακέλου πηγής
        destination_dir: Διαδρομή του φακέλου προορισμού
    """
    # Διαδρομή του αρχείου παραμέτρων κανονικοποίησης
    norm_path = os.path.join(source_dir, "normalization_params.json")
    
    # Έλεγχος αν υπάρχει το αρχείο
    if os.path.exists(norm_path):
        # Αντιγραφή του αρχείου
        copy_file(norm_path, os.path.join(destination_dir, "normalization_params.json"))
    else:
        # Φόρτωση των χαρακτηριστικών
        feature_path = os.path.join(source_dir, "feature_columns.json")
        with open(feature_path, "r") as f:
            feature_columns = json.load(f)
        
        # Δημιουργία προεπιλεγμένων παραμέτρων κανονικοποίησης
        normalization_params = {
            "method": "min-max",
            "params": {}
        }
        
        # Προσθήκη παραμέτρων για κάθε χαρακτηριστικό
        for col in feature_columns:
            raw_col = col.replace("_normalized", "")
            normalization_params["params"][raw_col] = {
                "min": 0,
                "max": 1
            }
        
        # Αποθήκευση των παραμέτρων κανονικοποίησης
        norm_path = os.path.join(destination_dir, "normalization_params.json")
        with open(norm_path, "w") as f:
            json.dump(normalization_params, f, indent=4)
        
        logger.info(f"Παράμετροι κανονικοποίησης δημιουργήθηκαν: {norm_path}")

def copy_model():
    """
    Αντιγραφή του Ενισχυμένου Μοντέλου v2 στον κατάλληλο φάκελο.
    """
    # Διαδρομές
    source_dir = os.path.join(project_dir, "models/enhanced_v2_all")
    destination_dir = os.path.join(project_dir, "app/models/enhanced_v2")
    
    # Έλεγχος αν υπάρχει ο φάκελος πηγής
    if not os.path.exists(source_dir):
        logger.error(f"Ο φάκελος πηγής δεν υπάρχει: {source_dir}")
        raise FileNotFoundError(f"Ο φάκελος πηγής δεν υπάρχει: {source_dir}")
    
    # Δημιουργία του φακέλου προορισμού αν δεν υπάρχει
    create_directory(destination_dir)
    
    # Αντιγραφή του μοντέλου
    copy_file(
        os.path.join(source_dir, "lightgbm_model.txt"),
        os.path.join(destination_dir, "lightgbm_model.txt")
    )
    
    # Αντιγραφή των χαρακτηριστικών
    copy_file(
        os.path.join(source_dir, "feature_columns.json"),
        os.path.join(destination_dir, "feature_columns.json")
    )
    
    # Αντιγραφή των μεταδεδομένων
    copy_file(
        os.path.join(source_dir, "metadata.json"),
        os.path.join(destination_dir, "metadata.json")
    )
    
    # Δημιουργία αρχείου παραμέτρων κανονικοποίησης
    create_normalization_params(source_dir, destination_dir)
    
    # Ενημέρωση των μεταδεδομένων
    update_metadata(os.path.join(destination_dir, "metadata.json"))
    
    logger.info(f"Το μοντέλο αντιγράφηκε στο {destination_dir}")

if __name__ == "__main__":
    try:
        copy_model()
        logger.info("Η αντιγραφή του μοντέλου ολοκληρώθηκε με επιτυχία")
    except Exception as e:
        logger.error(f"Σφάλμα κατά την αντιγραφή του μοντέλου: {e}")
        sys.exit(1)
