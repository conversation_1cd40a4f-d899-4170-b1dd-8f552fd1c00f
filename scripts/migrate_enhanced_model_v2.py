#!/usr/bin/env python3
"""
Migrate Enhanced Model v2 from original project
"""
import os
import sys
import shutil
import json
from pathlib import Path
from datetime import datetime

def migrate_enhanced_model():
    """Migrate Enhanced Model v2 files and scripts"""
    print("🚀 MIGRATING ENHANCED MODEL V2")
    print("=" * 60)
    
    # Source and destination paths
    source_project = Path("/home/<USER>/mining-ai-project")
    current_project = Path(".")
    
    # 1. Copy Enhanced Model v2 files
    source_models = source_project / "models" / "enhanced_v2"
    dest_models = current_project / "models" / "enhanced_v2"
    
    if source_models.exists():
        print("📁 Copying Enhanced Model v2 files...")
        dest_models.mkdir(exist_ok=True)
        
        for file in source_models.glob("*"):
            if file.is_file():
                dest_file = dest_models / file.name
                shutil.copy2(file, dest_file)
                print(f"   ✅ Copied {file.name}")
    
    # 2. Copy training scripts
    source_scripts = source_project / "scripts"
    dest_scripts = current_project / "scripts"
    
    enhanced_scripts = [
        "train_enhanced_solar_model.py",
        "test_enhanced_model_v2.py",
        "copy_enhanced_model_v2.py"
    ]
    
    print("\n📜 Copying training scripts...")
    for script_name in enhanced_scripts:
        source_script = source_scripts / script_name
        if source_script.exists():
            dest_script = dest_scripts / script_name
            shutil.copy2(source_script, dest_script)
            print(f"   ✅ Copied {script_name}")
    
    # 3. Update symlink to point to Enhanced Model v2
    models_dir = current_project / "models"
    enhanced_model_link = models_dir / "enhanced_model_v2.json"
    
    if (dest_models / "xgboost_model.json").exists():
        if enhanced_model_link.exists():
            enhanced_model_link.unlink()
        
        enhanced_model_link.symlink_to("enhanced_v2/xgboost_model.json")
        print(f"   ✅ Created symlink to Enhanced Model v2")
    
    print("\n✅ Enhanced Model v2 migration completed!")
    return True

if __name__ == "__main__":
    migrate_enhanced_model()
