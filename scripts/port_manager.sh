#!/bin/bash
# Solar Prediction System - Port Manager
# Centralized port management and cleanup

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Port Registry
declare -A SOLAR_PORTS=(
    [8080]="Web Server"
    [8100]="Enhanced Production API"
    [8103]="Charts API"
    [8108]="Configuration Manager"
    [8110]="Enhanced Billing System"
)

# Function to check port status
check_port_status() {
    local port=$1
    local service_name=${SOLAR_PORTS[$port]:-"Unknown Service"}
    
    local pid=$(lsof -ti:$port 2>/dev/null)
    if [ -n "$pid" ]; then
        local process_info=$(ps -p $pid -o comm= 2>/dev/null)
        echo -e "${GREEN}✅ Port $port${NC} - ${service_name} (PID: $pid, Process: $process_info)"
        return 0
    else
        echo -e "${RED}❌ Port $port${NC} - ${service_name} (Not running)"
        return 1
    fi
}

# Function to cleanup specific port
cleanup_port() {
    local port=$1
    local service_name=${SOLAR_PORTS[$port]:-"Unknown Service"}
    
    echo "🧹 Cleaning up port $port ($service_name)..."
    local pids=$(lsof -ti:$port 2>/dev/null)
    
    if [ -n "$pids" ]; then
        echo "   Found processes: $pids"
        for pid in $pids; do
            if ps -p $pid > /dev/null 2>&1; then
                echo "   Stopping PID $pid gracefully..."
                kill $pid 2>/dev/null
                sleep 2
                
                # Force kill if still running
                if ps -p $pid > /dev/null 2>&1; then
                    echo "   Force killing PID $pid..."
                    kill -9 $pid 2>/dev/null
                fi
            fi
        done
        
        # Verify cleanup
        sleep 1
        if ! lsof -ti:$port >/dev/null 2>&1; then
            echo -e "   ${GREEN}✅ Port $port cleaned successfully${NC}"
        else
            echo -e "   ${RED}❌ Port $port still in use${NC}"
        fi
    else
        echo -e "   ${GREEN}✅ Port $port already free${NC}"
    fi
}

# Function to cleanup all solar ports
cleanup_all_ports() {
    echo -e "${BLUE}🧹 Cleaning up all Solar System ports...${NC}"
    echo "=================================================="
    
    for port in "${!SOLAR_PORTS[@]}"; do
        cleanup_port $port
    done
    
    # Also cleanup any remaining solar processes
    echo ""
    echo "🔍 Cleaning up remaining solar processes..."
    pkill -f "production_scripts_api.py" 2>/dev/null && echo "   Stopped production_scripts_api.py"
    pkill -f "charts_api.py" 2>/dev/null && echo "   Stopped charts_api.py"
    pkill -f "enhanced_billing_system.py" 2>/dev/null && echo "   Stopped enhanced_billing_system.py"
    pkill -f "configuration_manager.py" 2>/dev/null && echo "   Stopped configuration_manager.py"
    pkill -f "alert_system.py" 2>/dev/null && echo "   Stopped alert_system.py"
    pkill -f "greek_telegram_bot.py" 2>/dev/null && echo "   Stopped greek_telegram_bot.py"
    
    echo -e "${GREEN}✅ All ports and processes cleaned${NC}"
}

# Function to show port status
show_port_status() {
    echo -e "${BLUE}📊 Solar System Port Status${NC}"
    echo "=================================================="
    
    local running_count=0
    local total_count=${#SOLAR_PORTS[@]}
    
    for port in $(printf '%s\n' "${!SOLAR_PORTS[@]}" | sort -n); do
        if check_port_status $port; then
            ((running_count++))
        fi
    done
    
    echo ""
    echo -e "${BLUE}Summary: ${running_count}/${total_count} services running${NC}"
    
    # Show additional processes
    echo ""
    echo -e "${BLUE}📋 Additional Solar Processes:${NC}"
    local alert_pids=$(pgrep -f "alert_system.py" 2>/dev/null)
    local telegram_pids=$(pgrep -f "greek_telegram_bot.py" 2>/dev/null)
    
    if [ -n "$alert_pids" ]; then
        echo -e "${GREEN}✅ Alert System${NC} (PIDs: $alert_pids)"
    else
        echo -e "${RED}❌ Alert System${NC} (Not running)"
    fi
    
    if [ -n "$telegram_pids" ]; then
        echo -e "${GREEN}✅ Telegram Bot${NC} (PIDs: $telegram_pids)"
    else
        echo -e "${RED}❌ Telegram Bot${NC} (Not running)"
    fi
}

# Function to test all services
test_services() {
    echo -e "${BLUE}🔍 Testing Solar System Services${NC}"
    echo "=================================================="
    
    # Test Web Server
    if curl -s "http://localhost:8080/" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Web Server (8080)${NC} - Responding"
    else
        echo -e "${RED}❌ Web Server (8080)${NC} - Not responding"
    fi
    
    # Test Enhanced Production API
    if curl -s "http://localhost:8100/health" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Enhanced Production API (8100)${NC} - Responding"
    else
        echo -e "${RED}❌ Enhanced Production API (8100)${NC} - Not responding"
    fi
    
    # Test Charts API
    if curl -s "http://localhost:8103/api/charts/statistics" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Charts API (8103)${NC} - Responding"
    else
        echo -e "${RED}❌ Charts API (8103)${NC} - Not responding"
    fi
    
    # Test Configuration Manager
    if curl -s "http://localhost:8108/" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Configuration Manager (8108)${NC} - Responding"
    else
        echo -e "${RED}❌ Configuration Manager (8108)${NC} - Not responding"
    fi
    
    # Test Enhanced Billing
    if curl -s "http://localhost:8110/" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Enhanced Billing (8110)${NC} - Responding"
    else
        echo -e "${RED}❌ Enhanced Billing (8110)${NC} - Not responding"
    fi
}

# Main function
main() {
    case "${1:-status}" in
        "status"|"")
            show_port_status
            ;;
        "cleanup")
            cleanup_all_ports
            ;;
        "test")
            test_services
            ;;
        "clean-port")
            if [ -z "$2" ]; then
                echo "Usage: $0 clean-port <port_number>"
                exit 1
            fi
            cleanup_port $2
            ;;
        "help")
            echo "Solar System Port Manager"
            echo "========================"
            echo "Usage: $0 [command]"
            echo ""
            echo "Commands:"
            echo "  status      Show port status (default)"
            echo "  cleanup     Cleanup all solar ports"
            echo "  test        Test all services"
            echo "  clean-port  Cleanup specific port"
            echo "  help        Show this help"
            echo ""
            echo "Examples:"
            echo "  $0                    # Show status"
            echo "  $0 cleanup            # Cleanup all ports"
            echo "  $0 clean-port 8080    # Cleanup port 8080"
            echo "  $0 test               # Test all services"
            ;;
        *)
            echo "Unknown command: $1"
            echo "Use '$0 help' for available commands"
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
