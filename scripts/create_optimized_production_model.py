#!/usr/bin/env python3
"""
Create Optimized Production Model
Creates the missing optimized production model using existing data
"""

import os
import sys
import joblib
import numpy as np
import pandas as pd
from pathlib import Path
from sklearn.ensemble import GradientBoostingRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, r2_score
import psycopg2
from psycopg2.extras import RealDictCursor
from dotenv import load_dotenv
import json
from datetime import datetime, timedelta

# Load environment variables
load_dotenv()

class OptimizedModelCreator:
    def __init__(self):
        self.project_root = Path("/home/<USER>/solar-prediction-project")
        self.models_dir = self.project_root / "models" / "optimized_production_model"
        self.models_dir.mkdir(parents=True, exist_ok=True)
        
    def get_db_connection(self):
        """Get database connection"""
        try:
            # Try to parse DATABASE_URL first
            database_url = os.getenv('DATABASE_URL')
            if database_url:
                conn = psycopg2.connect(database_url)
            else:
                # Fallback to individual parameters
                conn = psycopg2.connect(
                    host=os.getenv('DB_HOST', 'localhost'),
                    database=os.getenv('DB_NAME', 'solar_prediction'),
                    user=os.getenv('DB_USER', 'postgres'),
                    password=os.getenv('DB_PASSWORD', 'postgres'),
                    port=os.getenv('DB_PORT', '5432')
                )
            return conn
        except Exception as e:
            print(f"Database connection failed: {e}")
            return None
    
    def extract_features(self, timestamp, temp=25, clouds=50, ghi=500, soc=50):
        """Extract features from timestamp and weather data"""
        dt = pd.to_datetime(timestamp)
        
        return {
            'hour': dt.hour,
            'month': dt.month,
            'day_of_year': dt.timetuple().tm_yday,
            'season': (dt.month - 1) // 3,
            'temperature': temp,
            'cloud_cover': clouds,
            'ghi': ghi,
            'soc': soc
        }
    
    def get_training_data(self, system_table):
        """Get training data from database"""
        conn = self.get_db_connection()
        if not conn:
            return None
            
        try:
            query = f"""
            SELECT
                s.timestamp,
                s.yield_today,
                s.soc,
                s.bat_power,
                s.ac_power,
                s.temperature,
                w.cloud_cover,
                w.global_horizontal_irradiance as ghi
            FROM {system_table} s
            LEFT JOIN weather_data w ON DATE(s.timestamp) = DATE(w.timestamp)
            WHERE s.timestamp >= NOW() - INTERVAL '90 days'
            AND s.yield_today IS NOT NULL
            AND s.yield_today > 0
            ORDER BY s.timestamp
            """
            
            df = pd.read_sql(query, conn)
            conn.close()
            
            if df.empty:
                print(f"No data found for {system_table}")
                return None
                
            print(f"Loaded {len(df)} records for {system_table}")
            return df
            
        except Exception as e:
            print(f"Error loading data for {system_table}: {e}")
            if conn:
                conn.close()
            return None
    
    def prepare_hourly_data(self, df):
        """Prepare hourly yield data for training"""
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df = df.sort_values('timestamp')
        
        # Calculate hourly yield differences
        df['prev_yield'] = df['yield_today'].shift(1)
        df['hourly_yield'] = df['yield_today'] - df['prev_yield']
        
        # Remove negative values and outliers
        df = df[df['hourly_yield'] >= 0]
        df = df[df['hourly_yield'] <= 15]  # Max 15kWh per hour
        
        # Fill missing weather data with defaults
        df['temperature'] = df['temperature'].fillna(25)
        df['cloud_cover'] = df['cloud_cover'].fillna(50)
        df['ghi'] = df['ghi'].fillna(500)
        df['soc'] = df['soc'].fillna(50)
        
        # Extract features
        features_list = []
        targets = []
        
        for _, row in df.iterrows():
            if pd.isna(row['hourly_yield']):
                continue
                
            features = self.extract_features(
                row['timestamp'],
                row['temperature'],
                row['cloud_cover'], 
                row['ghi'],
                row['soc']
            )
            
            features_list.append(list(features.values()))
            targets.append(row['hourly_yield'])
        
        feature_names = list(self.extract_features(datetime.now()).keys())
        
        return np.array(features_list), np.array(targets), feature_names
    
    def create_model_for_system(self, system_id, system_table):
        """Create optimized model for a specific system"""
        print(f"\n🔧 Creating optimized model for {system_id}...")
        
        # Get training data
        df = self.get_training_data(system_table)
        if df is None:
            return False
            
        # Prepare features and targets
        X, y, feature_names = self.prepare_hourly_data(df)
        
        if len(X) < 100:
            print(f"❌ Insufficient data for {system_id}: {len(X)} samples")
            return False
            
        print(f"✅ Prepared {len(X)} training samples for {system_id}")
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, shuffle=False
        )
        
        # Create and fit scaler
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        # Create and train model
        model = GradientBoostingRegressor(
            n_estimators=100,
            learning_rate=0.1,
            max_depth=6,
            random_state=42,
            subsample=0.8
        )
        
        model.fit(X_train_scaled, y_train)
        
        # Evaluate model
        y_pred = model.predict(X_test_scaled)
        mae = mean_absolute_error(y_test, y_pred)
        r2 = r2_score(y_test, y_pred)
        
        print(f"📊 {system_id} Model Performance:")
        print(f"   MAE: {mae:.3f} kWh")
        print(f"   R²: {r2:.3f}")
        
        # Save model and scaler
        model_path = self.models_dir / f"{system_id}_model.joblib"
        scaler_path = self.models_dir / f"{system_id}_scaler.joblib"
        
        joblib.dump(model, model_path)
        joblib.dump(scaler, scaler_path)
        
        print(f"✅ Saved {system_id} model to {model_path}")
        print(f"✅ Saved {system_id} scaler to {scaler_path}")
        
        return True
    
    def create_all_models(self):
        """Create optimized models for all systems"""
        print("🚀 Creating Optimized Production Models...")
        
        systems = [
            ('system1', 'solax_data'),
            ('system2', 'solax_data2')
        ]
        
        success_count = 0
        
        for system_id, table_name in systems:
            if self.create_model_for_system(system_id, table_name):
                success_count += 1
        
        print(f"\n🎉 Created {success_count}/{len(systems)} optimized models successfully!")
        
        if success_count == len(systems):
            print("✅ All optimized production models are ready!")
            return True
        else:
            print("⚠️ Some models failed to create")
            return False

def main():
    """Main function"""
    print("🌞 Optimized Production Model Creator")
    print("=====================================")
    
    creator = OptimizedModelCreator()
    
    if creator.create_all_models():
        print("\n🎯 Optimized Production Models created successfully!")
        print("   The enhanced production app should now work without errors.")
    else:
        print("\n❌ Failed to create some models")
        print("   Check the database connection and data availability.")

if __name__ == "__main__":
    main()
