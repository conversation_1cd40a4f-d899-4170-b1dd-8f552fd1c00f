#!/usr/bin/env python3
"""
Gap Analysis - Ανάλυση διαστημάτων που λείπουν από τα δύο συστήματα
"""

import psycopg2
from datetime import datetime, timedelta
import os

def analyze_data_gaps():
    """Ανάλυση gaps στα δεδομένα των δύο συστημάτων"""
    print("🔍 GAP ANALYSIS - ΚΑΙ ΤΑ ΔΥΟ ΣΥΣΤΗΜΑΤΑ")
    print("=" * 60)
    print("Στόχος: Εύρεση διαστημάτων που λείπουν από March 2024 έως σήμερα")
    
    try:
        conn = psycopg2.connect(
            host="localhost",
            database="solar_prediction",
            user="postgres",
            password="postgres"
        )
        cursor = conn.cursor()
        
        # Ορισμός περιόδων για έλεγχο
        periods = [
            ('2024-03-01', '2024-04-01', 'March 2024'),
            ('2024-04-01', '2024-05-01', 'April 2024'),
            ('2024-05-01', '2024-06-01', 'May 2024'),
            ('2024-06-01', '2024-07-01', 'June 2024'),
            ('2024-07-01', '2024-08-01', 'July 2024'),
            ('2024-08-01', '2024-09-01', 'August 2024'),
            ('2024-09-01', '2024-10-01', 'September 2024'),
            ('2024-10-01', '2024-11-01', 'October 2024'),
            ('2024-11-01', '2024-12-01', 'November 2024'),
            ('2024-12-01', '2025-01-01', 'December 2024'),
            ('2025-01-01', '2025-02-01', 'January 2025'),
            ('2025-02-01', '2025-03-01', 'February 2025'),
            ('2025-03-01', '2025-04-01', 'March 2025'),
            ('2025-04-01', '2025-05-01', 'April 2025'),
            ('2025-05-01', '2025-06-01', 'May 2025'),
            ('2025-06-01', '2025-07-01', 'June 2025 (partial)')
        ]
        
        systems = [
            ('solax_data', 'System 1 (Σπίτι Πάνω)'),
            ('solax_data2', 'System 2 (Σπίτι Κάτω)')
        ]
        
        gaps_summary = {
            'System 1': [],
            'System 2': []
        }
        
        for table_name, description in systems:
            print(f"\n📊 {description}:")
            
            # Συνολικά στατιστικά
            cursor.execute(f"SELECT COUNT(*), MIN(timestamp), MAX(timestamp) FROM {table_name}")
            total_count, min_date, max_date = cursor.fetchone()
            
            print(f"   📈 Συνολικά: {total_count:,} records")
            print(f"   📅 Εύρος: {min_date} έως {max_date}")
            print(f"   📋 Ανάλυση ανά μήνα:")
            
            system_key = 'System 1' if 'solax_data' == table_name else 'System 2'
            
            for start_date, end_date, period_name in periods:
                cursor.execute(f"""
                    SELECT COUNT(*) FROM {table_name} 
                    WHERE timestamp >= '{start_date}' AND timestamp < '{end_date}'
                """)
                count = cursor.fetchone()[0]
                
                if count > 0:
                    print(f"      ✅ {period_name}: {count:,} records")
                else:
                    print(f"      ❌ {period_name}: 0 records - GAP!")
                    gaps_summary[system_key].append(period_name)
        
        # Ανάλυση διαθέσιμων Excel files
        print(f"\n📁 ΔΙΑΘΕΣΙΜΑ EXCEL FILES:")
        
        excel_files = [
            "/home/<USER>/Downloads/new bot/solax/Plant Reports 2024-03-01-2024-06-28.xlsx",
            "/home/<USER>/Downloads/new bot/solax/Plant Reports 2024-06-28-2024-06-30.xlsx", 
            "/home/<USER>/Downloads/new bot/solax/Plant Reports 2024-07-01-2024-10-28.xlsx",
            "/home/<USER>/Downloads/new bot/solax/Plant Reports 2024-10-28-2025-02-24.xlsx",
            "/home/<USER>/Downloads/new bot/solax/Plant Reports 2025-02-24-2025-04-11.xlsx",
            "/home/<USER>/Downloads/new bot/backup_old_files/data_exports/Plant Reports 2024-06-28-2024-10-25.xlsx",
            "/home/<USER>/Downloads/new bot/backup_old_files/data_exports/Plant Reports 2025-02-22-2025-04-14.xlsx"
        ]
        
        available_files = []
        for file_path in excel_files:
            if os.path.exists(file_path):
                size_mb = os.path.getsize(file_path) / 1024 / 1024
                print(f"   ✅ {os.path.basename(file_path)} ({size_mb:.1f} MB)")
                available_files.append(file_path)
            else:
                print(f"   ❌ {os.path.basename(file_path)} - Not found")
        
        # Σύνοψη gaps
        print(f"\n📋 ΣΥΝΟΨΗ GAPS:")
        
        total_gaps = 0
        for system, gaps in gaps_summary.items():
            print(f"\n🏠 {system}:")
            if gaps:
                print(f"   ❌ Λείπουν {len(gaps)} περίοδοι:")
                for gap in gaps:
                    print(f"      🔴 {gap}")
                total_gaps += len(gaps)
            else:
                print(f"   ✅ Δεν λείπουν δεδομένα!")
        
        # Προτάσεις
        print(f"\n🎯 ΠΡΟΤΑΣΕΙΣ:")
        
        if total_gaps > 0:
            print(f"   📥 Χρειάζεται import από {len(available_files)} Excel files")
            print(f"   🔧 Τα files καλύπτουν: March 2024 - April 2025")
            print(f"   ⚠️  Συνολικά gaps: {total_gaps} περίοδοι")
            
            # Ανάλυση κάλυψης
            print(f"\n📊 ΑΝΑΛΥΣΗ ΚΑΛΥΨΗΣ ΑΠΟ EXCEL FILES:")
            coverage_periods = [
                ("2024-03-01", "2024-06-28", "Plant Reports 2024-03-01-2024-06-28.xlsx"),
                ("2024-06-28", "2024-06-30", "Plant Reports 2024-06-28-2024-06-30.xlsx"),
                ("2024-07-01", "2024-10-28", "Plant Reports 2024-07-01-2024-10-28.xlsx"),
                ("2024-10-28", "2025-02-24", "Plant Reports 2024-10-28-2025-02-24.xlsx"),
                ("2025-02-24", "2025-04-11", "Plant Reports 2025-02-24-2025-04-11.xlsx")
            ]
            
            for start, end, filename in coverage_periods:
                print(f"   📄 {filename}")
                print(f"      📅 Καλύπτει: {start} έως {end}")
        else:
            print(f"   ✅ Δεν χρειάζεται import - όλα τα δεδομένα υπάρχουν!")
        
        cursor.close()
        conn.close()
        
        return gaps_summary, available_files
        
    except Exception as e:
        print(f"❌ Σφάλμα: {e}")
        import traceback
        traceback.print_exc()
        return {}, []

def main():
    """Κύρια συνάρτηση"""
    gaps, files = analyze_data_gaps()
    
    # Τελική σύνοψη
    print(f"\n🎯 ΤΕΛΙΚΗ ΣΥΝΟΨΗ:")
    
    system1_gaps = len(gaps.get('System 1', []))
    system2_gaps = len(gaps.get('System 2', []))
    
    print(f"   🏠 System 1 (Σπίτι Πάνω): {system1_gaps} gaps")
    print(f"   🏠 System 2 (Σπίτι Κάτω): {system2_gaps} gaps")
    print(f"   📁 Διαθέσιμα Excel files: {len(files)}")
    
    if system1_gaps > 0 or system2_gaps > 0:
        print(f"\n🔧 ΕΠΟΜΕΝΟ ΒΗΜΑ: Import historical data από Excel files")
    else:
        print(f"\n✅ ΕΤΟΙΜΟΙ για Enhanced Model v3!")

if __name__ == "__main__":
    main()
