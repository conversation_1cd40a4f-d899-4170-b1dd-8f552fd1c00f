#!/usr/bin/env python3
"""
YIELD-BASED MULTI-HORIZON MODEL TRAINING
Direct database access for real data training
Created: June 4, 2025
"""

import os
import sys
import pandas as pd
import numpy as np
import psycopg2
import joblib
import json
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Tuple, Any
import warnings
warnings.filterwarnings('ignore')

# ML Libraries
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import Ridge, ElasticNet, LinearRegression
from sklearn.svm import SVR
from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler
from sklearn.model_selection import TimeSeriesSplit, cross_val_score
from sklearn.metrics import r2_score, mean_absolute_error, mean_squared_error
import xgboost as xgb
import lightgbm as lgb

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

class YieldBasedModelTrainer:
    """Train yield-based models for all horizons and systems"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent.parent
        self.models_dir = self.project_root / "models"
        self.models_dir.mkdir(exist_ok=True)
        
        # Database connection
        self.db_configs = [
            "postgresql://grlv:Gr1234@localhost:5433/solar_prediction",
            "postgresql://postgres:postgres@localhost:5433/solar_prediction"
        ]
        
        # Model configurations per horizon
        self.horizon_configs = {
            'hourly': {
                'algorithms': [RandomForestRegressor, GradientBoostingRegressor, xgb.XGBRegressor],
                'scalers': [StandardScaler, RobustScaler],
                'target_r2': 0.95
            },
            'daily': {
                'algorithms': [xgb.XGBRegressor, lgb.LGBMRegressor, RandomForestRegressor],
                'scalers': [StandardScaler, MinMaxScaler],
                'target_r2': 0.90
            },
            'monthly': {
                'algorithms': [Ridge, ElasticNet, SVR],
                'scalers': [StandardScaler, RobustScaler],
                'target_r2': 0.95
            },
            'yearly': {
                'algorithms': [LinearRegression, Ridge, SVR],
                'scalers': [StandardScaler, MinMaxScaler],
                'target_r2': 0.85
            }
        }
        
        print("🚀 YIELD-BASED MODEL TRAINER INITIALIZED")
        print(f"📁 Models directory: {self.models_dir}")
    
    def connect_database(self):
        """Connect to PostgreSQL database"""
        for config in self.db_configs:
            try:
                conn = psycopg2.connect(config)
                print(f"✅ Connected to database: {config.split('@')[1]}")
                return conn
            except Exception as e:
                print(f"❌ Failed to connect with {config.split('@')[1]}: {e}")
                continue
        
        raise Exception("❌ Could not connect to any database")
    
    def get_training_data(self, system_id: int) -> pd.DataFrame:
        """Get training data for specific system"""
        print(f"📊 Loading training data for System {system_id}...")
        
        conn = self.connect_database()
        
        # Determine table name
        table_name = "solax_data" if system_id == 1 else "solax_data2"
        
        query = f"""
        SELECT 
            s.timestamp,
            s.yield_today,
            s.soc,
            s.bat_power,
            s.powerdc1,
            s.powerdc2,
            s.feedin_power,
            s.consume_energy,
            s.temperature,
            w.temperature_2m,
            w.cloud_cover,
            w.direct_radiation as ghi,
            w.diffuse_radiation as dni,
            w.shortwave_radiation
        FROM {table_name} s
        LEFT JOIN weather_data w ON DATE_TRUNC('hour', s.timestamp) = DATE_TRUNC('hour', w.timestamp)
        WHERE s.timestamp >= '2024-03-01'
        AND s.yield_today IS NOT NULL
        ORDER BY s.timestamp
        """
        
        df = pd.read_sql(query, conn)
        conn.close()
        
        print(f"   📈 Loaded {len(df):,} records")
        print(f"   📅 Date range: {df['timestamp'].min()} to {df['timestamp'].max()}")
        
        return df
    
    def calculate_yield_targets(self, df: pd.DataFrame, horizon: str) -> pd.DataFrame:
        """Calculate yield targets for different horizons"""
        df = df.copy()
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df = df.sort_values('timestamp')
        
        if horizon == 'hourly':
            # Hourly yield difference
            df['target'] = df['yield_today'].diff()
            # Remove negative values (reset occurred)
            df.loc[df['target'] < 0, 'target'] = 0
            
        elif horizon == 'daily':
            # Daily total yield (max yield_today per day)
            df['date'] = df['timestamp'].dt.date
            daily_yield = df.groupby('date')['yield_today'].max().reset_index()
            daily_yield['target'] = daily_yield['yield_today']
            # Merge back
            df = df.merge(daily_yield[['date', 'target']], on='date', how='left')
            
        elif horizon == 'monthly':
            # Monthly average daily yield
            df['year_month'] = df['timestamp'].dt.to_period('M')
            df['date'] = df['timestamp'].dt.date
            # Get daily yields first
            daily_yields = df.groupby('date')['yield_today'].max().reset_index()
            daily_yields['year_month'] = pd.to_datetime(daily_yields['date']).dt.to_period('M')
            # Calculate monthly averages
            monthly_avg = daily_yields.groupby('year_month')['yield_today'].mean().reset_index()
            monthly_avg.columns = ['year_month', 'target']
            # Merge back
            df = df.merge(monthly_avg, on='year_month', how='left')
            
        elif horizon == 'yearly':
            # Yearly average daily yield
            df['year'] = df['timestamp'].dt.year
            df['date'] = df['timestamp'].dt.date
            # Get daily yields first
            daily_yields = df.groupby('date')['yield_today'].max().reset_index()
            daily_yields['year'] = pd.to_datetime(daily_yields['date']).dt.year
            # Calculate yearly averages
            yearly_avg = daily_yields.groupby('year')['yield_today'].mean().reset_index()
            yearly_avg.columns = ['year', 'target']
            # Merge back
            df = df.merge(yearly_avg, on='year', how='left')
        
        # Remove rows with missing targets
        df = df.dropna(subset=['target'])
        
        print(f"   🎯 {horizon.capitalize()} targets calculated: {len(df):,} samples")
        print(f"   📊 Target range: {df['target'].min():.2f} - {df['target'].max():.2f} kWh")
        
        return df
    
    def engineer_features(self, df: pd.DataFrame, horizon: str) -> pd.DataFrame:
        """Engineer features for specific horizon"""
        df = df.copy()
        
        # Common temporal features
        df['hour'] = df['timestamp'].dt.hour
        df['day_of_week'] = df['timestamp'].dt.dayofweek
        df['month'] = df['timestamp'].dt.month
        df['day_of_year'] = df['timestamp'].dt.dayofyear
        df['season'] = df['month'].map({12: 0, 1: 0, 2: 0, 3: 1, 4: 1, 5: 1, 
                                       6: 2, 7: 2, 8: 2, 9: 3, 10: 3, 11: 3})
        
        # Cyclical encoding
        df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
        df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
        
        # Horizon-specific features
        if horizon == 'hourly':
            # Lag features for hourly
            df['yield_lag_1h'] = df['yield_today'].shift(1)
            df['yield_lag_24h'] = df['yield_today'].shift(24)
            df['is_peak_hour'] = ((df['hour'] >= 10) & (df['hour'] <= 16)).astype(int)
            
        elif horizon == 'daily':
            # Daily aggregations
            df['yield_lag_1d'] = df.groupby(df['timestamp'].dt.date)['yield_today'].shift(1)
            df['yield_rolling_7d'] = df.groupby(df['timestamp'].dt.date)['yield_today'].rolling(7).mean().values
            
        elif horizon == 'monthly':
            # Monthly features
            df['yield_lag_1m'] = df['yield_today'].shift(30*24)  # Approximate
            df['seasonal_intensity'] = df['month'].map({6: 1.0, 7: 1.0, 5: 0.9, 8: 0.9, 
                                                       4: 0.7, 9: 0.7, 3: 0.5, 10: 0.5,
                                                       2: 0.3, 11: 0.3, 1: 0.2, 12: 0.2})
            
        elif horizon == 'yearly':
            # Yearly trend
            df['year'] = df['timestamp'].dt.year
            df['year_normalized'] = (df['year'] - df['year'].min()) / (df['year'].max() - df['year'].min())
            
        # Fill missing values
        df = df.fillna(method='forward').fillna(method='backward').fillna(0)

        return df

    def select_features(self, df: pd.DataFrame, horizon: str) -> List[str]:
        """Select relevant features for each horizon"""
        base_features = ['hour_sin', 'hour_cos', 'month_sin', 'month_cos', 'season']

        if horizon == 'hourly':
            features = base_features + [
                'hour', 'soc', 'bat_power', 'temperature_2m', 'cloud_cover',
                'ghi', 'dni', 'yield_lag_1h', 'yield_lag_24h', 'is_peak_hour'
            ]
        elif horizon == 'daily':
            features = base_features + [
                'day_of_year', 'soc', 'temperature_2m', 'cloud_cover',
                'ghi', 'dni', 'yield_lag_1d', 'yield_rolling_7d'
            ]
        elif horizon == 'monthly':
            features = base_features + [
                'month', 'temperature_2m', 'cloud_cover', 'ghi',
                'yield_lag_1m', 'seasonal_intensity'
            ]
        elif horizon == 'yearly':
            features = base_features + [
                'year_normalized', 'temperature_2m', 'ghi'
            ]

        # Filter features that exist in dataframe
        available_features = [f for f in features if f in df.columns]
        print(f"   🔧 Selected {len(available_features)} features for {horizon}")

        return available_features

    def train_single_model(self, X: pd.DataFrame, y: pd.Series, horizon: str) -> Dict[str, Any]:
        """Train and evaluate models for single horizon"""
        print(f"🤖 Training {horizon} models...")

        config = self.horizon_configs[horizon]
        best_score = -np.inf
        best_model = None
        best_scaler = None
        best_config = None

        # Time series cross-validation
        tscv = TimeSeriesSplit(n_splits=5)

        for algorithm in config['algorithms']:
            for scaler_class in config['scalers']:
                try:
                    # Initialize scaler and model
                    scaler = scaler_class()

                    # Model-specific parameters
                    if algorithm == RandomForestRegressor:
                        model = algorithm(n_estimators=100, random_state=42, n_jobs=-1)
                    elif algorithm == GradientBoostingRegressor:
                        model = algorithm(n_estimators=100, random_state=42)
                    elif algorithm == xgb.XGBRegressor:
                        model = algorithm(n_estimators=100, random_state=42, n_jobs=-1)
                    elif algorithm == lgb.LGBMRegressor:
                        model = algorithm(n_estimators=100, random_state=42, n_jobs=-1, verbose=-1)
                    elif algorithm == Ridge:
                        model = algorithm(alpha=1.0, random_state=42)
                    elif algorithm == ElasticNet:
                        model = algorithm(alpha=1.0, random_state=42)
                    elif algorithm == SVR:
                        model = algorithm(kernel='rbf', C=1.0)
                    else:
                        model = algorithm()

                    # Cross-validation
                    X_scaled = scaler.fit_transform(X)
                    scores = cross_val_score(model, X_scaled, y, cv=tscv, scoring='r2', n_jobs=-1)
                    mean_score = scores.mean()

                    print(f"   📊 {algorithm.__name__} + {scaler_class.__name__}: R² = {mean_score:.4f}")

                    if mean_score > best_score:
                        best_score = mean_score
                        best_model = model
                        best_scaler = scaler
                        best_config = f"{algorithm.__name__}_{scaler_class.__name__}"

                except Exception as e:
                    print(f"   ❌ Failed {algorithm.__name__} + {scaler_class.__name__}: {e}")
                    continue

        if best_model is None:
            raise Exception(f"❌ No model succeeded for {horizon}")

        # Train best model on full dataset
        X_scaled = best_scaler.fit_transform(X)
        best_model.fit(X_scaled, y)

        # Calculate final metrics
        y_pred = best_model.predict(X_scaled)
        r2 = r2_score(y, y_pred)
        mae = mean_absolute_error(y, y_pred)
        rmse = np.sqrt(mean_squared_error(y, y_pred))

        print(f"   🏆 Best model: {best_config}")
        print(f"   📈 Final metrics: R² = {r2:.4f}, MAE = {mae:.4f}, RMSE = {rmse:.4f}")

        return {
            'model': best_model,
            'scaler': best_scaler,
            'config': best_config,
            'metrics': {
                'r2': r2,
                'mae': mae,
                'rmse': rmse,
                'cv_score': best_score
            },
            'features': list(X.columns),
            'target_achieved': r2 >= config['target_r2']
        }

    def save_model(self, model_data: Dict[str, Any], system_id: int, horizon: str):
        """Save trained model and metadata"""
        model_dir = self.models_dir / f"multi_horizon_{horizon}_system{system_id}"
        model_dir.mkdir(exist_ok=True)

        # Save model and scaler
        joblib.dump(model_data['model'], model_dir / "model.joblib")
        joblib.dump(model_data['scaler'], model_dir / "scaler.joblib")

        # Save metadata
        metadata = {
            'system_id': system_id,
            'aggregation': horizon,
            'best_model': model_data['config'],
            'performance': model_data['metrics'],
            'features': model_data['features'],
            'training_date': datetime.now().isoformat(),
            'model_type': f'multi_horizon_{horizon}_prediction',
            'data_source': 'real_database_data',
            'target_achieved': model_data['target_achieved']
        }

        with open(model_dir / "metadata.json", 'w') as f:
            json.dump(metadata, f, indent=2)

        print(f"   💾 Saved model to {model_dir}")

        return model_dir

    def train_system_models(self, system_id: int) -> Dict[str, Any]:
        """Train all horizon models for a specific system"""
        print(f"\n🏠 TRAINING SYSTEM {system_id} MODELS")
        print("=" * 50)

        # Load training data
        df = self.get_training_data(system_id)

        if len(df) < 1000:
            print(f"❌ Insufficient data for System {system_id}: {len(df)} records")
            return {}

        system_results = {}
        horizons = ['hourly', 'daily', 'monthly', 'yearly']

        for horizon in horizons:
            try:
                print(f"\n📊 Processing {horizon} horizon...")

                # Calculate targets and engineer features
                df_processed = self.calculate_yield_targets(df, horizon)
                df_processed = self.engineer_features(df_processed, horizon)

                # Select features
                feature_columns = self.select_features(df_processed, horizon)

                if len(feature_columns) == 0:
                    print(f"   ❌ No valid features for {horizon}")
                    continue

                # Prepare training data
                X = df_processed[feature_columns].copy()
                y = df_processed['target'].copy()

                # Remove any remaining NaN values
                mask = ~(X.isna().any(axis=1) | y.isna())
                X = X[mask]
                y = y[mask]

                if len(X) < 100:
                    print(f"   ❌ Insufficient clean data for {horizon}: {len(X)} samples")
                    continue

                print(f"   📈 Training with {len(X):,} samples and {len(feature_columns)} features")

                # Train model
                model_data = self.train_single_model(X, y, horizon)

                # Save model
                model_dir = self.save_model(model_data, system_id, horizon)

                system_results[horizon] = {
                    'model_dir': str(model_dir),
                    'metrics': model_data['metrics'],
                    'target_achieved': model_data['target_achieved'],
                    'features': model_data['features']
                }

                print(f"   ✅ {horizon.capitalize()} model completed successfully")

            except Exception as e:
                print(f"   ❌ Failed to train {horizon} model: {e}")
                system_results[horizon] = {'error': str(e)}
                continue

        return system_results

    def train_all_models(self) -> Dict[str, Any]:
        """Train all models for all systems and horizons"""
        print("🚀 STARTING COMPREHENSIVE MODEL TRAINING")
        print("=" * 60)
        print("Target: 8 yield-based models (4 horizons × 2 systems)")
        print("Data source: Real database (solax_data, solax_data2, weather_data)")
        print("=" * 60)

        all_results = {}

        # Train models for both systems
        for system_id in [1, 2]:
            system_results = self.train_system_models(system_id)
            all_results[f'system_{system_id}'] = system_results

        # Generate summary report
        self.generate_training_report(all_results)

        return all_results

    def generate_training_report(self, results: Dict[str, Any]):
        """Generate comprehensive training report"""
        print("\n" + "=" * 60)
        print("🏆 TRAINING COMPLETION REPORT")
        print("=" * 60)

        total_models = 0
        successful_models = 0
        target_achieved = 0

        for system_key, system_results in results.items():
            system_id = system_key.split('_')[1]
            print(f"\n🏠 SYSTEM {system_id} RESULTS:")

            for horizon, result in system_results.items():
                total_models += 1

                if 'error' in result:
                    print(f"   ❌ {horizon.capitalize()}: FAILED - {result['error']}")
                else:
                    successful_models += 1
                    metrics = result['metrics']
                    achieved = result['target_achieved']

                    if achieved:
                        target_achieved += 1
                        status = "✅ TARGET ACHIEVED"
                    else:
                        status = "⚠️  Below target"

                    print(f"   📊 {horizon.capitalize()}: R² = {metrics['r2']:.4f}, "
                          f"MAE = {metrics['mae']:.4f} - {status}")

        print(f"\n📈 OVERALL SUMMARY:")
        print(f"   Total models: {total_models}")
        print(f"   Successful: {successful_models}/{total_models} ({successful_models/total_models*100:.1f}%)")
        print(f"   Target achieved: {target_achieved}/{successful_models} ({target_achieved/successful_models*100:.1f}%)")

        # Update registry
        self.update_model_registry(results)

        if successful_models == total_models:
            print(f"\n🎉 ALL MODELS TRAINED SUCCESSFULLY!")
        elif successful_models >= 6:
            print(f"\n✅ TRAINING MOSTLY SUCCESSFUL - {successful_models}/8 models ready")
        else:
            print(f"\n⚠️  TRAINING PARTIALLY SUCCESSFUL - Only {successful_models}/8 models ready")

    def update_model_registry(self, results: Dict[str, Any]):
        """Update the production models registry"""
        registry_path = self.models_dir / "yield_models_registry.json"

        registry = {
            "created_date": datetime.now().isoformat(),
            "model_type": "yield_based_multi_horizon",
            "total_models": 8,
            "successful_models": 0,
            "target_achieved": 0,
            "systems": [1, 2],
            "horizons": ["hourly", "daily", "monthly", "yearly"],
            "data_source": "real_database_data",
            "models": {}
        }

        for system_key, system_results in results.items():
            system_id = int(system_key.split('_')[1])

            for horizon, result in system_results.items():
                model_key = f"system_{system_id}_{horizon}"

                if 'error' not in result:
                    registry["successful_models"] += 1
                    if result['target_achieved']:
                        registry["target_achieved"] += 1

                    registry["models"][model_key] = {
                        "system_id": system_id,
                        "horizon": horizon,
                        "model_dir": result['model_dir'],
                        "metrics": result['metrics'],
                        "target_achieved": result['target_achieved'],
                        "features_count": len(result['features']),
                        "status": "ready"
                    }
                else:
                    registry["models"][model_key] = {
                        "system_id": system_id,
                        "horizon": horizon,
                        "status": "failed",
                        "error": result['error']
                    }

        with open(registry_path, 'w') as f:
            json.dump(registry, f, indent=2)

        print(f"📋 Registry updated: {registry_path}")


def main():
    """Main training function"""
    try:
        trainer = YieldBasedModelTrainer()
        results = trainer.train_all_models()

        print("\n🎯 TRAINING COMPLETED!")
        print("Next steps:")
        print("1. Test models with real predictions")
        print("2. Implement ensemble methods")
        print("3. Set up monitoring system")
        print("4. Deploy to production")

        return results

    except Exception as e:
        print(f"❌ Training failed: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    main()
