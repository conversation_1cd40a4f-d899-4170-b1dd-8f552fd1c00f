#!/usr/bin/env python3
"""
Advanced ML Pipeline Configuration
==================================

Configuration settings for GPU-accelerated ML pipeline
Hardware: RTX 4070 Ti (12GB VRAM) + 32GB RAM
Created: June 6, 2025
"""

import os
from pathlib import Path

# Hardware Configuration
HARDWARE_CONFIG = {
    'gpu_memory_gb': 12,  # RTX 4070 Ti VRAM
    'system_memory_gb': 32,  # System RAM
    'cuda_cores': 7680,
    'tensor_cores': 240,
    'gpu_compute_capability': '8.9',
    'max_gpu_utilization': 0.9,  # Use 90% of GPU memory
}

# GPU Memory Optimization
GPU_MEMORY_CONFIG = {
    'training_batch_size': 8192,  # Optimized for 12GB VRAM
    'inference_batch_size': 16384,
    'model_parallel': True,
    'gradient_accumulation_steps': 4,
    'mixed_precision': True,  # FP16 for memory efficiency
    'pin_memory': True,
    'num_workers': 8,  # Data loading workers
}

# Model Configuration
MODEL_CONFIG = {
    'xgboost_gpu': {
        'tree_method': 'gpu_hist',
        'gpu_id': 0,
        'n_estimators': 1000,
        'max_depth': 8,
        'learning_rate': 0.1,
        'subsample': 0.8,
        'colsample_bytree': 0.8,
        'reg_alpha': 0.1,
        'reg_lambda': 1.0,
        'random_state': 42,
        'n_jobs': -1,
        'early_stopping_rounds': 50,
        'eval_metric': 'rmse'
    },
    
    'lightgbm_gpu': {
        'device': 'gpu',
        'gpu_platform_id': 0,
        'gpu_device_id': 0,
        'n_estimators': 1000,
        'max_depth': 8,
        'learning_rate': 0.1,
        'subsample': 0.8,
        'colsample_bytree': 0.8,
        'reg_alpha': 0.1,
        'reg_lambda': 1.0,
        'random_state': 42,
        'n_jobs': -1,
        'verbose': -1,
        'early_stopping_rounds': 50,
        'metric': 'rmse'
    },
    
    'random_forest_gpu': {
        'n_estimators': 200,
        'max_depth': 15,
        'min_samples_split': 5,
        'min_samples_leaf': 2,
        'max_features': 'sqrt',
        'random_state': 42,
        'bootstrap': True
    },
    
    'random_forest_cpu': {
        'n_estimators': 200,
        'max_depth': 15,
        'min_samples_split': 5,
        'min_samples_leaf': 2,
        'max_features': 'sqrt',
        'random_state': 42,
        'n_jobs': -1,
        'bootstrap': True
    }
}

# Deep Learning Configuration
DEEP_LEARNING_CONFIG = {
    'lstm': {
        'hidden_size': 128,
        'num_layers': 2,
        'dropout': 0.2,
        'bidirectional': True,
        'sequence_length': 24,  # 24 hours lookback
        'batch_size': 256,
        'learning_rate': 0.001,
        'epochs': 100,
        'early_stopping_patience': 10
    },
    
    'cnn_1d': {
        'filters': [64, 128, 256],
        'kernel_sizes': [3, 5, 7],
        'dropout': 0.3,
        'sequence_length': 24,
        'batch_size': 256,
        'learning_rate': 0.001,
        'epochs': 100,
        'early_stopping_patience': 10
    },
    
    'transformer': {
        'embed_dim': 128,
        'num_heads': 8,
        'num_layers': 4,
        'dropout': 0.1,
        'sequence_length': 24,
        'batch_size': 128,
        'learning_rate': 0.0001,
        'epochs': 100,
        'early_stopping_patience': 15
    }
}

# Hyperparameter Optimization Configuration
OPTUNA_CONFIG = {
    'n_trials': 500,
    'timeout': 3600,  # 1 hour
    'n_jobs': 4,  # Parallel trials
    'sampler': 'TPESampler',
    'pruner': 'MedianPruner',
    'direction': 'minimize',
    'metric': 'rmse',
    
    'xgboost_search_space': {
        'n_estimators': (100, 2000),
        'max_depth': (3, 15),
        'learning_rate': (0.01, 0.3),
        'subsample': (0.6, 1.0),
        'colsample_bytree': (0.6, 1.0),
        'reg_alpha': (0.0, 10.0),
        'reg_lambda': (0.0, 10.0)
    },
    
    'lightgbm_search_space': {
        'n_estimators': (100, 2000),
        'max_depth': (3, 15),
        'learning_rate': (0.01, 0.3),
        'subsample': (0.6, 1.0),
        'colsample_bytree': (0.6, 1.0),
        'reg_alpha': (0.0, 10.0),
        'reg_lambda': (0.0, 10.0),
        'num_leaves': (10, 300)
    },
    
    'random_forest_search_space': {
        'n_estimators': (50, 500),
        'max_depth': (5, 30),
        'min_samples_split': (2, 20),
        'min_samples_leaf': (1, 10),
        'max_features': ['sqrt', 'log2', 0.5, 0.8]
    }
}

# Feature Engineering Configuration
FEATURE_CONFIG = {
    'temporal_features': {
        'cyclical_encoding': True,
        'lag_features': [1, 6, 12, 24, 48],
        'rolling_windows': [6, 12, 24, 48, 72],
        'seasonal_decomposition': True
    },
    
    'astronomical_features': {
        'solar_elevation': True,
        'solar_azimuth': True,
        'day_length': True,
        'solar_noon_difference': True,
        'sunrise_sunset_factor': True
    },
    
    'weather_features': {
        'temperature_efficiency': True,
        'cloud_factor': True,
        'ghi_normalization': True,
        'weather_interactions': True,
        'forecast_features': True
    },
    
    'battery_features': {
        'soc_normalization': True,
        'charge_discharge_rates': True,
        'battery_efficiency': True,
        'cycle_estimation': True
    },
    
    'interaction_features': {
        'ghi_temperature': True,
        'soc_hour': True,
        'cloud_elevation': True,
        'system_weather': True
    }
}

# Data Configuration
DATA_CONFIG = {
    'database_url': "postgresql://postgres:postgres@localhost/solar_prediction",
    'min_date': '2024-03-01',
    'target_column': 'yield_today',  # Using yield as per requirements
    'system_column': 'system_id',
    'timestamp_column': 'timestamp',
    
    'data_quality': {
        'min_records_per_system': 1000,
        'max_missing_percentage': 0.1,
        'outlier_detection': True,
        'outlier_method': 'iqr',
        'outlier_threshold': 3.0
    },
    
    'train_test_split': {
        'test_size': 0.2,
        'validation_size': 0.1,
        'time_series_split': True,
        'shuffle': False  # Preserve temporal order
    }
}

# Ensemble Configuration
ENSEMBLE_CONFIG = {
    'stacking': {
        'meta_learner': 'linear_regression',
        'cv_folds': 5,
        'use_probas': False,
        'stack_method': 'auto'
    },
    
    'blending': {
        'weights_optimization': True,
        'weight_bounds': (0.0, 1.0),
        'normalize_weights': True
    },
    
    'voting': {
        'voting_type': 'soft',
        'weights': None  # Auto-calculate based on performance
    }
}

# Monitoring Configuration
MONITORING_CONFIG = {
    'drift_detection': {
        'method': 'ks_test',
        'threshold': 0.05,
        'window_size': 1000,
        'reference_window': 5000
    },
    
    'performance_tracking': {
        'metrics': ['rmse', 'mae', 'r2', 'mape'],
        'alert_thresholds': {
            'rmse_increase': 0.1,  # 10% increase
            'r2_decrease': 0.05,   # 5% decrease
            'mae_increase': 0.1    # 10% increase
        }
    },
    
    'retraining_triggers': {
        'performance_degradation': True,
        'drift_detection': True,
        'time_based': True,
        'time_interval_days': 7,
        'min_new_samples': 1000
    }
}

# Output Configuration
OUTPUT_CONFIG = {
    'model_directory': 'models/advanced_ml',
    'results_directory': 'test/results/advanced_ml',
    'logs_directory': 'logs/advanced_ml',
    
    'save_formats': ['joblib', 'onnx'],  # Model formats
    'compression': True,
    'versioning': True,
    'backup_previous': True,
    
    'documentation': {
        'auto_generate': True,
        'include_plots': True,
        'include_feature_importance': True,
        'include_hyperparameters': True
    }
}

# Performance Targets
PERFORMANCE_TARGETS = {
    'accuracy': {
        'r2_score': 0.98,  # >98% accuracy target
        'rmse_max': 2.0,   # <2 kWh RMSE
        'mae_max': 1.5,    # <1.5 kWh MAE
        'mape_max': 0.05   # <5% MAPE
    },
    
    'speed': {
        'training_time_max_minutes': 30,
        'inference_time_max_ms': 5,
        'feature_engineering_time_max_seconds': 10
    },
    
    'resource_usage': {
        'gpu_memory_max_gb': 10,  # Leave 2GB free
        'system_memory_max_gb': 28,  # Leave 4GB free
        'cpu_usage_max_percent': 90
    }
}

# Validation Configuration
VALIDATION_CONFIG = {
    'cross_validation': {
        'method': 'TimeSeriesSplit',
        'n_splits': 5,
        'test_size': None,
        'gap': 0
    },
    
    'holdout_validation': {
        'holdout_size': 0.1,
        'holdout_period': 'latest',  # Use latest data for holdout
        'temporal_validation': True
    },
    
    'bootstrap_validation': {
        'n_bootstrap_samples': 100,
        'confidence_interval': 0.95,
        'stratify': False
    }
}

# Paths
BASE_DIR = Path(__file__).parent.parent.parent
MODELS_DIR = BASE_DIR / "models" / "advanced_ml"
RESULTS_DIR = BASE_DIR / "test" / "results" / "advanced_ml"
LOGS_DIR = BASE_DIR / "logs" / "advanced_ml"
DATA_DIR = BASE_DIR / "data"

# Create directories
for directory in [MODELS_DIR, RESULTS_DIR, LOGS_DIR]:
    directory.mkdir(parents=True, exist_ok=True)

# Environment Variables
ENVIRONMENT = {
    'CUDA_VISIBLE_DEVICES': '0',
    'PYTORCH_CUDA_ALLOC_CONF': 'max_split_size_mb:512',
    'CUPY_CACHE_DIR': str(BASE_DIR / '.cupy_cache'),
    'OPTUNA_STORAGE': f'sqlite:///{BASE_DIR}/optuna_studies.db'
}

# Set environment variables
for key, value in ENVIRONMENT.items():
    os.environ[key] = value

# Export configuration
__all__ = [
    'HARDWARE_CONFIG',
    'GPU_MEMORY_CONFIG', 
    'MODEL_CONFIG',
    'DEEP_LEARNING_CONFIG',
    'OPTUNA_CONFIG',
    'FEATURE_CONFIG',
    'DATA_CONFIG',
    'ENSEMBLE_CONFIG',
    'MONITORING_CONFIG',
    'OUTPUT_CONFIG',
    'PERFORMANCE_TARGETS',
    'VALIDATION_CONFIG',
    'MODELS_DIR',
    'RESULTS_DIR',
    'LOGS_DIR',
    'DATA_DIR'
]
