#!/usr/bin/env python3
"""
Advanced Hyperparameter Optimization System
===========================================

Optuna-based hyperparameter optimization with GPU acceleration:
- Bayesian optimization with TPE sampler
- Parallel trials with GPU acceleration
- Multi-objective optimization
- Pruning for early stopping
- Advanced search spaces
- Real-time monitoring and visualization

Target: Automatic model tuning for maximum performance
Hardware: RTX 4070 Ti optimized
Created: June 6, 2025
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import numpy as np
import pandas as pd
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Any, Optional, Union, Callable
import joblib
import json
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# Optuna for hyperparameter optimization
try:
    import optuna
    from optuna.integration import XGBoostPruningCallback, LightGBMPruningCallback
    from optuna.samplers import TPESampler, CmaEsSampler
    from optuna.pruners import MedianPruner, HyperbandPruner
    OPTUNA_AVAILABLE = True
    print("✅ Optuna available for hyperparameter optimization")
except ImportError:
    OPTUNA_AVAILABLE = False
    print("⚠️ Optuna not available - hyperparameter optimization disabled")

# ML libraries
import xgboost as xgb
import lightgbm as lgb
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import cross_val_score, TimeSeriesSplit
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.preprocessing import StandardScaler

# GPU libraries
try:
    import cupy as cp
    CUPY_AVAILABLE = True
except ImportError:
    CUPY_AVAILABLE = False

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AdvancedHyperparameterOptimizer:
    """
    Advanced hyperparameter optimization system with GPU acceleration
    """
    
    def __init__(self, study_name: str = "solar_prediction_optimization", 
                 storage_url: str = None, n_jobs: int = 4):
        """
        Initialize hyperparameter optimizer
        
        Args:
            study_name: Name of the optimization study
            storage_url: Database URL for study persistence
            n_jobs: Number of parallel jobs
        """
        self.study_name = study_name
        self.storage_url = storage_url or f"sqlite:///optuna_studies_{study_name}.db"
        self.n_jobs = n_jobs
        
        # Optimization results
        self.studies = {}
        self.best_params = {}
        self.optimization_history = {}
        
        # GPU configuration
        self.gpu_available = CUPY_AVAILABLE
        
        logger.info("🎛️ Advanced Hyperparameter Optimizer initialized")
        logger.info(f"   Study name: {study_name}")
        logger.info(f"   Storage: {storage_url}")
        logger.info(f"   Parallel jobs: {n_jobs}")
        logger.info(f"   GPU available: {self.gpu_available}")
    
    def create_xgboost_objective(self, X_train: np.ndarray, y_train: np.ndarray,
                                X_val: np.ndarray, y_val: np.ndarray) -> Callable:
        """Create objective function for XGBoost optimization"""
        
        def objective(trial):
            # Suggest hyperparameters
            params = {
                'n_estimators': trial.suggest_int('n_estimators', 100, 2000),
                'max_depth': trial.suggest_int('max_depth', 3, 15),
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3, log=True),
                'subsample': trial.suggest_float('subsample', 0.6, 1.0),
                'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
                'reg_alpha': trial.suggest_float('reg_alpha', 0.0, 10.0),
                'reg_lambda': trial.suggest_float('reg_lambda', 0.0, 10.0),
                'min_child_weight': trial.suggest_int('min_child_weight', 1, 10),
                'gamma': trial.suggest_float('gamma', 0.0, 5.0),
                'random_state': 42,
                'n_jobs': -1,
                'tree_method': 'gpu_hist' if self.gpu_available else 'hist'
            }
            
            if self.gpu_available:
                params['gpu_id'] = 0
            
            # Create and train model
            model = xgb.XGBRegressor(**params)
            
            # Add pruning callback
            pruning_callback = XGBoostPruningCallback(trial, 'validation_0-rmse')
            
            model.fit(
                X_train, y_train,
                eval_set=[(X_val, y_val)],
                callbacks=[pruning_callback],
                verbose=False
            )
            
            # Calculate validation score
            y_pred = model.predict(X_val)
            rmse = np.sqrt(mean_squared_error(y_val, y_pred))
            
            return rmse
        
        return objective
    
    def create_lightgbm_objective(self, X_train: np.ndarray, y_train: np.ndarray,
                                 X_val: np.ndarray, y_val: np.ndarray) -> Callable:
        """Create objective function for LightGBM optimization"""
        
        def objective(trial):
            # Suggest hyperparameters
            params = {
                'n_estimators': trial.suggest_int('n_estimators', 100, 2000),
                'max_depth': trial.suggest_int('max_depth', 3, 15),
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3, log=True),
                'subsample': trial.suggest_float('subsample', 0.6, 1.0),
                'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
                'reg_alpha': trial.suggest_float('reg_alpha', 0.0, 10.0),
                'reg_lambda': trial.suggest_float('reg_lambda', 0.0, 10.0),
                'min_child_samples': trial.suggest_int('min_child_samples', 5, 100),
                'num_leaves': trial.suggest_int('num_leaves', 10, 300),
                'random_state': 42,
                'n_jobs': -1,
                'verbose': -1
            }
            
            if self.gpu_available:
                params.update({
                    'device': 'gpu',
                    'gpu_platform_id': 0,
                    'gpu_device_id': 0
                })
            
            # Create and train model
            model = lgb.LGBMRegressor(**params)
            
            # Add pruning callback
            pruning_callback = LightGBMPruningCallback(trial, 'valid_0-l2')
            
            model.fit(
                X_train, y_train,
                eval_set=[(X_val, y_val)],
                callbacks=[pruning_callback, lgb.log_evaluation(0)],
            )
            
            # Calculate validation score
            y_pred = model.predict(X_val)
            rmse = np.sqrt(mean_squared_error(y_val, y_pred))
            
            return rmse
        
        return objective
    
    def create_random_forest_objective(self, X_train: np.ndarray, y_train: np.ndarray,
                                      X_val: np.ndarray, y_val: np.ndarray) -> Callable:
        """Create objective function for Random Forest optimization"""
        
        def objective(trial):
            # Suggest hyperparameters
            params = {
                'n_estimators': trial.suggest_int('n_estimators', 50, 500),
                'max_depth': trial.suggest_int('max_depth', 5, 30),
                'min_samples_split': trial.suggest_int('min_samples_split', 2, 20),
                'min_samples_leaf': trial.suggest_int('min_samples_leaf', 1, 10),
                'max_features': trial.suggest_categorical('max_features', ['sqrt', 'log2', 0.5, 0.8]),
                'bootstrap': trial.suggest_categorical('bootstrap', [True, False]),
                'random_state': 42,
                'n_jobs': -1
            }
            
            # Create and train model
            model = RandomForestRegressor(**params)
            model.fit(X_train, y_train)
            
            # Calculate validation score
            y_pred = model.predict(X_val)
            rmse = np.sqrt(mean_squared_error(y_val, y_pred))
            
            return rmse
        
        return objective
    
    def optimize_model(self, model_type: str, X_train: np.ndarray, y_train: np.ndarray,
                      X_val: np.ndarray, y_val: np.ndarray, 
                      n_trials: int = 100, timeout: int = 3600) -> Dict[str, Any]:
        """Optimize hyperparameters for a specific model"""
        
        if not OPTUNA_AVAILABLE:
            logger.error("❌ Optuna not available - cannot perform optimization")
            return {}
        
        logger.info(f"🎯 Optimizing {model_type} hyperparameters...")
        logger.info(f"   Trials: {n_trials}, Timeout: {timeout}s")
        
        # Create study
        study_name_full = f"{self.study_name}_{model_type}"
        
        # Configure sampler and pruner
        sampler = TPESampler(n_startup_trials=20, n_ei_candidates=24)
        pruner = MedianPruner(n_startup_trials=10, n_warmup_steps=5)
        
        study = optuna.create_study(
            study_name=study_name_full,
            storage=self.storage_url,
            load_if_exists=True,
            direction='minimize',
            sampler=sampler,
            pruner=pruner
        )
        
        # Create objective function
        if model_type == 'xgboost':
            objective = self.create_xgboost_objective(X_train, y_train, X_val, y_val)
        elif model_type == 'lightgbm':
            objective = self.create_lightgbm_objective(X_train, y_train, X_val, y_val)
        elif model_type == 'random_forest':
            objective = self.create_random_forest_objective(X_train, y_train, X_val, y_val)
        else:
            raise ValueError(f"Unknown model type: {model_type}")
        
        # Run optimization
        start_time = datetime.now()
        
        try:
            study.optimize(
                objective,
                n_trials=n_trials,
                timeout=timeout,
                n_jobs=self.n_jobs,
                show_progress_bar=True
            )
        except KeyboardInterrupt:
            logger.info("⏹️ Optimization interrupted by user")
        
        optimization_time = (datetime.now() - start_time).total_seconds()
        
        # Get results
        best_params = study.best_params
        best_value = study.best_value
        n_completed_trials = len([t for t in study.trials if t.state == optuna.trial.TrialState.COMPLETE])
        
        # Store results
        self.studies[model_type] = study
        self.best_params[model_type] = best_params
        
        results = {
            'best_params': best_params,
            'best_rmse': best_value,
            'n_trials': n_completed_trials,
            'optimization_time_seconds': optimization_time,
            'study': study
        }
        
        logger.info(f"✅ {model_type} optimization completed!")
        logger.info(f"   Best RMSE: {best_value:.4f}")
        logger.info(f"   Completed trials: {n_completed_trials}")
        logger.info(f"   Optimization time: {optimization_time:.1f}s")
        logger.info(f"   Best parameters:")
        for param, value in best_params.items():
            logger.info(f"     {param}: {value}")
        
        return results
    
    def multi_objective_optimization(self, X_train: np.ndarray, y_train: np.ndarray,
                                   X_val: np.ndarray, y_val: np.ndarray,
                                   model_type: str = 'xgboost', n_trials: int = 100) -> Dict[str, Any]:
        """Multi-objective optimization (accuracy vs speed)"""
        
        if not OPTUNA_AVAILABLE:
            logger.error("❌ Optuna not available - cannot perform optimization")
            return {}
        
        logger.info(f"🎯 Multi-objective optimization for {model_type}...")
        
        def multi_objective(trial):
            if model_type == 'xgboost':
                params = {
                    'n_estimators': trial.suggest_int('n_estimators', 100, 1000),
                    'max_depth': trial.suggest_int('max_depth', 3, 10),
                    'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3, log=True),
                    'subsample': trial.suggest_float('subsample', 0.7, 1.0),
                    'colsample_bytree': trial.suggest_float('colsample_bytree', 0.7, 1.0),
                    'random_state': 42,
                    'n_jobs': -1,
                    'tree_method': 'gpu_hist' if self.gpu_available else 'hist'
                }
                
                if self.gpu_available:
                    params['gpu_id'] = 0
                
                model = xgb.XGBRegressor(**params)
            else:
                raise ValueError(f"Multi-objective optimization not implemented for {model_type}")
            
            # Measure training time
            start_time = datetime.now()
            model.fit(X_train, y_train)
            training_time = (datetime.now() - start_time).total_seconds()
            
            # Calculate accuracy
            y_pred = model.predict(X_val)
            rmse = np.sqrt(mean_squared_error(y_val, y_pred))
            
            # Return both objectives (minimize RMSE, minimize training time)
            return rmse, training_time
        
        # Create multi-objective study
        study = optuna.create_study(
            directions=['minimize', 'minimize'],  # minimize RMSE and training time
            sampler=TPESampler()
        )
        
        study.optimize(multi_objective, n_trials=n_trials)
        
        # Analyze Pareto front
        pareto_trials = []
        for trial in study.trials:
            if trial.state == optuna.trial.TrialState.COMPLETE:
                pareto_trials.append({
                    'params': trial.params,
                    'rmse': trial.values[0],
                    'training_time': trial.values[1]
                })
        
        # Sort by RMSE
        pareto_trials.sort(key=lambda x: x['rmse'])
        
        results = {
            'pareto_trials': pareto_trials,
            'best_accuracy': pareto_trials[0] if pareto_trials else None,
            'best_speed': min(pareto_trials, key=lambda x: x['training_time']) if pareto_trials else None,
            'study': study
        }
        
        logger.info(f"✅ Multi-objective optimization completed!")
        logger.info(f"   Pareto optimal solutions: {len(pareto_trials)}")
        
        if pareto_trials:
            best_acc = results['best_accuracy']
            best_speed = results['best_speed']
            logger.info(f"   Best accuracy: RMSE={best_acc['rmse']:.4f}, Time={best_acc['training_time']:.1f}s")
            logger.info(f"   Best speed: RMSE={best_speed['rmse']:.4f}, Time={best_speed['training_time']:.1f}s")
        
        return results
    
    def optimize_ensemble_weights(self, predictions: Dict[str, np.ndarray], 
                                 y_true: np.ndarray, n_trials: int = 200) -> Dict[str, Any]:
        """Optimize ensemble weights using Optuna"""
        
        if not OPTUNA_AVAILABLE:
            logger.error("❌ Optuna not available - cannot perform optimization")
            return {}
        
        logger.info("⚖️ Optimizing ensemble weights...")
        
        model_names = list(predictions.keys())
        pred_matrix = np.column_stack([predictions[name] for name in model_names])
        
        def objective(trial):
            # Suggest weights (they will be normalized)
            weights = []
            for name in model_names:
                weight = trial.suggest_float(f'weight_{name}', 0.0, 1.0)
                weights.append(weight)
            
            # Normalize weights
            weights = np.array(weights)
            weights = weights / np.sum(weights)
            
            # Calculate ensemble prediction
            ensemble_pred = np.dot(pred_matrix, weights)
            
            # Return negative R² (since we want to maximize R²)
            r2 = r2_score(y_true, ensemble_pred)
            return -r2
        
        # Create study
        study = optuna.create_study(direction='minimize')
        study.optimize(objective, n_trials=n_trials)
        
        # Get best weights
        best_weights_raw = []
        for name in model_names:
            weight = study.best_params[f'weight_{name}']
            best_weights_raw.append(weight)
        
        # Normalize best weights
        best_weights_raw = np.array(best_weights_raw)
        best_weights_normalized = best_weights_raw / np.sum(best_weights_raw)
        
        best_weights = {name: weight for name, weight in zip(model_names, best_weights_normalized)}
        
        # Calculate final performance
        ensemble_pred = np.dot(pred_matrix, best_weights_normalized)
        final_r2 = r2_score(y_true, ensemble_pred)
        final_rmse = np.sqrt(mean_squared_error(y_true, ensemble_pred))
        
        results = {
            'best_weights': best_weights,
            'best_r2': final_r2,
            'best_rmse': final_rmse,
            'study': study
        }
        
        logger.info(f"✅ Ensemble weight optimization completed!")
        logger.info(f"   Best R²: {final_r2:.4f}")
        logger.info(f"   Best RMSE: {final_rmse:.4f}")
        logger.info("   Optimal weights:")
        for name, weight in best_weights.items():
            logger.info(f"     {name}: {weight:.4f}")
        
        return results
    
    def save_optimization_results(self, filepath: str = None) -> str:
        """Save optimization results"""
        if filepath is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filepath = f"models/optimization/hyperopt_results_{timestamp}.json"
        
        # Create directory
        Path(filepath).parent.mkdir(parents=True, exist_ok=True)
        
        # Prepare results for saving
        results = {
            'study_name': self.study_name,
            'best_params': self.best_params,
            'optimization_timestamp': datetime.now().isoformat(),
            'gpu_available': self.gpu_available,
            'n_jobs': self.n_jobs
        }
        
        # Add study summaries
        study_summaries = {}
        for model_type, study in self.studies.items():
            study_summaries[model_type] = {
                'best_value': study.best_value,
                'best_params': study.best_params,
                'n_trials': len(study.trials),
                'study_name': study.study_name
            }
        
        results['study_summaries'] = study_summaries
        
        # Save to file
        with open(filepath, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        logger.info(f"💾 Optimization results saved to: {filepath}")
        return filepath

    def bayesian_optimization_with_constraints(self, model_type: str,
                                              X_train: np.ndarray, y_train: np.ndarray,
                                              X_val: np.ndarray, y_val: np.ndarray,
                                              performance_constraint: float = 0.95,
                                              speed_constraint: float = 60.0,
                                              n_trials: int = 100) -> Dict[str, Any]:
        """Bayesian optimization with performance and speed constraints"""

        if not OPTUNA_AVAILABLE:
            logger.error("❌ Optuna not available - cannot perform optimization")
            return {}

        logger.info(f"🧠 Bayesian optimization with constraints for {model_type}...")
        logger.info(f"   Performance constraint: R² ≥ {performance_constraint}")
        logger.info(f"   Speed constraint: Training time ≤ {speed_constraint}s")

        def constrained_objective(trial):
            if model_type == 'xgboost':
                params = {
                    'n_estimators': trial.suggest_int('n_estimators', 100, 1500),
                    'max_depth': trial.suggest_int('max_depth', 3, 12),
                    'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3, log=True),
                    'subsample': trial.suggest_float('subsample', 0.6, 1.0),
                    'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
                    'reg_alpha': trial.suggest_float('reg_alpha', 0.0, 5.0),
                    'reg_lambda': trial.suggest_float('reg_lambda', 0.0, 5.0),
                    'random_state': 42,
                    'n_jobs': -1,
                    'tree_method': 'gpu_hist' if self.gpu_available else 'hist'
                }

                if self.gpu_available:
                    params['gpu_id'] = 0

                model = xgb.XGBRegressor(**params)
            else:
                raise ValueError(f"Constrained optimization not implemented for {model_type}")

            # Measure training time
            start_time = datetime.now()
            model.fit(X_train, y_train)
            training_time = (datetime.now() - start_time).total_seconds()

            # Calculate performance
            y_pred = model.predict(X_val)
            r2 = r2_score(y_val, y_pred)
            rmse = np.sqrt(mean_squared_error(y_val, y_pred))

            # Apply constraints
            if r2 < performance_constraint:
                # Penalize heavily if performance constraint not met
                penalty = (performance_constraint - r2) * 100
                return rmse + penalty

            if training_time > speed_constraint:
                # Penalize if speed constraint not met
                penalty = (training_time - speed_constraint) * 0.1
                return rmse + penalty

            return rmse

        # Use CMA-ES sampler for better constraint handling
        sampler = CmaEsSampler()

        study = optuna.create_study(
            direction='minimize',
            sampler=sampler
        )

        study.optimize(constrained_objective, n_trials=n_trials)

        # Validate best solution
        best_params = study.best_params

        if model_type == 'xgboost':
            best_model = xgb.XGBRegressor(**{
                **best_params,
                'random_state': 42,
                'n_jobs': -1,
                'tree_method': 'gpu_hist' if self.gpu_available else 'hist',
                'gpu_id': 0 if self.gpu_available else None
            })

        # Test best model
        start_time = datetime.now()
        best_model.fit(X_train, y_train)
        final_training_time = (datetime.now() - start_time).total_seconds()

        y_pred = best_model.predict(X_val)
        final_r2 = r2_score(y_val, y_pred)
        final_rmse = np.sqrt(mean_squared_error(y_val, y_pred))

        # Check constraints
        performance_satisfied = final_r2 >= performance_constraint
        speed_satisfied = final_training_time <= speed_constraint

        results = {
            'best_params': best_params,
            'final_r2': final_r2,
            'final_rmse': final_rmse,
            'final_training_time': final_training_time,
            'performance_constraint_satisfied': performance_satisfied,
            'speed_constraint_satisfied': speed_satisfied,
            'constraints_satisfied': performance_satisfied and speed_satisfied,
            'study': study
        }

        logger.info(f"✅ Constrained optimization completed!")
        logger.info(f"   Final R²: {final_r2:.4f} (constraint: ≥{performance_constraint})")
        logger.info(f"   Final training time: {final_training_time:.1f}s (constraint: ≤{speed_constraint}s)")
        logger.info(f"   Performance constraint: {'✅' if performance_satisfied else '❌'}")
        logger.info(f"   Speed constraint: {'✅' if speed_satisfied else '❌'}")

        return results

    def adaptive_optimization(self, model_type: str, X_train: np.ndarray, y_train: np.ndarray,
                             X_val: np.ndarray, y_val: np.ndarray,
                             target_improvement: float = 0.01, max_trials: int = 500) -> Dict[str, Any]:
        """Adaptive optimization that stops when improvement plateaus"""

        if not OPTUNA_AVAILABLE:
            logger.error("❌ Optuna not available - cannot perform optimization")
            return {}

        logger.info(f"🔄 Adaptive optimization for {model_type}...")
        logger.info(f"   Target improvement: {target_improvement}")
        logger.info(f"   Max trials: {max_trials}")

        # Create objective function
        if model_type == 'xgboost':
            objective = self.create_xgboost_objective(X_train, y_train, X_val, y_val)
        elif model_type == 'lightgbm':
            objective = self.create_lightgbm_objective(X_train, y_train, X_val, y_val)
        elif model_type == 'random_forest':
            objective = self.create_random_forest_objective(X_train, y_train, X_val, y_val)
        else:
            raise ValueError(f"Unknown model type: {model_type}")

        # Create study with early stopping
        study = optuna.create_study(
            direction='minimize',
            sampler=TPESampler(n_startup_trials=20)
        )

        # Track improvement
        best_values = []
        no_improvement_count = 0
        patience = 20  # Stop if no improvement for 20 trials

        for trial_num in range(max_trials):
            study.optimize(objective, n_trials=1)

            current_best = study.best_value
            best_values.append(current_best)

            # Check for improvement
            if len(best_values) >= patience:
                recent_improvement = best_values[-patience] - current_best
                if recent_improvement < target_improvement:
                    no_improvement_count += 1
                    if no_improvement_count >= patience:
                        logger.info(f"   Early stopping: No improvement for {patience} trials")
                        break
                else:
                    no_improvement_count = 0

            if (trial_num + 1) % 50 == 0:
                logger.info(f"   Trial {trial_num + 1}: Best RMSE = {current_best:.4f}")

        results = {
            'best_params': study.best_params,
            'best_value': study.best_value,
            'n_trials': len(study.trials),
            'improvement_history': best_values,
            'early_stopped': len(study.trials) < max_trials,
            'study': study
        }

        logger.info(f"✅ Adaptive optimization completed!")
        logger.info(f"   Best RMSE: {study.best_value:.4f}")
        logger.info(f"   Trials used: {len(study.trials)}/{max_trials}")
        logger.info(f"   Early stopped: {results['early_stopped']}")

        return results

def main():
    """Test hyperparameter optimization system"""
    logger.info("🎛️ Testing Advanced Hyperparameter Optimization System")
    logger.info("=" * 80)
    
    # Create synthetic solar data
    np.random.seed(42)
    n_samples = 2000
    n_features = 20
    
    # Generate features with solar patterns
    X = np.random.randn(n_samples, n_features)
    
    # Create target with complex relationships
    y = (2 * X[:, 0] + 1.5 * X[:, 1] + 0.8 * X[:, 2] + 
         0.5 * np.sin(X[:, 3]) + 0.3 * X[:, 4] * X[:, 5] +
         np.random.normal(0, 0.3, n_samples))
    
    # Split data
    split_idx = int(0.8 * n_samples)
    X_train, X_test = X[:split_idx], X[split_idx:]
    y_train, y_test = y[:split_idx], y[split_idx:]
    
    # Further split for validation
    val_split = int(0.8 * len(X_train))
    X_train_final, X_val = X_train[:val_split], X_train[val_split:]
    y_train_final, y_val = y_train[:val_split], y_train[val_split:]
    
    logger.info(f"Data split: Train={X_train_final.shape}, Val={X_val.shape}, Test={X_test.shape}")
    
    # Initialize optimizer
    optimizer = AdvancedHyperparameterOptimizer(
        study_name="test_solar_optimization",
        n_jobs=2  # Reduced for testing
    )
    
    # Test optimization for different models
    models_to_optimize = ['xgboost', 'lightgbm', 'random_forest']
    optimization_results = {}
    
    for model_type in models_to_optimize:
        logger.info(f"\n🔧 Optimizing {model_type}...")
        
        # Run optimization with reduced trials for testing
        result = optimizer.optimize_model(
            model_type=model_type,
            X_train=X_train_final,
            y_train=y_train_final,
            X_val=X_val,
            y_val=y_val,
            n_trials=20,  # Reduced for testing
            timeout=300   # 5 minutes max
        )
        
        optimization_results[model_type] = result
    
    # Save results
    results_path = optimizer.save_optimization_results()
    
    # Display summary
    logger.info("\n🎯 HYPERPARAMETER OPTIMIZATION RESULTS")
    logger.info("=" * 80)
    
    for model_type, result in optimization_results.items():
        if result:
            logger.info(f"\n📊 {model_type.upper()}:")
            logger.info(f"   Best RMSE: {result['best_rmse']:.4f}")
            logger.info(f"   Trials completed: {result['n_trials']}")
            logger.info(f"   Optimization time: {result['optimization_time_seconds']:.1f}s")
            logger.info("   Best parameters:")
            for param, value in result['best_params'].items():
                logger.info(f"     {param}: {value}")
    
    logger.info(f"\n💾 Results saved to: {results_path}")
    logger.info("\n✅ Hyperparameter Optimization System test completed!")
    
    return optimizer, optimization_results

if __name__ == "__main__":
    optimizer, results = main()
