#!/usr/bin/env python3
"""
Database-based Model Training Script
Fixes all permission issues by storing models in PostgreSQL database
"""

import os
import sys
import tempfile
import logging
import psycopg2
import joblib
import json
import pandas as pd
import numpy as np
from io import BytesIO
from datetime import datetime, timedelta
from pathlib import Path

# Add project root to path
sys.path.append('/app')

# Set environment variables to fix issues
os.environ['MPLCONFIGDIR'] = tempfile.mkdtemp(prefix='matplotlib_')
os.environ['NUMBA_CACHE_DIR'] = tempfile.mkdtemp(prefix='numba_')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseModelTrainer:
    """
    Enhanced model trainer that stores models in PostgreSQL database
    Fixes all permission and storage issues
    """
    
    def __init__(self):
        self.db_config = {
            'host': os.getenv('DB_HOST', 'solar-prediction-db'),
            'port': int(os.getenv('DB_PORT', 5432)),
            'database': os.getenv('DB_NAME', 'solar_prediction'),
            'user': os.getenv('DB_USER', 'postgres'),
            'password': os.getenv('DB_PASSWORD', 'postgres')
        }
        
    def get_db_connection(self):
        """Get database connection"""
        return psycopg2.connect(**self.db_config)
    
    def save_model_to_database(self, model, scaler, system_id, model_type, model_name, metadata=None):
        """Save model and scaler to database"""
        try:
            # Convert model to binary
            model_buffer = BytesIO()
            joblib.dump(model, model_buffer)
            model_binary = model_buffer.getvalue()
            
            # Convert scaler to binary
            scaler_buffer = BytesIO()
            joblib.dump(scaler, scaler_buffer)
            scaler_binary = scaler_buffer.getvalue()
            
            # Prepare metadata
            if metadata is None:
                metadata = {}
            metadata.update({
                'created_at': datetime.now().isoformat(),
                'model_size_bytes': len(model_binary),
                'scaler_size_bytes': len(scaler_binary)
            })
            
            conn = self.get_db_connection()
            cursor = conn.cursor()
            
            # Deactivate old models
            cursor.execute("""
                UPDATE ml_models
                SET is_active = FALSE, updated_at = NOW()
                WHERE system_id = %s AND model_type = %s AND model_name = %s
            """, (system_id, model_type, model_name))

            # Insert new model (with ON CONFLICT handling)
            cursor.execute("""
                INSERT INTO ml_models (
                    system_id, model_type, model_name, model_data,
                    scaler_data, metadata, created_at, updated_at, is_active
                ) VALUES (%s, %s, %s, %s, %s, %s, NOW(), NOW(), TRUE)
                ON CONFLICT (system_id, model_type, model_name)
                DO UPDATE SET
                    model_data = EXCLUDED.model_data,
                    scaler_data = EXCLUDED.scaler_data,
                    metadata = EXCLUDED.metadata,
                    updated_at = NOW(),
                    is_active = TRUE
            """, (system_id, model_type, model_name, model_binary, scaler_binary, json.dumps(metadata)))
            
            conn.commit()
            conn.close()
            
            logger.info(f"✅ Model saved to database: System {system_id} {model_type} {model_name}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to save model to database: {e}")
            return False
    
    def load_training_data(self, system_id, days_back=90):
        """Load training data from unified table"""
        try:
            conn = self.get_db_connection()
            
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days_back)
            
            query = """
                SELECT 
                    timestamp,
                    ac_power,
                    EXTRACT(hour FROM timestamp) as hour,
                    EXTRACT(day FROM timestamp) as day,
                    temperature,
                    soc,
                    bat_power,
                    yield_today,
                    powerdc1,
                    powerdc2,
                    system_id
                FROM solax_unified_data 
                WHERE system_id = %s 
                AND timestamp BETWEEN %s AND %s
                AND ac_power IS NOT NULL
                ORDER BY timestamp
            """
            
            df = pd.read_sql_query(query, conn, params=(system_id, start_date, end_date))
            conn.close()
            
            logger.info(f"✅ Loaded {len(df)} training records for System {system_id}")
            return df
            
        except Exception as e:
            logger.error(f"❌ Failed to load training data: {e}")
            return None
    
    def create_features(self, df):
        """Create features for training"""
        try:
            # Create temporal features
            df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
            df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
            df['day_sin'] = np.sin(2 * np.pi * df['day'] / 365)
            df['day_cos'] = np.cos(2 * np.pi * df['day'] / 365)
            
            # Feature columns
            feature_cols = [
                'hour_sin', 'hour_cos', 'day_sin', 'day_cos',
                'temperature', 'soc', 'bat_power', 'powerdc1', 'powerdc2', 'yield_today'
            ]
            
            # Remove rows with missing features and filter valid power
            df = df.dropna(subset=feature_cols + ['ac_power'])
            df = df[df['ac_power'] >= 0]  # Include zero power (nighttime)

            logger.info(f"✅ Created features: {len(df)} valid records")

            if len(df) < 100:
                logger.error(f"❌ Insufficient data after filtering: {len(df)} records")
                return None, None

            return df, feature_cols
            
        except Exception as e:
            logger.error(f"❌ Feature creation failed: {e}")
            return None, None
    
    def train_lightgbm_model(self, df, feature_cols, system_id):
        """Train LightGBM model with CPU fallback"""
        try:
            import lightgbm as lgb
            from sklearn.model_selection import train_test_split
            from sklearn.preprocessing import StandardScaler
            from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
            
            # Prepare data
            X = df[feature_cols].values
            y = df['ac_power'].values
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42
            )
            
            # Scale features
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)
            
            # Try GPU first, fallback to CPU
            try:
                model = lgb.LGBMRegressor(
                    device='gpu',
                    gpu_platform_id=0,
                    gpu_device_id=0,
                    n_estimators=100,
                    random_state=42
                )
                # Test if GPU actually works
                model.fit(X_train_scaled[:100], y_train[:100])
                logger.info("🚀 Using LightGBM with GPU acceleration")
            except Exception as gpu_error:
                logger.warning(f"⚠️ GPU not available: {gpu_error}")
                model = lgb.LGBMRegressor(
                    device='cpu',
                    n_estimators=100,
                    random_state=42
                )
                logger.info("🔄 Using LightGBM with CPU (GPU not available)")
            
            # Train model
            model.fit(X_train_scaled, y_train)
            
            # Evaluate
            y_pred = model.predict(X_test_scaled)
            
            r2 = r2_score(y_test, y_pred)
            mae = mean_absolute_error(y_test, y_pred)
            rmse = np.sqrt(mean_squared_error(y_test, y_pred))
            
            logger.info(f"📊 Model Performance for System {system_id}:")
            logger.info(f"   R²: {r2:.4f}")
            logger.info(f"   MAE: {mae:.2f} W")
            logger.info(f"   RMSE: {rmse:.2f} W")
            
            # Save to database
            metadata = {
                'r2_score': float(r2),
                'mae': float(mae),
                'rmse': float(rmse),
                'training_samples': len(X_train),
                'test_samples': len(X_test),
                'features': feature_cols
            }
            
            success = self.save_model_to_database(
                model, scaler, system_id, 'enhanced_lightgbm', 'production_v1', metadata
            )
            
            return success
            
        except Exception as e:
            logger.error(f"❌ Model training failed: {e}")
            return False
    
    def train_system_models(self, system_id):
        """Train models for a specific system"""
        logger.info(f"🚀 Training models for System {system_id}")
        
        # Load data
        df = self.load_training_data(system_id)
        if df is None or len(df) < 100:
            logger.error(f"❌ Insufficient data for System {system_id}")
            return False
        
        # Create features
        df, feature_cols = self.create_features(df)
        if df is None:
            return False
        
        # Train model
        success = self.train_lightgbm_model(df, feature_cols, system_id)
        
        return success
    
    def train_all_systems(self):
        """Train models for all systems"""
        logger.info("🌞 Starting Database-based Model Training")
        logger.info("="*60)
        
        results = {}
        
        for system_id in [1, 2]:
            try:
                success = self.train_system_models(system_id)
                results[f'system_{system_id}'] = success
                
                if success:
                    logger.info(f"✅ System {system_id} training completed successfully")
                else:
                    logger.error(f"❌ System {system_id} training failed")
                    
            except Exception as e:
                logger.error(f"❌ System {system_id} training error: {e}")
                results[f'system_{system_id}'] = False
        
        # Summary
        successful = sum(1 for success in results.values() if success)
        total = len(results)
        
        logger.info("="*60)
        logger.info(f"🎉 Training Summary: {successful}/{total} systems successful")
        
        if successful == total:
            logger.info("✅ All systems trained successfully!")
            return True
        else:
            logger.warning(f"⚠️ {total - successful} systems failed training")
            return False

def main():
    """Main training function"""
    try:
        trainer = DatabaseModelTrainer()
        success = trainer.train_all_systems()
        
        if success:
            print("🎉 Database-based model training completed successfully!")
            return True
        else:
            print("❌ Some models failed to train")
            return False
            
    except Exception as e:
        logger.error(f"❌ Training failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
