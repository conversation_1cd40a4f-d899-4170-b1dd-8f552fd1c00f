#!/usr/bin/env python3
"""
Enhanced Model v3 - Production Ready Implementation
Target: >95% accuracy with real data validation
"""

import pandas as pd
import numpy as np
import joblib
import json
import os
from datetime import datetime, timedelta
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import TimeSeriesSplit, cross_val_score
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import Ridge, ElasticNet
from sklearn.preprocessing import StandardScaler, RobustScaler, MinMaxScaler
from sklearn.metrics import r2_score, mean_absolute_error, mean_squared_error
import lightgbm as lgb
import xgboost as xgb
import warnings
warnings.filterwarnings('ignore')

class EnhancedModelV3ProductionReady:
    """Enhanced Model v3 for Production with >95% accuracy target"""
    
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.feature_columns = []
        self.target_accuracy = 0.95
        self.best_model_name = None
        self.best_score = 0
        
    def load_real_data(self):
        """Load and prepare real solar production data"""
        print("📊 LOADING REAL SOLAR PRODUCTION DATA")
        print("=" * 60)
        
        try:
            # Connect to database and get real data
            import psycopg2
            import os
            
            conn = psycopg2.connect(
                host=os.getenv('DB_HOST', 'localhost'),
                database=os.getenv('DB_NAME', 'solar_prediction'),
                user=os.getenv('DB_USER', 'postgres'),
                password=os.getenv('DB_PASSWORD', 'postgres')
            )
            
            # Get comprehensive data from both systems
            query = """
            SELECT
                s1.timestamp,
                s1.ac_power as system1_ac_power,
                s1.soc as system1_soc,
                s1.bat_power as system1_battery_power,
                s1.powerdc1 + s1.powerdc2 as system1_pv_power,
                s1.feedin_power as system1_load_power,
                s1.yield_today as system1_yield_today,
                s1.yield_total as system1_yield_total,
                s2.ac_power as system2_ac_power,
                s2.soc as system2_soc,
                s2.bat_power as system2_battery_power,
                s2.powerdc1 + s2.powerdc2 as system2_pv_power,
                s2.feedin_power as system2_load_power,
                s2.yield_today as system2_yield_today,
                s2.yield_total as system2_yield_total,
                w.temperature_2m,
                w.cloud_cover,
                w.relative_humidity_2m,
                w.shortwave_radiation,
                w.direct_normal_irradiance,
                w.diffuse_radiation,
                w.global_horizontal_irradiance,
                w.solar_elevation_angle
            FROM solax_data s1
            LEFT JOIN solax_data2 s2 ON DATE_TRUNC('hour', s1.timestamp) = DATE_TRUNC('hour', s2.timestamp)
            LEFT JOIN weather_data w ON DATE_TRUNC('hour', s1.timestamp) = DATE_TRUNC('hour', w.timestamp)
            WHERE s1.timestamp >= '2024-03-01'
            AND s1.ac_power IS NOT NULL
            AND s2.ac_power IS NOT NULL
            AND w.temperature_2m IS NOT NULL
            ORDER BY s1.timestamp
            """
            
            df = pd.read_sql(query, conn)
            conn.close()
            
            print(f"✅ Loaded {len(df):,} records from database")
            print(f"📅 Date range: {df['timestamp'].min()} to {df['timestamp'].max()}")
            
            # Data quality checks
            print(f"\n🔍 DATA QUALITY ANALYSIS:")
            print(f"   System 1 AC Power: {df['system1_ac_power'].min():.1f}W to {df['system1_ac_power'].max():.1f}W")
            print(f"   System 2 AC Power: {df['system2_ac_power'].min():.1f}W to {df['system2_ac_power'].max():.1f}W")
            print(f"   Temperature: {df['temperature_2m'].min():.1f}°C to {df['temperature_2m'].max():.1f}°C")
            print(f"   Missing values: {df.isnull().sum().sum()}")
            
            return df
            
        except Exception as e:
            print(f"❌ Failed to load real data: {e}")
            return None
    
    def engineer_advanced_features(self, df):
        """Create advanced feature engineering for maximum accuracy"""
        print("\n🔧 ADVANCED FEATURE ENGINEERING")
        print("=" * 60)
        
        # Convert timestamp
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df = df.set_index('timestamp')
        
        # === TEMPORAL FEATURES ===
        df['hour'] = df.index.hour
        df['day_of_year'] = df.index.dayofyear
        df['month'] = df.index.month
        df['day_of_week'] = df.index.dayofweek
        df['week_of_year'] = df.index.isocalendar().week
        df['is_weekend'] = (df.index.dayofweek >= 5).astype(int)
        
        # Cyclical encoding for temporal features
        df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
        df['day_sin'] = np.sin(2 * np.pi * df['day_of_year'] / 365)
        df['day_cos'] = np.cos(2 * np.pi * df['day_of_year'] / 365)
        df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
        
        # === SOLAR POSITION FEATURES ===
        # Simplified solar position calculation
        df['solar_elevation'] = np.where(
            (df['hour'] >= 6) & (df['hour'] <= 18),
            90 * np.sin(np.pi * (df['hour'] - 6) / 12),
            0
        )
        df['solar_azimuth'] = 180 + 15 * (df['hour'] - 12)  # Simplified
        df['daylight_hours'] = np.where(df['solar_elevation'] > 0, 1, 0)
        
        # === WEATHER FEATURES ===
        # Normalize weather features
        df['temperature_norm'] = (df['temperature_2m'] - df['temperature_2m'].mean()) / df['temperature_2m'].std()
        df['cloud_cover_norm'] = df['cloud_cover'] / 100.0
        df['humidity_norm'] = df['relative_humidity_2m'] / 100.0
        df['ghi_norm'] = df['global_horizontal_irradiance'] / 1000.0

        # Weather interactions
        df['clear_sky_factor'] = 1 - df['cloud_cover_norm']
        df['temp_radiation_interaction'] = df['temperature_norm'] * df['shortwave_radiation']
        df['elevation_radiation'] = df['solar_elevation_angle'] * df['shortwave_radiation']
        df['ghi_elevation'] = df['ghi_norm'] * df['solar_elevation_angle']
        
        # === SYSTEM PERFORMANCE FEATURES ===
        # System 1 features
        df['system1_efficiency'] = np.where(
            df['system1_pv_power'] > 0,
            df['system1_ac_power'] / df['system1_pv_power'],
            0
        )
        df['system1_battery_ratio'] = df['system1_battery_power'] / (df['system1_ac_power'] + 1)
        df['system1_load_ratio'] = df['system1_load_power'] / (df['system1_ac_power'] + 1)
        
        # System 2 features
        df['system2_efficiency'] = np.where(
            df['system2_pv_power'] > 0,
            df['system2_ac_power'] / df['system2_pv_power'],
            0
        )
        df['system2_battery_ratio'] = df['system2_battery_power'] / (df['system2_ac_power'] + 1)
        df['system2_load_ratio'] = df['system2_load_power'] / (df['system2_ac_power'] + 1)
        
        # === ROLLING FEATURES ===
        # 24-hour rolling averages
        for col in ['system1_ac_power', 'system2_ac_power', 'temperature_2m', 'cloud_cover']:
            df[f'{col}_24h_mean'] = df[col].rolling(window=24, min_periods=1).mean()
            df[f'{col}_24h_std'] = df[col].rolling(window=24, min_periods=1).std()
        
        # === LAG FEATURES ===
        # Previous hour features
        for col in ['system1_ac_power', 'system2_ac_power', 'system1_soc', 'system2_soc']:
            df[f'{col}_lag1'] = df[col].shift(1)
            df[f'{col}_lag24'] = df[col].shift(24)  # Same hour yesterday
        
        # === INTERACTION FEATURES ===
        df['systems_correlation'] = df['system1_ac_power'] * df['system2_ac_power']
        df['total_production'] = df['system1_ac_power'] + df['system2_ac_power']
        df['production_difference'] = abs(df['system1_ac_power'] - df['system2_ac_power'])
        
        # === SEASONAL FEATURES ===
        df['season'] = ((df['month'] - 1) // 3) % 4  # 0=Winter, 1=Spring, 2=Summer, 3=Autumn
        df['is_summer'] = (df['season'] == 2).astype(int)
        df['is_winter'] = (df['season'] == 0).astype(int)
        
        # Handle missing values more carefully
        print(f"\n🧹 HANDLING MISSING VALUES:")
        print(f"   Before cleaning: {len(df):,} records")

        # Fill missing values for critical features
        critical_features = ['system1_ac_power', 'system2_ac_power', 'temperature_2m', 'cloud_cover']
        for feature in critical_features:
            if feature in df.columns:
                missing_count = df[feature].isnull().sum()
                if missing_count > 0:
                    df[feature] = df[feature].fillna(df[feature].median())
                    print(f"   Filled {missing_count:,} missing values in {feature}")

        # Remove rows where both systems have no AC power data
        df = df.dropna(subset=['system1_ac_power', 'system2_ac_power'], how='all')

        # Fill remaining missing values with forward fill then backward fill
        df = df.fillna(method='ffill').fillna(method='bfill')

        # Final cleanup - remove any remaining NaN rows
        df = df.dropna()

        print(f"   After cleaning: {len(df):,} records")
        print(f"✅ Created {len(df.columns)} total features")
        print(f"📊 Final dataset: {len(df):,} records")
        
        return df
    
    def select_optimal_features(self, df):
        """Select optimal features for maximum predictive power"""
        print("\n🎯 OPTIMAL FEATURE SELECTION")
        print("=" * 60)
        
        # Define feature categories
        temporal_features = [
            'hour', 'day_of_year', 'month', 'day_of_week', 'is_weekend',
            'hour_sin', 'hour_cos', 'day_sin', 'day_cos', 'month_sin', 'month_cos'
        ]
        
        solar_features = [
            'solar_elevation', 'solar_azimuth', 'daylight_hours'
        ]
        
        weather_features = [
            'temperature_2m', 'cloud_cover', 'relative_humidity_2m', 'shortwave_radiation',
            'direct_normal_irradiance', 'diffuse_radiation', 'global_horizontal_irradiance', 'solar_elevation_angle',
            'temperature_norm', 'cloud_cover_norm', 'humidity_norm', 'ghi_norm',
            'clear_sky_factor', 'temp_radiation_interaction', 'elevation_radiation', 'ghi_elevation'
        ]
        
        system_features = [
            'system1_soc', 'system2_soc', 'system1_battery_power', 'system2_battery_power',
            'system1_pv_power', 'system2_pv_power', 'system1_load_power', 'system2_load_power',
            'system1_efficiency', 'system2_efficiency', 'system1_battery_ratio', 'system2_battery_ratio',
            'system1_load_ratio', 'system2_load_ratio'
        ]
        
        rolling_features = [col for col in df.columns if '_24h_' in col]
        lag_features = [col for col in df.columns if '_lag' in col]
        interaction_features = ['systems_correlation', 'total_production', 'production_difference']
        seasonal_features = ['season', 'is_summer', 'is_winter']
        
        # Combine all features
        all_features = (temporal_features + solar_features + weather_features + 
                       system_features + rolling_features + lag_features + 
                       interaction_features + seasonal_features)
        
        # Filter features that exist in dataframe
        available_features = [f for f in all_features if f in df.columns]
        
        print(f"📊 Feature Categories:")
        print(f"   Temporal: {len([f for f in temporal_features if f in df.columns])}")
        print(f"   Solar: {len([f for f in solar_features if f in df.columns])}")
        print(f"   Weather: {len([f for f in weather_features if f in df.columns])}")
        print(f"   System: {len([f for f in system_features if f in df.columns])}")
        print(f"   Rolling: {len(rolling_features)}")
        print(f"   Lag: {len(lag_features)}")
        print(f"   Interaction: {len(interaction_features)}")
        print(f"   Seasonal: {len(seasonal_features)}")
        print(f"   Total Available: {len(available_features)}")
        
        self.feature_columns = available_features
        return available_features
    
    def prepare_training_data(self, df):
        """Prepare training data for both systems"""
        print("\n📊 PREPARING TRAINING DATA")
        print("=" * 60)
        
        # Create separate datasets for each system
        system1_data = df[self.feature_columns + ['system1_ac_power']].copy()
        system2_data = df[self.feature_columns + ['system2_ac_power']].copy()
        
        # Remove outliers (beyond 3 standard deviations)
        for data, system in [(system1_data, 'system1'), (system2_data, 'system2')]:
            target_col = f'{system}_ac_power'
            mean_val = data[target_col].mean()
            std_val = data[target_col].std()
            
            before_count = len(data)
            if before_count > 0:
                data = data[abs(data[target_col] - mean_val) <= 3 * std_val]
                after_count = len(data)
                removed_count = before_count - after_count
                removal_pct = (removed_count / before_count) * 100 if before_count > 0 else 0
                print(f"   {system}: Removed {removed_count:,} outliers ({removal_pct:.1f}%)")
            else:
                print(f"   {system}: No data available")
                after_count = 0
            
            if system == 'system1':
                system1_data = data
            else:
                system2_data = data
        
        # Time series split (80% train, 20% test)
        split_date = df.index[int(len(df) * 0.8)]
        
        system1_train = system1_data[system1_data.index <= split_date]
        system1_test = system1_data[system1_data.index > split_date]
        
        system2_train = system2_data[system2_data.index <= split_date]
        system2_test = system2_data[system2_data.index > split_date]
        
        print(f"📅 Split date: {split_date}")
        print(f"📊 System 1 - Train: {len(system1_train):,}, Test: {len(system1_test):,}")
        print(f"📊 System 2 - Train: {len(system2_train):,}, Test: {len(system2_test):,}")
        
        return {
            'system1': {'train': system1_train, 'test': system1_test},
            'system2': {'train': system2_train, 'test': system2_test}
        }
    
    def benchmark_algorithms(self, data_splits):
        """Benchmark multiple algorithms to find the best performer"""
        print("\n🏆 ALGORITHM BENCHMARKING")
        print("=" * 60)
        
        # Define algorithms to test
        algorithms = {
            'LightGBM': lgb.LGBMRegressor(
                n_estimators=1000,
                learning_rate=0.1,
                max_depth=8,
                num_leaves=31,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=42,
                verbose=-1
            ),
            'XGBoost': xgb.XGBRegressor(
                n_estimators=1000,
                learning_rate=0.1,
                max_depth=8,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=42,
                verbosity=0
            ),
            'RandomForest': RandomForestRegressor(
                n_estimators=500,
                max_depth=15,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42,
                n_jobs=-1
            ),
            'GradientBoosting': GradientBoostingRegressor(
                n_estimators=500,
                learning_rate=0.1,
                max_depth=8,
                subsample=0.8,
                random_state=42
            ),
            'ElasticNet': ElasticNet(
                alpha=0.1,
                l1_ratio=0.5,
                random_state=42
            )
        }
        
        # Define scalers to test
        scalers = {
            'StandardScaler': StandardScaler(),
            'RobustScaler': RobustScaler(),
            'MinMaxScaler': MinMaxScaler()
        }
        
        results = {}
        
        for system in ['system1', 'system2']:
            print(f"\n🔄 Benchmarking {system.upper()}")
            print("-" * 40)
            
            train_data = data_splits[system]['train']
            test_data = data_splits[system]['test']
            
            X_train = train_data[self.feature_columns]
            y_train = train_data[f'{system}_ac_power']
            X_test = test_data[self.feature_columns]
            y_test = test_data[f'{system}_ac_power']
            
            system_results = {}
            
            for scaler_name, scaler in scalers.items():
                for algo_name, algorithm in algorithms.items():
                    try:
                        # Scale features
                        X_train_scaled = scaler.fit_transform(X_train)
                        X_test_scaled = scaler.transform(X_test)
                        
                        # Train model
                        model = algorithm.fit(X_train_scaled, y_train)
                        
                        # Predict
                        y_pred_train = model.predict(X_train_scaled)
                        y_pred_test = model.predict(X_test_scaled)
                        
                        # Calculate metrics
                        train_r2 = r2_score(y_train, y_pred_train)
                        test_r2 = r2_score(y_test, y_pred_test)
                        test_mae = mean_absolute_error(y_test, y_pred_test)
                        test_rmse = np.sqrt(mean_squared_error(y_test, y_pred_test))
                        
                        # Store results
                        combo_name = f"{algo_name}_{scaler_name}"
                        system_results[combo_name] = {
                            'train_r2': train_r2,
                            'test_r2': test_r2,
                            'test_mae': test_mae,
                            'test_rmse': test_rmse,
                            'model': model,
                            'scaler': scaler,
                            'algorithm': algo_name,
                            'scaler_name': scaler_name
                        }
                        
                        print(f"   {combo_name:<25} R²: {test_r2:.4f} ({test_r2*100:.1f}%) MAE: {test_mae:.1f}W")
                        
                    except Exception as e:
                        print(f"   {combo_name:<25} FAILED: {e}")
            
            results[system] = system_results
        
        return results
    
    def select_best_models(self, benchmark_results):
        """Select the best performing models for each system"""
        print("\n🥇 SELECTING BEST MODELS")
        print("=" * 60)
        
        best_models = {}
        
        for system in ['system1', 'system2']:
            system_results = benchmark_results[system]
            
            # Find best model by test R²
            best_combo = max(system_results.keys(), key=lambda k: system_results[k]['test_r2'])
            best_result = system_results[best_combo]
            
            best_models[system] = best_result
            
            print(f"\n🏆 {system.upper()} BEST MODEL:")
            print(f"   Algorithm: {best_result['algorithm']}")
            print(f"   Scaler: {best_result['scaler_name']}")
            print(f"   Test R²: {best_result['test_r2']:.4f} ({best_result['test_r2']*100:.1f}%)")
            print(f"   Test MAE: {best_result['test_mae']:.1f}W")
            print(f"   Test RMSE: {best_result['test_rmse']:.1f}W")
            
            # Check if meets target accuracy
            if best_result['test_r2'] >= self.target_accuracy:
                print(f"   ✅ MEETS TARGET ACCURACY (>{self.target_accuracy*100:.0f}%)")
            else:
                print(f"   ⚠️ BELOW TARGET ACCURACY (>{self.target_accuracy*100:.0f}%)")
        
        return best_models

if __name__ == "__main__":
    print("🚀 ENHANCED MODEL V3 - PRODUCTION READY TRAINING")
    print("=" * 80)
    print(f"🎯 Target Accuracy: >95%")
    print(f"📊 Real Data Validation")
    print(f"🔬 Comprehensive Benchmarking")
    print("=" * 80)
    
    trainer = EnhancedModelV3ProductionReady()
    
    # Step 1: Load real data
    df = trainer.load_real_data()
    if df is None:
        print("❌ Cannot proceed without real data")
        exit(1)
    
    # Step 2: Engineer features
    df = trainer.engineer_advanced_features(df)
    
    # Step 3: Select features
    features = trainer.select_optimal_features(df)
    
    # Step 4: Prepare training data
    data_splits = trainer.prepare_training_data(df)
    
    # Step 5: Benchmark algorithms
    results = trainer.benchmark_algorithms(data_splits)
    
    # Step 6: Select best models
    best_models = trainer.select_best_models(results)
    
    print(f"\n🎉 BENCHMARKING COMPLETED!")
    print(f"📊 Ready for model selection and validation")
