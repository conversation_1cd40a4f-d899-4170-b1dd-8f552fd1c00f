#!/usr/bin/env python3
"""
Production Ensemble System
==========================

Production-ready ensemble system for solar prediction:
- XGBoost + LightGBM + Random Forest ensemble
- Intelligent weight optimization
- Cross-validation based model selection
- Real-time prediction capabilities

Target: >98% accuracy through ensemble methods
Created: June 6, 2025
"""

import numpy as np
import pandas as pd
import logging
from datetime import datetime
from typing import Dict, List, Tuple, Any, Optional
import joblib
import json
from pathlib import Path

# ML libraries
import xgboost as xgb
import lightgbm as lgb
from sklearn.ensemble import RandomForestRegressor, VotingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import cross_val_score, TimeSeriesSplit
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ProductionEnsembleSystem:
    """
    Production-ready ensemble system for solar prediction
    """
    
    def __init__(self, output_dir: str = "models/ensemble_production"):
        """Initialize production ensemble system"""
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Model containers
        self.models = {}
        self.ensemble_weights = {}
        self.scaler = StandardScaler()
        
        # Performance tracking
        self.model_performance = {}
        self.ensemble_performance = {}
        
        logger.info("🏭 Production Ensemble System initialized")
        logger.info(f"   Output directory: {output_dir}")
    
    def create_base_models(self) -> Dict[str, Any]:
        """Create optimized base models"""
        logger.info("🌳 Creating optimized base models...")
        
        models = {
            'xgboost': xgb.XGBRegressor(
                n_estimators=1000,
                max_depth=8,
                learning_rate=0.1,
                subsample=0.8,
                colsample_bytree=0.8,
                reg_alpha=0.1,
                reg_lambda=1.0,
                random_state=42,
                n_jobs=-1,
                tree_method='hist'  # CPU optimized
            ),
            
            'lightgbm': lgb.LGBMRegressor(
                n_estimators=1000,
                max_depth=8,
                learning_rate=0.1,
                subsample=0.8,
                colsample_bytree=0.8,
                reg_alpha=0.1,
                reg_lambda=1.0,
                random_state=42,
                n_jobs=-1,
                verbose=-1
            ),
            
            'random_forest': RandomForestRegressor(
                n_estimators=200,
                max_depth=15,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42,
                n_jobs=-1
            )
        }
        
        self.models = models
        
        logger.info(f"✅ Created {len(models)} base models")
        return models
    
    def train_individual_models(self, X_train: np.ndarray, y_train: np.ndarray,
                               X_val: np.ndarray, y_val: np.ndarray) -> Dict[str, Dict[str, float]]:
        """Train individual models and evaluate performance"""
        logger.info("🎓 Training individual models...")
        
        performance = {}
        
        for name, model in self.models.items():
            logger.info(f"   Training {name}...")
            
            # Train model
            if name == 'xgboost':
                model.fit(
                    X_train, y_train,
                    eval_set=[(X_val, y_val)],
                    verbose=False
                )
            elif name == 'lightgbm':
                model.fit(
                    X_train, y_train,
                    eval_set=[(X_val, y_val)],
                    callbacks=[lgb.early_stopping(50), lgb.log_evaluation(0)]
                )
            else:
                model.fit(X_train, y_train)
            
            # Evaluate on validation set
            y_pred = model.predict(X_val)
            
            # Calculate metrics
            rmse = np.sqrt(mean_squared_error(y_val, y_pred))
            mae = mean_absolute_error(y_val, y_pred)
            r2 = r2_score(y_val, y_pred)
            
            performance[name] = {
                'rmse': rmse,
                'mae': mae,
                'r2': r2
            }
            
            logger.info(f"     ✅ {name}: R²={r2:.4f}, RMSE={rmse:.3f}")
        
        self.model_performance = performance
        return performance
    
    def calculate_ensemble_weights(self, X_val: np.ndarray, y_val: np.ndarray) -> Dict[str, float]:
        """Calculate optimal ensemble weights based on performance"""
        logger.info("⚖️ Calculating ensemble weights...")
        
        # Get predictions from all models
        predictions = {}
        for name, model in self.models.items():
            predictions[name] = model.predict(X_val)
        
        # Calculate performance-based weights
        weights = {}
        total_score = 0
        
        for name, metrics in self.model_performance.items():
            # Use R² score as weight basis (higher is better)
            score = max(0, metrics['r2'])  # Ensure non-negative
            weights[name] = score
            total_score += score
        
        # Normalize weights
        if total_score > 0:
            weights = {name: weight / total_score for name, weight in weights.items()}
        else:
            # Equal weights if all models perform poorly
            n_models = len(weights)
            weights = {name: 1.0 / n_models for name in weights.keys()}
        
        self.ensemble_weights = weights
        
        logger.info("   Ensemble weights:")
        for name, weight in weights.items():
            logger.info(f"     {name}: {weight:.4f}")
        
        return weights
    
    def create_voting_ensemble(self) -> VotingRegressor:
        """Create voting ensemble with optimized weights"""
        logger.info("🗳️ Creating voting ensemble...")
        
        # Prepare estimators with weights
        estimators = [(name, model) for name, model in self.models.items()]
        weights = list(self.ensemble_weights.values())
        
        voting_ensemble = VotingRegressor(
            estimators=estimators,
            weights=weights
        )
        
        logger.info(f"✅ Voting ensemble created with {len(estimators)} models")
        return voting_ensemble
    
    def train_ensemble(self, X_train: np.ndarray, y_train: np.ndarray,
                      X_val: np.ndarray, y_val: np.ndarray) -> Dict[str, float]:
        """Train complete ensemble system"""
        logger.info("🚀 Training complete ensemble system...")
        
        # Step 1: Scale features
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_val_scaled = self.scaler.transform(X_val)
        
        # Step 2: Create and train base models
        self.create_base_models()
        self.train_individual_models(X_train_scaled, y_train, X_val_scaled, y_val)
        
        # Step 3: Calculate ensemble weights
        self.calculate_ensemble_weights(X_val_scaled, y_val)
        
        # Step 4: Create and train voting ensemble
        voting_ensemble = self.create_voting_ensemble()
        voting_ensemble.fit(X_train_scaled, y_train)
        
        # Step 5: Evaluate ensemble
        ensemble_pred = voting_ensemble.predict(X_val_scaled)
        
        ensemble_rmse = np.sqrt(mean_squared_error(y_val, ensemble_pred))
        ensemble_mae = mean_absolute_error(y_val, ensemble_pred)
        ensemble_r2 = r2_score(y_val, ensemble_pred)
        
        self.ensemble_performance = {
            'rmse': ensemble_rmse,
            'mae': ensemble_mae,
            'r2': ensemble_r2
        }
        
        # Store ensemble
        self.voting_ensemble = voting_ensemble
        
        logger.info(f"✅ Ensemble training completed!")
        logger.info(f"   Ensemble R²: {ensemble_r2:.4f}")
        logger.info(f"   Ensemble RMSE: {ensemble_rmse:.3f}")
        
        return self.ensemble_performance
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        """Make predictions using the trained ensemble"""
        if not hasattr(self, 'voting_ensemble'):
            raise ValueError("Ensemble must be trained before making predictions")
        
        # Scale features
        X_scaled = self.scaler.transform(X)
        
        # Make prediction
        predictions = self.voting_ensemble.predict(X_scaled)
        
        return predictions
    
    def cross_validate_ensemble(self, X: np.ndarray, y: np.ndarray, cv: int = 5) -> Dict[str, float]:
        """Cross-validate ensemble performance"""
        logger.info(f"🔄 Cross-validating ensemble (CV={cv})...")
        
        # Use TimeSeriesSplit for time series data
        tscv = TimeSeriesSplit(n_splits=cv)
        
        cv_scores = []
        
        for fold, (train_idx, val_idx) in enumerate(tscv.split(X)):
            logger.info(f"   Fold {fold + 1}/{cv}...")
            
            X_train_fold, X_val_fold = X[train_idx], X[val_idx]
            y_train_fold, y_val_fold = y[train_idx], y[val_idx]
            
            # Create temporary ensemble for this fold
            temp_ensemble = ProductionEnsembleSystem()
            temp_performance = temp_ensemble.train_ensemble(
                X_train_fold, y_train_fold, X_val_fold, y_val_fold
            )
            
            cv_scores.append(temp_performance['r2'])
            logger.info(f"     Fold {fold + 1} R²: {temp_performance['r2']:.4f}")
        
        cv_mean = np.mean(cv_scores)
        cv_std = np.std(cv_scores)
        
        cv_results = {
            'cv_mean_r2': cv_mean,
            'cv_std_r2': cv_std,
            'cv_scores': cv_scores
        }
        
        logger.info(f"✅ Cross-validation completed!")
        logger.info(f"   Mean R²: {cv_mean:.4f} ± {cv_std:.4f}")
        
        return cv_results
    
    def save_ensemble(self, filepath: str = None) -> str:
        """Save the trained ensemble system"""
        if filepath is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filepath = self.output_dir / f"production_ensemble_{timestamp}.joblib"
        
        ensemble_data = {
            'models': self.models,
            'voting_ensemble': self.voting_ensemble if hasattr(self, 'voting_ensemble') else None,
            'scaler': self.scaler,
            'ensemble_weights': self.ensemble_weights,
            'model_performance': self.model_performance,
            'ensemble_performance': self.ensemble_performance
        }
        
        joblib.dump(ensemble_data, filepath)
        
        # Save metadata
        metadata = {
            'model_performance': self.model_performance,
            'ensemble_performance': self.ensemble_performance,
            'ensemble_weights': self.ensemble_weights,
            'timestamp': datetime.now().isoformat()
        }
        
        metadata_path = str(filepath).replace('.joblib', '_metadata.json')
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2, default=str)
        
        logger.info(f"💾 Ensemble saved to: {filepath}")
        return str(filepath)
    
    def load_ensemble(self, filepath: str):
        """Load a saved ensemble system"""
        logger.info(f"📂 Loading ensemble from: {filepath}")
        
        ensemble_data = joblib.load(filepath)
        
        self.models = ensemble_data['models']
        self.voting_ensemble = ensemble_data['voting_ensemble']
        self.scaler = ensemble_data['scaler']
        self.ensemble_weights = ensemble_data['ensemble_weights']
        self.model_performance = ensemble_data['model_performance']
        self.ensemble_performance = ensemble_data['ensemble_performance']
        
        logger.info("✅ Ensemble loaded successfully")
    
    def get_ensemble_summary(self) -> Dict[str, Any]:
        """Get comprehensive ensemble summary"""
        return {
            'models': list(self.models.keys()),
            'ensemble_weights': self.ensemble_weights,
            'model_performance': self.model_performance,
            'ensemble_performance': self.ensemble_performance,
            'total_models': len(self.models)
        }

def main():
    """Test production ensemble system"""
    logger.info("🏭 Testing Production Ensemble System")
    logger.info("=" * 60)
    
    # Create synthetic solar data
    np.random.seed(42)
    n_samples = 2000
    n_features = 30
    
    # Generate features with solar patterns
    X = np.random.randn(n_samples, n_features)
    
    # Create target with complex relationships
    y = (2 * X[:, 0] + 1.5 * X[:, 1] + 0.8 * X[:, 2] + 
         0.5 * np.sin(X[:, 3]) + 0.3 * X[:, 4] * X[:, 5] +
         np.random.normal(0, 0.3, n_samples))
    
    # Split data
    split_idx = int(0.8 * n_samples)
    X_train, X_test = X[:split_idx], X[split_idx:]
    y_train, y_test = y[:split_idx], y[split_idx:]
    
    # Further split for validation
    val_split = int(0.8 * len(X_train))
    X_train_final, X_val = X_train[:val_split], X_train[val_split:]
    y_train_final, y_val = y_train[:val_split], y_train[val_split:]
    
    logger.info(f"Data split: Train={X_train_final.shape}, Val={X_val.shape}, Test={X_test.shape}")
    
    # Initialize and train ensemble
    ensemble_system = ProductionEnsembleSystem()
    
    # Train ensemble
    ensemble_performance = ensemble_system.train_ensemble(
        X_train_final, y_train_final, X_val, y_val
    )
    
    # Test on holdout set
    test_predictions = ensemble_system.predict(X_test)
    test_r2 = r2_score(y_test, test_predictions)
    test_rmse = np.sqrt(mean_squared_error(y_test, test_predictions))
    
    # Cross-validation
    cv_results = ensemble_system.cross_validate_ensemble(X_train, y_train, cv=3)
    
    # Save ensemble
    ensemble_path = ensemble_system.save_ensemble()
    
    # Get summary
    summary = ensemble_system.get_ensemble_summary()
    
    # Display results
    logger.info("🎯 PRODUCTION ENSEMBLE RESULTS")
    logger.info("=" * 60)
    
    logger.info("📊 Individual Model Performance:")
    for name, metrics in summary['model_performance'].items():
        logger.info(f"   {name}: R²={metrics['r2']:.4f}, RMSE={metrics['rmse']:.3f}")
    
    logger.info(f"\n🎭 Ensemble Performance:")
    logger.info(f"   Validation R²: {ensemble_performance['r2']:.4f}")
    logger.info(f"   Validation RMSE: {ensemble_performance['rmse']:.3f}")
    logger.info(f"   Test R²: {test_r2:.4f}")
    logger.info(f"   Test RMSE: {test_rmse:.3f}")
    
    logger.info(f"\n🔄 Cross-Validation:")
    logger.info(f"   Mean R²: {cv_results['cv_mean_r2']:.4f} ± {cv_results['cv_std_r2']:.4f}")
    
    logger.info(f"\n⚖️ Ensemble Weights:")
    for name, weight in summary['ensemble_weights'].items():
        logger.info(f"   {name}: {weight:.4f}")
    
    # Check target achievement
    target_accuracy = 0.98
    if test_r2 >= target_accuracy:
        logger.info(f"\n🎉 TARGET ACHIEVED: {test_r2:.1%} ≥ {target_accuracy:.1%}")
    else:
        logger.info(f"\n⚠️ Target not reached: {test_r2:.1%} < {target_accuracy:.1%}")
    
    logger.info(f"\n💾 Ensemble saved to: {ensemble_path}")
    logger.info("\n✅ Production Ensemble System test completed!")
    
    return ensemble_system, {
        'ensemble_performance': ensemble_performance,
        'test_performance': {'r2': test_r2, 'rmse': test_rmse},
        'cv_results': cv_results,
        'summary': summary
    }

if __name__ == "__main__":
    ensemble_system, results = main()
