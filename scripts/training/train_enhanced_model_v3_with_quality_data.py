#!/usr/bin/env python3
"""
Enhanced Model v3 Training with High-Quality Data

This script trains Enhanced Model v3 using the high-quality, system-aware data
produced by the Enhanced Data Quality System. It implements:

1. Advanced algorithm comparison (XGBoost, LightGBM, CatBoost, Ensemble)
2. System-aware training with quality-weighted samples
3. Advanced feature engineering and selection
4. Comprehensive model validation and testing
5. Production-ready model deployment

Based on extensive documentation analysis and best practices.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import pandas as pd
import numpy as np
import psycopg2
from dotenv import load_dotenv
import logging
from datetime import datetime
from sklearn.model_selection import train_test_split, cross_val_score, TimeSeriesSplit
from sklearn.ensemble import RandomForestRegressor, VotingRegressor
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import joblib
from pathlib import Path
import json
import time

# Advanced ML libraries
try:
    import xgboost as xgb
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False

try:
    import lightgbm as lgb
    LIGHTGBM_AVAILABLE = True
except ImportError:
    LIGHTGBM_AVAILABLE = False

try:
    import catboost as cb
    CATBOOST_AVAILABLE = True
except ImportError:
    CATBOOST_AVAILABLE = False

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedModelV3Trainer:
    """Enhanced Model v3 trainer with quality-aware data"""
    
    def __init__(self, output_dir="models/enhanced_v3_quality"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Feature sets for different model types
        self.feature_sets = {
            'temporal': [
                'hour_sin', 'hour_cos', 'day_sin', 'day_cos', 'month_sin', 'month_cos',
                'season_normalized', 'is_weekend_normalized'
            ],
            'system_aware': [
                'system_capacity_normalized', 'consumption_profile_encoded',
                'grid_dependency_normalized', 'self_sufficiency_normalized'
            ],
            'power': [
                'ac_power_normalized', 'soc_normalized', 'bat_power_normalized',
                'powerdc1_normalized', 'powerdc2_normalized'
            ],
            'weather': [
                'ghi_robust_normalized', 'dni_robust_normalized', 'dhi_robust_normalized',
                'temperature_robust_normalized', 'cloud_cover_robust_normalized'
            ],
            'advanced': [
                'power_efficiency_ratio', 'battery_utilization_rate',
                'weather_production_correlation', 'system_performance_index'
            ],
            'quality': [
                'data_quality_score', 'feature_completeness', 'temporal_consistency'
            ]
        }
        
        # All features combined
        self.all_features = []
        for feature_set in self.feature_sets.values():
            self.all_features.extend(feature_set)
        
        self.models = {}
        self.results = {}
        
    def load_quality_data(self):
        """Load high-quality, system-aware data"""
        logger.info("Loading high-quality system-aware data...")
        
        load_dotenv()
        conn = psycopg2.connect(
            host=os.getenv('DB_HOST', 'localhost'),
            database=os.getenv('DB_NAME', 'solar_prediction'),
            user=os.getenv('DB_USER', 'postgres'),
            password=os.getenv('DB_PASSWORD', 'postgres')
        )
        
        # Load from enhanced normalized table
        query = f"""
        SELECT 
            timestamp, system_id, ac_power,
            {', '.join(self.all_features)}
        FROM normalized_training_data_enhanced_v3
        WHERE data_quality_score > 0.5
        AND feature_completeness > 0.8
        ORDER BY timestamp, system_id
        """
        
        df = pd.read_sql(query, conn)
        conn.close()
        
        # Remove any remaining NaN values
        df = df.dropna()
        
        logger.info(f"Loaded {len(df)} high-quality records")
        logger.info(f"System 1: {len(df[df['system_id']==1])} records")
        logger.info(f"System 2: {len(df[df['system_id']==2])} records")
        logger.info(f"Average quality score: {df['data_quality_score'].mean():.3f}")
        
        return df
    
    def prepare_training_data(self, df):
        """Prepare data for training with quality weighting"""
        logger.info("Preparing training data with quality weighting...")
        
        # Features and target
        X = df[self.all_features].copy()
        y = df['ac_power'].copy()
        
        # Quality-based sample weights
        sample_weights = df['data_quality_score'].values
        
        # Time-based split to avoid data leakage
        split_date = df['timestamp'].quantile(0.8)
        train_mask = df['timestamp'] <= split_date
        test_mask = df['timestamp'] > split_date
        
        X_train = X[train_mask]
        X_test = X[test_mask]
        y_train = y[train_mask]
        y_test = y[test_mask]
        weights_train = sample_weights[train_mask]
        weights_test = sample_weights[test_mask]
        
        # Additional validation split
        X_train, X_val, y_train, y_val, weights_train, weights_val = train_test_split(
            X_train, y_train, weights_train, 
            test_size=0.2, random_state=42, shuffle=False
        )
        
        logger.info(f"Training set: {len(X_train)} samples")
        logger.info(f"Validation set: {len(X_val)} samples")
        logger.info(f"Test set: {len(X_test)} samples")
        
        return (X_train, X_val, X_test, y_train, y_val, y_test, 
                weights_train, weights_val, weights_test)
    
    def train_algorithms(self, X_train, X_val, X_test, y_train, y_val, y_test, 
                        weights_train, weights_val, weights_test):
        """Train multiple algorithms with quality weighting"""
        logger.info("Training multiple algorithms with quality weighting...")
        
        algorithms = []
        
        # 1. XGBoost with quality weighting
        if XGBOOST_AVAILABLE:
            xgb_model = xgb.XGBRegressor(
                n_estimators=200,
                max_depth=8,
                learning_rate=0.1,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=42,
                n_jobs=-1
            )
            algorithms.append(('XGBoost', xgb_model))
        
        # 2. LightGBM with quality weighting
        if LIGHTGBM_AVAILABLE:
            lgb_model = lgb.LGBMRegressor(
                n_estimators=200,
                max_depth=8,
                learning_rate=0.1,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=42,
                verbose=-1
            )
            algorithms.append(('LightGBM', lgb_model))
        
        # 3. CatBoost with quality weighting
        if CATBOOST_AVAILABLE:
            cat_model = cb.CatBoostRegressor(
                iterations=200,
                depth=8,
                learning_rate=0.1,
                random_seed=42,
                verbose=False
            )
            algorithms.append(('CatBoost', cat_model))
        
        # 4. Random Forest
        rf_model = RandomForestRegressor(
            n_estimators=200,
            max_depth=15,
            random_state=42,
            n_jobs=-1
        )
        algorithms.append(('Random Forest', rf_model))
        
        # Train all algorithms
        for name, model in algorithms:
            logger.info(f"Training {name}...")
            
            start_time = time.time()
            
            # Train with sample weights
            if hasattr(model, 'fit') and 'sample_weight' in model.fit.__code__.co_varnames:
                model.fit(X_train, y_train, sample_weight=weights_train)
            else:
                model.fit(X_train, y_train)
            
            training_time = time.time() - start_time
            
            # Evaluate
            y_pred_val = model.predict(X_val)
            y_pred_test = model.predict(X_test)
            
            # Calculate metrics
            val_metrics = {
                'rmse': np.sqrt(mean_squared_error(y_val, y_pred_val)),
                'mae': mean_absolute_error(y_val, y_pred_val),
                'r2': r2_score(y_val, y_pred_val)
            }
            
            test_metrics = {
                'rmse': np.sqrt(mean_squared_error(y_test, y_pred_test)),
                'mae': mean_absolute_error(y_test, y_pred_test),
                'r2': r2_score(y_test, y_pred_test)
            }
            
            # System-specific evaluation
            system_metrics = {}
            for system_id in [1, 2]:
                # Get system-specific test data
                system_mask = X_test['consumption_profile_encoded'] == system_id
                if system_mask.sum() > 0:
                    y_test_sys = y_test[system_mask]
                    y_pred_sys = y_pred_test[system_mask]
                    
                    system_metrics[f'system_{system_id}'] = {
                        'rmse': np.sqrt(mean_squared_error(y_test_sys, y_pred_sys)),
                        'mae': mean_absolute_error(y_test_sys, y_pred_sys),
                        'r2': r2_score(y_test_sys, y_pred_sys),
                        'samples': len(y_test_sys)
                    }
            
            # Store results
            self.models[name] = model
            self.results[name] = {
                'validation_metrics': val_metrics,
                'test_metrics': test_metrics,
                'system_metrics': system_metrics,
                'training_time': training_time,
                'feature_importance': self.get_feature_importance(model, name)
            }
            
            logger.info(f"{name} - Test R²: {test_metrics['r2']:.3f}, RMSE: {test_metrics['rmse']:.1f}W")
        
        return self.models, self.results
    
    def get_feature_importance(self, model, model_name):
        """Extract feature importance from model"""
        try:
            if hasattr(model, 'feature_importances_'):
                importance = model.feature_importances_
            elif hasattr(model, 'get_feature_importance'):
                importance = model.get_feature_importance()
            else:
                return None
            
            # Create importance dictionary
            feature_importance = dict(zip(self.all_features, importance))
            return feature_importance
            
        except Exception as e:
            logger.warning(f"Could not extract feature importance for {model_name}: {e}")
            return None
    
    def create_ensemble(self, X_train, y_train, weights_train):
        """Create ensemble of best models"""
        logger.info("Creating ensemble model...")
        
        # Select best models based on validation R²
        best_models = []
        for name, result in self.results.items():
            if result['test_metrics']['r2'] > 0.7:  # Only good models
                best_models.append((name, self.models[name]))
        
        if len(best_models) >= 2:
            # Create voting regressor
            ensemble = VotingRegressor(best_models)
            
            # Train ensemble
            if hasattr(ensemble, 'fit') and 'sample_weight' in ensemble.fit.__code__.co_varnames:
                ensemble.fit(X_train, y_train, sample_weight=weights_train)
            else:
                ensemble.fit(X_train, y_train)
            
            self.models['Ensemble'] = ensemble
            logger.info(f"Ensemble created with {len(best_models)} models: {[name for name, _ in best_models]}")
            
            return ensemble
        else:
            logger.warning("Not enough good models for ensemble")
            return None
    
    def evaluate_ensemble(self, ensemble, X_val, X_test, y_val, y_test):
        """Evaluate ensemble model"""
        if ensemble is None:
            return
        
        logger.info("Evaluating ensemble model...")
        
        # Predictions
        y_pred_val = ensemble.predict(X_val)
        y_pred_test = ensemble.predict(X_test)
        
        # Metrics
        val_metrics = {
            'rmse': np.sqrt(mean_squared_error(y_val, y_pred_val)),
            'mae': mean_absolute_error(y_val, y_pred_val),
            'r2': r2_score(y_val, y_pred_val)
        }
        
        test_metrics = {
            'rmse': np.sqrt(mean_squared_error(y_test, y_pred_test)),
            'mae': mean_absolute_error(y_test, y_pred_test),
            'r2': r2_score(y_test, y_pred_test)
        }
        
        # System-specific evaluation
        system_metrics = {}
        for system_id in [1, 2]:
            system_mask = X_test['consumption_profile_encoded'] == system_id
            if system_mask.sum() > 0:
                y_test_sys = y_test[system_mask]
                y_pred_sys = y_pred_test[system_mask]
                
                system_metrics[f'system_{system_id}'] = {
                    'rmse': np.sqrt(mean_squared_error(y_test_sys, y_pred_sys)),
                    'mae': mean_absolute_error(y_test_sys, y_pred_sys),
                    'r2': r2_score(y_test_sys, y_pred_sys),
                    'samples': len(y_test_sys)
                }
        
        # Store ensemble results
        self.results['Ensemble'] = {
            'validation_metrics': val_metrics,
            'test_metrics': test_metrics,
            'system_metrics': system_metrics,
            'training_time': 0,  # Ensemble doesn't have separate training time
            'feature_importance': None
        }
        
        logger.info(f"Ensemble - Test R²: {test_metrics['r2']:.3f}, RMSE: {test_metrics['rmse']:.1f}W")
    
    def analyze_results(self):
        """Analyze and rank results"""
        logger.info("Analyzing results...")
        
        # Create comparison DataFrame
        comparison_data = []
        for name, result in self.results.items():
            test_metrics = result['test_metrics']
            comparison_data.append({
                'Algorithm': name,
                'Test_R2': test_metrics['r2'],
                'Test_RMSE': test_metrics['rmse'],
                'Test_MAE': test_metrics['mae'],
                'Training_Time': result['training_time'],
                'System1_R2': result['system_metrics'].get('system_1', {}).get('r2', 0),
                'System2_R2': result['system_metrics'].get('system_2', {}).get('r2', 0)
            })
        
        df_results = pd.DataFrame(comparison_data)
        df_results = df_results.sort_values('Test_R2', ascending=False)
        
        # Display results
        logger.info("\n" + "="*100)
        logger.info("🏆 ENHANCED MODEL V3 WITH QUALITY DATA - RESULTS")
        logger.info("="*100)
        
        for _, row in df_results.iterrows():
            logger.info(f"{row['Algorithm']:15} | R²: {row['Test_R2']:.3f} | "
                       f"RMSE: {row['Test_RMSE']:6.1f}W | "
                       f"MAE: {row['Test_MAE']:6.1f}W | "
                       f"S1_R²: {row['System1_R2']:.3f} | "
                       f"S2_R²: {row['System2_R2']:.3f}")
        
        # Find best model
        best_model_name = df_results.iloc[0]['Algorithm']
        best_metrics = self.results[best_model_name]['test_metrics']
        
        logger.info(f"\n🥇 BEST ALGORITHM: {best_model_name}")
        logger.info(f"   Test R²: {best_metrics['r2']:.3f}")
        logger.info(f"   Test RMSE: {best_metrics['rmse']:.1f}W")
        logger.info(f"   System 1 R²: {self.results[best_model_name]['system_metrics'].get('system_1', {}).get('r2', 0):.3f}")
        logger.info(f"   System 2 R²: {self.results[best_model_name]['system_metrics'].get('system_2', {}).get('r2', 0):.3f}")
        
        return df_results, best_model_name
    
    def save_best_model(self, best_model_name):
        """Save the best performing model"""
        logger.info(f"Saving best model: {best_model_name}")
        
        best_model = self.models[best_model_name]
        
        # Save model
        model_path = self.output_dir / f"enhanced_v3_quality_{best_model_name.lower().replace(' ', '_')}.joblib"
        joblib.dump(best_model, model_path)
        
        # Save features
        features_path = self.output_dir / "feature_columns_quality.json"
        with open(features_path, 'w') as f:
            json.dump(self.all_features, f, indent=2)
        
        # Save feature sets
        feature_sets_path = self.output_dir / "feature_sets_quality.json"
        with open(feature_sets_path, 'w') as f:
            json.dump(self.feature_sets, f, indent=2)
        
        # Save results
        results_path = self.output_dir / "training_results_quality.json"
        serializable_results = {}
        for name, result in self.results.items():
            serializable_results[name] = {
                'validation_metrics': result['validation_metrics'],
                'test_metrics': result['test_metrics'],
                'system_metrics': result['system_metrics'],
                'training_time': result['training_time']
            }
        
        with open(results_path, 'w') as f:
            json.dump(serializable_results, f, indent=2, default=str)
        
        logger.info(f"✅ Best model saved: {model_path}")
        return str(model_path)

def main():
    """Main training function"""
    logger.info("🚀 ENHANCED MODEL V3 TRAINING WITH QUALITY DATA")
    logger.info("="*80)
    
    # Check available libraries
    logger.info("📦 Available libraries:")
    logger.info(f"   XGBoost: {'✅' if XGBOOST_AVAILABLE else '❌'}")
    logger.info(f"   LightGBM: {'✅' if LIGHTGBM_AVAILABLE else '❌'}")
    logger.info(f"   CatBoost: {'✅' if CATBOOST_AVAILABLE else '❌'}")
    
    try:
        # Initialize trainer
        trainer = EnhancedModelV3Trainer()
        
        # Load quality data
        df = trainer.load_quality_data()
        
        if len(df) < 1000:
            logger.error(f"❌ Insufficient quality data: {len(df)} records")
            return False
        
        # Prepare training data
        (X_train, X_val, X_test, y_train, y_val, y_test, 
         weights_train, weights_val, weights_test) = trainer.prepare_training_data(df)
        
        # Train algorithms
        models, results = trainer.train_algorithms(
            X_train, X_val, X_test, y_train, y_val, y_test,
            weights_train, weights_val, weights_test
        )
        
        # Create and evaluate ensemble
        ensemble = trainer.create_ensemble(X_train, y_train, weights_train)
        if ensemble:
            trainer.evaluate_ensemble(ensemble, X_val, X_test, y_val, y_test)
        
        # Analyze results
        df_results, best_model_name = trainer.analyze_results()
        
        # Save best model
        model_path = trainer.save_best_model(best_model_name)
        
        logger.info("\n" + "="*80)
        logger.info("🎯 ENHANCED MODEL V3 WITH QUALITY DATA COMPLETE!")
        logger.info(f"🏆 Best Algorithm: {best_model_name}")
        logger.info(f"💾 Model saved: {model_path}")
        logger.info("🚀 Enhanced Model v3 with quality data ready for production!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Training failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
