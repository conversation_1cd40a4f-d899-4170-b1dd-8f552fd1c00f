#!/usr/bin/env python3
"""
Master Training Script με Unified Preprocessing Pipeline
=======================================================

Εκτελεί εκπαίδευση όλων των 16 μοντέλων με το unified preprocessing pipeline:
- 8 Multi-horizon μοντέλα (hourly, daily, monthly, yearly × 2 systems)
- 8 Seasonal μοντέλα (spring, summer, autumn, winter × 2 systems)

Δημιουργήθηκε: 2025-06-05
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))

import argparse
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, Any
import json

# Import unified training modules
from scripts.training.train_yield_based_models_unified import UnifiedYieldBasedTrainer
from scripts.training.seasonal_models_trainer_unified import UnifiedSeasonalModelsTrainer

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MasterUnifiedTrainer:
    """
    Master trainer που εκτελεί όλα τα training workflows με unified pipeline
    """
    
    def __init__(self, output_dir: str = "models", pipeline_version: str = "v1.0.0"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.pipeline_version = pipeline_version
        
        # Initialize trainers
        self.multi_horizon_trainer = UnifiedYieldBasedTrainer(
            output_dir=str(self.output_dir),
            pipeline_version=pipeline_version
        )
        
        self.seasonal_trainer = UnifiedSeasonalModelsTrainer(
            output_dir=str(self.output_dir / "seasonal_models"),
            pipeline_version=pipeline_version
        )
        
        logger.info(f"🏗️ Initialized MasterUnifiedTrainer with pipeline {pipeline_version}")
    
    def train_all_models(self, train_multi_horizon: bool = True, train_seasonal: bool = True) -> Dict[str, Any]:
        """Εκπαίδευση όλων των μοντέλων"""
        
        logger.info("🚀 STARTING MASTER UNIFIED TRAINING")
        logger.info("=" * 100)
        logger.info("Target: 16 solar prediction models with unified preprocessing")
        logger.info("Multi-horizon: 8 models (4 horizons × 2 systems)")
        logger.info("Seasonal: 8 models (4 seasons × 2 systems)")
        logger.info(f"Pipeline version: {self.pipeline_version}")
        logger.info("=" * 100)
        
        master_results = {
            'training_start': datetime.now().isoformat(),
            'pipeline_version': self.pipeline_version,
            'total_target_models': 16,
            'multi_horizon_results': None,
            'seasonal_results': None,
            'overall_summary': {}
        }
        
        # Train multi-horizon models
        if train_multi_horizon:
            logger.info("\n🎯 PHASE 1: MULTI-HORIZON MODELS TRAINING")
            logger.info("=" * 60)
            
            try:
                multi_horizon_results = self.multi_horizon_trainer.train_all_models()
                master_results['multi_horizon_results'] = multi_horizon_results
                
                logger.info("✅ Multi-horizon training completed successfully!")
                
            except Exception as e:
                logger.error(f"❌ Multi-horizon training failed: {e}")
                master_results['multi_horizon_error'] = str(e)
        
        # Train seasonal models
        if train_seasonal:
            logger.info("\n🌿 PHASE 2: SEASONAL MODELS TRAINING")
            logger.info("=" * 60)
            
            try:
                seasonal_results = self.seasonal_trainer.train_all_seasonal_models()
                master_results['seasonal_results'] = seasonal_results
                
                logger.info("✅ Seasonal training completed successfully!")
                
            except Exception as e:
                logger.error(f"❌ Seasonal training failed: {e}")
                master_results['seasonal_error'] = str(e)
        
        # Generate overall summary
        master_results['training_end'] = datetime.now().isoformat()
        self.generate_master_summary(master_results)
        
        return master_results
    
    def generate_master_summary(self, results: Dict[str, Any]):
        """Generate comprehensive master training summary"""
        
        logger.info("\n🎉 MASTER UNIFIED TRAINING COMPLETED!")
        logger.info("=" * 100)
        
        # Calculate overall statistics
        total_successful = 0
        total_target_achieved = 0
        total_attempted = 0
        
        # Multi-horizon statistics
        if results['multi_horizon_results']:
            mh_results = results['multi_horizon_results']
            mh_successful = mh_results.get('successful_models', 0)
            mh_target_achieved = mh_results.get('target_achieved', 0)
            mh_attempted = mh_results.get('total_models', 8)
            
            total_successful += mh_successful
            total_target_achieved += mh_target_achieved
            total_attempted += mh_attempted
            
            logger.info(f"🎯 MULTI-HORIZON RESULTS:")
            logger.info(f"   Successful: {mh_successful}/{mh_attempted} ({mh_successful/mh_attempted*100:.1f}%)")
            logger.info(f"   Target achieved: {mh_target_achieved}/{mh_successful} ({mh_target_achieved/mh_successful*100:.1f}%)")
        else:
            logger.info(f"🎯 MULTI-HORIZON: ❌ FAILED OR SKIPPED")
        
        # Seasonal statistics
        if results['seasonal_results']:
            seasonal_results = results['seasonal_results']
            seasonal_successful = seasonal_results.get('successful_models', 0)
            seasonal_target_achieved = seasonal_results.get('target_achieved', 0)
            seasonal_attempted = seasonal_results.get('total_models', 8)
            
            total_successful += seasonal_successful
            total_target_achieved += seasonal_target_achieved
            total_attempted += seasonal_attempted
            
            logger.info(f"🌿 SEASONAL RESULTS:")
            logger.info(f"   Successful: {seasonal_successful}/{seasonal_attempted} ({seasonal_successful/seasonal_attempted*100:.1f}%)")
            logger.info(f"   Target achieved: {seasonal_target_achieved}/{seasonal_successful} ({seasonal_target_achieved/seasonal_successful*100:.1f}%)")
        else:
            logger.info(f"🌿 SEASONAL: ❌ FAILED OR SKIPPED")
        
        # Overall statistics
        logger.info(f"\n📊 OVERALL RESULTS:")
        logger.info(f"   Total successful: {total_successful}/{total_attempted} ({total_successful/total_attempted*100:.1f}%)")
        logger.info(f"   Total target achieved: {total_target_achieved}/{total_successful} ({total_target_achieved/total_successful*100:.1f}%)")
        logger.info(f"   Pipeline version: {results['pipeline_version']}")
        
        # Training duration
        start_time = datetime.fromisoformat(results['training_start'])
        end_time = datetime.fromisoformat(results['training_end'])
        duration = end_time - start_time
        logger.info(f"   Training duration: {duration}")
        
        # Update results with summary
        results['overall_summary'] = {
            'total_successful': total_successful,
            'total_attempted': total_attempted,
            'total_target_achieved': total_target_achieved,
            'success_rate': total_successful / total_attempted if total_attempted > 0 else 0,
            'target_achievement_rate': total_target_achieved / total_successful if total_successful > 0 else 0,
            'training_duration_seconds': duration.total_seconds()
        }
        
        # Save master summary
        summary_path = self.output_dir / "master_unified_training_summary.json"
        with open(summary_path, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        logger.info(f"\n💾 Master training summary saved: {summary_path}")
        
        # Final status
        if total_successful == 16:
            logger.info("🎉 ALL 16 MODELS TRAINED SUCCESSFULLY!")
        elif total_successful >= 12:
            logger.info(f"✅ MOSTLY SUCCESSFUL: {total_successful}/16 models trained")
        elif total_successful >= 8:
            logger.info(f"⚠️ PARTIAL SUCCESS: {total_successful}/16 models trained")
        else:
            logger.info(f"❌ TRAINING ISSUES: Only {total_successful}/16 models trained")
    
    def validate_models(self) -> Dict[str, Any]:
        """Validate όλα τα trained μοντέλα"""
        logger.info("\n🔍 VALIDATING TRAINED MODELS")
        logger.info("=" * 50)
        
        validation_results = {
            'multi_horizon_models': {},
            'seasonal_models': {},
            'total_valid_models': 0,
            'validation_issues': []
        }
        
        # Validate multi-horizon models
        for system_id in [1, 2]:
            for horizon in ['hourly', 'daily', 'monthly', 'yearly']:
                model_dir = self.output_dir / f"multi_horizon_{horizon}_system{system_id}"
                
                if model_dir.exists():
                    model_valid = (
                        (model_dir / "model.joblib").exists() and
                        (model_dir / "scaler.joblib").exists() and
                        (model_dir / "metadata.json").exists()
                    )
                    
                    validation_results['multi_horizon_models'][f"{horizon}_system{system_id}"] = model_valid
                    
                    if model_valid:
                        validation_results['total_valid_models'] += 1
                    else:
                        validation_results['validation_issues'].append(f"Multi-horizon {horizon} system {system_id} incomplete")
        
        # Validate seasonal models
        for system_id in [1, 2]:
            for season in ['spring', 'summer', 'autumn', 'winter']:
                model_dir = self.output_dir / "seasonal_models" / f"{season}_system{system_id}"
                
                if model_dir.exists():
                    model_valid = (
                        (model_dir / "model.joblib").exists() and
                        (model_dir / "scaler.joblib").exists() and
                        (model_dir / "metadata.json").exists()
                    )
                    
                    validation_results['seasonal_models'][f"{season}_system{system_id}"] = model_valid
                    
                    if model_valid:
                        validation_results['total_valid_models'] += 1
                    else:
                        validation_results['validation_issues'].append(f"Seasonal {season} system {system_id} incomplete")
        
        logger.info(f"📊 Validation results: {validation_results['total_valid_models']}/16 models valid")
        
        if validation_results['validation_issues']:
            logger.warning("⚠️ Validation issues found:")
            for issue in validation_results['validation_issues']:
                logger.warning(f"   - {issue}")
        else:
            logger.info("✅ All models passed validation!")
        
        return validation_results

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Master Unified Training Script")
    parser.add_argument("--output-dir", default="models", help="Output directory for models")
    parser.add_argument("--pipeline-version", default="v1.0.0", help="Pipeline version")
    parser.add_argument("--skip-multi-horizon", action="store_true", help="Skip multi-horizon training")
    parser.add_argument("--skip-seasonal", action="store_true", help="Skip seasonal training")
    parser.add_argument("--validate-only", action="store_true", help="Only validate existing models")
    
    args = parser.parse_args()
    
    try:
        # Initialize master trainer
        trainer = MasterUnifiedTrainer(
            output_dir=args.output_dir,
            pipeline_version=args.pipeline_version
        )
        
        if args.validate_only:
            # Only validate existing models
            validation_results = trainer.validate_models()
            return validation_results
        else:
            # Train models
            results = trainer.train_all_models(
                train_multi_horizon=not args.skip_multi_horizon,
                train_seasonal=not args.skip_seasonal
            )
            
            # Validate trained models
            validation_results = trainer.validate_models()
            results['validation_results'] = validation_results
            
            logger.info("\n✅ MASTER UNIFIED TRAINING COMPLETED!")
            return results
        
    except Exception as e:
        logger.error(f"❌ Master training failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()
