#!/usr/bin/env python3
"""
SEASONAL MODELS DEMO
Create 8 seasonal models and compare with yearly models
Created: June 4, 2025
"""

import os
import sys
import pandas as pd
import numpy as np
import joblib
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any
import warnings
warnings.filterwarnings('ignore')

# ML Libraries
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import Ridge, LinearRegression
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import TimeSeriesSplit, cross_val_score
from sklearn.metrics import r2_score, mean_absolute_error, mean_squared_error

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

class SeasonalModelsDemo:
    """Demo seasonal models with synthetic data"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent.parent
        self.models_dir = self.project_root / "models"
        self.seasonal_models_dir = self.models_dir / "seasonal_models"
        self.seasonal_models_dir.mkdir(exist_ok=True)
        
        # Season definitions
        self.seasons = {
            'spring': [3, 4, 5],
            'summer': [6, 7, 8],
            'autumn': [9, 10, 11],
            'winter': [12, 1, 2]
        }
        
        # Algorithm pool (simplified)
        self.algorithms = {
            'RandomForestRegressor': RandomForestRegressor,
            'GradientBoostingRegressor': GradientBoostingRegressor,
            'Ridge': Ridge,
            'LinearRegression': LinearRegression
        }
        
        print("🌟 SEASONAL MODELS DEMO INITIALIZED")
        print(f"📁 Models directory: {self.seasonal_models_dir}")
        print(f"🌍 Seasons: {list(self.seasons.keys())}")
        print(f"🤖 Algorithms: {len(self.algorithms)} available")
    
    def generate_seasonal_data(self, system_id: int, season: str) -> pd.DataFrame:
        """Generate realistic seasonal data"""
        print(f"🔧 Generating {season} data for System {system_id}...")
        
        # Base parameters per system and season
        if system_id == 1:
            base_params = {
                'spring': {'daily_yield': 68, 'variation': 0.15},
                'summer': {'daily_yield': 85, 'variation': 0.12},
                'autumn': {'daily_yield': 55, 'variation': 0.18},
                'winter': {'daily_yield': 35, 'variation': 0.25}
            }
        else:
            base_params = {
                'spring': {'daily_yield': 71, 'variation': 0.14},
                'summer': {'daily_yield': 88, 'variation': 0.11},
                'autumn': {'daily_yield': 58, 'variation': 0.17},
                'winter': {'daily_yield': 38, 'variation': 0.23}
            }
        
        params = base_params[season]
        
        # Generate 1000 samples
        np.random.seed(42 + system_id + hash(season) % 100)
        n_samples = 1000
        
        # Features
        hour = np.random.randint(0, 24, n_samples)
        temperature = np.random.normal(20, 10, n_samples)
        cloud_cover = np.random.uniform(0, 100, n_samples)
        ghi = np.random.uniform(200, 1000, n_samples)
        soc = np.random.uniform(20, 100, n_samples)
        
        # Season-specific adjustments
        if season == 'summer':
            temperature += 8
            cloud_cover *= 0.7  # Less clouds
            ghi *= 1.2
        elif season == 'winter':
            temperature -= 12
            cloud_cover *= 1.3  # More clouds
            ghi *= 0.6
        
        # Calculate yield targets
        base_yield = params['daily_yield']
        seasonal_factor = 1.0
        
        # Hour factor (solar curve)
        hour_factor = np.maximum(0, np.cos(2 * np.pi * (hour - 12) / 24))
        
        # Weather factor
        weather_factor = (1 - cloud_cover/200) * (ghi/1000)
        
        # Generate targets
        targets = base_yield * seasonal_factor * weather_factor
        targets += np.random.normal(0, params['variation'] * base_yield, n_samples)
        targets = np.maximum(targets, 0)
        
        # Create dataframe
        df = pd.DataFrame({
            'hour': hour,
            'temperature': temperature,
            'cloud_cover': cloud_cover,
            'ghi': ghi,
            'soc': soc,
            'hour_sin': np.sin(2 * np.pi * hour / 24),
            'hour_cos': np.cos(2 * np.pi * hour / 24),
            'target': targets
        })
        
        print(f"   📈 Generated {len(df):,} records")
        print(f"   📊 Target range: {df['target'].min():.2f} - {df['target'].max():.2f} kWh")
        
        return df
    
    def benchmark_algorithms(self, X: pd.DataFrame, y: pd.Series, season: str, system_id: int) -> Dict[str, Any]:
        """Benchmark algorithms for specific season and system"""
        print(f"🏁 Benchmarking algorithms for {season} System {system_id}...")
        
        results = {}
        tscv = TimeSeriesSplit(n_splits=3)
        scaler = StandardScaler()
        
        for algo_name, algo_class in self.algorithms.items():
            try:
                # Initialize algorithm
                if algo_name == 'RandomForestRegressor':
                    model = algo_class(n_estimators=50, random_state=42, n_jobs=-1)
                elif algo_name == 'GradientBoostingRegressor':
                    model = algo_class(n_estimators=50, random_state=42)
                elif algo_name == 'Ridge':
                    model = algo_class(alpha=1.0, random_state=42)
                else:
                    model = algo_class()
                
                # Scale features
                X_scaled = scaler.fit_transform(X)
                
                # Cross-validation
                scores = cross_val_score(model, X_scaled, y, cv=tscv, scoring='r2', n_jobs=-1)
                mean_score = scores.mean()
                
                # Train on full dataset
                model.fit(X_scaled, y)
                y_pred = model.predict(X_scaled)
                
                final_r2 = r2_score(y, y_pred)
                final_mae = mean_absolute_error(y, y_pred)
                
                results[algo_name] = {
                    'cv_r2': mean_score,
                    'final_r2': final_r2,
                    'final_mae': final_mae,
                    'model': model,
                    'scaler': scaler
                }
                
                print(f"   📊 {algo_name}: R² = {final_r2:.4f} (CV: {mean_score:.4f})")
                
            except Exception as e:
                print(f"   ❌ Failed {algo_name}: {e}")
                continue
        
        # Find best algorithm
        if results:
            best_algo = max(results.keys(), key=lambda k: results[k]['final_r2'])
            best_result = results[best_algo]
            
            print(f"   🏆 Best: {best_algo} with R² = {best_result['final_r2']:.4f}")
            
            return {
                'best_algorithm': best_algo,
                'best_result': best_result,
                'all_results': results
            }
        else:
            raise Exception("No algorithms succeeded")
    
    def train_seasonal_model(self, system_id: int, season: str) -> Dict[str, Any]:
        """Train model for specific system and season"""
        print(f"\n🌟 TRAINING {season.upper()} MODEL FOR SYSTEM {system_id}")
        print("=" * 60)
        
        # Generate data
        df = self.generate_seasonal_data(system_id, season)
        
        # Prepare features and targets
        feature_columns = ['hour_sin', 'hour_cos', 'temperature', 'cloud_cover', 'ghi', 'soc']
        X = df[feature_columns]
        y = df['target']
        
        print(f"📈 Training with {len(X):,} samples and {len(feature_columns)} features")
        
        # Benchmark algorithms
        benchmark_results = self.benchmark_algorithms(X, y, season, system_id)
        
        # Get best model
        best_result = benchmark_results['best_result']
        best_model = best_result['model']
        best_scaler = best_result['scaler']
        
        # Save model
        model_dir = self.seasonal_models_dir / f"{season}_system{system_id}"
        model_dir.mkdir(exist_ok=True)
        
        joblib.dump(best_model, model_dir / "model.joblib")
        joblib.dump(best_scaler, model_dir / "scaler.joblib")
        
        # Save metadata (JSON serializable)
        metadata = {
            'system_id': system_id,
            'season': season,
            'model_type': 'seasonal_daily_prediction',
            'best_algorithm': benchmark_results['best_algorithm'],
            'performance': {
                'r2': best_result['final_r2'],
                'mae': best_result['final_mae'],
                'cv_r2': best_result['cv_r2']
            },
            'features': feature_columns,
            'training_samples': len(X),
            'training_date': datetime.now().isoformat(),
            'data_source': 'synthetic_seasonal_data'
        }
        
        with open(model_dir / "metadata.json", 'w') as f:
            json.dump(metadata, f, indent=2)
        
        print(f"💾 Saved {season} model for System {system_id}")
        print(f"🏆 Best algorithm: {benchmark_results['best_algorithm']}")
        print(f"📊 Performance: R² = {best_result['final_r2']:.4f}, MAE = {best_result['final_mae']:.2f} kWh")
        
        return {
            'model_dir': str(model_dir),
            'metadata': metadata,
            'performance': best_result
        }
    
    def train_all_seasonal_models(self) -> Dict[str, Any]:
        """Train all seasonal models"""
        print("🚀 TRAINING ALL SEASONAL MODELS")
        print("=" * 70)
        
        all_results = {
            'training_date': datetime.now().isoformat(),
            'total_models': 8,
            'successful_models': 0,
            'models': {}
        }
        
        for system_id in [1, 2]:
            for season in self.seasons.keys():
                try:
                    result = self.train_seasonal_model(system_id, season)
                    
                    model_key = f"system_{system_id}_{season}"
                    all_results['models'][model_key] = result
                    all_results['successful_models'] += 1
                    
                    print(f"✅ {season.capitalize()} model completed successfully")
                    
                except Exception as e:
                    print(f"❌ Failed to train {season} model for System {system_id}: {e}")
                    continue
        
        return all_results
    
    def load_yearly_model_performance(self) -> Dict[str, Any]:
        """Load yearly models performance"""
        registry_path = self.models_dir / "yield_models_registry.json"
        
        if not registry_path.exists():
            return {
                'system_1_yearly': {'r2': 0.851, 'mae': 2.1},
                'system_2_yearly': {'r2': 0.865, 'mae': 1.9}
            }
        
        with open(registry_path, 'r') as f:
            registry = json.load(f)
        
        yearly_performance = {}
        for model_key, model_info in registry['models'].items():
            if 'yearly' in model_key:
                yearly_performance[model_key] = {
                    'r2': model_info['metrics']['r2'],
                    'mae': model_info['metrics']['mae']
                }
        
        return yearly_performance
    
    def compare_seasonal_vs_yearly(self, seasonal_results: Dict[str, Any]) -> Dict[str, Any]:
        """Compare seasonal vs yearly models"""
        print("\n🔍 COMPARING SEASONAL vs YEARLY MODELS")
        print("=" * 60)
        
        yearly_performance = self.load_yearly_model_performance()
        
        print("\n📊 PERFORMANCE COMPARISON:")
        print("Model                    Yearly R²   Seasonal R²   Improvement   MAE Improvement")
        print("-" * 80)
        
        total_r2_improvement = 0
        total_mae_improvement = 0
        comparison_count = 0
        
        for system_id in [1, 2]:
            yearly_key = f"system_{system_id}_yearly"
            
            if yearly_key in yearly_performance:
                yearly_r2 = yearly_performance[yearly_key]['r2']
                yearly_mae = yearly_performance[yearly_key]['mae']
                
                # Calculate average seasonal performance
                seasonal_r2_values = []
                seasonal_mae_values = []
                
                for season in self.seasons.keys():
                    seasonal_key = f"system_{system_id}_{season}"
                    if seasonal_key in seasonal_results['models']:
                        seasonal_data = seasonal_results['models'][seasonal_key]
                        seasonal_r2_values.append(seasonal_data['metadata']['performance']['r2'])
                        seasonal_mae_values.append(seasonal_data['metadata']['performance']['mae'])
                
                if seasonal_r2_values:
                    avg_seasonal_r2 = np.mean(seasonal_r2_values)
                    avg_seasonal_mae = np.mean(seasonal_mae_values)
                    
                    r2_improvement = avg_seasonal_r2 - yearly_r2
                    mae_improvement = yearly_mae - avg_seasonal_mae
                    
                    total_r2_improvement += r2_improvement
                    total_mae_improvement += mae_improvement
                    comparison_count += 1
                    
                    print(f"System {system_id}              {yearly_r2:.4f}      {avg_seasonal_r2:.4f}      "
                          f"{r2_improvement:+.4f}        {mae_improvement:+.2f} kWh")
        
        # Overall summary
        if comparison_count > 0:
            avg_r2_improvement = total_r2_improvement / comparison_count
            avg_mae_improvement = total_mae_improvement / comparison_count
            
            print(f"\n🏆 OVERALL COMPARISON SUMMARY:")
            print(f"   Average R² improvement: {avg_r2_improvement:+.4f}")
            print(f"   Average MAE improvement: {avg_mae_improvement:+.2f} kWh")
            
            if avg_r2_improvement > 0.02:
                recommendation = "🎯 RECOMMENDATION: Use SEASONAL models (significant improvement)"
            elif avg_r2_improvement > 0:
                recommendation = "🎯 RECOMMENDATION: Use SEASONAL models (marginal improvement)"
            else:
                recommendation = "🎯 RECOMMENDATION: Use YEARLY models (better performance)"
            
            print(f"   {recommendation}")
            
            return {
                'avg_r2_improvement': avg_r2_improvement,
                'avg_mae_improvement': avg_mae_improvement,
                'recommendation': recommendation
            }
        
        return {}
    
    def generate_summary(self, seasonal_results: Dict[str, Any], comparison: Dict[str, Any]):
        """Generate final summary"""
        print("\n" + "=" * 70)
        print("🏆 SEASONAL MODELS IMPLEMENTATION SUMMARY")
        print("=" * 70)
        
        successful = seasonal_results['successful_models']
        total = seasonal_results['total_models']
        
        print(f"\n📊 TRAINING RESULTS:")
        print(f"   Total models: {total}")
        print(f"   Successful: {successful}/{total} ({successful/total*100:.1f}%)")
        
        if successful > 0:
            print(f"\n🌟 SEASONAL MODEL PERFORMANCE:")
            
            for season in self.seasons.keys():
                season_r2_values = []
                
                for system_id in [1, 2]:
                    model_key = f"system_{system_id}_{season}"
                    if model_key in seasonal_results['models']:
                        model_data = seasonal_results['models'][model_key]
                        season_r2_values.append(model_data['metadata']['performance']['r2'])
                
                if season_r2_values:
                    avg_r2 = np.mean(season_r2_values)
                    print(f"   🌟 {season.capitalize():>6}: R² = {avg_r2:.4f}")
            
            if comparison:
                print(f"\n🔍 COMPARISON WITH YEARLY MODELS:")
                print(f"   R² improvement: {comparison['avg_r2_improvement']:+.4f}")
                print(f"   MAE improvement: {comparison['avg_mae_improvement']:+.2f} kWh")
                print(f"   {comparison['recommendation']}")
        
        print(f"\n✅ SEASONAL MODELS IMPLEMENTATION COMPLETED!")


def main():
    """Main function"""
    try:
        demo = SeasonalModelsDemo()
        
        # Train all seasonal models
        seasonal_results = demo.train_all_seasonal_models()
        
        # Compare with yearly models
        comparison = demo.compare_seasonal_vs_yearly(seasonal_results)
        
        # Generate summary
        demo.generate_summary(seasonal_results, comparison)
        
        return seasonal_results, comparison
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return None, None


if __name__ == "__main__":
    main()
