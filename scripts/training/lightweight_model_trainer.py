#!/usr/bin/env python3
"""
Lightweight Model Trainer
Fast training with simplified data loading for immediate results
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import joblib
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import r2_score, mean_absolute_error, mean_squared_error
import psycopg2

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class LightweightModelTrainer:
    """Lightweight model trainer for quick results"""
    
    def __init__(self):
        self.model = None
        self.scaler = None
        self.features = []
        
    def load_recent_data(self, days_back: int = 30) -> pd.DataFrame:
        """Load recent data with simple queries"""
        
        logger.info(f"Loading last {days_back} days of data...")
        
        try:
            conn = psycopg2.connect(
                host='localhost',
                database='solar_prediction',
                user='postgres',
                password='postgres'
            )
            
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days_back)
            
            # Simple query - just solar data with basic weather
            query = """
            SELECT 
                s.timestamp,
                s.ac_power,
                s.yield_today,
                s.soc,
                EXTRACT(hour FROM s.timestamp) as hour,
                EXTRACT(dow FROM s.timestamp) as day_of_week,
                EXTRACT(month FROM s.timestamp) as month
            FROM solax_data s
            WHERE s.timestamp >= %s 
            AND s.timestamp <= %s
            AND s.ac_power >= 0
            AND s.ac_power IS NOT NULL
            ORDER BY s.timestamp
            """
            
            logger.info("Executing solar data query...")
            df = pd.read_sql(query, conn, params=[start_date, end_date])
            
            logger.info(f"Loaded {len(df)} solar records")
            
            # Try to add weather data if available (separate query)
            try:
                weather_query = """
                SELECT 
                    DATE_TRUNC('hour', timestamp) as hour_timestamp,
                    AVG(shortwave_radiation) as avg_ghi,
                    AVG(temperature_2m) as avg_temp,
                    AVG(cloud_cover) as avg_cloud
                FROM weather_data
                WHERE timestamp >= %s AND timestamp <= %s
                GROUP BY DATE_TRUNC('hour', timestamp)
                """
                
                logger.info("Loading weather data...")
                weather_df = pd.read_sql(weather_query, conn, params=[start_date, end_date])
                logger.info(f"Loaded {len(weather_df)} weather records")
                
                # Merge with solar data
                df['hour_timestamp'] = df['timestamp'].dt.floor('H')
                df = df.merge(weather_df, on='hour_timestamp', how='left')
                
            except Exception as e:
                logger.warning(f"Could not load weather data: {e}")
                # Add dummy weather columns
                df['avg_ghi'] = 500  # Default GHI
                df['avg_temp'] = 20  # Default temperature
                df['avg_cloud'] = 50  # Default cloud cover
            
            conn.close()
            
            logger.info(f"Final dataset: {len(df)} records with {len(df.columns)} columns")
            return df
            
        except Exception as e:
            logger.error(f"Error loading data: {e}")
            return pd.DataFrame()
    
    def engineer_simple_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create simple but effective features"""
        
        logger.info("Engineering simple features...")
        
        # Fill missing weather data
        df['avg_ghi'] = df['avg_ghi'].fillna(500)
        df['avg_temp'] = df['avg_temp'].fillna(20)
        df['avg_cloud'] = df['avg_cloud'].fillna(50)
        
        # Time-based features
        df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
        df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
        df['is_weekend'] = (df['day_of_week'] >= 5).astype(int)
        
        # Solar elevation (simplified)
        df['solar_elevation_factor'] = np.maximum(0, np.sin(np.radians(
            90 - abs(df['hour'] - 12) * 7.5  # Simplified solar elevation
        )))
        
        # Physics-based features
        df['module_temp_est'] = df['avg_temp'] + (df['avg_ghi'] / 800.0) * 25
        df['temp_efficiency'] = np.maximum(0.7, 1 - 0.004 * (df['module_temp_est'] - 25))
        
        # Weather interaction
        df['ghi_elevation'] = df['avg_ghi'] * df['solar_elevation_factor']
        df['cloud_factor'] = (100 - df['avg_cloud']) / 100
        df['effective_ghi'] = df['avg_ghi'] * df['cloud_factor']
        
        # Rolling features (3-hour window)
        df['ac_power_lag_1h'] = df['ac_power'].shift(1)
        df['ghi_rolling_3h'] = df['avg_ghi'].rolling(window=3, min_periods=1).mean()
        
        logger.info(f"Feature engineering completed: {len(df.columns)} features")
        return df
    
    def prepare_training_data(self, df: pd.DataFrame) -> tuple:
        """Prepare data for training"""
        
        logger.info("Preparing training data...")
        
        # Select features
        feature_columns = [
            'avg_ghi', 'avg_temp', 'avg_cloud', 'soc',
            'hour_sin', 'hour_cos', 'month_sin', 'month_cos', 'is_weekend',
            'solar_elevation_factor', 'temp_efficiency', 'ghi_elevation',
            'cloud_factor', 'effective_ghi', 'ghi_rolling_3h'
        ]
        
        # Filter available features
        available_features = [col for col in feature_columns if col in df.columns]
        logger.info(f"Using {len(available_features)} features")
        
        # Prepare X and y
        X = df[available_features].copy()
        y = df['ac_power'].copy()
        
        # Remove rows with missing target
        valid_mask = y.notna() & (y >= 0)
        X = X[valid_mask]
        y = y[valid_mask]
        
        # Fill missing features
        X = X.fillna(X.median())
        
        logger.info(f"Training data ready: {len(X)} samples")
        return X, y, available_features
    
    def train_model(self, X: pd.DataFrame, y: pd.Series, features: list) -> dict:
        """Train a simple but effective model"""
        
        logger.info("Training Random Forest model...")
        
        # Split data (temporal split)
        split_idx = int(len(X) * 0.8)
        X_train, X_test = X.iloc[:split_idx], X.iloc[split_idx:]
        y_train, y_test = y.iloc[:split_idx], y.iloc[split_idx:]
        
        # Scale features
        self.scaler = StandardScaler()
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # Train model
        self.model = RandomForestRegressor(
            n_estimators=50,  # Reduced for speed
            max_depth=10,
            min_samples_split=5,
            random_state=42,
            n_jobs=-1
        )
        
        self.model.fit(X_train_scaled, y_train)
        self.features = features
        
        # Evaluate
        y_pred_train = self.model.predict(X_train_scaled)
        y_pred_test = self.model.predict(X_test_scaled)
        
        # Metrics
        train_r2 = r2_score(y_train, y_pred_train)
        test_r2 = r2_score(y_test, y_pred_test)
        test_mae = mean_absolute_error(y_test, y_pred_test)
        test_rmse = np.sqrt(mean_squared_error(y_test, y_pred_test))
        
        # Feature importance
        importance = dict(zip(features, self.model.feature_importances_))
        top_features = sorted(importance.items(), key=lambda x: x[1], reverse=True)[:5]
        
        results = {
            'train_r2': train_r2,
            'test_r2': test_r2,
            'test_mae': test_mae,
            'test_rmse': test_rmse,
            'top_features': top_features,
            'training_samples': len(X_train),
            'test_samples': len(X_test)
        }
        
        logger.info(f"Model trained - Test R²: {test_r2:.3f}, MAE: {test_mae:.1f}W")
        return results
    
    def save_model(self, results: dict) -> str:
        """Save the trained model"""
        
        if self.model is None:
            logger.error("No model to save")
            return ""
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        model_dir = f"/home/<USER>/solar-prediction-project/models/lightweight_model_{timestamp}"
        os.makedirs(model_dir, exist_ok=True)
        
        # Save model and scaler
        joblib.dump(self.model, f"{model_dir}/model.joblib")
        joblib.dump(self.scaler, f"{model_dir}/scaler.joblib")
        
        # Save metadata
        metadata = {
            'model_type': 'RandomForest_Lightweight',
            'features': self.features,
            'performance': {
                'test_r2': results['test_r2'],
                'test_mae': results['test_mae'],
                'test_rmse': results['test_rmse']
            },
            'top_features': results['top_features'],
            'training_info': {
                'training_samples': results['training_samples'],
                'test_samples': results['test_samples'],
                'timestamp': timestamp
            }
        }
        
        import json
        with open(f"{model_dir}/metadata.json", 'w') as f:
            json.dump(metadata, f, indent=2, default=str)
        
        logger.info(f"Model saved to: {model_dir}")
        return model_dir


def main():
    """Main execution function"""
    
    print("🚀 Lightweight Model Trainer")
    print("=" * 35)
    print("Fast training with simplified approach")
    print()
    
    # Initialize trainer
    trainer = LightweightModelTrainer()
    
    # Load recent data (30 days for speed)
    print("📊 Loading recent data...")
    df = trainer.load_recent_data(days_back=30)
    
    if df.empty:
        print("❌ No data loaded")
        return False
    
    print(f"✅ Loaded {len(df)} records")
    
    # Engineer features
    print("🔧 Engineering features...")
    df = trainer.engineer_simple_features(df)
    
    # Prepare training data
    print("📋 Preparing training data...")
    X, y, features = trainer.prepare_training_data(df)
    
    if len(X) == 0:
        print("❌ No valid training data")
        return False
    
    print(f"✅ Training data ready: {len(X)} samples, {len(features)} features")
    
    # Train model
    print("🤖 Training model...")
    results = trainer.train_model(X, y, features)
    
    # Display results
    print("\n📊 Training Results:")
    print("=" * 25)
    print(f"Test R²: {results['test_r2']:.3f}")
    print(f"Test MAE: {results['test_mae']:.1f} W")
    print(f"Test RMSE: {results['test_rmse']:.1f} W")
    print(f"Training samples: {results['training_samples']:,}")
    print(f"Test samples: {results['test_samples']:,}")
    
    print("\n🔝 Top Features:")
    for feature, importance in results['top_features']:
        print(f"   {feature}: {importance:.3f}")
    
    # Save model
    print("\n💾 Saving model...")
    model_path = trainer.save_model(results)
    
    if model_path:
        print(f"✅ Model saved to: {model_path}")
        print("\n🎯 Model Performance Grade:")
        
        if results['test_r2'] >= 0.9:
            print("   🏆 Grade A: Excellent (R² ≥ 0.90)")
        elif results['test_r2'] >= 0.8:
            print("   🥈 Grade B: Good (R² ≥ 0.80)")
        elif results['test_r2'] >= 0.7:
            print("   🥉 Grade C: Fair (R² ≥ 0.70)")
        else:
            print("   ⚠️ Grade D: Needs improvement (R² < 0.70)")
        
        return True
    else:
        print("❌ Failed to save model")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
