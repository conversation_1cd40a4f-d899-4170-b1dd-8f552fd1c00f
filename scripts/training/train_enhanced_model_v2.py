#!/usr/bin/env python3
"""
Train the enhanced solar prediction model (Version 2).

This script:
1. Loads data from the normalized_training_data_enhanced table
2. Trains multiple models (XGBoost, LightGBM, Random Forest)
3. Optimizes hyperparameters using Optuna
4. Creates an ensemble model using stacking
5. Evaluates the models and saves the best one

Usage:
    python train_enhanced_model_v2.py [--output_dir=models/enhanced_v2]

Options:
    --output_dir    Directory to save the trained models (default: models/enhanced_v2)
"""

import os
import sys
import logging
import argparse
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
from typing import Dict, List, Tuple, Any, Optional
from sqlalchemy import func
from sqlalchemy.orm import Session
import joblib
import json

# Machine learning libraries
import xgboost as xgb
import lightgbm as lgb
from sklearn.ensemble import RandomForestRegressor, StackingRegressor
from sklearn.linear_model import Ridge
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.base import BaseEstimator, RegressorMixin
import optuna

# Add the project directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Database
from app.db.session import SessionLocal
from app.db.models.weather.normalized_training_data_enhanced import (
    NormalizedTrainingDataEnhanced,
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()],
)
logger = logging.getLogger(__name__)


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Train the enhanced solar prediction model (Version 2)"
    )
    parser.add_argument(
        "--output_dir",
        type=str,
        default="models/enhanced_v2",
        help="Directory to save the trained models (default: models/enhanced_v2)",
    )
    return parser.parse_args()


def load_training_data(db: Session) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
    """
    Load training data from the NormalizedTrainingDataEnhanced table.

    Args:
        db: Database session

    Returns:
        Tuple of (training data, validation data, test data)
    """
    logger.info("Loading training data...")

    # Load training data
    train_data = pd.read_sql(
        db.query(NormalizedTrainingDataEnhanced)
        .filter(NormalizedTrainingDataEnhanced.dataset == "train")
        .statement,
        db.bind,
    )

    # Load validation data
    val_data = pd.read_sql(
        db.query(NormalizedTrainingDataEnhanced)
        .filter(NormalizedTrainingDataEnhanced.dataset == "validation")
        .statement,
        db.bind,
    )

    # Load test data
    test_data = pd.read_sql(
        db.query(NormalizedTrainingDataEnhanced)
        .filter(NormalizedTrainingDataEnhanced.dataset == "test")
        .statement,
        db.bind,
    )

    logger.info(
        f"Loaded {len(train_data)} training samples, {len(val_data)} validation samples, {len(test_data)} test samples"
    )

    return train_data, val_data, test_data


def prepare_features_and_target(
    data: pd.DataFrame,
) -> Tuple[pd.DataFrame, pd.Series]:
    """
    Prepare features and target for model training.

    Args:
        data: DataFrame with normalized data

    Returns:
        Tuple of (features, target)
    """
    # Define the target variable
    target = data["ac_power"]

    # Remove rows with NaN in the target variable
    valid_indices = ~target.isna()
    data = data[valid_indices]
    target = data["ac_power"]

    # Define the features
    # Exclude non-feature columns and the target variable
    exclude_columns = [
        "id",
        "timestamp",
        "created_at",
        "integrated_data_id",
        "ac_power",
        "ac_power_normalized",
        "dataset",
        "normalization_params",
    ]

    # Get all columns that end with "_normalized"
    feature_columns = [
        col
        for col in data.columns
        if col.endswith("_normalized") and col != "ac_power_normalized"
    ]

    # Create the feature matrix
    features = data[feature_columns]

    # Fill NaN values with 0
    features = features.fillna(0)

    return features, target


def optimize_xgboost_hyperparameters(
    train_features: pd.DataFrame,
    train_target: pd.Series,
    val_features: pd.DataFrame,
    val_target: pd.Series,
    n_trials: int = 100,
) -> Dict[str, Any]:
    """
    Optimize XGBoost hyperparameters using Optuna.

    Args:
        train_features: Training features
        train_target: Training target
        val_features: Validation features
        val_target: Validation target
        n_trials: Number of optimization trials

    Returns:
        Dictionary with optimized hyperparameters
    """
    logger.info("Optimizing XGBoost hyperparameters...")

    # Import XGBoost at the function level to ensure it's available in the objective function
    import xgboost as xgb

    # Check if GPU is available
    gpu_available = False
    try:
        import cupy

        gpu_available = True
        logger.info("GPU is available and will be used for XGBoost training")
    except ImportError:
        logger.info("CuPy not found, checking for GPU directly with XGBoost")
        try:
            # Try to create a small XGBoost model with GPU
            dtrain = xgb.DMatrix(np.random.rand(10, 5), label=np.random.rand(10))
            params = {"tree_method": "gpu_hist"}
            # Train for just 1 iteration to test GPU availability
            xgb.train(params, dtrain, num_boost_round=1)
            gpu_available = True
            logger.info("GPU is available and will be used for XGBoost training")
        except Exception as e:
            logger.info(f"GPU is not available for XGBoost: {e}")
            logger.info("Using CPU for XGBoost training")

    def objective(trial):
        # Define the hyperparameters to optimize
        params = {
            "objective": "reg:squarederror",
            "eval_metric": "rmse",
            "booster": trial.suggest_categorical(
                "booster", ["gbtree", "gblinear", "dart"]
            ),
            "lambda": trial.suggest_float("lambda", 1e-8, 1.0, log=True),
            "alpha": trial.suggest_float("alpha", 1e-8, 1.0, log=True),
        }

        # Add GPU parameters if available
        if gpu_available and params["booster"] in ["gbtree", "dart"]:
            params["tree_method"] = "gpu_hist"
            params["gpu_id"] = 0

        # Add booster-specific parameters
        if params["booster"] == "gbtree" or params["booster"] == "dart":
            params.update(
                {
                    "max_depth": trial.suggest_int("max_depth", 3, 12),
                    "eta": trial.suggest_float("eta", 0.01, 0.3, log=True),
                    "gamma": trial.suggest_float("gamma", 1e-8, 1.0, log=True),
                    "grow_policy": trial.suggest_categorical(
                        "grow_policy", ["depthwise", "lossguide"]
                    ),
                }
            )

        # Create the evaluation dataset
        dtrain = xgb.DMatrix(train_features, label=train_target)
        dval = xgb.DMatrix(val_features, label=val_target)

        # Train the model
        pruning_callback = optuna.integration.XGBoostPruningCallback(
            trial, "validation-rmse"
        )
        model = xgb.train(
            params,
            dtrain,
            num_boost_round=10000,
            evals=[(dtrain, "train"), (dval, "validation")],
            early_stopping_rounds=50,
            verbose_eval=False,
            callbacks=[pruning_callback],
        )

        # Return the validation RMSE
        return model.best_score

    # Create the Optuna study
    study = optuna.create_study(direction="minimize")

    # Optimize the hyperparameters
    study.optimize(objective, n_trials=n_trials)

    logger.info(f"Best XGBoost hyperparameters: {study.best_params}")
    logger.info(f"Best XGBoost validation RMSE: {study.best_value:.4f}")

    return study.best_params


def optimize_lightgbm_hyperparameters(
    train_features: pd.DataFrame,
    train_target: pd.Series,
    val_features: pd.DataFrame,
    val_target: pd.Series,
    n_trials: int = 100,
) -> Dict[str, Any]:
    """
    Optimize LightGBM hyperparameters using Optuna.

    Args:
        train_features: Training features
        train_target: Training target
        val_features: Validation features
        val_target: Validation target
        n_trials: Number of optimization trials

    Returns:
        Dictionary with optimized hyperparameters
    """
    logger.info("Optimizing LightGBM hyperparameters...")

    # Import LightGBM at the function level to ensure it's available in the objective function
    import lightgbm as lgb

    # Check if GPU is available for LightGBM
    gpu_available = False
    try:
        # Try to create a small LightGBM model with GPU
        dtrain = lgb.Dataset(np.random.rand(10, 5), label=np.random.rand(10))
        params = {"device": "gpu"}
        # Train for just 1 iteration to test GPU availability
        lgb.train(params, dtrain, num_boost_round=1)
        gpu_available = True
        logger.info("GPU is available and will be used for LightGBM training")
    except Exception as e:
        logger.info(f"GPU is not available for LightGBM: {e}")
        logger.info("Using CPU for LightGBM training")

    def objective(trial):
        # Define the hyperparameters to optimize
        boosting_type = trial.suggest_categorical(
            "boosting_type", ["gbdt", "dart", "goss"]
        )

        params = {
            "objective": "regression",
            "metric": "rmse",
            "verbosity": -1,
            "boosting_type": boosting_type,
            "lambda_l1": trial.suggest_float("lambda_l1", 1e-8, 10.0, log=True),
            "lambda_l2": trial.suggest_float("lambda_l2", 1e-8, 10.0, log=True),
            "num_leaves": trial.suggest_int("num_leaves", 2, 256),
            "feature_fraction": trial.suggest_float("feature_fraction", 0.4, 1.0),
            "min_child_samples": trial.suggest_int("min_child_samples", 5, 100),
        }

        # Add GPU parameters if available
        if gpu_available:
            params["device"] = "gpu"
            params["gpu_platform_id"] = 0
            params["gpu_device_id"] = 0

        # Add boosting-specific parameters
        if boosting_type == "goss":
            # GOSS doesn't use bagging
            pass
        else:
            # Add bagging parameters for gbdt and dart
            params["bagging_fraction"] = trial.suggest_float(
                "bagging_fraction", 0.4, 1.0
            )
            params["bagging_freq"] = trial.suggest_int("bagging_freq", 1, 7)

        # Create the evaluation dataset
        dtrain = lgb.Dataset(train_features, label=train_target)
        dval = lgb.Dataset(val_features, label=val_target, reference=dtrain)

        # Train the model
        pruning_callback = optuna.integration.LightGBMPruningCallback(
            trial, "rmse", "validation"
        )
        model = lgb.train(
            params,
            dtrain,
            num_boost_round=10000,
            valid_sets=[dtrain, dval],
            valid_names=["train", "validation"],
            early_stopping_rounds=50,
            verbose_eval=False,
            callbacks=[pruning_callback],
        )

        # Return the validation RMSE
        return model.best_score["validation"]["rmse"]

    # Create the Optuna study
    study = optuna.create_study(direction="minimize")

    # Optimize the hyperparameters
    study.optimize(objective, n_trials=n_trials)

    logger.info(f"Best LightGBM hyperparameters: {study.best_params}")
    logger.info(f"Best LightGBM validation RMSE: {study.best_value:.4f}")

    return study.best_params


def optimize_random_forest_hyperparameters(
    train_features: pd.DataFrame,
    train_target: pd.Series,
    val_features: pd.DataFrame,
    val_target: pd.Series,
    n_trials: int = 50,
) -> Dict[str, Any]:
    """
    Optimize Random Forest hyperparameters using Optuna.

    Args:
        train_features: Training features
        train_target: Training target
        val_features: Validation features
        val_target: Validation target
        n_trials: Number of optimization trials

    Returns:
        Dictionary with optimized hyperparameters
    """
    logger.info("Optimizing Random Forest hyperparameters...")

    # Import required libraries at the function level
    from sklearn.ensemble import RandomForestRegressor

    # Check if GPU is available for XGBoost or LightGBM
    # If not, we'll use more CPU cores for Random Forest
    gpu_available = False
    try:
        import cupy

        gpu_available = True
    except ImportError:
        try:
            import xgboost as xgb

            dtrain = xgb.DMatrix(np.random.rand(10, 5), label=np.random.rand(10))
            params = {"tree_method": "gpu_hist"}
            xgb.train(params, dtrain, num_boost_round=1)
            gpu_available = True
        except Exception:
            try:
                import lightgbm as lgb

                dtrain = lgb.Dataset(np.random.rand(10, 5), label=np.random.rand(10))
                params = {"device": "gpu"}
                lgb.train(params, dtrain, num_boost_round=1)
                gpu_available = True
            except Exception:
                gpu_available = False

    # If GPU is not available, use more CPU cores for Random Forest
    n_jobs = 2 if gpu_available else -1  # Use all cores if GPU is not available
    logger.info(
        f"Using {n_jobs} CPU cores for Random Forest (GPU available: {gpu_available})"
    )

    def objective(trial):
        # Define the hyperparameters to optimize
        params = {
            "n_estimators": trial.suggest_int("n_estimators", 50, 500),
            "max_depth": trial.suggest_int("max_depth", 3, 25),
            "min_samples_split": trial.suggest_int("min_samples_split", 2, 20),
            "min_samples_leaf": trial.suggest_int("min_samples_leaf", 1, 20),
            "max_features": trial.suggest_float("max_features", 0.1, 1.0),
            "bootstrap": trial.suggest_categorical("bootstrap", [True, False]),
        }

        # Create the model
        model = RandomForestRegressor(
            random_state=42,
            n_jobs=n_jobs,
            **params,
        )

        # Train the model
        model.fit(train_features, train_target)

        # Evaluate the model
        val_pred = model.predict(val_features)
        val_rmse = np.sqrt(mean_squared_error(val_target, val_pred))

        return val_rmse

    # Create the Optuna study
    study = optuna.create_study(direction="minimize")

    # Optimize the hyperparameters
    study.optimize(objective, n_trials=n_trials)

    logger.info(f"Best Random Forest hyperparameters: {study.best_params}")
    logger.info(f"Best Random Forest validation RMSE: {study.best_value:.4f}")

    return study.best_params


def train_xgboost_model(
    train_features: pd.DataFrame,
    train_target: pd.Series,
    val_features: pd.DataFrame,
    val_target: pd.Series,
    hyperparameters: Dict[str, Any],
) -> xgb.Booster:
    """
    Train an XGBoost model with the given hyperparameters.

    Args:
        train_features: Training features
        train_target: Training target
        val_features: Validation features
        val_target: Validation target
        hyperparameters: Dictionary with hyperparameters

    Returns:
        Trained XGBoost model
    """
    logger.info("Training XGBoost model...")

    # Check if GPU parameters are in hyperparameters
    if (
        "tree_method" in hyperparameters
        and hyperparameters["tree_method"] == "gpu_hist"
    ):
        logger.info("Using GPU for XGBoost training")
    else:
        logger.info("Using CPU for XGBoost training")

    # Create the evaluation dataset
    dtrain = xgb.DMatrix(train_features, label=train_target)
    dval = xgb.DMatrix(val_features, label=val_target)

    # Train the model
    model = xgb.train(
        hyperparameters,
        dtrain,
        num_boost_round=10000,
        evals=[(dtrain, "train"), (dval, "validation")],
        early_stopping_rounds=50,
        verbose_eval=100,
    )

    logger.info(f"XGBoost model trained with {model.best_iteration} iterations")
    logger.info(f"XGBoost validation RMSE: {model.best_score:.4f}")

    return model


def train_lightgbm_model(
    train_features: pd.DataFrame,
    train_target: pd.Series,
    val_features: pd.DataFrame,
    val_target: pd.Series,
    hyperparameters: Dict[str, Any],
) -> lgb.Booster:
    """
    Train a LightGBM model with the given hyperparameters.

    Args:
        train_features: Training features
        train_target: Training target
        val_features: Validation features
        val_target: Validation target
        hyperparameters: Dictionary with hyperparameters

    Returns:
        Trained LightGBM model
    """
    logger.info("Training LightGBM model...")

    # Check if GPU parameters are in hyperparameters
    if "device" in hyperparameters and hyperparameters["device"] == "gpu":
        logger.info("Using GPU for LightGBM training")
    else:
        logger.info("Using CPU for LightGBM training")

    # Create the evaluation dataset
    dtrain = lgb.Dataset(train_features, label=train_target)
    dval = lgb.Dataset(val_features, label=val_target, reference=dtrain)

    # Train the model
    model = lgb.train(
        hyperparameters,
        dtrain,
        num_boost_round=10000,
        valid_sets=[dtrain, dval],
        valid_names=["train", "validation"],
        early_stopping_rounds=50,
        verbose_eval=100,
    )

    # Check if best_iteration exists
    if hasattr(model, "best_iteration"):
        logger.info(f"LightGBM model trained with {model.best_iteration} iterations")
    else:
        logger.info("LightGBM model trained (iterations not available)")
    # Check if best_score exists and has the expected structure
    if (
        hasattr(model, "best_score")
        and isinstance(model.best_score, dict)
        and "validation" in model.best_score
    ):
        if "rmse" in model.best_score["validation"]:
            logger.info(
                f"LightGBM validation RMSE: {model.best_score['validation']['rmse']:.4f}"
            )
        else:
            # For dart mode, the metric might be different
            metric = list(model.best_score["validation"].keys())[0]
            logger.info(
                f"LightGBM validation {metric}: {model.best_score['validation'][metric]:.4f}"
            )
    else:
        logger.info("LightGBM validation metrics not available")

    return model


def train_random_forest_model(
    train_features: pd.DataFrame,
    train_target: pd.Series,
    hyperparameters: Dict[str, Any],
) -> RandomForestRegressor:
    """
    Train a Random Forest model with the given hyperparameters.

    Args:
        train_features: Training features
        train_target: Training target
        hyperparameters: Dictionary with hyperparameters

    Returns:
        Trained Random Forest model
    """
    logger.info("Training Random Forest model...")

    # Create the model
    model = RandomForestRegressor(
        random_state=42,
        n_jobs=-1,
        **hyperparameters,
    )

    # Train the model
    model.fit(train_features, train_target)

    logger.info(f"Random Forest model trained with {model.n_estimators} trees")

    return model


# Create wrapper classes for XGBoost and LightGBM models
class XGBoostWrapper(BaseEstimator, RegressorMixin):
    def __init__(self, model):
        self.model = model

    def predict(self, X):
        dmatrix = xgb.DMatrix(X)
        return self.model.predict(dmatrix)

    def fit(self, X, y):
        # This is a dummy method, as the model is already trained
        return self


class LightGBMWrapper(BaseEstimator, RegressorMixin):
    def __init__(self, model):
        self.model = model

    def predict(self, X):
        return self.model.predict(X)

    def fit(self, X, y):
        # This is a dummy method, as the model is already trained
        return self


def create_ensemble_model(
    xgb_model: xgb.Booster,
    lgb_model: lgb.Booster,
    rf_model: RandomForestRegressor,
    train_features: pd.DataFrame,
    train_target: pd.Series,
) -> StackingRegressor:
    """
    Create an ensemble model using stacking.

    Args:
        xgb_model: Trained XGBoost model
        lgb_model: Trained LightGBM model
        rf_model: Trained Random Forest model
        train_features: Training features
        train_target: Training target

    Returns:
        Trained ensemble model
    """
    logger.info("Creating ensemble model...")

    # Create the base estimators
    estimators = [
        ("xgboost", XGBoostWrapper(xgb_model)),
        ("lightgbm", LightGBMWrapper(lgb_model)),
        ("random_forest", rf_model),
    ]

    # Create the stacking regressor
    ensemble = StackingRegressor(
        estimators=estimators,
        final_estimator=Ridge(alpha=1.0),
        cv=5,
        n_jobs=-1,
    )

    # Train the ensemble model
    ensemble.fit(train_features, train_target)

    logger.info("Ensemble model created and trained")

    return ensemble


def evaluate_models(
    models: Dict[str, Any],
    test_features: pd.DataFrame,
    test_target: pd.Series,
) -> Dict[str, float]:
    """
    Evaluate the models on the test set.

    Args:
        models: Dictionary with trained models
        test_features: Test features
        test_target: Test target

    Returns:
        Dictionary with evaluation metrics
    """
    logger.info("Evaluating models...")

    results = {}

    # Evaluate XGBoost model
    xgb_pred = models["xgboost"].predict(xgb.DMatrix(test_features))
    xgb_rmse = np.sqrt(mean_squared_error(test_target, xgb_pred))
    xgb_mae = mean_absolute_error(test_target, xgb_pred)
    xgb_r2 = r2_score(test_target, xgb_pred)

    results["xgboost"] = {
        "rmse": xgb_rmse,
        "mae": xgb_mae,
        "r2": xgb_r2,
    }

    logger.info(
        f"XGBoost test RMSE: {xgb_rmse:.4f}, MAE: {xgb_mae:.4f}, R²: {xgb_r2:.4f}"
    )

    # Evaluate LightGBM model
    lgb_pred = models["lightgbm"].predict(test_features)
    lgb_rmse = np.sqrt(mean_squared_error(test_target, lgb_pred))
    lgb_mae = mean_absolute_error(test_target, lgb_pred)
    lgb_r2 = r2_score(test_target, lgb_pred)

    results["lightgbm"] = {
        "rmse": lgb_rmse,
        "mae": lgb_mae,
        "r2": lgb_r2,
    }

    logger.info(
        f"LightGBM test RMSE: {lgb_rmse:.4f}, MAE: {lgb_mae:.4f}, R²: {lgb_r2:.4f}"
    )

    # Evaluate Random Forest model
    rf_pred = models["random_forest"].predict(test_features)
    rf_rmse = np.sqrt(mean_squared_error(test_target, rf_pred))
    rf_mae = mean_absolute_error(test_target, rf_pred)
    rf_r2 = r2_score(test_target, rf_pred)

    results["random_forest"] = {
        "rmse": rf_rmse,
        "mae": rf_mae,
        "r2": rf_r2,
    }

    logger.info(
        f"Random Forest test RMSE: {rf_rmse:.4f}, MAE: {rf_mae:.4f}, R²: {rf_r2:.4f}"
    )

    # Evaluate Ensemble model
    ensemble_pred = models["ensemble"].predict(test_features)
    ensemble_rmse = np.sqrt(mean_squared_error(test_target, ensemble_pred))
    ensemble_mae = mean_absolute_error(test_target, ensemble_pred)
    ensemble_r2 = r2_score(test_target, ensemble_pred)

    results["ensemble"] = {
        "rmse": ensemble_rmse,
        "mae": ensemble_mae,
        "r2": ensemble_r2,
    }

    logger.info(
        f"Ensemble test RMSE: {ensemble_rmse:.4f}, MAE: {ensemble_mae:.4f}, R²: {ensemble_r2:.4f}"
    )

    # Find the best model
    best_model = min(results.items(), key=lambda x: x[1]["rmse"])
    logger.info(f"Best model: {best_model[0]} with RMSE: {best_model[1]['rmse']:.4f}")

    return results


def save_models(
    models: Dict[str, Any],
    hyperparameters: Dict[str, Dict[str, Any]],
    evaluation_results: Dict[str, Dict[str, float]],
    feature_columns: List[str],
    output_dir: str,
) -> None:
    """
    Save the trained models and metadata.

    Args:
        models: Dictionary with trained models
        hyperparameters: Dictionary with hyperparameters for each model
        evaluation_results: Dictionary with evaluation metrics for each model
        feature_columns: List of feature column names
        output_dir: Directory to save the models
    """
    logger.info(f"Saving models to {output_dir}...")

    # Create the output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Save XGBoost model
    xgb_model_path = os.path.join(output_dir, "xgboost_model.json")
    models["xgboost"].save_model(xgb_model_path)
    logger.info(f"XGBoost model saved to {xgb_model_path}")

    # Save LightGBM model
    lgb_model_path = os.path.join(output_dir, "lightgbm_model.txt")
    models["lightgbm"].save_model(lgb_model_path)
    logger.info(f"LightGBM model saved to {lgb_model_path}")

    # Save Random Forest model
    rf_model_path = os.path.join(output_dir, "random_forest_model.pkl")
    joblib.dump(models["random_forest"], rf_model_path)
    logger.info(f"Random Forest model saved to {rf_model_path}")

    # Save Ensemble model
    ensemble_model_path = os.path.join(output_dir, "ensemble_model.pkl")
    joblib.dump(models["ensemble"], ensemble_model_path)
    logger.info(f"Ensemble model saved to {ensemble_model_path}")

    # Save hyperparameters
    hyperparameters_path = os.path.join(output_dir, "hyperparameters.json")
    with open(hyperparameters_path, "w") as f:
        json.dump(hyperparameters, f, indent=4)
    logger.info(f"Hyperparameters saved to {hyperparameters_path}")

    # Save evaluation results
    evaluation_path = os.path.join(output_dir, "evaluation_results.json")
    with open(evaluation_path, "w") as f:
        json.dump(evaluation_results, f, indent=4)
    logger.info(f"Evaluation results saved to {evaluation_path}")

    # Save feature columns
    feature_columns_path = os.path.join(output_dir, "feature_columns.json")
    with open(feature_columns_path, "w") as f:
        json.dump(feature_columns, f, indent=4)
    logger.info(f"Feature columns saved to {feature_columns_path}")

    # Save metadata
    metadata = {
        "created_at": datetime.now().isoformat(),
        "models": [
            "xgboost_model.json",
            "lightgbm_model.txt",
            "random_forest_model.pkl",
            "ensemble_model.pkl",
        ],
        "hyperparameters": "hyperparameters.json",
        "evaluation_results": "evaluation_results.json",
        "feature_columns": "feature_columns.json",
        "best_model": min(evaluation_results.items(), key=lambda x: x[1]["rmse"])[0],
    }

    metadata_path = os.path.join(output_dir, "metadata.json")
    with open(metadata_path, "w") as f:
        json.dump(metadata, f, indent=4)
    logger.info(f"Metadata saved to {metadata_path}")


def main():
    """Main function to train the enhanced solar prediction model."""
    # Parse command line arguments
    args = parse_arguments()

    # Record start time
    start_time = datetime.now()
    logger.info(f"Starting model training at {start_time}")

    # Connect to the database
    db = SessionLocal()

    try:
        # Load training data
        train_data, val_data, test_data = load_training_data(db)

        # Prepare features and target
        train_features, train_target = prepare_features_and_target(train_data)
        val_features, val_target = prepare_features_and_target(val_data)
        test_features, test_target = prepare_features_and_target(test_data)

        # Optimize hyperparameters
        xgb_hyperparameters = optimize_xgboost_hyperparameters(
            train_features, train_target, val_features, val_target, n_trials=10
        )

        lgb_hyperparameters = optimize_lightgbm_hyperparameters(
            train_features, train_target, val_features, val_target, n_trials=10
        )

        rf_hyperparameters = optimize_random_forest_hyperparameters(
            train_features, train_target, val_features, val_target, n_trials=5
        )

        # Train models
        xgb_model = train_xgboost_model(
            train_features, train_target, val_features, val_target, xgb_hyperparameters
        )

        lgb_model = train_lightgbm_model(
            train_features, train_target, val_features, val_target, lgb_hyperparameters
        )

        rf_model = train_random_forest_model(
            train_features, train_target, rf_hyperparameters
        )

        # Create ensemble model
        ensemble_model = create_ensemble_model(
            xgb_model, lgb_model, rf_model, train_features, train_target
        )

        # Collect all models
        models = {
            "xgboost": xgb_model,
            "lightgbm": lgb_model,
            "random_forest": rf_model,
            "ensemble": ensemble_model,
        }

        # Collect all hyperparameters
        hyperparameters = {
            "xgboost": xgb_hyperparameters,
            "lightgbm": lgb_hyperparameters,
            "random_forest": rf_hyperparameters,
        }

        # Evaluate models
        evaluation_results = evaluate_models(models, test_features, test_target)

        # Save models
        save_models(
            models,
            hyperparameters,
            evaluation_results,
            train_features.columns.tolist(),
            args.output_dir,
        )

        # Record end time
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()

        logger.info(f"Model training completed at {end_time}")
        logger.info(f"Total duration: {duration:.2f} seconds")

    finally:
        db.close()


if __name__ == "__main__":
    main()
