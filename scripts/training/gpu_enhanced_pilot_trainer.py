#!/usr/bin/env python3
"""
GPU-Enhanced Pilot Models Trainer
=================================

GPU-accelerated training pipeline για τα 2 pilot models χρησιμοποιώντας:
- RAPIDS cuML για GPU RandomForest
- XGBoost με GPU support
- CuPy για GPU array operations
- GPU-accelerated hyperparameter optimization

Hardware: NVIDIA GeForce RTX 4070 Ti (12GB VRAM)

Δημιουργήθηκε: 2025-06-05
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))

import pandas as pd
import numpy as np
import psycopg2
from pathlib import Path
from datetime import datetime, timedelta
import joblib
import json
from typing import Dict, List, Tuple, Any, Optional
import logging
import warnings
warnings.filterwarnings('ignore')

# Standard ML imports
from sklearn.model_selection import train_test_split, cross_val_score, TimeSeriesSplit
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.preprocessing import StandardScaler

# GPU imports (with fallbacks)
try:
    import cupy as cp
    import cudf
    import cuml
    from cuml.ensemble import RandomForestRegressor as cuRandomForestRegressor
    from cuml.model_selection import GridSearchCV as cuGridSearchCV
    GPU_AVAILABLE = True
    print("🚀 GPU libraries loaded successfully!")
except ImportError as e:
    print(f"⚠️ GPU libraries not available: {e}")
    print("📦 Falling back to CPU-only mode")
    GPU_AVAILABLE = False
    # Fallback imports
    from sklearn.ensemble import RandomForestRegressor
    from sklearn.model_selection import GridSearchCV

# XGBoost με GPU support
try:
    import xgboost as xgb
    XGBOOST_AVAILABLE = True
except ImportError:
    print("⚠️ XGBoost not available")
    XGBOOST_AVAILABLE = False

# Import unified preprocessing pipeline
from src.preprocessing.unified_pipeline import create_unified_pipeline

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class GPUEnhancedPilotTrainer:
    """
    GPU-accelerated trainer για pilot models
    """
    
    def __init__(self, output_dir: str = "models/gpu_enhanced_pilot", pipeline_version: str = "v1.0.0"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True, parents=True)
        
        # Initialize unified pipeline
        self.pipeline = create_unified_pipeline(pipeline_version)
        self.pipeline_version = pipeline_version
        
        # GPU configuration
        self.gpu_available = GPU_AVAILABLE
        self.device_info = self.check_gpu_availability()
        
        # Pilot models configuration
        self.pilot_models = {
            'spring_system1': {
                'type': 'seasonal',
                'system_id': 1,
                'season': 'spring',
                'target_r2': 0.95,
                'target_mae': 2.5
            },
            'multi_horizon_daily_system1': {
                'type': 'multi_horizon',
                'system_id': 1,
                'horizon': 'daily',
                'target_r2': 0.90,
                'target_mae': 3.0
            }
        }
        
        # Algorithm configurations
        self.algorithms = self.setup_algorithms()
        
        logger.info(f"🏗️ Initialized GPUEnhancedPilotTrainer with pipeline {pipeline_version}")
        logger.info(f"🎮 GPU Status: {'✅ Available' if self.gpu_available else '❌ Not Available'}")
        if self.gpu_available:
            logger.info(f"🎮 GPU Info: {self.device_info}")
    
    def check_gpu_availability(self) -> Dict[str, Any]:
        """Check GPU availability και memory"""
        if not self.gpu_available:
            return {'available': False}
        
        try:
            # Get GPU info
            gpu_count = cp.cuda.runtime.getDeviceCount()
            current_device = cp.cuda.Device()
            
            # Get memory info
            mempool = cp.get_default_memory_pool()
            total_memory = current_device.mem_info[1]  # Total memory
            free_memory = current_device.mem_info[0]   # Free memory
            
            device_info = {
                'available': True,
                'gpu_count': gpu_count,
                'current_device': current_device.id,
                'total_memory_gb': total_memory / (1024**3),
                'free_memory_gb': free_memory / (1024**3),
                'memory_pool_used_gb': mempool.used_bytes() / (1024**3)
            }
            
            return device_info
            
        except Exception as e:
            logger.warning(f"⚠️ Failed to get GPU info: {e}")
            return {'available': False, 'error': str(e)}
    
    def setup_algorithms(self) -> Dict[str, Any]:
        """Setup algorithms με GPU support"""
        algorithms = {}
        
        if self.gpu_available:
            # GPU RandomForest
            algorithms['GPU_RandomForest'] = {
                'model': cuRandomForestRegressor,
                'param_grid': {
                    'n_estimators': [100, 200, 300],
                    'max_depth': [10, 15, 20],
                    'min_samples_split': [2, 5, 10],
                    'min_samples_leaf': [1, 2, 4],
                    'max_features': [0.3, 0.5, 0.7, 1.0]
                },
                'gpu': True
            }
        else:
            # CPU RandomForest fallback
            algorithms['CPU_RandomForest'] = {
                'model': RandomForestRegressor,
                'param_grid': {
                    'n_estimators': [100, 200, 300],
                    'max_depth': [10, 15, 20, None],
                    'min_samples_split': [2, 5, 10],
                    'min_samples_leaf': [1, 2, 4],
                    'max_features': ['sqrt', 'log2', None]
                },
                'gpu': False
            }
        
        if XGBOOST_AVAILABLE:
            # XGBoost με GPU support
            xgb_params = {
                'n_estimators': [100, 200, 300],
                'max_depth': [6, 8, 10],
                'learning_rate': [0.01, 0.1, 0.2],
                'subsample': [0.8, 0.9, 1.0]
            }
            
            if self.gpu_available:
                algorithms['GPU_XGBoost'] = {
                    'model': lambda **kwargs: xgb.XGBRegressor(tree_method='gpu_hist', gpu_id=0, **kwargs),
                    'param_grid': xgb_params,
                    'gpu': True
                }
            else:
                algorithms['CPU_XGBoost'] = {
                    'model': xgb.XGBRegressor,
                    'param_grid': xgb_params,
                    'gpu': False
                }
        
        return algorithms
    
    def load_training_data(self) -> pd.DataFrame:
        """Load training data με GPU optimization"""
        logger.info("📊 Loading training data...")
        
        try:
            conn = psycopg2.connect(
                host='localhost',
                database='solar_prediction',
                user='postgres',
                password='postgres'
            )
            
            # Optimized query για GPU processing
            query = """
            SELECT 
                s.timestamp,
                s.yield_today,
                s.soc,
                s.bat_power,
                s.temperature,
                s.powerdc1,
                s.powerdc2,
                w.global_horizontal_irradiance,
                w.temperature_2m,
                w.relative_humidity_2m,
                w.cloud_cover,
                w.direct_radiation,
                w.diffuse_radiation,
                1 as system_id,
                EXTRACT(MONTH FROM s.timestamp) as month,
                EXTRACT(HOUR FROM s.timestamp) as hour,
                EXTRACT(DOY FROM s.timestamp) as day_of_year
            FROM solax_data s
            LEFT JOIN weather_data w ON DATE_TRUNC('hour', s.timestamp) = DATE_TRUNC('hour', w.timestamp)
            WHERE s.timestamp >= '2024-03-01' 
              AND s.yield_today IS NOT NULL
              AND s.yield_today >= 0 
              AND s.yield_today <= 100
              AND w.global_horizontal_irradiance IS NOT NULL
            ORDER BY s.timestamp
            LIMIT 50000
            """
            
            df = pd.read_sql(query, conn)
            conn.close()
            
            # Data cleaning
            df = df.fillna(method='ffill').fillna(method='bfill').fillna(0)
            df = df.drop_duplicates(subset=['timestamp'])
            
            logger.info(f"✅ Loaded {len(df):,} training records")
            
            # Convert to GPU DataFrame if available
            if self.gpu_available:
                try:
                    gpu_df = cudf.from_pandas(df)
                    logger.info("🎮 Converted data to GPU DataFrame")
                    return gpu_df
                except Exception as e:
                    logger.warning(f"⚠️ Failed to convert to GPU DataFrame: {e}")
                    return df
            
            return df
            
        except Exception as e:
            logger.error(f"❌ Failed to load training data: {e}")
            return self.generate_synthetic_data()
    
    def generate_synthetic_data(self, n_samples: int = 10000) -> pd.DataFrame:
        """Generate synthetic data με GPU optimization"""
        logger.info(f"🎭 Generating {n_samples} synthetic samples...")
        
        if self.gpu_available:
            # Generate data on GPU
            cp.random.seed(42)
            
            # Generate timestamps (on CPU first)
            start_date = datetime(2024, 3, 1)
            timestamps = [start_date + timedelta(hours=i/12) for i in range(n_samples)]
            
            # Generate features on GPU
            hours = cp.array([t.hour for t in timestamps])
            months = cp.array([t.month for t in timestamps])
            days_of_year = cp.array([t.timetuple().tm_yday for t in timestamps])
            
            # Vectorized calculations on GPU
            seasonal_factor = 0.5 + 0.5 * cp.sin(2 * cp.pi * days_of_year / 365)
            hour_factor = cp.where(
                (hours >= 6) & (hours <= 18),
                cp.sin(cp.pi * (hours - 6) / 12),
                0
            )
            
            # Weather simulation on GPU
            base_temp = 15 + 10 * seasonal_factor + cp.random.normal(0, 3, n_samples)
            cloud_cover = cp.random.uniform(0, 100, n_samples)
            ghi = 1000 * hour_factor * (1 - cloud_cover/100) * seasonal_factor
            ghi = cp.maximum(0, ghi + cp.random.normal(0, 50, n_samples))
            
            # Yield simulation on GPU
            yield_today = ghi * 0.05 * (1 + cp.random.normal(0, 0.1, n_samples))
            yield_today = cp.clip(yield_today, 0, 100)
            
            # Create GPU DataFrame
            gpu_data = {
                'timestamp': timestamps,  # Keep on CPU
                'yield_today': yield_today,
                'soc': cp.random.uniform(20, 90, n_samples),
                'bat_power': cp.random.normal(0, 500, n_samples),
                'temperature': base_temp + cp.random.normal(0, 2, n_samples),
                'powerdc1': yield_today * 600,
                'powerdc2': yield_today * 600,
                'global_horizontal_irradiance': ghi,
                'temperature_2m': base_temp,
                'relative_humidity_2m': cp.random.uniform(30, 90, n_samples),
                'cloud_cover': cloud_cover,
                'direct_radiation': ghi * 0.8,
                'diffuse_radiation': ghi * 0.2,
                'system_id': cp.ones(n_samples, dtype=cp.int32),
                'month': months,
                'hour': hours,
                'day_of_year': days_of_year
            }
            
            try:
                # Convert GPU arrays to pandas for compatibility
                pandas_data = {}
                for key, value in gpu_data.items():
                    if key == 'timestamp':
                        pandas_data[key] = value
                    else:
                        pandas_data[key] = cp.asnumpy(value)
                
                df = pd.DataFrame(pandas_data)
                logger.info(f"✅ Generated synthetic data on GPU: {len(df):,} samples")
                
                # Convert back to GPU DataFrame if possible
                if self.gpu_available:
                    return cudf.from_pandas(df)
                return df
                
            except Exception as e:
                logger.warning(f"⚠️ GPU synthetic data generation failed: {e}")
                # Fallback to CPU
                return self.generate_cpu_synthetic_data(n_samples)
        else:
            return self.generate_cpu_synthetic_data(n_samples)
    
    def generate_cpu_synthetic_data(self, n_samples: int) -> pd.DataFrame:
        """Fallback CPU synthetic data generation"""
        np.random.seed(42)
        
        start_date = datetime(2024, 3, 1)
        timestamps = [start_date + timedelta(hours=i/12) for i in range(n_samples)]
        
        data = []
        for timestamp in timestamps:
            hour = timestamp.hour
            month = timestamp.month
            day_of_year = timestamp.timetuple().tm_yday
            
            seasonal_factor = 0.5 + 0.5 * np.sin(2 * np.pi * day_of_year / 365)
            hour_factor = np.sin(np.pi * (hour - 6) / 12) if 6 <= hour <= 18 else 0
            
            base_temp = 15 + 10 * seasonal_factor + np.random.normal(0, 3)
            cloud_cover = np.random.uniform(0, 100)
            ghi = max(0, 1000 * hour_factor * (1 - cloud_cover/100) * seasonal_factor + np.random.normal(0, 50))
            yield_today = max(0, min(100, ghi * 0.05 * (1 + np.random.normal(0, 0.1))))
            
            data.append({
                'timestamp': timestamp,
                'yield_today': yield_today,
                'soc': np.random.uniform(20, 90),
                'bat_power': np.random.normal(0, 500),
                'temperature': base_temp + np.random.normal(0, 2),
                'powerdc1': yield_today * 600,
                'powerdc2': yield_today * 600,
                'global_horizontal_irradiance': ghi,
                'temperature_2m': base_temp,
                'relative_humidity_2m': np.random.uniform(30, 90),
                'cloud_cover': cloud_cover,
                'direct_radiation': ghi * 0.8,
                'diffuse_radiation': ghi * 0.2,
                'system_id': 1,
                'month': month,
                'hour': hour,
                'day_of_year': day_of_year
            })
        
        df = pd.DataFrame(data)
        logger.info(f"✅ Generated synthetic data on CPU: {len(df):,} samples")
        return df
    
    def engineer_gpu_features(self, df) -> pd.DataFrame:
        """GPU-accelerated feature engineering"""
        logger.info("🔧 Engineering features με GPU acceleration...")
        
        # Start με unified pipeline (CPU)
        if self.gpu_available and hasattr(df, 'to_pandas'):
            # Convert GPU DataFrame to pandas για unified pipeline
            pandas_df = df.to_pandas()
        else:
            pandas_df = df
        
        processed_df = self.pipeline.engineer_features(pandas_df)
        
        # Advanced feature engineering
        if self.gpu_available:
            try:
                # Convert to GPU για advanced features
                gpu_df = cudf.from_pandas(processed_df) if not hasattr(processed_df, 'to_pandas') else processed_df
                
                # GPU-accelerated lag features
                for lag in [1, 6, 12, 24]:
                    gpu_df[f'yield_lag_{lag}'] = gpu_df['yield_today'].shift(lag)
                    gpu_df[f'ghi_lag_{lag}'] = gpu_df['global_horizontal_irradiance'].shift(lag)
                
                # GPU-accelerated rolling features
                for window in [12, 24, 72]:
                    gpu_df[f'yield_rolling_mean_{window}'] = gpu_df['yield_today'].rolling(window).mean()
                    gpu_df[f'temp_rolling_mean_{window}'] = gpu_df['temperature_2m'].rolling(window).mean()
                
                # GPU-accelerated derivatives
                gpu_df['temp_change'] = gpu_df['temperature_2m'].diff()
                gpu_df['ghi_change'] = gpu_df['global_horizontal_irradiance'].diff()
                
                # GPU-accelerated interactions
                gpu_df['temp_ghi_interaction'] = gpu_df['temperature_2m'] * gpu_df['global_horizontal_irradiance'] / 1000
                gpu_df['cloud_temp_interaction'] = gpu_df['cloud_cover'] * gpu_df['temperature_2m'] / 100
                
                # Fill missing values
                gpu_df = gpu_df.fillna(0)
                
                logger.info(f"✅ GPU feature engineering complete: {gpu_df.shape[1]} features")
                return gpu_df
                
            except Exception as e:
                logger.warning(f"⚠️ GPU feature engineering failed: {e}")
                # Fallback to CPU
                return self.engineer_cpu_features(processed_df)
        else:
            return self.engineer_cpu_features(processed_df)
    
    def engineer_cpu_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """CPU fallback feature engineering"""
        logger.info("🔧 Engineering features on CPU...")
        
        df = df.sort_values('timestamp').reset_index(drop=True)
        
        # Lag features
        for lag in [1, 6, 12, 24]:
            df[f'yield_lag_{lag}'] = df['yield_today'].shift(lag)
            df[f'ghi_lag_{lag}'] = df['global_horizontal_irradiance'].shift(lag)
        
        # Rolling features
        for window in [12, 24, 72]:
            df[f'yield_rolling_mean_{window}'] = df['yield_today'].rolling(window).mean()
            df[f'temp_rolling_mean_{window}'] = df['temperature_2m'].rolling(window).mean()
        
        # Derivatives
        df['temp_change'] = df['temperature_2m'].diff()
        df['ghi_change'] = df['global_horizontal_irradiance'].diff()
        
        # Interactions
        df['temp_ghi_interaction'] = df['temperature_2m'] * df['global_horizontal_irradiance'] / 1000
        df['cloud_temp_interaction'] = df['cloud_cover'] * df['temperature_2m'] / 100
        
        # Fill missing values
        df = df.fillna(method='ffill').fillna(method='bfill').fillna(0)
        
        logger.info(f"✅ CPU feature engineering complete: {df.shape[1]} features")
        return df

    def save_gpu_model(self, result: Dict[str, Any]):
        """Save GPU-enhanced model"""
        model_name = result['model_name']
        model_dir = self.output_dir / model_name
        model_dir.mkdir(exist_ok=True)

        # Save models
        for name, model_result in result['models'].items():
            model_path = model_dir / f"{name}_model.joblib"
            joblib.dump(model_result['best_model'], model_path)

        # Save scaler
        joblib.dump(result['scaler'], model_dir / "scaler.joblib")

        # Save metadata
        metadata = {
            'model_name': model_name,
            'model_type': 'gpu_enhanced_ensemble',
            'algorithms': list(result['models'].keys()),
            'ensemble_weights': result['ensemble_weights'],
            'features': result['features'],
            'performance': result['ensemble_metrics'],
            'individual_performance': result['individual_metrics'],
            'target_achieved': result['target_achieved'],
            'training_samples': result['training_samples'],
            'test_samples': result['test_samples'],
            'training_date': datetime.now().isoformat(),
            'pipeline_version': self.pipeline_version,
            'gpu_used': result['gpu_used'],
            'gpu_info': self.device_info
        }

        with open(model_dir / "metadata.json", 'w') as f:
            json.dump(metadata, f, indent=2)

        logger.info(f"💾 Saved GPU-enhanced model: {model_dir}")

    def optimize_gpu_hyperparameters(self, algorithm_name: str, algorithm_config: Dict,
                                   X_train, y_train) -> Dict:
        """GPU-accelerated hyperparameter optimization"""

        logger.info(f"🔍 GPU optimizing {algorithm_name} hyperparameters...")

        try:
            if self.gpu_available and algorithm_config['gpu']:
                # Simplified parameter grid για GPU speed
                reduced_grid = {}
                for param, values in algorithm_config['param_grid'].items():
                    if len(values) > 3:
                        # Take first, middle, and last values
                        reduced_grid[param] = [values[0], values[len(values)//2], values[-1]]
                    else:
                        reduced_grid[param] = values

                # CPU GridSearchCV (cuML GridSearchCV has issues)
                from sklearn.model_selection import GridSearchCV
                grid_search = GridSearchCV(
                    estimator=algorithm_config['model'](),
                    param_grid=reduced_grid,
                    cv=3,
                    scoring='neg_mean_absolute_error',
                    n_jobs=1
                )

                # Convert data για training
                if hasattr(X_train, 'to_pandas'):
                    X_train_cpu = X_train.to_pandas().values
                    y_train_cpu = y_train.to_pandas().values
                else:
                    X_train_cpu = X_train
                    y_train_cpu = y_train

                grid_search.fit(X_train_cpu, y_train_cpu)

            else:
                # CPU fallback
                from sklearn.model_selection import GridSearchCV, TimeSeriesSplit
                tscv = TimeSeriesSplit(n_splits=3)

                grid_search = GridSearchCV(
                    estimator=algorithm_config['model'](),
                    param_grid=algorithm_config['param_grid'],
                    cv=tscv,
                    scoring='neg_mean_absolute_error',
                    n_jobs=-1
                )

                # Convert to numpy if needed
                if hasattr(X_train, 'to_pandas'):
                    X_train_cpu = X_train.to_pandas().values
                    y_train_cpu = y_train.to_pandas().values
                else:
                    X_train_cpu = X_train
                    y_train_cpu = y_train

                grid_search.fit(X_train_cpu, y_train_cpu)

            logger.info(f"✅ Best parameters: {grid_search.best_params_}")
            logger.info(f"✅ Best score: {-grid_search.best_score_:.4f}")

            return {
                'best_model': grid_search.best_estimator_,
                'best_params': grid_search.best_params_,
                'best_score': -grid_search.best_score_,
                'algorithm': algorithm_name,
                'gpu_used': self.gpu_available and algorithm_config['gpu']
            }

        except Exception as e:
            logger.error(f"❌ GPU hyperparameter optimization failed για {algorithm_name}: {e}")
            raise

    def train_gpu_ensemble_model(self, model_name: str, processed_data) -> Dict[str, Any]:
        """Train ensemble model με GPU acceleration"""

        logger.info(f"\n🎯 Training GPU-enhanced model: {model_name}")
        logger.info("=" * 80)

        model_config = self.pilot_models[model_name]

        # Filter data για seasonal models
        if model_config['type'] == 'seasonal':
            season_months = {'spring': [3, 4, 5]}[model_config['season']]
            if hasattr(processed_data, 'to_pandas'):
                # GPU DataFrame
                model_data = processed_data[processed_data['month'].isin(season_months)]
            else:
                # CPU DataFrame
                model_data = processed_data[processed_data['month'].isin(season_months)].copy()
        else:
            model_data = processed_data

        # Check data size
        data_len = len(model_data.to_pandas()) if hasattr(model_data, 'to_pandas') else len(model_data)
        if data_len < 100:
            logger.error(f"❌ Insufficient data για {model_name}: {data_len} records")
            return None

        # Feature selection
        if model_config['type'] == 'seasonal':
            base_features = ['hour_sin', 'hour_cos', 'temperature', 'cloud_cover', 'ghi', 'soc']
        else:
            base_features = ['soc', 'bat_power', 'temperature', 'ghi', 'air_temp', 'humidity', 'cloud_cover', 'hour']

        # Add advanced features
        if hasattr(model_data, 'columns'):
            all_columns = model_data.columns.tolist()
        else:
            all_columns = model_data.columns.tolist()

        advanced_features = [col for col in all_columns if any(x in col for x in
            ['lag_', 'rolling_', 'change', 'interaction'])]

        all_features = base_features + advanced_features
        available_features = [f for f in all_features if f in all_columns]

        logger.info(f"📊 Using {len(available_features)} features για training")

        # Prepare data
        if hasattr(model_data, 'to_pandas'):
            # GPU DataFrame
            X = model_data[available_features]
            y = model_data['yield_today']
        else:
            # CPU DataFrame
            X = model_data[available_features].values
            y = model_data['yield_today'].values

        # Train/test split
        if hasattr(X, 'to_pandas'):
            # GPU data
            split_idx = int(0.8 * len(X))
            X_train, X_test = X.iloc[:split_idx], X.iloc[split_idx:]
            y_train, y_test = y.iloc[:split_idx], y.iloc[split_idx:]
        else:
            # CPU data
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, shuffle=False
            )

        # Scale data
        if hasattr(X_train, 'to_pandas'):
            # GPU scaling
            X_train_pandas = X_train.to_pandas()
            X_test_pandas = X_test.to_pandas()

            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train_pandas)
            X_test_scaled = scaler.transform(X_test_pandas)
        else:
            # CPU scaling
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)

        # Train models
        models = {}
        predictions = {}

        for algorithm_name, algorithm_config in self.algorithms.items():
            try:
                logger.info(f"🚀 Training {algorithm_name}...")

                # Optimize hyperparameters
                result = self.optimize_gpu_hyperparameters(
                    algorithm_name, algorithm_config, X_train_scaled, y_train
                )

                models[algorithm_name] = result

                # Make predictions
                predictions[algorithm_name] = result['best_model'].predict(X_test_scaled)

                logger.info(f"✅ {algorithm_name} training completed")

            except Exception as e:
                logger.error(f"❌ Failed to train {algorithm_name}: {e}")
                continue

        if not models:
            logger.error("❌ No models trained successfully")
            return None

        # Ensemble predictions
        if len(predictions) > 1:
            # Multi-model ensemble
            pred_values = list(predictions.values())
            ensemble_pred = np.mean(pred_values, axis=0)
            weights = {name: 1.0/len(predictions) for name in predictions.keys()}
        else:
            # Single model
            ensemble_pred = list(predictions.values())[0]
            weights = {list(predictions.keys())[0]: 1.0}

        # Convert y_test για metrics calculation
        if hasattr(y_test, 'to_pandas'):
            y_test_values = y_test.to_pandas().values
        else:
            y_test_values = y_test

        # Calculate metrics
        ensemble_metrics = {
            'r2': r2_score(y_test_values, ensemble_pred),
            'mae': mean_absolute_error(y_test_values, ensemble_pred),
            'rmse': np.sqrt(mean_squared_error(y_test_values, ensemble_pred))
        }

        # Individual model metrics
        individual_metrics = {}
        for name, pred in predictions.items():
            individual_metrics[name] = {
                'r2': r2_score(y_test_values, pred),
                'mae': mean_absolute_error(y_test_values, pred),
                'rmse': np.sqrt(mean_squared_error(y_test_values, pred))
            }

        # Check if targets achieved
        target_achieved = (
            ensemble_metrics['r2'] >= model_config['target_r2'] and
            ensemble_metrics['mae'] <= model_config['target_mae']
        )

        result = {
            'model_name': model_name,
            'models': models,
            'scaler': scaler,
            'features': available_features,
            'ensemble_weights': weights,
            'ensemble_metrics': ensemble_metrics,
            'individual_metrics': individual_metrics,
            'target_achieved': target_achieved,
            'training_samples': len(X_train_scaled),
            'test_samples': len(X_test_scaled),
            'gpu_used': self.gpu_available
        }

        # Save model
        self.save_gpu_model(result)

        # Log results
        logger.info(f"📊 GPU-ENHANCED MODEL RESULTS:")
        logger.info(f"   Ensemble R²: {ensemble_metrics['r2']:.4f}")
        logger.info(f"   Ensemble MAE: {ensemble_metrics['mae']:.3f}")
        logger.info(f"   GPU used: {'✅' if self.gpu_available else '❌'}")
        logger.info(f"   Target achieved: {'✅' if target_achieved else '❌'}")

        for name, metrics in individual_metrics.items():
            gpu_status = "🎮" if models[name]['gpu_used'] else "💻"
            logger.info(f"   {name} {gpu_status}: R²={metrics['r2']:.4f}, MAE={metrics['mae']:.3f}")

        return result

    def train_pilot_models(self) -> Dict[str, Any]:
        """Train pilot models με GPU acceleration"""
        logger.info("🚀 STARTING GPU-ENHANCED PILOT TRAINING")
        logger.info("=" * 100)
        logger.info(f"GPU Status: {'✅ Available' if self.gpu_available else '❌ Not Available'}")
        if self.gpu_available:
            logger.info(f"GPU Memory: {self.device_info.get('free_memory_gb', 0):.1f}GB free")
        logger.info("=" * 100)

        # Load and prepare data
        raw_data = self.load_training_data()
        processed_data = self.engineer_gpu_features(raw_data)

        # Train pilot models
        results = {
            'training_start': datetime.now().isoformat(),
            'pipeline_version': self.pipeline_version,
            'gpu_used': self.gpu_available,
            'gpu_info': self.device_info,
            'total_models': len(self.pilot_models),
            'successful_models': 0,
            'target_achieved': 0,
            'models': {}
        }

        for model_name in self.pilot_models.keys():
            try:
                logger.info(f"\n🎯 Training GPU pilot model: {model_name}")

                result = self.train_gpu_ensemble_model(model_name, processed_data)

                if result:
                    results['models'][model_name] = result
                    results['successful_models'] += 1

                    if result['target_achieved']:
                        results['target_achieved'] += 1

            except Exception as e:
                logger.error(f"❌ Failed to train {model_name}: {e}")
                import traceback
                traceback.print_exc()
                continue

        # Generate summary
        results['training_end'] = datetime.now().isoformat()
        self.generate_gpu_summary(results)

        return results

    def generate_gpu_summary(self, results: Dict[str, Any]):
        """Generate GPU training summary"""
        logger.info(f"\n🎉 GPU-ENHANCED PILOT TRAINING COMPLETED!")
        logger.info("=" * 100)

        successful = results['successful_models']
        total = results['total_models']
        target_met = results['target_achieved']

        logger.info(f"📊 OVERALL RESULTS:")
        logger.info(f"   Successful models: {successful}/{total} ({successful/total*100:.1f}%)")
        logger.info(f"   Target achieved: {target_met}/{successful} ({target_met/successful*100:.1f}%)")
        logger.info(f"   GPU used: {'✅' if results['gpu_used'] else '❌'}")

        # Performance comparison
        for model_name, result in results['models'].items():
            logger.info(f"\n🎯 {model_name.upper()} RESULTS:")

            ensemble_metrics = result['ensemble_metrics']
            logger.info(f"   Ensemble Performance:")
            logger.info(f"     R²: {ensemble_metrics['r2']:.4f}")
            logger.info(f"     MAE: {ensemble_metrics['mae']:.3f}")
            logger.info(f"     RMSE: {ensemble_metrics['rmse']:.3f}")

            logger.info(f"   Individual Models:")
            for alg, metrics in result['individual_metrics'].items():
                gpu_icon = "🎮" if result['models'][alg]['gpu_used'] else "💻"
                logger.info(f"     {alg} {gpu_icon}: R²={metrics['r2']:.3f}, MAE={metrics['mae']:.2f}")

        # Save summary
        summary_path = self.output_dir / "gpu_enhanced_pilot_summary.json"
        with open(summary_path, 'w') as f:
            json.dump(results, f, indent=2, default=str)

        logger.info(f"\n💾 GPU training summary saved: {summary_path}")

def main():
    """Main GPU-enhanced pilot training function"""
    try:
        # Initialize GPU trainer
        trainer = GPUEnhancedPilotTrainer(
            output_dir="models/gpu_enhanced_pilot",
            pipeline_version="v1.0.0"
        )

        # Train pilot models
        results = trainer.train_pilot_models()

        logger.info("\n✅ GPU-ENHANCED PILOT TRAINING COMPLETED SUCCESSFULLY!")
        return results

    except Exception as e:
        logger.error(f"❌ GPU-enhanced pilot training failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()
