#!/usr/bin/env python3
"""
Enhanced Model v3 - Baseline Model Development
Phase 2: Create working baseline models with battery-aware features
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import pandas as pd
import numpy as np
import psycopg2
from dotenv import load_dotenv
import logging
from datetime import datetime
from sklearn.model_selection import train_test_split, TimeSeriesSplit
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import joblib
from pathlib import Path
import json

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BaselineModelDevelopment:
    """Baseline model development for Enhanced Model v3"""
    
    def __init__(self):
        self.output_dir = Path("models/enhanced_v3_improved")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def load_simple_dataset(self):
        """Load dataset with minimal filtering to get more data"""
        logger.info("🔍 Loading dataset with minimal filtering...")
        
        try:
            load_dotenv()
            conn = psycopg2.connect(
                host=os.getenv('DB_HOST', 'localhost'),
                database=os.getenv('DB_NAME', 'solar_prediction'),
                user=os.getenv('DB_USER', 'postgres'),
                password=os.getenv('DB_PASSWORD', 'postgres')
            )
            
            # Simple query with minimal filtering
            query = """
            WITH system1_data AS (
                SELECT
                    timestamp, ac_power, soc, bat_power, powerdc1, powerdc2, yield_today,
                    1 as system_id
                FROM solax_data
                WHERE timestamp >= '2024-03-01'
                AND ac_power IS NOT NULL 
                AND soc IS NOT NULL
                AND bat_power IS NOT NULL
            ),
            system2_data AS (
                SELECT
                    timestamp, ac_power, soc, bat_power, powerdc1, powerdc2, yield_today,
                    2 as system_id
                FROM solax_data2
                WHERE timestamp >= '2024-03-01'
                AND ac_power IS NOT NULL 
                AND soc IS NOT NULL
                AND bat_power IS NOT NULL
            )
            SELECT * FROM system1_data
            UNION ALL
            SELECT * FROM system2_data
            ORDER BY timestamp, system_id
            """
            
            df = pd.read_sql(query, conn)
            conn.close()
            
            initial_count = len(df)
            logger.info(f"✅ Initial data loaded: {initial_count:,} records")
            
            # Only remove clearly impossible values
            df = df[
                (df['ac_power'] >= 0) & (df['ac_power'] <= 15000) &
                (df['soc'] >= 0) & (df['soc'] <= 100) &
                (df['bat_power'] >= -8000) & (df['bat_power'] <= 8000)
            ]
            
            final_count = len(df)
            retention_rate = (final_count / initial_count) * 100
            
            logger.info(f"✅ Filtering complete:")
            logger.info(f"   Final records: {final_count:,}")
            logger.info(f"   Retention rate: {retention_rate:.1f}%")
            logger.info(f"   System 1: {len(df[df['system_id']==1]):,} records")
            logger.info(f"   System 2: {len(df[df['system_id']==2]):,} records")
            
            return df
            
        except Exception as e:
            logger.error(f"❌ Data loading failed: {e}")
            raise
    
    def create_simple_features(self, df):
        """Create simple but effective features"""
        logger.info("🔧 Creating simple battery-aware features...")
        
        # Basic temporal features
        df['hour'] = df['timestamp'].dt.hour
        df['month'] = df['timestamp'].dt.month
        df['day_of_year'] = df['timestamp'].dt.dayofyear
        df['is_weekend'] = df['timestamp'].dt.dayofweek.isin([5, 6]).astype(int)
        
        # Cyclical encoding for key temporal features
        df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
        df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
        
        # Battery features
        df['soc_normalized'] = df['soc'] / 100
        df['battery_mode'] = np.sign(df['bat_power'])  # Charging/discharging
        df['battery_utilization'] = np.abs(df['bat_power']) / 6000
        
        # System features
        df['is_system_1'] = (df['system_id'] == 1).astype(int)
        df['is_system_2'] = (df['system_id'] == 2).astype(int)
        
        # Production features
        df['dc_total'] = df['powerdc1'] + df['powerdc2']
        df['efficiency'] = np.where(df['dc_total'] > 0, df['ac_power'] / df['dc_total'], 0)
        
        # Time-based indicators
        df['is_daylight'] = df['hour'].between(6, 20).astype(int)
        df['is_peak_solar'] = df['hour'].between(10, 16).astype(int)
        df['is_evening'] = df['hour'].between(18, 22).astype(int)
        
        # Define feature columns for ML
        self.feature_columns = [
            'system_id', 'hour_sin', 'hour_cos', 'month_sin', 'month_cos',
            'day_of_year', 'is_weekend', 'soc_normalized', 'battery_mode',
            'battery_utilization', 'is_system_1', 'is_system_2', 'dc_total',
            'efficiency', 'is_daylight', 'is_peak_solar', 'is_evening'
        ]
        
        logger.info(f"✅ Created {len(self.feature_columns)} features")
        return df
    
    def train_baseline_models(self, df):
        """Train baseline models"""
        logger.info("🚀 Training baseline models...")
        
        # Prepare data
        df_clean = df.dropna(subset=self.feature_columns + ['ac_power'])
        X = df_clean[self.feature_columns]
        y = df_clean['ac_power']
        
        logger.info(f"Training data: {len(X):,} samples, {len(self.feature_columns)} features")
        
        # Time-based split
        split_date = df_clean['timestamp'].quantile(0.8)
        train_mask = df_clean['timestamp'] <= split_date
        test_mask = df_clean['timestamp'] > split_date
        
        X_train = X[train_mask]
        X_test = X[test_mask]
        y_train = y[train_mask]
        y_test = y[test_mask]
        
        logger.info(f"Train set: {len(X_train):,} samples")
        logger.info(f"Test set: {len(X_test):,} samples")
        
        # Train Random Forest model
        logger.info("Training Random Forest...")
        rf_model = RandomForestRegressor(
            n_estimators=100,
            max_depth=15,
            min_samples_split=10,
            min_samples_leaf=5,
            random_state=42,
            n_jobs=-1
        )
        
        rf_model.fit(X_train, y_train)
        
        # Predictions
        y_pred_train = rf_model.predict(X_train)
        y_pred_test = rf_model.predict(X_test)
        
        # Metrics
        train_metrics = {
            'rmse': np.sqrt(mean_squared_error(y_train, y_pred_train)),
            'mae': mean_absolute_error(y_train, y_pred_train),
            'r2': r2_score(y_train, y_pred_train)
        }
        
        test_metrics = {
            'rmse': np.sqrt(mean_squared_error(y_test, y_pred_test)),
            'mae': mean_absolute_error(y_test, y_pred_test),
            'r2': r2_score(y_test, y_pred_test)
        }
        
        # System-specific evaluation
        system_metrics = {}
        for system_id in [1, 2]:
            system_mask = X_test['system_id'] == system_id
            if system_mask.sum() > 0:
                y_test_sys = y_test[system_mask]
                y_pred_sys = y_pred_test[system_mask]
                
                system_metrics[f'system_{system_id}'] = {
                    'rmse': np.sqrt(mean_squared_error(y_test_sys, y_pred_sys)),
                    'mae': mean_absolute_error(y_test_sys, y_pred_sys),
                    'r2': r2_score(y_test_sys, y_pred_sys),
                    'samples': len(y_test_sys)
                }
        
        results = {
            'model_name': 'Random Forest Baseline',
            'train_metrics': train_metrics,
            'test_metrics': test_metrics,
            'system_metrics': system_metrics,
            'feature_count': len(self.feature_columns),
            'training_samples': len(X_train),
            'test_samples': len(X_test)
        }
        
        logger.info(f"✅ Random Forest Results:")
        logger.info(f"   Test R²: {test_metrics['r2']:.3f}")
        logger.info(f"   Test RMSE: {test_metrics['rmse']:.1f}W")
        logger.info(f"   Test MAE: {test_metrics['mae']:.1f}W")
        
        for system_id in [1, 2]:
            if f'system_{system_id}' in system_metrics:
                sys_r2 = system_metrics[f'system_{system_id}']['r2']
                logger.info(f"   System {system_id} R²: {sys_r2:.3f}")
        
        # Save model and results
        model_path = self.output_dir / "baseline_random_forest.joblib"
        joblib.dump(rf_model, model_path)
        
        results_path = self.output_dir / "baseline_results.json"
        with open(results_path, 'w') as f:
            json.dump(results, f, indent=2)
        
        features_path = self.output_dir / "baseline_features.json"
        with open(features_path, 'w') as f:
            json.dump(self.feature_columns, f, indent=2)
        
        return rf_model, results

def main():
    """Execute Phase 2: Baseline Model Development"""
    logger.info("🚀 ENHANCED MODEL V3 - PHASE 2: BASELINE MODEL DEVELOPMENT")
    logger.info("=" * 80)
    logger.info(f"📅 Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("🎯 Objective: Create working baseline with R² > 0.80")
    
    try:
        baseline = BaselineModelDevelopment()
        
        # Load dataset
        df = baseline.load_simple_dataset()
        
        # Create features
        df_features = baseline.create_simple_features(df)
        
        # Train baseline models
        model, results = baseline.train_baseline_models(df_features)
        
        logger.info("\n" + "=" * 80)
        logger.info("🎉 PHASE 2 COMPLETE: BASELINE MODEL DEVELOPMENT")
        logger.info(f"✅ Model R²: {results['test_metrics']['r2']:.3f}")
        logger.info(f"✅ Model RMSE: {results['test_metrics']['rmse']:.1f}W")
        logger.info(f"✅ Training samples: {results['training_samples']:,}")
        logger.info(f"✅ Features: {results['feature_count']}")
        
        if results['test_metrics']['r2'] > 0.5:
            logger.info("🚀 Ready for Phase 3: Advanced Battery-Aware Modeling")
        else:
            logger.info("⚠️ Need to improve baseline before proceeding")
        
        logger.info("=" * 80)
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Phase 2 failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
