#!/usr/bin/env python3
"""
Enhanced Model v3 - Smart Data Preparation
Phase 1: Battery-aware data preparation with balanced filtering
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import pandas as pd
import numpy as np
import psycopg2
from dotenv import load_dotenv
import logging
from datetime import datetime, timedelta
from sklearn.preprocessing import RobustScaler
import joblib
from pathlib import Path
import json

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SmartDataPreparation:
    """Smart data preparation for Enhanced Model v3"""
    
    def __init__(self):
        self.output_dir = Path("models/enhanced_v3_improved")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Battery-aware system configurations
        self.system_configs = {
            1: {
                'name': 'Σπίτι Πάνω',
                'consumption_pattern': 'low_steady',
                'avg_daily_consumption': 22.45,
                'battery_capacity': 12.0,
                'panel_capacity': 10.5,
                'grid_dependency': 0.0,
                'self_sufficiency': 1.0
            },
            2: {
                'name': 'Σπίτι Κάτω',
                'consumption_pattern': 'high_variable', 
                'avg_daily_consumption': 35.29,
                'battery_capacity': 12.0,
                'panel_capacity': 10.5,
                'grid_dependency': 0.395,
                'self_sufficiency': 0.605
            }
        }
        
    def load_unified_data(self):
        """Load data from both systems with balanced filtering"""
        logger.info("🔍 Loading unified data with balanced filtering...")
        
        try:
            load_dotenv()
            conn = psycopg2.connect(
                host=os.getenv('DB_HOST', 'localhost'),
                database=os.getenv('DB_NAME', 'solar_prediction'),
                user=os.getenv('DB_USER', 'postgres'),
                password=os.getenv('DB_PASSWORD', 'postgres')
            )
            
            # Much more lenient filtering to get more data
            query = """
            WITH system1_data AS (
                SELECT
                    timestamp,
                    COALESCE(ac_power, 0) as ac_power,
                    COALESCE(soc, 50) as soc,
                    COALESCE(bat_power, 0) as bat_power,
                    COALESCE(powerdc1, 0) as powerdc1,
                    COALESCE(powerdc2, 0) as powerdc2,
                    COALESCE(feedin_power, 0) as feedin_power,
                    COALESCE(consume_energy, 0) as consume_energy,
                    COALESCE(feedin_energy, 0) as feedin_energy,
                    COALESCE(yield_today, 0) as yield_today,
                    1 as system_id
                FROM solax_data
                WHERE timestamp >= '2024-03-01'
                AND timestamp IS NOT NULL
            ),
            system2_data AS (
                SELECT
                    timestamp,
                    COALESCE(ac_power, 0) as ac_power,
                    COALESCE(soc, 50) as soc,
                    COALESCE(bat_power, 0) as bat_power,
                    COALESCE(powerdc1, 0) as powerdc1,
                    COALESCE(powerdc2, 0) as powerdc2,
                    COALESCE(feedin_power, 0) as feedin_power,
                    COALESCE(consume_energy, 0) as consume_energy,
                    COALESCE(feedin_energy, 0) as feedin_energy,
                    COALESCE(yield_today, 0) as yield_today,
                    2 as system_id
                FROM solax_data2
                WHERE timestamp >= '2024-03-01'
                AND timestamp IS NOT NULL
            ),
            combined_systems AS (
                SELECT * FROM system1_data
                UNION ALL
                SELECT * FROM system2_data
            ),
            weather_data AS (
                SELECT
                    timestamp,
                    COALESCE(ghi, 400) as ghi,
                    COALESCE(dni, 300) as dni,
                    COALESCE(dhi, 100) as dhi,
                    COALESCE(temperature, 20) as temperature,
                    COALESCE(cloud_cover, 50) as cloud_cover
                FROM cams_radiation_data
                WHERE timestamp >= '2024-03-01'
            )
            SELECT
                cs.*,
                COALESCE(w.ghi, 400) as ghi,
                COALESCE(w.dni, 300) as dni,
                COALESCE(w.dhi, 100) as dhi,
                COALESCE(w.temperature, 20) as temperature,
                COALESCE(w.cloud_cover, 50) as cloud_cover
            FROM combined_systems cs
            LEFT JOIN weather_data w ON DATE_TRUNC('hour', cs.timestamp) = DATE_TRUNC('hour', w.timestamp)
            ORDER BY cs.timestamp, cs.system_id
            """
            
            df = pd.read_sql(query, conn)
            conn.close()
            
            initial_count = len(df)
            logger.info(f"✅ Initial data loaded: {initial_count:,} records")

            # Very gentle filtering - only remove extreme outliers
            # Remove only clearly impossible values
            df = df[
                (df['ac_power'] >= 0) & (df['ac_power'] <= 20000) &  # Very generous AC power range
                (df['soc'] >= 0) & (df['soc'] <= 100) &  # Valid SOC range
                (df['bat_power'] >= -10000) & (df['bat_power'] <= 10000)  # Very generous battery power
            ]

            # Calculate efficiency but don't filter on it initially
            df['dc_total'] = df['powerdc1'] + df['powerdc2']
            df['efficiency'] = np.where(df['dc_total'] > 0, df['ac_power'] / df['dc_total'], 1.0)

            # Only remove completely unrealistic efficiency values
            df = df[(df['efficiency'] >= 0) & (df['efficiency'] <= 2.0)]  # Very lenient efficiency
            
            final_count = len(df)
            retention_rate = (final_count / initial_count) * 100
            
            logger.info(f"✅ Balanced filtering complete:")
            logger.info(f"   Final records: {final_count:,}")
            logger.info(f"   Retention rate: {retention_rate:.1f}%")
            logger.info(f"   System 1: {len(df[df['system_id']==1]):,} records")
            logger.info(f"   System 2: {len(df[df['system_id']==2]):,} records")
            logger.info(f"   Date range: {df['timestamp'].min()} to {df['timestamp'].max()}")
            
            return df
            
        except Exception as e:
            logger.error(f"❌ Data loading failed: {e}")
            raise
    
    def engineer_battery_aware_features(self, df):
        """Engineer comprehensive battery-aware features"""
        logger.info("🔧 Engineering battery-aware features...")
        
        # Add system configurations
        for system_id in [1, 2]:
            mask = df['system_id'] == system_id
            config = self.system_configs[system_id]
            
            df.loc[mask, 'consumption_pattern'] = config['consumption_pattern']
            df.loc[mask, 'avg_daily_consumption'] = config['avg_daily_consumption']
            df.loc[mask, 'battery_capacity'] = config['battery_capacity']
            df.loc[mask, 'panel_capacity'] = config['panel_capacity']
            df.loc[mask, 'grid_dependency'] = config['grid_dependency']
            df.loc[mask, 'self_sufficiency'] = config['self_sufficiency']
        
        # 1. TEMPORAL FEATURES (9 features)
        df['hour'] = df['timestamp'].dt.hour
        df['day_of_year'] = df['timestamp'].dt.dayofyear
        df['month'] = df['timestamp'].dt.month
        df['day_of_week'] = df['timestamp'].dt.dayofweek
        
        # Cyclical encoding
        df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
        df['day_of_year_sin'] = np.sin(2 * np.pi * df['day_of_year'] / 365)
        df['day_of_year_cos'] = np.cos(2 * np.pi * df['day_of_year'] / 365)
        df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
        
        # Time-based indicators
        df['is_weekend'] = df['day_of_week'].isin([5, 6]).astype(int)
        df['is_daylight'] = df['hour'].between(6, 20).astype(int)
        df['is_peak_solar'] = df['hour'].between(10, 16).astype(int)
        
        # 2. BATTERY STATE FEATURES (8 features)
        df['soc_normalized'] = df['soc'] / 100
        df['battery_mode'] = np.sign(df['bat_power'])  # Charging(+1), Discharging(-1), Idle(0)
        df['battery_utilization'] = np.abs(df['bat_power']) / 6000  # Normalized to max capacity
        
        # Calculate SOC change rate
        df = df.sort_values(['system_id', 'timestamp'])
        df['soc_change_rate'] = df.groupby('system_id')['soc'].diff().fillna(0)
        
        # Time since full/empty (simplified)
        df['is_full_charge'] = (df['soc'] >= 95).astype(int)
        df['is_empty_charge'] = (df['soc'] <= 5).astype(int)
        
        # Expected battery state based on time of day
        df['expected_battery_state'] = np.where(
            df['hour'].between(10, 16), 0.8,  # High during solar hours
            np.where(df['hour'].between(18, 22), 0.4, 0.6)  # Lower during evening
        )
        
        df['battery_efficiency'] = np.where(
            df['bat_power'] != 0, 
            np.abs(df['bat_power']) / (np.abs(df['bat_power']) + 100), 
            0.95
        )
        
        # 3. SYSTEM-SPECIFIC FEATURES (6 features)
        df['consumption_profile_encoded'] = df['consumption_pattern'].map({
            'low_steady': 1, 'high_variable': 2
        })
        
        # Predicted consumption based on hour and system
        consumption_patterns = {
            1: {6: 0.5, 7: 0.7, 8: 0.6, 18: 1.2, 19: 1.5, 20: 1.3, 21: 1.0},  # Low steady
            2: {6: 0.8, 7: 1.2, 8: 1.0, 18: 2.0, 19: 2.5, 20: 2.2, 21: 1.8}   # High variable
        }
        
        df['consumption_prediction'] = df.apply(
            lambda row: consumption_patterns.get(row['system_id'], {}).get(row['hour'], 1.0),
            axis=1
        )
        
        df['system_efficiency'] = df['efficiency']  # Use calculated efficiency
        
        logger.info(f"✅ Battery-aware features engineered: {len([c for c in df.columns if c not in ['timestamp', 'system_id']])} total features")
        return df

def main():
    """Execute Phase 1: Smart Data Preparation"""
    logger.info("🚀 ENHANCED MODEL V3 - PHASE 1: SMART DATA PREPARATION")
    logger.info("=" * 80)
    logger.info(f"📅 Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("🎯 Objective: Create high-quality dataset with 40,000+ records")
    logger.info("🔋 Focus: Battery-aware feature engineering")
    
    try:
        prep = SmartDataPreparation()
        
        # Load unified data with balanced filtering
        df = prep.load_unified_data()
        
        # Engineer battery-aware features
        df_features = prep.engineer_battery_aware_features(df)
        
        # Save prepared dataset
        output_path = prep.output_dir / "smart_prepared_dataset.pkl"
        df_features.to_pickle(output_path)
        
        # Save feature info
        feature_info = {
            'total_records': len(df_features),
            'system_1_records': len(df_features[df_features['system_id'] == 1]),
            'system_2_records': len(df_features[df_features['system_id'] == 2]),
            'date_range': [str(df_features['timestamp'].min()), str(df_features['timestamp'].max())],
            'total_features': len(df_features.columns),
            'preparation_date': datetime.now().isoformat()
        }
        
        with open(prep.output_dir / "preparation_info.json", 'w') as f:
            json.dump(feature_info, f, indent=2)
        
        logger.info("\n" + "=" * 80)
        logger.info("🎉 PHASE 1 COMPLETE: SMART DATA PREPARATION")
        logger.info(f"✅ Dataset saved: {output_path}")
        logger.info(f"✅ Total records: {feature_info['total_records']:,}")
        logger.info(f"✅ System 1: {feature_info['system_1_records']:,} records")
        logger.info(f"✅ System 2: {feature_info['system_2_records']:,} records")
        logger.info(f"✅ Total features: {feature_info['total_features']}")
        logger.info("🚀 Ready for Phase 2: Baseline Model Development")
        logger.info("=" * 80)
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Phase 1 failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
