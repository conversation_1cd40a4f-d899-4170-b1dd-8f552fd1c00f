#!/usr/bin/env python3
"""
Enhanced Pilot Models Trainer
============================

Advanced training pipeline για τα 2 pilot models:
- spring_system1 (seasonal model)
- multi_horizon_daily_system1 (multi-horizon model)

Features:
- LSTM + XGBoost + RandomForest ensemble
- Hyperparameter optimization με GridSearchCV
- Advanced feature engineering
- Confidence intervals
- Comprehensive evaluation

Δημιουργήθηκε: 2025-06-05
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))

import pandas as pd
import numpy as np
import psycopg2
from pathlib import Path
from datetime import datetime, timedelta
import joblib
import json
from typing import Dict, List, Tuple, Any, Optional
import logging
import warnings
warnings.filterwarnings('ignore')

# Sklearn imports
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import GridSearchCV, train_test_split, cross_val_score, TimeSeriesSplit
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.preprocessing import StandardScaler

# XGBoost
import xgboost as xgb

# TensorFlow/Keras για LSTM
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout, BatchNormalization
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau

# Import unified preprocessing pipeline
from src.preprocessing.unified_pipeline import create_unified_pipeline

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedPilotModelsTrainer:
    """
    Enhanced trainer για pilot models με advanced algorithms
    """
    
    def __init__(self, output_dir: str = "models/enhanced_pilot", pipeline_version: str = "v1.0.0"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True, parents=True)
        
        # Initialize unified pipeline
        self.pipeline = create_unified_pipeline(pipeline_version)
        self.pipeline_version = pipeline_version
        
        # Pilot models configuration
        self.pilot_models = {
            'spring_system1': {
                'type': 'seasonal',
                'system_id': 1,
                'season': 'spring',
                'target_r2': 0.95,
                'target_mae': 2.5
            },
            'multi_horizon_daily_system1': {
                'type': 'multi_horizon',
                'system_id': 1,
                'horizon': 'daily',
                'target_r2': 0.90,
                'target_mae': 3.0
            }
        }
        
        # Algorithm configurations
        self.algorithms = {
            'RandomForest': {
                'model': RandomForestRegressor,
                'param_grid': {
                    'n_estimators': [100, 200, 300],
                    'max_depth': [10, 15, 20, None],
                    'min_samples_split': [2, 5, 10],
                    'min_samples_leaf': [1, 2, 4],
                    'max_features': ['sqrt', 'log2', None]
                }
            },
            'XGBoost': {
                'model': xgb.XGBRegressor,
                'param_grid': {
                    'n_estimators': [100, 200, 300],
                    'max_depth': [6, 8, 10],
                    'learning_rate': [0.01, 0.1, 0.2],
                    'subsample': [0.8, 0.9, 1.0],
                    'colsample_bytree': [0.8, 0.9, 1.0]
                }
            }
        }
        
        logger.info(f"🏗️ Initialized EnhancedPilotModelsTrainer with pipeline {pipeline_version}")
    
    def load_training_data(self) -> pd.DataFrame:
        """Load real training data από τη βάση δεδομένων"""
        logger.info("📊 Loading real training data from database...")
        
        try:
            conn = psycopg2.connect(
                host='localhost',
                database='solar_prediction',
                user='postgres',
                password='postgres'
            )
            
            # Enhanced query με περισσότερα features
            query = """
            WITH system1_data AS (
                SELECT 
                    s.timestamp,
                    s.yield_today,
                    s.soc,
                    s.bat_power,
                    s.temperature,
                    s.powerdc1,
                    s.powerdc2,
                    s.feedin_power,
                    s.consume_energy,
                    w.global_horizontal_irradiance,
                    w.temperature_2m,
                    w.relative_humidity_2m,
                    w.cloud_cover,
                    w.direct_radiation,
                    w.diffuse_radiation,
                    w.shortwave_radiation,
                    1 as system_id,
                    EXTRACT(MONTH FROM s.timestamp) as month,
                    EXTRACT(HOUR FROM s.timestamp) as hour,
                    EXTRACT(DOY FROM s.timestamp) as day_of_year
                FROM solax_data s
                LEFT JOIN weather_data w ON DATE_TRUNC('hour', s.timestamp) = DATE_TRUNC('hour', w.timestamp)
                WHERE s.timestamp >= '2024-03-01' 
                  AND s.yield_today IS NOT NULL
                  AND s.yield_today >= 0 
                  AND s.yield_today <= 100
                  AND w.global_horizontal_irradiance IS NOT NULL
            )
            SELECT * FROM system1_data
            ORDER BY timestamp
            """
            
            df = pd.read_sql(query, conn)
            conn.close()
            
            # Data cleaning
            df = df.fillna(method='forward').fillna(method='backward').fillna(0)
            df = df.drop_duplicates(subset=['timestamp'])
            
            logger.info(f"✅ Loaded {len(df):,} training records")
            logger.info(f"   Date range: {df['timestamp'].min()} to {df['timestamp'].max()}")
            
            return df
            
        except Exception as e:
            logger.error(f"❌ Failed to load training data: {e}")
            # Fallback to synthetic data
            return self.generate_synthetic_data()
    
    def generate_synthetic_data(self, n_samples: int = 5000) -> pd.DataFrame:
        """Generate synthetic training data για testing"""
        logger.info(f"🎭 Generating {n_samples} synthetic training samples...")
        
        np.random.seed(42)
        
        # Generate timestamps
        start_date = datetime(2024, 3, 1)
        timestamps = [start_date + timedelta(hours=i/12) for i in range(n_samples)]
        
        data = []
        for i, timestamp in enumerate(timestamps):
            hour = timestamp.hour
            month = timestamp.month
            day_of_year = timestamp.timetuple().tm_yday
            
            # Seasonal patterns
            seasonal_factor = 0.5 + 0.5 * np.sin(2 * np.pi * day_of_year / 365)
            
            # Daily patterns
            if 6 <= hour <= 18:
                hour_factor = np.sin(np.pi * (hour - 6) / 12)
            else:
                hour_factor = 0
            
            # Weather simulation
            base_temp = 15 + 10 * seasonal_factor + np.random.normal(0, 3)
            cloud_cover = np.random.uniform(0, 100)
            ghi = 1000 * hour_factor * (1 - cloud_cover/100) * seasonal_factor
            ghi = max(0, ghi + np.random.normal(0, 50))
            
            # Yield simulation
            yield_today = ghi * 0.05 * (1 + np.random.normal(0, 0.1))
            yield_today = max(0, min(100, yield_today))
            
            data.append({
                'timestamp': timestamp,
                'yield_today': yield_today,
                'soc': np.random.uniform(20, 90),
                'bat_power': np.random.normal(0, 500),
                'temperature': base_temp + np.random.normal(0, 2),
                'powerdc1': yield_today * 600,
                'powerdc2': yield_today * 600,
                'feedin_power': np.random.exponential(100),
                'consume_energy': np.random.exponential(200),
                'global_horizontal_irradiance': ghi,
                'temperature_2m': base_temp,
                'relative_humidity_2m': np.random.uniform(30, 90),
                'cloud_cover': cloud_cover,
                'direct_radiation': ghi * 0.8,
                'diffuse_radiation': ghi * 0.2,
                'shortwave_radiation': ghi,
                'system_id': 1,
                'month': month,
                'hour': hour,
                'day_of_year': day_of_year
            })
        
        df = pd.DataFrame(data)
        logger.info(f"✅ Generated synthetic data: {len(df):,} samples")
        
        return df
    
    def engineer_advanced_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Advanced feature engineering"""
        logger.info("🔧 Engineering advanced features...")
        
        # Start με unified pipeline
        processed_df = self.pipeline.engineer_features(df)
        
        # Add advanced features
        processed_df = processed_df.sort_values('timestamp').reset_index(drop=True)
        
        # Lag features
        for lag in [1, 6, 12, 24]:  # 5min, 30min, 1h, 2h lags
            processed_df[f'yield_lag_{lag}'] = processed_df['yield_today'].shift(lag)
            processed_df[f'ghi_lag_{lag}'] = processed_df['global_horizontal_irradiance'].shift(lag)
        
        # Rolling statistics
        for window in [12, 24, 72]:  # 1h, 2h, 6h windows
            processed_df[f'yield_rolling_mean_{window}'] = processed_df['yield_today'].rolling(window).mean()
            processed_df[f'yield_rolling_std_{window}'] = processed_df['yield_today'].rolling(window).std()
            processed_df[f'temp_rolling_mean_{window}'] = processed_df['temperature_2m'].rolling(window).mean()
            processed_df[f'ghi_rolling_mean_{window}'] = processed_df['global_horizontal_irradiance'].rolling(window).mean()
        
        # Weather derivatives
        processed_df['temp_change'] = processed_df['temperature_2m'].diff()
        processed_df['ghi_change'] = processed_df['global_horizontal_irradiance'].diff()
        processed_df['cloud_change'] = processed_df['cloud_cover'].diff()
        
        # Interaction features
        processed_df['temp_ghi_interaction'] = processed_df['temperature_2m'] * processed_df['global_horizontal_irradiance'] / 1000
        processed_df['cloud_temp_interaction'] = processed_df['cloud_cover'] * processed_df['temperature_2m'] / 100
        processed_df['soc_power_interaction'] = processed_df['soc'] * processed_df['bat_power'] / 1000
        
        # Solar position features (simplified)
        processed_df['sun_elevation'] = np.sin(2 * np.pi * processed_df['hour'] / 24) * np.sin(2 * np.pi * processed_df['day_of_year'] / 365)
        processed_df['sun_azimuth'] = np.cos(2 * np.pi * processed_df['hour'] / 24)
        
        # Seasonal trend
        processed_df['seasonal_trend'] = np.sin(2 * np.pi * processed_df['day_of_year'] / 365)
        processed_df['seasonal_trend_cos'] = np.cos(2 * np.pi * processed_df['day_of_year'] / 365)
        
        # Fill missing values
        processed_df = processed_df.fillna(method='forward').fillna(method='backward').fillna(0)
        
        logger.info(f"✅ Feature engineering complete: {processed_df.shape[1]} features")
        
        return processed_df
    
    def create_lstm_model(self, input_shape: Tuple[int, int]) -> tf.keras.Model:
        """Create LSTM model για time series prediction"""
        
        model = Sequential([
            LSTM(64, return_sequences=True, input_shape=input_shape),
            Dropout(0.2),
            BatchNormalization(),
            
            LSTM(32, return_sequences=False),
            Dropout(0.2),
            BatchNormalization(),
            
            Dense(16, activation='relu'),
            Dropout(0.1),
            
            Dense(1, activation='linear')
        ])
        
        model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss='mse',
            metrics=['mae']
        )
        
        return model
    
    def prepare_lstm_data(self, df: pd.DataFrame, feature_cols: List[str], 
                         sequence_length: int = 24) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare data για LSTM training"""
        
        # Scale features
        scaler = StandardScaler()
        scaled_features = scaler.fit_transform(df[feature_cols])
        
        # Create sequences
        X, y = [], []
        for i in range(sequence_length, len(scaled_features)):
            X.append(scaled_features[i-sequence_length:i])
            y.append(df['yield_today'].iloc[i])
        
        return np.array(X), np.array(y), scaler
    
    def optimize_hyperparameters(self, model_class, param_grid: Dict, 
                                X_train: np.ndarray, y_train: np.ndarray) -> Dict:
        """Optimize hyperparameters using GridSearchCV"""
        
        logger.info(f"🔍 Optimizing hyperparameters για {model_class.__name__}...")
        
        # Use TimeSeriesSplit για time series data
        tscv = TimeSeriesSplit(n_splits=5)
        
        grid_search = GridSearchCV(
            estimator=model_class(random_state=42),
            param_grid=param_grid,
            cv=tscv,
            scoring='neg_mean_absolute_error',
            n_jobs=-1,
            verbose=1
        )
        
        grid_search.fit(X_train, y_train)
        
        logger.info(f"✅ Best parameters: {grid_search.best_params_}")
        logger.info(f"✅ Best CV score: {-grid_search.best_score_:.4f}")
        
        return {
            'best_model': grid_search.best_estimator_,
            'best_params': grid_search.best_params_,
            'best_score': -grid_search.best_score_,
            'cv_results': grid_search.cv_results_
        }
    
    def train_ensemble_model(self, model_name: str, processed_data: pd.DataFrame) -> Dict[str, Any]:
        """Train ensemble model με RandomForest + XGBoost + LSTM"""
        
        logger.info(f"\n🎯 Training enhanced ensemble model: {model_name}")
        logger.info("=" * 80)
        
        model_config = self.pilot_models[model_name]
        
        # Filter data για seasonal models
        if model_config['type'] == 'seasonal':
            season_months = {'spring': [3, 4, 5]}[model_config['season']]
            model_data = processed_data[processed_data['month'].isin(season_months)].copy()
        else:
            model_data = processed_data.copy()
        
        if len(model_data) < 100:
            logger.error(f"❌ Insufficient data για {model_name}: {len(model_data)} records")
            return None
        
        # Feature selection
        if model_config['type'] == 'seasonal':
            base_features = ['hour_sin', 'hour_cos', 'temperature', 'cloud_cover', 'ghi', 'soc']
        else:
            base_features = ['soc', 'bat_power', 'temperature', 'ghi', 'air_temp', 'humidity', 'cloud_cover', 'hour']
        
        # Add advanced features
        advanced_features = [col for col in model_data.columns if any(x in col for x in 
            ['lag_', 'rolling_', 'change', 'interaction', 'sun_', 'seasonal_trend'])]
        
        all_features = base_features + advanced_features
        available_features = [f for f in all_features if f in model_data.columns]
        
        logger.info(f"📊 Using {len(available_features)} features για training")
        
        # Prepare data
        X = model_data[available_features].values
        y = model_data['yield_today'].values
        
        # Train/test split
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, shuffle=False  # No shuffle για time series
        )
        
        # Scale data
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        # Train individual models
        models = {}
        
        # 1. RandomForest με hyperparameter optimization
        logger.info("🌲 Training optimized RandomForest...")
        rf_result = self.optimize_hyperparameters(
            RandomForestRegressor, 
            self.algorithms['RandomForest']['param_grid'],
            X_train_scaled, y_train
        )
        models['RandomForest'] = rf_result['best_model']
        
        # 2. XGBoost με hyperparameter optimization
        logger.info("🚀 Training optimized XGBoost...")
        xgb_result = self.optimize_hyperparameters(
            xgb.XGBRegressor,
            self.algorithms['XGBoost']['param_grid'],
            X_train_scaled, y_train
        )
        models['XGBoost'] = xgb_result['best_model']
        
        # 3. LSTM model
        logger.info("🧠 Training LSTM model...")
        sequence_length = min(24, len(X_train) // 10)  # Adaptive sequence length
        
        if len(X_train) > sequence_length * 2:
            X_lstm, y_lstm, lstm_scaler = self.prepare_lstm_data(
                model_data.iloc[:len(X_train) + len(X_test)], 
                available_features, 
                sequence_length
            )
            
            if len(X_lstm) > 50:  # Minimum samples για LSTM
                lstm_model = self.create_lstm_model((sequence_length, len(available_features)))
                
                # Split LSTM data
                lstm_split = int(0.8 * len(X_lstm))
                X_lstm_train, X_lstm_test = X_lstm[:lstm_split], X_lstm[lstm_split:]
                y_lstm_train, y_lstm_test = y_lstm[:lstm_split], y_lstm[lstm_split:]
                
                # Train LSTM
                callbacks = [
                    EarlyStopping(patience=10, restore_best_weights=True),
                    ReduceLROnPlateau(patience=5, factor=0.5)
                ]
                
                lstm_model.fit(
                    X_lstm_train, y_lstm_train,
                    epochs=50,
                    batch_size=32,
                    validation_data=(X_lstm_test, y_lstm_test),
                    callbacks=callbacks,
                    verbose=0
                )
                
                models['LSTM'] = lstm_model
                models['LSTM_scaler'] = lstm_scaler
                logger.info("✅ LSTM training completed")
            else:
                logger.warning("⚠️ Insufficient data για LSTM, skipping...")
        else:
            logger.warning("⚠️ Insufficient data για LSTM sequences, skipping...")
        
        # Ensemble predictions
        predictions = {}
        
        # RandomForest predictions
        rf_pred = models['RandomForest'].predict(X_test_scaled)
        predictions['RandomForest'] = rf_pred
        
        # XGBoost predictions
        xgb_pred = models['XGBoost'].predict(X_test_scaled)
        predictions['XGBoost'] = xgb_pred
        
        # LSTM predictions (if available)
        if 'LSTM' in models and len(X_test) >= sequence_length:
            # Prepare LSTM test data
            test_start_idx = len(X_train)
            lstm_test_data = model_data.iloc[test_start_idx-sequence_length:test_start_idx+len(X_test)]
            
            if len(lstm_test_data) >= sequence_length + len(X_test):
                X_lstm_test_full, _, _ = self.prepare_lstm_data(
                    lstm_test_data, available_features, sequence_length
                )
                
                if len(X_lstm_test_full) >= len(X_test):
                    lstm_pred = models['LSTM'].predict(X_lstm_test_full[-len(X_test):])
                    predictions['LSTM'] = lstm_pred.flatten()
        
        # Weighted ensemble
        if 'LSTM' in predictions:
            # 3-model ensemble
            ensemble_pred = (0.4 * predictions['RandomForest'] + 
                           0.35 * predictions['XGBoost'] + 
                           0.25 * predictions['LSTM'])
            weights = {'RandomForest': 0.4, 'XGBoost': 0.35, 'LSTM': 0.25}
        else:
            # 2-model ensemble
            ensemble_pred = (0.6 * predictions['RandomForest'] + 
                           0.4 * predictions['XGBoost'])
            weights = {'RandomForest': 0.6, 'XGBoost': 0.4}
        
        # Calculate metrics
        ensemble_metrics = {
            'r2': r2_score(y_test, ensemble_pred),
            'mae': mean_absolute_error(y_test, ensemble_pred),
            'rmse': np.sqrt(mean_squared_error(y_test, ensemble_pred))
        }
        
        # Individual model metrics
        individual_metrics = {}
        for name, pred in predictions.items():
            individual_metrics[name] = {
                'r2': r2_score(y_test, pred),
                'mae': mean_absolute_error(y_test, pred),
                'rmse': np.sqrt(mean_squared_error(y_test, pred))
            }
        
        # Confidence intervals (από RandomForest)
        if hasattr(models['RandomForest'], 'estimators_'):
            tree_predictions = np.array([tree.predict(X_test_scaled) for tree in models['RandomForest'].estimators_])
            prediction_std = np.std(tree_predictions, axis=0)
            confidence_intervals = {
                'lower_80': ensemble_pred - 1.28 * prediction_std,
                'upper_80': ensemble_pred + 1.28 * prediction_std,
                'std': prediction_std
            }
        else:
            confidence_intervals = None
        
        # Check if targets achieved
        target_achieved = (
            ensemble_metrics['r2'] >= model_config['target_r2'] and
            ensemble_metrics['mae'] <= model_config['target_mae']
        )
        
        result = {
            'model_name': model_name,
            'models': models,
            'scaler': scaler,
            'features': available_features,
            'ensemble_weights': weights,
            'ensemble_metrics': ensemble_metrics,
            'individual_metrics': individual_metrics,
            'confidence_intervals': confidence_intervals,
            'target_achieved': target_achieved,
            'training_samples': len(X_train),
            'test_samples': len(X_test),
            'hyperparameter_results': {
                'RandomForest': rf_result,
                'XGBoost': xgb_result
            }
        }
        
        # Save enhanced model
        self.save_enhanced_model(result)
        
        # Log results
        logger.info(f"📊 ENHANCED MODEL RESULTS:")
        logger.info(f"   Ensemble R²: {ensemble_metrics['r2']:.4f}")
        logger.info(f"   Ensemble MAE: {ensemble_metrics['mae']:.3f}")
        logger.info(f"   Target achieved: {'✅' if target_achieved else '❌'}")
        
        for name, metrics in individual_metrics.items():
            logger.info(f"   {name} R²: {metrics['r2']:.4f}, MAE: {metrics['mae']:.3f}")
        
        return result

    def save_enhanced_model(self, result: Dict[str, Any]):
        """Save enhanced model με όλα τα components"""
        model_name = result['model_name']
        model_dir = self.output_dir / model_name
        model_dir.mkdir(exist_ok=True)

        # Save individual models
        for name, model in result['models'].items():
            if name == 'LSTM_scaler':
                continue

            if name == 'LSTM':
                model.save(model_dir / f"{name}_model.h5")
                if 'LSTM_scaler' in result['models']:
                    joblib.dump(result['models']['LSTM_scaler'], model_dir / f"{name}_scaler.joblib")
            else:
                joblib.dump(model, model_dir / f"{name}_model.joblib")

        # Save main scaler
        joblib.dump(result['scaler'], model_dir / "scaler.joblib")

        # Save ensemble metadata
        metadata = {
            'model_name': model_name,
            'model_type': 'enhanced_ensemble',
            'algorithms': list(result['models'].keys()),
            'ensemble_weights': result['ensemble_weights'],
            'features': result['features'],
            'performance': result['ensemble_metrics'],
            'individual_performance': result['individual_metrics'],
            'target_achieved': result['target_achieved'],
            'training_samples': result['training_samples'],
            'test_samples': result['test_samples'],
            'training_date': datetime.now().isoformat(),
            'pipeline_version': self.pipeline_version,
            'hyperparameter_optimization': True,
            'confidence_intervals_available': result['confidence_intervals'] is not None
        }

        with open(model_dir / "metadata.json", 'w') as f:
            json.dump(metadata, f, indent=2)

        # Save hyperparameter results
        with open(model_dir / "hyperparameter_results.json", 'w') as f:
            # Convert numpy types για JSON serialization
            hp_results = {}
            for alg, res in result['hyperparameter_results'].items():
                hp_results[alg] = {
                    'best_params': res['best_params'],
                    'best_score': float(res['best_score'])
                }
            json.dump(hp_results, f, indent=2)

        logger.info(f"💾 Saved enhanced model: {model_dir}")

    def train_pilot_models(self) -> Dict[str, Any]:
        """Train both pilot models"""
        logger.info("🚀 STARTING ENHANCED PILOT MODELS TRAINING")
        logger.info("=" * 100)
        logger.info("Target: 2 pilot models με advanced algorithms")
        logger.info("Algorithms: RandomForest + XGBoost + LSTM ensemble")
        logger.info("Features: Advanced feature engineering + hyperparameter optimization")
        logger.info("=" * 100)

        # Load and prepare data
        raw_data = self.load_training_data()
        processed_data = self.engineer_advanced_features(raw_data)

        # Train pilot models
        results = {
            'training_start': datetime.now().isoformat(),
            'pipeline_version': self.pipeline_version,
            'total_models': len(self.pilot_models),
            'successful_models': 0,
            'target_achieved': 0,
            'models': {}
        }

        for model_name in self.pilot_models.keys():
            try:
                logger.info(f"\n🎯 Training pilot model: {model_name}")

                result = self.train_ensemble_model(model_name, processed_data)

                if result:
                    results['models'][model_name] = result
                    results['successful_models'] += 1

                    if result['target_achieved']:
                        results['target_achieved'] += 1

            except Exception as e:
                logger.error(f"❌ Failed to train {model_name}: {e}")
                import traceback
                traceback.print_exc()
                continue

        # Generate summary
        results['training_end'] = datetime.now().isoformat()
        self.generate_pilot_summary(results)

        return results

    def generate_pilot_summary(self, results: Dict[str, Any]):
        """Generate comprehensive pilot training summary"""
        logger.info(f"\n🎉 ENHANCED PILOT TRAINING COMPLETED!")
        logger.info("=" * 100)

        successful = results['successful_models']
        total = results['total_models']
        target_met = results['target_achieved']

        logger.info(f"📊 OVERALL RESULTS:")
        logger.info(f"   Successful models: {successful}/{total} ({successful/total*100:.1f}%)")
        logger.info(f"   Target achieved: {target_met}/{successful} ({target_met/successful*100:.1f}%)")
        logger.info(f"   Pipeline version: {results['pipeline_version']}")

        # Detailed results per model
        for model_name, result in results['models'].items():
            logger.info(f"\n🎯 {model_name.upper()} RESULTS:")

            ensemble_metrics = result['ensemble_metrics']
            logger.info(f"   Ensemble Performance:")
            logger.info(f"     R²: {ensemble_metrics['r2']:.4f}")
            logger.info(f"     MAE: {ensemble_metrics['mae']:.3f}")
            logger.info(f"     RMSE: {ensemble_metrics['rmse']:.3f}")

            logger.info(f"   Individual Models:")
            for alg, metrics in result['individual_metrics'].items():
                logger.info(f"     {alg}: R²={metrics['r2']:.3f}, MAE={metrics['mae']:.2f}")

            logger.info(f"   Ensemble Weights: {result['ensemble_weights']}")
            logger.info(f"   Features used: {len(result['features'])}")
            logger.info(f"   Target achieved: {'✅' if result['target_achieved'] else '❌'}")

        # Performance comparison με original models
        logger.info(f"\n📈 PERFORMANCE IMPROVEMENTS:")

        # Original spring_system1: R²=0.938, MAE=3.12
        if 'spring_system1' in results['models']:
            spring_result = results['models']['spring_system1']
            original_r2, original_mae = 0.938, 3.12
            new_r2 = spring_result['ensemble_metrics']['r2']
            new_mae = spring_result['ensemble_metrics']['mae']

            r2_improvement = ((new_r2 - original_r2) / original_r2) * 100
            mae_improvement = ((original_mae - new_mae) / original_mae) * 100

            logger.info(f"   spring_system1:")
            logger.info(f"     R² improvement: {r2_improvement:+.1f}% ({original_r2:.3f} → {new_r2:.3f})")
            logger.info(f"     MAE improvement: {mae_improvement:+.1f}% ({original_mae:.2f} → {new_mae:.2f})")

        # Original multi_horizon_daily_system1: R²=0.844, MAE=3.16
        if 'multi_horizon_daily_system1' in results['models']:
            daily_result = results['models']['multi_horizon_daily_system1']
            original_r2, original_mae = 0.844, 3.16
            new_r2 = daily_result['ensemble_metrics']['r2']
            new_mae = daily_result['ensemble_metrics']['mae']

            r2_improvement = ((new_r2 - original_r2) / original_r2) * 100
            mae_improvement = ((original_mae - new_mae) / original_mae) * 100

            logger.info(f"   multi_horizon_daily_system1:")
            logger.info(f"     R² improvement: {r2_improvement:+.1f}% ({original_r2:.3f} → {new_r2:.3f})")
            logger.info(f"     MAE improvement: {mae_improvement:+.1f}% ({original_mae:.2f} → {new_mae:.2f})")

        # Save summary
        summary_path = self.output_dir / "enhanced_pilot_training_summary.json"
        with open(summary_path, 'w') as f:
            json.dump(results, f, indent=2, default=str)

        logger.info(f"\n💾 Enhanced pilot training summary saved: {summary_path}")

    def compare_with_original(self, model_name: str) -> Dict[str, Any]:
        """Compare enhanced model με original model"""
        logger.info(f"🔍 Comparing enhanced {model_name} with original...")

        # Load original model metadata
        if 'spring' in model_name:
            original_path = Path("models/seasonal_models/spring_system1/metadata.json")
        else:
            original_path = Path("models/multi_horizon_daily_system1/metadata.json")

        if not original_path.exists():
            logger.warning(f"⚠️ Original model metadata not found: {original_path}")
            return {}

        with open(original_path, 'r') as f:
            original_metadata = json.load(f)

        # Load enhanced model metadata
        enhanced_path = self.output_dir / model_name / "metadata.json"

        if not enhanced_path.exists():
            logger.warning(f"⚠️ Enhanced model metadata not found: {enhanced_path}")
            return {}

        with open(enhanced_path, 'r') as f:
            enhanced_metadata = json.load(f)

        # Calculate improvements
        original_r2 = original_metadata['performance']['r2']
        original_mae = original_metadata['performance']['mae']

        enhanced_r2 = enhanced_metadata['performance']['r2']
        enhanced_mae = enhanced_metadata['performance']['mae']

        r2_improvement = ((enhanced_r2 - original_r2) / original_r2) * 100
        mae_improvement = ((original_mae - enhanced_mae) / original_mae) * 100

        comparison = {
            'model_name': model_name,
            'original': {
                'r2': original_r2,
                'mae': original_mae,
                'algorithm': original_metadata.get('best_algorithm', 'RandomForest')
            },
            'enhanced': {
                'r2': enhanced_r2,
                'mae': enhanced_mae,
                'algorithms': enhanced_metadata['algorithms']
            },
            'improvements': {
                'r2_improvement_percent': r2_improvement,
                'mae_improvement_percent': mae_improvement,
                'r2_absolute': enhanced_r2 - original_r2,
                'mae_absolute': original_mae - enhanced_mae
            },
            'significant_improvement': r2_improvement > 5 and mae_improvement > 10
        }

        logger.info(f"📊 Comparison results για {model_name}:")
        logger.info(f"   R² improvement: {r2_improvement:+.1f}%")
        logger.info(f"   MAE improvement: {mae_improvement:+.1f}%")
        logger.info(f"   Significant improvement: {'✅' if comparison['significant_improvement'] else '❌'}")

        return comparison

def main():
    """Main enhanced pilot training function"""
    try:
        # Initialize enhanced trainer
        trainer = EnhancedPilotModelsTrainer(
            output_dir="models/enhanced_pilot",
            pipeline_version="v1.0.0"
        )

        # Train pilot models
        results = trainer.train_pilot_models()

        # Compare με original models
        comparisons = {}
        for model_name in trainer.pilot_models.keys():
            if model_name in results['models']:
                comparison = trainer.compare_with_original(model_name)
                if comparison:
                    comparisons[model_name] = comparison

        # Save comparisons
        if comparisons:
            comparison_path = trainer.output_dir / "model_comparisons.json"
            with open(comparison_path, 'w') as f:
                json.dump(comparisons, f, indent=2)

            logger.info(f"💾 Model comparisons saved: {comparison_path}")

        logger.info("\n✅ ENHANCED PILOT TRAINING COMPLETED SUCCESSFULLY!")
        return results

    except Exception as e:
        logger.error(f"❌ Enhanced pilot training failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()
