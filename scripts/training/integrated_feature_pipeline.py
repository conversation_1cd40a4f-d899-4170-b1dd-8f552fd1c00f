#!/usr/bin/env python3
"""
Integrated Feature Engineering & Selection Pipeline
==================================================

Complete pipeline combining advanced feature engineering with intelligent selection:
- 200+ feature engineering from solar, weather, battery, temporal data
- Intelligent feature selection to optimal subset
- Real data integration with solar prediction database
- Production-ready feature pipeline

Target: Transform raw data to optimal feature set for >98% accuracy
Created: June 6, 2025
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import numpy as np
import pandas as pd
import psycopg2
from psycopg2.extras import RealDictCursor
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Any, Optional
import joblib
import json
from pathlib import Path

# Import our feature engineering components
from scripts.training.advanced_feature_engineering import AdvancedFeatureEngineering
from scripts.training.feature_selection_pipeline import AdvancedFeatureSelector

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class IntegratedFeaturePipeline:
    """
    Integrated feature engineering and selection pipeline for solar prediction
    """
    
    def __init__(self, target_features: int = 40, output_dir: str = "models/features"):
        """
        Initialize integrated feature pipeline
        
        Args:
            target_features: Target number of features after selection
            output_dir: Directory to save feature pipeline artifacts
        """
        self.target_features = target_features
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize components
        self.feature_engineer = AdvancedFeatureEngineering()
        self.feature_selector = AdvancedFeatureSelector(target_features=target_features)
        
        # Pipeline state
        self.selected_features = []
        self.feature_metadata = {}
        self.pipeline_fitted = False
        
        logger.info("🔧 Integrated Feature Pipeline initialized")
        logger.info(f"   Target features: {target_features}")
        logger.info(f"   Output directory: {output_dir}")
    
    def load_solar_data(self, start_date: str = "2024-03-01", end_date: str = None) -> pd.DataFrame:
        """Load solar prediction data from database"""
        logger.info("📊 Loading solar data from database...")
        
        if end_date is None:
            end_date = datetime.now().strftime("%Y-%m-%d")
        
        try:
            conn = psycopg2.connect("postgresql://postgres:postgres@localhost/solar_prediction")
            
            query = """
            SELECT 
                s.timestamp,
                s.ac_power,
                s.yield_today,
                s.soc,
                s.bat_power,
                s.temperature as system_temp,
                s.wifi_sn,
                w.temperature_2m,
                w.cloud_cover,
                w.global_horizontal_irradiance as ghi,
                w.direct_normal_irradiance as dni,
                w.diffuse_horizontal_irradiance as dhi,
                w.wind_speed_10m,
                w.relative_humidity_2m,
                CASE WHEN s.wifi_sn = 'SRFQDPDN9W' THEN 1 ELSE 2 END as system_id
            FROM solax_data s
            LEFT JOIN weather_data w ON DATE_TRUNC('hour', s.timestamp) = DATE_TRUNC('hour', w.timestamp)
            WHERE s.timestamp >= %s
                AND s.timestamp <= %s
                AND s.ac_power IS NOT NULL
                AND s.yield_today IS NOT NULL
                AND w.temperature_2m IS NOT NULL
            ORDER BY s.timestamp
            """
            
            df = pd.read_sql(query, conn, params=[start_date, end_date])
            conn.close()
            
            # Convert timestamp to datetime
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            
            logger.info(f"✅ Loaded {len(df):,} records")
            logger.info(f"   Date range: {df['timestamp'].min()} to {df['timestamp'].max()}")
            logger.info(f"   Systems: {sorted(df['system_id'].unique())}")
            
            return df
            
        except Exception as e:
            logger.error(f"❌ Database loading failed: {e}")
            raise
    
    def prepare_target_variable(self, df: pd.DataFrame, target_type: str = "yield_hourly") -> pd.Series:
        """Prepare target variable for training"""
        logger.info(f"🎯 Preparing target variable: {target_type}")
        
        if target_type == "yield_hourly":
            # Calculate hourly yield difference
            df_sorted = df.sort_values(['system_id', 'timestamp'])
            df_sorted['yield_hourly'] = df_sorted.groupby('system_id')['yield_today'].diff()
            
            # Handle negative values (yield reset at midnight)
            df_sorted['yield_hourly'] = df_sorted['yield_hourly'].clip(lower=0)
            
            target = df_sorted['yield_hourly']
            
        elif target_type == "yield_daily":
            # Use daily total yield
            target = df['yield_today']
            
        elif target_type == "ac_power":
            # Use AC power output
            target = df['ac_power']
            
        else:
            raise ValueError(f"Unknown target type: {target_type}")
        
        # Remove outliers (values beyond 3 standard deviations)
        mean_val = target.mean()
        std_val = target.std()
        target_cleaned = target.clip(lower=mean_val - 3*std_val, upper=mean_val + 3*std_val)
        
        logger.info(f"   Target statistics:")
        logger.info(f"     Mean: {target_cleaned.mean():.2f}")
        logger.info(f"     Std: {target_cleaned.std():.2f}")
        logger.info(f"     Range: {target_cleaned.min():.2f} - {target_cleaned.max():.2f}")
        
        return target_cleaned
    
    def fit_pipeline(self, df: pd.DataFrame, target: pd.Series) -> Dict[str, Any]:
        """Fit the complete feature pipeline"""
        logger.info("🚀 Fitting integrated feature pipeline...")
        
        # Step 1: Feature Engineering
        logger.info("Step 1: Advanced Feature Engineering")
        df_features = self.feature_engineer.create_all_features(df)
        
        # Align target with features (after NaN removal)
        target_aligned = target.loc[df_features.index]
        
        # Step 2: Prepare features for selection
        feature_columns = [col for col in df_features.columns 
                          if col not in ['timestamp', 'wifi_sn', 'ac_power', 'yield_today']]
        
        X = df_features[feature_columns].copy()
        
        # Handle any remaining NaN values
        X = X.fillna(X.mean())
        
        logger.info(f"   Features for selection: {len(feature_columns)}")
        
        # Step 3: Feature Selection
        logger.info("Step 2: Intelligent Feature Selection")
        selected_features, selection_summary = self.feature_selector.select_optimal_features(X, target_aligned)
        
        # Store results
        self.selected_features = selected_features
        self.feature_metadata = {
            'feature_engineering_summary': self.feature_engineer.get_feature_summary(),
            'feature_selection_summary': selection_summary,
            'total_original_features': len(df.columns),
            'total_engineered_features': len(feature_columns),
            'final_selected_features': len(selected_features),
            'target_type': 'yield_hourly',  # Default
            'fit_timestamp': datetime.now().isoformat()
        }
        
        self.pipeline_fitted = True
        
        # Create final dataset
        final_features = df_features[['timestamp'] + selected_features].copy()
        final_features['target'] = target_aligned
        
        logger.info("✅ Pipeline fitting completed!")
        logger.info(f"   Original columns: {len(df.columns)}")
        logger.info(f"   Engineered features: {len(feature_columns)}")
        logger.info(f"   Selected features: {len(selected_features)}")
        logger.info(f"   Final dataset shape: {final_features.shape}")
        
        return {
            'features_dataset': final_features,
            'selected_features': selected_features,
            'metadata': self.feature_metadata
        }
    
    def transform_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Transform new data using fitted pipeline"""
        if not self.pipeline_fitted:
            raise ValueError("Pipeline must be fitted before transform")
        
        logger.info("🔄 Transforming data with fitted pipeline...")
        
        # Apply feature engineering
        df_features = self.feature_engineer.create_all_features(df)
        
        # Select only the fitted features
        available_features = [f for f in self.selected_features if f in df_features.columns]
        missing_features = [f for f in self.selected_features if f not in df_features.columns]
        
        if missing_features:
            logger.warning(f"⚠️ Missing features: {len(missing_features)}")
            for feature in missing_features[:5]:  # Show first 5
                logger.warning(f"   - {feature}")
        
        # Create final dataset
        final_features = df_features[['timestamp'] + available_features].copy()
        
        # Handle missing features by filling with zeros
        for feature in missing_features:
            final_features[feature] = 0.0
        
        logger.info(f"✅ Data transformed: {final_features.shape}")
        
        return final_features
    
    def save_pipeline(self, filepath: str = None) -> str:
        """Save the fitted pipeline"""
        if not self.pipeline_fitted:
            raise ValueError("Pipeline must be fitted before saving")
        
        if filepath is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filepath = self.output_dir / f"feature_pipeline_{timestamp}.joblib"
        
        pipeline_data = {
            'feature_engineer': self.feature_engineer,
            'feature_selector': self.feature_selector,
            'selected_features': self.selected_features,
            'feature_metadata': self.feature_metadata,
            'pipeline_fitted': self.pipeline_fitted
        }
        
        joblib.dump(pipeline_data, filepath)
        
        # Also save metadata as JSON
        metadata_path = str(filepath).replace('.joblib', '_metadata.json')
        with open(metadata_path, 'w') as f:
            json.dump(self.feature_metadata, f, indent=2, default=str)
        
        logger.info(f"💾 Pipeline saved to: {filepath}")
        logger.info(f"💾 Metadata saved to: {metadata_path}")
        
        return str(filepath)
    
    def load_pipeline(self, filepath: str):
        """Load a fitted pipeline"""
        logger.info(f"📂 Loading pipeline from: {filepath}")
        
        pipeline_data = joblib.load(filepath)
        
        self.feature_engineer = pipeline_data['feature_engineer']
        self.feature_selector = pipeline_data['feature_selector']
        self.selected_features = pipeline_data['selected_features']
        self.feature_metadata = pipeline_data['feature_metadata']
        self.pipeline_fitted = pipeline_data['pipeline_fitted']
        
        logger.info("✅ Pipeline loaded successfully")
        logger.info(f"   Selected features: {len(self.selected_features)}")
        logger.info(f"   Fit timestamp: {self.feature_metadata.get('fit_timestamp', 'Unknown')}")
    
    def get_pipeline_summary(self) -> Dict[str, Any]:
        """Get comprehensive pipeline summary"""
        if not self.pipeline_fitted:
            return {"status": "not_fitted"}
        
        return {
            "status": "fitted",
            "pipeline_metadata": self.feature_metadata,
            "selected_features": self.selected_features,
            "feature_categories": self.feature_engineer.feature_categories,
            "selection_method": self.feature_selector.selection_methods,
            "performance_summary": {
                "original_to_engineered_ratio": (
                    self.feature_metadata['total_engineered_features'] / 
                    self.feature_metadata['total_original_features']
                ),
                "engineered_to_selected_ratio": (
                    self.feature_metadata['final_selected_features'] / 
                    self.feature_metadata['total_engineered_features']
                ),
                "overall_reduction_ratio": (
                    self.feature_metadata['final_selected_features'] / 
                    self.feature_metadata['total_original_features']
                )
            }
        }

def main():
    """Test integrated feature pipeline with real solar data"""
    logger.info("🚀 Testing Integrated Feature Pipeline with Real Solar Data")
    logger.info("=" * 80)
    
    try:
        # Initialize pipeline
        pipeline = IntegratedFeaturePipeline(target_features=30)
        
        # Load real solar data
        df = pipeline.load_solar_data(start_date="2024-05-01", end_date="2024-05-31")
        
        if len(df) == 0:
            logger.error("❌ No data loaded - check database connection")
            return
        
        # Prepare target variable
        target = pipeline.prepare_target_variable(df, target_type="yield_hourly")
        
        # Fit pipeline
        results = pipeline.fit_pipeline(df, target)
        
        # Save pipeline
        pipeline_path = pipeline.save_pipeline()
        
        # Get summary
        summary = pipeline.get_pipeline_summary()
        
        # Display results
        logger.info("🎯 INTEGRATED FEATURE PIPELINE RESULTS")
        logger.info("=" * 80)
        logger.info(f"📊 Data Processing:")
        logger.info(f"   Original records: {len(df):,}")
        logger.info(f"   Final records: {len(results['features_dataset']):,}")
        logger.info(f"   Data retention: {len(results['features_dataset'])/len(df)*100:.1f}%")
        
        logger.info(f"\n🔧 Feature Engineering:")
        logger.info(f"   Original features: {summary['pipeline_metadata']['total_original_features']}")
        logger.info(f"   Engineered features: {summary['pipeline_metadata']['total_engineered_features']}")
        logger.info(f"   Engineering ratio: {summary['performance_summary']['original_to_engineered_ratio']:.1f}x")
        
        logger.info(f"\n🎯 Feature Selection:")
        logger.info(f"   Selected features: {summary['pipeline_metadata']['final_selected_features']}")
        logger.info(f"   Selection ratio: {summary['performance_summary']['engineered_to_selected_ratio']:.1%}")
        logger.info(f"   Overall reduction: {summary['performance_summary']['overall_reduction_ratio']:.1%}")
        
        logger.info(f"\n📁 Output:")
        logger.info(f"   Pipeline saved: {pipeline_path}")
        logger.info(f"   Final dataset shape: {results['features_dataset'].shape}")
        
        logger.info(f"\n🏆 Top 10 Selected Features:")
        for i, feature in enumerate(results['selected_features'][:10], 1):
            logger.info(f"   {i:2d}. {feature}")
        
        logger.info("\n✅ Integrated Feature Pipeline test completed successfully!")
        
        return results, summary
        
    except Exception as e:
        logger.error(f"❌ Pipeline test failed: {e}")
        import traceback
        traceback.print_exc()
        return None, None

if __name__ == "__main__":
    results, summary = main()
