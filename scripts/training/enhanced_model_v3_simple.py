#!/usr/bin/env python3
"""
Enhanced Model v3 - Simple Implementation
Focus on working with available data to achieve >95% accuracy
"""

import pandas as pd
import numpy as np
import joblib
import json
import os
from datetime import datetime, timedelta
from pathlib import Path
from sklearn.model_selection import TimeSeriesSplit
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import r2_score, mean_absolute_error
import lightgbm as lgb
import warnings
warnings.filterwarnings('ignore')

def load_simple_data():
    """Load available data with simple approach"""
    print("📊 LOADING AVAILABLE DATA")
    print("=" * 50)
    
    try:
        import psycopg2
        
        conn = psycopg2.connect(
            host=os.getenv('DB_HOST', 'localhost'),
            database=os.getenv('DB_NAME', 'solar_prediction'),
            user=os.getenv('DB_USER', 'postgres'),
            password=os.getenv('DB_PASSWORD', 'postgres')
        )
        
        # Simple query with available data
        query = """
        SELECT 
            s1.timestamp,
            s1.ac_power as system1_ac_power,
            s1.soc as system1_soc,
            s1.yield_today as system1_yield_today,
            s2.ac_power as system2_ac_power,
            s2.soc as system2_soc,
            s2.yield_today as system2_yield_today,
            w.temperature_2m,
            w.cloud_cover,
            w.global_horizontal_irradiance,
            w.solar_elevation_angle
        FROM solax_data s1
        LEFT JOIN solax_data2 s2 ON DATE_TRUNC('hour', s1.timestamp) = DATE_TRUNC('hour', s2.timestamp)
        LEFT JOIN weather_data w ON DATE_TRUNC('hour', s1.timestamp) = DATE_TRUNC('hour', w.timestamp)
        WHERE s1.timestamp >= '2024-03-01'
        AND s1.ac_power IS NOT NULL
        AND s2.ac_power IS NOT NULL
        AND w.temperature_2m IS NOT NULL
        AND w.global_horizontal_irradiance IS NOT NULL
        ORDER BY s1.timestamp
        LIMIT 100000
        """
        
        df = pd.read_sql(query, conn)
        conn.close()
        
        print(f"✅ Loaded {len(df):,} records")
        print(f"📅 Date range: {df['timestamp'].min()} to {df['timestamp'].max()}")
        
        return df
        
    except Exception as e:
        print(f"❌ Failed to load data: {e}")
        return None

def create_simple_features(df):
    """Create simple but effective features"""
    print("\n🔧 CREATING SIMPLE FEATURES")
    print("=" * 50)
    
    # Convert timestamp
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    df = df.set_index('timestamp')
    
    # Basic temporal features
    df['hour'] = df.index.hour
    df['day_of_year'] = df.index.dayofyear
    df['month'] = df.index.month
    df['is_weekend'] = (df.index.dayofweek >= 5).astype(int)
    
    # Cyclical encoding
    df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
    df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
    df['day_sin'] = np.sin(2 * np.pi * df['day_of_year'] / 365)
    df['day_cos'] = np.cos(2 * np.pi * df['day_of_year'] / 365)
    
    # Weather normalization
    df['temp_norm'] = (df['temperature_2m'] - 20) / 20  # Normalize around 20°C
    df['cloud_norm'] = df['cloud_cover'] / 100.0
    df['ghi_norm'] = df['global_horizontal_irradiance'] / 1000.0
    df['elevation_norm'] = df['solar_elevation_angle'] / 90.0
    
    # Simple interactions
    df['ghi_elevation'] = df['ghi_norm'] * df['elevation_norm']
    df['clear_sky_temp'] = df['temp_norm'] * (1 - df['cloud_norm'])
    
    # System features
    df['soc_avg'] = (df['system1_soc'] + df['system2_soc']) / 2
    df['yield_total'] = df['system1_yield_today'] + df['system2_yield_today']
    
    # Remove missing values
    df = df.dropna()
    
    print(f"✅ Created features, final dataset: {len(df):,} records")
    
    return df

def train_enhanced_model(df, system='system1'):
    """Train enhanced model for specific system"""
    print(f"\n🤖 TRAINING ENHANCED MODEL FOR {system.upper()}")
    print("=" * 60)
    
    # Define features
    feature_columns = [
        'hour', 'day_of_year', 'month', 'is_weekend',
        'hour_sin', 'hour_cos', 'day_sin', 'day_cos',
        'temp_norm', 'cloud_norm', 'ghi_norm', 'elevation_norm',
        'ghi_elevation', 'clear_sky_temp', 'soc_avg', 'yield_total'
    ]
    
    target_column = f'{system}_ac_power'
    
    # Prepare data
    X = df[feature_columns].values
    y = df[target_column].values
    
    print(f"📊 Features: {len(feature_columns)}")
    print(f"📊 Samples: {len(X):,}")
    print(f"🎯 Target range: {y.min():.1f}W to {y.max():.1f}W")
    
    # Time series split (80% train, 20% test)
    split_idx = int(len(X) * 0.8)
    X_train, X_test = X[:split_idx], X[split_idx:]
    y_train, y_test = y[:split_idx], y[split_idx:]
    
    print(f"📊 Train: {len(X_train):,}, Test: {len(X_test):,}")
    
    # Scale features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # Test multiple algorithms
    algorithms = {
        'LightGBM': lgb.LGBMRegressor(
            n_estimators=1000,
            learning_rate=0.1,
            max_depth=8,
            num_leaves=31,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=42,
            verbose=-1
        ),
        'RandomForest': RandomForestRegressor(
            n_estimators=500,
            max_depth=15,
            min_samples_split=5,
            random_state=42,
            n_jobs=-1
        )
    }
    
    best_model = None
    best_scaler = None
    best_score = 0
    best_name = ""
    
    for name, model in algorithms.items():
        print(f"\n🔄 Testing {name}...")
        
        # Train
        model.fit(X_train_scaled, y_train)
        
        # Predict
        y_pred_train = model.predict(X_train_scaled)
        y_pred_test = model.predict(X_test_scaled)
        
        # Metrics
        train_r2 = r2_score(y_train, y_pred_train)
        test_r2 = r2_score(y_test, y_pred_test)
        test_mae = mean_absolute_error(y_test, y_pred_test)
        
        print(f"   Train R²: {train_r2:.4f} ({train_r2*100:.1f}%)")
        print(f"   Test R²:  {test_r2:.4f} ({test_r2*100:.1f}%)")
        print(f"   Test MAE: {test_mae:.1f}W")
        
        if test_r2 > best_score:
            best_score = test_r2
            best_model = model
            best_scaler = scaler
            best_name = name
    
    print(f"\n🏆 BEST MODEL: {best_name}")
    print(f"🎯 Best Test R²: {best_score:.4f} ({best_score*100:.1f}%)")
    
    # Check if meets target
    if best_score >= 0.95:
        print(f"✅ MEETS TARGET ACCURACY (>95%)")
    else:
        print(f"⚠️ BELOW TARGET ACCURACY (>95%)")
    
    return best_model, best_scaler, best_score, feature_columns

def save_enhanced_model(model, scaler, score, features, system):
    """Save the enhanced model"""
    print(f"\n💾 SAVING ENHANCED MODEL FOR {system.upper()}")
    print("=" * 50)
    
    # Create model directory
    model_dir = Path("models/enhanced_model_v3_production")
    model_dir.mkdir(exist_ok=True)
    
    # Save model and scaler
    model_file = model_dir / f"{system}_model.joblib"
    scaler_file = model_dir / f"{system}_scaler.joblib"
    
    joblib.dump(model, model_file)
    joblib.dump(scaler, scaler_file)
    
    # Save metadata
    metadata = {
        "model_name": "Enhanced Model v3 Production",
        "version": "3.0.0",
        "created_at": datetime.now().isoformat(),
        "system": system,
        "test_r2": score,
        "test_accuracy_percent": score * 100,
        "feature_columns": features,
        "algorithm": type(model).__name__,
        "target_accuracy_met": score >= 0.95
    }
    
    metadata_file = model_dir / f"{system}_metadata.json"
    with open(metadata_file, 'w') as f:
        json.dump(metadata, f, indent=2)
    
    print(f"✅ Model saved: {model_file}")
    print(f"✅ Scaler saved: {scaler_file}")
    print(f"✅ Metadata saved: {metadata_file}")
    
    return model_dir

def test_model_predictions(model_dir, system, df):
    """Test model with real predictions"""
    print(f"\n🧪 TESTING MODEL PREDICTIONS FOR {system.upper()}")
    print("=" * 60)
    
    try:
        # Load model
        model_file = model_dir / f"{system}_model.joblib"
        scaler_file = model_dir / f"{system}_scaler.joblib"
        metadata_file = model_dir / f"{system}_metadata.json"
        
        model = joblib.load(model_file)
        scaler = joblib.load(scaler_file)
        
        with open(metadata_file, 'r') as f:
            metadata = json.load(f)
        
        features = metadata['feature_columns']
        
        # Test with recent data
        test_data = df.tail(100)  # Last 100 records
        X_test = test_data[features].values
        y_actual = test_data[f'{system}_ac_power'].values
        
        # Scale and predict
        X_test_scaled = scaler.transform(X_test)
        y_pred = model.predict(X_test_scaled)
        
        # Calculate metrics
        r2 = r2_score(y_actual, y_pred)
        mae = mean_absolute_error(y_actual, y_pred)
        
        print(f"📊 Test Results on Recent Data:")
        print(f"   R² Score: {r2:.4f} ({r2*100:.1f}%)")
        print(f"   MAE: {mae:.1f}W")
        print(f"   Actual range: {y_actual.min():.1f}W to {y_actual.max():.1f}W")
        print(f"   Predicted range: {y_pred.min():.1f}W to {y_pred.max():.1f}W")
        
        # Show sample predictions
        print(f"\n📋 Sample Predictions:")
        for i in range(min(5, len(y_actual))):
            print(f"   Actual: {y_actual[i]:>6.1f}W, Predicted: {y_pred[i]:>6.1f}W, Error: {abs(y_actual[i]-y_pred[i]):>5.1f}W")
        
        return r2, mae
        
    except Exception as e:
        print(f"❌ Testing failed: {e}")
        return 0, 0

def main():
    """Main training function"""
    print("🚀 ENHANCED MODEL V3 - SIMPLE PRODUCTION TRAINING")
    print("=" * 80)
    print("🎯 Target: >95% accuracy")
    print("📊 Simple but effective approach")
    print("=" * 80)
    
    # Load data
    df = load_simple_data()
    if df is None or len(df) == 0:
        print("❌ No data available")
        return False
    
    # Create features
    df = create_simple_features(df)
    if len(df) == 0:
        print("❌ No data after feature engineering")
        return False
    
    # Train models for both systems
    results = {}
    
    for system in ['system1', 'system2']:
        model, scaler, score, features = train_enhanced_model(df, system)
        model_dir = save_enhanced_model(model, scaler, score, features, system)
        test_r2, test_mae = test_model_predictions(model_dir, system, df)
        
        results[system] = {
            'score': score,
            'test_r2': test_r2,
            'test_mae': test_mae,
            'model_dir': model_dir
        }
    
    # Summary
    print(f"\n🎉 ENHANCED MODEL V3 TRAINING COMPLETED!")
    print("=" * 80)
    
    for system, result in results.items():
        print(f"\n📊 {system.upper()} RESULTS:")
        print(f"   Training R²: {result['score']:.4f} ({result['score']*100:.1f}%)")
        print(f"   Test R²: {result['test_r2']:.4f} ({result['test_r2']*100:.1f}%)")
        print(f"   Test MAE: {result['test_mae']:.1f}W")
        
        if result['score'] >= 0.95:
            print(f"   ✅ MEETS TARGET ACCURACY")
        else:
            print(f"   ⚠️ NEEDS IMPROVEMENT")
    
    avg_score = np.mean([r['score'] for r in results.values()])
    print(f"\n🏆 OVERALL AVERAGE ACCURACY: {avg_score:.4f} ({avg_score*100:.1f}%)")
    
    if avg_score >= 0.95:
        print(f"🎯 ✅ TARGET ACHIEVED!")
    else:
        print(f"🎯 ⚠️ TARGET NOT MET - NEEDS MORE WORK")
    
    return True

if __name__ == "__main__":
    main()
