#!/usr/bin/env python3
"""
Enhanced Solar Model Training with Database Storage
Trains enhanced ML models with database storage to avoid permission issues
"""

import sys
import os
import tempfile
import json
import numpy as np
import pandas as pd
import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime, timed<PERSON><PERSON>
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import r2_score, mean_absolute_error, mean_squared_error
import joblib
from io import BytesIO
import warnings
warnings.filterwarnings('ignore')

# Fix matplotlib cache issues
os.environ['MPLCONFIGDIR'] = tempfile.mkdtemp(prefix='matplotlib_')

# Fix LightGBM GPU issues with fallback
try:
    import lightgbm as lgb
    USE_LIGHTGBM = True
    print("🚀 LightGBM available")
except ImportError:
    USE_LIGHTGBM = False
    print("⚠️ LightGBM not available, using GradientBoosting")

def create_ml_models_table(conn):
    """Create ml_models table if it doesn't exist"""
    try:
        cur = conn.cursor()
        cur.execute("""
            CREATE TABLE IF NOT EXISTS ml_models (
                id SERIAL PRIMARY KEY,
                system_id INTEGER NOT NULL,
                model_type VARCHAR(50) NOT NULL,
                model_data BYTEA NOT NULL,
                metadata JSONB,
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW()
            )
        """)
        
        # Create index for faster lookups
        cur.execute("""
            CREATE INDEX IF NOT EXISTS idx_ml_models_system_type 
            ON ml_models(system_id, model_type)
        """)
        
        conn.commit()
        print("✅ ML models table ready")
        return True
    except Exception as e:
        print(f"❌ Error creating ml_models table: {e}")
        return False

def save_model_to_database(model, scaler, system_id, model_type, metadata, conn):
    """Save model and scaler to database"""
    try:
        # Serialize model and scaler separately
        model_buffer = BytesIO()
        joblib.dump(model, model_buffer)
        model_binary = model_buffer.getvalue()

        scaler_buffer = BytesIO()
        joblib.dump(scaler, scaler_buffer)
        scaler_binary = scaler_buffer.getvalue()

        cur = conn.cursor()

        # Delete existing model of same type for this system
        cur.execute("""
            DELETE FROM ml_models
            WHERE system_id = %s AND model_type = %s
        """, (system_id, model_type))

        # Insert new model with all required fields
        model_name = f"enhanced_system_{system_id}_{model_type}"
        cur.execute("""
            INSERT INTO ml_models (
                system_id, model_type, model_name, model_data,
                scaler_data, metadata, is_active
            )
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        """, (system_id, model_type, model_name, model_binary,
              scaler_binary, json.dumps(metadata), True))

        conn.commit()
        print(f"💾 Model saved to database: {system_id}/{model_type}")
        return True

    except Exception as e:
        print(f"❌ Error saving model to database: {e}")
        return False

def load_model_from_database(system_id, model_type, conn):
    """Load model and scaler from database"""
    try:
        cur = conn.cursor()
        cur.execute("""
            SELECT model_data, scaler_data, metadata FROM ml_models
            WHERE system_id = %s AND model_type = %s AND is_active = true
            ORDER BY created_at DESC LIMIT 1
        """, (system_id, model_type))

        result = cur.fetchone()
        if result:
            model_data, scaler_data, metadata = result

            # Load model
            model_buffer = BytesIO(model_data)
            model = joblib.load(model_buffer)

            # Load scaler
            scaler = None
            if scaler_data:
                scaler_buffer = BytesIO(scaler_data)
                scaler = joblib.load(scaler_buffer)

            return model, scaler, metadata
        return None, None, None

    except Exception as e:
        print(f"❌ Error loading model from database: {e}")
        return None, None, None

def train_enhanced_model_database(system_id=1):
    """Train enhanced model with database storage"""
    print(f"🔄 Training enhanced model for System {system_id} with database storage")
    
    try:
        # Database configuration
        db_config = {
            'host': os.getenv('DATABASE_HOST', 'postgres'),
            'port': int(os.getenv('DATABASE_PORT', '5432')),
            'database': 'solar_prediction',
            'user': 'postgres',
            'password': 'postgres'
        }
        
        # Connect to database
        conn = psycopg2.connect(**db_config)
        print("✅ Database connection successful")
        
        # ML models table already exists
        print("✅ Using existing ml_models table")
        
        # Training data
        end_date = datetime.now()
        start_date = end_date - timedelta(days=90)
        
        # Determine table name based on system_id
        table_name = 'solax_data' if system_id == 1 else 'solax_data2'
        
        query = f"""
        SELECT 
            timestamp,
            ac_power,
            EXTRACT(hour FROM timestamp) as hour,
            EXTRACT(day FROM timestamp) as day,
            temperature,
            soc,
            bat_power,
            yield_today,
            powerdc1,
            powerdc2
        FROM {table_name}
        WHERE timestamp BETWEEN %s AND %s
        AND ac_power IS NOT NULL
        AND ac_power >= 0
        ORDER BY timestamp
        """
        
        df = pd.read_sql_query(query, conn, params=(start_date, end_date))
        
        if len(df) < 50:
            print(f"❌ Insufficient training data: {len(df)} records from {table_name}")
            conn.close()
            return False
        
        print(f"✅ Loaded {len(df):,} training records from {table_name}")
        
        # Feature engineering
        features = ['hour', 'day', 'temperature', 'soc', 'bat_power', 'yield_today']
        available_features = [f for f in features if f in df.columns and df[f].notna().sum() > 0]
        
        if len(available_features) < 3:
            print(f"❌ Insufficient features: {available_features}")
            conn.close()
            return False
        
        # Prepare data
        X = df[available_features].fillna(0)
        y = df['ac_power']
        
        # Remove invalid data
        mask = (y >= 0) & (y <= 15000)  # Reasonable power range
        X = X[mask]
        y = y[mask]
        
        if len(X) < 30:
            print(f"❌ Insufficient valid data: {len(X)} records")
            conn.close()
            return False
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42
        )
        
        # Scale features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        # Train model with GPU fallback
        if USE_LIGHTGBM:
            try:
                model = lgb.LGBMRegressor(
                    device='gpu',
                    gpu_platform_id=0,
                    gpu_device_id=0,
                    n_estimators=100,
                    learning_rate=0.1,
                    max_depth=6,
                    random_state=42
                )
                model.fit(X_train_scaled, y_train)
                print("🚀 Using LightGBM with GPU")
            except Exception as gpu_error:
                print(f"⚠️ GPU not available: {gpu_error}")
                model = lgb.LGBMRegressor(
                    device='cpu',
                    n_estimators=100,
                    learning_rate=0.1,
                    max_depth=6,
                    random_state=42
                )
                model.fit(X_train_scaled, y_train)
                print("🔄 Fallback to LightGBM CPU mode")
        else:
            model = GradientBoostingRegressor(
                n_estimators=100,
                learning_rate=0.1,
                max_depth=6,
                random_state=42
            )
            model.fit(X_train_scaled, y_train)
            print("🔄 Using GradientBoosting")
        
        # Evaluate
        y_pred = model.predict(X_test_scaled)
        
        # Calculate metrics
        metrics = {
            'r2': float(r2_score(y_test, y_pred)),
            'mae': float(mean_absolute_error(y_test, y_pred)),
            'rmse': float(np.sqrt(mean_squared_error(y_test, y_pred))),
            'training_samples': int(len(X_train)),
            'test_samples': int(len(X_test)),
            'features': list(available_features),
            'system_id': int(system_id),
            'training_date': datetime.now().isoformat(),
            'model_type': 'enhanced_database_storage',
            'data_source': table_name
        }
        
        print(f"📊 Model Performance:")
        print(f"   R²: {metrics['r2']:.4f}")
        print(f"   MAE: {metrics['mae']:.2f} W")
        print(f"   RMSE: {metrics['rmse']:.2f} W")
        
        # Save model to database
        success = save_model_to_database(
            model, scaler, system_id,
            "enhanced_database", metrics, conn
        )
        
        conn.close()
        
        if success:
            print(f"✅ Enhanced model training completed for System {system_id}")
            return metrics['r2'] > 0.5
        else:
            print(f"❌ Failed to save model for System {system_id}")
            return False
            
    except Exception as e:
        print(f"❌ Training error: {e}")
        return False

if __name__ == "__main__":
    print("🌞 Enhanced Solar Model Training (Database Storage)")
    print("=" * 60)
    print("🎯 Target: Enhanced ML models with database storage")
    print("🔧 Pipeline: Avoid permission issues with database storage")
    print("🚀 Features: GPU fallback, matplotlib cache fix")
    print("=" * 60)
    print()
    
    success_count = 0
    for system_id in [1, 2]:
        try:
            if train_enhanced_model_database(system_id):
                success_count += 1
        except Exception as e:
            print(f"❌ Training failed for System {system_id}: {e}")
        print()
    
    print(f"🎉 Training completed: {success_count}/2 systems successful")
    if success_count == 2:
        print("✅ All systems trained successfully!")
    elif success_count > 0:
        print("⚠️ Some systems trained successfully")
    else:
        print("❌ All training attempts failed")
