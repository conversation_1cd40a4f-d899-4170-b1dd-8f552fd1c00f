#!/usr/bin/env python3
"""
Advanced Feature Selection Pipeline
==================================

Intelligent feature selection for optimal model performance:
- Correlation analysis and multicollinearity removal
- Feature importance ranking
- Recursive feature elimination
- Statistical significance testing
- Performance-based selection

Target: Select optimal subset from 60+ engineered features
Created: June 6, 2025
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Tuple, Any, Optional
from sklearn.feature_selection import (
    SelectKBest, f_regression, mutual_info_regression,
    RFE, RFECV, VarianceThreshold
)
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import LassoCV
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import cross_val_score
import warnings
warnings.filterwarnings('ignore')

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AdvancedFeatureSelector:
    """
    Advanced feature selection pipeline for solar prediction
    """
    
    def __init__(self, target_features: int = 40, correlation_threshold: float = 0.95):
        """
        Initialize feature selector
        
        Args:
            target_features: Target number of features to select
            correlation_threshold: Threshold for removing highly correlated features
        """
        self.target_features = target_features
        self.correlation_threshold = correlation_threshold
        
        # Selection results
        self.selected_features = []
        self.feature_scores = {}
        self.selection_methods = {}
        
        logger.info("🎯 Advanced Feature Selector initialized")
        logger.info(f"   Target features: {target_features}")
        logger.info(f"   Correlation threshold: {correlation_threshold}")
    
    def remove_low_variance_features(self, X: pd.DataFrame, threshold: float = 0.01) -> Tuple[pd.DataFrame, List[str]]:
        """Remove features with low variance"""
        logger.info(f"🔍 Removing low variance features (threshold: {threshold})...")
        
        selector = VarianceThreshold(threshold=threshold)
        X_selected = selector.fit_transform(X)
        
        selected_features = X.columns[selector.get_support()].tolist()
        removed_features = X.columns[~selector.get_support()].tolist()
        
        logger.info(f"   Removed {len(removed_features)} low variance features")
        logger.info(f"   Remaining features: {len(selected_features)}")
        
        return pd.DataFrame(X_selected, columns=selected_features, index=X.index), removed_features
    
    def remove_highly_correlated_features(self, X: pd.DataFrame) -> Tuple[pd.DataFrame, List[str]]:
        """Remove highly correlated features"""
        logger.info(f"🔗 Removing highly correlated features (threshold: {self.correlation_threshold})...")
        
        # Calculate correlation matrix
        corr_matrix = X.corr().abs()
        
        # Find highly correlated pairs
        upper_triangle = corr_matrix.where(
            np.triu(np.ones(corr_matrix.shape), k=1).astype(bool)
        )
        
        # Find features to remove
        to_remove = [column for column in upper_triangle.columns 
                    if any(upper_triangle[column] > self.correlation_threshold)]
        
        # Remove features
        X_selected = X.drop(columns=to_remove)
        
        logger.info(f"   Removed {len(to_remove)} highly correlated features")
        logger.info(f"   Remaining features: {len(X_selected.columns)}")
        
        return X_selected, to_remove
    
    def univariate_feature_selection(self, X: pd.DataFrame, y: pd.Series, k: int = None) -> Dict[str, float]:
        """Univariate feature selection using statistical tests"""
        logger.info("📊 Performing univariate feature selection...")
        
        if k is None:
            k = min(self.target_features * 2, len(X.columns))
        
        # F-regression scores
        f_scores, f_pvalues = f_regression(X, y)
        f_selector = SelectKBest(score_func=f_regression, k=k)
        f_selector.fit(X, y)
        
        # Mutual information scores
        mi_scores = mutual_info_regression(X, y, random_state=42)
        mi_selector = SelectKBest(score_func=mutual_info_regression, k=k)
        mi_selector.fit(X, y)
        
        # Combine scores
        feature_scores = {}
        for i, feature in enumerate(X.columns):
            feature_scores[feature] = {
                'f_score': f_scores[i],
                'f_pvalue': f_pvalues[i],
                'mi_score': mi_scores[i],
                'combined_score': (f_scores[i] / np.max(f_scores) + mi_scores[i] / np.max(mi_scores)) / 2
            }
        
        # Sort by combined score
        sorted_features = sorted(feature_scores.items(), key=lambda x: x[1]['combined_score'], reverse=True)
        
        logger.info(f"   Calculated univariate scores for {len(feature_scores)} features")
        
        return feature_scores
    
    def tree_based_feature_importance(self, X: pd.DataFrame, y: pd.Series) -> Dict[str, float]:
        """Tree-based feature importance using Random Forest"""
        logger.info("🌳 Calculating tree-based feature importance...")
        
        # Random Forest feature importance
        rf = RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1)
        rf.fit(X, y)
        
        feature_importance = {}
        for feature, importance in zip(X.columns, rf.feature_importances_):
            feature_importance[feature] = importance
        
        # Sort by importance
        sorted_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)
        
        logger.info(f"   Calculated tree-based importance for {len(feature_importance)} features")
        
        return feature_importance
    
    def lasso_feature_selection(self, X: pd.DataFrame, y: pd.Series) -> Dict[str, float]:
        """LASSO-based feature selection"""
        logger.info("🎯 Performing LASSO feature selection...")
        
        # Standardize features
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # LASSO with cross-validation
        lasso = LassoCV(cv=5, random_state=42, max_iter=2000)
        lasso.fit(X_scaled, y)
        
        # Get coefficients
        feature_coefficients = {}
        for feature, coef in zip(X.columns, lasso.coef_):
            feature_coefficients[feature] = abs(coef)
        
        # Sort by absolute coefficient value
        sorted_features = sorted(feature_coefficients.items(), key=lambda x: x[1], reverse=True)
        
        # Count non-zero coefficients
        non_zero_features = sum(1 for coef in lasso.coef_ if abs(coef) > 1e-6)
        
        logger.info(f"   LASSO selected {non_zero_features} features with non-zero coefficients")
        logger.info(f"   Alpha used: {lasso.alpha_:.6f}")
        
        return feature_coefficients
    
    def recursive_feature_elimination(self, X: pd.DataFrame, y: pd.Series) -> List[str]:
        """Recursive Feature Elimination with Cross-Validation"""
        logger.info("🔄 Performing Recursive Feature Elimination...")
        
        # Use Random Forest as estimator
        estimator = RandomForestRegressor(n_estimators=50, random_state=42, n_jobs=-1)
        
        # RFECV with cross-validation
        rfecv = RFECV(
            estimator=estimator,
            step=1,
            cv=5,
            scoring='neg_mean_squared_error',
            n_jobs=-1
        )
        
        rfecv.fit(X, y)
        
        selected_features = X.columns[rfecv.support_].tolist()
        
        logger.info(f"   RFE selected {len(selected_features)} optimal features")
        logger.info(f"   Optimal number of features: {rfecv.n_features_}")
        
        return selected_features
    
    def ensemble_feature_selection(self, X: pd.DataFrame, y: pd.Series) -> Dict[str, float]:
        """Ensemble feature selection combining multiple methods"""
        logger.info("🎭 Performing ensemble feature selection...")
        
        # Get scores from different methods
        univariate_scores = self.univariate_feature_selection(X, y)
        tree_importance = self.tree_based_feature_importance(X, y)
        lasso_coefficients = self.lasso_feature_selection(X, y)
        
        # Normalize scores to 0-1 range
        def normalize_scores(scores_dict):
            values = list(scores_dict.values())
            if isinstance(values[0], dict):
                # For univariate scores
                combined_values = [v['combined_score'] for v in values]
            else:
                combined_values = values
            
            max_val = max(combined_values)
            min_val = min(combined_values)
            
            if max_val == min_val:
                return {k: 0.5 for k in scores_dict.keys()}
            
            normalized = {}
            for k, v in scores_dict.items():
                if isinstance(v, dict):
                    score = v['combined_score']
                else:
                    score = v
                normalized[k] = (score - min_val) / (max_val - min_val)
            
            return normalized
        
        # Normalize all scores
        norm_univariate = normalize_scores(univariate_scores)
        norm_tree = normalize_scores(tree_importance)
        norm_lasso = normalize_scores(lasso_coefficients)
        
        # Combine scores with weights
        ensemble_scores = {}
        weights = {'univariate': 0.3, 'tree': 0.4, 'lasso': 0.3}
        
        for feature in X.columns:
            ensemble_scores[feature] = (
                weights['univariate'] * norm_univariate.get(feature, 0) +
                weights['tree'] * norm_tree.get(feature, 0) +
                weights['lasso'] * norm_lasso.get(feature, 0)
            )
        
        # Store individual scores
        self.feature_scores = {
            'univariate': univariate_scores,
            'tree_importance': tree_importance,
            'lasso_coefficients': lasso_coefficients,
            'ensemble': ensemble_scores
        }
        
        logger.info(f"   Calculated ensemble scores for {len(ensemble_scores)} features")
        
        return ensemble_scores
    
    def select_optimal_features(self, X: pd.DataFrame, y: pd.Series) -> Tuple[List[str], Dict[str, Any]]:
        """Select optimal features using comprehensive pipeline"""
        logger.info("🚀 Starting comprehensive feature selection...")
        
        original_features = len(X.columns)
        
        # Step 1: Remove low variance features
        X_step1, removed_low_var = self.remove_low_variance_features(X)
        
        # Step 2: Remove highly correlated features
        X_step2, removed_corr = self.remove_highly_correlated_features(X_step1)
        
        # Step 3: Ensemble feature selection
        ensemble_scores = self.ensemble_feature_selection(X_step2, y)
        
        # Step 4: Select top features based on ensemble scores
        sorted_features = sorted(ensemble_scores.items(), key=lambda x: x[1], reverse=True)
        top_features = [feature for feature, score in sorted_features[:self.target_features]]
        
        # Step 5: Validate with RFE (optional refinement)
        if len(X_step2.columns) > self.target_features * 2:
            logger.info("🔍 Validating selection with RFE...")
            X_top = X_step2[top_features]
            rfe_features = self.recursive_feature_elimination(X_top, y)
            
            # Use RFE features if they perform better
            if len(rfe_features) < len(top_features):
                final_features = rfe_features
                selection_method = "ensemble + RFE"
            else:
                final_features = top_features
                selection_method = "ensemble"
        else:
            final_features = top_features
            selection_method = "ensemble"
        
        # Store results
        self.selected_features = final_features
        self.selection_methods = {
            'method': selection_method,
            'removed_low_variance': len(removed_low_var),
            'removed_correlated': len(removed_corr),
            'final_features': len(final_features)
        }
        
        # Create summary
        selection_summary = {
            'original_features': original_features,
            'after_variance_filter': len(X_step1.columns),
            'after_correlation_filter': len(X_step2.columns),
            'final_selected': len(final_features),
            'reduction_ratio': len(final_features) / original_features,
            'removed_features': {
                'low_variance': removed_low_var,
                'high_correlation': removed_corr
            },
            'selection_method': selection_method,
            'top_features': final_features[:10],  # Top 10 features
            'feature_scores': {feature: ensemble_scores[feature] for feature in final_features}
        }
        
        logger.info("✅ Feature selection completed!")
        logger.info(f"   Original features: {original_features}")
        logger.info(f"   Selected features: {len(final_features)}")
        logger.info(f"   Reduction ratio: {len(final_features) / original_features:.2%}")
        logger.info(f"   Selection method: {selection_method}")
        
        return final_features, selection_summary
    
    def get_feature_ranking(self, method: str = 'ensemble') -> List[Tuple[str, float]]:
        """Get feature ranking by specified method"""
        if method not in self.feature_scores:
            raise ValueError(f"Method '{method}' not available. Available: {list(self.feature_scores.keys())}")
        
        scores = self.feature_scores[method]
        
        if isinstance(list(scores.values())[0], dict):
            # For univariate scores
            sorted_features = sorted(scores.items(), key=lambda x: x[1]['combined_score'], reverse=True)
            return [(feature, data['combined_score']) for feature, data in sorted_features]
        else:
            # For other methods
            return sorted(scores.items(), key=lambda x: x[1], reverse=True)

def main():
    """Test feature selection pipeline"""
    logger.info("🎯 Testing Advanced Feature Selection Pipeline")
    logger.info("=" * 70)
    
    # Create sample data with many features
    np.random.seed(42)
    n_samples = 1000
    n_features = 80
    
    # Generate features with different characteristics
    X = pd.DataFrame(np.random.randn(n_samples, n_features), 
                    columns=[f'feature_{i}' for i in range(n_features)])
    
    # Add some correlated features
    X['feature_corr_1'] = X['feature_0'] + np.random.normal(0, 0.1, n_samples)
    X['feature_corr_2'] = X['feature_1'] * 0.9 + np.random.normal(0, 0.1, n_samples)
    
    # Add some low variance features
    X['feature_low_var_1'] = np.ones(n_samples) + np.random.normal(0, 0.001, n_samples)
    X['feature_low_var_2'] = np.full(n_samples, 5.0) + np.random.normal(0, 0.001, n_samples)
    
    # Create target with some relationship to features
    y = (X['feature_0'] * 2 + X['feature_1'] * 1.5 + X['feature_5'] * 0.8 + 
         np.random.normal(0, 0.5, n_samples))
    
    logger.info(f"Sample data created: {X.shape[0]} samples, {X.shape[1]} features")
    
    # Initialize feature selector
    feature_selector = AdvancedFeatureSelector(target_features=20)
    
    # Select optimal features
    selected_features, summary = feature_selector.select_optimal_features(X, y)
    
    # Display results
    logger.info("🎯 Feature Selection Results:")
    logger.info(f"   Original features: {summary['original_features']}")
    logger.info(f"   Selected features: {summary['final_selected']}")
    logger.info(f"   Reduction ratio: {summary['reduction_ratio']:.2%}")
    
    logger.info("\n📊 Top 10 selected features:")
    for i, feature in enumerate(summary['top_features'], 1):
        score = summary['feature_scores'][feature]
        logger.info(f"   {i:2d}. {feature}: {score:.4f}")
    
    logger.info(f"\n✅ Feature Selection Pipeline test completed!")
    
    return selected_features, summary

if __name__ == "__main__":
    selected_features, summary = main()
