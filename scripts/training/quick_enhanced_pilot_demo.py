#!/usr/bin/env python3
"""
Quick Enhanced Pilot Demo
=========================

Γρήγορο demo του enhanced training με synthetic data για immediate results.

Δημιουργήθηκε: 2025-06-05
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))

import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
import joblib
import json
from typing import Dict, List, Tuple, Any, Optional
import logging

# Sklearn imports
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import GridSearchCV, train_test_split, TimeSeriesSplit
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.preprocessing import StandardScaler

# Import unified preprocessing pipeline
from src.preprocessing.unified_pipeline import create_unified_pipeline

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QuickEnhancedPilotDemo:
    """
    Quick demo για enhanced pilot training
    """
    
    def __init__(self, output_dir: str = "models/quick_enhanced_demo"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True, parents=True)
        
        # Initialize unified pipeline
        self.pipeline = create_unified_pipeline("v1.0.0")
        
        # Quick configuration
        self.pilot_models = {
            'spring_system1': {
                'type': 'seasonal',
                'target_r2': 0.90,
                'target_mae': 3.0
            }
        }
        
        logger.info("🏗️ Initialized QuickEnhancedPilotDemo")
    
    def generate_quick_data(self, n_samples: int = 2000) -> pd.DataFrame:
        """Generate quick synthetic data"""
        logger.info(f"🎭 Generating {n_samples} quick samples...")
        
        np.random.seed(42)
        
        # Generate timestamps
        start_date = datetime(2024, 3, 1)
        timestamps = [start_date + timedelta(hours=i/12) for i in range(n_samples)]
        
        data = []
        for timestamp in timestamps:
            hour = timestamp.hour
            month = timestamp.month
            day_of_year = timestamp.timetuple().tm_yday
            
            # Seasonal patterns
            seasonal_factor = 0.5 + 0.5 * np.sin(2 * np.pi * day_of_year / 365)
            
            # Daily patterns
            if 6 <= hour <= 18:
                hour_factor = np.sin(np.pi * (hour - 6) / 12)
            else:
                hour_factor = 0
            
            # Weather simulation
            base_temp = 15 + 10 * seasonal_factor + np.random.normal(0, 3)
            cloud_cover = np.random.uniform(0, 100)
            ghi = 1000 * hour_factor * (1 - cloud_cover/100) * seasonal_factor
            ghi = max(0, ghi + np.random.normal(0, 50))
            
            # Yield simulation με realistic patterns
            yield_today = ghi * 0.05 * (1 + np.random.normal(0, 0.1))
            yield_today = max(0, min(100, yield_today))
            
            data.append({
                'timestamp': timestamp,
                'yield_today': yield_today,
                'soc': np.random.uniform(20, 90),
                'bat_power': np.random.normal(0, 500),
                'temperature': base_temp + np.random.normal(0, 2),
                'global_horizontal_irradiance': ghi,
                'temperature_2m': base_temp,
                'relative_humidity_2m': np.random.uniform(30, 90),
                'cloud_cover': cloud_cover,
                'system_id': 1,
                'month': month,
                'hour': hour,
                'day_of_year': day_of_year
            })
        
        df = pd.DataFrame(data)
        logger.info(f"✅ Generated quick data: {len(df):,} samples")
        
        return df
    
    def engineer_quick_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Quick feature engineering"""
        logger.info("🔧 Quick feature engineering...")
        
        # Start με unified pipeline
        processed_df = self.pipeline.engineer_features(df)
        
        # Add key advanced features
        processed_df = processed_df.sort_values('timestamp').reset_index(drop=True)
        
        # Essential lag features
        for lag in [1, 12, 24]:  # 5min, 1h, 2h lags
            processed_df[f'yield_lag_{lag}'] = processed_df['yield_today'].shift(lag)
            processed_df[f'ghi_lag_{lag}'] = processed_df['global_horizontal_irradiance'].shift(lag)
        
        # Essential rolling features
        for window in [12, 24]:  # 1h, 2h windows
            processed_df[f'yield_rolling_mean_{window}'] = processed_df['yield_today'].rolling(window).mean()
            processed_df[f'temp_rolling_mean_{window}'] = processed_df['temperature_2m'].rolling(window).mean()
        
        # Key interactions
        processed_df['temp_ghi_interaction'] = processed_df['temperature_2m'] * processed_df['global_horizontal_irradiance'] / 1000
        processed_df['cloud_temp_interaction'] = processed_df['cloud_cover'] * processed_df['temperature_2m'] / 100
        
        # Solar position (simplified)
        processed_df['sun_elevation'] = np.sin(2 * np.pi * processed_df['hour'] / 24) * np.sin(2 * np.pi * processed_df['day_of_year'] / 365)
        
        # Fill missing values
        processed_df = processed_df.fillna(method='ffill').fillna(method='bfill').fillna(0)
        
        logger.info(f"✅ Quick feature engineering complete: {processed_df.shape[1]} features")
        
        return processed_df
    
    def optimize_quick_hyperparameters(self, X_train: np.ndarray, y_train: np.ndarray) -> Dict:
        """Quick hyperparameter optimization"""
        
        logger.info("🔍 Quick hyperparameter optimization...")
        
        # Reduced parameter grid για speed
        param_grid = {
            'n_estimators': [50, 100, 200],
            'max_depth': [10, 15, None],
            'min_samples_split': [2, 5],
            'min_samples_leaf': [1, 2],
            'max_features': ['sqrt', None]
        }
        
        # Quick cross-validation
        tscv = TimeSeriesSplit(n_splits=3)
        
        grid_search = GridSearchCV(
            estimator=RandomForestRegressor(random_state=42),
            param_grid=param_grid,
            cv=tscv,
            scoring='neg_mean_absolute_error',
            n_jobs=-1,
            verbose=1
        )
        
        grid_search.fit(X_train, y_train)
        
        logger.info(f"✅ Best parameters: {grid_search.best_params_}")
        logger.info(f"✅ Best CV score: {-grid_search.best_score_:.4f}")
        
        return {
            'best_model': grid_search.best_estimator_,
            'best_params': grid_search.best_params_,
            'best_score': -grid_search.best_score_
        }
    
    def train_quick_enhanced_model(self, processed_data: pd.DataFrame) -> Dict[str, Any]:
        """Train quick enhanced model"""
        
        logger.info("\n🎯 Training quick enhanced model")
        logger.info("=" * 60)
        
        # Filter spring data
        spring_data = processed_data[processed_data['month'].isin([3, 4, 5])].copy()
        
        if len(spring_data) < 100:
            logger.error(f"❌ Insufficient spring data: {len(spring_data)} records")
            return None
        
        # Feature selection
        base_features = ['hour_sin', 'hour_cos', 'temperature', 'cloud_cover', 'ghi', 'soc']
        
        # Add available advanced features
        advanced_features = [col for col in spring_data.columns if any(x in col for x in 
            ['lag_', 'rolling_', 'interaction', 'sun_elevation'])]
        
        all_features = base_features + advanced_features
        available_features = [f for f in all_features if f in spring_data.columns]
        
        logger.info(f"📊 Using {len(available_features)} features για training")
        
        # Prepare data
        X = spring_data[available_features].values
        y = spring_data['yield_today'].values
        
        # Train/test split
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, shuffle=False
        )
        
        # Scale data
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        # Optimize hyperparameters
        optimization_result = self.optimize_quick_hyperparameters(X_train_scaled, y_train)
        optimized_model = optimization_result['best_model']
        
        # Make predictions
        y_pred = optimized_model.predict(X_test_scaled)
        
        # Calculate metrics
        metrics = {
            'r2': r2_score(y_test, y_pred),
            'mae': mean_absolute_error(y_test, y_pred),
            'rmse': np.sqrt(mean_squared_error(y_test, y_pred))
        }
        
        # Feature importance
        feature_importance = dict(zip(available_features, optimized_model.feature_importances_))
        top_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)[:10]
        
        # Check if targets achieved
        target_achieved = (
            metrics['r2'] >= self.pilot_models['spring_system1']['target_r2'] and
            metrics['mae'] <= self.pilot_models['spring_system1']['target_mae']
        )
        
        result = {
            'model_name': 'spring_system1_quick',
            'model': optimized_model,
            'scaler': scaler,
            'features': available_features,
            'metrics': metrics,
            'feature_importance': feature_importance,
            'top_features': top_features,
            'target_achieved': target_achieved,
            'training_samples': len(X_train),
            'test_samples': len(X_test),
            'optimization_result': optimization_result
        }
        
        # Save model
        self.save_quick_model(result)
        
        # Log results
        logger.info(f"📊 QUICK ENHANCED MODEL RESULTS:")
        logger.info(f"   R²: {metrics['r2']:.4f}")
        logger.info(f"   MAE: {metrics['mae']:.3f}")
        logger.info(f"   RMSE: {metrics['rmse']:.3f}")
        logger.info(f"   Target achieved: {'✅' if target_achieved else '❌'}")
        
        logger.info(f"   Top 5 features:")
        for feature, importance in top_features[:5]:
            logger.info(f"     {feature}: {importance:.4f}")
        
        return result
    
    def save_quick_model(self, result: Dict[str, Any]):
        """Save quick model"""
        model_dir = self.output_dir / result['model_name']
        model_dir.mkdir(exist_ok=True)
        
        # Save model και scaler
        joblib.dump(result['model'], model_dir / "model.joblib")
        joblib.dump(result['scaler'], model_dir / "scaler.joblib")
        
        # Save metadata
        metadata = {
            'model_name': result['model_name'],
            'model_type': 'quick_enhanced_random_forest',
            'algorithm': 'RandomForestRegressor',
            'features': result['features'],
            'performance': result['metrics'],
            'feature_importance': result['feature_importance'],
            'top_features': result['top_features'],
            'target_achieved': result['target_achieved'],
            'training_samples': result['training_samples'],
            'test_samples': result['test_samples'],
            'training_date': datetime.now().isoformat(),
            'best_params': result['optimization_result']['best_params']
        }
        
        with open(model_dir / "metadata.json", 'w') as f:
            json.dump(metadata, f, indent=2, default=str)
        
        logger.info(f"💾 Saved quick model: {model_dir}")
    
    def compare_with_baseline(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Compare με baseline performance"""
        
        # Baseline spring_system1 performance (από previous results)
        baseline = {
            'r2': 0.938,
            'mae': 3.12,
            'algorithm': 'RandomForest_basic'
        }
        
        enhanced = result['metrics']
        
        # Calculate improvements
        r2_improvement = ((enhanced['r2'] - baseline['r2']) / baseline['r2']) * 100
        mae_improvement = ((baseline['mae'] - enhanced['mae']) / baseline['mae']) * 100
        
        comparison = {
            'baseline': baseline,
            'enhanced': enhanced,
            'improvements': {
                'r2_improvement_percent': r2_improvement,
                'mae_improvement_percent': mae_improvement,
                'r2_absolute': enhanced['r2'] - baseline['r2'],
                'mae_absolute': baseline['mae'] - enhanced['mae']
            },
            'significant_improvement': r2_improvement > 1 and mae_improvement > 5
        }
        
        logger.info(f"\n📈 PERFORMANCE COMPARISON:")
        logger.info(f"   Baseline R²: {baseline['r2']:.4f} → Enhanced R²: {enhanced['r2']:.4f}")
        logger.info(f"   R² improvement: {r2_improvement:+.1f}%")
        logger.info(f"   Baseline MAE: {baseline['mae']:.3f} → Enhanced MAE: {enhanced['mae']:.3f}")
        logger.info(f"   MAE improvement: {mae_improvement:+.1f}%")
        logger.info(f"   Significant improvement: {'✅' if comparison['significant_improvement'] else '❌'}")
        
        return comparison
    
    def run_quick_demo(self) -> Dict[str, Any]:
        """Run complete quick demo"""
        logger.info("🚀 STARTING QUICK ENHANCED PILOT DEMO")
        logger.info("=" * 80)
        
        # Generate data
        raw_data = self.generate_quick_data(n_samples=2000)
        
        # Engineer features
        processed_data = self.engineer_quick_features(raw_data)
        
        # Train model
        result = self.train_quick_enhanced_model(processed_data)
        
        if result:
            # Compare με baseline
            comparison = self.compare_with_baseline(result)
            
            # Save comparison
            comparison_path = self.output_dir / "quick_comparison.json"
            with open(comparison_path, 'w') as f:
                json.dump(comparison, f, indent=2, default=str)
            
            logger.info(f"💾 Comparison saved: {comparison_path}")
            
            logger.info("\n✅ QUICK ENHANCED DEMO COMPLETED SUCCESSFULLY!")
            
            return {
                'result': result,
                'comparison': comparison,
                'demo_completed': True
            }
        else:
            logger.error("❌ Quick demo failed")
            return {'demo_completed': False}

def main():
    """Main quick demo function"""
    try:
        demo = QuickEnhancedPilotDemo()
        results = demo.run_quick_demo()
        
        if results['demo_completed']:
            print("\n🎉 QUICK ENHANCED PILOT DEMO SUCCESS!")
            print("=" * 50)
            
            result = results['result']
            comparison = results['comparison']
            
            print(f"📊 Enhanced Model Performance:")
            print(f"   R²: {result['metrics']['r2']:.4f}")
            print(f"   MAE: {result['metrics']['mae']:.3f}")
            print(f"   Features: {len(result['features'])}")
            
            print(f"\n📈 Improvements vs Baseline:")
            print(f"   R² improvement: {comparison['improvements']['r2_improvement_percent']:+.1f}%")
            print(f"   MAE improvement: {comparison['improvements']['mae_improvement_percent']:+.1f}%")
            
            return True
        else:
            print("❌ Quick demo failed")
            return False
        
    except Exception as e:
        logger.error(f"❌ Quick demo failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
