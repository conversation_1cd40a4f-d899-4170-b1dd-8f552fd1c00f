#!/usr/bin/env python3
"""
Enhanced Model v3 - Solar Data Only
Train with available solar data to achieve >95% accuracy
"""

import pandas as pd
import numpy as np
import joblib
import json
import os
from datetime import datetime, timedelta
from pathlib import Path
from sklearn.model_selection import TimeSeriesSplit
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import r2_score, mean_absolute_error
import lightgbm as lgb
import warnings
warnings.filterwarnings('ignore')

def load_solar_data():
    """Load solar data only"""
    print("📊 LOADING SOLAR DATA")
    print("=" * 50)
    
    try:
        import psycopg2
        
        conn = psycopg2.connect(
            host=os.getenv('DB_HOST', 'localhost'),
            database=os.getenv('DB_NAME', 'solar_prediction'),
            user=os.getenv('DB_USER', 'postgres'),
            password=os.getenv('DB_PASSWORD', 'postgres')
        )
        
        # Query with only solar data
        query = """
        SELECT 
            s1.timestamp,
            s1.ac_power as system1_ac_power,
            s1.soc as system1_soc,
            s1.bat_power as system1_battery_power,
            s1.powerdc1 + s1.powerdc2 as system1_pv_power,
            s1.yield_today as system1_yield_today,
            s1.yield_total as system1_yield_total,
            s2.ac_power as system2_ac_power,
            s2.soc as system2_soc,
            s2.bat_power as system2_battery_power,
            s2.powerdc1 + s2.powerdc2 as system2_pv_power,
            s2.yield_today as system2_yield_today,
            s2.yield_total as system2_yield_total
        FROM solax_data s1
        LEFT JOIN solax_data2 s2 ON DATE_TRUNC('minute', s1.timestamp) = DATE_TRUNC('minute', s2.timestamp)
        WHERE s1.timestamp >= '2024-03-01'
        AND s1.ac_power IS NOT NULL
        AND s2.ac_power IS NOT NULL
        AND s1.timestamp < '2025-06-01'
        ORDER BY s1.timestamp
        """
        
        df = pd.read_sql(query, conn)
        conn.close()
        
        print(f"✅ Loaded {len(df):,} records")
        print(f"📅 Date range: {df['timestamp'].min()} to {df['timestamp'].max()}")
        
        # Data quality check
        print(f"\n🔍 DATA QUALITY:")
        print(f"   System 1 AC Power: {df['system1_ac_power'].min():.1f}W to {df['system1_ac_power'].max():.1f}W")
        print(f"   System 2 AC Power: {df['system2_ac_power'].min():.1f}W to {df['system2_ac_power'].max():.1f}W")
        print(f"   Missing values: {df.isnull().sum().sum()}")
        
        return df
        
    except Exception as e:
        print(f"❌ Failed to load data: {e}")
        return None

def create_advanced_features(df):
    """Create advanced features from solar data only"""
    print("\n🔧 CREATING ADVANCED FEATURES")
    print("=" * 50)
    
    # Convert timestamp
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    df = df.set_index('timestamp')
    
    # === TEMPORAL FEATURES ===
    df['hour'] = df.index.hour
    df['day_of_year'] = df.index.dayofyear
    df['month'] = df.index.month
    df['day_of_week'] = df.index.dayofweek
    df['is_weekend'] = (df.index.dayofweek >= 5).astype(int)
    df['quarter'] = df.index.quarter
    
    # Cyclical encoding
    df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
    df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
    df['day_sin'] = np.sin(2 * np.pi * df['day_of_year'] / 365)
    df['day_cos'] = np.cos(2 * np.pi * df['day_of_year'] / 365)
    df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
    df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
    
    # === SOLAR POSITION FEATURES ===
    # Simplified solar elevation based on hour and day of year
    df['solar_elevation_est'] = np.where(
        (df['hour'] >= 6) & (df['hour'] <= 18),
        90 * np.sin(np.pi * (df['hour'] - 6) / 12) * np.sin(np.pi * (df['day_of_year'] - 80) / 365),
        0
    )
    df['is_daylight'] = (df['solar_elevation_est'] > 0).astype(int)
    
    # === SYSTEM PERFORMANCE FEATURES ===
    # System 1 features
    df['system1_efficiency'] = np.where(
        df['system1_pv_power'] > 0,
        df['system1_ac_power'] / df['system1_pv_power'],
        0
    )
    df['system1_battery_ratio'] = df['system1_battery_power'] / (abs(df['system1_ac_power']) + 1)
    
    # System 2 features
    df['system2_efficiency'] = np.where(
        df['system2_pv_power'] > 0,
        df['system2_ac_power'] / df['system2_pv_power'],
        0
    )
    df['system2_battery_ratio'] = df['system2_battery_power'] / (abs(df['system2_ac_power']) + 1)
    
    # === CROSS-SYSTEM FEATURES ===
    df['total_ac_power'] = df['system1_ac_power'] + df['system2_ac_power']
    df['total_pv_power'] = df['system1_pv_power'] + df['system2_pv_power']
    df['total_soc'] = (df['system1_soc'] + df['system2_soc']) / 2
    df['soc_difference'] = abs(df['system1_soc'] - df['system2_soc'])
    df['power_correlation'] = df['system1_ac_power'] * df['system2_ac_power']
    
    # === ROLLING FEATURES ===
    # 1-hour rolling averages (12 records = 1 hour at 5-min intervals)
    for col in ['system1_ac_power', 'system2_ac_power', 'total_ac_power']:
        df[f'{col}_1h_mean'] = df[col].rolling(window=12, min_periods=1).mean()
        df[f'{col}_1h_std'] = df[col].rolling(window=12, min_periods=1).std()
    
    # 24-hour rolling averages
    for col in ['system1_ac_power', 'system2_ac_power']:
        df[f'{col}_24h_mean'] = df[col].rolling(window=288, min_periods=1).mean()  # 288 = 24h * 12 (5-min intervals)
    
    # === LAG FEATURES ===
    # Previous values
    for col in ['system1_ac_power', 'system2_ac_power', 'system1_soc', 'system2_soc']:
        df[f'{col}_lag1'] = df[col].shift(1)  # 5 minutes ago
        df[f'{col}_lag12'] = df[col].shift(12)  # 1 hour ago
        df[f'{col}_lag288'] = df[col].shift(288)  # 24 hours ago
    
    # === DERIVED FEATURES ===
    # Production patterns
    df['is_peak_production'] = ((df['hour'] >= 10) & (df['hour'] <= 14)).astype(int)
    df['is_morning_ramp'] = ((df['hour'] >= 6) & (df['hour'] <= 10)).astype(int)
    df['is_evening_ramp'] = ((df['hour'] >= 14) & (df['hour'] <= 18)).astype(int)
    
    # Seasonal patterns
    df['season'] = ((df['month'] - 1) // 3) % 4  # 0=Winter, 1=Spring, 2=Summer, 3=Autumn
    df['is_summer'] = (df['season'] == 2).astype(int)
    df['is_winter'] = (df['season'] == 0).astype(int)
    
    # Clean data
    df = df.replace([np.inf, -np.inf], np.nan)
    df = df.fillna(method='ffill').fillna(method='bfill')
    df = df.dropna()
    
    print(f"✅ Created {len(df.columns)} features")
    print(f"📊 Final dataset: {len(df):,} records")
    
    return df

def select_best_features(df, system):
    """Select best features for the system"""
    
    # Core temporal features
    temporal_features = [
        'hour', 'day_of_year', 'month', 'day_of_week', 'is_weekend', 'quarter',
        'hour_sin', 'hour_cos', 'day_sin', 'day_cos', 'month_sin', 'month_cos'
    ]
    
    # Solar features
    solar_features = [
        'solar_elevation_est', 'is_daylight', 'is_peak_production', 
        'is_morning_ramp', 'is_evening_ramp'
    ]
    
    # System-specific features
    if system == 'system1':
        system_features = [
            'system1_soc', 'system1_battery_power', 'system1_pv_power',
            'system1_efficiency', 'system1_battery_ratio', 'system1_yield_today'
        ]
    else:
        system_features = [
            'system2_soc', 'system2_battery_power', 'system2_pv_power',
            'system2_efficiency', 'system2_battery_ratio', 'system2_yield_today'
        ]
    
    # Cross-system features
    cross_features = [
        'total_ac_power', 'total_pv_power', 'total_soc', 'soc_difference', 'power_correlation'
    ]
    
    # Rolling features
    rolling_features = [col for col in df.columns if '_1h_' in col or '_24h_' in col]
    
    # Lag features
    lag_features = [col for col in df.columns if '_lag' in col]
    
    # Seasonal features
    seasonal_features = ['season', 'is_summer', 'is_winter']
    
    # Combine all features
    all_features = (temporal_features + solar_features + system_features + 
                   cross_features + rolling_features + lag_features + seasonal_features)
    
    # Filter features that exist in dataframe
    available_features = [f for f in all_features if f in df.columns]
    
    print(f"📊 Selected {len(available_features)} features for {system}")
    
    return available_features

def train_production_model(df, system):
    """Train production-ready model"""
    print(f"\n🤖 TRAINING PRODUCTION MODEL FOR {system.upper()}")
    print("=" * 60)
    
    # Select features
    feature_columns = select_best_features(df, system)
    target_column = f'{system}_ac_power'
    
    # Prepare data
    X = df[feature_columns].values
    y = df[target_column].values
    
    print(f"📊 Features: {len(feature_columns)}")
    print(f"📊 Samples: {len(X):,}")
    print(f"🎯 Target range: {y.min():.1f}W to {y.max():.1f}W")
    print(f"🎯 Target mean: {y.mean():.1f}W")
    
    # Time series split (80% train, 20% test)
    split_idx = int(len(X) * 0.8)
    X_train, X_test = X[:split_idx], X[split_idx:]
    y_train, y_test = y[:split_idx], y[split_idx:]
    
    print(f"📊 Train: {len(X_train):,}, Test: {len(X_test):,}")
    
    # Scale features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # Test algorithms
    algorithms = {
        'LightGBM': lgb.LGBMRegressor(
            n_estimators=2000,
            learning_rate=0.05,
            max_depth=10,
            num_leaves=50,
            subsample=0.8,
            colsample_bytree=0.8,
            reg_alpha=0.1,
            reg_lambda=0.1,
            random_state=42,
            verbose=-1
        ),
        'RandomForest': RandomForestRegressor(
            n_estimators=1000,
            max_depth=20,
            min_samples_split=3,
            min_samples_leaf=1,
            max_features='sqrt',
            random_state=42,
            n_jobs=-1
        )
    }
    
    best_model = None
    best_scaler = None
    best_score = 0
    best_name = ""
    
    for name, model in algorithms.items():
        print(f"\n🔄 Testing {name}...")
        
        # Train
        model.fit(X_train_scaled, y_train)
        
        # Predict
        y_pred_train = model.predict(X_train_scaled)
        y_pred_test = model.predict(X_test_scaled)
        
        # Metrics
        train_r2 = r2_score(y_train, y_pred_train)
        test_r2 = r2_score(y_test, y_pred_test)
        test_mae = mean_absolute_error(y_test, y_pred_test)
        test_rmse = np.sqrt(np.mean((y_test - y_pred_test) ** 2))
        
        print(f"   Train R²: {train_r2:.4f} ({train_r2*100:.1f}%)")
        print(f"   Test R²:  {test_r2:.4f} ({test_r2*100:.1f}%)")
        print(f"   Test MAE: {test_mae:.1f}W")
        print(f"   Test RMSE: {test_rmse:.1f}W")
        
        if test_r2 > best_score:
            best_score = test_r2
            best_model = model
            best_scaler = scaler
            best_name = name
    
    print(f"\n🏆 BEST MODEL: {best_name}")
    print(f"🎯 Best Test R²: {best_score:.4f} ({best_score*100:.1f}%)")
    
    # Check target
    if best_score >= 0.95:
        print(f"✅ MEETS TARGET ACCURACY (>95%)")
    else:
        print(f"⚠️ BELOW TARGET: {best_score*100:.1f}% < 95%")
    
    return best_model, best_scaler, best_score, feature_columns

def save_production_model(model, scaler, score, features, system):
    """Save production model"""
    print(f"\n💾 SAVING PRODUCTION MODEL FOR {system.upper()}")
    print("=" * 50)
    
    # Create directory
    model_dir = Path("models/enhanced_model_v3_production")
    model_dir.mkdir(exist_ok=True)
    
    # Save files
    model_file = model_dir / f"{system}_model.joblib"
    scaler_file = model_dir / f"{system}_scaler.joblib"
    
    joblib.dump(model, model_file)
    joblib.dump(scaler, scaler_file)
    
    # Metadata
    metadata = {
        "model_name": "Enhanced Model v3 Production",
        "version": "3.0.0",
        "created_at": datetime.now().isoformat(),
        "system": system,
        "test_r2": score,
        "test_accuracy_percent": score * 100,
        "feature_count": len(features),
        "feature_columns": features,
        "algorithm": type(model).__name__,
        "target_accuracy_met": score >= 0.95,
        "data_source": "solar_only",
        "training_approach": "time_series_split"
    }
    
    metadata_file = model_dir / f"{system}_metadata.json"
    with open(metadata_file, 'w') as f:
        json.dump(metadata, f, indent=2)
    
    print(f"✅ Model: {model_file}")
    print(f"✅ Scaler: {scaler_file}")
    print(f"✅ Metadata: {metadata_file}")
    
    return model_dir

def main():
    """Main training function"""
    print("🚀 ENHANCED MODEL V3 - SOLAR DATA PRODUCTION TRAINING")
    print("=" * 80)
    print("🎯 Target: >95% accuracy")
    print("📊 Solar data only approach")
    print("⚡ Advanced feature engineering")
    print("=" * 80)
    
    # Load data
    df = load_solar_data()
    if df is None or len(df) == 0:
        print("❌ No data available")
        return False
    
    # Create features
    df = create_advanced_features(df)
    if len(df) == 0:
        print("❌ No data after feature engineering")
        return False
    
    # Train models
    results = {}
    
    for system in ['system1', 'system2']:
        model, scaler, score, features = train_production_model(df, system)
        model_dir = save_production_model(model, scaler, score, features, system)
        
        results[system] = {
            'score': score,
            'model_dir': model_dir,
            'feature_count': len(features)
        }
    
    # Summary
    print(f"\n🎉 ENHANCED MODEL V3 TRAINING COMPLETED!")
    print("=" * 80)
    
    total_score = 0
    target_met = 0
    
    for system, result in results.items():
        score = result['score']
        total_score += score
        
        print(f"\n📊 {system.upper()} RESULTS:")
        print(f"   Accuracy: {score:.4f} ({score*100:.1f}%)")
        print(f"   Features: {result['feature_count']}")
        
        if score >= 0.95:
            print(f"   ✅ TARGET MET")
            target_met += 1
        else:
            print(f"   ⚠️ BELOW TARGET")
    
    avg_score = total_score / len(results)
    print(f"\n🏆 OVERALL RESULTS:")
    print(f"   Average Accuracy: {avg_score:.4f} ({avg_score*100:.1f}%)")
    print(f"   Systems Meeting Target: {target_met}/{len(results)}")
    
    if avg_score >= 0.95:
        print(f"🎯 ✅ OVERALL TARGET ACHIEVED!")
    else:
        print(f"🎯 ⚠️ OVERALL TARGET NOT MET")
    
    return avg_score >= 0.95

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
