#!/usr/bin/env python3
"""
Train Yield-Based Models με Unified Preprocessing Pipeline
=========================================================

Ενημερωμένο training script που χρησιμοποιεί το unified preprocessing pipeline
για εκπαίδευση των 8 multi-horizon μοντέλων με consistency και version control.

Βασισμένο στο: train_yield_based_models.py
Ενημερώθηκε: 2025-06-05

Μοντέλα που εκπαιδεύονται:
- multi_horizon_hourly_system1/2
- multi_horizon_daily_system1/2  
- multi_horizon_monthly_system1/2
- multi_horizon_yearly_system1/2
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))

import pandas as pd
import numpy as np
import psycopg2
from pathlib import Path
from datetime import datetime, timedelta
import joblib
import json
from typing import Dict, List, Tuple, Any, Optional
import logging

# Import enhanced JSON encoder
sys.path.append('/home/<USER>/solar-prediction-project')
from src.utils.json_utils import EnhancedJSONEncoder

# Sklearn imports
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression, Ridge
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.model_selection import train_test_split, cross_val_score

# Import unified preprocessing pipeline
from src.preprocessing.unified_pipeline import create_unified_pipeline
from src.preprocessing.pipeline_config import create_default_pipeline_config, PipelineValidator

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class UnifiedYieldBasedTrainer:
    """
    Trainer για yield-based μοντέλα με unified preprocessing pipeline
    """
    
    def __init__(self, output_dir: str = "models", pipeline_version: str = "v1.0.0"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Initialize unified pipeline
        self.pipeline = create_unified_pipeline(pipeline_version)
        self.pipeline_version = pipeline_version
        
        # Model configurations
        self.horizons = ['hourly', 'daily', 'monthly', 'yearly']
        self.systems = [1, 2]
        
        # Algorithm configurations
        self.algorithms = {
            'RandomForest': RandomForestRegressor(
                n_estimators=100,
                max_depth=10,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42
            ),
            'GradientBoosting': GradientBoostingRegressor(
                n_estimators=100,
                max_depth=6,
                learning_rate=0.1,
                random_state=42
            ),
            'Ridge': Ridge(alpha=1.0),
            'LinearRegression': LinearRegression()
        }
        
        # Performance targets
        self.performance_targets = {
            'hourly': {'r2': 0.85, 'mae': 2.0},
            'daily': {'r2': 0.90, 'mae': 3.0},
            'monthly': {'r2': 0.95, 'mae': 5.0},
            'yearly': {'r2': 0.98, 'mae': 10.0}
        }
        
        logger.info(f"🏗️ Initialized UnifiedYieldBasedTrainer with pipeline {pipeline_version}")
    
    def load_training_data(self) -> pd.DataFrame:
        """Load training data από τη βάση δεδομένων"""
        logger.info("📊 Loading training data from database...")
        
        try:
            conn = psycopg2.connect(
                host=os.getenv('DB_HOST', 'localhost'),
                port=int(os.getenv('DB_PORT', '5433')),
                database=os.getenv('DB_NAME', 'solar_prediction'),
                user=os.getenv('DB_USER', 'postgres'),
                password=os.getenv('DB_PASSWORD', 'postgres')
            )
            
            # Query για unified data από όλα τα συστήματα
            query = """
            WITH system1_data AS (
                SELECT 
                    s.timestamp,
                    s.yield_today,
                    s.soc,
                    s.bat_power,
                    s.temperature,
                    w.global_horizontal_irradiance,
                    w.temperature_2m,
                    w.relative_humidity_2m,
                    w.cloud_cover,
                    1 as system_id
                FROM solax_data s
                LEFT JOIN weather_data w ON DATE_TRUNC('hour', s.timestamp) = DATE_TRUNC('hour', w.timestamp)
                WHERE s.timestamp >= '2024-03-01' 
                  AND s.yield_today IS NOT NULL
                  AND s.yield_today >= 0 
                  AND s.yield_today <= 100
            ),
            system2_data AS (
                SELECT 
                    s.timestamp,
                    s.yield_today,
                    s.soc,
                    s.bat_power,
                    s.temperature,
                    w.global_horizontal_irradiance,
                    w.temperature_2m,
                    w.relative_humidity_2m,
                    w.cloud_cover,
                    2 as system_id
                FROM solax_data2 s
                LEFT JOIN weather_data w ON DATE_TRUNC('hour', s.timestamp) = DATE_TRUNC('hour', w.timestamp)
                WHERE s.timestamp >= '2024-03-01'
                  AND s.yield_today IS NOT NULL
                  AND s.yield_today >= 0 
                  AND s.yield_today <= 100
            )
            SELECT * FROM system1_data
            UNION ALL
            SELECT * FROM system2_data
            ORDER BY system_id, timestamp
            """
            
            df = pd.read_sql(query, conn)
            conn.close()
            
            # Data cleaning
            df = df.fillna(0)
            df = df.drop_duplicates(subset=['timestamp', 'system_id'])
            
            logger.info(f"✅ Loaded {len(df):,} training records")
            logger.info(f"   System 1: {len(df[df['system_id']==1]):,} records")
            logger.info(f"   System 2: {len(df[df['system_id']==2]):,} records")
            logger.info(f"   Date range: {df['timestamp'].min()} to {df['timestamp'].max()}")
            
            return df
            
        except Exception as e:
            logger.error(f"❌ Failed to load training data: {e}")
            raise
    
    def prepare_training_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Προετοιμασία training data με unified pipeline"""
        logger.info("🔧 Preparing training data with unified pipeline...")
        
        # Feature engineering με το unified pipeline
        processed_df = self.pipeline.engineer_features(df)
        
        # Fit scalers για multi-horizon group
        logger.info("⚖️ Fitting scalers for multi-horizon models...")
        fitted_scalers = self.pipeline.fit_scalers(processed_df, groups=['multi_horizon'])
        
        # Save scalers
        scalers_dir = self.output_dir / "preprocessing"
        saved_files = self.pipeline.save_scalers(scalers_dir)
        logger.info(f"💾 Saved scalers to: {scalers_dir}")
        
        return processed_df
    
    def create_horizon_targets(self, df: pd.DataFrame, horizon: str) -> pd.DataFrame:
        """Δημιουργία targets για συγκεκριμένο horizon"""
        logger.info(f"🎯 Creating {horizon} targets...")
        
        df_targets = df.copy()
        
        if horizon == 'hourly':
            # Hourly yield difference
            df_targets = df_targets.sort_values(['system_id', 'timestamp'])
            df_targets['target'] = df_targets.groupby('system_id')['yield_today'].diff().fillna(0)
            df_targets['target'] = df_targets['target'].clip(lower=0)  # No negative yields
            
        elif horizon == 'daily':
            # Daily total yield
            df_targets['target'] = df_targets['yield_today']
            
        elif horizon == 'monthly':
            # Monthly aggregated yield
            df_targets['month_year'] = df_targets['timestamp'].dt.to_period('M')
            monthly_yield = df_targets.groupby(['system_id', 'month_year'])['yield_today'].sum().reset_index()
            monthly_yield.columns = ['system_id', 'month_year', 'target']
            df_targets = df_targets.merge(monthly_yield, on=['system_id', 'month_year'], how='left')
            
        elif horizon == 'yearly':
            # Yearly aggregated yield
            df_targets['year'] = df_targets['timestamp'].dt.year
            yearly_yield = df_targets.groupby(['system_id', 'year'])['yield_today'].sum().reset_index()
            yearly_yield.columns = ['system_id', 'year', 'target']
            df_targets = df_targets.merge(yearly_yield, on=['system_id', 'year'], how='left')
        
        # Remove invalid targets
        df_targets = df_targets.dropna(subset=['target'])
        df_targets = df_targets[df_targets['target'] >= 0]
        
        logger.info(f"✅ Created {len(df_targets):,} {horizon} targets")
        return df_targets
    
    def benchmark_algorithms(self, X: pd.DataFrame, y: pd.Series, horizon: str, system_id: int) -> Dict[str, Any]:
        """Benchmark algorithms για συγκεκριμένο horizon και system"""
        logger.info(f"🏆 Benchmarking algorithms for {horizon} system {system_id}...")
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, shuffle=True
        )
        
        results = {}
        best_score = -np.inf
        best_algorithm = None
        
        for algo_name, algorithm in self.algorithms.items():
            try:
                logger.info(f"   Testing {algo_name}...")
                
                # Train model
                model = algorithm
                model.fit(X_train, y_train)
                
                # Predictions
                y_pred = model.predict(X_test)
                
                # Metrics
                r2 = r2_score(y_test, y_pred)
                mae = mean_absolute_error(y_test, y_pred)
                rmse = np.sqrt(mean_squared_error(y_test, y_pred))
                
                # Cross-validation
                cv_scores = cross_val_score(model, X_train, y_train, cv=5, scoring='r2')
                cv_r2 = cv_scores.mean()
                
                results[algo_name] = {
                    'model': model,
                    'r2': r2,
                    'mae': mae,
                    'rmse': rmse,
                    'cv_r2': cv_r2,
                    'cv_std': cv_scores.std()
                }
                
                # Track best model
                if r2 > best_score:
                    best_score = r2
                    best_algorithm = algo_name
                
                logger.info(f"     R²: {r2:.4f}, MAE: {mae:.3f}, CV R²: {cv_r2:.4f}")
                
            except Exception as e:
                logger.warning(f"     ⚠️ {algo_name} failed: {e}")
                continue
        
        if best_algorithm:
            logger.info(f"🏆 Best algorithm: {best_algorithm} (R² = {best_score:.4f})")
            # Create metrics without model object for JSON serialization
            metrics_without_model = {k: v for k, v in results[best_algorithm].items() if k != 'model'}
            results['best'] = {
                'algorithm': best_algorithm,
                'model': results[best_algorithm]['model'],
                'metrics': metrics_without_model
            }
        
        return results
    
    def train_model_for_system_horizon(self, df: pd.DataFrame, system_id: int, horizon: str) -> Dict[str, Any]:
        """Εκπαίδευση μοντέλου για συγκεκριμένο system και horizon"""
        logger.info(f"\n🎯 Training {horizon} model for system {system_id}")
        logger.info("=" * 60)
        
        # Filter data για το συγκεκριμένο system
        system_df = df[df['system_id'] == system_id].copy()
        
        if len(system_df) < 100:
            logger.error(f"❌ Insufficient data for system {system_id}: {len(system_df)} records")
            return None
        
        # Create targets για το horizon
        df_with_targets = self.create_horizon_targets(system_df, horizon)
        
        if len(df_with_targets) < 50:
            logger.error(f"❌ Insufficient targets for {horizon}: {len(df_with_targets)} records")
            return None
        
        # Prepare features με unified pipeline
        X_transformed = self.pipeline.transform_for_group(df_with_targets, 'multi_horizon')
        y = df_with_targets['target'].values
        
        # Convert to DataFrame για consistency
        feature_names = [f'feature_{i}' for i in range(X_transformed.shape[1])]
        X_df = pd.DataFrame(X_transformed, columns=feature_names)
        
        logger.info(f"📊 Training data: {len(X_df):,} samples, {len(feature_names)} features")
        
        # Benchmark algorithms
        benchmark_results = self.benchmark_algorithms(X_df, pd.Series(y), horizon, system_id)
        
        if 'best' not in benchmark_results:
            logger.error(f"❌ No successful algorithm for {horizon} system {system_id}")
            return None
        
        best_result = benchmark_results['best']
        
        # Check performance targets
        targets = self.performance_targets.get(horizon, {'r2': 0.8, 'mae': 5.0})
        target_achieved = (
            best_result['metrics']['r2'] >= targets['r2'] and
            best_result['metrics']['mae'] <= targets['mae']
        )
        
        # Prepare model data (exclude model objects from benchmark_results for JSON serialization)
        json_safe_benchmark_results = {}
        for k, v in benchmark_results.items():
            if k != 'best':  # Skip 'best' key
                if isinstance(v, dict):
                    # For algorithm results, exclude the 'model' key
                    json_safe_benchmark_results[k] = {
                        sub_k: sub_v for sub_k, sub_v in v.items()
                        if sub_k != 'model' and not hasattr(sub_v, 'fit')
                    }
                elif not hasattr(v, 'fit'):  # Skip model objects
                    json_safe_benchmark_results[k] = v

        model_data = {
            'model': best_result['model'],
            'algorithm': best_result['algorithm'],
            'metrics': best_result['metrics'],
            'features': feature_names,
            'target_achieved': target_achieved,
            'training_samples': len(X_df),
            'benchmark_results': json_safe_benchmark_results
        }
        
        # Save model
        self.save_model(model_data, system_id, horizon)
        
        # Log results
        metrics = best_result['metrics']
        status = "✅ TARGET MET" if target_achieved else "⚠️ BELOW TARGET"
        logger.info(f"📊 Results: R²={metrics['r2']:.4f}, MAE={metrics['mae']:.3f} - {status}")
        
        return model_data
    
    def save_model(self, model_data: Dict[str, Any], system_id: int, horizon: str):
        """Save trained model με unified pipeline compatibility"""
        model_dir = self.output_dir / f"multi_horizon_{horizon}_system{system_id}"
        model_dir.mkdir(exist_ok=True)
        
        # Save model
        joblib.dump(model_data['model'], model_dir / "model.joblib")
        
        # Get scaler από unified pipeline για multi-horizon group
        scaler = self.pipeline.scalers.get(f"multi_horizon_{self.pipeline_version}")
        if scaler:
            joblib.dump(scaler, model_dir / "scaler.joblib")
        else:
            logger.warning(f"⚠️ No scaler found for multi-horizon group")
        
        # Save metadata
        # Create metadata without model object (JSON serializable)
        metadata = {
            'system_id': system_id,
            'aggregation': horizon,
            'model_type': f'multi_horizon_{horizon}_prediction',
            'best_algorithm': model_data['algorithm'],
            'performance': model_data['metrics'],
            'features': model_data['features'],
            'training_samples': model_data['training_samples'],
            'training_date': datetime.now().isoformat(),
            'pipeline_version': self.pipeline_version,
            'data_source': 'real_database_data',
            'target_achieved': model_data['target_achieved']
        }
        
        with open(model_dir / "metadata.json", 'w') as f:
            json.dump(metadata, f, indent=2, cls=EnhancedJSONEncoder)
        
        logger.info(f"💾 Saved model: {model_dir}")
    
    def train_all_models(self) -> Dict[str, Any]:
        """Train όλα τα multi-horizon μοντέλα"""
        logger.info("🚀 STARTING UNIFIED MULTI-HORIZON MODEL TRAINING")
        logger.info("=" * 80)
        logger.info("Target: 8 multi-horizon models (4 horizons × 2 systems)")
        logger.info("Pipeline: Unified preprocessing with version control")
        logger.info("=" * 80)
        
        # Load and prepare data
        df = self.load_training_data()
        processed_df = self.prepare_training_data(df)
        
        # Train models
        all_results = {
            'training_date': datetime.now().isoformat(),
            'pipeline_version': self.pipeline_version,
            'total_models': 8,
            'successful_models': 0,
            'target_achieved': 0,
            'systems': self.systems,
            'horizons': self.horizons,
            'models': {}
        }
        
        for system_id in self.systems:
            for horizon in self.horizons:
                model_key = f"system_{system_id}_{horizon}"
                
                try:
                    result = self.train_model_for_system_horizon(processed_df, system_id, horizon)
                    
                    if result:
                        all_results['models'][model_key] = result
                        all_results['successful_models'] += 1
                        
                        if result['target_achieved']:
                            all_results['target_achieved'] += 1
                    
                except Exception as e:
                    logger.error(f"❌ Failed to train {model_key}: {e}")
                    continue
        
        # Generate summary
        self.generate_training_summary(all_results)
        
        return all_results
    
    def generate_training_summary(self, results: Dict[str, Any]):
        """Generate comprehensive training summary"""
        logger.info(f"\n🎉 UNIFIED MULTI-HORIZON TRAINING COMPLETED!")
        logger.info("=" * 80)
        
        successful = results['successful_models']
        total = results['total_models']
        target_met = results['target_achieved']
        
        logger.info(f"📊 OVERALL RESULTS:")
        logger.info(f"   Successful models: {successful}/{total} ({successful/total*100:.1f}%)")
        logger.info(f"   Target achieved: {target_met}/{successful} ({target_met/successful*100:.1f}%)")
        logger.info(f"   Pipeline version: {results['pipeline_version']}")
        
        # Per-system summary
        for system_id in self.systems:
            system_models = [k for k in results['models'].keys() if f'system_{system_id}' in k]
            system_successful = len([k for k in system_models if results['models'][k]])
            
            logger.info(f"\n🎯 SYSTEM {system_id} RESULTS:")
            logger.info(f"   Models: {system_successful}/{len(self.horizons)}")
            
            for horizon in self.horizons:
                model_key = f"system_{system_id}_{horizon}"
                if model_key in results['models']:
                    model_result = results['models'][model_key]
                    metrics = model_result['metrics']
                    status = "✅" if model_result['target_achieved'] else "⚠️"
                    logger.info(f"   {horizon}: R²={metrics['r2']:.3f}, MAE={metrics['mae']:.2f} {status}")
        
        # Save summary (exclude model objects from JSON)
        summary_path = self.output_dir / "unified_training_summary.json"

        # Create JSON-safe copy of results (recursive function to handle nested dicts)
        def make_json_safe(obj):
            if isinstance(obj, dict):
                safe_dict = {}
                for k, v in obj.items():
                    if k != 'model' and not hasattr(v, 'fit'):
                        safe_dict[k] = make_json_safe(v)
                return safe_dict
            elif isinstance(obj, list):
                return [make_json_safe(item) for item in obj]
            else:
                return obj

        json_safe_results = make_json_safe(results)

        with open(summary_path, 'w') as f:
            json.dump(json_safe_results, f, indent=2, cls=EnhancedJSONEncoder)
        
        logger.info(f"\n💾 Training summary saved: {summary_path}")

def main():
    """Main training function"""
    try:
        # Initialize trainer με unified pipeline
        trainer = UnifiedYieldBasedTrainer(
            output_dir="models",
            pipeline_version="v1.0.0"
        )
        
        # Train all models
        results = trainer.train_all_models()
        
        logger.info("\n✅ UNIFIED MULTI-HORIZON TRAINING COMPLETED SUCCESSFULLY!")
        return results
        
    except Exception as e:
        logger.error(f"❌ Training failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()
