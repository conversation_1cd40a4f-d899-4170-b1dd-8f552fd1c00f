#!/usr/bin/env python3
"""
Advanced Blending System
========================

Sophisticated model blending with dynamic weight optimization:
- Weighted averaging with performance-based weights
- Dynamic weight adjustment based on prediction confidence
- Temporal weight adaptation for time series
- Bayesian optimization for optimal weight combinations

Target: Optimal model combination for maximum accuracy
Created: June 6, 2025
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import numpy as np
import pandas as pd
import logging
from datetime import datetime
from typing import Dict, List, Tuple, Any, Optional
from scipy.optimize import minimize, differential_evolution
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.model_selection import cross_val_score, TimeSeriesSplit
import warnings
warnings.filterwarnings('ignore')

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AdvancedBlendingSystem:
    """
    Advanced blending system for model combination
    """
    
    def __init__(self, blending_method: str = 'weighted_average'):
        """
        Initialize blending system
        
        Args:
            blending_method: Method for blending ('weighted_average', 'dynamic', 'temporal')
        """
        self.blending_method = blending_method
        self.weights = {}
        self.model_performance = {}
        self.confidence_scores = {}
        
        logger.info("🎭 Advanced Blending System initialized")
        logger.info(f"   Blending method: {blending_method}")
    
    def calculate_performance_weights(self, predictions: Dict[str, np.ndarray], 
                                    y_true: np.ndarray) -> Dict[str, float]:
        """Calculate weights based on individual model performance"""
        logger.info("📊 Calculating performance-based weights...")
        
        model_scores = {}
        
        for model_name, pred in predictions.items():
            # Calculate R² score (higher is better)
            r2 = r2_score(y_true, pred)
            
            # Calculate RMSE (lower is better, so we use 1/RMSE)
            rmse = np.sqrt(mean_squared_error(y_true, pred))
            rmse_score = 1 / (1 + rmse)  # Normalize to 0-1 range
            
            # Combine scores
            combined_score = 0.7 * r2 + 0.3 * rmse_score
            model_scores[model_name] = max(0, combined_score)  # Ensure non-negative
        
        # Normalize weights to sum to 1
        total_score = sum(model_scores.values())
        if total_score > 0:
            weights = {name: score / total_score for name, score in model_scores.items()}
        else:
            # Equal weights if all models perform poorly
            n_models = len(model_scores)
            weights = {name: 1.0 / n_models for name in model_scores.keys()}
        
        self.weights = weights
        self.model_performance = model_scores
        
        logger.info("   Performance weights calculated:")
        for name, weight in weights.items():
            logger.info(f"     {name}: {weight:.4f} (score: {model_scores[name]:.4f})")
        
        return weights
    
    def optimize_weights_scipy(self, predictions: Dict[str, np.ndarray], 
                              y_true: np.ndarray) -> Dict[str, float]:
        """Optimize weights using scipy optimization"""
        logger.info("🎯 Optimizing weights with scipy...")
        
        model_names = list(predictions.keys())
        pred_matrix = np.column_stack([predictions[name] for name in model_names])
        
        def objective(weights):
            # Ensure weights sum to 1
            weights = weights / np.sum(weights)
            
            # Calculate blended prediction
            blended_pred = np.dot(pred_matrix, weights)
            
            # Return negative R² (since we want to maximize R²)
            return -r2_score(y_true, blended_pred)
        
        # Constraints: weights sum to 1 and are non-negative
        constraints = {'type': 'eq', 'fun': lambda w: np.sum(w) - 1}
        bounds = [(0, 1) for _ in range(len(model_names))]
        
        # Initial guess: equal weights
        initial_weights = np.ones(len(model_names)) / len(model_names)
        
        # Optimize
        result = minimize(
            objective, 
            initial_weights, 
            method='SLSQP',
            bounds=bounds,
            constraints=constraints,
            options={'maxiter': 1000}
        )
        
        if result.success:
            optimized_weights = result.x / np.sum(result.x)  # Normalize
            weights_dict = {name: weight for name, weight in zip(model_names, optimized_weights)}
            
            # Calculate final performance
            blended_pred = np.dot(pred_matrix, optimized_weights)
            final_r2 = r2_score(y_true, blended_pred)
            
            logger.info(f"   Optimization successful! Final R²: {final_r2:.4f}")
            logger.info("   Optimized weights:")
            for name, weight in weights_dict.items():
                logger.info(f"     {name}: {weight:.4f}")
            
            self.weights = weights_dict
            return weights_dict
        else:
            logger.warning("   Optimization failed, using performance-based weights")
            return self.calculate_performance_weights(predictions, y_true)
    
    def optimize_weights_evolutionary(self, predictions: Dict[str, np.ndarray], 
                                    y_true: np.ndarray) -> Dict[str, float]:
        """Optimize weights using evolutionary algorithm"""
        logger.info("🧬 Optimizing weights with evolutionary algorithm...")
        
        model_names = list(predictions.keys())
        pred_matrix = np.column_stack([predictions[name] for name in model_names])
        
        def objective(weights):
            # Normalize weights
            weights = weights / np.sum(weights)
            
            # Calculate blended prediction
            blended_pred = np.dot(pred_matrix, weights)
            
            # Return negative R² (since we want to maximize R²)
            return -r2_score(y_true, blended_pred)
        
        # Bounds for each weight
        bounds = [(0, 1) for _ in range(len(model_names))]
        
        # Optimize using differential evolution
        result = differential_evolution(
            objective,
            bounds,
            seed=42,
            maxiter=300,
            popsize=15
        )
        
        if result.success:
            optimized_weights = result.x / np.sum(result.x)  # Normalize
            weights_dict = {name: weight for name, weight in zip(model_names, optimized_weights)}
            
            # Calculate final performance
            blended_pred = np.dot(pred_matrix, optimized_weights)
            final_r2 = r2_score(y_true, blended_pred)
            
            logger.info(f"   Evolutionary optimization successful! Final R²: {final_r2:.4f}")
            logger.info("   Optimized weights:")
            for name, weight in weights_dict.items():
                logger.info(f"     {name}: {weight:.4f}")
            
            self.weights = weights_dict
            return weights_dict
        else:
            logger.warning("   Evolutionary optimization failed, using scipy optimization")
            return self.optimize_weights_scipy(predictions, y_true)
    
    def calculate_prediction_confidence(self, predictions: Dict[str, np.ndarray]) -> np.ndarray:
        """Calculate confidence scores for predictions"""
        # Calculate variance across models as inverse confidence
        pred_matrix = np.column_stack(list(predictions.values()))
        prediction_variance = np.var(pred_matrix, axis=1)
        
        # Convert variance to confidence (lower variance = higher confidence)
        max_variance = np.max(prediction_variance)
        if max_variance > 0:
            confidence = 1 - (prediction_variance / max_variance)
        else:
            confidence = np.ones(len(prediction_variance))
        
        return confidence
    
    def dynamic_weight_blending(self, predictions: Dict[str, np.ndarray], 
                               base_weights: Dict[str, float]) -> np.ndarray:
        """Dynamic blending with confidence-based weight adjustment"""
        logger.info("🔄 Applying dynamic weight blending...")
        
        # Calculate prediction confidence
        confidence = self.calculate_prediction_confidence(predictions)
        
        # Adjust weights based on confidence
        model_names = list(predictions.keys())
        pred_matrix = np.column_stack([predictions[name] for name in model_names])
        base_weight_array = np.array([base_weights[name] for name in model_names])
        
        # Dynamic weight adjustment
        dynamic_predictions = []
        
        for i in range(len(pred_matrix)):
            # Adjust weights based on confidence
            conf_factor = confidence[i]
            
            if conf_factor > 0.8:  # High confidence - use optimized weights
                weights = base_weight_array
            elif conf_factor > 0.5:  # Medium confidence - blend with equal weights
                equal_weights = np.ones(len(model_names)) / len(model_names)
                weights = 0.7 * base_weight_array + 0.3 * equal_weights
            else:  # Low confidence - use more conservative equal weights
                weights = np.ones(len(model_names)) / len(model_names)
            
            # Normalize weights
            weights = weights / np.sum(weights)
            
            # Calculate weighted prediction
            dynamic_pred = np.dot(pred_matrix[i], weights)
            dynamic_predictions.append(dynamic_pred)
        
        return np.array(dynamic_predictions)
    
    def temporal_weight_blending(self, predictions: Dict[str, np.ndarray], 
                                base_weights: Dict[str, float],
                                window_size: int = 24) -> np.ndarray:
        """Temporal blending with time-varying weights"""
        logger.info(f"⏰ Applying temporal weight blending (window: {window_size})...")
        
        model_names = list(predictions.keys())
        pred_matrix = np.column_stack([predictions[name] for name in model_names])
        base_weight_array = np.array([base_weights[name] for name in model_names])
        
        temporal_predictions = []
        
        for i in range(len(pred_matrix)):
            # Calculate temporal weights based on recent performance
            start_idx = max(0, i - window_size)
            
            if i > window_size:
                # Calculate recent performance for weight adjustment
                recent_variance = np.var(pred_matrix[start_idx:i], axis=0)
                
                # Adjust weights (lower variance = higher weight)
                if np.sum(recent_variance) > 0:
                    variance_weights = 1 / (1 + recent_variance)
                    variance_weights = variance_weights / np.sum(variance_weights)
                    
                    # Blend with base weights
                    temporal_weights = 0.6 * base_weight_array + 0.4 * variance_weights
                else:
                    temporal_weights = base_weight_array
            else:
                temporal_weights = base_weight_array
            
            # Normalize weights
            temporal_weights = temporal_weights / np.sum(temporal_weights)
            
            # Calculate weighted prediction
            temporal_pred = np.dot(pred_matrix[i], temporal_weights)
            temporal_predictions.append(temporal_pred)
        
        return np.array(temporal_predictions)
    
    def blend_predictions(self, predictions: Dict[str, np.ndarray], 
                         y_true: np.ndarray = None) -> np.ndarray:
        """Main blending function"""
        logger.info(f"🎭 Blending predictions using {self.blending_method}...")
        
        if self.blending_method == 'weighted_average':
            if y_true is not None:
                weights = self.calculate_performance_weights(predictions, y_true)
            else:
                # Equal weights if no ground truth
                n_models = len(predictions)
                weights = {name: 1.0/n_models for name in predictions.keys()}
            
            # Simple weighted average
            pred_matrix = np.column_stack(list(predictions.values()))
            weight_array = np.array(list(weights.values()))
            blended = np.dot(pred_matrix, weight_array)
            
        elif self.blending_method == 'optimized':
            if y_true is not None:
                weights = self.optimize_weights_evolutionary(predictions, y_true)
                pred_matrix = np.column_stack(list(predictions.values()))
                weight_array = np.array(list(weights.values()))
                blended = np.dot(pred_matrix, weight_array)
            else:
                raise ValueError("Ground truth required for optimized blending")
        
        elif self.blending_method == 'dynamic':
            if y_true is not None:
                base_weights = self.optimize_weights_scipy(predictions, y_true)
                blended = self.dynamic_weight_blending(predictions, base_weights)
            else:
                raise ValueError("Ground truth required for dynamic blending")
        
        elif self.blending_method == 'temporal':
            if y_true is not None:
                base_weights = self.optimize_weights_scipy(predictions, y_true)
                blended = self.temporal_weight_blending(predictions, base_weights)
            else:
                raise ValueError("Ground truth required for temporal blending")
        
        else:
            raise ValueError(f"Unknown blending method: {self.blending_method}")
        
        logger.info(f"✅ Blending completed using {self.blending_method}")
        return blended
    
    def evaluate_blending(self, predictions: Dict[str, np.ndarray], 
                         y_true: np.ndarray) -> Dict[str, Any]:
        """Evaluate different blending methods"""
        logger.info("📊 Evaluating blending methods...")
        
        methods = ['weighted_average', 'optimized', 'dynamic', 'temporal']
        results = {}
        
        for method in methods:
            logger.info(f"   Testing {method}...")
            
            # Create temporary blending system
            temp_blender = AdvancedBlendingSystem(blending_method=method)
            
            try:
                blended_pred = temp_blender.blend_predictions(predictions, y_true)
                
                # Calculate metrics
                rmse = np.sqrt(mean_squared_error(y_true, blended_pred))
                mae = mean_absolute_error(y_true, blended_pred)
                r2 = r2_score(y_true, blended_pred)
                
                results[method] = {
                    'rmse': rmse,
                    'mae': mae,
                    'r2': r2,
                    'weights': temp_blender.weights
                }
                
                logger.info(f"     {method}: R²={r2:.4f}, RMSE={rmse:.3f}")
                
            except Exception as e:
                logger.warning(f"     {method} failed: {e}")
                results[method] = {'error': str(e)}
        
        # Find best method
        valid_results = {k: v for k, v in results.items() if 'error' not in v}
        if valid_results:
            best_method = max(valid_results.keys(), key=lambda k: valid_results[k]['r2'])
            logger.info(f"🏆 Best blending method: {best_method} (R²={valid_results[best_method]['r2']:.4f})")
            
            # Update current blending method
            self.blending_method = best_method
            self.weights = valid_results[best_method]['weights']
        
        return results

def main():
    """Test advanced blending system"""
    logger.info("🎭 Testing Advanced Blending System")
    logger.info("=" * 60)
    
    # Create synthetic predictions from multiple models
    np.random.seed(42)
    n_samples = 1000
    
    # Generate ground truth
    t = np.linspace(0, 10, n_samples)
    y_true = 2 * np.sin(t) + 0.5 * np.cos(2*t) + np.random.normal(0, 0.1, n_samples)
    
    # Generate model predictions with different characteristics
    predictions = {
        'model_1': y_true + np.random.normal(0, 0.2, n_samples),  # Good model
        'model_2': y_true + np.random.normal(0, 0.3, n_samples),  # Decent model
        'model_3': y_true + np.random.normal(0, 0.4, n_samples),  # Weaker model
        'model_4': y_true * 0.9 + np.random.normal(0, 0.25, n_samples),  # Biased model
    }
    
    # Calculate individual model performance
    logger.info("📊 Individual Model Performance:")
    for name, pred in predictions.items():
        r2 = r2_score(y_true, pred)
        rmse = np.sqrt(mean_squared_error(y_true, pred))
        logger.info(f"   {name}: R²={r2:.4f}, RMSE={rmse:.3f}")
    
    # Test blending system
    blending_system = AdvancedBlendingSystem()
    
    # Evaluate all blending methods
    blending_results = blending_system.evaluate_blending(predictions, y_true)
    
    # Display results
    logger.info("\n🎯 BLENDING RESULTS SUMMARY")
    logger.info("=" * 60)
    
    for method, results in blending_results.items():
        if 'error' not in results:
            logger.info(f"{method}:")
            logger.info(f"   R² Score: {results['r2']:.4f}")
            logger.info(f"   RMSE: {results['rmse']:.3f}")
            logger.info(f"   MAE: {results['mae']:.3f}")
            
            if 'weights' in results:
                logger.info("   Weights:")
                for model, weight in results['weights'].items():
                    logger.info(f"     {model}: {weight:.4f}")
        else:
            logger.info(f"{method}: FAILED - {results['error']}")
        logger.info("")
    
    logger.info("✅ Advanced Blending System test completed!")
    
    return blending_system, blending_results

if __name__ == "__main__":
    blending_system, results = main()
