#!/usr/bin/env python3
"""
Simplified Corrected Data Pipeline
==================================

Simplified implementation της επιστημονικής ανάλυσης χωρίς external dependencies:

ΚΥΡΙΕΣ ΔΙΟΡΘΩΣΕΙΣ:
1. Σωστή ερμηνεία yield_today ως daily totals (MAX per day)
2. Simplified solar geometry features
3. Weather aggregates αντί για raw values
4. Enhanced feature engineering
5. GradientBoostingRegressor με MAE optimization

Δημιουργήθηκε: 2025-06-06
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))

import pandas as pd
import numpy as np
import psycopg2
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import logging
import warnings
warnings.filterwarnings('ignore')

# ML imports
from sklearn.ensemble import GradientBoostingRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import joblib
import json
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimplifiedCorrectedPipeline:
    """
    Simplified corrected pipeline implementing key scientific recommendations
    """
    
    def __init__(self):
        self.pipeline_start = datetime.now()
        
        # System coordinates για solar geometry (Marathon, Attica)
        self.latitude = 38.141348260997596
        self.longitude = 24.0071653937747
        
        # Corrected model directory
        self.corrected_models_dir = Path("models/simplified_corrected")
        self.corrected_models_dir.mkdir(exist_ok=True, parents=True)
        
        logger.info("🔧 Initialized SimplifiedCorrectedPipeline")
        logger.info(f"📍 Solar coordinates: {self.latitude:.4f}, {self.longitude:.4f}")
    
    def load_corrected_training_data(self) -> pd.DataFrame:
        """Load training data με ΣΩΣΤΗ ερμηνεία του yield_today"""
        logger.info("📊 Loading corrected training data...")
        
        try:
            conn = psycopg2.connect(
                host='localhost',
                database='solar_prediction',
                user='postgres',
                password='postgres'
            )
            
            # CORRECTED QUERY - Key scientific fix
            corrected_query = """
            WITH daily_aggregates AS (
                -- System 1 data με ΣΩΣΤΑ daily totals
                SELECT 
                    DATE(s.timestamp) as date,
                    1 as system_id,
                    MAX(s.yield_today) as daily_yield,  -- ✅ ΚΥΡΙΑ ΔΙΟΡΘΩΣΗ
                    AVG(s.temperature) as avg_temp,
                    AVG(s.soc) as avg_soc,
                    AVG(s.bat_power) as avg_bat_power,
                    -- Weather aggregates
                    AVG(w.global_horizontal_irradiance) as avg_ghi,
                    MAX(w.global_horizontal_irradiance) as max_ghi,
                    AVG(w.temperature_2m) as avg_weather_temp,
                    MAX(w.cloud_cover) as max_cloud_cover,
                    AVG(w.cloud_cover) as avg_cloud_cover,
                    AVG(w.relative_humidity_2m) as avg_humidity
                FROM solax_data s
                LEFT JOIN weather_data w ON DATE_TRUNC('hour', s.timestamp) = DATE_TRUNC('hour', w.timestamp)
                WHERE s.timestamp >= '2024-03-01' 
                  AND s.yield_today IS NOT NULL
                  AND s.yield_today >= 0 
                  AND s.yield_today <= 100
                GROUP BY DATE(s.timestamp)
                
                UNION ALL
                
                -- System 2 data με ΣΩΣΤΑ daily totals
                SELECT 
                    DATE(s.timestamp) as date,
                    2 as system_id,
                    MAX(s.yield_today) as daily_yield,  -- ✅ ΚΥΡΙΑ ΔΙΟΡΘΩΣΗ
                    AVG(s.temperature) as avg_temp,
                    AVG(s.soc) as avg_soc,
                    AVG(s.bat_power) as avg_bat_power,
                    -- Weather aggregates
                    AVG(w.global_horizontal_irradiance) as avg_ghi,
                    MAX(w.global_horizontal_irradiance) as max_ghi,
                    AVG(w.temperature_2m) as avg_weather_temp,
                    MAX(w.cloud_cover) as max_cloud_cover,
                    AVG(w.cloud_cover) as avg_cloud_cover,
                    AVG(w.relative_humidity_2m) as avg_humidity
                FROM solax_data2 s
                LEFT JOIN weather_data w ON DATE_TRUNC('hour', s.timestamp) = DATE_TRUNC('hour', w.timestamp)
                WHERE s.timestamp >= '2024-03-01' 
                  AND s.yield_today IS NOT NULL
                  AND s.yield_today >= 0 
                  AND s.yield_today <= 100
                GROUP BY DATE(s.timestamp)
            )
            SELECT * FROM daily_aggregates
            WHERE daily_yield > 10  -- Filter unrealistic low values
            ORDER BY system_id, date
            """
            
            df = pd.read_sql(corrected_query, conn)
            conn.close()
            
            # Data quality validation
            logger.info(f"✅ Loaded {len(df):,} corrected daily records")
            logger.info(f"   Date range: {df['date'].min()} to {df['date'].max()}")
            logger.info(f"   System 1 records: {len(df[df['system_id'] == 1]):,}")
            logger.info(f"   System 2 records: {len(df[df['system_id'] == 2]):,}")
            
            # Validate yield ranges
            for system_id in [1, 2]:
                system_data = df[df['system_id'] == system_id]
                if len(system_data) > 0:
                    yield_stats = {
                        'min': system_data['daily_yield'].min(),
                        'max': system_data['daily_yield'].max(),
                        'mean': system_data['daily_yield'].mean(),
                        'std': system_data['daily_yield'].std()
                    }
                    logger.info(f"   System {system_id} yield range: {yield_stats['min']:.1f}-{yield_stats['max']:.1f} kWh (mean: {yield_stats['mean']:.1f})")
            
            return df
            
        except Exception as e:
            logger.error(f"❌ Failed to load corrected training data: {e}")
            raise
    
    def engineer_simplified_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Simplified feature engineering με key improvements"""
        logger.info("🔧 Engineering simplified features...")
        
        # Convert date column
        df['date'] = pd.to_datetime(df['date'])
        
        # Sort by system and date
        df = df.sort_values(['system_id', 'date']).reset_index(drop=True)
        
        enhanced_dfs = []
        
        for system_id in [1, 2]:
            system_df = df[df['system_id'] == system_id].copy()
            
            if len(system_df) == 0:
                continue
            
            # 1. TEMPORAL FEATURES
            system_df['day_of_year'] = system_df['date'].dt.dayofyear
            system_df['month'] = system_df['date'].dt.month
            system_df['day_of_week'] = system_df['date'].dt.dayofweek
            
            # Trigonometric encoding
            system_df['day_of_year_sin'] = np.sin(2 * np.pi * system_df['day_of_year'] / 365)
            system_df['day_of_year_cos'] = np.cos(2 * np.pi * system_df['day_of_year'] / 365)
            system_df['month_sin'] = np.sin(2 * np.pi * system_df['month'] / 12)
            system_df['month_cos'] = np.cos(2 * np.pi * system_df['month'] / 12)
            
            # 2. SIMPLIFIED SOLAR GEOMETRY
            # Solar declination
            declination = 23.45 * np.sin(np.radians(360 * (284 + system_df['day_of_year']) / 365))
            
            # Solar elevation at noon (simplified)
            system_df['solar_elevation'] = np.degrees(np.arcsin(
                np.sin(np.radians(self.latitude)) * np.sin(np.radians(declination)) +
                np.cos(np.radians(self.latitude)) * np.cos(np.radians(declination))
            ))
            
            # Day length calculation
            hour_angle = np.degrees(np.arccos(-np.tan(np.radians(self.latitude)) * np.tan(np.radians(declination))))
            system_df['day_length'] = 2 * hour_angle / 15  # Hours of daylight
            
            # 3. ROLLING FEATURES (Corrected - Daily basis)
            for window in [3, 7]:
                system_df[f'yield_rolling_mean_{window}d'] = system_df['daily_yield'].rolling(window, min_periods=1).mean()
                system_df[f'ghi_rolling_mean_{window}d'] = system_df['avg_ghi'].rolling(window, min_periods=1).mean()
            
            # 4. LAG FEATURES (Corrected - Daily basis)
            for lag in [1, 3, 7]:
                system_df[f'yield_lag_{lag}d'] = system_df['daily_yield'].shift(lag)
                system_df[f'ghi_lag_{lag}d'] = system_df['avg_ghi'].shift(lag)
            
            # 5. INTERACTION FEATURES
            system_df['temp_ghi_interaction'] = system_df['avg_weather_temp'] * system_df['avg_ghi'] / 1000
            system_df['cloud_ghi_interaction'] = (100 - system_df['avg_cloud_cover']) * system_df['avg_ghi'] / 100
            system_df['solar_elevation_ghi'] = system_df['solar_elevation'] * system_df['avg_ghi'] / 1000
            
            # 6. WEATHER EFFICIENCY FEATURES
            system_df['cloud_efficiency'] = (100 - system_df['avg_cloud_cover']) / 100
            system_df['temperature_efficiency'] = 1 - np.maximum(0, (system_df['avg_weather_temp'] - 25) * 0.004)
            
            # 7. SYSTEM-SPECIFIC FEATURES
            system_df['system_capacity'] = 10.5 if system_id == 1 else 12.0
            system_df['system_efficiency'] = 0.85 if system_id == 1 else 0.90
            system_df['system_advantage'] = 1.0 if system_id == 1 else 1.1
            
            # 8. SEASONAL FEATURES
            system_df['is_summer'] = ((system_df['month'] >= 6) & (system_df['month'] <= 8)).astype(int)
            system_df['is_winter'] = ((system_df['month'] <= 2) | (system_df['month'] == 12)).astype(int)
            
            enhanced_dfs.append(system_df)
        
        # Combine systems
        if enhanced_dfs:
            enhanced_df = pd.concat(enhanced_dfs, ignore_index=True)
            enhanced_df = enhanced_df.sort_values(['system_id', 'date']).reset_index(drop=True)
        else:
            enhanced_df = df
        
        # Fill missing values
        enhanced_df = enhanced_df.fillna(method='bfill').fillna(method='ffill').fillna(0)
        
        logger.info(f"✅ Simplified feature engineering complete: {enhanced_df.shape[1]} features")
        
        return enhanced_df
    
    def train_simplified_model(self, system_id: int, enhanced_df: pd.DataFrame) -> Dict[str, Any]:
        """Train simplified corrected model"""
        
        logger.info(f"\n🚀 Training simplified corrected model για System {system_id}")
        
        # Filter data για specific system
        system_data = enhanced_df[enhanced_df['system_id'] == system_id].copy()
        
        if len(system_data) < 50:
            logger.warning(f"⚠️ Insufficient data για System {system_id}: {len(system_data)} records")
            return None
        
        # Prepare features και target
        feature_columns = [col for col in system_data.columns if col not in [
            'date', 'system_id', 'daily_yield'
        ]]
        
        X = system_data[feature_columns]
        y = system_data['daily_yield']  # ✅ CORRECTED TARGET: Daily totals
        
        logger.info(f"📊 Training data: {len(X)} samples, {len(feature_columns)} features")
        logger.info(f"   Target range: {y.min():.1f} - {y.max():.1f} kWh (mean: {y.mean():.1f})")
        
        # Time-based train/test split
        split_idx = int(0.8 * len(X))
        X_train, X_test = X.iloc[:split_idx], X.iloc[split_idx:]
        y_train, y_test = y.iloc[:split_idx], y.iloc[split_idx:]
        
        # Scale features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        # Train GradientBoostingRegressor
        model = GradientBoostingRegressor(
            loss='absolute_error',  # MAE optimization
            n_estimators=300,
            max_depth=6,
            learning_rate=0.1,
            subsample=0.8,
            random_state=42
        )
        
        logger.info("🚀 Training GradientBoostingRegressor...")
        training_start = datetime.now()
        
        model.fit(X_train_scaled, y_train)
        
        training_time = (datetime.now() - training_start).total_seconds()
        
        # Make predictions
        y_pred_train = model.predict(X_train_scaled)
        y_pred_test = model.predict(X_test_scaled)
        
        # Calculate metrics
        train_metrics = {
            'r2': r2_score(y_train, y_pred_train),
            'mae': mean_absolute_error(y_train, y_pred_train),
            'rmse': np.sqrt(mean_squared_error(y_train, y_pred_train))
        }
        
        test_metrics = {
            'r2': r2_score(y_test, y_pred_test),
            'mae': mean_absolute_error(y_test, y_pred_test),
            'rmse': np.sqrt(mean_squared_error(y_test, y_pred_test))
        }
        
        # Feature importance
        feature_importance = dict(zip(feature_columns, model.feature_importances_))
        top_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)[:10]
        
        result = {
            'system_id': system_id,
            'model': model,
            'scaler': scaler,
            'features': feature_columns,
            'training_samples': len(X_train),
            'test_samples': len(X_test),
            'training_time': training_time,
            'train_metrics': train_metrics,
            'test_metrics': test_metrics,
            'feature_importance': feature_importance,
            'top_features': top_features,
            'target_range': {'min': float(y.min()), 'max': float(y.max()), 'mean': float(y.mean())}
        }
        
        # Log results
        logger.info(f"📊 SIMPLIFIED CORRECTED MODEL RESULTS:")
        logger.info(f"   Training R²: {train_metrics['r2']:.4f}, MAE: {train_metrics['mae']:.3f}")
        logger.info(f"   Test R²: {test_metrics['r2']:.4f}, MAE: {test_metrics['mae']:.3f}")
        logger.info(f"   Training time: {training_time:.1f} seconds")
        logger.info(f"   Top feature: {top_features[0][0]} ({top_features[0][1]:.4f})")
        
        # Expected improvement
        original_mae = 28.98 if system_id == 1 else 13.83
        improvement = (1 - test_metrics['mae'] / original_mae) * 100
        logger.info(f"   Expected improvement: {improvement:+.1f}% vs original")
        
        return result

    def save_simplified_model(self, result: Dict[str, Any]):
        """Save simplified corrected model"""

        system_id = result['system_id']
        model_name = f"simplified_corrected_system{system_id}"
        model_dir = self.corrected_models_dir / model_name
        model_dir.mkdir(exist_ok=True)

        # Save model και scaler
        joblib.dump(result['model'], model_dir / "model.joblib")
        joblib.dump(result['scaler'], model_dir / "scaler.joblib")

        # Save metadata
        metadata = {
            'model_name': model_name,
            'system_id': result['system_id'],
            'features': result['features'],
            'training_samples': result['training_samples'],
            'test_samples': result['test_samples'],
            'training_time': result['training_time'],
            'performance': {
                'train_metrics': result['train_metrics'],
                'test_metrics': result['test_metrics']
            },
            'feature_importance': result['feature_importance'],
            'top_features': result['top_features'],
            'target_range': result['target_range'],
            'scientific_improvements': [
                'Corrected target variable (daily totals)',
                'Simplified solar geometry features',
                'Weather aggregates',
                'Enhanced interaction features',
                'GradientBoostingRegressor με MAE optimization'
            ],
            'training_date': datetime.now().isoformat()
        }

        with open(model_dir / "metadata.json", 'w') as f:
            json.dump(metadata, f, indent=2, default=str)

        logger.info(f"💾 Simplified model saved: {model_dir}")

    def test_corrected_predictions(self, result: Dict[str, Any], enhanced_df: pd.DataFrame) -> Dict[str, Any]:
        """Test corrected predictions"""

        system_id = result['system_id']
        model = result['model']
        scaler = result['scaler']
        features = result['features']

        # Get recent data
        system_data = enhanced_df[enhanced_df['system_id'] == system_id].tail(10)

        if len(system_data) == 0:
            return {'test': 'no_data'}

        # Prepare features
        X_test = system_data[features]
        X_test_scaled = scaler.transform(X_test)

        # Make predictions
        predictions = model.predict(X_test_scaled)
        actual = system_data['daily_yield'].values

        # Calculate test metrics
        test_mae = mean_absolute_error(actual, predictions)
        test_r2 = r2_score(actual, predictions)

        # Range validation
        in_range = np.sum((predictions >= 60) & (predictions <= 80)) / len(predictions) * 100

        test_result = {
            'test_samples': len(predictions),
            'predictions': predictions.tolist(),
            'actual': actual.tolist(),
            'test_mae': test_mae,
            'test_r2': test_r2,
            'range_accuracy': in_range,
            'realistic': in_range > 70,
            'avg_prediction': float(np.mean(predictions)),
            'avg_actual': float(np.mean(actual))
        }

        logger.info(f"🧪 Test για System {system_id}:")
        logger.info(f"   Test MAE: {test_mae:.2f} kWh")
        logger.info(f"   Range accuracy: {in_range:.1f}%")
        logger.info(f"   Avg prediction: {test_result['avg_prediction']:.1f} kWh")
        logger.info(f"   Avg actual: {test_result['avg_actual']:.1f} kWh")

        return test_result

    def run_simplified_pipeline(self) -> Dict[str, Any]:
        """Run simplified corrected pipeline"""

        logger.info("🚀 RUNNING SIMPLIFIED CORRECTED PIPELINE")
        logger.info("=" * 80)
        logger.info("Key scientific improvements:")
        logger.info("1. Corrected target variable (daily totals)")
        logger.info("2. Simplified solar geometry")
        logger.info("3. Weather aggregates")
        logger.info("4. Enhanced features")
        logger.info("5. GradientBoostingRegressor")
        logger.info("=" * 80)

        results = {
            'pipeline_start': self.pipeline_start.isoformat(),
            'models': {},
            'tests': {},
            'overall': {}
        }

        try:
            # Load corrected data
            logger.info("\n📊 Loading corrected training data...")
            enhanced_df = self.load_corrected_training_data()

            # Engineer features
            logger.info("\n🔧 Engineering simplified features...")
            enhanced_df = self.engineer_simplified_features(enhanced_df)

            # Train models για both systems
            logger.info("\n🤖 Training simplified models...")

            all_mae = []
            all_r2 = []

            for system_id in [1, 2]:
                # Train model
                result = self.train_simplified_model(system_id, enhanced_df)

                if result:
                    # Save model
                    self.save_simplified_model(result)

                    # Test predictions
                    test_result = self.test_corrected_predictions(result, enhanced_df)

                    # Store results
                    results['models'][f'system{system_id}'] = {
                        'performance': result['test_metrics'],
                        'top_feature': result['top_features'][0] if result['top_features'] else None,
                        'training_samples': result['training_samples']
                    }

                    results['tests'][f'system{system_id}'] = test_result

                    all_mae.append(result['test_metrics']['mae'])
                    all_r2.append(result['test_metrics']['r2'])

            # Overall performance
            if all_mae:
                results['overall'] = {
                    'average_mae': np.mean(all_mae),
                    'average_r2': np.mean(all_r2),
                    'models_trained': len(all_mae)
                }

            results['pipeline_end'] = datetime.now().isoformat()
            results['duration'] = (datetime.now() - self.pipeline_start).total_seconds()

            logger.info("\n✅ SIMPLIFIED PIPELINE COMPLETED!")
            return results

        except Exception as e:
            logger.error(f"❌ Simplified pipeline failed: {e}")
            results['error'] = str(e)
            return results

def main():
    """Main simplified pipeline function"""
    try:
        print("\n🚀 SIMPLIFIED CORRECTED DATA PIPELINE")
        print("=" * 60)
        print("Implementing key scientific improvements:")
        print("• Corrected target variable (daily totals)")
        print("• Simplified solar geometry")
        print("• Weather aggregates")
        print("• GradientBoostingRegressor με MAE optimization")

        # Run pipeline
        pipeline = SimplifiedCorrectedPipeline()
        results = pipeline.run_simplified_pipeline()

        # Display results
        print(f"\n📊 SIMPLIFIED PIPELINE RESULTS:")
        print("=" * 60)

        if 'error' in results:
            print(f"❌ Pipeline failed: {results['error']}")
            return False

        # Overall performance
        overall = results.get('overall', {})
        print(f"📈 Overall Performance:")
        print(f"   Models trained: {overall.get('models_trained', 0)}")
        print(f"   Average MAE: {overall.get('average_mae', 0):.3f} kWh")
        print(f"   Average R²: {overall.get('average_r2', 0):.4f}")

        # Individual results
        for system_key, model_info in results.get('models', {}).items():
            performance = model_info['performance']
            print(f"\n🤖 {system_key.upper()}:")
            print(f"   MAE: {performance['mae']:.3f} kWh")
            print(f"   R²: {performance['r2']:.4f}")
            print(f"   Training samples: {model_info['training_samples']:,}")

            if model_info['top_feature']:
                top_feature, importance = model_info['top_feature']
                print(f"   Top feature: {top_feature} ({importance:.4f})")

        # Test results
        print(f"\n🧪 TEST RESULTS:")
        for system_key, test_info in results.get('tests', {}).items():
            if test_info.get('test') != 'no_data':
                print(f"   {system_key.upper()}:")
                print(f"     Test MAE: {test_info['test_mae']:.2f} kWh")
                print(f"     Range accuracy: {test_info['range_accuracy']:.1f}%")
                print(f"     Avg prediction: {test_info['avg_prediction']:.1f} kWh")
                print(f"     Realistic: {'✅' if test_info['realistic'] else '❌'}")

        # Comparison με original
        print(f"\n📈 COMPARISON με ORIGINAL:")
        print(f"   Original: System 1: 29.0 kWh, System 2: 13.8 kWh")

        if 'tests' in results:
            for system_key, test_info in results['tests'].items():
                if test_info.get('test') != 'no_data':
                    system_id = system_key[-1]
                    original = 28.98 if system_id == '1' else 13.83
                    corrected = test_info['avg_prediction']
                    improvement = (corrected / original - 1) * 100
                    print(f"   {system_key}: {corrected:.1f} kWh ({improvement:+.1f}% vs original)")

        # Save results
        results_dir = Path("analysis_results")
        results_dir.mkdir(exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = results_dir / f"simplified_corrected_results_{timestamp}.json"

        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)

        print(f"\n💾 Results saved: {results_file}")
        print(f"⏱️ Duration: {results.get('duration', 0):.1f} seconds")

        # Final assessment
        avg_mae = overall.get('average_mae', 0)
        if avg_mae < 5:
            print(f"\n🏆 EXCELLENT RESULTS - SCIENTIFIC ANALYSIS VALIDATED!")
            print(f"✅ Target variable correction successful")
            print(f"📊 MAE < 5 kWh achieved")
        elif avg_mae < 10:
            print(f"\n✅ GOOD RESULTS - SIGNIFICANT IMPROVEMENT!")
            print(f"📈 Major improvement vs original pipeline")
        else:
            print(f"\n⚠️ MODERATE RESULTS - FURTHER OPTIMIZATION NEEDED")

        return True

    except Exception as e:
        print(f"❌ Simplified pipeline failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
