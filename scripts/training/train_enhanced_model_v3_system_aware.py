#!/usr/bin/env python3
"""
Train Enhanced Model v3 - System-Aware Solar Prediction Model

This script addresses the critical issues found in Enhanced Model v2:
1. Adds system_id to distinguish between the two solar systems
2. Includes consumption pattern features
3. Adds battery behavior features
4. Implements seasonal pattern matching

Key improvements:
- System-specific training with consumption patterns
- Battery-aware feature engineering
- Seasonal pattern matching for better accuracy
- Separate models or system-aware unified model
"""

import os
import sys
import json
import logging
import argparse
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Any, Optional
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

# Database connection
import psycopg2
from psycopg2.extras import RealDictCursor
import os
from dotenv import load_dotenv

# Setup basic logging
import logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# ML imports
import xgboost as xgb
import lightgbm as lgb
from sklearn.ensemble import RandomForestRegressor, StackingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import optuna
import joblib

class DatabaseConnection:
    """Simple database connection class"""

    def __init__(self):
        load_dotenv()
        self.connection = None

    def get_connection(self):
        if self.connection is None:
            self.connection = psycopg2.connect(
                host=os.getenv('DB_HOST', 'localhost'),
                database=os.getenv('DB_NAME', 'solar_prediction'),
                user=os.getenv('DB_USER', 'postgres'),
                password=os.getenv('DB_PASSWORD', 'postgres')
            )
        return self.connection

class SystemAwareDataLoader:
    """Load and prepare system-aware training data"""

    def __init__(self, db_connection):
        self.db = db_connection

    def load_system_aware_data(self) -> pd.DataFrame:
        """Load data with system identification"""
        logger.info("Loading system-aware training data...")

        query = """
        WITH system1_data AS (
            SELECT
                timestamp, ac_power, soc, bat_power, powerdc1, powerdc2,
                feedin_power, consume_energy, feedin_energy, yield_today,
                1 as system_id,
                'system1' as system_name
            FROM solax_data
            WHERE timestamp >= '2024-03-01'
            AND ac_power IS NOT NULL
        ),
        system2_data AS (
            SELECT
                timestamp, ac_power, soc, bat_power, powerdc1, powerdc2,
                feedin_power, consume_energy, feedin_energy, yield_today,
                2 as system_id,
                'system2' as system_name
            FROM solax_data2
            WHERE timestamp >= '2024-03-01'
            AND ac_power IS NOT NULL
        ),
        combined_systems AS (
            SELECT * FROM system1_data
            UNION ALL
            SELECT * FROM system2_data
        ),
        weather_data AS (
            SELECT
                timestamp, ghi, dni, dhi, temperature, cloud_cover,
                aod, is_forecast
            FROM cams_radiation_data
            WHERE timestamp >= '2024-03-01'
        )
        SELECT
            cs.*,
            w.ghi, w.dni, w.dhi, w.temperature, w.cloud_cover, w.aod
        FROM combined_systems cs
        LEFT JOIN weather_data w ON DATE_TRUNC('hour', cs.timestamp) = DATE_TRUNC('hour', w.timestamp)
        WHERE w.ghi IS NOT NULL
        ORDER BY cs.timestamp
        """

        df = pd.read_sql(query, self.db.get_connection())
        logger.info(f"Loaded {len(df)} records with system identification")

        return df

    def add_consumption_pattern_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add consumption pattern features based on historical analysis"""
        logger.info("Adding consumption pattern features...")

        # Historical consumption patterns (from yesterday's analysis)
        consumption_patterns = {
            1: {  # System 1 (Σπίτι Πάνω)
                'daily_consumption_avg': 22.45,
                'grid_dependency_ratio': 0.0,
                'self_sufficiency': 1.0,
                'consumption_pattern': 'low_steady',
                'grid_interaction': 'minimal'
            },
            2: {  # System 2 (Σπίτι Κάτω)
                'daily_consumption_avg': 35.29,
                'grid_dependency_ratio': 0.395,
                'self_sufficiency': 0.605,
                'consumption_pattern': 'high_variable',
                'grid_interaction': 'heavy'
            }
        }

        # Add consumption features
        for system_id, patterns in consumption_patterns.items():
            mask = df['system_id'] == system_id
            df.loc[mask, 'daily_consumption_avg'] = patterns['daily_consumption_avg']
            df.loc[mask, 'grid_dependency_ratio'] = patterns['grid_dependency_ratio']
            df.loc[mask, 'self_sufficiency'] = patterns['self_sufficiency']
            df.loc[mask, 'consumption_pattern_encoded'] = 1 if patterns['consumption_pattern'] == 'low_steady' else 2
            df.loc[mask, 'grid_interaction_encoded'] = 1 if patterns['grid_interaction'] == 'minimal' else 2

        return df

    def add_battery_behavior_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add battery behavior features"""
        logger.info("Adding battery behavior features...")

        # Sort by system and timestamp for proper calculation
        df = df.sort_values(['system_id', 'timestamp'])

        # Calculate morning SOC (first SOC of each day)
        df['date'] = df['timestamp'].dt.date
        morning_soc = df.groupby(['system_id', 'date'])['soc'].first().reset_index()
        morning_soc.rename(columns={'soc': 'morning_soc'}, inplace=True)

        df = df.merge(morning_soc, on=['system_id', 'date'], how='left')

        # Calculate battery energy stored during the day
        df['battery_energy_stored'] = (df['soc'] - df['morning_soc']) / 100 * 12.0  # 12kWh capacity

        # Battery utilization pattern
        df['battery_utilization'] = df.groupby(['system_id', 'date'])['bat_power'].transform('std')

        # Grid interaction history (rolling average)
        df['grid_interaction_history'] = df.groupby('system_id')['consume_energy'].transform(
            lambda x: x.rolling(window=24, min_periods=1).mean()
        )

        return df

    def add_seasonal_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add seasonal and temporal features"""
        logger.info("Adding seasonal features...")

        df['hour'] = df['timestamp'].dt.hour
        df['day_of_year'] = df['timestamp'].dt.dayofyear
        df['month'] = df['timestamp'].dt.month
        df['is_weekend'] = df['timestamp'].dt.dayofweek.isin([5, 6]).astype(int)
        df['season'] = (df['month'] % 12 + 3) // 3 % 4

        # Consumption timing patterns
        df['is_peak_consumption'] = df['hour'].isin([18, 19, 20, 21]).astype(int)
        df['is_peak_production'] = df['hour'].isin([11, 12, 13, 14]).astype(int)

        return df

class SystemAwareFeatureEngineer:
    """Feature engineering for system-aware model"""

    def __init__(self):
        self.feature_columns = None
        self.normalization_params = {}

    def create_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create all features for training"""
        logger.info("Creating system-aware features...")

        # Basic features
        feature_df = df.copy()

        # Weather-based features
        feature_df['ghi_normalized'] = self._normalize_feature(feature_df['ghi'], 0, 1200)
        feature_df['dni_normalized'] = self._normalize_feature(feature_df['dni'], 0, 1000)
        feature_df['dhi_normalized'] = self._normalize_feature(feature_df['dhi'], 0, 600)
        feature_df['temperature_normalized'] = self._normalize_feature(feature_df['temperature'], -10, 45)
        feature_df['cloud_cover_normalized'] = self._normalize_feature(feature_df['cloud_cover'], 0, 100)

        # Time features
        feature_df['hour_normalized'] = self._normalize_feature(feature_df['hour'], 0, 23)
        feature_df['day_of_year_normalized'] = self._normalize_feature(feature_df['day_of_year'], 1, 365)
        feature_df['month_normalized'] = self._normalize_feature(feature_df['month'], 1, 12)
        feature_df['season_normalized'] = self._normalize_feature(feature_df['season'], 0, 3)

        # System features
        feature_df['system_id_normalized'] = self._normalize_feature(feature_df['system_id'], 1, 2)
        feature_df['soc_normalized'] = self._normalize_feature(feature_df['soc'], 0, 100)
        feature_df['bat_power_normalized'] = self._normalize_feature(feature_df['bat_power'], -6000, 6000)

        # Consumption pattern features
        feature_df['daily_consumption_normalized'] = self._normalize_feature(feature_df['daily_consumption_avg'], 20, 40)
        feature_df['grid_dependency_normalized'] = self._normalize_feature(feature_df['grid_dependency_ratio'], 0, 1)
        feature_df['self_sufficiency_normalized'] = self._normalize_feature(feature_df['self_sufficiency'], 0, 1)

        # Battery behavior features
        feature_df['morning_soc_normalized'] = self._normalize_feature(feature_df['morning_soc'], 0, 100)
        feature_df['battery_energy_stored_normalized'] = self._normalize_feature(feature_df['battery_energy_stored'], -12, 12)
        feature_df['battery_utilization_normalized'] = self._normalize_feature(feature_df['battery_utilization'], 0, 3000)

        # Derived features
        feature_df['weather_production_factor'] = (
            feature_df['ghi_normalized'] * 0.6 +
            feature_df['dni_normalized'] * 0.3 +
            (1 - feature_df['cloud_cover_normalized']) * 0.1
        )

        feature_df['system_consumption_factor'] = (
            feature_df['daily_consumption_normalized'] * feature_df['grid_dependency_normalized']
        )

        # Define feature columns
        self.feature_columns = [col for col in feature_df.columns if col.endswith('_normalized') or col in [
            'is_weekend', 'is_peak_consumption', 'is_peak_production',
            'consumption_pattern_encoded', 'grid_interaction_encoded'
        ]]

        logger.info(f"Created {len(self.feature_columns)} features")
        return feature_df

    def _normalize_feature(self, series: pd.Series, min_val: float, max_val: float) -> pd.Series:
        """Normalize feature to 0-1 range"""
        return (series.clip(min_val, max_val) - min_val) / (max_val - min_val)

class SystemAwareModelTrainer:
    """Train system-aware models"""

    def __init__(self, output_dir: str = "models/enhanced_v3"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.models = {}
        self.metrics = {}

    def train_system_specific_models(self, df: pd.DataFrame, feature_columns: List[str]) -> Dict[str, Any]:
        """Train separate models for each system"""
        logger.info("Training system-specific models...")

        results = {}

        for system_id in [1, 2]:
            logger.info(f"Training model for System {system_id}")

            # Filter data for this system
            system_data = df[df['system_id'] == system_id].copy()

            if len(system_data) < 100:
                logger.warning(f"Insufficient data for System {system_id}: {len(system_data)} records")
                continue

            # Prepare features and target
            X = system_data[feature_columns].fillna(0)
            y = system_data['ac_power']

            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, shuffle=True
            )

            # Train XGBoost model
            model = xgb.XGBRegressor(
                n_estimators=200,
                max_depth=8,
                learning_rate=0.1,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=42
            )

            model.fit(X_train, y_train)

            # Evaluate
            y_pred = model.predict(X_test)
            metrics = {
                'rmse': np.sqrt(mean_squared_error(y_test, y_pred)),
                'mae': mean_absolute_error(y_test, y_pred),
                'r2': r2_score(y_test, y_pred),
                'system_id': system_id,
                'train_samples': len(X_train),
                'test_samples': len(X_test)
            }

            logger.info(f"System {system_id} - RMSE: {metrics['rmse']:.2f}, MAE: {metrics['mae']:.2f}, R²: {metrics['r2']:.3f}")

            # Save model
            model_path = self.output_dir / f"enhanced_v3_system_{system_id}.joblib"
            joblib.dump(model, model_path)

            results[f'system_{system_id}'] = {
                'model': model,
                'metrics': metrics,
                'model_path': str(model_path)
            }

        return results

    def train_unified_system_aware_model(self, df: pd.DataFrame, feature_columns: List[str]) -> Dict[str, Any]:
        """Train unified model with system awareness"""
        logger.info("Training unified system-aware model...")

        # Prepare features and target
        X = df[feature_columns].fillna(0)
        y = df['ac_power']

        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, shuffle=True
        )

        # Train XGBoost model
        model = xgb.XGBRegressor(
            n_estimators=300,
            max_depth=10,
            learning_rate=0.1,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=42
        )

        model.fit(X_train, y_train)

        # Evaluate
        y_pred = model.predict(X_test)
        metrics = {
            'rmse': np.sqrt(mean_squared_error(y_test, y_pred)),
            'mae': mean_absolute_error(y_test, y_pred),
            'r2': r2_score(y_test, y_pred),
            'train_samples': len(X_train),
            'test_samples': len(X_test)
        }

        # Evaluate by system
        for system_id in [1, 2]:
            system_mask = X_test['system_id_normalized'] == (system_id - 1) / 1  # Normalized system_id
            if system_mask.sum() > 0:
                y_test_system = y_test[system_mask]
                y_pred_system = y_pred[system_mask]

                system_metrics = {
                    'rmse': np.sqrt(mean_squared_error(y_test_system, y_pred_system)),
                    'mae': mean_absolute_error(y_test_system, y_pred_system),
                    'r2': r2_score(y_test_system, y_pred_system),
                    'samples': len(y_test_system)
                }

                metrics[f'system_{system_id}'] = system_metrics
                logger.info(f"System {system_id} - RMSE: {system_metrics['rmse']:.2f}, MAE: {system_metrics['mae']:.2f}, R²: {system_metrics['r2']:.3f}")

        logger.info(f"Overall - RMSE: {metrics['rmse']:.2f}, MAE: {metrics['mae']:.2f}, R²: {metrics['r2']:.3f}")

        # Save model
        model_path = self.output_dir / "enhanced_v3_unified.joblib"
        joblib.dump(model, model_path)

        return {
            'model': model,
            'metrics': metrics,
            'model_path': str(model_path)
        }

def main():
    """Main training function"""
    parser = argparse.ArgumentParser(description="Train Enhanced Model v3 - System-Aware")
    parser.add_argument("--output_dir", default="models/enhanced_v3", help="Output directory for models")
    parser.add_argument("--strategy", choices=["separate", "unified", "both"], default="both",
                       help="Training strategy: separate models, unified model, or both")

    args = parser.parse_args()

    logger.info("🚀 Starting Enhanced Model v3 Training - System-Aware")
    logger.info(f"Strategy: {args.strategy}")
    logger.info(f"Output directory: {args.output_dir}")

    try:
        # Initialize components
        db = DatabaseConnection()
        data_loader = SystemAwareDataLoader(db)
        feature_engineer = SystemAwareFeatureEngineer()
        trainer = SystemAwareModelTrainer(args.output_dir)

        # Load and prepare data
        logger.info("Loading system-aware data...")
        df = data_loader.load_system_aware_data()

        logger.info("Adding consumption pattern features...")
        df = data_loader.add_consumption_pattern_features(df)

        logger.info("Adding battery behavior features...")
        df = data_loader.add_battery_behavior_features(df)

        logger.info("Adding seasonal features...")
        df = data_loader.add_seasonal_features(df)

        logger.info("Creating features...")
        df = feature_engineer.create_features(df)

        # Train models based on strategy
        results = {}

        if args.strategy in ["separate", "both"]:
            logger.info("Training separate system-specific models...")
            results['separate'] = trainer.train_system_specific_models(df, feature_engineer.feature_columns)

        if args.strategy in ["unified", "both"]:
            logger.info("Training unified system-aware model...")
            results['unified'] = trainer.train_unified_system_aware_model(df, feature_engineer.feature_columns)

        # Save results
        results_path = Path(args.output_dir) / "training_results.json"
        with open(results_path, 'w') as f:
            # Convert non-serializable objects to strings
            serializable_results = {}
            for strategy, strategy_results in results.items():
                if strategy == 'separate':
                    serializable_results[strategy] = {}
                    for system, system_results in strategy_results.items():
                        serializable_results[strategy][system] = {
                            'metrics': system_results['metrics'],
                            'model_path': system_results['model_path']
                        }
                else:
                    serializable_results[strategy] = {
                        'metrics': strategy_results['metrics'],
                        'model_path': strategy_results['model_path']
                    }

            json.dump(serializable_results, f, indent=2, default=str)

        logger.info(f"✅ Training completed! Results saved to {results_path}")
        logger.info("🎯 Enhanced Model v3 is ready with system-aware capabilities!")

    except Exception as e:
        logger.error(f"❌ Training failed: {e}")
        raise

if __name__ == "__main__":
    main()
