#!/usr/bin/env python3
"""
Complete Remaining Models Training
=================================

Training για τα υπόλοιπα enhanced models που δεν έχουν εκπαιδευτεί ακόμα:
- System 2 models (seasonal + multi-horizon)
- Winter models (both systems)
- Additional enhanced variants

Βασισμένο στα επιτυχημένα αποτελέσματα:
- 9 models trained με 100% success rate
- 97.17% average R² accuracy
- Proven methodology και parameters

Δημιουργήθηκε: 2025-06-06
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))

import pandas as pd
import numpy as np
import psycopg2
from pathlib import Path
from datetime import datetime
import joblib
import json
from typing import Dict, List, Any
import logging
import warnings
warnings.filterwarnings('ignore')

# Sklearn imports
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.preprocessing import StandardScaler

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RemainingModelsTrainer:
    """
    Trainer για τα υπόλοιπα enhanced models
    """
    
    def __init__(self):
        self.training_start = datetime.now()
        
        # Paths
        self.remaining_models_dir = Path("models/remaining_enhanced")
        self.remaining_models_dir.mkdir(exist_ok=True, parents=True)
        
        # Use proven parameters από successful models
        self.proven_params = {
            'n_estimators': 200,
            'max_depth': 20,
            'min_samples_split': 2,
            'min_samples_leaf': 1,
            'max_features': 'sqrt',
            'random_state': 42
        }
        
        # Remaining models to train
        self.remaining_models = {
            # System 2 seasonal models
            'spring_system2': {'system_id': 2, 'type': 'seasonal', 'season': 'spring', 'months': [3, 4, 5]},
            'summer_system2': {'system_id': 2, 'type': 'seasonal', 'season': 'summer', 'months': [6, 7, 8]},
            'autumn_system2': {'system_id': 2, 'type': 'seasonal', 'season': 'autumn', 'months': [9, 10, 11]},
            
            # System 2 multi-horizon models
            'hourly_system2': {'system_id': 2, 'type': 'multi_horizon', 'horizon': 'hourly'},
            'daily_system2': {'system_id': 2, 'type': 'multi_horizon', 'horizon': 'daily'},
            'monthly_system2': {'system_id': 2, 'type': 'multi_horizon', 'horizon': 'monthly'},
            'yearly_system2': {'system_id': 2, 'type': 'multi_horizon', 'horizon': 'yearly'},
            
            # Winter models (both systems) - if data available
            'winter_system1': {'system_id': 1, 'type': 'seasonal', 'season': 'winter', 'months': [12, 1, 2]},
            'winter_system2': {'system_id': 2, 'type': 'seasonal', 'season': 'winter', 'months': [12, 1, 2]}
        }
        
        logger.info("🔧 Initialized RemainingModelsTrainer")
        logger.info(f"📊 Target: {len(self.remaining_models)} remaining enhanced models")
    
    def load_comprehensive_data(self) -> pd.DataFrame:
        """Load comprehensive data για remaining models"""
        logger.info("📊 Loading comprehensive training data...")
        
        try:
            conn = psycopg2.connect(
                host='localhost',
                database='solar_prediction',
                user='postgres',
                password='postgres'
            )
            
            # Comprehensive query για both systems
            query = """
            WITH combined_data AS (
                -- System 1 data
                SELECT 
                    s.timestamp,
                    s.yield_today,
                    s.soc,
                    s.bat_power,
                    s.temperature,
                    w.global_horizontal_irradiance,
                    w.temperature_2m,
                    w.relative_humidity_2m,
                    w.cloud_cover,
                    1 as system_id,
                    EXTRACT(MONTH FROM s.timestamp) as month,
                    EXTRACT(HOUR FROM s.timestamp) as hour,
                    EXTRACT(DOY FROM s.timestamp) as day_of_year,
                    EXTRACT(DOW FROM s.timestamp) as day_of_week,
                    CASE 
                        WHEN EXTRACT(MONTH FROM s.timestamp) IN (3,4,5) THEN 'spring'
                        WHEN EXTRACT(MONTH FROM s.timestamp) IN (6,7,8) THEN 'summer'
                        WHEN EXTRACT(MONTH FROM s.timestamp) IN (9,10,11) THEN 'autumn'
                        WHEN EXTRACT(MONTH FROM s.timestamp) IN (12,1,2) THEN 'winter'
                    END as season
                FROM solax_data s
                LEFT JOIN weather_data w ON DATE_TRUNC('hour', s.timestamp) = DATE_TRUNC('hour', w.timestamp)
                WHERE s.timestamp >= '2024-03-01' 
                  AND s.yield_today IS NOT NULL
                  AND s.yield_today >= 0 
                  AND s.yield_today <= 100
                  AND w.global_horizontal_irradiance IS NOT NULL
                
                UNION ALL
                
                -- System 2 data
                SELECT 
                    s.timestamp,
                    s.yield_today,
                    s.soc,
                    s.bat_power,
                    s.temperature,
                    w.global_horizontal_irradiance,
                    w.temperature_2m,
                    w.relative_humidity_2m,
                    w.cloud_cover,
                    2 as system_id,
                    EXTRACT(MONTH FROM s.timestamp) as month,
                    EXTRACT(HOUR FROM s.timestamp) as hour,
                    EXTRACT(DOY FROM s.timestamp) as day_of_year,
                    EXTRACT(DOW FROM s.timestamp) as day_of_week,
                    CASE 
                        WHEN EXTRACT(MONTH FROM s.timestamp) IN (3,4,5) THEN 'spring'
                        WHEN EXTRACT(MONTH FROM s.timestamp) IN (6,7,8) THEN 'summer'
                        WHEN EXTRACT(MONTH FROM s.timestamp) IN (9,10,11) THEN 'autumn'
                        WHEN EXTRACT(MONTH FROM s.timestamp) IN (12,1,2) THEN 'winter'
                    END as season
                FROM solax_data2 s
                LEFT JOIN weather_data w ON DATE_TRUNC('hour', s.timestamp) = DATE_TRUNC('hour', w.timestamp)
                WHERE s.timestamp >= '2024-03-01' 
                  AND s.yield_today IS NOT NULL
                  AND s.yield_today >= 0 
                  AND s.yield_today <= 100
                  AND w.global_horizontal_irradiance IS NOT NULL
            )
            SELECT * FROM combined_data
            ORDER BY system_id, timestamp
            """
            
            df = pd.read_sql(query, conn)
            conn.close()
            
            # Data cleaning
            df = df.fillna(method='ffill').fillna(method='bfill').fillna(0)
            df = df.drop_duplicates(subset=['timestamp', 'system_id'])
            
            # Log data distribution
            logger.info(f"✅ Loaded {len(df):,} comprehensive records")
            
            for system_id in [1, 2]:
                system_data = df[df['system_id'] == system_id]
                logger.info(f"   System {system_id}: {len(system_data):,} records")
                
                for season in ['spring', 'summer', 'autumn', 'winter']:
                    season_data = system_data[system_data['season'] == season]
                    logger.info(f"     {season.title()}: {len(season_data):,} records")
            
            return df
            
        except Exception as e:
            logger.error(f"❌ Failed to load comprehensive data: {e}")
            raise
    
    def engineer_enhanced_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Enhanced feature engineering με proven features"""
        logger.info("🔧 Engineering enhanced features...")
        
        # Convert timestamp to datetime
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        # Sort by system and timestamp
        df = df.sort_values(['system_id', 'timestamp']).reset_index(drop=True)
        
        # Group by system για proper calculations
        enhanced_dfs = []
        
        for system_id in [1, 2]:
            system_df = df[df['system_id'] == system_id].copy()
            
            if len(system_df) == 0:
                continue
            
            # PROVEN FEATURES από successful models
            
            # Trigonometric features
            system_df['hour_sin'] = np.sin(2 * np.pi * system_df['hour'] / 24)
            system_df['hour_cos'] = np.cos(2 * np.pi * system_df['hour'] / 24)
            system_df['day_sin'] = np.sin(2 * np.pi * system_df['day_of_year'] / 365)
            system_df['day_cos'] = np.cos(2 * np.pi * system_df['day_of_year'] / 365)
            
            # PROVEN KEY INTERACTION FEATURES
            system_df['temp_ghi_interaction'] = system_df['temperature_2m'] * system_df['global_horizontal_irradiance'] / 1000
            system_df['cloud_temp_interaction'] = system_df['cloud_cover'] * system_df['temperature_2m'] / 100
            system_df['soc_power_interaction'] = system_df['soc'] * system_df['bat_power'] / 1000
            
            # PROVEN LAG FEATURES (key για performance)
            for lag in [1, 12, 24]:  # 5min, 1h, 2h lags
                system_df[f'yield_lag_{lag}'] = system_df['yield_today'].shift(lag)
                system_df[f'ghi_lag_{lag}'] = system_df['global_horizontal_irradiance'].shift(lag)
                system_df[f'temp_lag_{lag}'] = system_df['temperature_2m'].shift(lag)
            
            # PROVEN ROLLING FEATURES
            for window in [12, 24, 72]:  # 1h, 2h, 6h windows
                system_df[f'yield_rolling_mean_{window}'] = system_df['yield_today'].rolling(window).mean()
                system_df[f'yield_rolling_std_{window}'] = system_df['yield_today'].rolling(window).std()
                system_df[f'temp_rolling_mean_{window}'] = system_df['temperature_2m'].rolling(window).mean()
                system_df[f'ghi_rolling_mean_{window}'] = system_df['global_horizontal_irradiance'].rolling(window).mean()
            
            # Additional features
            system_df['weekend'] = (system_df['day_of_week'] >= 5).astype(int)
            system_df['month_progress'] = system_df['day_of_year'] % 30 / 30
            
            # Solar position features
            system_df['sun_elevation'] = np.sin(2 * np.pi * system_df['hour'] / 24) * np.sin(2 * np.pi * system_df['day_of_year'] / 365)
            system_df['sun_azimuth'] = np.cos(2 * np.pi * system_df['hour'] / 24)
            
            # Seasonal trends
            system_df['seasonal_trend'] = np.sin(2 * np.pi * system_df['day_of_year'] / 365)
            system_df['seasonal_trend_cos'] = np.cos(2 * np.pi * system_df['day_of_year'] / 365)
            
            enhanced_dfs.append(system_df)
        
        # Combine systems
        if enhanced_dfs:
            enhanced_df = pd.concat(enhanced_dfs, ignore_index=True)
            enhanced_df = enhanced_df.sort_values(['system_id', 'timestamp']).reset_index(drop=True)
        else:
            enhanced_df = df
        
        # Fill missing values
        enhanced_df = enhanced_df.fillna(method='ffill').fillna(method='bfill').fillna(0)
        
        logger.info(f"✅ Enhanced feature engineering complete: {enhanced_df.shape[1]} features")
        
        return enhanced_df
    
    def train_remaining_model(self, model_name: str, model_config: Dict, 
                             processed_data: pd.DataFrame) -> Dict[str, Any]:
        """Train single remaining model"""
        
        logger.info(f"\n🔧 Training remaining model: {model_name}")
        logger.info("=" * 80)
        
        # Filter data based on model configuration
        system_id = model_config['system_id']
        model_type = model_config['type']
        
        # Filter by system
        model_data = processed_data[processed_data['system_id'] == system_id].copy()
        
        # Additional filtering based on model type
        if model_type == 'seasonal':
            season = model_config['season']
            months = model_config.get('months', [])
            if months:
                model_data = model_data[model_data['month'].isin(months)]
            else:
                model_data = model_data[model_data['season'] == season]
        
        if len(model_data) < 100:  # Minimum για training
            logger.warning(f"⚠️ Insufficient data για {model_name}: {len(model_data)} records")
            return None
        
        # Enhanced feature selection
        base_features = [
            'hour_sin', 'hour_cos', 'day_sin', 'day_cos',
            'temperature_2m', 'cloud_cover', 'global_horizontal_irradiance', 
            'soc', 'bat_power', 'day_of_week', 'weekend'
        ]
        
        # Add proven advanced features
        advanced_features = [col for col in model_data.columns if any(x in col for x in 
            ['lag_', 'rolling_', 'interaction', 'sun_', 'seasonal_', 'month_progress'])]
        
        all_features = base_features + advanced_features
        available_features = [f for f in all_features if f in model_data.columns]
        
        logger.info(f"📊 Using {len(available_features)} features για {model_name}")
        logger.info(f"   System: {system_id}, Type: {model_type}")
        logger.info(f"   Training samples: {len(model_data):,}")
        
        # Prepare data
        X = model_data[available_features].values
        y = model_data['yield_today'].values
        
        # Time-based train/test split
        split_idx = int(0.8 * len(X))
        X_train, X_test = X[:split_idx], X[split_idx:]
        y_train, y_test = y[:split_idx], y[split_idx:]
        
        # Scale data
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        # Train με proven parameters
        logger.info("🚀 Training με proven parameters...")
        model = RandomForestRegressor(**self.proven_params)
        model.fit(X_train_scaled, y_train)
        
        # Make predictions
        y_pred = model.predict(X_test_scaled)
        
        # Calculate metrics
        metrics = {
            'r2': r2_score(y_test, y_pred),
            'mae': mean_absolute_error(y_test, y_pred),
            'rmse': np.sqrt(mean_squared_error(y_test, y_pred))
        }
        
        # Feature importance
        feature_importance = dict(zip(available_features, model.feature_importances_))
        top_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)[:10]
        
        result = {
            'model_name': model_name,
            'model_config': model_config,
            'model': model,
            'scaler': scaler,
            'features': available_features,
            'metrics': metrics,
            'feature_importance': feature_importance,
            'top_features': top_features,
            'training_samples': len(X_train),
            'test_samples': len(X_test),
            'system_id': system_id,
            'model_type': model_type
        }
        
        # Save model
        self.save_remaining_model(result)
        
        # Log results
        logger.info(f"📊 REMAINING MODEL RESULTS:")
        logger.info(f"   R²: {metrics['r2']:.4f}")
        logger.info(f"   MAE: {metrics['mae']:.3f}")
        logger.info(f"   RMSE: {metrics['rmse']:.3f}")
        
        if top_features:
            logger.info(f"   Top feature: {top_features[0][0]} ({top_features[0][1]:.4f})")
        
        return result
    
    def save_remaining_model(self, result: Dict[str, Any]):
        """Save trained remaining model"""
        model_name = result['model_name']
        model_dir = self.remaining_models_dir / model_name
        model_dir.mkdir(exist_ok=True)
        
        # Save model και scaler
        joblib.dump(result['model'], model_dir / "model.joblib")
        joblib.dump(result['scaler'], model_dir / "scaler.joblib")
        
        # Save enhanced metadata
        metadata = {
            'model_name': model_name,
            'model_type': f"remaining_{result['model_type']}",
            'model_config': result['model_config'],
            'system_id': result['system_id'],
            'features': result['features'],
            'performance': result['metrics'],
            'feature_importance': result['feature_importance'],
            'top_features': result['top_features'],
            'training_samples': result['training_samples'],
            'test_samples': result['test_samples'],
            'training_date': datetime.now().isoformat(),
            'proven_params_used': True,
            'enhanced_features': True,
            'pipeline_version': 'v1.0.0'
        }
        
        with open(model_dir / "metadata.json", 'w') as f:
            json.dump(metadata, f, indent=2, default=str)
        
        logger.info(f"💾 Saved remaining model: {model_dir}")
    
    def train_all_remaining_models(self) -> Dict[str, Any]:
        """Train όλα τα remaining models"""
        logger.info("🔧 STARTING REMAINING MODELS TRAINING")
        logger.info("=" * 100)
        logger.info(f"Target: {len(self.remaining_models)} remaining enhanced models")
        logger.info("Based on proven methodology (97.17% avg R², 60-84% MAE improvement)")
        logger.info("=" * 100)
        
        # Load and prepare data
        raw_data = self.load_comprehensive_data()
        processed_data = self.engineer_enhanced_features(raw_data)
        
        # Training results
        results = {
            'training_start': self.training_start.isoformat(),
            'total_models': len(self.remaining_models),
            'successful_models': 0,
            'failed_models': 0,
            'models': {},
            'type_summary': {},
            'system_summary': {}
        }
        
        # Train each remaining model
        for model_name, model_config in self.remaining_models.items():
            try:
                result = self.train_remaining_model(model_name, model_config, processed_data)
                
                if result:
                    results['models'][model_name] = result
                    results['successful_models'] += 1
                    
                    # Update summaries
                    model_type = result['model_type']
                    system_id = result['system_id']
                    
                    if model_type not in results['type_summary']:
                        results['type_summary'][model_type] = {'models': 0, 'avg_r2': 0, 'avg_mae': 0}
                    
                    if system_id not in results['system_summary']:
                        results['system_summary'][system_id] = {'models': 0, 'avg_r2': 0, 'avg_mae': 0}
                    
                    # Update type summary
                    type_sum = results['type_summary'][model_type]
                    type_sum['models'] += 1
                    type_sum['avg_r2'] += result['metrics']['r2']
                    type_sum['avg_mae'] += result['metrics']['mae']
                    
                    # Update system summary
                    system_sum = results['system_summary'][system_id]
                    system_sum['models'] += 1
                    system_sum['avg_r2'] += result['metrics']['r2']
                    system_sum['avg_mae'] += result['metrics']['mae']
                
                else:
                    results['failed_models'] += 1
                
            except Exception as e:
                logger.error(f"❌ Failed to train {model_name}: {e}")
                results['failed_models'] += 1
                continue
        
        # Calculate averages
        for type_data in results['type_summary'].values():
            if type_data['models'] > 0:
                type_data['avg_r2'] /= type_data['models']
                type_data['avg_mae'] /= type_data['models']
        
        for system_data in results['system_summary'].values():
            if system_data['models'] > 0:
                system_data['avg_r2'] /= system_data['models']
                system_data['avg_mae'] /= system_data['models']
        
        # Generate summary
        results['training_end'] = datetime.now().isoformat()
        self.generate_remaining_summary(results)
        
        return results
    
    def generate_remaining_summary(self, results: Dict[str, Any]):
        """Generate comprehensive remaining models summary"""
        logger.info(f"\n🔧 REMAINING MODELS TRAINING COMPLETED!")
        logger.info("=" * 100)
        
        successful = results['successful_models']
        total = results['total_models']
        failed = results['failed_models']
        
        logger.info(f"📊 OVERALL RESULTS:")
        logger.info(f"   Successful models: {successful}/{total} ({successful/total*100:.1f}%)")
        logger.info(f"   Failed models: {failed}")
        
        # Type analysis
        logger.info(f"\n🔧 PERFORMANCE BY TYPE:")
        for model_type, type_data in results['type_summary'].items():
            logger.info(f"   {model_type.title()}:")
            logger.info(f"     Models: {type_data['models']}")
            logger.info(f"     Avg R²: {type_data['avg_r2']:.4f}")
            logger.info(f"     Avg MAE: {type_data['avg_mae']:.3f}")
        
        # System analysis
        logger.info(f"\n🏠 PERFORMANCE BY SYSTEM:")
        for system_id, system_data in results['system_summary'].items():
            logger.info(f"   System {system_id}:")
            logger.info(f"     Models: {system_data['models']}")
            logger.info(f"     Avg R²: {system_data['avg_r2']:.4f}")
            logger.info(f"     Avg MAE: {system_data['avg_mae']:.3f}")
        
        # Save comprehensive summary
        summary_path = self.remaining_models_dir / "remaining_models_training_summary.json"
        with open(summary_path, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        logger.info(f"\n💾 Remaining models training summary saved: {summary_path}")

def main():
    """Main remaining models training function"""
    try:
        trainer = RemainingModelsTrainer()
        results = trainer.train_all_remaining_models()
        
        successful = results['successful_models']
        total = results['total_models']
        
        print(f"\n🔧 REMAINING MODELS TRAINING RESULTS:")
        print(f"=" * 80)
        print(f"📊 Successful: {successful}/{total} models ({successful/total*100:.1f}%)")
        
        if successful > 0:
            # Calculate average performance
            total_r2 = sum(result['metrics']['r2'] for result in results['models'].values())
            total_mae = sum(result['metrics']['mae'] for result in results['models'].values())
            avg_r2 = total_r2 / successful
            avg_mae = total_mae / successful
            
            print(f"\n📈 AVERAGE PERFORMANCE:")
            print(f"   Average R²: {avg_r2:.4f}")
            print(f"   Average MAE: {avg_mae:.3f}")
            
            # Compare με existing ecosystem
            existing_avg_r2 = 0.9717  # From ecosystem
            existing_avg_mae = 1.326  # From ecosystem
            
            print(f"\n🔄 ECOSYSTEM EXPANSION:")
            print(f"   Existing models: 9 (avg R²={existing_avg_r2:.4f}, MAE={existing_avg_mae:.3f})")
            print(f"   New models: {successful} (avg R²={avg_r2:.4f}, MAE={avg_mae:.3f})")
            print(f"   Total ecosystem: {9 + successful} models")
            
            print(f"\n🎯 TYPE BREAKDOWN:")
            for model_type, type_data in results['type_summary'].items():
                print(f"   {model_type.title()}: {type_data['models']} models")
        
        if successful >= total * 0.75:
            print(f"\n✅ REMAINING MODELS TRAINING SUCCESS!")
            print(f"🌐 Ready για complete ecosystem expansion")
            return True
        else:
            print(f"\n⚠️ PARTIAL SUCCESS")
            return False
        
    except Exception as e:
        print(f"❌ Remaining models training failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
