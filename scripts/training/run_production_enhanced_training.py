#!/usr/bin/env python3
"""
Run Production Enhanced Training
===============================

Batch execution script για production enhanced training με:
- Memory management
- Progress tracking
- Error recovery
- Incremental training

Δημιουργήθηκε: 2025-06-05
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))

import time
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ProductionTrainingRunner:
    """
    Runner για production enhanced training με batch processing
    """
    
    def __init__(self):
        self.start_time = datetime.now()
        self.models_trained = 0
        self.models_failed = 0
        self.batch_size = 4  # Train 4 models at a time
        
        # Model groups για batch processing
        self.model_groups = self.setup_model_groups()
        
        logger.info("🏗️ Initialized ProductionTrainingRunner")
        logger.info(f"📊 Total model groups: {len(self.model_groups)}")
    
    def setup_model_groups(self) -> List[List[str]]:
        """Setup model groups για batch processing"""
        
        # All 16 models
        all_models = []
        
        # Seasonal models (8 models)
        seasons = ['spring', 'summer', 'autumn', 'winter']
        for season in seasons:
            for system_id in [1, 2]:
                all_models.append(f"{season}_system{system_id}")
        
        # Multi-horizon models (8 models)
        horizons = ['hourly', 'daily', 'monthly', 'yearly']
        for horizon in horizons:
            for system_id in [1, 2]:
                all_models.append(f"multi_horizon_{horizon}_system{system_id}")
        
        # Group into batches
        groups = []
        for i in range(0, len(all_models), self.batch_size):
            groups.append(all_models[i:i + self.batch_size])
        
        return groups
    
    def check_system_resources(self) -> Dict[str, Any]:
        """Check system resources"""
        try:
            import psutil
            
            # Memory info
            memory = psutil.virtual_memory()
            
            # CPU info
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Disk info
            disk = psutil.disk_usage('/')
            
            resources = {
                'memory_total_gb': memory.total / (1024**3),
                'memory_available_gb': memory.available / (1024**3),
                'memory_percent': memory.percent,
                'cpu_percent': cpu_percent,
                'disk_free_gb': disk.free / (1024**3),
                'disk_percent': (disk.used / disk.total) * 100
            }
            
            logger.info(f"💻 System Resources:")
            logger.info(f"   Memory: {resources['memory_available_gb']:.1f}GB available ({resources['memory_percent']:.1f}% used)")
            logger.info(f"   CPU: {resources['cpu_percent']:.1f}% usage")
            logger.info(f"   Disk: {resources['disk_free_gb']:.1f}GB free")
            
            return resources
            
        except ImportError:
            logger.warning("⚠️ psutil not available, skipping resource check")
            return {}
    
    def run_training_batch(self, model_names: List[str]) -> Dict[str, Any]:
        """Run training για a batch of models"""
        
        logger.info(f"\n🚀 Starting training batch: {model_names}")
        logger.info("=" * 80)
        
        batch_start = time.time()
        batch_results = {
            'models': model_names,
            'successful': [],
            'failed': [],
            'start_time': datetime.now().isoformat(),
            'duration': 0
        }
        
        try:
            # Import trainer (fresh import για memory management)
            from scripts.training.production_enhanced_models_trainer import ProductionEnhancedModelsTrainer
            
            # Initialize trainer
            trainer = ProductionEnhancedModelsTrainer(
                output_dir="models/production_enhanced",
                pipeline_version="v1.0.0"
            )
            
            # Load data once για the batch
            logger.info("📊 Loading production data...")
            raw_data = trainer.load_production_data()
            processed_data = trainer.engineer_production_features(raw_data)
            
            # Train each model in the batch
            for model_name in model_names:
                try:
                    logger.info(f"\n🎯 Training: {model_name}")
                    
                    model_config = trainer.models_config[model_name]
                    result = trainer.train_production_model(model_name, model_config, processed_data)
                    
                    if result and result['target_achieved']:
                        batch_results['successful'].append(model_name)
                        self.models_trained += 1
                        logger.info(f"✅ {model_name} completed successfully")
                    else:
                        batch_results['failed'].append(model_name)
                        self.models_failed += 1
                        logger.warning(f"⚠️ {model_name} completed but targets not met")
                    
                except Exception as e:
                    logger.error(f"❌ Failed to train {model_name}: {e}")
                    batch_results['failed'].append(model_name)
                    self.models_failed += 1
                    continue
            
            # Clean up
            del trainer, raw_data, processed_data
            
        except Exception as e:
            logger.error(f"❌ Batch training failed: {e}")
            batch_results['failed'].extend([m for m in model_names if m not in batch_results['successful']])
            self.models_failed += len(batch_results['failed'])
        
        batch_results['duration'] = time.time() - batch_start
        batch_results['end_time'] = datetime.now().isoformat()
        
        logger.info(f"\n📊 Batch Results:")
        logger.info(f"   Successful: {len(batch_results['successful'])}/{len(model_names)}")
        logger.info(f"   Duration: {batch_results['duration']:.1f}s")
        
        return batch_results
    
    def run_full_production_training(self) -> Dict[str, Any]:
        """Run complete production training με batch processing"""
        
        logger.info("🚀 STARTING FULL PRODUCTION ENHANCED TRAINING")
        logger.info("=" * 100)
        logger.info(f"Strategy: Batch processing με {self.batch_size} models per batch")
        logger.info(f"Total batches: {len(self.model_groups)}")
        logger.info(f"Total models: {sum(len(group) for group in self.model_groups)}")
        logger.info("=" * 100)
        
        # Check system resources
        self.check_system_resources()
        
        # Training results
        full_results = {
            'start_time': self.start_time.isoformat(),
            'batches': [],
            'total_models': sum(len(group) for group in self.model_groups),
            'successful_models': 0,
            'failed_models': 0,
            'total_duration': 0
        }
        
        # Process each batch
        for batch_idx, model_group in enumerate(self.model_groups):
            logger.info(f"\n🔄 Processing batch {batch_idx + 1}/{len(self.model_groups)}")
            
            # Run batch training
            batch_result = self.run_training_batch(model_group)
            full_results['batches'].append(batch_result)
            
            # Update totals
            full_results['successful_models'] += len(batch_result['successful'])
            full_results['failed_models'] += len(batch_result['failed'])
            
            # Progress update
            progress = ((batch_idx + 1) / len(self.model_groups)) * 100
            logger.info(f"📈 Overall Progress: {progress:.1f}% ({self.models_trained + self.models_failed}/{full_results['total_models']} models)")
            
            # Memory cleanup between batches
            import gc
            gc.collect()
            
            # Brief pause between batches
            if batch_idx < len(self.model_groups) - 1:
                logger.info("⏸️ Brief pause between batches...")
                time.sleep(5)
        
        # Final results
        full_results['end_time'] = datetime.now().isoformat()
        full_results['total_duration'] = (datetime.now() - self.start_time).total_seconds()
        
        # Generate final summary
        self.generate_final_summary(full_results)
        
        return full_results
    
    def generate_final_summary(self, results: Dict[str, Any]):
        """Generate final training summary"""
        
        logger.info(f"\n🎉 FULL PRODUCTION TRAINING COMPLETED!")
        logger.info("=" * 100)
        
        total = results['total_models']
        successful = results['successful_models']
        failed = results['failed_models']
        duration = results['total_duration']
        
        logger.info(f"📊 FINAL RESULTS:")
        logger.info(f"   Total models: {total}")
        logger.info(f"   Successful: {successful} ({successful/total*100:.1f}%)")
        logger.info(f"   Failed: {failed} ({failed/total*100:.1f}%)")
        logger.info(f"   Total duration: {duration/60:.1f} minutes")
        logger.info(f"   Average per model: {duration/total:.1f} seconds")
        
        # Batch analysis
        logger.info(f"\n📊 BATCH ANALYSIS:")
        for i, batch in enumerate(results['batches']):
            batch_success_rate = len(batch['successful']) / len(batch['models']) * 100
            logger.info(f"   Batch {i+1}: {len(batch['successful'])}/{len(batch['models'])} ({batch_success_rate:.1f}%) - {batch['duration']:.1f}s")
        
        # Success/failure breakdown
        successful_models = []
        failed_models = []
        
        for batch in results['batches']:
            successful_models.extend(batch['successful'])
            failed_models.extend(batch['failed'])
        
        if successful_models:
            logger.info(f"\n✅ SUCCESSFUL MODELS ({len(successful_models)}):")
            for model in successful_models:
                logger.info(f"   ✅ {model}")
        
        if failed_models:
            logger.info(f"\n❌ FAILED MODELS ({len(failed_models)}):")
            for model in failed_models:
                logger.info(f"   ❌ {model}")
        
        # Save results
        results_path = Path("models/production_enhanced/full_training_results.json")
        results_path.parent.mkdir(exist_ok=True, parents=True)
        
        import json
        with open(results_path, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        logger.info(f"\n💾 Full training results saved: {results_path}")
        
        # Performance estimation
        if successful >= total * 0.8:  # 80% success rate
            logger.info(f"\n🎉 TRAINING SUCCESS! High success rate achieved.")
            logger.info(f"📈 Expected improvements based on pilot results:")
            logger.info(f"   R² improvement: +4-10%")
            logger.info(f"   MAE improvement: +15-75%")
        else:
            logger.info(f"\n⚠️ PARTIAL SUCCESS. Some models may need attention.")
    
    def check_existing_models(self) -> List[str]:
        """Check which models already exist"""
        
        existing_models = []
        models_dir = Path("models/production_enhanced")
        
        if models_dir.exists():
            for model_dir in models_dir.iterdir():
                if model_dir.is_dir() and (model_dir / "metadata.json").exists():
                    existing_models.append(model_dir.name)
        
        if existing_models:
            logger.info(f"📁 Found {len(existing_models)} existing enhanced models")
            logger.info(f"   Existing: {existing_models}")
        
        return existing_models
    
    def run_incremental_training(self, skip_existing: bool = True) -> Dict[str, Any]:
        """Run incremental training (skip existing models)"""
        
        if skip_existing:
            existing_models = self.check_existing_models()
            
            # Filter out existing models από groups
            filtered_groups = []
            for group in self.model_groups:
                filtered_group = [model for model in group if model not in existing_models]
                if filtered_group:
                    filtered_groups.append(filtered_group)
            
            self.model_groups = filtered_groups
            
            remaining_models = sum(len(group) for group in self.model_groups)
            logger.info(f"🔄 Incremental training: {remaining_models} models remaining")
            
            if remaining_models == 0:
                logger.info("✅ All models already exist! No training needed.")
                return {'status': 'all_complete', 'models_trained': 0}
        
        return self.run_full_production_training()

def main():
    """Main function"""
    try:
        runner = ProductionTrainingRunner()
        
        # Check για existing models
        existing = runner.check_existing_models()
        
        if existing:
            print(f"\n📁 Found {len(existing)} existing enhanced models.")
            print("Options:")
            print("1. Skip existing models (incremental training)")
            print("2. Retrain all models")
            
            choice = input("\nChoose option (1 or 2): ").strip()
            
            if choice == "1":
                results = runner.run_incremental_training(skip_existing=True)
            else:
                results = runner.run_full_production_training()
        else:
            results = runner.run_full_production_training()
        
        if results.get('status') == 'all_complete':
            print("\n✅ All models already trained!")
            return True
        
        # Final status
        total = results['total_models']
        successful = results['successful_models']
        
        if successful >= total * 0.8:
            print(f"\n🎉 PRODUCTION TRAINING SUCCESS!")
            print(f"   {successful}/{total} models trained successfully")
            return True
        else:
            print(f"\n⚠️ PARTIAL SUCCESS")
            print(f"   {successful}/{total} models trained")
            return False
        
    except Exception as e:
        logger.error(f"❌ Production training runner failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
