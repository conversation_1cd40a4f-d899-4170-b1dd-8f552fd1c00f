#!/usr/bin/env python3
"""
Ensemble & Deep Learning Models
===============================

Advanced ensemble system combining traditional ML with deep learning:
- Level 1: XGBoost, LightGBM, Random Forest (GPU-accelerated)
- Level 2: LSTM for time series patterns, CNN for spatial-temporal features
- Level 3: Meta-learner with stacking/blending optimization
- Target: >98% accuracy through sophisticated model combination

Hardware: RTX 4070 Ti + 32GB RAM optimized
Created: June 6, 2025
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import numpy as np
import pandas as pd
import logging
from datetime import datetime
from typing import Dict, List, Tuple, Any, Optional, Union
import joblib
import json
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# Traditional ML libraries
import xgboost as xgb
import lightgbm as lgb
from sklearn.ensemble import RandomForestRegressor, StackingRegressor, VotingRegressor
from sklearn.linear_model import LinearRegression, Ridge, ElasticNet
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import train_test_split, TimeSeriesSplit, cross_val_score
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

# Deep learning libraries
try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    from torch.utils.data import DataLoader, TensorDataset
    TORCH_AVAILABLE = True
    print("✅ PyTorch available for deep learning")
except ImportError:
    TORCH_AVAILABLE = False
    print("⚠️ PyTorch not available - deep learning disabled")

# GPU libraries
try:
    import cupy as cp
    CUPY_AVAILABLE = True
except ImportError:
    CUPY_AVAILABLE = False

# Import our components
from scripts.training.gpu_training_infrastructure import GPUTrainingInfrastructure

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LSTMTimeSeriesModel(nn.Module):
    """LSTM model for time series prediction"""
    
    def __init__(self, input_size: int, hidden_size: int = 128, num_layers: int = 2, 
                 dropout: float = 0.2, output_size: int = 1):
        super(LSTMTimeSeriesModel, self).__init__()
        
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        self.lstm = nn.LSTM(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            dropout=dropout if num_layers > 1 else 0,
            batch_first=True,
            bidirectional=True
        )
        
        # Bidirectional LSTM doubles the hidden size
        self.fc = nn.Sequential(
            nn.Linear(hidden_size * 2, hidden_size),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_size // 2, output_size)
        )
        
    def forward(self, x):
        # x shape: (batch_size, sequence_length, input_size)
        lstm_out, _ = self.lstm(x)
        
        # Take the last output
        last_output = lstm_out[:, -1, :]
        
        # Pass through fully connected layers
        output = self.fc(last_output)
        
        return output

class CNN1DModel(nn.Module):
    """1D CNN model for pattern recognition in time series"""
    
    def __init__(self, input_size: int, sequence_length: int = 24, 
                 filters: List[int] = [64, 128, 256], kernel_sizes: List[int] = [3, 5, 7],
                 dropout: float = 0.3, output_size: int = 1):
        super(CNN1DModel, self).__init__()
        
        self.input_size = input_size
        self.sequence_length = sequence_length
        
        # Convolutional layers
        conv_layers = []
        in_channels = input_size
        
        for i, (out_channels, kernel_size) in enumerate(zip(filters, kernel_sizes)):
            conv_layers.extend([
                nn.Conv1d(in_channels, out_channels, kernel_size, padding=kernel_size//2),
                nn.BatchNorm1d(out_channels),
                nn.ReLU(),
                nn.Dropout(dropout),
                nn.MaxPool1d(2) if i < len(filters) - 1 else nn.Identity()
            ])
            in_channels = out_channels
        
        self.conv_layers = nn.Sequential(*conv_layers)
        
        # Calculate the size after convolutions
        with torch.no_grad():
            dummy_input = torch.randn(1, input_size, sequence_length)
            conv_output = self.conv_layers(dummy_input)
            flattened_size = conv_output.numel()
        
        # Fully connected layers
        self.fc = nn.Sequential(
            nn.Flatten(),
            nn.Linear(flattened_size, 512),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(512, 128),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(128, output_size)
        )
        
    def forward(self, x):
        # x shape: (batch_size, sequence_length, input_size)
        # Transpose for Conv1d: (batch_size, input_size, sequence_length)
        x = x.transpose(1, 2)
        
        # Pass through convolutional layers
        x = self.conv_layers(x)
        
        # Pass through fully connected layers
        output = self.fc(x)
        
        return output

class EnsembleDeepLearningSystem:
    """
    Advanced ensemble system combining traditional ML with deep learning
    """
    
    def __init__(self, sequence_length: int = 24, output_dir: str = "models/ensemble"):
        """
        Initialize ensemble system
        
        Args:
            sequence_length: Length of time series sequences for deep learning
            output_dir: Directory to save models
        """
        self.sequence_length = sequence_length
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize GPU infrastructure
        self.gpu_trainer = GPUTrainingInfrastructure()
        
        # Model containers
        self.base_models = {}
        self.deep_models = {}
        self.meta_learner = None
        self.scalers = {}
        
        # Training history
        self.training_history = {}
        self.model_performance = {}
        
        # Configuration
        self.device = torch.device('cuda' if torch.cuda.is_available() and TORCH_AVAILABLE else 'cpu')
        
        logger.info("🎭 Ensemble & Deep Learning System initialized")
        logger.info(f"   Sequence length: {sequence_length}")
        logger.info(f"   Device: {self.device}")
        logger.info(f"   Output directory: {output_dir}")
    
    def create_base_models(self) -> Dict[str, Any]:
        """Create traditional ML base models"""
        logger.info("🌳 Creating base models (XGBoost, LightGBM, Random Forest)...")
        
        base_models = {
            'xgboost': self.gpu_trainer.create_gpu_xgboost_model(
                n_estimators=1000,
                max_depth=8,
                learning_rate=0.1,
                subsample=0.8,
                colsample_bytree=0.8,
                reg_alpha=0.1,
                reg_lambda=1.0,
                random_state=42
            ),
            
            'lightgbm': self.gpu_trainer.create_gpu_lightgbm_model(
                n_estimators=1000,
                max_depth=8,
                learning_rate=0.1,
                subsample=0.8,
                colsample_bytree=0.8,
                reg_alpha=0.1,
                reg_lambda=1.0,
                random_state=42
            ),
            
            'random_forest': self.gpu_trainer.create_gpu_random_forest_model(
                n_estimators=200,
                max_depth=15,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42
            )
        }
        
        self.base_models = base_models
        
        logger.info(f"✅ Created {len(base_models)} base models")
        return base_models
    
    def create_deep_models(self, input_size: int) -> Dict[str, Any]:
        """Create deep learning models"""
        if not TORCH_AVAILABLE:
            logger.warning("⚠️ PyTorch not available - skipping deep learning models")
            return {}
        
        logger.info("🧠 Creating deep learning models (LSTM, CNN)...")
        
        deep_models = {
            'lstm': LSTMTimeSeriesModel(
                input_size=input_size,
                hidden_size=128,
                num_layers=2,
                dropout=0.2
            ).to(self.device),
            
            'cnn_1d': CNN1DModel(
                input_size=input_size,
                sequence_length=self.sequence_length,
                filters=[64, 128, 256],
                kernel_sizes=[3, 5, 7],
                dropout=0.3
            ).to(self.device)
        }
        
        self.deep_models = deep_models
        
        logger.info(f"✅ Created {len(deep_models)} deep learning models")
        return deep_models
    
    def prepare_sequences(self, X: np.ndarray, y: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare sequences for deep learning models"""
        if len(X) < self.sequence_length:
            raise ValueError(f"Data length {len(X)} is less than sequence length {self.sequence_length}")
        
        X_sequences = []
        y_sequences = []
        
        for i in range(self.sequence_length, len(X)):
            X_sequences.append(X[i-self.sequence_length:i])
            y_sequences.append(y[i])
        
        return np.array(X_sequences), np.array(y_sequences)
    
    def train_base_models(self, X_train: np.ndarray, y_train: np.ndarray, 
                         X_val: np.ndarray, y_val: np.ndarray) -> Dict[str, Dict[str, float]]:
        """Train traditional ML base models"""
        logger.info("🎓 Training base models...")
        
        base_performance = {}
        
        for name, model in self.base_models.items():
            logger.info(f"   Training {name}...")
            
            # Train model with GPU acceleration
            trained_model, metrics = self.gpu_trainer.train_model_gpu(
                model, X_train, y_train, X_val, y_val
            )
            
            # Update model
            self.base_models[name] = trained_model
            base_performance[name] = metrics
            
            logger.info(f"     ✅ {name}: R²={metrics['r2']:.4f}, RMSE={metrics['rmse']:.2f}")
        
        self.model_performance.update(base_performance)
        
        logger.info(f"✅ Base models training completed")
        return base_performance
    
    def train_deep_models(self, X_train: np.ndarray, y_train: np.ndarray,
                         X_val: np.ndarray, y_val: np.ndarray) -> Dict[str, Dict[str, float]]:
        """Train deep learning models"""
        if not TORCH_AVAILABLE or not self.deep_models:
            logger.warning("⚠️ Deep learning models not available - skipping")
            return {}
        
        logger.info("🧠 Training deep learning models...")
        
        # Prepare sequences
        X_train_seq, y_train_seq = self.prepare_sequences(X_train, y_train)
        X_val_seq, y_val_seq = self.prepare_sequences(X_val, y_val)
        
        # Scale data for deep learning
        scaler_X = StandardScaler()
        scaler_y = StandardScaler()
        
        X_train_scaled = scaler_X.fit_transform(X_train_seq.reshape(-1, X_train_seq.shape[-1]))
        X_train_scaled = X_train_scaled.reshape(X_train_seq.shape)
        
        X_val_scaled = scaler_X.transform(X_val_seq.reshape(-1, X_val_seq.shape[-1]))
        X_val_scaled = X_val_scaled.reshape(X_val_seq.shape)
        
        y_train_scaled = scaler_y.fit_transform(y_train_seq.reshape(-1, 1)).flatten()
        y_val_scaled = scaler_y.transform(y_val_seq.reshape(-1, 1)).flatten()
        
        # Store scalers
        self.scalers['deep_X'] = scaler_X
        self.scalers['deep_y'] = scaler_y
        
        # Convert to tensors
        X_train_tensor = torch.FloatTensor(X_train_scaled).to(self.device)
        y_train_tensor = torch.FloatTensor(y_train_scaled).to(self.device)
        X_val_tensor = torch.FloatTensor(X_val_scaled).to(self.device)
        y_val_tensor = torch.FloatTensor(y_val_scaled).to(self.device)
        
        deep_performance = {}
        
        for name, model in self.deep_models.items():
            logger.info(f"   Training {name}...")
            
            # Training configuration
            criterion = nn.MSELoss()
            optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-5)
            scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)
            
            # Training loop
            epochs = 100
            batch_size = 64
            best_val_loss = float('inf')
            patience_counter = 0
            patience = 15
            
            train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
            train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
            
            for epoch in range(epochs):
                # Training
                model.train()
                train_loss = 0.0
                
                for batch_X, batch_y in train_loader:
                    optimizer.zero_grad()
                    outputs = model(batch_X).squeeze()
                    loss = criterion(outputs, batch_y)
                    loss.backward()
                    optimizer.step()
                    train_loss += loss.item()
                
                # Validation
                model.eval()
                with torch.no_grad():
                    val_outputs = model(X_val_tensor).squeeze()
                    val_loss = criterion(val_outputs, y_val_tensor).item()
                
                scheduler.step(val_loss)
                
                # Early stopping
                if val_loss < best_val_loss:
                    best_val_loss = val_loss
                    patience_counter = 0
                    # Save best model state
                    torch.save(model.state_dict(), self.output_dir / f"{name}_best.pth")
                else:
                    patience_counter += 1
                
                if patience_counter >= patience:
                    logger.info(f"     Early stopping at epoch {epoch+1}")
                    break
                
                if (epoch + 1) % 20 == 0:
                    logger.info(f"     Epoch {epoch+1}: Train Loss={train_loss/len(train_loader):.6f}, Val Loss={val_loss:.6f}")
            
            # Load best model
            model.load_state_dict(torch.load(self.output_dir / f"{name}_best.pth"))
            
            # Calculate final metrics
            model.eval()
            with torch.no_grad():
                val_outputs = model(X_val_tensor).squeeze()
                val_outputs_np = val_outputs.cpu().numpy()
                
                # Inverse transform
                val_outputs_original = scaler_y.inverse_transform(val_outputs_np.reshape(-1, 1)).flatten()
                
                # Calculate metrics
                rmse = np.sqrt(mean_squared_error(y_val_seq, val_outputs_original))
                mae = mean_absolute_error(y_val_seq, val_outputs_original)
                r2 = r2_score(y_val_seq, val_outputs_original)
            
            metrics = {
                'rmse': rmse,
                'mae': mae,
                'r2': r2,
                'final_val_loss': best_val_loss
            }
            
            deep_performance[name] = metrics
            
            logger.info(f"     ✅ {name}: R²={r2:.4f}, RMSE={rmse:.2f}")
        
        self.model_performance.update(deep_performance)
        
        logger.info(f"✅ Deep learning models training completed")
        return deep_performance
    
    def create_meta_learner(self, stacking_method: str = 'linear') -> Any:
        """Create meta-learner for ensemble"""
        logger.info(f"🎯 Creating meta-learner ({stacking_method})...")
        
        if stacking_method == 'linear':
            meta_learner = LinearRegression()
        elif stacking_method == 'ridge':
            meta_learner = Ridge(alpha=1.0)
        elif stacking_method == 'elastic':
            meta_learner = ElasticNet(alpha=1.0, l1_ratio=0.5)
        else:
            meta_learner = LinearRegression()
        
        self.meta_learner = meta_learner
        
        logger.info(f"✅ Meta-learner created: {type(meta_learner).__name__}")
        return meta_learner
    
    def get_base_predictions(self, X: np.ndarray) -> np.ndarray:
        """Get predictions from all base models"""
        predictions = []
        
        # Traditional ML predictions
        for name, model in self.base_models.items():
            pred = self.gpu_trainer.predict_gpu(model, X)
            predictions.append(pred)
        
        # Deep learning predictions
        if TORCH_AVAILABLE and self.deep_models:
            # Prepare sequences
            if len(X) >= self.sequence_length:
                X_seq, _ = self.prepare_sequences(X, np.zeros(len(X)))  # Dummy y
                
                # Scale data
                X_scaled = self.scalers['deep_X'].transform(X_seq.reshape(-1, X_seq.shape[-1]))
                X_scaled = X_scaled.reshape(X_seq.shape)
                X_tensor = torch.FloatTensor(X_scaled).to(self.device)
                
                for name, model in self.deep_models.items():
                    model.eval()
                    with torch.no_grad():
                        pred_tensor = model(X_tensor).squeeze()
                        pred_np = pred_tensor.cpu().numpy()
                        
                        # Inverse transform
                        pred_original = self.scalers['deep_y'].inverse_transform(pred_np.reshape(-1, 1)).flatten()
                        
                        # Pad with zeros for sequence alignment
                        pred_padded = np.zeros(len(X))
                        pred_padded[self.sequence_length:] = pred_original
                        
                        predictions.append(pred_padded)
        
        return np.column_stack(predictions)

    def train_stacking_ensemble(self, X_train: np.ndarray, y_train: np.ndarray,
                               X_val: np.ndarray, y_val: np.ndarray) -> Dict[str, float]:
        """Train complete stacking ensemble"""
        logger.info("🏗️ Training stacking ensemble...")

        # Step 1: Train base models
        base_performance = self.train_base_models(X_train, y_train, X_val, y_val)

        # Step 2: Train deep learning models
        deep_performance = self.train_deep_models(X_train, y_train, X_val, y_val)

        # Step 3: Create meta-learner
        meta_learner = self.create_meta_learner('ridge')

        # Step 4: Get base predictions for meta-learning
        base_pred_train = self.get_base_predictions(X_train)
        base_pred_val = self.get_base_predictions(X_val)

        # Step 5: Train meta-learner
        meta_learner.fit(base_pred_train, y_train)

        # Step 6: Final ensemble predictions
        ensemble_pred_val = meta_learner.predict(base_pred_val)

        # Calculate ensemble performance
        ensemble_rmse = np.sqrt(mean_squared_error(y_val, ensemble_pred_val))
        ensemble_mae = mean_absolute_error(y_val, ensemble_pred_val)
        ensemble_r2 = r2_score(y_val, ensemble_pred_val)

        ensemble_performance = {
            'rmse': ensemble_rmse,
            'mae': ensemble_mae,
            'r2': ensemble_r2
        }

        # Store ensemble performance
        self.model_performance['ensemble'] = ensemble_performance

        logger.info(f"✅ Stacking ensemble trained: R²={ensemble_r2:.4f}")

        return ensemble_performance

    def predict_ensemble(self, X: np.ndarray) -> np.ndarray:
        """Make predictions using the trained ensemble"""
        if self.meta_learner is None:
            raise ValueError("Ensemble must be trained before making predictions")

        # Get base predictions
        base_predictions = self.get_base_predictions(X)

        # Meta-learner prediction
        ensemble_predictions = self.meta_learner.predict(base_predictions)

        return ensemble_predictions

    def save_ensemble(self, filepath: str = None) -> str:
        """Save the complete ensemble system"""
        if filepath is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filepath = self.output_dir / f"ensemble_system_{timestamp}.joblib"

        ensemble_data = {
            'base_models': self.base_models,
            'deep_models': {name: model.state_dict() for name, model in self.deep_models.items()},
            'meta_learner': self.meta_learner,
            'scalers': self.scalers,
            'model_performance': self.model_performance,
            'sequence_length': self.sequence_length,
            'device': str(self.device)
        }

        joblib.dump(ensemble_data, filepath)

        # Save metadata
        metadata_path = str(filepath).replace('.joblib', '_metadata.json')
        with open(metadata_path, 'w') as f:
            json.dump({
                'model_performance': self.model_performance,
                'sequence_length': self.sequence_length,
                'device': str(self.device),
                'timestamp': datetime.now().isoformat()
            }, f, indent=2, default=str)

        logger.info(f"💾 Ensemble system saved to: {filepath}")
        return str(filepath)

    def load_ensemble(self, filepath: str):
        """Load a saved ensemble system"""
        logger.info(f"📂 Loading ensemble system from: {filepath}")

        ensemble_data = joblib.load(filepath)

        self.base_models = ensemble_data['base_models']
        self.meta_learner = ensemble_data['meta_learner']
        self.scalers = ensemble_data['scalers']
        self.model_performance = ensemble_data['model_performance']
        self.sequence_length = ensemble_data['sequence_length']

        # Recreate deep models if available
        if TORCH_AVAILABLE and ensemble_data['deep_models']:
            # This would require recreating the model architecture
            # For now, we'll skip this in the simplified version
            pass

        logger.info("✅ Ensemble system loaded successfully")

    def get_ensemble_summary(self) -> Dict[str, Any]:
        """Get comprehensive ensemble summary"""
        return {
            'base_models': list(self.base_models.keys()),
            'deep_models': list(self.deep_models.keys()),
            'meta_learner': type(self.meta_learner).__name__ if self.meta_learner else None,
            'model_performance': self.model_performance,
            'sequence_length': self.sequence_length,
            'device': str(self.device),
            'total_models': len(self.base_models) + len(self.deep_models)
        }

def main():
    """Test ensemble and deep learning system"""
    logger.info("🎭 Testing Ensemble & Deep Learning System")
    logger.info("=" * 70)
    
    try:
        # Create synthetic time series data
        np.random.seed(42)
        n_samples = 2000
        n_features = 20
        
        # Generate time series with patterns
        t = np.linspace(0, 10, n_samples)
        
        # Create features with temporal patterns
        X = np.column_stack([
            np.sin(2 * np.pi * t / 24) + np.random.normal(0, 0.1, n_samples),  # Daily pattern
            np.cos(2 * np.pi * t / 24) + np.random.normal(0, 0.1, n_samples),
            np.sin(2 * np.pi * t / (24 * 7)) + np.random.normal(0, 0.1, n_samples),  # Weekly pattern
            np.random.normal(0, 1, (n_samples, n_features - 3))  # Random features
        ])
        
        # Create target with complex patterns
        y = (2 * X[:, 0] + 1.5 * X[:, 1] + 0.8 * X[:, 2] + 
             0.1 * np.sin(4 * np.pi * t / 24) +  # Higher frequency component
             np.random.normal(0, 0.2, n_samples))
        
        # Split data
        split_idx = int(0.8 * n_samples)
        X_train, X_test = X[:split_idx], X[split_idx:]
        y_train, y_test = y[:split_idx], y[split_idx:]
        
        # Further split training data for validation
        X_train, X_val, y_train, y_val = train_test_split(
            X_train, y_train, test_size=0.2, random_state=42, shuffle=False
        )
        
        logger.info(f"Data prepared: Train={X_train.shape}, Val={X_val.shape}, Test={X_test.shape}")
        
        # Initialize ensemble system
        ensemble_system = EnsembleDeepLearningSystem(sequence_length=24)
        
        # Create models
        base_models = ensemble_system.create_base_models()
        deep_models = ensemble_system.create_deep_models(input_size=n_features)
        
        # Train models
        base_performance = ensemble_system.train_base_models(X_train, y_train, X_val, y_val)
        deep_performance = ensemble_system.train_deep_models(X_train, y_train, X_val, y_val)
        
        # Create meta-learner
        meta_learner = ensemble_system.create_meta_learner('ridge')
        
        # Get base predictions for meta-learning
        base_pred_train = ensemble_system.get_base_predictions(X_train)
        base_pred_val = ensemble_system.get_base_predictions(X_val)
        
        # Train meta-learner
        meta_learner.fit(base_pred_train, y_train)
        
        # Final ensemble predictions
        ensemble_pred_val = meta_learner.predict(base_pred_val)
        
        # Calculate ensemble performance
        ensemble_rmse = np.sqrt(mean_squared_error(y_val, ensemble_pred_val))
        ensemble_mae = mean_absolute_error(y_val, ensemble_pred_val)
        ensemble_r2 = r2_score(y_val, ensemble_pred_val)
        
        # Display results
        logger.info("🎯 ENSEMBLE & DEEP LEARNING RESULTS")
        logger.info("=" * 70)
        
        logger.info("📊 Base Models Performance:")
        for name, metrics in base_performance.items():
            logger.info(f"   {name}: R²={metrics['r2']:.4f}, RMSE={metrics['rmse']:.3f}")
        
        if deep_performance:
            logger.info("\n🧠 Deep Learning Models Performance:")
            for name, metrics in deep_performance.items():
                logger.info(f"   {name}: R²={metrics['r2']:.4f}, RMSE={metrics['rmse']:.3f}")
        
        logger.info(f"\n🎭 Ensemble Performance:")
        logger.info(f"   R² Score: {ensemble_r2:.4f}")
        logger.info(f"   RMSE: {ensemble_rmse:.3f}")
        logger.info(f"   MAE: {ensemble_mae:.3f}")
        
        # Compare with best individual model
        best_individual_r2 = max([m['r2'] for m in {**base_performance, **deep_performance}.values()])
        improvement = (ensemble_r2 - best_individual_r2) / best_individual_r2 * 100
        
        logger.info(f"\n📈 Ensemble Improvement:")
        logger.info(f"   Best individual R²: {best_individual_r2:.4f}")
        logger.info(f"   Ensemble R²: {ensemble_r2:.4f}")
        logger.info(f"   Improvement: {improvement:+.2f}%")
        
        target_accuracy = 0.98
        if ensemble_r2 >= target_accuracy:
            logger.info(f"🎉 TARGET ACHIEVED: {ensemble_r2:.1%} ≥ {target_accuracy:.1%}")
        else:
            logger.info(f"⚠️ Target not reached: {ensemble_r2:.1%} < {target_accuracy:.1%}")
        
        logger.info("\n✅ Ensemble & Deep Learning System test completed!")
        
        return ensemble_system, {
            'base_performance': base_performance,
            'deep_performance': deep_performance,
            'ensemble_performance': {
                'r2': ensemble_r2,
                'rmse': ensemble_rmse,
                'mae': ensemble_mae
            }
        }
        
    except Exception as e:
        logger.error(f"❌ Ensemble system test failed: {e}")
        import traceback
        traceback.print_exc()
        return None, None

if __name__ == "__main__":
    ensemble_system, results = main()
