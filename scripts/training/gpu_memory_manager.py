#!/usr/bin/env python3
"""
GPU Memory Manager for RTX 4070 Ti
==================================

Intelligent memory management for 12GB VRAM optimization
- Dynamic batch size calculation
- Memory pool management
- OOM prevention
- Performance monitoring

Hardware: RTX 4070 Ti (12GB VRAM)
Created: June 6, 2025
"""

import gc
import time
import logging
import psutil
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass
from contextlib import contextmanager

# GPU libraries with fallbacks
try:
    import cupy as cp
    CUPY_AVAILABLE = True
except ImportError:
    CUPY_AVAILABLE = False

try:
    import torch
    TORCH_AVAILABLE = torch.cuda.is_available()
except ImportError:
    TORCH_AVAILABLE = False

try:
    import nvidia_ml_py3 as nvml
    nvml.nvmlInit()
    NVML_AVAILABLE = True
except ImportError:
    NVML_AVAILABLE = False

logger = logging.getLogger(__name__)

@dataclass
class MemoryStats:
    """Memory statistics container"""
    total_gb: float
    used_gb: float
    free_gb: float
    utilization_percent: float
    timestamp: float

@dataclass
class BatchConfig:
    """Batch configuration for optimal memory usage"""
    batch_size: int
    num_batches: int
    memory_per_batch_gb: float
    estimated_peak_memory_gb: float

class GPUMemoryManager:
    """
    Intelligent GPU memory manager for RTX 4070 Ti
    """
    
    def __init__(self, gpu_id: int = 0, safety_margin_gb: float = 2.0):
        """
        Initialize GPU memory manager
        
        Args:
            gpu_id: GPU device ID
            safety_margin_gb: Memory to keep free (GB)
        """
        self.gpu_id = gpu_id
        self.safety_margin_gb = safety_margin_gb
        self.total_memory_gb = 12.0  # RTX 4070 Ti
        self.usable_memory_gb = self.total_memory_gb - safety_margin_gb
        
        # Memory tracking
        self.memory_history: List[MemoryStats] = []
        self.peak_usage_gb = 0.0
        self.allocation_count = 0
        
        # Performance tracking
        self.oom_events = 0
        self.gc_events = 0
        
        logger.info(f"🧠 GPU Memory Manager initialized")
        logger.info(f"   GPU ID: {gpu_id}")
        logger.info(f"   Total VRAM: {self.total_memory_gb}GB")
        logger.info(f"   Usable VRAM: {self.usable_memory_gb}GB")
        logger.info(f"   Safety margin: {safety_margin_gb}GB")
        
        # Initialize memory pools
        self._setup_memory_pools()
    
    def _setup_memory_pools(self):
        """Setup memory pools for efficient allocation"""
        if CUPY_AVAILABLE:
            try:
                # Configure CuPy memory pool
                mempool = cp.get_default_memory_pool()
                mempool.set_limit(size=int(self.usable_memory_gb * 1024**3))
                
                # Enable memory pool growth
                cp.cuda.MemoryPool().set_limit(size=int(self.usable_memory_gb * 1024**3))
                
                logger.info(f"✅ CuPy memory pool configured: {self.usable_memory_gb}GB")
            except Exception as e:
                logger.warning(f"⚠️ CuPy memory pool setup failed: {e}")
        
        if TORCH_AVAILABLE:
            try:
                # Set PyTorch memory fraction
                memory_fraction = self.usable_memory_gb / self.total_memory_gb
                torch.cuda.set_per_process_memory_fraction(memory_fraction, self.gpu_id)
                
                # Enable memory caching
                torch.cuda.empty_cache()
                
                logger.info(f"✅ PyTorch memory configured: {memory_fraction:.2f} fraction")
            except Exception as e:
                logger.warning(f"⚠️ PyTorch memory setup failed: {e}")
    
    def get_memory_stats(self) -> MemoryStats:
        """Get current GPU memory statistics"""
        try:
            if NVML_AVAILABLE:
                # Use NVIDIA ML for accurate stats
                handle = nvml.nvmlDeviceGetHandleByIndex(self.gpu_id)
                info = nvml.nvmlDeviceGetMemoryInfo(handle)
                
                total_gb = info.total / 1024**3
                used_gb = info.used / 1024**3
                free_gb = info.free / 1024**3
                utilization = (used_gb / total_gb) * 100
                
            elif CUPY_AVAILABLE:
                # Use CuPy memory pool
                mempool = cp.get_default_memory_pool()
                used_bytes = mempool.used_bytes()
                total_bytes = mempool.total_bytes()
                
                used_gb = used_bytes / 1024**3
                total_gb = self.total_memory_gb
                free_gb = total_gb - used_gb
                utilization = (used_gb / total_gb) * 100
                
            elif TORCH_AVAILABLE:
                # Use PyTorch CUDA stats
                used_bytes = torch.cuda.memory_allocated(self.gpu_id)
                reserved_bytes = torch.cuda.memory_reserved(self.gpu_id)
                
                used_gb = used_bytes / 1024**3
                total_gb = self.total_memory_gb
                free_gb = total_gb - (reserved_bytes / 1024**3)
                utilization = (used_gb / total_gb) * 100
                
            else:
                # Fallback estimates
                total_gb = self.total_memory_gb
                used_gb = 0.0
                free_gb = total_gb
                utilization = 0.0
            
            stats = MemoryStats(
                total_gb=total_gb,
                used_gb=used_gb,
                free_gb=free_gb,
                utilization_percent=utilization,
                timestamp=time.time()
            )
            
            # Track peak usage
            self.peak_usage_gb = max(self.peak_usage_gb, used_gb)
            
            # Store in history (keep last 100 entries)
            self.memory_history.append(stats)
            if len(self.memory_history) > 100:
                self.memory_history.pop(0)
            
            return stats
            
        except Exception as e:
            logger.warning(f"⚠️ Memory stats collection failed: {e}")
            return MemoryStats(
                total_gb=self.total_memory_gb,
                used_gb=0.0,
                free_gb=self.total_memory_gb,
                utilization_percent=0.0,
                timestamp=time.time()
            )
    
    def calculate_optimal_batch_size(self, 
                                   data_size: int, 
                                   feature_count: int,
                                   model_type: str = "xgboost",
                                   dtype_size: int = 4) -> BatchConfig:
        """
        Calculate optimal batch size for given data and model
        
        Args:
            data_size: Number of samples
            feature_count: Number of features
            model_type: Type of model (affects memory multiplier)
            dtype_size: Size of data type in bytes (4 for float32)
            
        Returns:
            BatchConfig with optimal settings
        """
        # Memory multipliers for different model types
        memory_multipliers = {
            'xgboost': 3.0,      # Features + gradients + tree structures
            'lightgbm': 2.5,     # More memory efficient than XGBoost
            'random_forest': 2.0, # Relatively memory efficient
            'neural_network': 4.0, # Forward + backward pass + optimizer states
            'ensemble': 3.5      # Multiple models in memory
        }
        
        multiplier = memory_multipliers.get(model_type.lower(), 3.0)
        
        # Get current memory stats
        stats = self.get_memory_stats()
        available_memory_gb = stats.free_gb - 1.0  # Keep 1GB buffer
        
        # Estimate memory per sample
        memory_per_sample_bytes = feature_count * dtype_size * multiplier
        memory_per_sample_gb = memory_per_sample_bytes / 1024**3
        
        # Calculate maximum batch size
        max_batch_size = int(available_memory_gb / memory_per_sample_gb)
        
        # Apply practical constraints
        optimal_batch_size = min(
            max_batch_size,
            16384,      # Maximum practical batch size
            data_size,  # Can't exceed data size
            max(32, data_size // 100)  # At least 32, at most 1% of data
        )
        
        # Ensure minimum batch size
        optimal_batch_size = max(optimal_batch_size, 32)
        
        # Calculate batch configuration
        num_batches = (data_size + optimal_batch_size - 1) // optimal_batch_size
        memory_per_batch_gb = optimal_batch_size * memory_per_sample_gb
        estimated_peak_memory_gb = memory_per_batch_gb + stats.used_gb
        
        config = BatchConfig(
            batch_size=optimal_batch_size,
            num_batches=num_batches,
            memory_per_batch_gb=memory_per_batch_gb,
            estimated_peak_memory_gb=estimated_peak_memory_gb
        )
        
        logger.info(f"📊 Optimal batch configuration calculated:")
        logger.info(f"   Data size: {data_size:,} samples")
        logger.info(f"   Features: {feature_count}")
        logger.info(f"   Model type: {model_type}")
        logger.info(f"   Batch size: {optimal_batch_size:,}")
        logger.info(f"   Number of batches: {num_batches}")
        logger.info(f"   Memory per batch: {memory_per_batch_gb:.2f}GB")
        logger.info(f"   Estimated peak memory: {estimated_peak_memory_gb:.2f}GB")
        
        return config
    
    @contextmanager
    def memory_context(self, operation_name: str = "operation"):
        """
        Context manager for memory-safe operations
        
        Args:
            operation_name: Name of the operation for logging
        """
        start_stats = self.get_memory_stats()
        start_time = time.time()
        
        logger.info(f"🔄 Starting {operation_name}")
        logger.info(f"   Initial memory: {start_stats.used_gb:.2f}GB / {start_stats.total_gb:.2f}GB")
        
        try:
            yield start_stats
            
        except RuntimeError as e:
            if "out of memory" in str(e).lower():
                logger.error(f"💥 OOM during {operation_name}: {e}")
                self.oom_events += 1
                self.emergency_cleanup()
                raise
            else:
                raise
                
        except Exception as e:
            logger.error(f"❌ Error during {operation_name}: {e}")
            raise
            
        finally:
            end_stats = self.get_memory_stats()
            duration = time.time() - start_time
            memory_delta = end_stats.used_gb - start_stats.used_gb
            
            logger.info(f"✅ Completed {operation_name}")
            logger.info(f"   Duration: {duration:.2f}s")
            logger.info(f"   Final memory: {end_stats.used_gb:.2f}GB / {end_stats.total_gb:.2f}GB")
            logger.info(f"   Memory delta: {memory_delta:+.2f}GB")
    
    def emergency_cleanup(self):
        """Emergency memory cleanup on OOM"""
        logger.warning("🚨 Emergency memory cleanup triggered")
        
        initial_stats = self.get_memory_stats()
        
        # Python garbage collection
        gc.collect()
        
        # CuPy cleanup
        if CUPY_AVAILABLE:
            try:
                mempool = cp.get_default_memory_pool()
                mempool.free_all_blocks()
                logger.info("   ✅ CuPy memory freed")
            except:
                pass
        
        # PyTorch cleanup
        if TORCH_AVAILABLE:
            try:
                torch.cuda.empty_cache()
                torch.cuda.synchronize()
                logger.info("   ✅ PyTorch cache cleared")
            except:
                pass
        
        final_stats = self.get_memory_stats()
        freed_gb = initial_stats.used_gb - final_stats.used_gb
        
        self.gc_events += 1
        
        logger.info(f"   Memory freed: {freed_gb:.2f}GB")
        logger.info(f"   Final usage: {final_stats.used_gb:.2f}GB / {final_stats.total_gb:.2f}GB")
    
    def monitor_memory_usage(self, threshold_percent: float = 85.0) -> bool:
        """
        Monitor memory usage and trigger cleanup if needed
        
        Args:
            threshold_percent: Memory usage threshold for cleanup
            
        Returns:
            True if memory is within safe limits
        """
        stats = self.get_memory_stats()
        
        if stats.utilization_percent > threshold_percent:
            logger.warning(f"⚠️ High memory usage: {stats.utilization_percent:.1f}%")
            logger.warning(f"   Used: {stats.used_gb:.2f}GB / {stats.total_gb:.2f}GB")
            
            # Trigger cleanup
            self.emergency_cleanup()
            
            # Check again
            new_stats = self.get_memory_stats()
            if new_stats.utilization_percent > threshold_percent:
                logger.error(f"❌ Memory usage still high after cleanup: {new_stats.utilization_percent:.1f}%")
                return False
            else:
                logger.info(f"✅ Memory usage reduced to: {new_stats.utilization_percent:.1f}%")
                return True
        
        return True
    
    def get_memory_report(self) -> Dict[str, Any]:
        """Get comprehensive memory usage report"""
        current_stats = self.get_memory_stats()
        
        # Calculate statistics from history
        if self.memory_history:
            usage_history = [stats.used_gb for stats in self.memory_history[-20:]]  # Last 20 entries
            avg_usage = sum(usage_history) / len(usage_history)
            max_usage = max(usage_history)
            min_usage = min(usage_history)
        else:
            avg_usage = max_usage = min_usage = current_stats.used_gb
        
        # System memory info
        system_memory = psutil.virtual_memory()
        
        report = {
            'current_gpu_memory': {
                'total_gb': current_stats.total_gb,
                'used_gb': current_stats.used_gb,
                'free_gb': current_stats.free_gb,
                'utilization_percent': current_stats.utilization_percent
            },
            'gpu_memory_statistics': {
                'peak_usage_gb': self.peak_usage_gb,
                'average_usage_gb': avg_usage,
                'max_usage_gb': max_usage,
                'min_usage_gb': min_usage,
                'safety_margin_gb': self.safety_margin_gb,
                'usable_memory_gb': self.usable_memory_gb
            },
            'system_memory': {
                'total_gb': system_memory.total / 1024**3,
                'used_gb': system_memory.used / 1024**3,
                'available_gb': system_memory.available / 1024**3,
                'utilization_percent': system_memory.percent
            },
            'performance_metrics': {
                'oom_events': self.oom_events,
                'gc_events': self.gc_events,
                'allocation_count': self.allocation_count,
                'memory_history_length': len(self.memory_history)
            },
            'hardware_info': {
                'gpu_id': self.gpu_id,
                'gpu_model': 'RTX 4070 Ti',
                'cuda_available': TORCH_AVAILABLE,
                'cupy_available': CUPY_AVAILABLE,
                'nvml_available': NVML_AVAILABLE
            }
        }
        
        return report
    
    def optimize_for_training(self, data_size: int, feature_count: int, model_type: str) -> Dict[str, Any]:
        """
        Optimize memory settings for training
        
        Args:
            data_size: Number of training samples
            feature_count: Number of features
            model_type: Type of model to train
            
        Returns:
            Optimization recommendations
        """
        logger.info(f"🎯 Optimizing memory for {model_type} training")
        logger.info(f"   Data: {data_size:,} samples × {feature_count} features")
        
        # Calculate optimal batch configuration
        batch_config = self.calculate_optimal_batch_size(data_size, feature_count, model_type)
        
        # Check if training is feasible
        current_stats = self.get_memory_stats()
        
        if batch_config.estimated_peak_memory_gb > self.usable_memory_gb:
            logger.warning("⚠️ Training may exceed available memory")
            logger.warning(f"   Estimated peak: {batch_config.estimated_peak_memory_gb:.2f}GB")
            logger.warning(f"   Available: {self.usable_memory_gb:.2f}GB")
            
            # Suggest smaller batch size
            safe_batch_size = max(32, int(batch_config.batch_size * 0.7))
            logger.info(f"   Suggested safer batch size: {safe_batch_size}")
        
        recommendations = {
            'batch_config': batch_config,
            'memory_feasible': batch_config.estimated_peak_memory_gb <= self.usable_memory_gb,
            'current_memory_usage_gb': current_stats.used_gb,
            'available_memory_gb': current_stats.free_gb,
            'recommendations': []
        }
        
        # Add specific recommendations
        if current_stats.utilization_percent > 70:
            recommendations['recommendations'].append("Consider running emergency cleanup before training")
        
        if batch_config.batch_size < 128:
            recommendations['recommendations'].append("Small batch size may slow training - consider reducing feature count")
        
        if batch_config.num_batches > 1000:
            recommendations['recommendations'].append("Many batches detected - consider data sampling or feature selection")
        
        return recommendations

def main():
    """Test GPU memory manager"""
    logger.info("🧠 Testing GPU Memory Manager")
    logger.info("=" * 50)
    
    # Initialize manager
    memory_manager = GPUMemoryManager()
    
    # Get memory report
    report = memory_manager.get_memory_report()
    logger.info("📊 Memory Report:")
    for section, data in report.items():
        logger.info(f"   {section}:")
        if isinstance(data, dict):
            for key, value in data.items():
                logger.info(f"     {key}: {value}")
        else:
            logger.info(f"     {data}")
    
    # Test batch size calculation
    batch_config = memory_manager.calculate_optimal_batch_size(
        data_size=100000,
        feature_count=50,
        model_type="xgboost"
    )
    
    # Test memory context
    with memory_manager.memory_context("test_operation"):
        time.sleep(1)  # Simulate work
    
    # Test optimization
    optimization = memory_manager.optimize_for_training(
        data_size=50000,
        feature_count=60,
        model_type="ensemble"
    )
    
    logger.info("🎯 Training Optimization:")
    for key, value in optimization.items():
        logger.info(f"   {key}: {value}")
    
    logger.info("🎉 GPU Memory Manager test completed!")

if __name__ == "__main__":
    main()
