#!/usr/bin/env python3
"""
Parallel Optimization System
============================

High-performance parallel hyperparameter optimization:
- Multi-process optimization with GPU sharing
- Distributed optimization across multiple GPUs
- Asynchronous trial execution
- Resource-aware scheduling
- Real-time progress monitoring

Hardware: RTX 4070 Ti + 32GB RAM + 16 CPU cores
Created: June 6, 2025
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import numpy as np
import pandas as pd
import logging
from datetime import datetime
from typing import Dict, List, Tuple, Any, Optional
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor, as_completed
import time
import psutil
import threading
import queue

# Optuna for parallel optimization
try:
    import optuna
    from optuna.samplers import TPESampler
    from optuna.pruners import MedianPruner
    OPTUNA_AVAILABLE = True
except ImportError:
    OPTUNA_AVAILABLE = False

# ML libraries
import xgboost as xgb
import lightgbm as lgb
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_squared_error, r2_score

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ParallelOptimizationSystem:
    """
    High-performance parallel optimization system
    """
    
    def __init__(self, n_workers: int = None, gpu_memory_per_worker: float = 2.0):
        """
        Initialize parallel optimization system
        
        Args:
            n_workers: Number of parallel workers (default: CPU cores)
            gpu_memory_per_worker: GPU memory allocation per worker (GB)
        """
        self.n_workers = n_workers or min(mp.cpu_count(), 8)  # Limit to 8 for memory
        self.gpu_memory_per_worker = gpu_memory_per_worker
        
        # System resources
        self.total_memory_gb = psutil.virtual_memory().total / 1024**3
        self.available_memory_gb = psutil.virtual_memory().available / 1024**3
        
        # Optimization tracking
        self.active_workers = 0
        self.completed_trials = 0
        self.best_results = {}
        self.worker_stats = {}
        
        # Thread-safe communication
        self.result_queue = queue.Queue()
        self.progress_lock = threading.Lock()
        
        logger.info("🚀 Parallel Optimization System initialized")
        logger.info(f"   Workers: {self.n_workers}")
        logger.info(f"   GPU memory per worker: {gpu_memory_per_worker}GB")
        logger.info(f"   System memory: {self.total_memory_gb:.1f}GB")
        logger.info(f"   Available memory: {self.available_memory_gb:.1f}GB")
    
    def create_worker_objective(self, model_type: str, X_train: np.ndarray, y_train: np.ndarray,
                               X_val: np.ndarray, y_val: np.ndarray, worker_id: int):
        """Create objective function for a specific worker"""
        
        def worker_objective(trial):
            start_time = time.time()
            
            try:
                if model_type == 'xgboost':
                    params = {
                        'n_estimators': trial.suggest_int('n_estimators', 100, 2000),
                        'max_depth': trial.suggest_int('max_depth', 3, 15),
                        'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3, log=True),
                        'subsample': trial.suggest_float('subsample', 0.6, 1.0),
                        'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
                        'reg_alpha': trial.suggest_float('reg_alpha', 0.0, 10.0),
                        'reg_lambda': trial.suggest_float('reg_lambda', 0.0, 10.0),
                        'random_state': 42,
                        'n_jobs': 1,  # Single thread per worker
                        'tree_method': 'hist'  # CPU for parallel workers
                    }
                    
                    model = xgb.XGBRegressor(**params)
                    
                elif model_type == 'lightgbm':
                    params = {
                        'n_estimators': trial.suggest_int('n_estimators', 100, 2000),
                        'max_depth': trial.suggest_int('max_depth', 3, 15),
                        'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3, log=True),
                        'subsample': trial.suggest_float('subsample', 0.6, 1.0),
                        'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
                        'reg_alpha': trial.suggest_float('reg_alpha', 0.0, 10.0),
                        'reg_lambda': trial.suggest_float('reg_lambda', 0.0, 10.0),
                        'num_leaves': trial.suggest_int('num_leaves', 10, 300),
                        'random_state': 42,
                        'n_jobs': 1,
                        'verbose': -1
                    }
                    
                    model = lgb.LGBMRegressor(**params)
                    
                elif model_type == 'random_forest':
                    params = {
                        'n_estimators': trial.suggest_int('n_estimators', 50, 500),
                        'max_depth': trial.suggest_int('max_depth', 5, 30),
                        'min_samples_split': trial.suggest_int('min_samples_split', 2, 20),
                        'min_samples_leaf': trial.suggest_int('min_samples_leaf', 1, 10),
                        'max_features': trial.suggest_categorical('max_features', ['sqrt', 'log2', 0.5, 0.8]),
                        'random_state': 42,
                        'n_jobs': 1
                    }
                    
                    model = RandomForestRegressor(**params)
                
                else:
                    raise ValueError(f"Unknown model type: {model_type}")
                
                # Train model
                model.fit(X_train, y_train)
                
                # Evaluate
                y_pred = model.predict(X_val)
                rmse = np.sqrt(mean_squared_error(y_val, y_pred))
                r2 = r2_score(y_val, y_pred)
                
                training_time = time.time() - start_time
                
                # Update worker stats
                with self.progress_lock:
                    if worker_id not in self.worker_stats:
                        self.worker_stats[worker_id] = {
                            'trials_completed': 0,
                            'total_time': 0,
                            'best_rmse': float('inf')
                        }
                    
                    self.worker_stats[worker_id]['trials_completed'] += 1
                    self.worker_stats[worker_id]['total_time'] += training_time
                    self.worker_stats[worker_id]['best_rmse'] = min(
                        self.worker_stats[worker_id]['best_rmse'], rmse
                    )
                    
                    self.completed_trials += 1
                
                # Store result
                result = {
                    'worker_id': worker_id,
                    'trial_number': trial.number,
                    'params': params,
                    'rmse': rmse,
                    'r2': r2,
                    'training_time': training_time,
                    'timestamp': datetime.now()
                }
                
                self.result_queue.put(result)
                
                return rmse
                
            except Exception as e:
                logger.error(f"Worker {worker_id} trial failed: {e}")
                return float('inf')
        
        return worker_objective
    
    def run_parallel_optimization(self, model_type: str, X_train: np.ndarray, y_train: np.ndarray,
                                 X_val: np.ndarray, y_val: np.ndarray,
                                 n_trials_per_worker: int = 50, timeout: int = 3600) -> Dict[str, Any]:
        """Run parallel optimization across multiple workers"""
        
        if not OPTUNA_AVAILABLE:
            logger.error("❌ Optuna not available - cannot perform optimization")
            return {}
        
        logger.info(f"🚀 Starting parallel optimization for {model_type}")
        logger.info(f"   Workers: {self.n_workers}")
        logger.info(f"   Trials per worker: {n_trials_per_worker}")
        logger.info(f"   Total trials: {self.n_workers * n_trials_per_worker}")
        logger.info(f"   Timeout: {timeout}s")
        
        # Create shared study
        study_name = f"parallel_{model_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        storage_url = f"sqlite:///parallel_studies.db"
        
        study = optuna.create_study(
            study_name=study_name,
            storage=storage_url,
            load_if_exists=True,
            direction='minimize',
            sampler=TPESampler(n_startup_trials=10),
            pruner=MedianPruner(n_startup_trials=5)
        )
        
        # Start progress monitoring thread
        progress_thread = threading.Thread(
            target=self._monitor_progress,
            args=(n_trials_per_worker * self.n_workers, timeout),
            daemon=True
        )
        progress_thread.start()
        
        # Launch parallel workers
        start_time = time.time()
        
        def worker_task(worker_id):
            """Task for individual worker"""
            try:
                objective = self.create_worker_objective(
                    model_type, X_train, y_train, X_val, y_val, worker_id
                )
                
                # Each worker optimizes independently
                study.optimize(
                    objective,
                    n_trials=n_trials_per_worker,
                    timeout=timeout // self.n_workers  # Distribute timeout
                )
                
                return f"Worker {worker_id} completed"
                
            except Exception as e:
                logger.error(f"Worker {worker_id} failed: {e}")
                return f"Worker {worker_id} failed: {e}"
        
        # Execute workers in parallel
        with ThreadPoolExecutor(max_workers=self.n_workers) as executor:
            futures = [executor.submit(worker_task, i) for i in range(self.n_workers)]
            
            # Wait for completion
            for future in as_completed(futures):
                result = future.result()
                logger.info(f"   {result}")
        
        total_time = time.time() - start_time
        
        # Collect results
        all_results = []
        while not self.result_queue.empty():
            all_results.append(self.result_queue.get())
        
        # Find best result
        if all_results:
            best_result = min(all_results, key=lambda x: x['rmse'])
        else:
            best_result = None
        
        # Calculate statistics
        if all_results:
            rmse_values = [r['rmse'] for r in all_results]
            training_times = [r['training_time'] for r in all_results]
            
            stats = {
                'mean_rmse': np.mean(rmse_values),
                'std_rmse': np.std(rmse_values),
                'min_rmse': np.min(rmse_values),
                'max_rmse': np.max(rmse_values),
                'mean_training_time': np.mean(training_times),
                'total_training_time': np.sum(training_times),
                'parallel_efficiency': np.sum(training_times) / total_time
            }
        else:
            stats = {}
        
        results = {
            'model_type': model_type,
            'best_result': best_result,
            'all_results': all_results,
            'statistics': stats,
            'worker_stats': self.worker_stats.copy(),
            'total_time': total_time,
            'n_workers': self.n_workers,
            'trials_completed': len(all_results),
            'study': study
        }
        
        logger.info(f"✅ Parallel optimization completed!")
        logger.info(f"   Total time: {total_time:.1f}s")
        logger.info(f"   Trials completed: {len(all_results)}")
        logger.info(f"   Parallel efficiency: {stats.get('parallel_efficiency', 0):.2f}x")
        
        if best_result:
            logger.info(f"   Best RMSE: {best_result['rmse']:.4f}")
            logger.info(f"   Best R²: {best_result['r2']:.4f}")
        
        return results
    
    def _monitor_progress(self, total_trials: int, timeout: int):
        """Monitor optimization progress"""
        start_time = time.time()
        last_update = 0
        
        while True:
            current_time = time.time()
            elapsed = current_time - start_time
            
            if elapsed > timeout:
                logger.info("⏰ Optimization timeout reached")
                break
            
            with self.progress_lock:
                completed = self.completed_trials
            
            if completed >= total_trials:
                logger.info("🎯 All trials completed")
                break
            
            # Update every 30 seconds
            if elapsed - last_update >= 30:
                progress_pct = (completed / total_trials) * 100
                trials_per_sec = completed / elapsed if elapsed > 0 else 0
                eta = (total_trials - completed) / trials_per_sec if trials_per_sec > 0 else 0
                
                logger.info(f"📊 Progress: {completed}/{total_trials} ({progress_pct:.1f}%) "
                           f"- {trials_per_sec:.2f} trials/s - ETA: {eta:.0f}s")
                
                # Worker statistics
                with self.progress_lock:
                    for worker_id, stats in self.worker_stats.items():
                        avg_time = stats['total_time'] / stats['trials_completed'] if stats['trials_completed'] > 0 else 0
                        logger.info(f"   Worker {worker_id}: {stats['trials_completed']} trials, "
                                   f"best RMSE: {stats['best_rmse']:.4f}, avg time: {avg_time:.2f}s")
                
                last_update = elapsed
            
            time.sleep(5)  # Check every 5 seconds
    
    def benchmark_parallel_performance(self, X_train: np.ndarray, y_train: np.ndarray,
                                     X_val: np.ndarray, y_val: np.ndarray) -> Dict[str, Any]:
        """Benchmark parallel vs sequential performance"""
        logger.info("🏁 Benchmarking parallel performance...")
        
        models = ['xgboost', 'lightgbm', 'random_forest']
        worker_counts = [1, 2, 4, self.n_workers]
        
        benchmark_results = {}
        
        for model_type in models:
            logger.info(f"\n📊 Benchmarking {model_type}...")
            model_results = {}
            
            for n_workers in worker_counts:
                if n_workers > self.n_workers:
                    continue
                
                logger.info(f"   Testing with {n_workers} workers...")
                
                # Reset stats
                self.completed_trials = 0
                self.worker_stats = {}
                
                # Temporarily change worker count
                original_workers = self.n_workers
                self.n_workers = n_workers
                
                # Run optimization
                result = self.run_parallel_optimization(
                    model_type=model_type,
                    X_train=X_train,
                    y_train=y_train,
                    X_val=X_val,
                    y_val=y_val,
                    n_trials_per_worker=10,  # Reduced for benchmarking
                    timeout=300  # 5 minutes max
                )
                
                model_results[f'{n_workers}_workers'] = {
                    'total_time': result['total_time'],
                    'trials_completed': result['trials_completed'],
                    'best_rmse': result['best_result']['rmse'] if result['best_result'] else float('inf'),
                    'parallel_efficiency': result['statistics'].get('parallel_efficiency', 0)
                }
                
                # Restore original worker count
                self.n_workers = original_workers
            
            benchmark_results[model_type] = model_results
        
        # Calculate speedup ratios
        for model_type, results in benchmark_results.items():
            baseline_time = results.get('1_workers', {}).get('total_time', 1)
            
            logger.info(f"\n📈 {model_type.upper()} Speedup Analysis:")
            for config, metrics in results.items():
                speedup = baseline_time / metrics['total_time'] if metrics['total_time'] > 0 else 0
                efficiency = speedup / int(config.split('_')[0]) * 100
                
                logger.info(f"   {config}: {speedup:.2f}x speedup, {efficiency:.1f}% efficiency")
        
        return benchmark_results

def main():
    """Test parallel optimization system"""
    logger.info("🚀 Testing Parallel Optimization System")
    logger.info("=" * 70)
    
    # Create synthetic data
    np.random.seed(42)
    n_samples = 1000
    n_features = 15
    
    X = np.random.randn(n_samples, n_features)
    y = (2 * X[:, 0] + 1.5 * X[:, 1] + 0.8 * X[:, 2] + 
         np.random.normal(0, 0.3, n_samples))
    
    # Split data
    split_idx = int(0.8 * n_samples)
    X_train, X_test = X[:split_idx], X[split_idx:]
    y_train, y_test = y[:split_idx], y[split_idx:]
    
    val_split = int(0.8 * len(X_train))
    X_train_final, X_val = X_train[:val_split], X_train[val_split:]
    y_train_final, y_val = y_train[:val_split], y_train[val_split:]
    
    logger.info(f"Data: Train={X_train_final.shape}, Val={X_val.shape}, Test={X_test.shape}")
    
    # Initialize parallel optimizer
    parallel_optimizer = ParallelOptimizationSystem(n_workers=4)
    
    # Test parallel optimization
    model_type = 'xgboost'
    
    results = parallel_optimizer.run_parallel_optimization(
        model_type=model_type,
        X_train=X_train_final,
        y_train=y_train_final,
        X_val=X_val,
        y_val=y_val,
        n_trials_per_worker=20,
        timeout=600  # 10 minutes
    )
    
    # Display results
    logger.info("\n🎯 PARALLEL OPTIMIZATION RESULTS")
    logger.info("=" * 70)
    
    if results['best_result']:
        logger.info(f"📊 Best Result:")
        logger.info(f"   RMSE: {results['best_result']['rmse']:.4f}")
        logger.info(f"   R²: {results['best_result']['r2']:.4f}")
        logger.info(f"   Worker: {results['best_result']['worker_id']}")
        logger.info(f"   Training time: {results['best_result']['training_time']:.2f}s")
    
    logger.info(f"\n⚡ Performance:")
    logger.info(f"   Total time: {results['total_time']:.1f}s")
    logger.info(f"   Trials completed: {results['trials_completed']}")
    logger.info(f"   Parallel efficiency: {results['statistics'].get('parallel_efficiency', 0):.2f}x")
    
    logger.info(f"\n👥 Worker Statistics:")
    for worker_id, stats in results['worker_stats'].items():
        avg_time = stats['total_time'] / stats['trials_completed'] if stats['trials_completed'] > 0 else 0
        logger.info(f"   Worker {worker_id}: {stats['trials_completed']} trials, "
                   f"best RMSE: {stats['best_rmse']:.4f}, avg time: {avg_time:.2f}s")
    
    logger.info("\n✅ Parallel Optimization System test completed!")
    
    return parallel_optimizer, results

if __name__ == "__main__":
    optimizer, results = main()
