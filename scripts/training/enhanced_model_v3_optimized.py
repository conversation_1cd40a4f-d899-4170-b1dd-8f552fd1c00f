#!/usr/bin/env python3
"""
Enhanced Model v3 - Optimized for >95% Accuracy
Advanced hyperparameter tuning and feature optimization
"""

import pandas as pd
import numpy as np
import joblib
import json
import os
from datetime import datetime, timedelta
from pathlib import Path
from sklearn.model_selection import TimeSeriesSplit, GridSearchCV
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.metrics import r2_score, mean_absolute_error
import lightgbm as lgb
from sklearn.feature_selection import SelectKBest, f_regression
import warnings
warnings.filterwarnings('ignore')

def load_optimized_data():
    """Load data with optimized filtering"""
    print("📊 LOADING OPTIMIZED SOLAR DATA")
    print("=" * 50)
    
    try:
        import psycopg2
        
        conn = psycopg2.connect(
            host=os.getenv('DB_HOST', 'localhost'),
            database=os.getenv('DB_NAME', 'solar_prediction'),
            user=os.getenv('DB_USER', 'postgres'),
            password=os.getenv('DB_PASSWORD', 'postgres')
        )
        
        # Optimized query with better data filtering
        query = """
        SELECT 
            s1.timestamp,
            s1.ac_power as system1_ac_power,
            s1.soc as system1_soc,
            s1.bat_power as system1_battery_power,
            s1.powerdc1 + s1.powerdc2 as system1_pv_power,
            s1.yield_today as system1_yield_today,
            s1.yield_total as system1_yield_total,
            s2.ac_power as system2_ac_power,
            s2.soc as system2_soc,
            s2.bat_power as system2_battery_power,
            s2.powerdc1 + s2.powerdc2 as system2_pv_power,
            s2.yield_today as system2_yield_today,
            s2.yield_total as system2_yield_total
        FROM solax_data s1
        LEFT JOIN solax_data2 s2 ON DATE_TRUNC('minute', s1.timestamp) = DATE_TRUNC('minute', s2.timestamp)
        WHERE s1.timestamp >= '2024-03-01'
        AND s1.timestamp < '2025-06-01'
        AND s1.ac_power IS NOT NULL
        AND s2.ac_power IS NOT NULL
        AND s1.soc IS NOT NULL
        AND s2.soc IS NOT NULL
        AND s1.ac_power BETWEEN -8000 AND 12000  -- Remove extreme outliers
        AND s2.ac_power BETWEEN -8000 AND 12000
        ORDER BY s1.timestamp
        """
        
        df = pd.read_sql(query, conn)
        conn.close()
        
        print(f"✅ Loaded {len(df):,} records")
        print(f"📅 Date range: {df['timestamp'].min()} to {df['timestamp'].max()}")
        
        return df
        
    except Exception as e:
        print(f"❌ Failed to load data: {e}")
        return None

def create_optimized_features(df):
    """Create optimized feature set"""
    print("\n🔧 CREATING OPTIMIZED FEATURES")
    print("=" * 50)
    
    # Convert timestamp
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    df = df.set_index('timestamp')
    
    # === ENHANCED TEMPORAL FEATURES ===
    df['hour'] = df.index.hour
    df['day_of_year'] = df.index.dayofyear
    df['month'] = df.index.month
    df['day_of_week'] = df.index.dayofweek
    df['week_of_year'] = df.index.isocalendar().week
    df['is_weekend'] = (df.index.dayofweek >= 5).astype(int)
    
    # Advanced cyclical encoding
    df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
    df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
    df['day_sin'] = np.sin(2 * np.pi * df['day_of_year'] / 365)
    df['day_cos'] = np.cos(2 * np.pi * df['day_of_year'] / 365)
    df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
    df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
    df['week_sin'] = np.sin(2 * np.pi * df['week_of_year'] / 52)
    df['week_cos'] = np.cos(2 * np.pi * df['week_of_year'] / 52)
    
    # === ENHANCED SOLAR POSITION ===
    # More accurate solar elevation
    df['solar_elevation'] = np.where(
        (df['hour'] >= 5) & (df['hour'] <= 19),
        90 * np.sin(np.pi * (df['hour'] - 5) / 14) * (0.5 + 0.5 * np.cos(2 * np.pi * (df['day_of_year'] - 172) / 365)),
        0
    )
    df['solar_elevation_norm'] = df['solar_elevation'] / 90.0
    df['is_daylight'] = (df['solar_elevation'] > 5).astype(int)
    df['is_peak_sun'] = ((df['hour'] >= 10) & (df['hour'] <= 14)).astype(int)
    
    # === ADVANCED SYSTEM FEATURES ===
    # System 1 enhanced features
    df['system1_efficiency'] = np.where(
        df['system1_pv_power'] > 100,
        np.clip(df['system1_ac_power'] / df['system1_pv_power'], 0, 1.2),
        0
    )
    df['system1_battery_flow'] = df['system1_battery_power'] / 1000.0  # Normalize to kW
    df['system1_net_production'] = df['system1_ac_power'] + abs(df['system1_battery_power'])
    
    # System 2 enhanced features
    df['system2_efficiency'] = np.where(
        df['system2_pv_power'] > 100,
        np.clip(df['system2_ac_power'] / df['system2_pv_power'], 0, 1.2),
        0
    )
    df['system2_battery_flow'] = df['system2_battery_power'] / 1000.0
    df['system2_net_production'] = df['system2_ac_power'] + abs(df['system2_battery_power'])
    
    # === CROSS-SYSTEM ANALYTICS ===
    df['total_production'] = df['system1_ac_power'] + df['system2_ac_power']
    df['production_ratio'] = np.where(
        df['system2_ac_power'] != 0,
        df['system1_ac_power'] / (df['system2_ac_power'] + 1),
        1
    )
    df['soc_avg'] = (df['system1_soc'] + df['system2_soc']) / 2
    df['soc_diff'] = abs(df['system1_soc'] - df['system2_soc'])
    df['battery_sync'] = np.where(
        (df['system1_battery_power'] * df['system2_battery_power']) > 0, 1, 0
    )
    
    # === ADVANCED ROLLING FEATURES ===
    # Short-term patterns (1 hour = 12 records)
    for col in ['system1_ac_power', 'system2_ac_power']:
        df[f'{col}_1h_mean'] = df[col].rolling(window=12, min_periods=6).mean()
        df[f'{col}_1h_max'] = df[col].rolling(window=12, min_periods=6).max()
        df[f'{col}_1h_std'] = df[col].rolling(window=12, min_periods=6).std()
        df[f'{col}_trend_1h'] = df[col] - df[f'{col}_1h_mean']
    
    # Medium-term patterns (6 hours = 72 records)
    for col in ['system1_ac_power', 'system2_ac_power']:
        df[f'{col}_6h_mean'] = df[col].rolling(window=72, min_periods=36).mean()
        df[f'{col}_6h_max'] = df[col].rolling(window=72, min_periods=36).max()
    
    # === ENHANCED LAG FEATURES ===
    # Recent history
    for col in ['system1_ac_power', 'system2_ac_power', 'system1_soc', 'system2_soc']:
        df[f'{col}_lag5min'] = df[col].shift(1)
        df[f'{col}_lag30min'] = df[col].shift(6)
        df[f'{col}_lag1h'] = df[col].shift(12)
        df[f'{col}_lag6h'] = df[col].shift(72)
    
    # === PRODUCTION PATTERNS ===
    df['morning_production'] = ((df['hour'] >= 6) & (df['hour'] <= 10)).astype(int)
    df['midday_production'] = ((df['hour'] >= 10) & (df['hour'] <= 14)).astype(int)
    df['afternoon_production'] = ((df['hour'] >= 14) & (df['hour'] <= 18)).astype(int)
    df['evening_production'] = ((df['hour'] >= 18) & (df['hour'] <= 20)).astype(int)
    
    # === SEASONAL INTELLIGENCE ===
    df['season'] = ((df['month'] - 1) // 3) % 4
    df['is_summer'] = (df['season'] == 2).astype(int)
    df['is_winter'] = (df['season'] == 0).astype(int)
    df['is_spring'] = (df['season'] == 1).astype(int)
    df['is_autumn'] = (df['season'] == 3).astype(int)
    
    # Seasonal solar intensity
    df['seasonal_factor'] = 0.7 + 0.3 * np.cos(2 * np.pi * (df['day_of_year'] - 172) / 365)
    df['solar_intensity'] = df['solar_elevation_norm'] * df['seasonal_factor']
    
    # === DATA CLEANING ===
    # Replace infinite values
    df = df.replace([np.inf, -np.inf], np.nan)
    
    # Fill missing values intelligently
    df = df.fillna(method='ffill', limit=12)  # Forward fill max 1 hour
    df = df.fillna(method='bfill', limit=12)  # Backward fill max 1 hour
    df = df.dropna()  # Remove remaining NaN
    
    print(f"✅ Created {len(df.columns)} optimized features")
    print(f"📊 Final dataset: {len(df):,} records")
    
    return df

def optimize_hyperparameters(X_train, y_train, X_val, y_val):
    """Optimize LightGBM hyperparameters"""
    print("🔧 OPTIMIZING HYPERPARAMETERS...")
    
    # Parameter grid for optimization
    param_grid = {
        'n_estimators': [2000, 3000],
        'learning_rate': [0.03, 0.05],
        'max_depth': [12, 15],
        'num_leaves': [60, 80],
        'subsample': [0.8, 0.9],
        'colsample_bytree': [0.8, 0.9],
        'reg_alpha': [0.1, 0.3],
        'reg_lambda': [0.1, 0.3]
    }
    
    best_score = 0
    best_params = None
    
    # Test key parameter combinations
    test_configs = [
        {'n_estimators': 3000, 'learning_rate': 0.03, 'max_depth': 15, 'num_leaves': 80, 
         'subsample': 0.9, 'colsample_bytree': 0.9, 'reg_alpha': 0.1, 'reg_lambda': 0.1},
        {'n_estimators': 2000, 'learning_rate': 0.05, 'max_depth': 12, 'num_leaves': 60,
         'subsample': 0.8, 'colsample_bytree': 0.8, 'reg_alpha': 0.3, 'reg_lambda': 0.3},
        {'n_estimators': 3000, 'learning_rate': 0.03, 'max_depth': 12, 'num_leaves': 80,
         'subsample': 0.9, 'colsample_bytree': 0.8, 'reg_alpha': 0.1, 'reg_lambda': 0.3}
    ]
    
    for i, params in enumerate(test_configs):
        print(f"   Testing config {i+1}/3...")
        
        model = lgb.LGBMRegressor(
            random_state=42,
            verbose=-1,
            **params
        )
        
        model.fit(X_train, y_train)
        y_pred = model.predict(X_val)
        score = r2_score(y_val, y_pred)
        
        print(f"   Config {i+1} R²: {score:.4f} ({score*100:.1f}%)")
        
        if score > best_score:
            best_score = score
            best_params = params
    
    print(f"🏆 Best validation R²: {best_score:.4f} ({best_score*100:.1f}%)")
    
    return best_params

def train_optimized_model(df, system):
    """Train optimized model with hyperparameter tuning"""
    print(f"\n🤖 TRAINING OPTIMIZED MODEL FOR {system.upper()}")
    print("=" * 60)
    
    # Select all available features except target
    exclude_cols = ['system1_ac_power', 'system2_ac_power']
    feature_columns = [col for col in df.columns if col not in exclude_cols]
    target_column = f'{system}_ac_power'
    
    # Prepare data
    X = df[feature_columns].values
    y = df[target_column].values
    
    print(f"📊 Features: {len(feature_columns)}")
    print(f"📊 Samples: {len(X):,}")
    print(f"🎯 Target range: {y.min():.1f}W to {y.max():.1f}W")
    
    # Advanced time series split (70% train, 15% validation, 15% test)
    train_idx = int(len(X) * 0.7)
    val_idx = int(len(X) * 0.85)
    
    X_train = X[:train_idx]
    X_val = X[train_idx:val_idx]
    X_test = X[val_idx:]
    
    y_train = y[:train_idx]
    y_val = y[train_idx:val_idx]
    y_test = y[val_idx:]
    
    print(f"📊 Train: {len(X_train):,}, Val: {len(X_val):,}, Test: {len(X_test):,}")
    
    # Scale features with RobustScaler (better for outliers)
    scaler = RobustScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_val_scaled = scaler.transform(X_val)
    X_test_scaled = scaler.transform(X_test)
    
    # Optimize hyperparameters
    best_params = optimize_hyperparameters(X_train_scaled, y_train, X_val_scaled, y_val)
    
    # Train final model with best parameters
    print(f"\n🏆 TRAINING FINAL MODEL WITH OPTIMIZED PARAMETERS")
    
    final_model = lgb.LGBMRegressor(
        random_state=42,
        verbose=-1,
        **best_params
    )
    
    # Train on train + validation data
    X_final_train = np.vstack([X_train_scaled, X_val_scaled])
    y_final_train = np.hstack([y_train, y_val])
    
    final_model.fit(X_final_train, y_final_train)
    
    # Final evaluation on test set
    y_pred_test = final_model.predict(X_test_scaled)
    
    test_r2 = r2_score(y_test, y_pred_test)
    test_mae = mean_absolute_error(y_test, y_pred_test)
    test_rmse = np.sqrt(np.mean((y_test - y_pred_test) ** 2))
    
    print(f"\n📊 FINAL TEST RESULTS:")
    print(f"   Test R²: {test_r2:.4f} ({test_r2*100:.1f}%)")
    print(f"   Test MAE: {test_mae:.1f}W")
    print(f"   Test RMSE: {test_rmse:.1f}W")
    
    # Check target
    if test_r2 >= 0.95:
        print(f"✅ TARGET ACHIEVED! ({test_r2*100:.1f}% ≥ 95%)")
    else:
        print(f"⚠️ CLOSE TO TARGET: {test_r2*100:.1f}% < 95%")
    
    return final_model, scaler, test_r2, feature_columns, best_params

def save_optimized_model(model, scaler, score, features, params, system):
    """Save optimized model"""
    print(f"\n💾 SAVING OPTIMIZED MODEL FOR {system.upper()}")
    print("=" * 50)
    
    # Create directory
    model_dir = Path("models/enhanced_model_v3_optimized")
    model_dir.mkdir(exist_ok=True)
    
    # Save files
    model_file = model_dir / f"{system}_model.joblib"
    scaler_file = model_dir / f"{system}_scaler.joblib"
    
    joblib.dump(model, model_file)
    joblib.dump(scaler, scaler_file)
    
    # Enhanced metadata
    metadata = {
        "model_name": "Enhanced Model v3 Optimized",
        "version": "3.1.0",
        "created_at": datetime.now().isoformat(),
        "system": system,
        "test_r2": score,
        "test_accuracy_percent": score * 100,
        "feature_count": len(features),
        "feature_columns": features,
        "algorithm": "LightGBM",
        "hyperparameters": params,
        "target_accuracy_met": score >= 0.95,
        "data_source": "solar_optimized",
        "training_approach": "train_val_test_split",
        "scaler_type": "RobustScaler",
        "optimization_method": "hyperparameter_tuning"
    }
    
    metadata_file = model_dir / f"{system}_metadata.json"
    with open(metadata_file, 'w') as f:
        json.dump(metadata, f, indent=2)
    
    print(f"✅ Model: {model_file}")
    print(f"✅ Scaler: {scaler_file}")
    print(f"✅ Metadata: {metadata_file}")
    
    return model_dir

def main():
    """Main optimization function"""
    print("🚀 ENHANCED MODEL V3 - OPTIMIZATION FOR >95% ACCURACY")
    print("=" * 80)
    print("🎯 Target: >95% accuracy")
    print("🔧 Hyperparameter optimization")
    print("⚡ Advanced feature engineering")
    print("📊 Robust validation")
    print("=" * 80)
    
    # Load optimized data
    df = load_optimized_data()
    if df is None or len(df) == 0:
        print("❌ No data available")
        return False
    
    # Create optimized features
    df = create_optimized_features(df)
    if len(df) == 0:
        print("❌ No data after feature engineering")
        return False
    
    # Train optimized models
    results = {}
    
    for system in ['system1', 'system2']:
        model, scaler, score, features, params = train_optimized_model(df, system)
        model_dir = save_optimized_model(model, scaler, score, features, params, system)
        
        results[system] = {
            'score': score,
            'model_dir': model_dir,
            'feature_count': len(features),
            'params': params
        }
    
    # Final summary
    print(f"\n🎉 ENHANCED MODEL V3 OPTIMIZATION COMPLETED!")
    print("=" * 80)
    
    total_score = 0
    target_met = 0
    
    for system, result in results.items():
        score = result['score']
        total_score += score
        
        print(f"\n📊 {system.upper()} OPTIMIZED RESULTS:")
        print(f"   Accuracy: {score:.4f} ({score*100:.1f}%)")
        print(f"   Features: {result['feature_count']}")
        
        if score >= 0.95:
            print(f"   ✅ TARGET ACHIEVED!")
            target_met += 1
        else:
            print(f"   ⚠️ CLOSE: {score*100:.1f}% < 95%")
    
    avg_score = total_score / len(results)
    print(f"\n🏆 FINAL OPTIMIZATION RESULTS:")
    print(f"   Average Accuracy: {avg_score:.4f} ({avg_score*100:.1f}%)")
    print(f"   Systems Meeting Target: {target_met}/{len(results)}")
    
    if avg_score >= 0.95:
        print(f"🎯 ✅ OPTIMIZATION SUCCESS! TARGET ACHIEVED!")
    elif avg_score >= 0.93:
        print(f"🎯 🔥 EXCELLENT PROGRESS! Very close to target!")
    else:
        print(f"🎯 ⚠️ NEEDS MORE OPTIMIZATION")
    
    return avg_score >= 0.95

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
