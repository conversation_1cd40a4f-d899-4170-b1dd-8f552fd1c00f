#!/usr/bin/env python3
"""
Enhanced Model Trainer
Trains improved solar prediction models using CAMS + Open-Meteo data with physics-based features
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import joblib
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.model_selection import train_test_split, cross_val_score, TimeSeriesSplit
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import r2_score, mean_absolute_error, mean_squared_error
import psycopg2

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class EnhancedModelTrainer:
    """Enhanced model trainer with physics-based features"""
    
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.feature_importance = {}
        self.performance_metrics = {}
        
        # Model configurations
        self.algorithms = {
            'random_forest': RandomForestRegressor(
                n_estimators=100,
                max_depth=15,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42,
                n_jobs=-1
            ),
            'gradient_boosting': GradientBoostingRegressor(
                n_estimators=100,
                max_depth=8,
                learning_rate=0.1,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42
            )
        }
    
    def load_training_data(self, days_back: int = 180) -> pd.DataFrame:
        """Load training data with enhanced features"""
        
        logger.info(f"Loading training data for last {days_back} days...")
        
        try:
            conn = psycopg2.connect(
                host='localhost',
                database='solar_prediction',
                user='postgres',
                password='postgres'
            )
            
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days_back)
            
            # Enhanced query with physics-based features
            query = """
            WITH hourly_data AS (
                -- CAMS radiation data
                SELECT 
                    DATE_TRUNC('hour', timestamp) as hour_timestamp,
                    AVG(ghi) as cams_ghi,
                    AVG(dni) as cams_dni,
                    AVG(dhi) as cams_dhi,
                    AVG(temperature) as cams_temperature,
                    AVG(cloud_cover) as cams_cloud_cover,
                    AVG(aod) as cams_aod
                FROM cams_radiation_data
                WHERE timestamp >= %s AND timestamp <= %s
                GROUP BY DATE_TRUNC('hour', timestamp)
            ),
            weather_data AS (
                -- Open-Meteo weather data
                SELECT 
                    DATE_TRUNC('hour', timestamp) as hour_timestamp,
                    AVG(shortwave_radiation) as om_ghi,
                    AVG(temperature_2m) as om_temperature,
                    AVG(relative_humidity_2m) as om_humidity,
                    AVG(cloud_cover) as om_cloud_cover
                FROM weather_data
                WHERE timestamp >= %s AND timestamp <= %s
                GROUP BY DATE_TRUNC('hour', timestamp)
            ),
            solar_data AS (
                -- Solar production data (target variable)
                SELECT 
                    DATE_TRUNC('hour', timestamp) as hour_timestamp,
                    AVG(ac_power) as avg_power,
                    MAX(yield_today) as max_yield_today,
                    AVG(soc) as avg_soc
                FROM solax_data
                WHERE timestamp >= %s AND timestamp <= %s
                AND ac_power >= 0
                GROUP BY DATE_TRUNC('hour', timestamp)
            )
            SELECT 
                COALESCE(h.hour_timestamp, w.hour_timestamp, s.hour_timestamp) as timestamp,
                
                -- Best available features (prefer CAMS, fallback to Open-Meteo)
                COALESCE(h.cams_ghi, w.om_ghi, 0) as ghi,
                COALESCE(h.cams_temperature, w.om_temperature, 20) as temperature,
                COALESCE(h.cams_cloud_cover, w.om_cloud_cover, 50) as cloud_cover,
                COALESCE(h.cams_dni, 0) as dni,
                COALESCE(h.cams_dhi, 0) as dhi,
                COALESCE(h.cams_aod, 0.15) as aod,
                COALESCE(w.om_humidity, 60) as humidity,
                
                -- Target variable
                s.avg_power as target_power,
                s.max_yield_today as target_yield,
                s.avg_soc as soc
                
            FROM hourly_data h
            FULL OUTER JOIN weather_data w ON h.hour_timestamp = w.hour_timestamp
            FULL OUTER JOIN solar_data s ON COALESCE(h.hour_timestamp, w.hour_timestamp) = s.hour_timestamp
            
            WHERE COALESCE(h.hour_timestamp, w.hour_timestamp, s.hour_timestamp) >= %s
            AND COALESCE(h.hour_timestamp, w.hour_timestamp, s.hour_timestamp) <= %s
            AND s.avg_power IS NOT NULL
            AND s.avg_power >= 0
            
            ORDER BY timestamp
            """
            
            # Execute query
            params = [start_date, end_date] * 4
            df = pd.read_sql(query, conn)
            conn.close()
            
            logger.info(f"Loaded {len(df)} training records")
            return df
            
        except Exception as e:
            logger.error(f"Error loading training data: {e}")
            return pd.DataFrame()
    
    def engineer_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Engineer physics-based features"""
        
        logger.info("Engineering physics-based features...")
        
        # Time-based features
        df['hour'] = df['timestamp'].dt.hour
        df['day_of_year'] = df['timestamp'].dt.dayofyear
        df['month'] = df['timestamp'].dt.month
        df['is_weekend'] = df['timestamp'].dt.weekday >= 5
        
        # Solar geometry (simplified)
        df['solar_declination'] = 23.45 * np.sin(np.radians(360 * (284 + df['day_of_year']) / 365))
        df['hour_angle'] = 15 * (df['hour'] - 12)
        
        # Solar elevation (simplified)
        lat_rad = np.radians(38.141348260997596)  # Marathon, Attica
        df['solar_elevation'] = np.degrees(np.arcsin(
            np.sin(lat_rad) * np.sin(np.radians(df['solar_declination'])) +
            np.cos(lat_rad) * np.cos(np.radians(df['solar_declination'])) * 
            np.cos(np.radians(df['hour_angle']))
        ))
        
        # Solar elevation factor (0 when sun below horizon)
        df['solar_elevation_factor'] = np.maximum(0, np.sin(np.radians(df['solar_elevation'])))
        
        # Physics-based features
        # Module temperature (NOCT model)
        NOCT = 45.0  # Nominal Operating Cell Temperature
        df['module_temperature'] = np.where(
            df['ghi'] > 0,
            df['temperature'] + (df['ghi'] / 800.0) * (NOCT - 20.0),
            df['temperature']
        )
        
        # Temperature efficiency factor
        temp_coeff = -0.004  # -0.4%/°C
        df['temp_efficiency_factor'] = np.maximum(
            0.5, 1 + temp_coeff * (df['module_temperature'] - 25.0)
        )
        
        # Clear sky estimation
        df['clear_sky_ghi'] = 900 * df['solar_elevation_factor'] * (1 - df['aod'])
        
        # Clearness index
        df['clearness_index'] = np.where(
            df['clear_sky_ghi'] > 0,
            np.minimum(1.2, df['ghi'] / df['clear_sky_ghi']),
            0
        )
        
        # Cyclical encoding for time features
        df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
        df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
        
        # Interaction features
        df['ghi_elevation_interaction'] = df['ghi'] * df['solar_elevation_factor']
        df['temp_elevation_interaction'] = df['temperature'] * df['solar_elevation_factor']
        df['cloud_radiation_factor'] = (100 - df['cloud_cover']) / 100 * df['ghi']
        
        # Weather quality score
        df['weather_quality_score'] = (
            (df['ghi'] / 1000) * 0.4 +
            df['clearness_index'] * 0.3 +
            (1 - df['aod']) * 0.2 +
            ((100 - df['cloud_cover']) / 100) * 0.1
        )
        
        # Rolling features (3-hour window)
        df['ghi_rolling_3h'] = df['ghi'].rolling(window=3, min_periods=1).mean()
        df['temp_rolling_3h'] = df['temperature'].rolling(window=3, min_periods=1).mean()
        
        # Lag features
        df['ghi_lag_1h'] = df['ghi'].shift(1)
        df['ghi_lag_3h'] = df['ghi'].shift(3)
        
        logger.info(f"Feature engineering completed: {len(df.columns)} features")
        return df
    
    def prepare_training_data(self, df: pd.DataFrame) -> tuple:
        """Prepare data for training"""
        
        logger.info("Preparing training data...")
        
        # Select features for training
        feature_columns = [
            'ghi', 'temperature', 'cloud_cover', 'dni', 'dhi', 'humidity',
            'solar_elevation_factor', 'module_temperature', 'temp_efficiency_factor',
            'clearness_index', 'hour_sin', 'hour_cos', 'month_sin', 'month_cos',
            'ghi_elevation_interaction', 'temp_elevation_interaction', 
            'cloud_radiation_factor', 'weather_quality_score',
            'ghi_rolling_3h', 'temp_rolling_3h', 'ghi_lag_1h', 'ghi_lag_3h'
        ]
        
        # Filter available features
        available_features = [col for col in feature_columns if col in df.columns]
        logger.info(f"Using {len(available_features)} features for training")
        
        # Prepare X and y
        X = df[available_features].copy()
        y = df['target_power'].copy()
        
        # Remove rows with missing target or too many missing features
        valid_mask = (
            y.notna() & 
            (X.isna().sum(axis=1) < len(available_features) * 0.5)  # Less than 50% missing
        )
        
        X = X[valid_mask]
        y = y[valid_mask]
        
        # Fill remaining missing values
        X = X.fillna(X.median())
        
        logger.info(f"Training data prepared: {len(X)} samples, {len(X.columns)} features")
        
        return X, y, available_features
    
    def train_enhanced_models(self, X: pd.DataFrame, y: pd.Series, features: list) -> dict:
        """Train enhanced models with cross-validation"""
        
        logger.info("Training enhanced models...")
        
        results = {}
        
        # Split data (temporal split to avoid data leakage)
        split_idx = int(len(X) * 0.8)
        X_train, X_test = X.iloc[:split_idx], X.iloc[split_idx:]
        y_train, y_test = y.iloc[:split_idx], y.iloc[split_idx:]
        
        for name, algorithm in self.algorithms.items():
            logger.info(f"Training {name}...")
            
            try:
                # Scale features
                scaler = StandardScaler()
                X_train_scaled = scaler.fit_transform(X_train)
                X_test_scaled = scaler.transform(X_test)
                
                # Train model
                model = algorithm.fit(X_train_scaled, y_train)
                
                # Predictions
                y_pred_train = model.predict(X_train_scaled)
                y_pred_test = model.predict(X_test_scaled)
                
                # Metrics
                train_r2 = r2_score(y_train, y_pred_train)
                test_r2 = r2_score(y_test, y_pred_test)
                test_mae = mean_absolute_error(y_test, y_pred_test)
                test_rmse = np.sqrt(mean_squared_error(y_test, y_pred_test))
                
                # Cross-validation
                cv_scores = cross_val_score(
                    algorithm, X_train_scaled, y_train, 
                    cv=TimeSeriesSplit(n_splits=3), 
                    scoring='r2'
                )
                
                # Feature importance
                if hasattr(model, 'feature_importances_'):
                    importance = dict(zip(features, model.feature_importances_))
                    top_features = sorted(importance.items(), key=lambda x: x[1], reverse=True)[:10]
                else:
                    top_features = []
                
                # Store results
                results[name] = {
                    'model': model,
                    'scaler': scaler,
                    'train_r2': train_r2,
                    'test_r2': test_r2,
                    'test_mae': test_mae,
                    'test_rmse': test_rmse,
                    'cv_mean': cv_scores.mean(),
                    'cv_std': cv_scores.std(),
                    'top_features': top_features,
                    'training_samples': len(X_train),
                    'test_samples': len(X_test)
                }
                
                logger.info(f"{name} - Test R²: {test_r2:.3f}, MAE: {test_mae:.1f}, CV: {cv_scores.mean():.3f}±{cv_scores.std():.3f}")
                
            except Exception as e:
                logger.error(f"Error training {name}: {e}")
                results[name] = {'error': str(e)}
        
        return results
    
    def save_best_model(self, results: dict, features: list) -> str:
        """Save the best performing model"""
        
        # Find best model based on test R²
        best_name = None
        best_r2 = -1
        
        for name, result in results.items():
            if 'test_r2' in result and result['test_r2'] > best_r2:
                best_r2 = result['test_r2']
                best_name = name
        
        if best_name is None:
            logger.error("No valid models to save")
            return ""
        
        logger.info(f"Best model: {best_name} with R² = {best_r2:.3f}")
        
        # Save model
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        model_dir = f"/home/<USER>/solar-prediction-project/models/enhanced_model_{timestamp}"
        os.makedirs(model_dir, exist_ok=True)
        
        best_result = results[best_name]
        
        # Save model and scaler
        joblib.dump(best_result['model'], f"{model_dir}/model.joblib")
        joblib.dump(best_result['scaler'], f"{model_dir}/scaler.joblib")
        
        # Save metadata
        metadata = {
            'model_type': best_name,
            'features': features,
            'performance': {
                'test_r2': best_result['test_r2'],
                'test_mae': best_result['test_mae'],
                'test_rmse': best_result['test_rmse'],
                'cv_mean': best_result['cv_mean'],
                'cv_std': best_result['cv_std']
            },
            'top_features': best_result['top_features'],
            'training_info': {
                'training_samples': best_result['training_samples'],
                'test_samples': best_result['test_samples'],
                'timestamp': timestamp
            }
        }
        
        import json
        with open(f"{model_dir}/metadata.json", 'w') as f:
            json.dump(metadata, f, indent=2, default=str)
        
        logger.info(f"Model saved to: {model_dir}")
        return model_dir


def main():
    """Main execution function"""
    
    print("🌟 Enhanced Model Trainer")
    print("=" * 35)
    print("Training improved solar prediction models with physics-based features")
    print()
    
    # Initialize trainer
    trainer = EnhancedModelTrainer()
    
    # Load training data
    print("📊 Loading training data...")
    df = trainer.load_training_data(days_back=180)
    
    if df.empty:
        print("❌ No training data loaded")
        return False
    
    print(f"✅ Loaded {len(df)} training records")
    
    # Engineer features
    print("🔧 Engineering features...")
    df = trainer.engineer_features(df)
    
    # Prepare training data
    print("📋 Preparing training data...")
    X, y, features = trainer.prepare_training_data(df)
    
    if len(X) == 0:
        print("❌ No valid training data after preparation")
        return False
    
    print(f"✅ Training data ready: {len(X)} samples, {len(features)} features")
    
    # Train models
    print("🤖 Training enhanced models...")
    results = trainer.train_enhanced_models(X, y, features)
    
    # Display results
    print("\n📊 Training Results:")
    print("=" * 25)
    
    for name, result in results.items():
        if 'error' in result:
            print(f"❌ {name}: {result['error']}")
        else:
            print(f"✅ {name}:")
            print(f"   Test R²: {result['test_r2']:.3f}")
            print(f"   Test MAE: {result['test_mae']:.1f} W")
            print(f"   CV Score: {result['cv_mean']:.3f} ± {result['cv_std']:.3f}")
    
    # Save best model
    print("\n💾 Saving best model...")
    model_path = trainer.save_best_model(results, features)
    
    if model_path:
        print(f"✅ Enhanced model saved to: {model_path}")
        print("\n🎯 Next steps:")
        print("   1. Test model with recent data")
        print("   2. Compare with baseline accuracy")
        print("   3. Deploy for production testing")
        return True
    else:
        print("❌ Failed to save model")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
