#!/usr/bin/env python3
"""
Demo Production Enhanced Training
=================================

Demo script που εκπαιδεύει 3 representative μοντέλα με enhanced techniques
για να δείξει τα αποτελέσματα γρήγορα.

Μοντέλα:
1. spring_system1 (seasonal)
2. multi_horizon_daily_system1 (multi-horizon)
3. summer_system2 (seasonal, different system)

Δημιουργήθηκε: 2025-06-05
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))

import pandas as pd
import numpy as np
import psycopg2
from pathlib import Path
from datetime import datetime, timedelta
import joblib
import json
from typing import Dict, List, Tuple, Any, Optional
import logging
import warnings
warnings.filterwarnings('ignore')

# Sklearn imports
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import GridSearchCV, train_test_split, TimeSeriesSplit
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.preprocessing import StandardScaler

# Import unified preprocessing pipeline
from src.preprocessing.unified_pipeline import create_unified_pipeline

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DemoProductionEnhancedTrainer:
    """
    Demo trainer για 3 representative enhanced μοντέλα
    """
    
    def __init__(self, output_dir: str = "models/demo_production_enhanced"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True, parents=True)
        
        # Initialize unified pipeline
        self.pipeline = create_unified_pipeline("v1.0.0")
        
        # Demo models (3 representative models)
        self.demo_models = {
            'spring_system1': {
                'type': 'seasonal',
                'system_id': 1,
                'season': 'spring',
                'months': [3, 4, 5],
                'target_r2': 0.92,
                'target_mae': 2.5,
                'features_type': 'seasonal'
            },
            'multi_horizon_daily_system1': {
                'type': 'multi_horizon',
                'system_id': 1,
                'horizon': 'daily',
                'target_r2': 0.88,
                'target_mae': 3.0,
                'features_type': 'multi_horizon'
            },
            'summer_system2': {
                'type': 'seasonal',
                'system_id': 2,
                'season': 'summer',
                'months': [6, 7, 8],
                'target_r2': 0.92,
                'target_mae': 2.5,
                'features_type': 'seasonal'
            }
        }
        
        logger.info(f"🏗️ Initialized DemoProductionEnhancedTrainer")
        logger.info(f"📊 Demo models: {list(self.demo_models.keys())}")
    
    def load_demo_data(self) -> pd.DataFrame:
        """Load demo data από database"""
        logger.info("📊 Loading demo data from database...")
        
        try:
            conn = psycopg2.connect(
                host='localhost',
                database='solar_prediction',
                user='postgres',
                password='postgres'
            )
            
            # Optimized query για demo (limited data)
            query = """
            WITH combined_data AS (
                -- System 1 data (limited)
                SELECT 
                    s.timestamp,
                    s.yield_today,
                    s.soc,
                    s.bat_power,
                    s.temperature,
                    w.global_horizontal_irradiance,
                    w.temperature_2m,
                    w.relative_humidity_2m,
                    w.cloud_cover,
                    1 as system_id,
                    EXTRACT(MONTH FROM s.timestamp) as month,
                    EXTRACT(HOUR FROM s.timestamp) as hour,
                    EXTRACT(DOY FROM s.timestamp) as day_of_year
                FROM solax_data s
                LEFT JOIN weather_data w ON DATE_TRUNC('hour', s.timestamp) = DATE_TRUNC('hour', w.timestamp)
                WHERE s.timestamp >= '2024-03-01' 
                  AND s.timestamp <= '2024-08-31'
                  AND s.yield_today IS NOT NULL
                  AND s.yield_today >= 0 
                  AND s.yield_today <= 100
                  AND w.global_horizontal_irradiance IS NOT NULL
                ORDER BY s.timestamp
                LIMIT 20000
                
                UNION ALL
                
                -- System 2 data (limited)
                SELECT 
                    s.timestamp,
                    s.yield_today,
                    s.soc,
                    s.bat_power,
                    s.temperature,
                    w.global_horizontal_irradiance,
                    w.temperature_2m,
                    w.relative_humidity_2m,
                    w.cloud_cover,
                    2 as system_id,
                    EXTRACT(MONTH FROM s.timestamp) as month,
                    EXTRACT(HOUR FROM s.timestamp) as hour,
                    EXTRACT(DOY FROM s.timestamp) as day_of_year
                FROM solax_data2 s
                LEFT JOIN weather_data w ON DATE_TRUNC('hour', s.timestamp) = DATE_TRUNC('hour', w.timestamp)
                WHERE s.timestamp >= '2024-06-01' 
                  AND s.timestamp <= '2024-08-31'
                  AND s.yield_today IS NOT NULL
                  AND s.yield_today >= 0 
                  AND s.yield_today <= 100
                  AND w.global_horizontal_irradiance IS NOT NULL
                ORDER BY s.timestamp
                LIMIT 10000
            )
            SELECT * FROM combined_data
            ORDER BY system_id, timestamp
            """
            
            df = pd.read_sql(query, conn)
            conn.close()
            
            # Data cleaning
            df = df.fillna(method='ffill').fillna(method='bfill').fillna(0)
            df = df.drop_duplicates(subset=['timestamp', 'system_id'])
            
            logger.info(f"✅ Loaded {len(df):,} demo records")
            logger.info(f"   System 1: {len(df[df['system_id']==1]):,} records")
            logger.info(f"   System 2: {len(df[df['system_id']==2]):,} records")
            
            return df
            
        except Exception as e:
            logger.error(f"❌ Failed to load demo data: {e}")
            raise
    
    def engineer_demo_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Demo feature engineering με proven techniques"""
        logger.info("🔧 Engineering demo features...")
        
        # Start με unified pipeline
        processed_df = self.pipeline.engineer_features(df)
        
        # Add proven advanced features
        processed_df = processed_df.sort_values(['system_id', 'timestamp']).reset_index(drop=True)
        
        # Group by system για proper calculations
        enhanced_dfs = []
        
        for system_id in [1, 2]:
            system_df = processed_df[processed_df['system_id'] == system_id].copy()
            
            if len(system_df) == 0:
                continue
            
            # Key lag features (proven effective από demo)
            for lag in [1, 12, 24]:  # 5min, 1h, 2h lags
                system_df[f'yield_lag_{lag}'] = system_df['yield_today'].shift(lag)
                system_df[f'ghi_lag_{lag}'] = system_df['global_horizontal_irradiance'].shift(lag)
            
            # Key rolling features
            for window in [12, 24]:  # 1h, 2h windows
                system_df[f'yield_rolling_mean_{window}'] = system_df['yield_today'].rolling(window).mean()
                system_df[f'temp_rolling_mean_{window}'] = system_df['temperature_2m'].rolling(window).mean()
            
            enhanced_dfs.append(system_df)
        
        # Combine systems
        if enhanced_dfs:
            enhanced_df = pd.concat(enhanced_dfs, ignore_index=True)
            enhanced_df = enhanced_df.sort_values(['system_id', 'timestamp']).reset_index(drop=True)
        else:
            enhanced_df = processed_df
        
        # PROVEN INTERACTION FEATURES (KEY SUCCESS FACTOR)
        enhanced_df['temp_ghi_interaction'] = enhanced_df['temperature_2m'] * enhanced_df['global_horizontal_irradiance'] / 1000
        enhanced_df['cloud_temp_interaction'] = enhanced_df['cloud_cover'] * enhanced_df['temperature_2m'] / 100
        enhanced_df['soc_power_interaction'] = enhanced_df['soc'] * enhanced_df['bat_power'] / 1000
        
        # Solar position
        enhanced_df['sun_elevation'] = np.sin(2 * np.pi * enhanced_df['hour'] / 24) * np.sin(2 * np.pi * enhanced_df['day_of_year'] / 365)
        
        # Fill missing values
        enhanced_df = enhanced_df.fillna(method='ffill').fillna(method='bfill').fillna(0)
        
        logger.info(f"✅ Demo feature engineering complete: {enhanced_df.shape[1]} features")
        
        return enhanced_df
    
    def train_demo_model(self, model_name: str, model_config: Dict, 
                        processed_data: pd.DataFrame) -> Dict[str, Any]:
        """Train single demo model με enhanced techniques"""
        
        logger.info(f"\n🎯 Training demo model: {model_name}")
        logger.info("=" * 60)
        
        # Filter data
        system_id = model_config['system_id']
        system_data = processed_data[processed_data['system_id'] == system_id].copy()
        
        if model_config['type'] == 'seasonal':
            model_data = system_data[system_data['month'].isin(model_config['months'])].copy()
        else:
            model_data = system_data.copy()
        
        if len(model_data) < 100:
            logger.error(f"❌ Insufficient data για {model_name}: {len(model_data)} records")
            return None
        
        # Feature selection
        if model_config['features_type'] == 'seasonal':
            base_features = ['hour_sin', 'hour_cos', 'temperature', 'cloud_cover', 'ghi', 'soc']
        else:
            base_features = ['soc', 'bat_power', 'temperature', 'ghi', 'air_temp', 'humidity', 'cloud_cover', 'hour']
        
        # Add proven advanced features
        advanced_features = [col for col in model_data.columns if any(x in col for x in 
            ['lag_', 'rolling_', 'interaction', 'sun_elevation'])]
        
        all_features = base_features + advanced_features
        available_features = [f for f in all_features if f in model_data.columns]
        
        logger.info(f"📊 Using {len(available_features)} features για {model_name}")
        
        # Prepare data
        X = model_data[available_features].values
        y = model_data['yield_today'].values
        
        # Train/test split
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, shuffle=False
        )
        
        # Scale data
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        # Quick hyperparameter optimization
        param_grid = {
            'n_estimators': [100, 200],
            'max_depth': [15, 20, None],
            'min_samples_split': [2, 5],
            'max_features': ['sqrt', None]
        }
        
        tscv = TimeSeriesSplit(n_splits=3)
        
        grid_search = GridSearchCV(
            estimator=RandomForestRegressor(random_state=42),
            param_grid=param_grid,
            cv=tscv,
            scoring='neg_mean_absolute_error',
            n_jobs=-1,
            verbose=0
        )
        
        logger.info("🔍 Optimizing hyperparameters...")
        grid_search.fit(X_train_scaled, y_train)
        
        optimized_model = grid_search.best_estimator_
        
        # Make predictions
        y_pred = optimized_model.predict(X_test_scaled)
        
        # Calculate metrics
        metrics = {
            'r2': r2_score(y_test, y_pred),
            'mae': mean_absolute_error(y_test, y_pred),
            'rmse': np.sqrt(mean_squared_error(y_test, y_pred))
        }
        
        # Feature importance
        feature_importance = dict(zip(available_features, optimized_model.feature_importances_))
        top_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)[:10]
        
        # Check targets
        target_achieved = (
            metrics['r2'] >= model_config['target_r2'] and
            metrics['mae'] <= model_config['target_mae']
        )
        
        result = {
            'model_name': model_name,
            'model_config': model_config,
            'model': optimized_model,
            'scaler': scaler,
            'features': available_features,
            'metrics': metrics,
            'feature_importance': feature_importance,
            'top_features': top_features,
            'target_achieved': target_achieved,
            'training_samples': len(X_train),
            'test_samples': len(X_test),
            'best_params': grid_search.best_params_
        }
        
        # Save model
        self.save_demo_model(result)
        
        # Log results
        logger.info(f"📊 DEMO MODEL RESULTS:")
        logger.info(f"   R²: {metrics['r2']:.4f}")
        logger.info(f"   MAE: {metrics['mae']:.3f}")
        logger.info(f"   RMSE: {metrics['rmse']:.3f}")
        logger.info(f"   Target achieved: {'✅' if target_achieved else '❌'}")
        logger.info(f"   Top feature: {top_features[0][0]} ({top_features[0][1]:.4f})")
        
        return result
    
    def save_demo_model(self, result: Dict[str, Any]):
        """Save demo model"""
        model_name = result['model_name']
        model_dir = self.output_dir / model_name
        model_dir.mkdir(exist_ok=True)
        
        # Save model και scaler
        joblib.dump(result['model'], model_dir / "model.joblib")
        joblib.dump(result['scaler'], model_dir / "scaler.joblib")
        
        # Save metadata
        metadata = {
            'model_name': model_name,
            'model_type': 'demo_production_enhanced',
            'model_config': result['model_config'],
            'features': result['features'],
            'performance': result['metrics'],
            'feature_importance': result['feature_importance'],
            'top_features': result['top_features'],
            'target_achieved': result['target_achieved'],
            'training_samples': result['training_samples'],
            'test_samples': result['test_samples'],
            'training_date': datetime.now().isoformat(),
            'best_params': result['best_params'],
            'enhanced_features': True
        }
        
        with open(model_dir / "metadata.json", 'w') as f:
            json.dump(metadata, f, indent=2, default=str)
        
        logger.info(f"💾 Saved demo model: {model_dir}")
    
    def run_demo_training(self) -> Dict[str, Any]:
        """Run complete demo training"""
        logger.info("🚀 STARTING DEMO PRODUCTION ENHANCED TRAINING")
        logger.info("=" * 80)
        logger.info(f"Demo models: {list(self.demo_models.keys())}")
        logger.info("=" * 80)
        
        # Load and prepare data
        raw_data = self.load_demo_data()
        processed_data = self.engineer_demo_features(raw_data)
        
        # Training results
        results = {
            'start_time': datetime.now().isoformat(),
            'total_models': len(self.demo_models),
            'successful_models': 0,
            'target_achieved': 0,
            'models': {}
        }
        
        # Train each demo model
        for model_name, model_config in self.demo_models.items():
            try:
                result = self.train_demo_model(model_name, model_config, processed_data)
                
                if result:
                    results['models'][model_name] = result
                    results['successful_models'] += 1
                    
                    if result['target_achieved']:
                        results['target_achieved'] += 1
                
            except Exception as e:
                logger.error(f"❌ Failed to train {model_name}: {e}")
                continue
        
        # Generate summary
        results['end_time'] = datetime.now().isoformat()
        self.generate_demo_summary(results)
        
        return results
    
    def generate_demo_summary(self, results: Dict[str, Any]):
        """Generate demo summary"""
        logger.info(f"\n🎉 DEMO PRODUCTION TRAINING COMPLETED!")
        logger.info("=" * 80)
        
        successful = results['successful_models']
        total = results['total_models']
        target_met = results['target_achieved']
        
        logger.info(f"📊 DEMO RESULTS:")
        logger.info(f"   Successful models: {successful}/{total} ({successful/total*100:.1f}%)")
        logger.info(f"   Target achieved: {target_met}/{successful} ({target_met/successful*100:.1f}%)")
        
        # Model details
        for model_name, result in results['models'].items():
            logger.info(f"\n🎯 {model_name.upper()}:")
            metrics = result['metrics']
            logger.info(f"   R²: {metrics['r2']:.4f}")
            logger.info(f"   MAE: {metrics['mae']:.3f}")
            logger.info(f"   Target: {'✅' if result['target_achieved'] else '❌'}")
            logger.info(f"   Top feature: {result['top_features'][0][0]} ({result['top_features'][0][1]:.4f})")
        
        # Save summary
        summary_path = self.output_dir / "demo_training_summary.json"
        with open(summary_path, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        logger.info(f"\n💾 Demo summary saved: {summary_path}")

def main():
    """Main demo function"""
    try:
        trainer = DemoProductionEnhancedTrainer()
        results = trainer.run_demo_training()
        
        successful = results['successful_models']
        total = results['total_models']
        target_met = results['target_achieved']
        
        print(f"\n🎉 DEMO PRODUCTION TRAINING RESULTS:")
        print(f"   Successful: {successful}/{total} models ({successful/total*100:.1f}%)")
        print(f"   Targets achieved: {target_met}/{successful} ({target_met/successful*100:.1f}%)")
        
        if target_met > 0:
            print(f"\n📈 ENHANCED FEATURES WORKING!")
            print(f"   Ready για full production training")
        
        return successful >= total * 0.8
        
    except Exception as e:
        logger.error(f"❌ Demo training failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
