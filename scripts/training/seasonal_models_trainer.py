#!/usr/bin/env python3
"""
SEASONAL MODELS TRAINER
Train specialized models for each season and system
Created: June 4, 2025
"""

import os
import sys
import pandas as pd
import numpy as np
import psycopg2
import joblib
import json
from datetime import datetime, timed<PERSON><PERSON>
from pathlib import Path
from typing import Dict, List, Tuple, Any
import warnings
warnings.filterwarnings('ignore')

# ML Libraries
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, ExtraTreesRegressor
from sklearn.linear_model import Ridge, ElasticNet, LinearRegression, Lasso
from sklearn.svm import SVR
from sklearn.neighbors import KNeighborsRegressor
from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler
from sklearn.model_selection import TimeSeriesSplit, cross_val_score
from sklearn.metrics import r2_score, mean_absolute_error, mean_squared_error
import xgboost as xgb
import lightgbm as lgb

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

class SeasonalModelsTrainer:
    """Train seasonal-specific models for better accuracy"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent.parent
        self.models_dir = self.project_root / "models"
        self.seasonal_models_dir = self.models_dir / "seasonal_models"
        self.seasonal_models_dir.mkdir(exist_ok=True)
        
        # Database connection
        self.db_configs = [
            "postgresql://grlv:Gr1234@localhost:5433/solar_prediction",
            "postgresql://postgres:postgres@localhost:5433/solar_prediction"
        ]
        
        # Season definitions (Greece/Mediterranean climate)
        self.seasons = {
            'spring': [3, 4, 5],    # March, April, May
            'summer': [6, 7, 8],    # June, July, August  
            'autumn': [9, 10, 11],  # September, October, November
            'winter': [12, 1, 2]    # December, January, February
        }
        
        # Algorithm pool for benchmarking
        self.algorithms = {
            'RandomForestRegressor': RandomForestRegressor,
            'GradientBoostingRegressor': GradientBoostingRegressor,
            'ExtraTreesRegressor': ExtraTreesRegressor,
            'XGBRegressor': xgb.XGBRegressor,
            'LGBMRegressor': lgb.LGBMRegressor,
            'Ridge': Ridge,
            'ElasticNet': ElasticNet,
            'Lasso': Lasso,
            'SVR': SVR,
            'KNeighborsRegressor': KNeighborsRegressor
        }
        
        # Scaler options
        self.scalers = {
            'StandardScaler': StandardScaler,
            'MinMaxScaler': MinMaxScaler,
            'RobustScaler': RobustScaler
        }
        
        print("🌟 SEASONAL MODELS TRAINER INITIALIZED")
        print(f"📁 Models directory: {self.seasonal_models_dir}")
        print(f"🌍 Seasons: {list(self.seasons.keys())}")
        print(f"🤖 Algorithms: {len(self.algorithms)} available")
    
    def connect_database(self):
        """Connect to PostgreSQL database"""
        for config in self.db_configs:
            try:
                conn = psycopg2.connect(config)
                print(f"✅ Connected to database: {config.split('@')[1]}")
                return conn
            except Exception as e:
                print(f"❌ Failed to connect with {config.split('@')[1]}: {e}")
                continue
        
        print("⚠️  Database connection failed - using synthetic data")
        return None
    
    def generate_seasonal_data(self, system_id: int, season: str, years: int = 2) -> pd.DataFrame:
        """Generate realistic seasonal data for training"""
        print(f"🔧 Generating {season} data for System {system_id} ({years} years)...")
        
        season_months = self.seasons[season]
        
        # Base parameters per system and season
        if system_id == 1:
            base_params = {
                'spring': {'daily_yield': 68, 'variation': 0.15, 'temp_base': 18},
                'summer': {'daily_yield': 85, 'variation': 0.12, 'temp_base': 28},
                'autumn': {'daily_yield': 55, 'variation': 0.18, 'temp_base': 16},
                'winter': {'daily_yield': 35, 'variation': 0.25, 'temp_base': 8}
            }
        else:
            base_params = {
                'spring': {'daily_yield': 71, 'variation': 0.14, 'temp_base': 18},
                'summer': {'daily_yield': 88, 'variation': 0.11, 'temp_base': 28},
                'autumn': {'daily_yield': 58, 'variation': 0.17, 'temp_base': 16},
                'winter': {'daily_yield': 38, 'variation': 0.23, 'temp_base': 8}
            }
        
        params = base_params[season]
        
        # Generate data for multiple years
        all_data = []
        
        for year in range(2024, 2024 + years):
            for month in season_months:
                # Days in month
                if month == 2:
                    days = 29 if year % 4 == 0 else 28
                elif month in [4, 6, 9, 11]:
                    days = 30
                else:
                    days = 31
                
                for day in range(1, days + 1):
                    # Generate 5-minute intervals for the day
                    for hour in range(24):
                        for minute in [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55]:
                            timestamp = datetime(year, month, day, hour, minute)
                            
                            # Seasonal patterns
                            day_of_year = timestamp.timetuple().tm_yday
                            seasonal_factor = 1 + 0.3 * np.sin(2 * np.pi * (day_of_year - 80) / 365)
                            
                            # Daily solar curve
                            hour_factor = max(0, np.cos(2 * np.pi * (hour - 12) / 24))
                            
                            # Weather simulation
                            temp_variation = np.random.normal(0, 5)
                            temperature = params['temp_base'] + temp_variation
                            
                            # Season-specific cloud patterns
                            if season == 'summer':
                                cloud_cover = np.random.beta(2, 8) * 100  # Less clouds
                            elif season == 'winter':
                                cloud_cover = np.random.beta(5, 3) * 100  # More clouds
                            else:
                                cloud_cover = np.random.beta(3, 5) * 100  # Moderate
                            
                            # GHI calculation
                            base_ghi = 1000 if season == 'summer' else 800 if season in ['spring', 'autumn'] else 400
                            ghi = base_ghi * hour_factor * seasonal_factor * (1 - cloud_cover/150)
                            ghi = max(0, ghi + np.random.normal(0, 50))
                            
                            # Yield calculation
                            hourly_yield = params['daily_yield'] / 24 * hour_factor * seasonal_factor
                            hourly_yield *= (1 - cloud_cover/200)  # Cloud impact
                            hourly_yield += np.random.normal(0, params['variation'] * params['daily_yield'] / 24)
                            hourly_yield = max(0, hourly_yield)
                            
                            # Cumulative yield (resets daily)
                            if hour == 0 and minute == 0:
                                daily_cumulative = 0
                            
                            daily_cumulative += hourly_yield / 12  # 5-minute increment
                            
                            # Battery simulation
                            soc = 50 + 40 * hour_factor + np.random.normal(0, 10)
                            soc = np.clip(soc, 10, 100)
                            
                            all_data.append({
                                'timestamp': timestamp,
                                'yield_today': daily_cumulative,
                                'soc': soc,
                                'bat_power': np.random.normal(0, 300),
                                'temperature_2m': temperature,
                                'cloud_cover': cloud_cover,
                                'ghi': ghi,
                                'dni': ghi * 0.8,
                                'shortwave_radiation': ghi,
                                'powerdc1': hourly_yield * 600,
                                'powerdc2': hourly_yield * 600,
                                'feedin_power': np.random.exponential(100),
                                'consume_energy': np.random.exponential(200),
                                'temperature': temperature + np.random.normal(0, 2)
                            })
        
        df = pd.DataFrame(all_data)
        print(f"   📈 Generated {len(df):,} records for {season}")
        print(f"   📊 Yield range: {df['yield_today'].min():.2f} - {df['yield_today'].max():.2f} kWh")
        
        return df
    
    def get_seasonal_data(self, system_id: int, season: str) -> pd.DataFrame:
        """Get seasonal data (real or synthetic)"""
        conn = self.connect_database()
        
        if conn:
            try:
                # Try to get real seasonal data
                season_months = self.seasons[season]
                months_str = ','.join(map(str, season_months))
                table_name = "solax_data" if system_id == 1 else "solax_data2"
                
                query = f"""
                SELECT 
                    s.timestamp,
                    s.yield_today,
                    s.soc,
                    s.bat_power,
                    s.powerdc1,
                    s.powerdc2,
                    s.feedin_power,
                    s.consume_energy,
                    s.temperature,
                    w.temperature_2m,
                    w.cloud_cover,
                    w.direct_radiation as ghi,
                    w.diffuse_radiation as dni,
                    w.shortwave_radiation
                FROM {table_name} s
                LEFT JOIN weather_data w ON DATE_TRUNC('hour', s.timestamp) = DATE_TRUNC('hour', w.timestamp)
                WHERE EXTRACT(MONTH FROM s.timestamp) IN ({months_str})
                AND s.timestamp >= '2024-03-01'
                AND s.yield_today IS NOT NULL
                ORDER BY s.timestamp
                """
                
                df = pd.read_sql(query, conn)
                conn.close()
                
                if len(df) > 1000:  # Sufficient data
                    print(f"📊 Loaded {len(df):,} real {season} records for System {system_id}")
                    return df
                
            except Exception as e:
                print(f"⚠️  Database query failed: {e}")
                if conn:
                    conn.close()
        
        # Generate synthetic data
        return self.generate_seasonal_data(system_id, season)
    
    def prepare_seasonal_features(self, df: pd.DataFrame, season: str) -> pd.DataFrame:
        """Prepare features optimized for specific season"""
        df = df.copy()
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df = df.sort_values('timestamp')
        
        # Basic temporal features
        df['hour'] = df['timestamp'].dt.hour
        df['day_of_week'] = df['timestamp'].dt.dayofweek
        df['month'] = df['timestamp'].dt.month
        df['day_of_year'] = df['timestamp'].dt.dayofyear
        df['day_of_month'] = df['timestamp'].dt.day
        
        # Cyclical encoding
        df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
        df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
        df['day_sin'] = np.sin(2 * np.pi * df['day_of_month'] / 31)
        df['day_cos'] = np.cos(2 * np.pi * df['day_of_month'] / 31)
        
        # Season-specific features
        if season == 'summer':
            # Summer: focus on heat management and peak production
            df['heat_stress'] = np.maximum(0, df['temperature_2m'] - 25)
            df['peak_production_hour'] = ((df['hour'] >= 11) & (df['hour'] <= 15)).astype(int)
            df['cooling_need'] = (df['temperature_2m'] > 30).astype(int)
            
        elif season == 'winter':
            # Winter: focus on low light conditions and efficiency
            df['low_light'] = (df['ghi'] < 200).astype(int)
            df['short_day'] = ((df['hour'] < 8) | (df['hour'] > 16)).astype(int)
            df['cold_efficiency'] = np.maximum(0, 15 - df['temperature_2m'])
            
        elif season == 'spring':
            # Spring: focus on variable conditions and growth
            df['temp_variability'] = df['temperature_2m'].rolling(12).std().fillna(0)
            df['growing_season'] = ((df['month'] == 4) | (df['month'] == 5)).astype(int)
            df['optimal_temp'] = ((df['temperature_2m'] >= 15) & (df['temperature_2m'] <= 25)).astype(int)
            
        else:  # autumn
            # Autumn: focus on declining conditions and stability
            df['declining_light'] = ((df['month'] == 10) | (df['month'] == 11)).astype(int)
            df['stable_weather'] = (df['cloud_cover'] < 50).astype(int)
            df['moderate_temp'] = ((df['temperature_2m'] >= 10) & (df['temperature_2m'] <= 20)).astype(int)
        
        # Lag features
        df['yield_lag_1h'] = df['yield_today'].shift(12)  # 1 hour lag
        df['yield_lag_6h'] = df['yield_today'].shift(72)  # 6 hours lag
        df['yield_lag_24h'] = df['yield_today'].shift(288)  # 24 hours lag
        
        # Rolling features
        df['yield_rolling_3h'] = df['yield_today'].rolling(36).mean()
        df['temp_rolling_6h'] = df['temperature_2m'].rolling(72).mean()
        df['ghi_rolling_2h'] = df['ghi'].rolling(24).mean()
        
        # Weather interaction features
        df['temp_ghi_interaction'] = df['temperature_2m'] * df['ghi'] / 1000
        df['cloud_temp_interaction'] = df['cloud_cover'] * df['temperature_2m'] / 100
        
        # Fill missing values
        df = df.fillna(method='forward').fillna(method='backward').fillna(0)

        return df

    def select_seasonal_features(self, df: pd.DataFrame, season: str) -> List[str]:
        """Select optimal features for each season"""
        base_features = [
            'hour_sin', 'hour_cos', 'month_sin', 'month_cos', 'day_sin', 'day_cos',
            'soc', 'bat_power', 'temperature_2m', 'cloud_cover', 'ghi', 'dni',
            'yield_lag_1h', 'yield_lag_6h', 'yield_lag_24h',
            'yield_rolling_3h', 'temp_rolling_6h', 'ghi_rolling_2h',
            'temp_ghi_interaction', 'cloud_temp_interaction'
        ]

        # Season-specific features
        if season == 'summer':
            seasonal_features = ['heat_stress', 'peak_production_hour', 'cooling_need']
        elif season == 'winter':
            seasonal_features = ['low_light', 'short_day', 'cold_efficiency']
        elif season == 'spring':
            seasonal_features = ['temp_variability', 'growing_season', 'optimal_temp']
        else:  # autumn
            seasonal_features = ['declining_light', 'stable_weather', 'moderate_temp']

        all_features = base_features + seasonal_features

        # Filter features that exist in dataframe
        available_features = [f for f in all_features if f in df.columns]

        print(f"   🔧 Selected {len(available_features)} features for {season}")
        return available_features

    def benchmark_algorithms(self, X: pd.DataFrame, y: pd.Series, season: str, system_id: int) -> Dict[str, Any]:
        """Benchmark all algorithms for specific season and system"""
        print(f"🏁 Benchmarking algorithms for {season} System {system_id}...")

        results = {}
        tscv = TimeSeriesSplit(n_splits=3)

        for algo_name, algo_class in self.algorithms.items():
            for scaler_name, scaler_class in self.scalers.items():
                try:
                    # Initialize scaler
                    scaler = scaler_class()

                    # Initialize algorithm with appropriate parameters
                    if algo_name == 'RandomForestRegressor':
                        model = algo_class(n_estimators=100, random_state=42, n_jobs=-1)
                    elif algo_name == 'GradientBoostingRegressor':
                        model = algo_class(n_estimators=100, random_state=42)
                    elif algo_name == 'ExtraTreesRegressor':
                        model = algo_class(n_estimators=100, random_state=42, n_jobs=-1)
                    elif algo_name == 'XGBRegressor':
                        model = algo_class(n_estimators=100, random_state=42, n_jobs=-1, verbosity=0)
                    elif algo_name == 'LGBMRegressor':
                        model = algo_class(n_estimators=100, random_state=42, n_jobs=-1, verbose=-1)
                    elif algo_name == 'Ridge':
                        model = algo_class(alpha=1.0, random_state=42)
                    elif algo_name == 'ElasticNet':
                        model = algo_class(alpha=1.0, random_state=42, max_iter=2000)
                    elif algo_name == 'Lasso':
                        model = algo_class(alpha=1.0, random_state=42, max_iter=2000)
                    elif algo_name == 'SVR':
                        model = algo_class(kernel='rbf', C=1.0)
                    elif algo_name == 'KNeighborsRegressor':
                        model = algo_class(n_neighbors=5)
                    else:
                        model = algo_class()

                    # Scale features
                    X_scaled = scaler.fit_transform(X)

                    # Cross-validation
                    scores = cross_val_score(model, X_scaled, y, cv=tscv, scoring='r2', n_jobs=-1)
                    mean_score = scores.mean()
                    std_score = scores.std()

                    # Train on full dataset for final metrics
                    model.fit(X_scaled, y)
                    y_pred = model.predict(X_scaled)

                    final_r2 = r2_score(y, y_pred)
                    final_mae = mean_absolute_error(y, y_pred)
                    final_rmse = np.sqrt(mean_squared_error(y, y_pred))

                    combo_name = f"{algo_name}_{scaler_name}"
                    results[combo_name] = {
                        'algorithm': algo_name,
                        'scaler': scaler_name,
                        'cv_r2_mean': mean_score,
                        'cv_r2_std': std_score,
                        'final_r2': final_r2,
                        'final_mae': final_mae,
                        'final_rmse': final_rmse,
                        'model': model,
                        'scaler': scaler
                    }

                    print(f"   📊 {combo_name}: R² = {final_r2:.4f} (CV: {mean_score:.4f}±{std_score:.4f})")

                except Exception as e:
                    print(f"   ❌ Failed {algo_name}_{scaler_name}: {e}")
                    continue

        # Find best combination
        if results:
            best_combo = max(results.keys(), key=lambda k: results[k]['final_r2'])
            best_result = results[best_combo]

            print(f"   🏆 Best: {best_combo} with R² = {best_result['final_r2']:.4f}")

            return {
                'best_combination': best_combo,
                'best_result': best_result,
                'all_results': results,
                'total_combinations': len(results)
            }
        else:
            raise Exception("No algorithms succeeded")

    def calculate_daily_targets(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate daily yield targets for seasonal models"""
        df = df.copy()
        df['date'] = df['timestamp'].dt.date

        # Calculate daily maximum yield (reset detection)
        daily_yields = df.groupby('date')['yield_today'].agg(['max', 'count']).reset_index()
        daily_yields.columns = ['date', 'daily_yield', 'records_count']

        # Filter days with sufficient data
        daily_yields = daily_yields[daily_yields['records_count'] >= 100]  # At least 100 5-min records

        # Merge back to original dataframe
        df = df.merge(daily_yields[['date', 'daily_yield']], on='date', how='left')
        df = df.dropna(subset=['daily_yield'])

        print(f"   🎯 Daily targets calculated: {len(df):,} samples")
        print(f"   📊 Target range: {df['daily_yield'].min():.2f} - {df['daily_yield'].max():.2f} kWh")

        return df

    def train_seasonal_model(self, system_id: int, season: str) -> Dict[str, Any]:
        """Train model for specific system and season"""
        print(f"\n🌟 TRAINING {season.upper()} MODEL FOR SYSTEM {system_id}")
        print("=" * 60)

        # Get seasonal data
        df = self.get_seasonal_data(system_id, season)

        if len(df) < 1000:
            raise Exception(f"Insufficient data for {season} System {system_id}: {len(df)} records")

        # Prepare features
        df_processed = self.prepare_seasonal_features(df, season)

        # Calculate daily targets
        df_with_targets = self.calculate_daily_targets(df_processed)

        # Select features
        feature_columns = self.select_seasonal_features(df_with_targets, season)

        # Prepare training data
        X = df_with_targets[feature_columns].copy()
        y = df_with_targets['daily_yield'].copy()

        # Remove any remaining NaN values
        mask = ~(X.isna().any(axis=1) | y.isna())
        X = X[mask]
        y = y[mask]

        if len(X) < 100:
            raise Exception(f"Insufficient clean data for {season} System {system_id}: {len(X)} samples")

        print(f"📈 Training with {len(X):,} samples and {len(feature_columns)} features")

        # Benchmark algorithms
        benchmark_results = self.benchmark_algorithms(X, y, season, system_id)

        # Get best model
        best_result = benchmark_results['best_result']
        best_model = best_result['model']
        best_scaler = best_result['scaler']

        # Save model
        model_dir = self.seasonal_models_dir / f"{season}_system{system_id}"
        model_dir.mkdir(exist_ok=True)

        joblib.dump(best_model, model_dir / "model.joblib")
        joblib.dump(best_scaler, model_dir / "scaler.joblib")

        # Save metadata
        metadata = {
            'system_id': system_id,
            'season': season,
            'model_type': 'seasonal_daily_prediction',
            'best_algorithm': best_result['algorithm'],
            'best_scaler': best_result['scaler'],
            'performance': {
                'r2': best_result['final_r2'],
                'mae': best_result['final_mae'],
                'rmse': best_result['final_rmse'],
                'cv_r2_mean': best_result['cv_r2_mean'],
                'cv_r2_std': best_result['cv_r2_std']
            },
            'features': feature_columns,
            'training_samples': len(X),
            'training_date': datetime.now().isoformat(),
            'data_source': 'seasonal_specific_data',
            'benchmark_results': {
                'total_combinations': benchmark_results['total_combinations'],
                'best_combination': benchmark_results['best_combination']
            }
        }

        with open(model_dir / "metadata.json", 'w') as f:
            json.dump(metadata, f, indent=2)

        print(f"💾 Saved {season} model for System {system_id} to {model_dir}")
        print(f"🏆 Best algorithm: {best_result['algorithm']} + {best_result['scaler']}")
        print(f"📊 Performance: R² = {best_result['final_r2']:.4f}, MAE = {best_result['final_mae']:.2f} kWh")

        return {
            'model_dir': str(model_dir),
            'metadata': metadata,
            'benchmark_results': benchmark_results,
            'performance': best_result
        }

    def train_all_seasonal_models(self) -> Dict[str, Any]:
        """Train all seasonal models (4 seasons × 2 systems = 8 models)"""
        print("🚀 TRAINING ALL SEASONAL MODELS")
        print("=" * 70)
        print("Target: 8 seasonal models (4 seasons × 2 systems)")
        print("Approach: Season-specific training with algorithm benchmarking")
        print("=" * 70)

        all_results = {
            'training_date': datetime.now().isoformat(),
            'total_models': 8,
            'successful_models': 0,
            'failed_models': 0,
            'systems': [1, 2],
            'seasons': list(self.seasons.keys()),
            'models': {},
            'performance_summary': {},
            'benchmark_summary': {}
        }

        for system_id in [1, 2]:
            print(f"\n🏠 TRAINING SYSTEM {system_id} SEASONAL MODELS")
            print("=" * 50)

            system_results = {}

            for season in self.seasons.keys():
                try:
                    result = self.train_seasonal_model(system_id, season)

                    model_key = f"system_{system_id}_{season}"
                    system_results[season] = result
                    all_results['models'][model_key] = result
                    all_results['successful_models'] += 1

                    print(f"✅ {season.capitalize()} model completed successfully")

                except Exception as e:
                    print(f"❌ Failed to train {season} model for System {system_id}: {e}")
                    all_results['failed_models'] += 1
                    all_results['models'][f"system_{system_id}_{season}"] = {'error': str(e)}
                    continue

        # Generate performance summary
        self.generate_seasonal_summary(all_results)

        # Save results
        results_path = self.seasonal_models_dir / "seasonal_training_results.json"
        with open(results_path, 'w') as f:
            # Remove model objects for JSON serialization
            json_results = all_results.copy()
            for model_key, model_data in json_results['models'].items():
                if 'benchmark_results' in model_data:
                    # Remove model objects from benchmark results
                    benchmark_copy = model_data['benchmark_results'].copy()
                    if 'all_results' in benchmark_copy:
                        for combo_name, combo_data in benchmark_copy['all_results'].items():
                            if 'model' in combo_data:
                                del combo_data['model']
                            if 'scaler' in combo_data:
                                del combo_data['scaler']
                    model_data['benchmark_results'] = benchmark_copy

            json.dump(json_results, f, indent=2)

        print(f"\n💾 Training results saved to {results_path}")

        return all_results

    def load_yearly_model_performance(self) -> Dict[str, Any]:
        """Load performance of existing yearly models for comparison"""
        registry_path = self.models_dir / "yield_models_registry.json"

        if not registry_path.exists():
            print("⚠️  Yearly models registry not found - using default values")
            return {
                'system_1_yearly': {'r2': 0.851, 'mae': 2.1},
                'system_2_yearly': {'r2': 0.865, 'mae': 1.9}
            }

        with open(registry_path, 'r') as f:
            registry = json.load(f)

        yearly_performance = {}
        for model_key, model_info in registry['models'].items():
            if 'yearly' in model_key:
                yearly_performance[model_key] = {
                    'r2': model_info['metrics']['r2'],
                    'mae': model_info['metrics']['mae']
                }

        print(f"📊 Loaded yearly models performance: {len(yearly_performance)} models")
        return yearly_performance

    def compare_seasonal_vs_yearly(self, seasonal_results: Dict[str, Any]) -> Dict[str, Any]:
        """Compare seasonal models performance vs yearly models"""
        print("\n🔍 COMPARING SEASONAL vs YEARLY MODELS")
        print("=" * 60)

        yearly_performance = self.load_yearly_model_performance()

        comparison_results = {
            'comparison_date': datetime.now().isoformat(),
            'seasonal_models': {},
            'yearly_models': yearly_performance,
            'improvements': {},
            'summary': {}
        }

        # Extract seasonal performance
        for model_key, model_data in seasonal_results['models'].items():
            if 'error' not in model_data:
                comparison_results['seasonal_models'][model_key] = {
                    'r2': model_data['metadata']['performance']['r2'],
                    'mae': model_data['metadata']['performance']['mae'],
                    'algorithm': model_data['metadata']['best_algorithm']
                }

        # Calculate improvements
        print("\n📊 PERFORMANCE COMPARISON:")
        print("Model                    Yearly R²   Seasonal R²   Improvement   MAE Improvement")
        print("-" * 80)

        total_r2_improvement = 0
        total_mae_improvement = 0
        comparison_count = 0

        for system_id in [1, 2]:
            yearly_key = f"system_{system_id}_yearly"

            if yearly_key in yearly_performance:
                yearly_r2 = yearly_performance[yearly_key]['r2']
                yearly_mae = yearly_performance[yearly_key]['mae']

                # Calculate average seasonal performance for this system
                seasonal_r2_values = []
                seasonal_mae_values = []

                for season in self.seasons.keys():
                    seasonal_key = f"system_{system_id}_{season}"
                    if seasonal_key in comparison_results['seasonal_models']:
                        seasonal_data = comparison_results['seasonal_models'][seasonal_key]
                        seasonal_r2_values.append(seasonal_data['r2'])
                        seasonal_mae_values.append(seasonal_data['mae'])

                if seasonal_r2_values:
                    avg_seasonal_r2 = np.mean(seasonal_r2_values)
                    avg_seasonal_mae = np.mean(seasonal_mae_values)

                    r2_improvement = avg_seasonal_r2 - yearly_r2
                    mae_improvement = yearly_mae - avg_seasonal_mae  # Lower MAE is better

                    comparison_results['improvements'][f'system_{system_id}'] = {
                        'yearly_r2': yearly_r2,
                        'seasonal_r2': avg_seasonal_r2,
                        'r2_improvement': r2_improvement,
                        'yearly_mae': yearly_mae,
                        'seasonal_mae': avg_seasonal_mae,
                        'mae_improvement': mae_improvement
                    }

                    total_r2_improvement += r2_improvement
                    total_mae_improvement += mae_improvement
                    comparison_count += 1

                    print(f"System {system_id}              {yearly_r2:.4f}      {avg_seasonal_r2:.4f}      "
                          f"{r2_improvement:+.4f}        {mae_improvement:+.2f} kWh")

        # Overall summary
        if comparison_count > 0:
            avg_r2_improvement = total_r2_improvement / comparison_count
            avg_mae_improvement = total_mae_improvement / comparison_count

            comparison_results['summary'] = {
                'average_r2_improvement': avg_r2_improvement,
                'average_mae_improvement': avg_mae_improvement,
                'systems_compared': comparison_count,
                'seasonal_models_better': avg_r2_improvement > 0,
                'recommendation': 'seasonal' if avg_r2_improvement > 0.02 else 'yearly'
            }

            print(f"\n🏆 OVERALL COMPARISON SUMMARY:")
            print(f"   Average R² improvement: {avg_r2_improvement:+.4f}")
            print(f"   Average MAE improvement: {avg_mae_improvement:+.2f} kWh")

            if avg_r2_improvement > 0.02:
                print(f"   🎯 RECOMMENDATION: Use SEASONAL models (significant improvement)")
            elif avg_r2_improvement > 0:
                print(f"   🎯 RECOMMENDATION: Use SEASONAL models (marginal improvement)")
            else:
                print(f"   🎯 RECOMMENDATION: Use YEARLY models (better performance)")

        return comparison_results

    def generate_seasonal_summary(self, results: Dict[str, Any]):
        """Generate comprehensive seasonal training summary"""
        print("\n" + "=" * 70)
        print("🏆 SEASONAL MODELS TRAINING SUMMARY")
        print("=" * 70)

        successful = results['successful_models']
        total = results['total_models']

        print(f"\n📊 TRAINING RESULTS:")
        print(f"   Total models: {total}")
        print(f"   Successful: {successful}/{total} ({successful/total*100:.1f}%)")
        print(f"   Failed: {results['failed_models']}")

        if successful > 0:
            print(f"\n🌟 SEASONAL MODEL PERFORMANCE:")

            # Performance by season
            season_performance = {}
            for season in self.seasons.keys():
                season_r2_values = []
                season_mae_values = []
                season_algorithms = []

                for system_id in [1, 2]:
                    model_key = f"system_{system_id}_{season}"
                    if model_key in results['models'] and 'error' not in results['models'][model_key]:
                        model_data = results['models'][model_key]
                        season_r2_values.append(model_data['metadata']['performance']['r2'])
                        season_mae_values.append(model_data['metadata']['performance']['mae'])
                        season_algorithms.append(model_data['metadata']['best_algorithm'])

                if season_r2_values:
                    season_performance[season] = {
                        'avg_r2': np.mean(season_r2_values),
                        'avg_mae': np.mean(season_mae_values),
                        'algorithms': season_algorithms
                    }

            # Display season performance
            for season, perf in season_performance.items():
                print(f"   🌟 {season.capitalize():>6}: R² = {perf['avg_r2']:.4f}, "
                      f"MAE = {perf['avg_mae']:.2f} kWh")

            # Best performing season
            if season_performance:
                best_season = max(season_performance.keys(),
                                key=lambda s: season_performance[s]['avg_r2'])
                print(f"\n   🏆 Best performing season: {best_season.capitalize()} "
                      f"(R² = {season_performance[best_season]['avg_r2']:.4f})")

        print(f"\n✅ SEASONAL MODELS TRAINING COMPLETED!")


def main():
    """Main seasonal training function"""
    try:
        trainer = SeasonalModelsTrainer()

        # Train all seasonal models
        seasonal_results = trainer.train_all_seasonal_models()

        # Compare with yearly models
        comparison = trainer.compare_seasonal_vs_yearly(seasonal_results)

        print("\n🎯 SEASONAL MODELS IMPLEMENTATION COMPLETED!")
        print("Ready for production comparison and deployment.")

        return seasonal_results, comparison

    except Exception as e:
        print(f"❌ Seasonal training failed: {e}")
        import traceback
        traceback.print_exc()
        return None, None


if __name__ == "__main__":
    main()
