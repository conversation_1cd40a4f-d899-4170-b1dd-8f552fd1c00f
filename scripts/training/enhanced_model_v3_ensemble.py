#!/usr/bin/env python3
"""
Enhanced Model v3 - Ensemble & Production Ready
Phase 4: Create production-ready ensemble model
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import pandas as pd
import numpy as np
import psycopg2
from dotenv import load_dotenv
import logging
from datetime import datetime
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestRegressor, VotingRegressor
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import xgboost as xgb
import joblib
from pathlib import Path
import json

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnsembleProductionModel:
    """Production-ready ensemble model for Enhanced Model v3"""
    
    def __init__(self):
        self.output_dir = Path("models/enhanced_v3_improved")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def load_optimized_dataset(self):
        """Load optimized dataset based on baseline success"""
        logger.info("🔍 Loading optimized dataset...")
        
        try:
            load_dotenv()
            conn = psycopg2.connect(
                host=os.getenv('DB_HOST', 'localhost'),
                database=os.getenv('DB_NAME', 'solar_prediction'),
                user=os.getenv('DB_USER', 'postgres'),
                password=os.getenv('DB_PASSWORD', 'postgres')
            )
            
            # Optimized query based on baseline success
            query = """
            WITH system1_data AS (
                SELECT
                    timestamp, ac_power, soc, bat_power, powerdc1, powerdc2, 
                    yield_today, feedin_power, consume_energy,
                    1 as system_id
                FROM solax_data
                WHERE timestamp >= '2024-03-01'
                AND ac_power IS NOT NULL AND soc IS NOT NULL AND bat_power IS NOT NULL
            ),
            system2_data AS (
                SELECT
                    timestamp, ac_power, soc, bat_power, powerdc1, powerdc2, 
                    yield_today, feedin_power, consume_energy,
                    2 as system_id
                FROM solax_data2
                WHERE timestamp >= '2024-03-01'
                AND ac_power IS NOT NULL AND soc IS NOT NULL AND bat_power IS NOT NULL
            ),
            combined_systems AS (
                SELECT * FROM system1_data
                UNION ALL
                SELECT * FROM system2_data
            ),
            weather_data AS (
                SELECT
                    DATE_TRUNC('hour', timestamp) as hour_timestamp,
                    AVG(COALESCE(ghi, 400)) as ghi,
                    AVG(COALESCE(temperature, 20)) as temperature,
                    AVG(COALESCE(cloud_cover, 50)) as cloud_cover
                FROM cams_radiation_data
                WHERE timestamp >= '2024-03-01'
                GROUP BY DATE_TRUNC('hour', timestamp)
            )
            SELECT
                cs.*,
                COALESCE(w.ghi, 400) as ghi,
                COALESCE(w.temperature, 20) as temperature,
                COALESCE(w.cloud_cover, 50) as cloud_cover
            FROM combined_systems cs
            LEFT JOIN weather_data w ON DATE_TRUNC('hour', cs.timestamp) = w.hour_timestamp
            ORDER BY cs.timestamp, cs.system_id
            """
            
            df = pd.read_sql(query, conn)
            conn.close()
            
            initial_count = len(df)
            logger.info(f"✅ Initial data loaded: {initial_count:,} records")
            
            # Gentle filtering
            df = df[
                (df['ac_power'] >= 0) & (df['ac_power'] <= 15000) &
                (df['soc'] >= 0) & (df['soc'] <= 100) &
                (df['bat_power'] >= -8000) & (df['bat_power'] <= 8000)
            ]
            
            final_count = len(df)
            retention_rate = (final_count / initial_count) * 100
            
            logger.info(f"✅ Optimized dataset loaded:")
            logger.info(f"   Final records: {final_count:,}")
            logger.info(f"   Retention rate: {retention_rate:.1f}%")
            logger.info(f"   System 1: {len(df[df['system_id']==1]):,} records")
            logger.info(f"   System 2: {len(df[df['system_id']==2]):,} records")
            
            return df
            
        except Exception as e:
            logger.error(f"❌ Optimized data loading failed: {e}")
            raise
    
    def create_production_features(self, df):
        """Create production-ready features"""
        logger.info("🔧 Creating production-ready features...")
        
        # Sort by system and timestamp
        df = df.sort_values(['system_id', 'timestamp']).reset_index(drop=True)
        
        # 1. CORE TEMPORAL FEATURES
        df['hour'] = df['timestamp'].dt.hour
        df['month'] = df['timestamp'].dt.month
        df['day_of_year'] = df['timestamp'].dt.dayofyear
        df['day_of_week'] = df['timestamp'].dt.dayofweek
        
        # Cyclical encoding
        df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
        df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
        df['day_of_year_sin'] = np.sin(2 * np.pi * df['day_of_year'] / 365)
        df['day_of_year_cos'] = np.cos(2 * np.pi * df['day_of_year'] / 365)
        
        # Time indicators
        df['is_weekend'] = df['day_of_week'].isin([5, 6]).astype(int)
        df['is_daylight'] = df['hour'].between(6, 20).astype(int)
        df['is_peak_solar'] = df['hour'].between(10, 16).astype(int)
        df['is_evening'] = df['hour'].between(18, 22).astype(int)
        df['is_night'] = df['hour'].isin([22, 23, 0, 1, 2, 3, 4, 5]).astype(int)
        
        # 2. BATTERY-AWARE FEATURES
        df['soc_normalized'] = df['soc'] / 100
        df['battery_mode'] = np.sign(df['bat_power'])
        df['battery_utilization'] = np.abs(df['bat_power']) / 6000
        
        # Battery state indicators
        df['is_charging'] = (df['bat_power'] > 100).astype(int)
        df['is_discharging'] = (df['bat_power'] < -100).astype(int)
        df['is_battery_idle'] = (np.abs(df['bat_power']) <= 100).astype(int)
        
        # Battery capacity features
        df['battery_capacity_used'] = (100 - df['soc']) / 100
        df['battery_capacity_available'] = df['soc'] / 100
        
        # SOC change rate
        df['soc_change'] = df.groupby('system_id')['soc'].diff().fillna(0)
        df['soc_change_rate'] = df['soc_change'] / 5  # Per 5-minute interval
        
        # 3. SYSTEM-SPECIFIC FEATURES
        df['is_system_1'] = (df['system_id'] == 1).astype(int)
        df['is_system_2'] = (df['system_id'] == 2).astype(int)
        
        # System consumption patterns
        consumption_patterns = {
            1: {6: 0.5, 7: 0.7, 8: 0.6, 18: 1.2, 19: 1.5, 20: 1.3, 21: 1.0},
            2: {6: 0.8, 7: 1.2, 8: 1.0, 18: 2.0, 19: 2.5, 20: 2.2, 21: 1.8}
        }
        
        df['expected_consumption'] = df.apply(
            lambda row: consumption_patterns.get(row['system_id'], {}).get(row['hour'], 1.0),
            axis=1
        )
        
        # 4. PRODUCTION FEATURES
        df['dc_total'] = df['powerdc1'] + df['powerdc2']
        df['efficiency'] = np.where(df['dc_total'] > 0, df['ac_power'] / df['dc_total'], 0)
        df['efficiency_normalized'] = np.clip(df['efficiency'], 0, 1.2)
        
        # Grid interaction
        df['feedin_power_norm'] = df['feedin_power'] / 10000
        df['consume_energy_norm'] = df['consume_energy'] / 50
        
        # 5. WEATHER FEATURES
        df['ghi_normalized'] = df['ghi'] / 1000
        df['temperature_normalized'] = (df['temperature'] - 20) / 30
        df['cloud_cover_normalized'] = df['cloud_cover'] / 100
        
        # Weather efficiency
        df['weather_efficiency'] = df['ghi_normalized'] * (1 - df['cloud_cover_normalized'] * 0.5)
        
        # Temperature effect on PV
        df['temperature_efficiency'] = 1 - (df['temperature'] - 25) * 0.004
        df['temperature_efficiency'] = np.clip(df['temperature_efficiency'], 0.7, 1.1)
        
        # 6. INTERACTION FEATURES
        df['battery_weather_interaction'] = df['soc_normalized'] * df['weather_efficiency']
        df['system_weather_interaction'] = df['system_id'] * df['weather_efficiency']
        df['time_battery_interaction'] = df['hour_sin'] * df['soc_normalized']
        df['system_time_interaction'] = df['system_id'] * df['hour_sin']
        
        # Define production feature set
        self.feature_columns = [
            # System identification
            'system_id', 'is_system_1', 'is_system_2',
            
            # Temporal features
            'hour_sin', 'hour_cos', 'month_sin', 'month_cos', 
            'day_of_year_sin', 'day_of_year_cos',
            'is_weekend', 'is_daylight', 'is_peak_solar', 'is_evening', 'is_night',
            
            # Battery features
            'soc_normalized', 'battery_mode', 'battery_utilization',
            'is_charging', 'is_discharging', 'is_battery_idle',
            'battery_capacity_used', 'battery_capacity_available', 'soc_change_rate',
            
            # System features
            'expected_consumption', 'efficiency_normalized',
            'feedin_power_norm', 'consume_energy_norm',
            
            # Weather features
            'ghi_normalized', 'temperature_normalized', 'cloud_cover_normalized',
            'weather_efficiency', 'temperature_efficiency',
            
            # Interaction features
            'battery_weather_interaction', 'system_weather_interaction',
            'time_battery_interaction', 'system_time_interaction'
        ]
        
        logger.info(f"✅ Created {len(self.feature_columns)} production features")
        return df
    
    def train_ensemble_model(self, df):
        """Train production-ready ensemble model"""
        logger.info("🚀 Training production-ready ensemble model...")
        
        # Prepare data
        df_clean = df.dropna(subset=self.feature_columns + ['ac_power'])
        X = df_clean[self.feature_columns]
        y = df_clean['ac_power']
        
        logger.info(f"Training data: {len(X):,} samples, {len(self.feature_columns)} features")
        
        # Time-based split
        split_date = df_clean['timestamp'].quantile(0.8)
        train_mask = df_clean['timestamp'] <= split_date
        test_mask = df_clean['timestamp'] > split_date
        
        X_train = X[train_mask]
        X_test = X[test_mask]
        y_train = y[train_mask]
        y_test = y[test_mask]
        
        logger.info(f"Train set: {len(X_train):,} samples")
        logger.info(f"Test set: {len(X_test):,} samples")
        
        # Create individual models
        rf_model = RandomForestRegressor(
            n_estimators=150,
            max_depth=18,
            min_samples_split=5,
            min_samples_leaf=2,
            max_features='sqrt',
            random_state=42,
            n_jobs=-1
        )
        
        xgb_model = xgb.XGBRegressor(
            n_estimators=150,
            max_depth=8,
            learning_rate=0.1,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=42,
            n_jobs=-1
        )
        
        # Create ensemble
        ensemble_model = VotingRegressor([
            ('rf', rf_model),
            ('xgb', xgb_model)
        ])
        
        logger.info("Training ensemble model...")
        ensemble_model.fit(X_train, y_train)
        
        # Predictions
        y_pred_train = ensemble_model.predict(X_train)
        y_pred_test = ensemble_model.predict(X_test)
        
        # Metrics
        train_metrics = {
            'rmse': np.sqrt(mean_squared_error(y_train, y_pred_train)),
            'mae': mean_absolute_error(y_train, y_pred_train),
            'r2': r2_score(y_train, y_pred_train)
        }
        
        test_metrics = {
            'rmse': np.sqrt(mean_squared_error(y_test, y_pred_test)),
            'mae': mean_absolute_error(y_test, y_pred_test),
            'r2': r2_score(y_test, y_pred_test)
        }
        
        # System-specific evaluation
        system_metrics = {}
        for system_id in [1, 2]:
            system_mask = X_test['system_id'] == system_id
            if system_mask.sum() > 0:
                y_test_sys = y_test[system_mask]
                y_pred_sys = y_pred_test[system_mask]
                
                system_metrics[f'system_{system_id}'] = {
                    'rmse': np.sqrt(mean_squared_error(y_test_sys, y_pred_sys)),
                    'mae': mean_absolute_error(y_test_sys, y_pred_sys),
                    'r2': r2_score(y_test_sys, y_pred_sys),
                    'samples': len(y_test_sys)
                }
        
        results = {
            'model_name': 'Enhanced Model v3 Ensemble',
            'train_metrics': train_metrics,
            'test_metrics': test_metrics,
            'system_metrics': system_metrics,
            'feature_count': len(self.feature_columns),
            'training_samples': len(X_train),
            'test_samples': len(X_test),
            'training_date': datetime.now().isoformat()
        }
        
        logger.info(f"✅ ENHANCED MODEL V3 ENSEMBLE RESULTS:")
        logger.info(f"   Test R²: {test_metrics['r2']:.3f}")
        logger.info(f"   Test RMSE: {test_metrics['rmse']:.1f}W")
        logger.info(f"   Test MAE: {test_metrics['mae']:.1f}W")
        
        for system_id in [1, 2]:
            if f'system_{system_id}' in system_metrics:
                sys_r2 = system_metrics[f'system_{system_id}']['r2']
                sys_rmse = system_metrics[f'system_{system_id}']['rmse']
                logger.info(f"   System {system_id}: R² = {sys_r2:.3f}, RMSE = {sys_rmse:.1f}W")
        
        return ensemble_model, results

def main():
    """Execute Phase 4: Ensemble & Production Ready"""
    logger.info("🚀 ENHANCED MODEL V3 - PHASE 4: ENSEMBLE & PRODUCTION READY")
    logger.info("=" * 80)
    logger.info(f"📅 Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("🎯 Objective: Create production-ready Enhanced Model v3")
    
    try:
        ensemble = EnsembleProductionModel()
        
        # Load optimized dataset
        df = ensemble.load_optimized_dataset()
        
        # Create production features
        df_features = ensemble.create_production_features(df)
        
        # Train ensemble model
        model, results = ensemble.train_ensemble_model(df_features)
        
        # Save production model
        model_path = ensemble.output_dir / "enhanced_model_v3_production.joblib"
        joblib.dump(model, model_path)
        
        results_path = ensemble.output_dir / "production_results.json"
        with open(results_path, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        features_path = ensemble.output_dir / "production_features.json"
        with open(features_path, 'w') as f:
            json.dump(ensemble.feature_columns, f, indent=2)
        
        # Create deployment info
        deployment_info = {
            'model_name': 'Enhanced Model v3 Production',
            'model_file': 'enhanced_model_v3_production.joblib',
            'features_file': 'production_features.json',
            'performance': results['test_metrics'],
            'system_performance': results['system_metrics'],
            'feature_count': len(ensemble.feature_columns),
            'training_samples': results['training_samples'],
            'deployment_ready': True,
            'created_date': datetime.now().isoformat()
        }
        
        deployment_path = ensemble.output_dir / "deployment_info.json"
        with open(deployment_path, 'w') as f:
            json.dump(deployment_info, f, indent=2)
        
        logger.info("\n" + "=" * 80)
        logger.info("🎉 ENHANCED MODEL V3 COMPLETE - PRODUCTION READY!")
        logger.info(f"🏆 Final R²: {results['test_metrics']['r2']:.3f}")
        logger.info(f"✅ Final RMSE: {results['test_metrics']['rmse']:.1f}W")
        logger.info(f"✅ System-Aware: ✅ Working")
        logger.info(f"✅ Battery-Aware: ✅ Working")
        logger.info(f"✅ Features: {len(ensemble.feature_columns)}")
        logger.info(f"✅ Training samples: {results['training_samples']:,}")
        logger.info(f"✅ Model saved: {model_path}")
        logger.info("🚀 READY FOR PRODUCTION DEPLOYMENT!")
        logger.info("=" * 80)
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Phase 4 failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
