#!/usr/bin/env python3
"""
GPU-Accelerated Training Infrastructure
=======================================

Ultra-fast training infrastructure optimized for RTX 4070 Ti (12GB VRAM)
- RAPIDS cuML integration
- XGBoost GPU acceleration  
- CuPy array operations
- Memory-optimized batch processing

Hardware: RTX 4070 Ti + 32GB RAM
Created: June 6, 2025
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import numpy as np
import pandas as pd
import time
import logging
import warnings
from pathlib import Path
from datetime import datetime
import joblib
import json
from typing import Dict, List, Tuple, Any, Optional, Union

warnings.filterwarnings('ignore')

# GPU libraries with comprehensive fallbacks
GPU_AVAILABLE = False
CUPY_AVAILABLE = False
RAPIDS_AVAILABLE = False
TORCH_AVAILABLE = False

try:
    import cupy as cp
    CUPY_AVAILABLE = True
    print("✅ CuPy loaded successfully!")
except ImportError:
    print("⚠️ CuPy not available - using NumPy fallback")

try:
    import cudf
    import cuml
    from cuml.ensemble import RandomForestRegressor as cuRandomForestRegressor
    from cuml.model_selection import train_test_split as cu_train_test_split
    from cuml.preprocessing import StandardScaler as cuStandardScaler
    from cuml.metrics import mean_squared_error as cu_mse
    RAPIDS_AVAILABLE = True
    print("✅ RAPIDS cuML loaded successfully!")
except ImportError:
    print("⚠️ RAPIDS not available - using scikit-learn fallback")

try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    from torch.utils.data import DataLoader, TensorDataset
    if torch.cuda.is_available():
        TORCH_AVAILABLE = True
        print(f"✅ PyTorch CUDA loaded: {torch.cuda.get_device_name(0)}")
    else:
        print("⚠️ PyTorch CUDA not available")
except ImportError:
    print("⚠️ PyTorch not available")

# Standard ML libraries
import xgboost as xgb
import lightgbm as lgb
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

# Check overall GPU availability
GPU_AVAILABLE = CUPY_AVAILABLE or RAPIDS_AVAILABLE or TORCH_AVAILABLE

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class GPUTrainingInfrastructure:
    """
    GPU-accelerated training infrastructure optimized for RTX 4070 Ti
    """
    
    def __init__(self, gpu_memory_limit_gb=10):
        """
        Initialize GPU training infrastructure
        
        Args:
            gpu_memory_limit_gb: GPU memory limit in GB (leave 2GB free for system)
        """
        self.gpu_memory_limit_gb = gpu_memory_limit_gb
        self.gpu_available = GPU_AVAILABLE
        self.cupy_available = CUPY_AVAILABLE
        self.rapids_available = RAPIDS_AVAILABLE
        self.torch_available = TORCH_AVAILABLE
        
        # Hardware specs
        self.gpu_memory_total_gb = 12  # RTX 4070 Ti
        self.system_memory_gb = 32
        self.cuda_cores = 7680
        
        # Performance tracking
        self.training_times = {}
        self.memory_usage = {}
        
        logger.info("🚀 GPU Training Infrastructure initialized")
        logger.info(f"   GPU Available: {self.gpu_available}")
        logger.info(f"   CuPy Available: {self.cupy_available}")
        logger.info(f"   RAPIDS Available: {self.rapids_available}")
        logger.info(f"   PyTorch CUDA Available: {self.torch_available}")
        logger.info(f"   GPU Memory Limit: {self.gpu_memory_limit_gb}GB")
        
        # Initialize GPU memory management
        self._setup_gpu_memory()
    
    def _setup_gpu_memory(self):
        """Setup GPU memory management"""
        if self.cupy_available:
            try:
                # Set memory pool limit
                mempool = cp.get_default_memory_pool()
                mempool.set_limit(size=self.gpu_memory_limit_gb * 1024**3)
                
                # Enable memory pool
                cp.cuda.MemoryPool().set_limit(size=self.gpu_memory_limit_gb * 1024**3)
                
                logger.info(f"✅ GPU memory pool configured: {self.gpu_memory_limit_gb}GB limit")
            except Exception as e:
                logger.warning(f"⚠️ GPU memory setup failed: {e}")
        
        if self.torch_available:
            try:
                # Set PyTorch memory fraction
                torch.cuda.set_per_process_memory_fraction(self.gpu_memory_limit_gb / self.gpu_memory_total_gb)
                torch.cuda.empty_cache()
                
                logger.info("✅ PyTorch GPU memory configured")
            except Exception as e:
                logger.warning(f"⚠️ PyTorch GPU memory setup failed: {e}")
    
    def get_gpu_status(self) -> Dict[str, Any]:
        """Get current GPU status and memory usage"""
        status = {
            'gpu_available': self.gpu_available,
            'cupy_available': self.cupy_available,
            'rapids_available': self.rapids_available,
            'torch_available': self.torch_available,
            'memory_info': {}
        }
        
        if self.cupy_available:
            try:
                mempool = cp.get_default_memory_pool()
                status['memory_info']['cupy'] = {
                    'used_gb': mempool.used_bytes() / 1024**3,
                    'total_gb': mempool.total_bytes() / 1024**3
                }
            except:
                pass
        
        if self.torch_available:
            try:
                status['memory_info']['torch'] = {
                    'allocated_gb': torch.cuda.memory_allocated() / 1024**3,
                    'reserved_gb': torch.cuda.memory_reserved() / 1024**3
                }
            except:
                pass
        
        return status
    
    def optimize_batch_size(self, data_size: int, feature_count: int) -> int:
        """
        Calculate optimal batch size based on GPU memory and data characteristics
        
        Args:
            data_size: Number of samples
            feature_count: Number of features
            
        Returns:
            Optimal batch size
        """
        if not self.gpu_available:
            return min(1024, data_size)  # CPU fallback
        
        # Estimate memory per sample (in bytes)
        # Features (float32) + gradients + intermediate calculations
        memory_per_sample = feature_count * 4 * 3  # Conservative estimate
        
        # Available memory (leave 20% buffer)
        available_memory = self.gpu_memory_limit_gb * 1024**3 * 0.8
        
        # Calculate batch size
        max_batch_size = int(available_memory / memory_per_sample)
        
        # Practical limits
        optimal_batch_size = min(
            max_batch_size,
            16384,  # Maximum practical batch size
            data_size  # Can't exceed data size
        )
        
        # Ensure minimum batch size
        optimal_batch_size = max(optimal_batch_size, 32)
        
        logger.info(f"📊 Optimal batch size calculated: {optimal_batch_size}")
        logger.info(f"   Data size: {data_size:,}")
        logger.info(f"   Features: {feature_count}")
        logger.info(f"   Memory per sample: {memory_per_sample} bytes")
        
        return optimal_batch_size
    
    def convert_to_gpu(self, data: Union[np.ndarray, pd.DataFrame]) -> Any:
        """
        Convert data to GPU format (CuPy or cuDF)
        
        Args:
            data: Input data (NumPy array or pandas DataFrame)
            
        Returns:
            GPU-optimized data format
        """
        if not self.gpu_available:
            return data
        
        try:
            if isinstance(data, pd.DataFrame) and self.rapids_available:
                # Convert to cuDF
                return cudf.from_pandas(data)
            elif isinstance(data, np.ndarray) and self.cupy_available:
                # Convert to CuPy
                return cp.asarray(data)
            elif hasattr(data, 'values') and self.cupy_available:
                # DataFrame to CuPy
                return cp.asarray(data.values)
            else:
                return data
        except Exception as e:
            logger.warning(f"⚠️ GPU conversion failed: {e}, using CPU")
            return data
    
    def convert_from_gpu(self, data: Any) -> Union[np.ndarray, pd.DataFrame]:
        """
        Convert data from GPU back to CPU format
        
        Args:
            data: GPU data (CuPy array or cuDF DataFrame)
            
        Returns:
            CPU data (NumPy array or pandas DataFrame)
        """
        try:
            if hasattr(data, 'to_pandas'):
                # cuDF to pandas
                return data.to_pandas()
            elif hasattr(data, 'get'):
                # CuPy to NumPy
                return data.get()
            else:
                return data
        except Exception as e:
            logger.warning(f"⚠️ GPU to CPU conversion failed: {e}")
            return data
    
    def create_gpu_xgboost_model(self, **kwargs) -> xgb.XGBRegressor:
        """Create GPU-accelerated XGBoost model"""
        default_params = {
            'tree_method': 'gpu_hist' if self.gpu_available else 'hist',
            'gpu_id': 0 if self.gpu_available else None,
            'n_estimators': 1000,
            'max_depth': 8,
            'learning_rate': 0.1,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'random_state': 42,
            'n_jobs': -1
        }
        
        # Update with user parameters
        default_params.update(kwargs)
        
        # Remove gpu_id if not using GPU
        if not self.gpu_available and 'gpu_id' in default_params:
            del default_params['gpu_id']
        
        return xgb.XGBRegressor(**default_params)
    
    def create_gpu_lightgbm_model(self, **kwargs) -> lgb.LGBMRegressor:
        """Create GPU-accelerated LightGBM model"""
        default_params = {
            'device': 'gpu' if self.gpu_available else 'cpu',
            'n_estimators': 1000,
            'max_depth': 8,
            'learning_rate': 0.1,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'random_state': 42,
            'n_jobs': -1,
            'verbose': -1
        }
        
        # Add GPU-specific parameters
        if self.gpu_available:
            default_params.update({
                'gpu_platform_id': 0,
                'gpu_device_id': 0
            })
        
        # Update with user parameters
        default_params.update(kwargs)
        
        return lgb.LGBMRegressor(**default_params)
    
    def create_gpu_random_forest_model(self, **kwargs) -> Any:
        """Create GPU-accelerated Random Forest model"""
        default_params = {
            'n_estimators': 200,
            'max_depth': 15,
            'min_samples_split': 5,
            'min_samples_leaf': 2,
            'random_state': 42
        }
        
        # Update with user parameters
        default_params.update(kwargs)
        
        if self.rapids_available:
            # Use cuML Random Forest
            return cuRandomForestRegressor(**default_params)
        else:
            # Fallback to sklearn with CPU parallelization
            default_params['n_jobs'] = -1
            return RandomForestRegressor(**default_params)
    
    def train_model_gpu(self, model: Any, X_train: Any, y_train: Any, 
                       X_val: Any = None, y_val: Any = None) -> Tuple[Any, Dict[str, float]]:
        """
        Train model with GPU acceleration and performance tracking
        
        Args:
            model: Model to train
            X_train: Training features
            y_train: Training targets
            X_val: Validation features (optional)
            y_val: Validation targets (optional)
            
        Returns:
            Tuple of (trained_model, performance_metrics)
        """
        model_name = type(model).__name__
        logger.info(f"🎓 Training {model_name} with GPU acceleration...")
        
        start_time = time.time()
        start_memory = self.get_gpu_status()
        
        try:
            # Convert data to GPU format if possible
            X_train_gpu = self.convert_to_gpu(X_train)
            y_train_gpu = self.convert_to_gpu(y_train) if hasattr(y_train, 'values') else y_train
            
            # Handle different model types
            if isinstance(model, (xgb.XGBRegressor, lgb.LGBMRegressor)):
                # XGBoost and LightGBM handle GPU internally
                if X_val is not None and y_val is not None:
                    model.fit(
                        X_train, y_train,
                        eval_set=[(X_val, y_val)],
                        early_stopping_rounds=50,
                        verbose=False
                    )
                else:
                    model.fit(X_train, y_train)
            
            elif self.rapids_available and hasattr(model, 'fit'):
                # cuML models
                if isinstance(X_train_gpu, cudf.DataFrame):
                    y_train_gpu = cudf.Series(y_train.values) if hasattr(y_train, 'values') else cudf.Series(y_train)
                model.fit(X_train_gpu, y_train_gpu)
            
            else:
                # Standard sklearn models
                model.fit(X_train, y_train)
            
            training_time = time.time() - start_time
            end_memory = self.get_gpu_status()
            
            # Calculate performance metrics
            if X_val is not None and y_val is not None:
                y_pred = self.predict_gpu(model, X_val)
                y_val_cpu = self.convert_from_gpu(y_val) if hasattr(y_val, 'get') else y_val
                y_pred_cpu = self.convert_from_gpu(y_pred) if hasattr(y_pred, 'get') else y_pred
                
                rmse = np.sqrt(mean_squared_error(y_val_cpu, y_pred_cpu))
                mae = mean_absolute_error(y_val_cpu, y_pred_cpu)
                r2 = r2_score(y_val_cpu, y_pred_cpu)
            else:
                rmse = mae = r2 = 0.0
            
            metrics = {
                'training_time_seconds': training_time,
                'rmse': rmse,
                'mae': mae,
                'r2': r2,
                'gpu_memory_used_gb': end_memory.get('memory_info', {}).get('cupy', {}).get('used_gb', 0)
            }
            
            # Store performance data
            self.training_times[model_name] = training_time
            self.memory_usage[model_name] = metrics['gpu_memory_used_gb']
            
            logger.info(f"✅ {model_name} training completed")
            logger.info(f"   Training time: {training_time:.2f}s")
            logger.info(f"   R² score: {r2:.4f}")
            logger.info(f"   RMSE: {rmse:.2f}")
            logger.info(f"   GPU memory used: {metrics['gpu_memory_used_gb']:.2f}GB")
            
            return model, metrics
            
        except Exception as e:
            logger.error(f"❌ {model_name} training failed: {e}")
            # Return basic metrics on failure
            return model, {
                'training_time_seconds': time.time() - start_time,
                'rmse': float('inf'),
                'mae': float('inf'),
                'r2': -1.0,
                'gpu_memory_used_gb': 0.0,
                'error': str(e)
            }
    
    def predict_gpu(self, model: Any, X: Any) -> Any:
        """
        Make predictions with GPU acceleration
        
        Args:
            model: Trained model
            X: Features for prediction
            
        Returns:
            Predictions
        """
        try:
            # Convert to GPU format if needed
            X_gpu = self.convert_to_gpu(X)
            
            # Make predictions
            if self.rapids_available and hasattr(model, 'predict') and isinstance(X_gpu, cudf.DataFrame):
                predictions = model.predict(X_gpu)
            else:
                predictions = model.predict(X)
            
            return predictions
            
        except Exception as e:
            logger.warning(f"⚠️ GPU prediction failed: {e}, using CPU fallback")
            return model.predict(X)
    
    def benchmark_gpu_performance(self, data_sizes: List[int] = None) -> Dict[str, Any]:
        """
        Benchmark GPU performance with different data sizes
        
        Args:
            data_sizes: List of data sizes to test
            
        Returns:
            Benchmark results
        """
        if data_sizes is None:
            data_sizes = [1000, 5000, 10000, 50000]
        
        logger.info("🏁 Starting GPU performance benchmark...")
        
        results = {
            'gpu_status': self.get_gpu_status(),
            'benchmarks': {}
        }
        
        for size in data_sizes:
            logger.info(f"   Testing with {size:,} samples...")
            
            # Generate synthetic data
            if self.cupy_available:
                X = cp.random.random((size, 20), dtype=cp.float32)
                y = cp.random.random(size, dtype=cp.float32)
            else:
                X = np.random.random((size, 20)).astype(np.float32)
                y = np.random.random(size).astype(np.float32)
            
            # Test different operations
            benchmark_results = {}
            
            # Matrix multiplication
            start_time = time.time()
            if self.cupy_available:
                result = cp.dot(X, X.T)
                cp.cuda.Stream.null.synchronize()  # Wait for GPU
            else:
                result = np.dot(X, X.T)
            benchmark_results['matrix_multiply_time'] = time.time() - start_time
            
            # Data transfer (if GPU available)
            if self.cupy_available:
                start_time = time.time()
                X_cpu = cp.asnumpy(X)
                benchmark_results['gpu_to_cpu_time'] = time.time() - start_time
                
                start_time = time.time()
                X_gpu = cp.asarray(X_cpu)
                benchmark_results['cpu_to_gpu_time'] = time.time() - start_time
            
            results['benchmarks'][f'{size}_samples'] = benchmark_results
        
        logger.info("✅ GPU performance benchmark completed")
        return results
    
    def cleanup_gpu_memory(self):
        """Clean up GPU memory"""
        if self.cupy_available:
            try:
                mempool = cp.get_default_memory_pool()
                mempool.free_all_blocks()
                logger.info("✅ CuPy memory cleaned up")
            except:
                pass
        
        if self.torch_available:
            try:
                torch.cuda.empty_cache()
                logger.info("✅ PyTorch CUDA cache cleared")
            except:
                pass
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary of all training operations"""
        return {
            'training_times': self.training_times,
            'memory_usage': self.memory_usage,
            'gpu_status': self.get_gpu_status(),
            'total_models_trained': len(self.training_times),
            'average_training_time': np.mean(list(self.training_times.values())) if self.training_times else 0,
            'peak_memory_usage': max(self.memory_usage.values()) if self.memory_usage else 0
        }

def main():
    """Test GPU training infrastructure"""
    logger.info("🚀 Testing GPU Training Infrastructure")
    logger.info("=" * 60)
    
    # Initialize infrastructure
    gpu_trainer = GPUTrainingInfrastructure()
    
    # Get GPU status
    status = gpu_trainer.get_gpu_status()
    logger.info("📊 GPU Status:")
    for key, value in status.items():
        logger.info(f"   {key}: {value}")
    
    # Run benchmark
    benchmark_results = gpu_trainer.benchmark_gpu_performance()
    logger.info("🏁 Benchmark Results:")
    for size, results in benchmark_results['benchmarks'].items():
        logger.info(f"   {size}:")
        for metric, value in results.items():
            logger.info(f"     {metric}: {value:.4f}s")
    
    # Test model creation
    logger.info("🎓 Testing model creation...")
    
    xgb_model = gpu_trainer.create_gpu_xgboost_model()
    lgb_model = gpu_trainer.create_gpu_lightgbm_model()
    rf_model = gpu_trainer.create_gpu_random_forest_model()
    
    logger.info(f"✅ XGBoost model created: {type(xgb_model).__name__}")
    logger.info(f"✅ LightGBM model created: {type(lgb_model).__name__}")
    logger.info(f"✅ Random Forest model created: {type(rf_model).__name__}")
    
    # Performance summary
    summary = gpu_trainer.get_performance_summary()
    logger.info("📈 Performance Summary:")
    for key, value in summary.items():
        logger.info(f"   {key}: {value}")
    
    # Cleanup
    gpu_trainer.cleanup_gpu_memory()
    
    logger.info("🎉 GPU Training Infrastructure test completed!")

if __name__ == "__main__":
    main()
