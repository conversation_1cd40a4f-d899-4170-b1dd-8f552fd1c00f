#!/usr/bin/env python3
"""
Process SolaX Excel data exports and convert to database format.
"""

import pandas as pd
import numpy as np
from datetime import datetime
import asyncio
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import psycopg2
from psycopg2.extras import RealDictCursor
import json

async def process_excel_file(filepath, plant_name, system_id):
    """Process a single Excel file and extract SolaX data."""
    print(f"\n=== Processing {plant_name} ===")
    print(f"File: {filepath}")

    try:
        # Read Excel file, skipping the header row
        df = pd.read_excel(filepath, skiprows=1)

        # Clean up column names
        df.columns = [
            'no',
            'timestamp',
            'daily_pv_yield_kwh',
            'daily_inverter_output_kwh',
            'daily_exported_energy_kwh',
            'daily_imported_energy_kwh',
            'export_power_w',
            'daily_consumed_kwh'
        ]

        print(f"Raw data shape: {df.shape}")

        # Convert timestamp
        df['timestamp'] = pd.to_datetime(df['timestamp'], errors='coerce')

        # Remove rows with invalid timestamps
        df = df.dropna(subset=['timestamp'])

        print(f"After timestamp cleaning: {df.shape}")
        print(f"Date range: {df['timestamp'].min()} to {df['timestamp'].max()}")

        # Convert to SolaX data format
        solax_records = []

        for _, row in df.iterrows():
            # Calculate AC power from daily inverter output
            # This is an approximation - we'll need to derive instantaneous power
            ac_power = row['export_power_w'] if pd.notna(row['export_power_w']) else 0

            # Map to SolaX data structure
            solax_record = {
                'timestamp': row['timestamp'],
                'ac_power': abs(ac_power),  # Make positive for production
                'yield_today': row['daily_pv_yield_kwh'] if pd.notna(row['daily_pv_yield_kwh']) else 0,
                'feedin_power': row['export_power_w'] if pd.notna(row['export_power_w']) else 0,
                'feedin_energy': row['daily_exported_energy_kwh'] if pd.notna(row['daily_exported_energy_kwh']) else 0,
                'consume_energy': row['daily_consumed_kwh'] if pd.notna(row['daily_consumed_kwh']) else 0,
                'powerdc1': ac_power * 0.5 if ac_power > 0 else 0,  # Estimate DC power split
                'powerdc2': ac_power * 0.5 if ac_power > 0 else 0,
                'soc': 50,  # Default SOC - we don't have battery data in these exports
                'bat_power': 0,  # Default battery power
                'temperature': None,  # Not available in exports
                'system_id': system_id,
                'plant_name': plant_name
            }

            solax_records.append(solax_record)

        print(f"Processed {len(solax_records)} records for {plant_name}")

        return solax_records

    except Exception as e:
        print(f"Error processing {filepath}: {e}")
        import traceback
        traceback.print_exc()
        return []

def save_to_database(solax_records, table_name="solax_data"):
    """Save SolaX records to database."""
    if not solax_records:
        print("No records to save")
        return

    print(f"\nSaving {len(solax_records)} records to {table_name}...")

    try:
        # Connect to database
        conn = psycopg2.connect(
            host="localhost",
            database="solar_prediction",
            user="postgres",
            password="postgres"
        )
        cursor = conn.cursor()

        # Insert records
        for record in solax_records:
            # Determine table based on system_id
            target_table = "solax_data" if record['system_id'] == 1 else "solax_data2"

            insert_query = f"""
                INSERT INTO {target_table} (
                    timestamp, ac_power, yield_today, feedin_power, feedin_energy,
                    consume_energy, powerdc1, powerdc2, soc, bat_power, temperature,
                    system_id, inverter_sn, wifi_sn, raw_data
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                )
            """

            # Create raw_data JSON
            raw_data = json.dumps({
                'plant_name': record.get('plant_name', ''),
                'source': 'excel_import',
                'processed_at': datetime.now().isoformat()
            })

            cursor.execute(insert_query, (
                record['timestamp'],
                record['ac_power'],
                record['yield_today'],
                record['feedin_power'],
                record['feedin_energy'],
                record['consume_energy'],
                record['powerdc1'],
                record['powerdc2'],
                record['soc'],
                record['bat_power'],
                record['temperature'],
                record['system_id'],
                f"EXCEL_IMPORT_{record['system_id']}",  # inverter_sn
                f"EXCEL_WIFI_{record['system_id']}",    # wifi_sn
                raw_data
            ))

        conn.commit()
        cursor.close()
        conn.close()

        print(f"✅ Successfully saved {len(solax_records)} records")

    except Exception as e:
        print(f"❌ Error saving to database: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """Main function to process both Excel files."""
    base_path = "/home/<USER>/solar-prediction-project/data/raw"

    # Define the files and their corresponding systems
    files_config = [
        {
            'filename': 'Plant Reports 2025-04-16-2025-05-30.xlsx',
            'plant_name': 'Σπίτι Κάτω',
            'system_id': 1
        },
        {
            'filename': 'Plant Reports 2025-04-16-2025-05-30 (1).xlsx',
            'plant_name': 'Σπίτι Πάνω',
            'system_id': 2
        }
    ]

    all_records = []

    for config in files_config:
        filepath = os.path.join(base_path, config['filename'])

        if os.path.exists(filepath):
            records = await process_excel_file(
                filepath,
                config['plant_name'],
                config['system_id']
            )
            all_records.extend(records)
        else:
            print(f"File not found: {filepath}")

    if all_records:
        print(f"\n=== SUMMARY ===")
        print(f"Total records processed: {len(all_records)}")

        # Show date range
        timestamps = [r['timestamp'] for r in all_records]
        print(f"Date range: {min(timestamps)} to {max(timestamps)}")

        # Save to database
        save_to_database(all_records)

        print("\n✅ Processing complete!")
    else:
        print("❌ No records processed")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
