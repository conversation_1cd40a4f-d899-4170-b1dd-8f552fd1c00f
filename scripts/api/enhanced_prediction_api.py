#!/usr/bin/env python3
"""
Enhanced Prediction API
FastAPI service for enhanced solar predictions with multi-source data
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
import joblib
import json
import logging
from typing import Dict, List, Optional
import psycopg2

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Enhanced Solar Prediction API",
    description="Advanced solar prediction with multi-source data integration",
    version="2.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


class PredictionRequest(BaseModel):
    system_id: int
    prediction_date: str
    prediction_type: str = "daily"  # daily, hourly
    include_confidence: bool = True
    include_features: bool = False


class PredictionResponse(BaseModel):
    system_id: int
    prediction_date: str
    prediction_type: str
    predicted_yield: float
    confidence_score: float
    model_version: str
    features_used: List[str]
    weather_conditions: Dict
    physics_factors: Dict
    timestamp: str


class EnhancedPredictionService:
    """Enhanced prediction service with multi-source data"""
    
    def __init__(self):
        self.models = {}
        self.model_metadata = {}
        self.load_models()
    
    def load_models(self):
        """Load enhanced models for both systems"""
        
        for system_id in [1, 2]:
            try:
                model_path = f"/home/<USER>/solar-prediction-project/models/enhanced_model_system_{system_id}.joblib"
                metadata_path = f"/home/<USER>/solar-prediction-project/models/enhanced_model_system_{system_id}_metadata.json"
                
                if os.path.exists(model_path):
                    self.models[system_id] = joblib.load(model_path)
                    
                    if os.path.exists(metadata_path):
                        with open(metadata_path, 'r') as f:
                            self.model_metadata[system_id] = json.load(f)
                    
                    logger.info(f"✅ Loaded enhanced model for System {system_id}")
                else:
                    logger.warning(f"⚠️ Enhanced model not found for System {system_id}")
                    
            except Exception as e:
                logger.error(f"❌ Error loading model for System {system_id}: {e}")
    
    def get_latest_features(self, system_id: int, target_date: datetime) -> Dict:
        """Get latest features for prediction"""
        
        try:
            conn = psycopg2.connect(
                host='localhost',
                database='solar_prediction',
                user='postgres',
                password='postgres'
            )
            
            # Get latest NASA POWER data
            nasa_query = """
            SELECT 
                ghi, temperature, wind_speed, clearness_index
            FROM nasa_power_data
            WHERE timestamp <= %s
            ORDER BY timestamp DESC
            LIMIT 1
            """
            
            nasa_df = pd.read_sql(nasa_query, conn, params=[target_date])
            
            # Get latest weather data
            weather_query = """
            SELECT 
                shortwave_radiation, temperature_2m, wind_speed_10m, cloud_cover
            FROM weather_data
            WHERE timestamp <= %s
            ORDER BY timestamp DESC
            LIMIT 1
            """
            
            weather_df = pd.read_sql(weather_query, conn, params=[target_date])
            
            conn.close()
            
            # Calculate enhanced features
            features = self.calculate_enhanced_features(target_date, nasa_df, weather_df)
            
            return features
            
        except Exception as e:
            logger.error(f"Error getting features: {e}")
            return self.get_default_features(target_date)
    
    def calculate_enhanced_features(self, target_date: datetime, nasa_df: pd.DataFrame, weather_df: pd.DataFrame) -> Dict:
        """Calculate enhanced features for prediction"""
        
        # Temporal features
        hour = target_date.hour
        day_of_year = target_date.timetuple().tm_yday
        
        features = {
            'hour_sin': np.sin(2 * np.pi * hour / 24),
            'hour_cos': np.cos(2 * np.pi * hour / 24),
            'day_sin': np.sin(2 * np.pi * day_of_year / 365.25),
            'day_cos': np.cos(2 * np.pi * day_of_year / 365.25)
        }
        
        # Solar geometry
        latitude = 38.141348260997596
        declination = 23.45 * np.sin(np.radians(360 * (284 + day_of_year) / 365))
        hour_angle = 15 * (hour - 12)
        
        lat_rad = np.radians(latitude)
        elevation = np.degrees(np.arcsin(
            np.sin(lat_rad) * np.sin(np.radians(declination)) +
            np.cos(lat_rad) * np.cos(np.radians(declination)) * 
            np.cos(np.radians(hour_angle))
        ))
        
        features['solar_elevation_factor'] = max(0, np.sin(np.radians(elevation)))
        
        # Weather features
        if not nasa_df.empty:
            features['ghi_primary'] = float(nasa_df['ghi'].iloc[0]) if pd.notna(nasa_df['ghi'].iloc[0]) else 500
            features['temperature_primary'] = float(nasa_df['temperature'].iloc[0]) if pd.notna(nasa_df['temperature'].iloc[0]) else 20
        elif not weather_df.empty:
            features['ghi_primary'] = float(weather_df['shortwave_radiation'].iloc[0]) if pd.notna(weather_df['shortwave_radiation'].iloc[0]) else 500
            features['temperature_primary'] = float(weather_df['temperature_2m'].iloc[0]) if pd.notna(weather_df['temperature_2m'].iloc[0]) else 20
        else:
            features['ghi_primary'] = 500
            features['temperature_primary'] = 20
        
        # Physics features
        module_temp = features['temperature_primary'] + (features['ghi_primary'] / 800.0) * (45.0 - 20.0)
        temp_efficiency = max(0.5, 1 + (-0.004) * (module_temp - 25.0))
        wind_cooling = 1.02  # Default wind cooling
        
        features['combined_efficiency'] = temp_efficiency * wind_cooling * features['solar_elevation_factor']
        
        return features
    
    def get_default_features(self, target_date: datetime) -> Dict:
        """Get default features when data is not available"""
        
        hour = target_date.hour
        day_of_year = target_date.timetuple().tm_yday
        
        return {
            'hour_sin': np.sin(2 * np.pi * hour / 24),
            'hour_cos': np.cos(2 * np.pi * hour / 24),
            'day_sin': np.sin(2 * np.pi * day_of_year / 365.25),
            'day_cos': np.cos(2 * np.pi * day_of_year / 365.25),
            'solar_elevation_factor': max(0, np.sin(np.radians(max(0, 90 - abs(hour - 12) * 15)))),
            'ghi_primary': 500,
            'temperature_primary': 20,
            'combined_efficiency': 0.8
        }
    
    def predict(self, system_id: int, target_date: datetime, prediction_type: str = "daily") -> Dict:
        """Make enhanced prediction"""
        
        if system_id not in self.models:
            raise ValueError(f"Model not available for System {system_id}")
        
        # Get features
        features = self.get_latest_features(system_id, target_date)
        
        # Prepare feature vector
        model_metadata = self.model_metadata.get(system_id, {})
        feature_names = model_metadata.get('features', list(features.keys()))
        
        # Create feature vector with available features
        feature_vector = []
        used_features = []
        
        for feature_name in feature_names:
            if feature_name in features:
                feature_vector.append(features[feature_name])
                used_features.append(feature_name)
            else:
                feature_vector.append(0)  # Default value
        
        # Make prediction
        model = self.models[system_id]
        prediction = model.predict([feature_vector])[0]
        
        # Calculate confidence based on model performance
        model_r2 = model_metadata.get('performance', {}).get('r2', 0.8)
        confidence = min(95, max(70, model_r2 * 100))
        
        # Prepare response
        result = {
            'system_id': system_id,
            'prediction_date': target_date.date().isoformat(),
            'prediction_type': prediction_type,
            'predicted_yield': round(prediction, 2),
            'confidence_score': round(confidence, 1),
            'model_version': model_metadata.get('model_type', 'enhanced_gradient_boosting'),
            'features_used': used_features,
            'weather_conditions': {
                'ghi': features.get('ghi_primary', 500),
                'temperature': features.get('temperature_primary', 20)
            },
            'physics_factors': {
                'solar_elevation_factor': features.get('solar_elevation_factor', 0.5),
                'combined_efficiency': features.get('combined_efficiency', 0.8)
            },
            'timestamp': datetime.now().isoformat()
        }
        
        return result


# Initialize service
prediction_service = EnhancedPredictionService()


@app.get("/")
async def root():
    """API root endpoint"""
    return {
        "service": "Enhanced Solar Prediction API",
        "version": "2.0.0",
        "status": "operational",
        "features": [
            "Multi-source data integration",
            "Physics-based features",
            "Enhanced machine learning models",
            "Real-time predictions"
        ]
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    
    models_loaded = len(prediction_service.models)
    
    return {
        "status": "healthy" if models_loaded > 0 else "degraded",
        "models_loaded": models_loaded,
        "timestamp": datetime.now().isoformat()
    }


@app.post("/api/v2/predict", response_model=PredictionResponse)
async def predict(request: PredictionRequest):
    """Enhanced prediction endpoint"""
    
    try:
        target_date = datetime.fromisoformat(request.prediction_date)
        
        result = prediction_service.predict(
            system_id=request.system_id,
            target_date=target_date,
            prediction_type=request.prediction_type
        )
        
        return PredictionResponse(**result)
        
    except Exception as e:
        logger.error(f"Prediction error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/v2/models")
async def get_models():
    """Get available models information"""
    
    models_info = {}
    
    for system_id, metadata in prediction_service.model_metadata.items():
        models_info[f"system_{system_id}"] = {
            "model_type": metadata.get('model_type', 'unknown'),
            "training_date": metadata.get('training_date', 'unknown'),
            "performance": metadata.get('performance', {}),
            "features": metadata.get('features', [])
        }
    
    return {
        "models": models_info,
        "total_models": len(models_info)
    }


@app.get("/api/v2/features/{system_id}")
async def get_current_features(system_id: int):
    """Get current features for a system"""
    
    try:
        target_date = datetime.now()
        features = prediction_service.get_latest_features(system_id, target_date)
        
        return {
            "system_id": system_id,
            "timestamp": target_date.isoformat(),
            "features": features
        }
        
    except Exception as e:
        logger.error(f"Features error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8200, reload=True)
