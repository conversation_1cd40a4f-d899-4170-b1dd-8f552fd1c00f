#!/usr/bin/env python3
"""
Enhanced Model Training Script - Fixed JSON Serialization
"""

import sys
import os
import json
import numpy as np
from decimal import Decimal

sys.path.append('/home/<USER>/solar-prediction-project')

# Enhanced JSON Encoder
from src.utils.json_utils import <PERSON>hancedJSONEncoder

def train_enhanced_model_fallback(system_id=1):
    """Fallback training with FIXED JSON serialization"""
    print(f"🔄 Using fallback training for System {system_id}")
    
    try:
        from datetime import datetime, timedelta
        import pandas as pd
        import numpy as np
        from sklearn.ensemble import GradientBoostingRegressor
        from sklearn.model_selection import train_test_split
        from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
        import joblib
        import psycopg2
        
        # Database configuration
        db_config = {
            'host': os.getenv('DB_HOST', 'localhost'),
            'port': int(os.getenv('DB_PORT', '5432')),
            'database': os.getenv('DB_NAME', 'solar_prediction'),
            'user': os.getenv('DB_USER', 'postgres'),
            'password': os.getenv('DB_PASSWORD', 'postgres')
        }
        
        # Test database connection
        try:
            conn = psycopg2.connect(**db_config)
            print("✅ Database connection successful")
            conn.close()
        except Exception as e:
            print(f"❌ Database connection failed: {e}")
            return False
        
        # Training data
        end_date = datetime.now()
        start_date = end_date - timedelta(days=90)
        
        conn = psycopg2.connect(**db_config)
        
        query = """
        SELECT 
            timestamp,
            ac_power,
            EXTRACT(hour FROM timestamp) as hour,
            EXTRACT(day FROM timestamp) as day,
            temperature,
            soc,
            bat_power,
            yield_today,
            powerdc1,
            powerdc2,
            system_id
        FROM solax_unified_data 
        WHERE system_id = %s 
        AND timestamp BETWEEN %s AND %s
        AND ac_power IS NOT NULL
        ORDER BY timestamp
        """
        
        try:
            df = pd.read_sql_query(query, conn, params=(system_id, start_date, end_date))
            conn.close()
            
            if len(df) < 50:
                print(f"❌ Insufficient training data: {len(df)} records from solax_unified_data")
                return False
            
            print(f"✅ Loaded {len(df):,} training records")
            
            # Feature engineering
            features = ['hour', 'day', 'temperature', 'soc', 'bat_power', 'yield_today']
            available_features = [f for f in features if f in df.columns and df[f].notna().sum() > 0]
            
            if len(available_features) < 3:
                print(f"❌ Insufficient features: {available_features}")
                return False
            
            # Prepare data
            X = df[available_features].fillna(0)
            y = df['ac_power']
            
            # Remove invalid data
            mask = (y >= 0) & (y <= 15000)  # Reasonable power range
            X = X[mask]
            y = y[mask]
            
            if len(X) < 30:
                print(f"❌ Insufficient valid data: {len(X)} records")
                return False
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42
            )
            
            # Train model
            model = GradientBoostingRegressor(
                n_estimators=100,
                learning_rate=0.1,
                max_depth=6,
                random_state=42
            )
            
            model.fit(X_train, y_train)
            
            # Evaluate
            y_pred = model.predict(X_test)
            
            # Convert numpy types to Python types for JSON serialization
            metrics = {
                'r2': float(r2_score(y_test, y_pred)),
                'mae': float(mean_absolute_error(y_test, y_pred)),
                'rmse': float(np.sqrt(mean_squared_error(y_test, y_pred))),
                'training_samples': int(len(X_train)),
                'test_samples': int(len(X_test)),
                'features': list(available_features),
                'system_id': int(system_id)
            }
            
            print(f"📊 Model Performance:")
            print(f"   R²: {metrics['r2']:.4f}")
            print(f"   MAE: {metrics['mae']:.2f} W")
            print(f"   RMSE: {metrics['rmse']:.2f} W")
            
            # Save model
            model_dir = f"models/enhanced_system_{system_id}_fixed"
            os.makedirs(model_dir, exist_ok=True)
            
            # Save model file
            joblib.dump(model, f"{model_dir}/model.joblib")
            
            # Save metadata with enhanced JSON encoder
            metadata = {
                'system_id': int(system_id),
                'model_type': 'enhanced_fallback_fixed',
                'training_date': datetime.now().isoformat(),
                'performance': metrics,
                'data_source': 'solax_unified_data',
                'target_achieved': bool(metrics['r2'] > 0.7)
            }
            
            with open(f"{model_dir}/metadata.json", 'w') as f:
                json.dump(metadata, f, indent=2, cls=EnhancedJSONEncoder)
            
            print(f"💾 Model saved to: {model_dir}")
            
            return metrics['r2'] > 0.5
            
        except Exception as e:
            print(f"❌ Training error: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Setup error: {e}")
        return False

def train_enhanced_model(system_id=1):
    """Main training function with production fallback"""
    try:
        # Try production training first
        from scripts.training.train_all_models_unified import MasterUnifiedTrainer
        
        trainer = MasterUnifiedTrainer(
            output_dir=f"models/enhanced_system_{system_id}_fixed",
            pipeline_version="v2.0.0"
        )
        
        results = trainer.train_all_models()
        
        if results and results.get('successful_models', 0) > 0:
            print(f"✅ Enhanced models trained successfully for System {system_id}")
            return True
        else:
            return train_enhanced_model_fallback(system_id)
            
    except Exception as e:
        print(f"❌ Production training failed: {e}")
        return train_enhanced_model_fallback(system_id)

if __name__ == "__main__":
    print("🌞 Enhanced Solar Model Training (JSON-Fixed)")
    print("=" * 55)
    print("🎯 Target: Enhanced ML models with fixed JSON serialization")
    print("🔧 Pipeline: Enhanced JSON encoder for numpy types")
    print("=" * 55)
    print()
    
    success_count = 0
    for system_id in [1, 2]:
        try:
            if train_enhanced_model(system_id):
                success_count += 1
        except Exception as e:
            print(f"❌ Training failed for System {system_id}: {e}")
        print()
    
    print(f"🎉 Training completed: {success_count}/2 systems successful")
    if success_count == 2:
        print("✅ All systems trained successfully!")
    elif success_count > 0:
        print("⚠️ Some systems trained successfully")
    else:
        print("❌ All training attempts failed")
