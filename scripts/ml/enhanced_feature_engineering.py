#!/usr/bin/env python3
"""
Enhanced Feature Engineering
Combines NASA POWER, ERA5, CAMS, and SolaX data for improved solar prediction
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import psycopg2
from typing import Dict, List, Optional
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class EnhancedFeatureEngineer:
    """Enhanced feature engineering with multiple data sources"""
    
    def __init__(self):
        self.latitude = 38.141348260997596
        self.longitude = 24.0071653937747
        
        # Physics constants
        self.SOLAR_CONSTANT = 1361  # W/m²
        self.NOCT = 45.0  # Nominal Operating Cell Temperature
        self.TEMP_COEFF = -0.004  # Temperature coefficient (-0.4%/°C)
        
        # Feature groups
        self.temporal_features = []
        self.astronomical_features = []
        self.weather_features = []
        self.physics_features = []
        self.lag_features = []
    
    def load_multi_source_data(self, start_date: datetime, end_date: datetime, system_id: int = 1) -> pd.DataFrame:
        """Load and combine data from multiple sources"""
        
        logger.info(f"📊 Loading multi-source data: {start_date.date()} to {end_date.date()}")
        
        try:
            # Database configuration from environment variables
            db_host = os.getenv('DB_HOST', 'solar-prediction-db')
            db_port = int(os.getenv('DB_PORT', 5432))
            db_name = os.getenv('DB_NAME', 'solar_prediction')
            db_user = os.getenv('DB_USER', 'postgres')
            db_password = os.getenv('DB_PASSWORD', 'postgres')

            conn = psycopg2.connect(
                host=db_host, port=db_port,
                database=db_name,
                user=db_user,
                password=db_password
            )
            
            # Base SolaX data
            solax_table = 'solax_data' if system_id == 1 else 'solax_data2'
            
            base_query = f"""
            SELECT
                timestamp,
                ac_power,
                yield_today,
                soc as battery_soc,
                bat_power as battery_power
            FROM {solax_table}
            WHERE timestamp BETWEEN %s AND %s
            AND ac_power >= 0
            ORDER BY timestamp
            """
            
            df_base = pd.read_sql(base_query, conn, params=[start_date, end_date])
            logger.info(f"✅ Loaded {len(df_base)} SolaX records")
            
            # NASA POWER data
            nasa_query = """
            SELECT 
                timestamp,
                ghi as nasa_ghi,
                temperature as nasa_temperature,
                wind_speed as nasa_wind_speed,
                relative_humidity as nasa_humidity,
                surface_pressure as nasa_pressure,
                clear_sky_ghi as nasa_clear_sky_ghi,
                clearness_index as nasa_clearness_index
            FROM nasa_power_data
            WHERE timestamp BETWEEN %s AND %s
            ORDER BY timestamp
            """
            
            df_nasa = pd.read_sql(nasa_query, conn, params=[start_date, end_date])
            logger.info(f"✅ Loaded {len(df_nasa)} NASA POWER records")
            
            # ERA5 data (if available)
            try:
                era5_query = """
                SELECT 
                    timestamp,
                    temperature_2m as era5_temperature,
                    surface_solar_radiation as era5_ghi,
                    total_cloud_cover as era5_cloud_cover,
                    wind_speed_10m as era5_wind_speed,
                    surface_pressure as era5_pressure,
                    relative_humidity as era5_humidity
                FROM era5_data
                WHERE timestamp BETWEEN %s AND %s
                ORDER BY timestamp
                """
                
                df_era5 = pd.read_sql(era5_query, conn, params=[start_date, end_date])
                logger.info(f"✅ Loaded {len(df_era5)} ERA5 records")
            except:
                logger.warning("⚠️ ERA5 data not available")
                df_era5 = pd.DataFrame()
            
            # CAMS data (if available)
            try:
                cams_query = """
                SELECT 
                    timestamp,
                    ghi as cams_ghi,
                    dni as cams_dni,
                    dhi as cams_dhi,
                    cloud_cover as cams_cloud_cover,
                    temperature as cams_temperature
                FROM cams_radiation_data
                WHERE timestamp BETWEEN %s AND %s
                ORDER BY timestamp
                """
                
                df_cams = pd.read_sql(cams_query, conn, params=[start_date, end_date])
                logger.info(f"✅ Loaded {len(df_cams)} CAMS records")
            except:
                logger.warning("⚠️ CAMS data not available")
                df_cams = pd.DataFrame()
            
            # Weather data (Open-Meteo)
            try:
                weather_query = """
                SELECT 
                    timestamp,
                    shortwave_radiation as weather_ghi,
                    temperature_2m as weather_temperature,
                    relative_humidity_2m as weather_humidity,
                    wind_speed_10m as weather_wind_speed,
                    cloud_cover as weather_cloud_cover
                FROM weather_data
                WHERE timestamp BETWEEN %s AND %s
                ORDER BY timestamp
                """
                
                df_weather = pd.read_sql(weather_query, conn, params=[start_date, end_date])
                logger.info(f"✅ Loaded {len(df_weather)} Weather records")
            except:
                logger.warning("⚠️ Weather data not available")
                df_weather = pd.DataFrame()
            
            conn.close()
            
            # Merge all data sources
            df = df_base.copy()

            # Ensure all timestamps are timezone-naive for merging
            df['timestamp'] = pd.to_datetime(df['timestamp']).dt.tz_localize(None)

            # Merge NASA POWER (primary external source)
            if not df_nasa.empty:
                df_nasa['timestamp'] = pd.to_datetime(df_nasa['timestamp']).dt.tz_localize(None)
                df = pd.merge_asof(
                    df.sort_values('timestamp'),
                    df_nasa.sort_values('timestamp'),
                    on='timestamp',
                    direction='nearest'
                )
            
            # Merge ERA5
            if not df_era5.empty:
                df_era5['timestamp'] = pd.to_datetime(df_era5['timestamp']).dt.tz_localize(None)
                df = pd.merge_asof(
                    df.sort_values('timestamp'),
                    df_era5.sort_values('timestamp'),
                    on='timestamp',
                    direction='nearest'
                )
            
            # Merge CAMS
            if not df_cams.empty:
                df_cams['timestamp'] = pd.to_datetime(df_cams['timestamp']).dt.tz_localize(None)
                df = pd.merge_asof(
                    df.sort_values('timestamp'),
                    df_cams.sort_values('timestamp'),
                    on='timestamp',
                    direction='nearest'
                )
            
            # Merge Weather
            if not df_weather.empty:
                df_weather['timestamp'] = pd.to_datetime(df_weather['timestamp']).dt.tz_localize(None)
                df = pd.merge_asof(
                    df.sort_values('timestamp'),
                    df_weather.sort_values('timestamp'),
                    on='timestamp',
                    direction='nearest'
                )
            
            logger.info(f"🔗 Combined dataset: {len(df)} records with {len(df.columns)} features")
            
            return df
            
        except Exception as e:
            logger.error(f"❌ Error loading multi-source data: {e}")
            raise
    
    def add_temporal_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add temporal features with cyclical encoding"""
        
        logger.info("⏰ Adding temporal features...")
        
        df = df.copy()
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        # Basic temporal features
        df['hour'] = df['timestamp'].dt.hour
        df['day'] = df['timestamp'].dt.day
        df['month'] = df['timestamp'].dt.month
        df['day_of_year'] = df['timestamp'].dt.dayofyear
        df['week_of_year'] = df['timestamp'].dt.isocalendar().week
        df['weekday'] = df['timestamp'].dt.weekday
        
        # Cyclical encoding
        df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
        df['day_sin'] = np.sin(2 * np.pi * df['day_of_year'] / 365.25)
        df['day_cos'] = np.cos(2 * np.pi * df['day_of_year'] / 365.25)
        df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
        
        # Season encoding
        df['season'] = ((df['month'] - 1) // 3) % 4  # 0=Winter, 1=Spring, 2=Summer, 3=Autumn
        
        self.temporal_features = [
            'hour', 'day', 'month', 'day_of_year', 'week_of_year', 'weekday',
            'hour_sin', 'hour_cos', 'day_sin', 'day_cos', 'month_sin', 'month_cos', 'season'
        ]
        
        logger.info(f"✅ Added {len(self.temporal_features)} temporal features")
        return df
    
    def add_astronomical_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add solar geometry features"""
        
        logger.info("🌞 Adding astronomical features...")
        
        df = df.copy()
        
        # Solar declination
        day_of_year = df['day_of_year']
        declination = 23.45 * np.sin(np.radians(360 * (284 + day_of_year) / 365))
        
        # Hour angle
        hour_angle = 15 * (df['hour'] - 12)
        
        # Solar elevation
        lat_rad = np.radians(self.latitude)
        elevation = np.degrees(np.arcsin(
            np.sin(lat_rad) * np.sin(np.radians(declination)) +
            np.cos(lat_rad) * np.cos(np.radians(declination)) * 
            np.cos(np.radians(hour_angle))
        ))
        
        # Solar azimuth
        azimuth = np.degrees(np.arctan2(
            np.sin(np.radians(hour_angle)),
            np.cos(np.radians(hour_angle)) * np.sin(lat_rad) - 
            np.tan(np.radians(declination)) * np.cos(lat_rad)
        ))
        
        # Solar features
        df['solar_declination'] = declination
        df['solar_elevation'] = elevation
        df['solar_azimuth'] = azimuth
        df['solar_elevation_factor'] = np.maximum(0, np.sin(np.radians(elevation)))
        
        # Day length
        df['day_length'] = 2 * np.degrees(np.arccos(-np.tan(lat_rad) * np.tan(np.radians(declination)))) / 15
        df['day_length'] = np.clip(df['day_length'], 0, 24)
        
        # Extraterrestrial radiation
        earth_sun_distance = 1 + 0.033 * np.cos(2 * np.pi * day_of_year / 365)
        df['extraterrestrial_radiation'] = (
            self.SOLAR_CONSTANT * earth_sun_distance * df['solar_elevation_factor']
        )
        
        self.astronomical_features = [
            'solar_declination', 'solar_elevation', 'solar_azimuth', 'solar_elevation_factor',
            'day_length', 'extraterrestrial_radiation'
        ]
        
        logger.info(f"✅ Added {len(self.astronomical_features)} astronomical features")
        return df
    
    def add_weather_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add enhanced weather features"""
        
        logger.info("🌤️ Adding weather features...")
        
        df = df.copy()
        
        # Primary GHI source (prioritize NASA POWER)
        df['ghi_primary'] = df.get('nasa_ghi', df.get('weather_ghi', df.get('cams_ghi', 0)))
        
        # Primary temperature source
        df['temperature_primary'] = df.get('nasa_temperature', df.get('weather_temperature', df.get('era5_temperature', 20)))
        
        # Wind speed
        df['wind_speed_primary'] = df.get('nasa_wind_speed', df.get('weather_wind_speed', df.get('era5_wind_speed', 2)))
        
        # Cloud cover
        df['cloud_cover_primary'] = df.get('weather_cloud_cover', df.get('era5_cloud_cover', df.get('cams_cloud_cover', 50)))
        
        # Clear sky index
        if 'nasa_clear_sky_ghi' in df.columns and 'nasa_ghi' in df.columns:
            df['clear_sky_index'] = np.where(
                df['nasa_clear_sky_ghi'] > 0,
                df['nasa_ghi'] / df['nasa_clear_sky_ghi'],
                0
            )
        else:
            df['clear_sky_index'] = 0.5  # Default
        
        # Module temperature (NOCT model)
        df['module_temperature'] = np.where(
            df['ghi_primary'] > 0,
            df['temperature_primary'] + (df['ghi_primary'] / 800.0) * (self.NOCT - 20.0),
            df['temperature_primary']
        )
        
        # Temperature efficiency factor
        df['temperature_efficiency'] = np.maximum(
            0.5, 1 + self.TEMP_COEFF * (df['module_temperature'] - 25.0)
        )
        
        # Wind cooling factor
        df['wind_cooling_factor'] = 1.0 + (df['wind_speed_primary'] * 0.02)
        df['wind_cooling_factor'] = np.minimum(1.3, df['wind_cooling_factor'])
        
        self.weather_features = [
            'ghi_primary', 'temperature_primary', 'wind_speed_primary', 'cloud_cover_primary',
            'clear_sky_index', 'module_temperature', 'temperature_efficiency', 'wind_cooling_factor'
        ]
        
        logger.info(f"✅ Added {len(self.weather_features)} weather features")
        return df
    
    def add_physics_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add physics-based features"""
        
        logger.info("⚗️ Adding physics features...")
        
        df = df.copy()
        
        # Air mass (simplified)
        df['air_mass'] = np.where(
            df['solar_elevation'] > 0,
            1 / np.cos(np.radians(90 - df['solar_elevation'])),
            10
        )
        df['air_mass'] = np.minimum(10, df['air_mass'])
        
        # Theoretical clear sky GHI
        df['theoretical_clear_sky_ghi'] = (
            df['extraterrestrial_radiation'] * 
            np.power(0.7, np.power(df['air_mass'], 0.678))
        )
        
        # Cloud attenuation factor
        df['cloud_attenuation'] = (100 - df['cloud_cover_primary']) / 100
        
        # Expected GHI with clouds
        df['expected_ghi'] = df['theoretical_clear_sky_ghi'] * df['cloud_attenuation']
        
        # GHI efficiency (actual vs expected)
        df['ghi_efficiency'] = np.where(
            df['expected_ghi'] > 0,
            df['ghi_primary'] / df['expected_ghi'],
            0
        )
        
        # Combined efficiency factor
        df['combined_efficiency'] = (
            df['temperature_efficiency'] * 
            df['wind_cooling_factor'] * 
            df['solar_elevation_factor']
        )
        
        self.physics_features = [
            'air_mass', 'theoretical_clear_sky_ghi', 'cloud_attenuation',
            'expected_ghi', 'ghi_efficiency', 'combined_efficiency'
        ]
        
        logger.info(f"✅ Added {len(self.physics_features)} physics features")
        return df
    
    def add_lag_features(self, df: pd.DataFrame, lag_hours: List[int] = [1, 2, 3, 6, 12, 24]) -> pd.DataFrame:
        """Add lag features for time series patterns"""
        
        logger.info(f"📈 Adding lag features for {lag_hours} hours...")
        
        df = df.copy()
        df = df.sort_values('timestamp')
        
        # Key variables for lag features
        lag_variables = ['ghi_primary', 'ac_power', 'yield_today', 'temperature_primary', 'cloud_cover_primary']
        
        for var in lag_variables:
            if var in df.columns:
                for lag in lag_hours:
                    df[f'{var}_lag_{lag}h'] = df[var].shift(lag * 4)  # Assuming 15-min intervals
                    self.lag_features.append(f'{var}_lag_{lag}h')
        
        # Rolling features
        for var in lag_variables:
            if var in df.columns:
                df[f'{var}_rolling_3h'] = df[var].rolling(12).mean()  # 3 hours
                df[f'{var}_rolling_6h'] = df[var].rolling(24).mean()  # 6 hours
                self.lag_features.extend([f'{var}_rolling_3h', f'{var}_rolling_6h'])
        
        logger.info(f"✅ Added {len(self.lag_features)} lag features")
        return df
    
    def create_enhanced_features(self, start_date: datetime, end_date: datetime, system_id: int = 1) -> pd.DataFrame:
        """Create complete enhanced feature set"""
        
        logger.info(f"🚀 Creating enhanced features for System {system_id}")
        
        # Load multi-source data
        df = self.load_multi_source_data(start_date, end_date, system_id)
        
        # Add all feature groups
        df = self.add_temporal_features(df)
        df = self.add_astronomical_features(df)
        df = self.add_weather_features(df)
        df = self.add_physics_features(df)
        df = self.add_lag_features(df)
        
        # Feature summary
        all_features = (
            self.temporal_features + 
            self.astronomical_features + 
            self.weather_features + 
            self.physics_features + 
            self.lag_features
        )
        
        logger.info(f"🎯 Enhanced feature engineering completed:")
        logger.info(f"   Total features: {len(all_features)}")
        logger.info(f"   Temporal: {len(self.temporal_features)}")
        logger.info(f"   Astronomical: {len(self.astronomical_features)}")
        logger.info(f"   Weather: {len(self.weather_features)}")
        logger.info(f"   Physics: {len(self.physics_features)}")
        logger.info(f"   Lag: {len(self.lag_features)}")
        logger.info(f"   Records: {len(df)}")
        
        return df


def main():
    """Test enhanced feature engineering"""
    
    print("🔧 Enhanced Feature Engineering Test")
    print("=" * 40)
    
    # Initialize feature engineer
    engineer = EnhancedFeatureEngineer()
    
    # Test with recent data
    end_date = datetime.now()
    start_date = end_date - timedelta(days=7)  # Last week
    
    print(f"🧪 Testing feature engineering: {start_date.date()} to {end_date.date()}")
    
    try:
        # Create enhanced features
        df = engineer.create_enhanced_features(start_date, end_date, system_id=1)
        
        print(f"\n📊 Feature Engineering Results:")
        print(f"   Records: {len(df)}")
        print(f"   Features: {len(df.columns)}")
        print(f"   Date range: {df['timestamp'].min()} to {df['timestamp'].max()}")
        
        # Show feature groups
        print(f"\n🎯 Feature Groups:")
        print(f"   Temporal: {len(engineer.temporal_features)}")
        print(f"   Astronomical: {len(engineer.astronomical_features)}")
        print(f"   Weather: {len(engineer.weather_features)}")
        print(f"   Physics: {len(engineer.physics_features)}")
        print(f"   Lag: {len(engineer.lag_features)}")
        
        # Show sample features
        print(f"\n📋 Sample Features:")
        key_features = ['ghi_primary', 'temperature_primary', 'solar_elevation', 'combined_efficiency']
        for feature in key_features:
            if feature in df.columns:
                values = df[feature].dropna()
                if len(values) > 0:
                    print(f"   {feature}: {values.mean():.2f} ± {values.std():.2f}")
        
        print("\n✅ Enhanced feature engineering test successful!")
        print("   → Ready for ML model training")
        print("   → Multiple data sources integrated")
        print("   → Physics-based features included")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Feature engineering test failed: {e}")
        print("   → Check data availability")
        print("   → Verify database connections")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
