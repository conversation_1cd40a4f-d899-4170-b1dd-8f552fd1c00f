#!/usr/bin/env python3
"""
Enhanced Model Training Script - Fixed with CORRECT Column Names from Database
Based on actual database schema and error analysis
"""

import sys
import os
from typing import Dict, Any

sys.path.append('/home/<USER>/solar-prediction-project')

def train_enhanced_model_fallback(system_id=1):
    """Fallback training with CORRECT column names from actual database schema"""
    print(f"🔄 Using fallback training for System {system_id}")
    
    try:
        from datetime import datetime, timedelta
        import pandas as pd
        import numpy as np
        from sklearn.ensemble import GradientBoostingRegressor
        from sklearn.model_selection import train_test_split
        from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
        import joblib
        import json
        import psycopg2
        
        # Correct database configuration with environment variables
        db_config = {
            'host': os.getenv('DB_HOST', 'solar-prediction-db'),
            'port': int(os.getenv('DB_PORT', 5432)),
            'database': os.getenv('DB_NAME', 'solar_prediction'),
            'user': os.getenv('DB_USER', 'postgres'),
            'password': os.getenv('DB_PASSWORD', 'postgres')
        }
        
        # Test database connection
        try:
            conn = psycopg2.connect(**db_config)
            print("✅ Database connection successful")
            conn.close()
        except Exception as e:
            print(f"❌ Database connection failed: {e}")
            return False
        
        # Create training data using CORRECT column names from error hint
        end_date = datetime.now()
        start_date = end_date - timedelta(days=90)  # 3 months of data
        
        conn = psycopg2.connect(**db_config)
        
        # Use CORRECT column names based on error message hint
        query = """
        SELECT 
            timestamp,
            ac_power,
            EXTRACT(hour FROM timestamp) as hour,
            EXTRACT(day FROM timestamp) as day,
            temperature,
            soc,
            bat_power,
            yield_today,
            powerdc1,
            powerdc2,
            system_id
        FROM solax_unified_data 
        WHERE system_id = %s 
        AND timestamp BETWEEN %s AND %s
        AND ac_power IS NOT NULL
        ORDER BY timestamp
        """
        
        try:
            df = pd.read_sql_query(query, conn, params=(system_id, start_date, end_date))
            table_used = "solax_unified_data"
            
            conn.close()
            
            if len(df) < 100:
                print(f"❌ Insufficient training data: {len(df)} records from {table_used}")
                return False
            
            print(f"📊 Loaded {len(df)} training records from {table_used}")
            print(f"📋 Available columns: {list(df.columns)}")
            
            # Create features
            df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
            df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
            df['day_sin'] = np.sin(2 * np.pi * df['day'] / 365)
            df['day_cos'] = np.cos(2 * np.pi * df['day'] / 365)
            
            # Feature columns based on available data
            feature_columns = [
                'hour_sin', 'hour_cos', 'day_sin', 'day_cos',
                'temperature', 'soc', 'bat_power'
            ]
            
            # Add additional features if available
            if 'powerdc1' in df.columns:
                feature_columns.append('powerdc1')
            if 'powerdc2' in df.columns:
                feature_columns.append('powerdc2')
            if 'yield_today' in df.columns:
                feature_columns.append('yield_today')
            
            # Filter available features
            available_features = [col for col in feature_columns if col in df.columns and df[col].notna().sum() > 50]
            
            if len(available_features) < 4:
                print(f"❌ Insufficient features: {len(available_features)}")
                print(f"📋 Available features: {available_features}")
                return False
            
            print(f"📋 Using features: {available_features}")
            
            X = df[available_features].fillna(0)
            y = df['ac_power'].fillna(0)
            
            # Remove zero power records for training
            mask = y > 0
            X = X[mask]
            y = y[mask]
            
            if len(X) < 50:
                print(f"❌ Insufficient non-zero training data: {len(X)} records")
                return False
            
            # Train-test split
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42
            )
            
            # Train model with GPU acceleration if available
            try:
                import lightgbm as lgb
                print("🚀 Using LightGBM with GPU acceleration")
                model = lgb.LGBMRegressor(
                    n_estimators=200,
                    learning_rate=0.1,
                    max_depth=6,
                    random_state=42,
                    device='gpu',
                    gpu_platform_id=0,
                    gpu_device_id=0,
                    objective='regression',
                    metric='rmse',
                    verbosity=-1
                )
            except Exception as gpu_error:
                print(f"⚠️ GPU not available, using CPU: {gpu_error}")
                from sklearn.ensemble import GradientBoostingRegressor
                model = GradientBoostingRegressor(
                    n_estimators=100,
                    learning_rate=0.1,
                    max_depth=6,
                    random_state=42
                )
            
            print("🔧 Training model...")
            model.fit(X_train, y_train)
            
            # Evaluate
            y_pred = model.predict(X_test)
            mae = mean_absolute_error(y_test, y_pred)
            rmse = np.sqrt(mean_squared_error(y_test, y_pred))
            r2 = r2_score(y_test, y_pred)
            
            print(f"📊 Model Performance:")
            print(f"   MAE: {mae:.2f} W")
            print(f"   RMSE: {rmse:.2f} W")
            print(f"   R²: {r2:.4f}")
            
            # Save model
            os.makedirs("/home/<USER>/solar-prediction-project/models", exist_ok=True)
            model_path = f"/home/<USER>/solar-prediction-project/models/enhanced_model_system_{system_id}.joblib"
            joblib.dump(model, model_path)
            
            # Save metadata
            metadata = {
                'model_type': 'enhanced_gradient_boosting_fallback',
                'system_id': system_id,
                'training_date': datetime.now().isoformat(),
                'features': available_features,
                'performance': {'mae': mae, 'rmse': rmse, 'r2': r2},
                'training_samples': len(X_train),
                'test_samples': len(X_test),
                'database_config': 'corrected_port_5433',
                'table_used': table_used,
                'data_period': f"{start_date.date()} to {end_date.date()}",
                'column_names_used': list(df.columns),
                'corrected_column_names': 'ac_power, bat_power, yield_today, powerdc1, powerdc2, system_id'
            }
            
            with open(f"/home/<USER>/solar-prediction-project/models/enhanced_model_system_{system_id}_metadata.json", 'w') as f:
                json.dump(metadata, f, indent=2)
            
            print(f"✅ Enhanced model saved: {model_path}")
            return True
            
        except Exception as e:
            print(f"❌ Data loading failed: {e}")
            print(f"📋 Attempted query with corrected column names")
            conn.close()
            return False
            
    except Exception as e:
        print(f"❌ Fallback training failed: {e}")
        return False

def train_enhanced_model(system_id=1):
    print(f"🚀 Training enhanced model for System {system_id} (COLUMN-FIXED)")
    print("📋 Using correct database schema and column names")
    
    try:
        # Try production training first
        from scripts.training.train_all_models_unified import MasterUnifiedTrainer
        
        trainer = MasterUnifiedTrainer(
            output_dir=f"models/enhanced_system_{system_id}",
            pipeline_version="v2.0.0"
        )
        
        results = trainer.train_all_models(
            train_multi_horizon=True,
            train_seasonal=True
        )
        
        if results and results.get('overall_summary', {}).get('total_models_trained', 0) > 0:
            print(f"✅ Enhanced models trained successfully for System {system_id}")
            return True
        else:
            return train_enhanced_model_fallback(system_id)
            
    except Exception as e:
        print(f"❌ Production training failed: {e}")
        return train_enhanced_model_fallback(system_id)

if __name__ == "__main__":
    print("🌞 Enhanced Solar Model Training (Column-Fixed)")
    print("=" * 55)
    print("🎯 Target: Enhanced ML models with correct column names")
    print("🔧 Pipeline: Schema-aware training with corrected column mapping")
    print("=" * 55)
    print()
    
    success_count = 0
    for system_id in [1, 2]:
        try:
            if train_enhanced_model(system_id):
                success_count += 1
        except Exception as e:
            print(f"❌ Training failed for System {system_id}: {e}")
        print()
    
    print(f"🎉 Training completed: {success_count}/2 systems successful")
    if success_count == 2:
        print("✅ All systems trained successfully!")
    elif success_count > 0:
        print("⚠️ Some systems trained successfully")
    else:
        print("❌ All training attempts failed")

