#!/usr/bin/env python3
"""
Production Solar Prediction API
Complete FastAPI application with background tasks and Telegram bot
"""
import os
import json
import asyncio
import subprocess
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from pydantic import BaseModel
import psycopg2
from psycopg2.extras import RealDictCursor

# Telegram Bot imports
import sys
import pytz
sys.path.append('/home/<USER>/solar-prediction-project')

# Configuration
DATABASE_URL = "postgresql://postgres:postgres@localhost:5433/solar_prediction"
WEATHER_API_URL = "https://api.open-meteo.com/v1/forecast"
SOLAX_API_URL = "https://www.solaxcloud.com:9443/proxy/api/getRealtimeInfo.do"
SOLAX_TOKEN_ID = "20250410220826567911082"

# Telegram Bot Configuration
BOT_TOKEN = "**********************************************"
CHAT_ID = "1510889515"

# Dual SolaX Systems Configuration
SOLAX_SYSTEMS = {
    1: {
        "name": "Σπίτι Πάνω",
        "wifi_sn": "SRFQDPDN9W",
        "table": "solax_data"
    },
    2: {
        "name": "Σπίτι Κάτω",
        "wifi_sn": "SRCV9TUD6S",
        "table": "solax_data2"
    }
}

# Background task state
background_tasks_running = False
background_task_handle = None
telegram_bot_running = False
telegram_bot_handle = None
telegram_application = None

class PredictionRequest(BaseModel):
    temperature: Optional[float] = None
    cloud_cover: Optional[float] = None
    soc: Optional[float] = 75.0
    hour: Optional[int] = None

class PredictionResponse(BaseModel):
    predicted_power: float
    confidence: float
    timestamp: str
    model_version: str
    inputs: Dict[str, Any]

class WeatherResponse(BaseModel):
    temperature: float
    cloud_cover: float
    humidity: Optional[float] = None
    timestamp: str
    source: str



def get_db_connection():
    """Get database connection"""
    try:
        return psycopg2.connect(DATABASE_URL)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database connection failed: {e}")

def call_weather_api() -> Dict[str, Any]:
    """Call Weather API using curl"""
    try:
        cmd = [
            "curl", "--tlsv1.2", "-s", "--connect-timeout", "10", "--max-time", "15",
            f"{WEATHER_API_URL}?latitude=37.9755&longitude=23.7348&current=temperature_2m,cloud_cover,relative_humidity_2m&timezone=Europe/Athens"
        ]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=20)

        if result.returncode == 0:
            data = json.loads(result.stdout)
            current = data.get("current", {})
            return {
                "success": True,
                "temperature": current.get("temperature_2m"),
                "cloud_cover": current.get("cloud_cover"),
                "humidity": current.get("relative_humidity_2m"),
                "timestamp": current.get("time")
            }
        else:
            return {"success": False, "error": "API call failed"}

    except Exception as e:
        return {"success": False, "error": str(e)}

def call_solax_api(wifi_sn: str) -> Dict[str, Any]:
    """Call SolaX Cloud API using curl for specific system"""
    try:
        # Prepare form data (not JSON!)
        form_data = f"tokenId={SOLAX_TOKEN_ID}&sn={wifi_sn}"

        cmd = [
            "curl", "--tlsv1.2", "-s", "--connect-timeout", "10", "--max-time", "15",
            "-H", "Content-Type: application/x-www-form-urlencoded",
            "-X", "POST",
            "-d", form_data,
            SOLAX_API_URL
        ]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=20)

        if result.returncode == 0:
            data = json.loads(result.stdout)

            if data.get("success"):
                result_data = data.get("result", {})
                return {
                    "success": True,
                    "data": result_data,
                    "timestamp": datetime.now().isoformat(),
                    "wifi_sn": wifi_sn
                }
            else:
                error_msg = data.get("exception", "Unknown SolaX API error")
                return {"success": False, "error": error_msg, "wifi_sn": wifi_sn}
        else:
            return {"success": False, "error": "SolaX API call failed", "wifi_sn": wifi_sn}

    except Exception as e:
        return {"success": False, "error": str(e), "wifi_sn": wifi_sn}

def collect_all_solax_systems() -> Dict[int, Dict[str, Any]]:
    """Collect data from all SolaX systems"""
    results = {}

    for system_id, system_config in SOLAX_SYSTEMS.items():
        try:
            result = call_solax_api(system_config["wifi_sn"])
            result["system_id"] = system_id
            result["system_name"] = system_config["name"]
            result["table"] = system_config["table"]
            results[system_id] = result
        except Exception as e:
            results[system_id] = {
                "success": False,
                "error": str(e),
                "system_id": system_id,
                "system_name": system_config["name"],
                "table": system_config["table"],
                "wifi_sn": system_config["wifi_sn"]
            }

    return results

def make_prediction(inputs: Dict[str, Any]) -> Dict[str, Any]:
    """Generate solar power prediction"""
    try:
        temp = inputs.get("temperature", 25)
        clouds = inputs.get("cloud_cover", 50)
        soc = inputs.get("soc", 75)
        hour = inputs.get("hour", datetime.now().hour)

        # Solar power estimation
        if 6 <= hour <= 18:  # Daylight hours
            # Calculate GHI estimate
            base_ghi = 800 * max(0.1, 1.0 - clouds / 100.0)

            # Temperature coefficient (panels lose efficiency in heat)
            temp_factor = max(0.8, 1.0 - (temp - 25) * 0.004)

            # Battery factor (reduce output if battery is full)
            soc_factor = 0.8 if soc > 90 else 1.0

            # System efficiency and panel rating
            system_efficiency = 0.85
            panel_rating = 6000  # 6kW system

            predicted_power = (base_ghi / 1000) * panel_rating * temp_factor * soc_factor * system_efficiency
            confidence = 0.85 if base_ghi > 200 else 0.65
        else:
            predicted_power = 0.0
            confidence = 0.95

        return {
            "predicted_power": max(0, predicted_power),
            "confidence": confidence,
            "model_version": "production_heuristic_v1.0",
            "timestamp": datetime.now().isoformat(),
            "inputs": inputs
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Prediction failed: {e}")

async def background_data_collection():
    """Background task for periodic data collection"""
    global background_tasks_running

    weather_counter = 0  # Counter for weather collection frequency (every 30 iterations = 15 minutes)

    while background_tasks_running:
        try:
            # Collect SolaX data from all systems every 30 seconds
            all_solax_results = collect_all_solax_systems()

            for system_id, solax_result in all_solax_results.items():
                if solax_result["success"]:
                    try:
                        conn = get_db_connection()
                        with conn.cursor() as cur:
                            timestamp = datetime.now()
                            solax_data = solax_result["data"]
                            table_name = solax_result["table"]

                            cur.execute(f"""
                                INSERT INTO {table_name} (timestamp, inverter_sn, wifi_sn,
                                                      ac_power, soc, bat_power, temperature,
                                                      powerdc1, powerdc2, yield_today, yield_total,
                                                      feedin_power, feedin_energy, consume_energy,
                                                      created_at, raw_data)
                                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                            """, (
                                timestamp,
                                solax_data.get("inverterSN"),
                                solax_data.get("wifiSN"),
                                solax_data.get("acpower"),
                                solax_data.get("soc"),
                                solax_data.get("batPower"),
                                solax_data.get("temperature"),
                                solax_data.get("powerdc1"),
                                solax_data.get("powerdc2"),
                                solax_data.get("yieldtoday"),
                                solax_data.get("yieldtotal"),
                                solax_data.get("feedinpower"),
                                solax_data.get("feedinenergy"),
                                solax_data.get("consumeenergy"),
                                timestamp,
                                json.dumps(solax_result)
                            ))
                            conn.commit()
                        conn.close()

                        print(f"Background: {solax_result['system_name']} data saved - {solax_data.get('acpower', 0)}W, SOC: {solax_data.get('soc', 0)}%")

                    except Exception as db_error:
                        print(f"Background: {solax_result['system_name']} database save failed - {db_error}")
                else:
                    print(f"Background: {solax_result['system_name']} API failed - {solax_result.get('error', 'Unknown error')}")

            # Collect weather data every 15 minutes (30 iterations * 30 seconds = 15 minutes)
            if weather_counter % 30 == 0:
                weather_result = call_weather_api()

                if weather_result["success"]:
                    # Save to database
                    try:
                        conn = get_db_connection()
                        with conn.cursor() as cur:
                            timestamp = datetime.now()

                            cur.execute("""
                                INSERT INTO weather_data (timestamp, temperature_2m, relative_humidity_2m,
                                                        cloud_cover, is_forecast, created_at, raw_data)
                                VALUES (%s, %s, %s, %s, %s, %s, %s)
                            """, (
                                timestamp,
                                weather_result["temperature"],
                                weather_result["humidity"],
                                weather_result["cloud_cover"],
                                False,
                                timestamp,
                                json.dumps(weather_result)
                            ))
                            conn.commit()
                        conn.close()

                        print(f"Background: Weather data saved - {weather_result['temperature']}°C")

                    except Exception as db_error:
                        print(f"Background: Weather database save failed - {db_error}")

                # Generate prediction with latest data
                try:
                    # Use real SOC from System 1 if available, otherwise default
                    current_soc = 75  # Default
                    if 1 in all_solax_results and all_solax_results[1].get("success"):
                        current_soc = all_solax_results[1]["data"].get("soc", 75)

                    prediction_inputs = {
                        "temperature": weather_result.get("temperature", 25),
                        "cloud_cover": weather_result.get("cloud_cover", 50),
                        "soc": current_soc,
                        "hour": datetime.now().hour
                    }

                    prediction = make_prediction(prediction_inputs)

                    # Save prediction
                    conn = get_db_connection()
                    with conn.cursor() as cur:
                        cur.execute("""
                            INSERT INTO predictions (timestamp, predicted_ac_power, confidence_score,
                                                   model_version, input_features, prediction_time_ms)
                            VALUES (%s, %s, %s, %s, %s, %s)
                        """, (
                            datetime.now(),
                            prediction["predicted_power"],
                            prediction["confidence"],
                            prediction["model_version"],
                            json.dumps(prediction["inputs"]),
                            5.0
                        ))
                        conn.commit()
                    conn.close()

                    print(f"Background: Prediction saved - {prediction['predicted_power']:.1f}W")

                except Exception as pred_error:
                    print(f"Background: Prediction failed - {pred_error}")

        except Exception as e:
            print(f"Background task error: {e}")

        # Increment counter and wait
        weather_counter += 1

        # Wait 30 seconds before next iteration
        await asyncio.sleep(30)

async def telegram_bot_task():
    """Background task for Telegram bot - Disabled (handled by startup script)"""
    global telegram_bot_running

    try:
        print("🤖 Telegram Bot management disabled - handled by startup script")
        print("✅ Telegram Bot task initialized (external management)")

        # Just wait and monitor the flag
        while telegram_bot_running:
            await asyncio.sleep(10)

    except Exception as e:
        print(f"❌ Telegram Bot task error: {e}")
        telegram_bot_running = False

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    global background_tasks_running, background_task_handle, telegram_bot_running, telegram_bot_handle

    # Startup
    print("🚀 Starting Solar Prediction API")

    # Start data collection background task
    background_tasks_running = True
    background_task_handle = asyncio.create_task(background_data_collection())
    print("✅ Data collection background task started")

    # Start Telegram bot monitoring task (external bot managed by startup script)
    telegram_bot_running = True
    telegram_bot_handle = asyncio.create_task(telegram_bot_task())
    print("✅ Telegram bot monitoring task started")

    yield

    # Shutdown
    print("🛑 Shutting down Solar Prediction API")

    # Stop data collection
    background_tasks_running = False
    if background_task_handle:
        background_task_handle.cancel()
        try:
            await background_task_handle
        except asyncio.CancelledError:
            pass

    # Stop Telegram bot
    telegram_bot_running = False
    if telegram_bot_handle:
        telegram_bot_handle.cancel()
        try:
            await telegram_bot_handle
        except asyncio.CancelledError:
            pass

    print("✅ All background tasks stopped")

# Initialize FastAPI app
app = FastAPI(
    title="Solar Prediction API",
    description="Production solar power prediction system",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
import os
static_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "static")
app.mount("/static", StaticFiles(directory=static_dir), name="static")

@app.get("/")
async def root():
    """Serve the main web interface"""
    static_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), "static", "index.html")
    return FileResponse(static_file)

@app.get("/admin")
async def admin_interface():
    """Admin interface"""
    admin_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), "static", "admin", "index.html")
    if os.path.exists(admin_file):
        return FileResponse(admin_file)
    else:
        return {"error": "Admin interface not found", "path": admin_file}

@app.get("/api")
async def api_root():
    """API root endpoint"""
    return {
        "message": "Solar Prediction API",
        "version": "1.0.0",
        "status": "running",
        "timestamp": datetime.now().isoformat(),
        "background_tasks": background_tasks_running,
        "telegram_bot": telegram_bot_running,
        "endpoints": {
            "health": "/health",
            "predict": "/api/v1/predict",
            "weather": "/api/v1/weather/current",
            "predictions": "/api/v1/predictions/recent"
        }
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Test database
        conn = get_db_connection()
        with conn.cursor() as cur:
            cur.execute("SELECT 1")
        conn.close()
        db_status = "healthy"

        # Test weather API
        weather_result = call_weather_api()
        weather_status = "healthy" if weather_result["success"] else "unhealthy"

        # Use timezone-aware timestamp
        import pytz
        greek_tz = pytz.timezone('Europe/Athens')
        greek_time = datetime.now(greek_tz)

        return {
            "status": "healthy",
            "timestamp": greek_time.isoformat(),
            "services": {
                "database": db_status,
                "weather_api": weather_status,
                "background_tasks": background_tasks_running,
                "telegram_bot": telegram_bot_running
            },
            "current_weather": weather_result if weather_result["success"] else None
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Health check failed: {e}")

@app.post("/api/v1/predict", response_model=PredictionResponse)
async def predict_power(request: PredictionRequest):
    """Generate solar power prediction"""
    try:
        # Get current weather if not provided
        if request.temperature is None or request.cloud_cover is None:
            weather_result = call_weather_api()
            if weather_result["success"]:
                temp = request.temperature or weather_result["temperature"]
                clouds = request.cloud_cover or weather_result["cloud_cover"]
            else:
                temp = request.temperature or 25
                clouds = request.cloud_cover or 50
        else:
            temp = request.temperature
            clouds = request.cloud_cover

        # Prepare inputs
        inputs = {
            "temperature": temp,
            "cloud_cover": clouds,
            "soc": request.soc,
            "hour": request.hour or datetime.now().hour
        }

        # Generate prediction
        prediction = make_prediction(inputs)

        # Save to database
        try:
            conn = get_db_connection()
            with conn.cursor() as cur:
                cur.execute("""
                    INSERT INTO predictions (timestamp, predicted_ac_power, confidence_score,
                                           model_version, input_features, prediction_time_ms)
                    VALUES (%s, %s, %s, %s, %s, %s)
                """, (
                    datetime.now(),
                    prediction["predicted_power"],
                    prediction["confidence"],
                    prediction["model_version"],
                    json.dumps(inputs),
                    5.0
                ))
                conn.commit()
            conn.close()
        except Exception as db_error:
            print(f"Database save failed: {db_error}")

        return PredictionResponse(
            predicted_power=prediction["predicted_power"],
            confidence=prediction["confidence"],
            timestamp=prediction["timestamp"],
            model_version=prediction["model_version"],
            inputs=inputs
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Prediction failed: {e}")

@app.get("/api/v1/weather/current", response_model=WeatherResponse)
async def get_current_weather():
    """Get current weather data"""
    weather_result = call_weather_api()

    if weather_result["success"]:
        return WeatherResponse(
            temperature=weather_result["temperature"],
            cloud_cover=weather_result["cloud_cover"],
            humidity=weather_result["humidity"],
            timestamp=weather_result["timestamp"],
            source="open_meteo"
        )
    else:
        raise HTTPException(status_code=503, detail="Weather API unavailable")

@app.get("/api/v1/solax/current")
async def get_current_solax_data():
    """Get current SolaX data from all systems"""
    all_results = collect_all_solax_systems()

    success_count = sum(1 for result in all_results.values() if result["success"])

    if success_count > 0:
        return {
            "status": "success",
            "systems": all_results,
            "success_count": success_count,
            "total_systems": len(all_results),
            "timestamp": datetime.now().isoformat(),
            "source": "solax_cloud"
        }
    else:
        raise HTTPException(status_code=503, detail="All SolaX systems unavailable")

@app.get("/api/v1/predictions/recent")
async def get_recent_predictions():
    """Get recent predictions"""
    try:
        conn = get_db_connection()
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute("""
                SELECT * FROM predictions
                ORDER BY timestamp DESC
                LIMIT 20
            """)
            predictions = cur.fetchall()
        conn.close()

        return {
            "status": "success",
            "count": len(predictions),
            "data": [dict(pred) for pred in predictions],
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database query failed: {e}")

# Admin API Endpoints
@app.get("/api/v1/admin/database/info")
async def get_admin_database_info():
    """Get database information for admin interface"""
    try:
        conn = get_db_connection()
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            # Get table information
            table_names = [
                "solax_data", "weather_data", "predictions",
                "system_logs", "model_metrics", "schedule_tasks",
                "api_sources"
            ]

            tables_info = []
            total_records = 0

            for table_name in table_names:
                try:
                    cur.execute(f"SELECT COUNT(*) as count FROM {table_name}")
                    count_result = cur.fetchone()
                    count = count_result['count'] if count_result else 0

                    # Get last updated (approximate)
                    last_updated = None
                    if table_name in ["solax_data", "weather_data", "predictions"]:
                        try:
                            cur.execute(f"SELECT MAX(timestamp) as last_updated FROM {table_name}")
                            last_result = cur.fetchone()
                            last_updated = last_result['last_updated'] if last_result else None
                        except:
                            pass

                    tables_info.append({
                        "table_name": table_name,
                        "record_count": count,
                        "size_mb": 0.0,  # Would need actual size calculation
                        "health_status": "healthy" if count >= 0 else "error",
                        "last_updated": last_updated.isoformat() if last_updated else None
                    })
                    total_records += count

                except Exception as e:
                    tables_info.append({
                        "table_name": table_name,
                        "record_count": 0,
                        "size_mb": 0.0,
                        "health_status": "error",
                        "last_updated": None
                    })

        conn.close()

        return {
            "status": "success",
            "data": {
                "total_tables": len(tables_info),
                "total_records": total_records,
                "total_size_mb": 0.0,
                "health_status": "healthy",
                "tables": tables_info
            },
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get database info: {e}")

@app.get("/api/v1/admin/database/tables")
async def get_admin_database_tables():
    """Get database tables for admin interface"""
    try:
        conn = get_db_connection()
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            # Get table information
            table_names = [
                "solax_data", "weather_data", "predictions",
                "system_logs", "model_metrics", "schedule_tasks",
                "api_sources"
            ]

            tables_data = []

            for table_name in table_names:
                try:
                    cur.execute(f"SELECT COUNT(*) as count FROM {table_name}")
                    count_result = cur.fetchone()
                    count = count_result['count'] if count_result else 0

                    # Get last updated (approximate)
                    last_updated = None
                    if table_name in ["solax_data", "weather_data", "predictions"]:
                        try:
                            cur.execute(f"SELECT MAX(timestamp) as last_updated FROM {table_name}")
                            last_result = cur.fetchone()
                            last_updated = last_result['last_updated'] if last_result else None
                        except:
                            pass

                    tables_data.append({
                        "table_name": table_name,
                        "record_count": count,
                        "size_mb": 0.0,
                        "health_status": "healthy" if count >= 0 else "error",
                        "last_updated": last_updated.isoformat() if last_updated else None,
                        "description": get_table_description(table_name)
                    })

                except Exception as e:
                    tables_data.append({
                        "table_name": table_name,
                        "record_count": 0,
                        "size_mb": 0.0,
                        "health_status": "error",
                        "last_updated": None,
                        "description": get_table_description(table_name)
                    })

        conn.close()

        return {
            "status": "success",
            "data": tables_data,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get database tables: {e}")

def get_table_description(table_name: str) -> str:
    """Get description for database table"""
    descriptions = {
        "solax_data": "Real-time solar system data from SolaX Cloud API",
        "weather_data": "Weather and radiation data from Open-Meteo API",
        "predictions": "ML model predictions for solar power output",
        "system_logs": "Application logs and system events",
        "model_metrics": "ML model performance metrics and training history",
        "schedule_tasks": "Background task scheduler configuration",
        "api_sources": "External API configuration and status"
    }
    return descriptions.get(table_name, "Database table")

@app.get("/api/v1/admin/schedules")
async def get_admin_schedules():
    """Get schedule tasks for admin interface"""
    try:
        conn = get_db_connection()
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute("""
                SELECT * FROM schedule_tasks
                ORDER BY task_name
            """)
            schedules = cur.fetchall()
        conn.close()

        return {
            "status": "success",
            "data": [dict(schedule) for schedule in schedules],
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get schedules: {e}")

@app.get("/api/v1/admin/status")
async def get_admin_status():
    """Get system status for admin interface"""
    try:
        # Test database
        conn = get_db_connection()
        with conn.cursor() as cur:
            cur.execute("SELECT 1")
        conn.close()
        db_status = "healthy"

        # Test weather API
        weather_result = call_weather_api()
        weather_status = "healthy" if weather_result["success"] else "unhealthy"

        # Check background tasks
        bg_status = "healthy" if background_tasks_running else "unhealthy"

        # Check Telegram bot
        telegram_status = "healthy" if telegram_bot_running else "unhealthy"

        # Overall status
        overall_status = "healthy"
        if weather_status != "healthy" or bg_status != "healthy" or telegram_status != "healthy":
            overall_status = "warning"
        if db_status != "healthy":
            overall_status = "unhealthy"

        return {
            "status": "success",
            "data": {
                "overall_status": overall_status,
                "services": {
                    "database": db_status,
                    "weather_api": weather_status,
                    "background_tasks": bg_status,
                    "telegram_bot": telegram_status
                },
                "uptime": "Running",
                "version": "1.0.0"
            },
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get system status: {e}")

# ROI and Billing Endpoints
@app.get("/api/v1/roi/{system_id}")
async def get_roi_analysis(system_id: str):
    """Get ROI analysis using clean data and dynamic billing"""
    try:
        # Import the billing calculator
        import sys
        sys.path.append('/home/<USER>/solar-prediction-project')
        from scripts.database.billing_calculator import BillingCalculator

        # Initialize calculator
        calculator = BillingCalculator()

        # Get data from database
        conn = get_db_connection()
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            # Determine table name
            table_name = "solax_data" if system_id == "system1" else "solax_data2"

            # Get daily data for the last year
            cur.execute(f"""
                WITH daily_data AS (
                    SELECT
                        DATE(timestamp) as date,
                        MAX(yield_today) - MIN(yield_today) as daily_production,
                        MAX(feedin_energy) - MIN(feedin_energy) as daily_export,
                        MAX(consume_energy) - MIN(consume_energy) as daily_consumption
                    FROM {table_name}
                    WHERE timestamp >= NOW() - INTERVAL '365 days'
                    GROUP BY DATE(timestamp)
                    HAVING MAX(yield_today) - MIN(yield_today) > 0
                )
                SELECT
                    date,
                    daily_production,
                    daily_export,
                    daily_consumption,
                    (daily_production - daily_export) as daily_self_consumption
                FROM daily_data
                ORDER BY date
            """)

            daily_data = cur.fetchall()
        conn.close()

        if not daily_data:
            return {"status": "error", "message": "No data available"}

        # Calculate totals
        total_production = sum(row['daily_production'] for row in daily_data)
        total_export = sum(row['daily_export'] for row in daily_data)
        total_self_consumption = sum(row['daily_self_consumption'] for row in daily_data)

        # Calculate savings using dynamic billing
        total_savings = 0
        for row in daily_data:
            # Use middle of day for rate calculation
            timestamp = datetime.combine(row['date'], datetime.min.time().replace(hour=12))
            rate_info = calculator.calculate_total_rate(timestamp)
            daily_savings = row['daily_self_consumption'] * rate_info['total_rate']
            total_savings += daily_savings

        # Calculate ROI
        investment_cost = 12500  # €12,500 per system
        roi_percentage = (total_savings / investment_cost) * 100
        payback_years = investment_cost / total_savings if total_savings > 0 else 0

        return {
            "status": "calculated",
            "system_id": system_id,
            "investment_cost_eur": investment_cost,
            "financial": {
                "annual_savings_eur": total_savings,
                "annual_roi_percent": roi_percentage,
                "payback_years": payback_years,
                "annual_benefit_eur": total_savings
            },
            "production": {
                "annual_production_kwh": total_production * 1000,  # Convert to Wh for compatibility
                "total_production": total_production
            },
            "consumption_analysis": {
                "self_consumption_kwh": total_self_consumption,
                "self_consumption_rate": (total_self_consumption / total_production * 100) if total_production > 0 else 0,
                "grid_consumption_rate": (total_export / total_production * 100) if total_production > 0 else 0
            },
            "calculation_method": "Clean Data + Dynamic Billing"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"ROI calculation failed: {e}")

@app.get("/api/v1/cost/{system_id}")
async def get_daily_cost(system_id: str, date: str = None):
    """Get daily cost using clean data and dynamic billing"""
    try:
        # Import the billing calculator
        import sys
        sys.path.append('/home/<USER>/solar-prediction-project')
        from scripts.database.billing_calculator import BillingCalculator
        from datetime import date as date_type

        # Use today if no date provided
        if date is None:
            target_date = datetime.now().date()
        else:
            target_date = datetime.strptime(date, '%Y-%m-%d').date()

        # Initialize calculator
        calculator = BillingCalculator()

        # Get data from database
        conn = get_db_connection()
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            # Determine table name
            table_name = "solax_data" if system_id == "system1" else "solax_data2"

            # Get daily data for the target date
            cur.execute(f"""
                SELECT
                    MAX(yield_today) - MIN(yield_today) as daily_production,
                    MAX(feedin_energy) - MIN(feedin_energy) as daily_export,
                    MAX(consume_energy) - MIN(consume_energy) as daily_consumption
                FROM {table_name}
                WHERE DATE(timestamp) = %s
                GROUP BY DATE(timestamp)
            """, (target_date,))

            day_data = cur.fetchone()
        conn.close()

        if not day_data or day_data['daily_production'] is None:
            return {"status": "error", "message": "No data available for this date"}

        # Calculate costs using dynamic billing
        timestamp = datetime.combine(target_date, datetime.min.time().replace(hour=12))
        rate_info = calculator.calculate_total_rate(timestamp)

        daily_production = day_data['daily_production']
        daily_export = day_data['daily_export']
        daily_consumption = day_data['daily_consumption']
        daily_self_consumption = daily_production - daily_export

        # Calculate costs
        energy_cost = daily_consumption * rate_info['total_rate']
        network_cost = 0  # Included in total rate
        surplus_value = daily_export * rate_info['base_energy_rate']  # Export at base rate
        net_cost = energy_cost - surplus_value

        return {
            "status": "calculated",
            "date": target_date.strftime('%Y-%m-%d'),
            "system_id": system_id,
            "cost_breakdown": {
                "energy_cost": energy_cost,
                "network_cost": network_cost,
                "surplus_value": surplus_value,
                "net_cost": net_cost
            },
            "tariff_info": {
                "period": "Winter" if calculator.is_winter(timestamp) else "Summer",
                "rate_used": rate_info['total_rate']
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Cost calculation failed: {e}")

@app.get("/api/v1/tariffs")
async def get_current_tariffs():
    """Get current tariff information"""
    try:
        # Import the billing calculator
        import sys
        sys.path.append('/home/<USER>/solar-prediction-project')
        from scripts.database.billing_calculator import BillingCalculator

        calculator = BillingCalculator()

        return {
            "energy_rates": {
                "day": calculator.winter_day_rate,  # Same for both seasons
                "night": calculator.winter_night_rate,
                "summer_night": calculator.summer_night_rate
            },
            "network_charges": {
                "tier1": calculator.network_tier1_rate,
                "tier2": calculator.network_tier2_rate,
                "tier1_limit": calculator.network_tier1_limit
            },
            "additional_charges": {
                "etmear": calculator.etmear_rate,
                "vat": calculator.vat_rate
            },
            "schedules": {
                "winter": {
                    "night": "23:00-07:00",
                    "day": "07:00-23:00"
                },
                "summer": {
                    "night": "23:30-07:00",
                    "day": "07:00-23:30"
                }
            },
            "net_metering": {
                "feed_in_tariff": calculator.winter_day_rate,  # Base rate for export
                "note": "Surplus is offset against consumption"
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Tariffs retrieval failed: {e}")

if __name__ == "__main__":
    import uvicorn
    print("🌞 Starting Production Solar Prediction API")
    uvicorn.run(app, host="0.0.0.0", port=8100, log_level="info")
