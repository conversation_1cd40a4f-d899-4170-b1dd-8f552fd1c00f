#!/usr/bin/env python3
"""
Direct Import - Χωρίς pandas, απευθείας με openpyxl
"""

import psycopg2
import json
import os
from datetime import datetime
import time

def direct_excel_import(file_path, system_id):
    """Direct import χωρίς pandas"""
    print(f"\n📥 DIRECT IMPORT: {os.path.basename(file_path)}")
    print(f"   🏠 System ID: {system_id}")
    
    try:
        # Import openpyxl μόνο όταν χρειάζεται
        from openpyxl import load_workbook
        
        print(f"   📖 Loading workbook...")
        wb = load_workbook(file_path, read_only=True, data_only=True)
        ws = wb.active
        
        print(f"   📊 Workbook loaded, max row: {ws.max_row}")
        
        # Βρίσκω headers (row 2 λόγω skiprows=1)
        headers = []
        for cell in ws[2]:
            if cell.value:
                headers.append(str(cell.value))
        
        print(f"   📋 Headers: {headers[:5]}...")
        
        # Βρίσκω timestamp column
        time_col_idx = None
        for i, header in enumerate(headers):
            if 'time' in header.lower() or 'date' in header.lower():
                time_col_idx = i
                break
        
        if time_col_idx is None:
            print(f"   ❌ No timestamp column found")
            return 0
        
        print(f"   📅 Time column: {headers[time_col_idx]} (index {time_col_idx})")
        
        # Database connection
        conn = psycopg2.connect(
            host="localhost",
            database="solar_prediction",
            user="postgres",
            password="postgres"
        )
        cursor = conn.cursor()
        
        target_table = "solax_data" if system_id == 1 else "solax_data2"
        print(f"   💾 Target table: {target_table}")
        
        # Process rows
        imported_count = 0
        error_count = 0
        batch_size = 100
        batch_count = 0
        
        print(f"   📦 Processing rows in batches of {batch_size}...")
        
        for row_num in range(3, min(ws.max_row + 1, 1000)):  # Limit to first 1000 rows for test
            try:
                row = ws[row_num]
                
                # Get timestamp
                timestamp_cell = row[time_col_idx]
                if not timestamp_cell.value:
                    error_count += 1
                    continue
                
                timestamp = timestamp_cell.value
                
                # Simple insert
                insert_sql = f"""
                    INSERT INTO {target_table} (timestamp, system_id, raw_data) 
                    VALUES (%s, %s, %s)
                """
                
                # Create row data
                row_data = {}
                for i, cell in enumerate(row):
                    if i < len(headers) and cell.value is not None:
                        row_data[headers[i]] = str(cell.value)
                
                raw_data = json.dumps({
                    'source': 'direct_import',
                    'file': os.path.basename(file_path),
                    'system_id': system_id,
                    'row_num': row_num,
                    'imported_at': datetime.now().isoformat(),
                    'data': row_data
                })
                
                cursor.execute(insert_sql, (timestamp, system_id, raw_data))
                imported_count += 1
                
                # Batch commit
                if imported_count % batch_size == 0:
                    conn.commit()
                    batch_count += 1
                    print(f"      ✅ Batch {batch_count}: {imported_count} records")
                    time.sleep(0.1)  # Small delay
                
            except Exception as e:
                error_count += 1
                if error_count <= 3:
                    print(f"      ⚠️  Row {row_num} error: {e}")
                continue
        
        # Final commit
        conn.commit()
        cursor.close()
        conn.close()
        wb.close()
        
        print(f"   📊 Import summary:")
        print(f"      ✅ Imported: {imported_count:,} records")
        print(f"      ❌ Errors: {error_count:,} records")
        
        return imported_count
        
    except Exception as e:
        print(f"   ❌ Direct import failed: {e}")
        import traceback
        traceback.print_exc()
        return 0

def test_basic_connection():
    """Test βασικής σύνδεσης"""
    print("🔍 BASIC CONNECTION TEST")
    print("=" * 30)
    
    try:
        conn = psycopg2.connect(
            host="localhost",
            database="solar_prediction",
            user="postgres",
            password="postgres",
            connect_timeout=5
        )
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM solax_data")
        count = cursor.fetchone()[0]
        print(f"✅ Database OK - solax_data has {count:,} records")
        cursor.close()
        conn.close()
        return True
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

def main():
    """Κύρια συνάρτηση"""
    print("📥 DIRECT IMPORT TEST")
    print("=" * 50)
    
    # Test connection
    if not test_basic_connection():
        print("❌ Cannot proceed - database issues")
        return False
    
    # Test file
    test_file = "data/raw/System1/Plant Reports 2024-03-01-2024-06-28.xlsx"
    
    if not os.path.exists(test_file):
        print(f"❌ Test file not found: {test_file}")
        return False
    
    print(f"✅ Test file found: {os.path.basename(test_file)}")
    
    # Direct import test
    imported = direct_excel_import(test_file, 1)
    
    if imported > 0:
        print(f"\n✅ DIRECT IMPORT SUCCESSFUL!")
        print(f"   📊 Imported {imported:,} records")
        print(f"   🎯 Method works - can proceed with full import")
        return True
    else:
        print(f"\n❌ DIRECT IMPORT FAILED!")
        print(f"   🔧 Need to investigate further")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n🚀 SOLUTION FOUND!")
        print(f"   ✅ Direct openpyxl import works")
        print(f"   📦 Use small batches (100 records)")
        print(f"   🎯 Ready for full historical import")
    else:
        print(f"\n❌ STILL INVESTIGATING...")
        print(f"   🔧 Need manual intervention")
