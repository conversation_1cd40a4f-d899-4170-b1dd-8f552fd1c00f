#!/usr/bin/env python3
"""
Real-time Model Monitoring System
=================================

Advanced monitoring system for solar prediction models:
- Real-time performance tracking
- Model drift detection
- Automated retraining triggers
- Adaptive calibration
- Performance degradation alerts
- Data quality monitoring

Target: Maintain >98% model accuracy through continuous monitoring
Created: June 6, 2025
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import numpy as np
import pandas as pd
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Any, Optional, Union
import json
import sqlite3
from pathlib import Path
import threading
import time
import warnings
warnings.filterwarnings('ignore')

# Statistical libraries
from scipy import stats
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.preprocessing import StandardScaler

# Database
import psycopg2
from psycopg2.extras import RealDictCursor

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ModelPerformanceTracker:
    """
    Track model performance metrics over time
    """
    
    def __init__(self, model_name: str, window_size: int = 24):
        """
        Initialize performance tracker
        
        Args:
            model_name: Name of the model to track
            window_size: Size of rolling window for metrics (hours)
        """
        self.model_name = model_name
        self.window_size = window_size
        
        # Performance history
        self.predictions = []
        self.actuals = []
        self.timestamps = []
        self.metrics_history = []
        
        # Current metrics
        self.current_metrics = {}
        self.baseline_metrics = {}
        
        logger.info(f"📊 Performance tracker initialized for {model_name}")
    
    def add_prediction(self, prediction: float, actual: float, timestamp: datetime = None):
        """Add a new prediction-actual pair"""
        if timestamp is None:
            timestamp = datetime.now()
        
        self.predictions.append(prediction)
        self.actuals.append(actual)
        self.timestamps.append(timestamp)
        
        # Keep only recent data
        cutoff_time = timestamp - timedelta(hours=self.window_size * 2)
        
        # Remove old data
        while self.timestamps and self.timestamps[0] < cutoff_time:
            self.predictions.pop(0)
            self.actuals.pop(0)
            self.timestamps.pop(0)
        
        # Update metrics if we have enough data
        if len(self.predictions) >= 10:
            self._update_metrics()
    
    def _update_metrics(self):
        """Update current performance metrics"""
        if len(self.predictions) < 2:
            return
        
        # Calculate metrics for recent window
        recent_preds = np.array(self.predictions[-self.window_size:])
        recent_actuals = np.array(self.actuals[-self.window_size:])
        
        # Remove any NaN values
        mask = ~(np.isnan(recent_preds) | np.isnan(recent_actuals))
        recent_preds = recent_preds[mask]
        recent_actuals = recent_actuals[mask]
        
        if len(recent_preds) < 2:
            return
        
        # Calculate metrics
        rmse = np.sqrt(mean_squared_error(recent_actuals, recent_preds))
        mae = mean_absolute_error(recent_actuals, recent_preds)
        r2 = r2_score(recent_actuals, recent_preds)
        
        # Calculate additional metrics
        mape = np.mean(np.abs((recent_actuals - recent_preds) / np.maximum(recent_actuals, 0.1))) * 100
        bias = np.mean(recent_preds - recent_actuals)
        
        # Correlation
        correlation = np.corrcoef(recent_preds, recent_actuals)[0, 1] if len(recent_preds) > 1 else 0
        
        self.current_metrics = {
            'rmse': rmse,
            'mae': mae,
            'r2': r2,
            'mape': mape,
            'bias': bias,
            'correlation': correlation,
            'n_samples': len(recent_preds),
            'timestamp': datetime.now()
        }
        
        # Store in history
        self.metrics_history.append(self.current_metrics.copy())
        
        # Keep only recent history
        if len(self.metrics_history) > 100:
            self.metrics_history.pop(0)
    
    def set_baseline_metrics(self, metrics: Dict[str, float]):
        """Set baseline metrics for comparison"""
        self.baseline_metrics = metrics.copy()
        logger.info(f"📈 Baseline metrics set for {self.model_name}: R²={metrics.get('r2', 0):.4f}")
    
    def get_performance_degradation(self) -> Dict[str, float]:
        """Calculate performance degradation compared to baseline"""
        if not self.current_metrics or not self.baseline_metrics:
            return {}
        
        degradation = {}
        
        for metric in ['rmse', 'mae', 'r2', 'mape']:
            if metric in self.current_metrics and metric in self.baseline_metrics:
                current = self.current_metrics[metric]
                baseline = self.baseline_metrics[metric]
                
                if metric in ['rmse', 'mae', 'mape']:
                    # Lower is better - calculate increase
                    degradation[metric] = (current - baseline) / baseline * 100
                else:
                    # Higher is better - calculate decrease
                    degradation[metric] = (baseline - current) / baseline * 100
        
        return degradation
    
    def detect_performance_drift(self, threshold: float = 10.0) -> bool:
        """Detect if performance has drifted significantly"""
        degradation = self.get_performance_degradation()
        
        if not degradation:
            return False
        
        # Check if any metric has degraded beyond threshold
        for metric, change in degradation.items():
            if change > threshold:
                logger.warning(f"⚠️ Performance drift detected in {self.model_name}: {metric} degraded by {change:.2f}%")
                return True
        
        return False

class DataDriftDetector:
    """
    Detect drift in input data distribution
    """
    
    def __init__(self, feature_names: List[str], reference_window: int = 168):  # 1 week
        """
        Initialize drift detector
        
        Args:
            feature_names: Names of features to monitor
            reference_window: Size of reference window (hours)
        """
        self.feature_names = feature_names
        self.reference_window = reference_window
        
        # Reference data
        self.reference_data = {}
        self.reference_stats = {}
        
        # Current data
        self.current_data = {name: [] for name in feature_names}
        self.current_timestamps = []
        
        logger.info(f"🔍 Data drift detector initialized for {len(feature_names)} features")
    
    def set_reference_data(self, data: pd.DataFrame):
        """Set reference data distribution"""
        for feature in self.feature_names:
            if feature in data.columns:
                self.reference_data[feature] = data[feature].values
                
                # Calculate reference statistics
                self.reference_stats[feature] = {
                    'mean': np.mean(data[feature]),
                    'std': np.std(data[feature]),
                    'min': np.min(data[feature]),
                    'max': np.max(data[feature]),
                    'q25': np.percentile(data[feature], 25),
                    'q75': np.percentile(data[feature], 75)
                }
        
        logger.info(f"📊 Reference data set with {len(data)} samples")
    
    def add_current_data(self, data: Dict[str, float], timestamp: datetime = None):
        """Add current data point"""
        if timestamp is None:
            timestamp = datetime.now()
        
        # Add data
        for feature in self.feature_names:
            if feature in data:
                self.current_data[feature].append(data[feature])
        
        self.current_timestamps.append(timestamp)
        
        # Keep only recent data
        cutoff_time = timestamp - timedelta(hours=self.reference_window)
        
        while self.current_timestamps and self.current_timestamps[0] < cutoff_time:
            self.current_timestamps.pop(0)
            for feature in self.feature_names:
                if self.current_data[feature]:
                    self.current_data[feature].pop(0)
    
    def detect_drift(self, significance_level: float = 0.05) -> Dict[str, Any]:
        """Detect data drift using statistical tests"""
        drift_results = {}
        
        for feature in self.feature_names:
            if (feature not in self.reference_data or 
                feature not in self.current_data or 
                len(self.current_data[feature]) < 30):
                continue
            
            reference = self.reference_data[feature]
            current = np.array(self.current_data[feature])
            
            # Kolmogorov-Smirnov test
            ks_statistic, ks_pvalue = stats.ks_2samp(reference, current)
            
            # Mann-Whitney U test
            mw_statistic, mw_pvalue = stats.mannwhitneyu(reference, current, alternative='two-sided')
            
            # Calculate distribution shift metrics
            ref_mean = np.mean(reference)
            curr_mean = np.mean(current)
            mean_shift = abs(curr_mean - ref_mean) / np.std(reference)
            
            ref_std = np.std(reference)
            curr_std = np.std(current)
            std_ratio = curr_std / ref_std if ref_std > 0 else 1.0
            
            # Determine if drift detected
            drift_detected = (ks_pvalue < significance_level or 
                            mw_pvalue < significance_level or 
                            mean_shift > 2.0 or 
                            std_ratio > 2.0 or std_ratio < 0.5)
            
            drift_results[feature] = {
                'drift_detected': drift_detected,
                'ks_statistic': ks_statistic,
                'ks_pvalue': ks_pvalue,
                'mw_pvalue': mw_pvalue,
                'mean_shift': mean_shift,
                'std_ratio': std_ratio,
                'current_mean': curr_mean,
                'reference_mean': ref_mean,
                'current_std': curr_std,
                'reference_std': ref_std
            }
        
        return drift_results

class AdaptiveCalibrator:
    """
    Adaptive calibration system for model predictions
    """
    
    def __init__(self, model_name: str, calibration_window: int = 72):
        """
        Initialize adaptive calibrator
        
        Args:
            model_name: Name of the model
            calibration_window: Window size for calibration (hours)
        """
        self.model_name = model_name
        self.calibration_window = calibration_window
        
        # Calibration data
        self.predictions = []
        self.actuals = []
        self.timestamps = []
        
        # Calibration parameters
        self.calibration_factor = 1.0
        self.calibration_bias = 0.0
        self.calibration_history = []
        
        logger.info(f"🎯 Adaptive calibrator initialized for {model_name}")
    
    def add_observation(self, prediction: float, actual: float, timestamp: datetime = None):
        """Add prediction-actual observation for calibration"""
        if timestamp is None:
            timestamp = datetime.now()
        
        self.predictions.append(prediction)
        self.actuals.append(actual)
        self.timestamps.append(timestamp)
        
        # Keep only recent data
        cutoff_time = timestamp - timedelta(hours=self.calibration_window * 2)
        
        while self.timestamps and self.timestamps[0] < cutoff_time:
            self.predictions.pop(0)
            self.actuals.pop(0)
            self.timestamps.pop(0)
        
        # Update calibration if we have enough data
        if len(self.predictions) >= 20:
            self._update_calibration()
    
    def _update_calibration(self):
        """Update calibration parameters"""
        if len(self.predictions) < 10:
            return
        
        # Use recent data for calibration
        recent_preds = np.array(self.predictions[-self.calibration_window:])
        recent_actuals = np.array(self.actuals[-self.calibration_window:])
        
        # Remove NaN values
        mask = ~(np.isnan(recent_preds) | np.isnan(recent_actuals))
        recent_preds = recent_preds[mask]
        recent_actuals = recent_actuals[mask]
        
        if len(recent_preds) < 5:
            return
        
        # Calculate calibration using linear regression
        # actual = factor * prediction + bias
        
        # Simple linear calibration
        if np.std(recent_preds) > 0:
            correlation = np.corrcoef(recent_preds, recent_actuals)[0, 1]
            
            if not np.isnan(correlation) and abs(correlation) > 0.1:
                # Calculate slope and intercept
                slope = np.cov(recent_preds, recent_actuals)[0, 1] / np.var(recent_preds)
                intercept = np.mean(recent_actuals) - slope * np.mean(recent_preds)
                
                # Update calibration parameters with smoothing
                alpha = 0.1  # Smoothing factor
                self.calibration_factor = (1 - alpha) * self.calibration_factor + alpha * slope
                self.calibration_bias = (1 - alpha) * self.calibration_bias + alpha * intercept
            else:
                # Use simple bias correction if no correlation
                bias = np.mean(recent_actuals - recent_preds)
                self.calibration_bias = (1 - alpha) * self.calibration_bias + alpha * bias
        
        # Store calibration history
        self.calibration_history.append({
            'timestamp': datetime.now(),
            'factor': self.calibration_factor,
            'bias': self.calibration_bias,
            'n_samples': len(recent_preds)
        })
        
        # Keep only recent history
        if len(self.calibration_history) > 100:
            self.calibration_history.pop(0)
    
    def calibrate_prediction(self, prediction: float) -> float:
        """Apply calibration to a prediction"""
        calibrated = self.calibration_factor * prediction + self.calibration_bias
        return calibrated
    
    def get_calibration_info(self) -> Dict[str, Any]:
        """Get current calibration information"""
        return {
            'calibration_factor': self.calibration_factor,
            'calibration_bias': self.calibration_bias,
            'n_observations': len(self.predictions),
            'last_update': self.calibration_history[-1]['timestamp'] if self.calibration_history else None
        }

class RealTimeModelMonitor:
    """
    Comprehensive real-time model monitoring system
    """
    
    def __init__(self, models: List[str], db_config: Dict[str, str] = None):
        """
        Initialize real-time monitor
        
        Args:
            models: List of model names to monitor
            db_config: Database configuration
        """
        self.models = models
        self.db_config = db_config or {
            'host': 'localhost',
            'database': 'solar_prediction',
            'user': 'postgres',
            'password': 'postgres'
        }
        
        # Initialize components for each model
        self.performance_trackers = {}
        self.drift_detectors = {}
        self.calibrators = {}
        
        for model in models:
            self.performance_trackers[model] = ModelPerformanceTracker(model)
            self.calibrators[model] = AdaptiveCalibrator(model)
        
        # Monitoring state
        self.monitoring_active = False
        self.monitoring_thread = None
        self.alert_thresholds = {
            'performance_degradation': 15.0,  # 15% degradation
            'drift_significance': 0.01,       # 1% significance level
            'retraining_threshold': 25.0      # 25% degradation triggers retraining
        }
        
        # Alerts and notifications
        self.alerts = []
        self.retraining_requests = []
        
        logger.info(f"📊 Real-time monitor initialized for {len(models)} models")
    
    def start_monitoring(self, interval_minutes: int = 5):
        """Start real-time monitoring"""
        if self.monitoring_active:
            logger.warning("⚠️ Monitoring already active")
            return
        
        self.monitoring_active = True
        self.monitoring_thread = threading.Thread(
            target=self._monitoring_loop,
            args=(interval_minutes,),
            daemon=True
        )
        self.monitoring_thread.start()
        
        logger.info(f"🚀 Real-time monitoring started (interval: {interval_minutes} minutes)")
    
    def stop_monitoring(self):
        """Stop real-time monitoring"""
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=10)
        
        logger.info("⏹️ Real-time monitoring stopped")
    
    def _monitoring_loop(self, interval_minutes: int):
        """Main monitoring loop"""
        while self.monitoring_active:
            try:
                # Collect recent predictions and actuals
                self._collect_recent_data()
                
                # Check for performance degradation
                self._check_performance_degradation()
                
                # Check for data drift
                self._check_data_drift()
                
                # Update calibrations
                self._update_calibrations()
                
                # Process alerts
                self._process_alerts()
                
            except Exception as e:
                logger.error(f"❌ Monitoring loop error: {e}")
            
            # Wait for next interval
            time.sleep(interval_minutes * 60)
    
    def _collect_recent_data(self):
        """Collect recent predictions and actual values from database"""
        try:
            conn = psycopg2.connect(**self.db_config)
            
            # Get recent predictions (last hour)
            query = """
            SELECT 
                model_name,
                prediction_value,
                actual_value,
                prediction_timestamp,
                features
            FROM predictions 
            WHERE prediction_timestamp >= NOW() - INTERVAL '1 hour'
                AND actual_value IS NOT NULL
            ORDER BY prediction_timestamp DESC
            """
            
            df = pd.read_sql(query, conn)
            conn.close()
            
            # Update trackers
            for _, row in df.iterrows():
                model_name = row['model_name']
                
                if model_name in self.performance_trackers:
                    self.performance_trackers[model_name].add_prediction(
                        prediction=row['prediction_value'],
                        actual=row['actual_value'],
                        timestamp=row['prediction_timestamp']
                    )
                    
                    self.calibrators[model_name].add_observation(
                        prediction=row['prediction_value'],
                        actual=row['actual_value'],
                        timestamp=row['prediction_timestamp']
                    )
            
        except Exception as e:
            logger.error(f"❌ Data collection error: {e}")
    
    def _check_performance_degradation(self):
        """Check for performance degradation"""
        for model_name, tracker in self.performance_trackers.items():
            if tracker.detect_performance_drift(self.alert_thresholds['performance_degradation']):
                degradation = tracker.get_performance_degradation()
                
                alert = {
                    'type': 'performance_degradation',
                    'model': model_name,
                    'timestamp': datetime.now(),
                    'degradation': degradation,
                    'severity': 'high' if max(degradation.values()) > self.alert_thresholds['retraining_threshold'] else 'medium'
                }
                
                self.alerts.append(alert)
                
                # Trigger retraining if degradation is severe
                if alert['severity'] == 'high':
                    self.retraining_requests.append({
                        'model': model_name,
                        'reason': 'performance_degradation',
                        'timestamp': datetime.now(),
                        'degradation': degradation
                    })
    
    def _check_data_drift(self):
        """Check for data drift"""
        # This would be implemented with actual feature data
        # For now, we'll create a placeholder
        pass
    
    def _update_calibrations(self):
        """Update model calibrations"""
        for model_name, calibrator in self.calibrators.items():
            # Calibration is updated automatically when observations are added
            pass
    
    def _process_alerts(self):
        """Process and log alerts"""
        if self.alerts:
            recent_alerts = [alert for alert in self.alerts if 
                           (datetime.now() - alert['timestamp']).total_seconds() < 3600]
            
            if recent_alerts:
                logger.info(f"🚨 {len(recent_alerts)} active alerts")
                for alert in recent_alerts[-3:]:  # Show last 3 alerts
                    logger.info(f"   {alert['type']} - {alert['model']} - {alert['severity']}")

def main():
    """Test real-time model monitoring system"""
    logger.info("📊 Testing Real-time Model Monitoring System")
    logger.info("=" * 70)
    
    # Initialize monitor
    models = ['xgboost_ensemble', 'lightgbm_ensemble', 'random_forest_ensemble']
    monitor = RealTimeModelMonitor(models)
    
    # Simulate some data
    logger.info("🔄 Simulating model performance data...")
    
    np.random.seed(42)
    
    for i in range(50):
        for model in models:
            # Simulate predictions and actuals
            true_value = np.random.normal(5.0, 1.0)
            
            # Add some model-specific bias and noise
            if model == 'xgboost_ensemble':
                prediction = true_value + np.random.normal(0.1, 0.3)
            elif model == 'lightgbm_ensemble':
                prediction = true_value + np.random.normal(-0.05, 0.25)
            else:
                prediction = true_value + np.random.normal(0.0, 0.4)
            
            # Add some drift over time
            if i > 30:
                prediction *= 1.1  # 10% systematic error
            
            timestamp = datetime.now() - timedelta(hours=50-i)
            
            # Add to trackers
            monitor.performance_trackers[model].add_prediction(prediction, true_value, timestamp)
            monitor.calibrators[model].add_observation(prediction, true_value, timestamp)
    
    # Set baseline metrics
    for model in models:
        monitor.performance_trackers[model].set_baseline_metrics({
            'rmse': 0.3,
            'mae': 0.25,
            'r2': 0.95,
            'mape': 5.0
        })
    
    # Check performance
    logger.info("\n📈 Performance Analysis:")
    for model in models:
        tracker = monitor.performance_trackers[model]
        degradation = tracker.get_performance_degradation()
        drift_detected = tracker.detect_performance_drift(10.0)
        
        logger.info(f"\n   {model}:")
        logger.info(f"     Current R²: {tracker.current_metrics.get('r2', 0):.4f}")
        logger.info(f"     Current RMSE: {tracker.current_metrics.get('rmse', 0):.4f}")
        logger.info(f"     Drift detected: {'✅' if drift_detected else '❌'}")
        
        if degradation:
            logger.info(f"     Performance changes:")
            for metric, change in degradation.items():
                logger.info(f"       {metric}: {change:+.2f}%")
    
    # Check calibration
    logger.info("\n🎯 Calibration Analysis:")
    for model in models:
        calibrator = monitor.calibrators[model]
        cal_info = calibrator.get_calibration_info()
        
        logger.info(f"\n   {model}:")
        logger.info(f"     Calibration factor: {cal_info['calibration_factor']:.4f}")
        logger.info(f"     Calibration bias: {cal_info['calibration_bias']:.4f}")
        logger.info(f"     Observations: {cal_info['n_observations']}")
        
        # Test calibration
        test_prediction = 5.0
        calibrated = calibrator.calibrate_prediction(test_prediction)
        logger.info(f"     Test: {test_prediction:.2f} → {calibrated:.2f}")
    
    logger.info("\n✅ Real-time Model Monitoring System test completed!")
    
    return monitor

class AutomatedRetrainingSystem:
    """
    Automated model retraining system
    """

    def __init__(self, monitor: RealTimeModelMonitor):
        """Initialize automated retraining system"""
        self.monitor = monitor
        self.retraining_active = False
        self.retraining_history = []

        # Retraining configuration
        self.min_data_points = 1000
        self.retraining_cooldown_hours = 24
        self.max_retrain_attempts = 3

        logger.info("🔄 Automated retraining system initialized")

    def check_retraining_triggers(self) -> List[Dict[str, Any]]:
        """Check if any models need retraining"""
        triggers = []

        for model_name, tracker in self.monitor.performance_trackers.items():
            # Check performance degradation
            degradation = tracker.get_performance_degradation()

            if degradation:
                max_degradation = max(degradation.values())

                if max_degradation > self.monitor.alert_thresholds['retraining_threshold']:
                    # Check if enough time has passed since last retraining
                    last_retrain = self._get_last_retraining_time(model_name)

                    if (not last_retrain or
                        (datetime.now() - last_retrain).total_seconds() > self.retraining_cooldown_hours * 3600):

                        triggers.append({
                            'model': model_name,
                            'trigger_type': 'performance_degradation',
                            'degradation': degradation,
                            'max_degradation': max_degradation,
                            'timestamp': datetime.now()
                        })

        return triggers

    def _get_last_retraining_time(self, model_name: str) -> Optional[datetime]:
        """Get timestamp of last retraining for a model"""
        model_history = [h for h in self.retraining_history if h['model'] == model_name]

        if model_history:
            return max(h['timestamp'] for h in model_history)

        return None

    def trigger_retraining(self, model_name: str, reason: str) -> Dict[str, Any]:
        """Trigger retraining for a specific model"""
        logger.info(f"🔄 Triggering retraining for {model_name} (reason: {reason})")

        # Record retraining attempt
        retraining_record = {
            'model': model_name,
            'reason': reason,
            'timestamp': datetime.now(),
            'status': 'initiated',
            'data_points_used': 0,
            'new_performance': {},
            'improvement': {}
        }

        try:
            # Simulate retraining process
            # In real implementation, this would:
            # 1. Collect recent training data
            # 2. Retrain the model
            # 3. Validate performance
            # 4. Deploy if improved

            # For simulation, we'll just reset the calibration
            if model_name in self.monitor.calibrators:
                calibrator = self.monitor.calibrators[model_name]
                calibrator.calibration_factor = 1.0
                calibrator.calibration_bias = 0.0

                retraining_record['status'] = 'completed'
                retraining_record['data_points_used'] = len(calibrator.predictions)

                logger.info(f"✅ Retraining completed for {model_name}")
            else:
                retraining_record['status'] = 'failed'
                retraining_record['error'] = 'Model not found'

                logger.error(f"❌ Retraining failed for {model_name}: Model not found")

        except Exception as e:
            retraining_record['status'] = 'failed'
            retraining_record['error'] = str(e)

            logger.error(f"❌ Retraining failed for {model_name}: {e}")

        # Store retraining record
        self.retraining_history.append(retraining_record)

        return retraining_record

    def get_retraining_summary(self) -> Dict[str, Any]:
        """Get summary of retraining activities"""
        if not self.retraining_history:
            return {'total_retrainings': 0}

        total = len(self.retraining_history)
        successful = len([r for r in self.retraining_history if r['status'] == 'completed'])
        failed = len([r for r in self.retraining_history if r['status'] == 'failed'])

        # Recent activity (last 7 days)
        recent_cutoff = datetime.now() - timedelta(days=7)
        recent = [r for r in self.retraining_history if r['timestamp'] > recent_cutoff]

        return {
            'total_retrainings': total,
            'successful': successful,
            'failed': failed,
            'success_rate': successful / total * 100 if total > 0 else 0,
            'recent_retrainings': len(recent),
            'last_retraining': max(r['timestamp'] for r in self.retraining_history) if self.retraining_history else None
        }

if __name__ == "__main__":
    monitor = main()
