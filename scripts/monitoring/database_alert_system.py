#!/usr/bin/env python3
"""
Database Alert System
=====================

Automated alert system for database freshness monitoring.
Sends notifications when data becomes stale or critical.

Features:
- Real-time monitoring with configurable intervals
- Multiple notification channels (console, file, telegram)
- Alert escalation based on severity
- Alert suppression to avoid spam
- Historical alert tracking

Created: June 12, 2025
"""

import asyncio
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path
import requests

from database_freshness_monitor import DatabaseFreshnessMonitor

class DatabaseAlertSystem:
    def __init__(self):
        self.monitor = DatabaseFreshnessMonitor()
        self.alert_log_path = Path("logs/monitoring/database_alerts.log")
        self.alert_log_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Alert configuration
        self.alert_config = {
            "check_interval_minutes": 5,  # Check every 5 minutes
            "alert_suppression_minutes": 30,  # Don't repeat same alert for 30 minutes
            "telegram_enabled": True,
            "telegram_bot_token": "**********************************************",
            "telegram_chat_id": "1510889515"
        }
        
        # Track last alerts to avoid spam
        self.last_alerts = {}
        
        # Alert severity levels
        self.severity_levels = {
            "fresh": 0,
            "stale": 1,
            "critical": 2,
            "error": 3
        }

    async def send_telegram_alert(self, message: str, severity: str = "warning"):
        """Send alert via Telegram bot"""
        if not self.alert_config["telegram_enabled"]:
            return False
        
        try:
            # Add severity emoji
            severity_emojis = {
                "info": "ℹ️",
                "warning": "⚠️",
                "critical": "🚨",
                "error": "💥"
            }
            
            emoji = severity_emojis.get(severity, "📊")
            formatted_message = f"{emoji} **Database Alert**\n\n{message}"
            
            url = f"https://api.telegram.org/bot{self.alert_config['telegram_bot_token']}/sendMessage"
            payload = {
                "chat_id": self.alert_config["telegram_chat_id"],
                "text": formatted_message,
                "parse_mode": "Markdown"
            }
            
            response = requests.post(url, json=payload, timeout=10)
            return response.status_code == 200
            
        except Exception as e:
            print(f"Failed to send Telegram alert: {e}")
            return False

    def log_alert(self, alert_data: Dict[str, Any]):
        """Log alert to file for historical tracking"""
        try:
            with open(self.alert_log_path, "a") as f:
                f.write(json.dumps(alert_data) + "\n")
        except Exception as e:
            print(f"Failed to log alert: {e}")

    def should_send_alert(self, table_name: str, status: str) -> bool:
        """Check if alert should be sent based on suppression rules"""
        alert_key = f"{table_name}_{status}"
        now = datetime.now()
        
        if alert_key in self.last_alerts:
            last_alert_time = self.last_alerts[alert_key]
            time_diff = (now - last_alert_time).total_seconds() / 60
            
            if time_diff < self.alert_config["alert_suppression_minutes"]:
                return False
        
        self.last_alerts[alert_key] = now
        return True

    async def process_alerts(self, freshness_summary: Dict[str, Any]):
        """Process and send alerts based on freshness summary"""
        alerts_sent = []
        
        for table_name, details in freshness_summary["tables"].items():
            status = details["status"]
            
            # Only alert for stale, critical, or error status
            if status in ["stale", "critical", "error"]:
                if self.should_send_alert(table_name, status):
                    
                    # Create alert message
                    if "error" in details:
                        message = f"🔴 **{table_name.upper()}** - {status.upper()}\n"
                        message += f"Error: {details['error']}\n"
                        message += f"Business Impact: {details['business_impact']}\n"
                        severity = "error"
                    else:
                        hours = details.get("hours_since_last_record", 0)
                        message = f"🔴 **{table_name.upper()}** - {status.upper()}\n"
                        message += f"Last Record: {hours}h ago\n"
                        message += f"Expected: Every {details.get('expected_frequency_minutes', 0)} minutes\n"
                        message += f"Business Impact: {details['business_impact']}\n"
                        message += f"Records: {details.get('records_count', 0):,}\n"
                        
                        if details.get('recent_records_24h', 0) == 0:
                            message += "⚠️ No recent activity in last 24h\n"
                        
                        severity = "critical" if status == "critical" else "warning"
                    
                    message += f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                    
                    # Log alert
                    alert_data = {
                        "timestamp": datetime.now().isoformat(),
                        "table_name": table_name,
                        "status": status,
                        "severity": severity,
                        "message": message,
                        "details": details
                    }
                    self.log_alert(alert_data)
                    
                    # Send console alert
                    print(f"\n🚨 ALERT: {table_name.upper()} - {status.upper()}")
                    print(f"   {message}")
                    
                    # Send Telegram alert
                    telegram_sent = await self.send_telegram_alert(message, severity)
                    
                    alerts_sent.append({
                        "table_name": table_name,
                        "status": status,
                        "severity": severity,
                        "telegram_sent": telegram_sent,
                        "timestamp": datetime.now().isoformat()
                    })
        
        return alerts_sent

    async def run_monitoring_cycle(self):
        """Run one monitoring cycle"""
        print(f"🔍 Running database freshness check at {datetime.now().strftime('%H:%M:%S')}")
        
        # Get freshness summary
        freshness_summary = await self.monitor.check_all_tables()
        
        # Process alerts
        alerts_sent = await self.process_alerts(freshness_summary)
        
        # Summary
        print(f"   Overall Status: {freshness_summary['overall_status'].upper()}")
        print(f"   Fresh: {freshness_summary['fresh_tables']}, Stale: {freshness_summary['stale_tables']}, Critical: {freshness_summary['critical_tables']}")
        
        if alerts_sent:
            print(f"   🚨 Alerts sent: {len(alerts_sent)}")
            for alert in alerts_sent:
                telegram_status = "✅" if alert["telegram_sent"] else "❌"
                print(f"      - {alert['table_name']} ({alert['severity']}) {telegram_status}")
        else:
            print("   ✅ No alerts needed")
        
        return {
            "timestamp": datetime.now().isoformat(),
            "freshness_summary": freshness_summary,
            "alerts_sent": alerts_sent
        }

    async def start_continuous_monitoring(self):
        """Start continuous monitoring with configured interval"""
        print("🚀 Starting Database Alert System")
        print(f"   Check Interval: {self.alert_config['check_interval_minutes']} minutes")
        print(f"   Alert Suppression: {self.alert_config['alert_suppression_minutes']} minutes")
        print(f"   Telegram Alerts: {'Enabled' if self.alert_config['telegram_enabled'] else 'Disabled'}")
        print("=" * 60)
        
        while True:
            try:
                await self.run_monitoring_cycle()
                
                # Wait for next check
                await asyncio.sleep(self.alert_config["check_interval_minutes"] * 60)
                
            except KeyboardInterrupt:
                print("\n🛑 Monitoring stopped by user")
                break
            except Exception as e:
                print(f"❌ Error in monitoring cycle: {e}")
                await asyncio.sleep(60)  # Wait 1 minute before retrying

    async def send_test_alert(self):
        """Send a test alert to verify configuration"""
        test_message = f"""🧪 **Database Alert System Test**

This is a test alert to verify the monitoring system is working correctly.

System Status: Operational
Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Monitoring Interval: {self.alert_config['check_interval_minutes']} minutes

If you receive this message, the alert system is configured correctly! 🎉"""
        
        print("📤 Sending test alert...")
        telegram_sent = await self.send_telegram_alert(test_message, "info")
        
        if telegram_sent:
            print("✅ Test alert sent successfully!")
        else:
            print("❌ Failed to send test alert")
        
        return telegram_sent

async def main():
    """Main function for standalone execution"""
    import sys
    
    alert_system = DatabaseAlertSystem()
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "test":
            # Send test alert
            await alert_system.send_test_alert()
        elif command == "check":
            # Run single check
            await alert_system.run_monitoring_cycle()
        elif command == "monitor":
            # Start continuous monitoring
            await alert_system.start_continuous_monitoring()
        else:
            print("Usage: python database_alert_system.py [test|check|monitor]")
    else:
        # Default: run single check
        await alert_system.run_monitoring_cycle()

if __name__ == "__main__":
    asyncio.run(main())
