#!/usr/bin/env python3
"""
PERFORMANCE MONITORING SYSTEM
Monitor seasonal models performance in production
Created: June 4, 2025
"""

import os
import sys
import time
import json
import requests
import psycopg2
from datetime import datetime, timedelta
from pathlib import Path
import logging
from typing import Dict, List, Any

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/performance_monitor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class PerformanceMonitor:
    """Monitor seasonal models performance in production"""
    
    def __init__(self):
        self.api_base_url = "http://localhost:8100"
        self.db_configs = [
            "postgresql://grlv:Gr1234@localhost:5433/solar_prediction",
            "postgresql://postgres:postgres@localhost:5433/solar_prediction"
        ]
        self.monitoring_interval = 900  # 15 minutes
        
        # Create logs directory
        Path("logs").mkdir(exist_ok=True)
        
        logger.info("🔍 Performance Monitor initialized")
    
    def connect_database(self):
        """Connect to PostgreSQL database"""
        for config in self.db_configs:
            try:
                conn = psycopg2.connect(config)
                return conn
            except Exception as e:
                continue
        return None
    
    def create_monitoring_table(self):
        """Create performance monitoring table"""
        conn = self.connect_database()
        if not conn:
            logger.error("❌ Cannot connect to database")
            return False
        
        try:
            cursor = conn.cursor()
            
            # Create performance_metrics table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS performance_metrics (
                    id SERIAL PRIMARY KEY,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    model_used VARCHAR(50),
                    system_id INTEGER,
                    season VARCHAR(20),
                    prediction_value FLOAT,
                    response_time_ms FLOAT,
                    confidence_score FLOAT,
                    api_endpoint VARCHAR(100),
                    status VARCHAR(20),
                    error_message TEXT,
                    weather_data JSONB
                )
            """)
            
            # Create index for faster queries
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_performance_timestamp 
                ON performance_metrics(timestamp)
            """)
            
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_performance_model 
                ON performance_metrics(model_used, system_id)
            """)
            
            conn.commit()
            cursor.close()
            conn.close()
            
            logger.info("✅ Performance monitoring table created/verified")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to create monitoring table: {e}")
            return False
    
    def test_api_performance(self) -> Dict[str, Any]:
        """Test API performance and collect metrics"""
        metrics = {
            'timestamp': datetime.now(),
            'tests_completed': 0,
            'tests_failed': 0,
            'average_response_time': 0,
            'test_results': []
        }
        
        # Test cases for both systems and different weather conditions
        test_cases = [
            {
                'system_id': 1,
                'weather_data': {
                    'temperature': 25.0,
                    'cloud_cover': 30.0,
                    'ghi': 700.0,
                    'soc': 85.0
                }
            },
            {
                'system_id': 2,
                'weather_data': {
                    'temperature': 22.0,
                    'cloud_cover': 45.0,
                    'ghi': 600.0,
                    'soc': 90.0
                }
            }
        ]
        
        total_response_time = 0
        
        for test_case in test_cases:
            try:
                # Measure response time
                start_time = time.time()
                
                response = requests.post(
                    f"{self.api_base_url}/api/v1/predict/seasonal",
                    json=test_case,
                    timeout=10
                )
                
                end_time = time.time()
                response_time_ms = (end_time - start_time) * 1000
                
                if response.status_code == 200:
                    result = response.json()
                    
                    test_result = {
                        'system_id': test_case['system_id'],
                        'season': result.get('season', 'unknown'),
                        'prediction': result.get('prediction', 0),
                        'confidence': result.get('confidence', 0),
                        'response_time_ms': response_time_ms,
                        'status': 'success',
                        'model_algorithm': result.get('model_algorithm', 'unknown')
                    }
                    
                    metrics['tests_completed'] += 1
                    total_response_time += response_time_ms
                    
                    logger.info(f"✅ Test passed: System {test_case['system_id']}, "
                              f"Season: {result.get('season')}, "
                              f"Response: {response_time_ms:.1f}ms")
                    
                else:
                    test_result = {
                        'system_id': test_case['system_id'],
                        'response_time_ms': response_time_ms,
                        'status': 'failed',
                        'error': f"HTTP {response.status_code}"
                    }
                    
                    metrics['tests_failed'] += 1
                    logger.error(f"❌ Test failed: System {test_case['system_id']}, "
                               f"Status: {response.status_code}")
                
                metrics['test_results'].append(test_result)
                
            except Exception as e:
                test_result = {
                    'system_id': test_case['system_id'],
                    'status': 'error',
                    'error': str(e)
                }
                
                metrics['test_results'].append(test_result)
                metrics['tests_failed'] += 1
                logger.error(f"❌ Test error: System {test_case['system_id']}, Error: {e}")
        
        # Calculate average response time
        if metrics['tests_completed'] > 0:
            metrics['average_response_time'] = total_response_time / metrics['tests_completed']
        
        return metrics
    
    def save_metrics_to_database(self, metrics: Dict[str, Any]):
        """Save performance metrics to database"""
        conn = self.connect_database()
        if not conn:
            logger.error("❌ Cannot connect to database for saving metrics")
            return False
        
        try:
            cursor = conn.cursor()
            
            for test_result in metrics['test_results']:
                cursor.execute("""
                    INSERT INTO performance_metrics (
                        timestamp, model_used, system_id, season,
                        prediction_value, response_time_ms, confidence_score,
                        api_endpoint, status, error_message, weather_data
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    metrics['timestamp'],
                    f"seasonal_{test_result.get('season', 'unknown')}",
                    test_result.get('system_id'),
                    test_result.get('season'),
                    test_result.get('prediction'),
                    test_result.get('response_time_ms'),
                    test_result.get('confidence'),
                    '/api/v1/predict/seasonal',
                    test_result.get('status'),
                    test_result.get('error'),
                    json.dumps(test_result)
                ))
            
            conn.commit()
            cursor.close()
            conn.close()
            
            logger.info(f"✅ Saved {len(metrics['test_results'])} metrics to database")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to save metrics: {e}")
            return False
    
    def generate_performance_report(self) -> Dict[str, Any]:
        """Generate performance report from recent data"""
        conn = self.connect_database()
        if not conn:
            return {}
        
        try:
            cursor = conn.cursor()
            
            # Get metrics from last 24 hours
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_tests,
                    COUNT(*) FILTER (WHERE status = 'success') as successful_tests,
                    AVG(response_time_ms) FILTER (WHERE status = 'success') as avg_response_time,
                    AVG(confidence_score) FILTER (WHERE status = 'success') as avg_confidence,
                    season,
                    COUNT(*) as season_count
                FROM performance_metrics 
                WHERE timestamp > NOW() - INTERVAL '24 hours'
                GROUP BY season
                ORDER BY season_count DESC
            """)
            
            results = cursor.fetchall()
            
            report = {
                'report_time': datetime.now().isoformat(),
                'period': 'Last 24 hours',
                'seasonal_performance': []
            }
            
            total_tests = 0
            total_successful = 0
            
            for row in results:
                total, successful, avg_response, avg_confidence, season, count = row
                
                total_tests += total or 0
                total_successful += successful or 0
                
                report['seasonal_performance'].append({
                    'season': season,
                    'total_tests': total,
                    'successful_tests': successful,
                    'success_rate': (successful / total * 100) if total > 0 else 0,
                    'avg_response_time_ms': round(avg_response or 0, 2),
                    'avg_confidence': round(avg_confidence or 0, 4)
                })
            
            report['overall'] = {
                'total_tests': total_tests,
                'successful_tests': total_successful,
                'success_rate': (total_successful / total_tests * 100) if total_tests > 0 else 0
            }
            
            cursor.close()
            conn.close()
            
            return report
            
        except Exception as e:
            logger.error(f"❌ Failed to generate report: {e}")
            return {}
    
    def run_monitoring_cycle(self):
        """Run one monitoring cycle"""
        logger.info("🔍 Starting performance monitoring cycle")
        
        # Test API performance
        metrics = self.test_api_performance()
        
        # Save to database
        self.save_metrics_to_database(metrics)
        
        # Generate and log report
        report = self.generate_performance_report()
        
        if report:
            logger.info(f"📊 Performance Report:")
            logger.info(f"   Total tests (24h): {report['overall']['total_tests']}")
            logger.info(f"   Success rate: {report['overall']['success_rate']:.1f}%")
            
            for season_perf in report['seasonal_performance']:
                logger.info(f"   {season_perf['season'].capitalize()}: "
                          f"{season_perf['avg_response_time_ms']:.1f}ms avg, "
                          f"{season_perf['avg_confidence']:.3f} confidence")
        
        logger.info("✅ Monitoring cycle completed")
        return metrics, report
    
    def start_continuous_monitoring(self):
        """Start continuous monitoring"""
        logger.info(f"🚀 Starting continuous performance monitoring")
        logger.info(f"   Interval: {self.monitoring_interval} seconds")
        logger.info(f"   API URL: {self.api_base_url}")
        
        # Create monitoring table
        if not self.create_monitoring_table():
            logger.error("❌ Failed to setup monitoring table")
            return
        
        try:
            while True:
                self.run_monitoring_cycle()
                
                logger.info(f"⏰ Waiting {self.monitoring_interval} seconds until next cycle...")
                time.sleep(self.monitoring_interval)
                
        except KeyboardInterrupt:
            logger.info("🛑 Monitoring stopped by user")
        except Exception as e:
            logger.error(f"❌ Monitoring error: {e}")


def main():
    """Main monitoring function"""
    monitor = PerformanceMonitor()
    
    if len(sys.argv) > 1 and sys.argv[1] == "once":
        # Run once
        metrics, report = monitor.run_monitoring_cycle()
        print(json.dumps(report, indent=2))
    else:
        # Run continuously
        monitor.start_continuous_monitoring()


if __name__ == "__main__":
    main()
