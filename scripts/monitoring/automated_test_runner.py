#!/usr/bin/env python3
"""
Automated Test Runner
====================

Automated test runner that executes comprehensive tests on schedule
and reports results via Telegram and logs.

Features:
- Scheduled test execution
- Integration with unified health monitoring
- Telegram notifications for test results
- Test history tracking
- Performance trend analysis

Created: June 12, 2025
"""

import subprocess
import json
import time
import requests
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any

class AutomatedTestRunner:
    def __init__(self):
        self.config = {
            "test_interval_hours": 6,  # Run tests every 6 hours
            "telegram_notifications": True,
            "telegram_bot_token": "**********:AAFBhGADTAAcUkbom_FEFSkOHoT5N6t5png",
            "telegram_chat_id": "**********",
            "unified_health_endpoint": "http://localhost:8130/api/v2/system/health",
            "test_scripts": [
                "test/scripts/unified_health_test.py",
                "test/scripts/system_health_check.py",
                "test/scripts/check_system_status.py"
            ]
        }
        
        self.log_path = Path("logs/testing/automated_test_runner.log")
        self.log_path.parent.mkdir(parents=True, exist_ok=True)
        
        self.results_path = Path("logs/testing/test_history.json")
        self.results_path.parent.mkdir(parents=True, exist_ok=True)

    def send_telegram_notification(self, message: str, severity: str = "info"):
        """Send test results via Telegram"""
        if not self.config["telegram_notifications"]:
            return False

        try:
            severity_emojis = {
                "info": "ℹ️",
                "success": "✅",
                "warning": "⚠️",
                "error": "❌"
            }

            emoji = severity_emojis.get(severity, "📊")
            formatted_message = f"{emoji} **Automated Test Results**\n\n{message}"

            url = f"https://api.telegram.org/bot{self.config['telegram_bot_token']}/sendMessage"
            payload = {
                "chat_id": self.config["telegram_chat_id"],
                "text": formatted_message,
                "parse_mode": "Markdown"
            }

            response = requests.post(url, json=payload, timeout=10)
            return response.status_code == 200

        except Exception as e:
            print(f"Failed to send Telegram notification: {e}")
            return False

    def run_unified_health_check(self) -> Dict[str, Any]:
        """Run unified health check and return results"""
        try:
            response = requests.get(self.config["unified_health_endpoint"], timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                # Calculate health score
                services = data.get("services", [])
                healthy_services = len([s for s in services if s.get("status") == "healthy"])
                total_services = len(services)
                
                database_freshness = data.get("database_freshness", [])
                fresh_tables = len([d for d in database_freshness if d.get("freshness_status") == "fresh"])
                total_tables = len(database_freshness)
                
                model_retrain = data.get("model_retrain", [])
                models_on_schedule = len([m for m in model_retrain if m.get("retrain_status") == "on_schedule"])
                total_models = len(model_retrain)
                
                alerts = data.get("alerts", [])
                
                # Calculate overall health score (0-100)
                service_score = (healthy_services / total_services * 100) if total_services > 0 else 0
                database_score = (fresh_tables / total_tables * 100) if total_tables > 0 else 0
                model_score = (models_on_schedule / total_models * 100) if total_models > 0 else 100  # 100% if no models to monitor

                # Calculate weighted average (only count components that exist)
                scores = []
                if total_services > 0:
                    scores.append(service_score)
                if total_tables > 0:
                    scores.append(database_score)
                if total_models > 0:
                    scores.append(model_score)
                elif total_models == 0:
                    # If no models to monitor, consider it perfect (100%)
                    scores.append(100)

                overall_score = sum(scores) / len(scores) if scores else 0
                
                return {
                    "status": "success",
                    "overall_status": data.get("overall_status", "unknown"),
                    "overall_score": overall_score,
                    "services": {
                        "healthy": healthy_services,
                        "total": total_services,
                        "score": service_score
                    },
                    "database": {
                        "fresh": fresh_tables,
                        "total": total_tables,
                        "score": database_score
                    },
                    "models": {
                        "on_schedule": models_on_schedule,
                        "total": total_models,
                        "score": model_score
                    },
                    "alerts_count": len(alerts),
                    "alerts": alerts[:3],  # First 3 alerts
                    "timestamp": datetime.now().isoformat()
                }
            else:
                return {
                    "status": "error",
                    "error": f"HTTP {response.status_code}",
                    "timestamp": datetime.now().isoformat()
                }
                
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    def run_basic_connectivity_tests(self) -> Dict[str, Any]:
        """Run basic connectivity tests"""
        services = [
            {"name": "Production Scripts", "url": "http://localhost:8100/health"},
            {"name": "GPU Service", "url": "http://localhost:8105/health"},
            {"name": "Billing System", "url": "http://localhost:8110/health"},
            {"name": "Unified Forecast", "url": "http://localhost:8120/health"},
            {"name": "Charts API", "url": "http://localhost:8103/health"}
        ]
        
        results = []
        
        for service in services:
            try:
                start_time = time.time()
                response = requests.get(service["url"], timeout=10)
                duration = (time.time() - start_time) * 1000
                
                results.append({
                    "name": service["name"],
                    "status": "healthy" if response.status_code == 200 else "error",
                    "response_time_ms": duration,
                    "status_code": response.status_code
                })
                
            except Exception as e:
                results.append({
                    "name": service["name"],
                    "status": "error",
                    "error": str(e)
                })
        
        healthy_count = len([r for r in results if r["status"] == "healthy"])
        
        return {
            "services": results,
            "healthy_count": healthy_count,
            "total_count": len(services),
            "success_rate": (healthy_count / len(services) * 100) if services else 0
        }

    def generate_test_report(self, health_results: Dict[str, Any], connectivity_results: Dict[str, Any]) -> str:
        """Generate comprehensive test report"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        report = f"**System Health Report**\n"
        report += f"Time: {timestamp}\n\n"
        
        if health_results["status"] == "success":
            report += f"**Overall Status: {health_results['overall_status'].upper()}**\n"
            report += f"Health Score: {health_results['overall_score']:.1f}%\n\n"
            
            report += f"**Services:** {health_results['services']['healthy']}/{health_results['services']['total']} healthy\n"
            report += f"**Database:** {health_results['database']['fresh']}/{health_results['database']['total']} fresh\n"
            report += f"**Models:** {health_results['models']['on_schedule']}/{health_results['models']['total']} on schedule\n\n"
            
            if health_results['alerts_count'] > 0:
                report += f"**Alerts ({health_results['alerts_count']}):**\n"
                for alert in health_results['alerts']:
                    report += f"• {alert}\n"
            else:
                report += "**No active alerts** ✅\n"
        else:
            report += f"**Health Check Failed:** {health_results.get('error', 'Unknown error')}\n"
        
        report += f"\n**Connectivity:** {connectivity_results['healthy_count']}/{connectivity_results['total_count']} services reachable\n"
        
        return report

    def save_test_results(self, results: Dict[str, Any]):
        """Save test results to history"""
        try:
            # Load existing history
            history = []
            if self.results_path.exists():
                with open(self.results_path, "r") as f:
                    history = json.load(f)
            
            # Add new result
            history.append(results)
            
            # Keep only last 100 results
            history = history[-100:]
            
            # Save updated history
            with open(self.results_path, "w") as f:
                json.dump(history, f, indent=2)
                
        except Exception as e:
            print(f"Failed to save test results: {e}")

    def run_comprehensive_test(self):
        """Run comprehensive test suite"""
        print(f"🧪 Running automated test suite at {datetime.now().strftime('%H:%M:%S')}")
        
        # Run unified health check
        health_results = self.run_unified_health_check()
        
        # Run basic connectivity tests
        connectivity_results = self.run_basic_connectivity_tests()
        
        # Generate report
        report = self.generate_test_report(health_results, connectivity_results)
        
        # Determine severity
        if health_results["status"] == "success":
            overall_score = health_results["overall_score"]
            if overall_score >= 90:
                severity = "success"
            elif overall_score >= 70:
                severity = "info"
            elif overall_score >= 50:
                severity = "warning"
            else:
                severity = "error"
        else:
            severity = "error"
        
        # Send notification
        self.send_telegram_notification(report, severity)
        
        # Save results
        test_results = {
            "timestamp": datetime.now().isoformat(),
            "health_results": health_results,
            "connectivity_results": connectivity_results,
            "report": report,
            "severity": severity
        }
        
        self.save_test_results(test_results)
        
        # Print summary
        print(f"✅ Test completed - Severity: {severity}")
        if health_results["status"] == "success":
            print(f"   Health Score: {health_results['overall_score']:.1f}%")
            print(f"   Services: {health_results['services']['healthy']}/{health_results['services']['total']}")
            print(f"   Alerts: {health_results['alerts_count']}")
        
        return test_results

    def start_continuous_testing(self):
        """Start continuous automated testing"""
        print("🚀 Starting Automated Test Runner")
        print(f"   Test Interval: {self.config['test_interval_hours']} hours")
        print(f"   Telegram Notifications: {'Enabled' if self.config['telegram_notifications'] else 'Disabled'}")
        print("=" * 50)
        
        # Send startup notification
        self.send_telegram_notification(
            f"🚀 **Automated Test Runner Started**\n\n"
            f"Test Interval: {self.config['test_interval_hours']} hours\n"
            f"Next test: {(datetime.now() + timedelta(hours=self.config['test_interval_hours'])).strftime('%H:%M')}",
            "info"
        )
        
        while True:
            try:
                # Run test
                self.run_comprehensive_test()
                
                # Wait for next test
                sleep_seconds = self.config["test_interval_hours"] * 3600
                print(f"⏰ Next test in {self.config['test_interval_hours']} hours")
                time.sleep(sleep_seconds)
                
            except KeyboardInterrupt:
                print("\n🛑 Test runner stopped by user")
                self.send_telegram_notification(
                    "🛑 **Automated Test Runner Stopped**\n\nTest runner stopped by user",
                    "warning"
                )
                break
            except Exception as e:
                print(f"❌ Error in test runner: {e}")
                time.sleep(300)  # Wait 5 minutes before retrying

def main():
    """Main function for standalone execution"""
    import sys
    
    runner = AutomatedTestRunner()
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "test":
            # Run single test
            runner.run_comprehensive_test()
        elif command == "continuous":
            # Start continuous testing
            runner.start_continuous_testing()
        else:
            print("Usage: python automated_test_runner.py [test|continuous]")
    else:
        # Default: run single test
        runner.run_comprehensive_test()

if __name__ == "__main__":
    main()
