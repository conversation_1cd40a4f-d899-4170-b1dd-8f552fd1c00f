#!/usr/bin/env python3
"""
Comprehensive Monitoring & Alerting System
Monitors ROI calculations, system health, and data quality
Date: June 11, 2025
"""

import asyncio
import aiohttp
import asyncpg
import smtplib
import json
import logging
import os
from datetime import datetime, timedelta
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart
from typing import Dict, List, Optional
import psycopg2
from dataclasses import dataclass

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/var/log/solar-prediction/monitoring.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class HealthCheck:
    service: str
    url: str
    status: str
    response_time: float
    error: Optional[str] = None

@dataclass
class ROIAlert:
    system_id: str
    current_roi: float
    previous_roi: float
    deviation: float
    threshold: float
    alert_type: str

@dataclass
class DataQualityIssue:
    table: str
    issue_type: str
    description: str
    severity: str
    count: int

class SolarMonitoringSystem:
    def __init__(self):
        self.db_config = {
            'host': os.getenv('DB_HOST', 'localhost'),
            'port': os.getenv('DB_PORT', 5432),
            'database': os.getenv('DB_NAME', 'solar_prediction'),
            'user': os.getenv('DB_USER', 'postgres'),
            'password': os.getenv('DB_PASSWORD', '')
        }
        
        self.services = {
            'Enhanced Billing System': 'http://localhost:8110/health',
            'Production Scripts API': 'http://localhost:8100/health',
            'GPU Prediction Service': 'http://localhost:8105/health',
            'Unified Forecast API': 'http://localhost:8120/health',
            'Charts API': 'http://localhost:8103/health'
        }
        
        self.roi_thresholds = {
            'deviation_percent': 20.0,  # Alert if ROI changes by more than 20%
            'minimum_roi': -10.0,       # Alert if ROI below -10%
            'maximum_roi': 50.0,        # Alert if ROI above 50%
            'payback_max': 50.0         # Alert if payback > 50 years
        }
        
        self.email_config = {
            'smtp_server': os.getenv('SMTP_SERVER', 'localhost'),
            'smtp_port': int(os.getenv('SMTP_PORT', 587)),
            'username': os.getenv('SMTP_USERNAME', ''),
            'password': os.getenv('SMTP_PASSWORD', ''),
            'from_email': os.getenv('FROM_EMAIL', 'solar-system@localhost'),
            'to_emails': os.getenv('ALERT_EMAILS', 'admin@localhost').split(',')
        }

    async def check_service_health(self) -> List[HealthCheck]:
        """Check health of all services"""
        health_checks = []
        
        async with aiohttp.ClientSession() as session:
            for service_name, url in self.services.items():
                try:
                    start_time = datetime.now()
                    async with session.get(url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                        end_time = datetime.now()
                        response_time = (end_time - start_time).total_seconds() * 1000
                        
                        if response.status == 200:
                            health_checks.append(HealthCheck(
                                service=service_name,
                                url=url,
                                status='healthy',
                                response_time=response_time
                            ))
                        else:
                            health_checks.append(HealthCheck(
                                service=service_name,
                                url=url,
                                status='unhealthy',
                                response_time=response_time,
                                error=f'HTTP {response.status}'
                            ))
                            
                except Exception as e:
                    health_checks.append(HealthCheck(
                        service=service_name,
                        url=url,
                        status='error',
                        response_time=0,
                        error=str(e)
                    ))
        
        return health_checks

    def check_roi_anomalies(self) -> List[ROIAlert]:
        """Check for ROI calculation anomalies"""
        alerts = []
        
        try:
            conn = psycopg2.connect(**self.db_config)
            cur = conn.cursor()
            
            # Check current ROI values for both systems
            for system_id in ['system1', 'system2']:
                try:
                    # Get current ROI from API
                    import requests
                    response = requests.get(f'http://localhost:8110/billing/enhanced/roi/{system_id}', timeout=10)
                    
                    if response.status_code == 200:
                        roi_data = response.json()
                        current_roi = roi_data.get('financial', {}).get('roi_percentage', 0)
                        payback_years = roi_data.get('financial', {}).get('payback_years')
                        
                        # Check for unrealistic values
                        if current_roi < self.roi_thresholds['minimum_roi']:
                            alerts.append(ROIAlert(
                                system_id=system_id,
                                current_roi=current_roi,
                                previous_roi=0,
                                deviation=0,
                                threshold=self.roi_thresholds['minimum_roi'],
                                alert_type='roi_too_low'
                            ))
                        
                        if current_roi > self.roi_thresholds['maximum_roi']:
                            alerts.append(ROIAlert(
                                system_id=system_id,
                                current_roi=current_roi,
                                previous_roi=0,
                                deviation=0,
                                threshold=self.roi_thresholds['maximum_roi'],
                                alert_type='roi_too_high'
                            ))
                        
                        if payback_years and payback_years > self.roi_thresholds['payback_max']:
                            alerts.append(ROIAlert(
                                system_id=system_id,
                                current_roi=current_roi,
                                previous_roi=0,
                                deviation=0,
                                threshold=self.roi_thresholds['payback_max'],
                                alert_type='payback_too_long'
                            ))
                        
                        # Store current ROI for trend analysis
                        cur.execute("""
                            INSERT INTO roi_monitoring (system_id, roi_percentage, payback_years, check_timestamp)
                            VALUES (%s, %s, %s, %s)
                            ON CONFLICT (system_id, DATE(check_timestamp)) 
                            DO UPDATE SET 
                                roi_percentage = EXCLUDED.roi_percentage,
                                payback_years = EXCLUDED.payback_years,
                                check_timestamp = EXCLUDED.check_timestamp
                        """, (system_id, current_roi, payback_years, datetime.now()))
                        
                except Exception as e:
                    logger.error(f"Error checking ROI for {system_id}: {e}")
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Error in ROI anomaly detection: {e}")
        
        return alerts

    def check_data_quality(self) -> List[DataQualityIssue]:
        """Check for data quality issues"""
        issues = []
        
        try:
            conn = psycopg2.connect(**self.db_config)
            cur = conn.cursor()
            
            # Check for missing production data
            cur.execute("""
                SELECT COUNT(*) FROM (
                    SELECT generate_series(
                        CURRENT_DATE - INTERVAL '7 days',
                        CURRENT_DATE,
                        '1 day'::interval
                    )::date as expected_date
                    EXCEPT
                    SELECT DISTINCT DATE(timestamp) 
                    FROM solax_data 
                    WHERE timestamp >= CURRENT_DATE - INTERVAL '7 days'
                ) missing_days
            """)
            
            missing_days = cur.fetchone()[0]
            if missing_days > 0:
                issues.append(DataQualityIssue(
                    table='solax_data',
                    issue_type='missing_data',
                    description=f'{missing_days} days of missing production data in last 7 days',
                    severity='high' if missing_days > 2 else 'medium',
                    count=missing_days
                ))
            
            # Check for zero production days (potential sensor issues)
            cur.execute("""
                SELECT COUNT(*) FROM (
                    SELECT DATE(timestamp) 
                    FROM solax_data 
                    WHERE timestamp >= CURRENT_DATE - INTERVAL '7 days'
                    GROUP BY DATE(timestamp)
                    HAVING MAX(yield_today) = 0
                ) zero_production_days
            """)
            
            zero_days = cur.fetchone()[0]
            if zero_days > 1:  # Allow 1 day for weather
                issues.append(DataQualityIssue(
                    table='solax_data',
                    issue_type='zero_production',
                    description=f'{zero_days} days with zero production in last 7 days',
                    severity='high',
                    count=zero_days
                ))
            
            # Check for duplicate records
            cur.execute("""
                SELECT COUNT(*) FROM (
                    SELECT timestamp, COUNT(*) 
                    FROM solax_data 
                    WHERE timestamp >= CURRENT_DATE - INTERVAL '1 day'
                    GROUP BY timestamp 
                    HAVING COUNT(*) > 1
                ) duplicates
            """)
            
            duplicates = cur.fetchone()[0]
            if duplicates > 0:
                issues.append(DataQualityIssue(
                    table='solax_data',
                    issue_type='duplicate_records',
                    description=f'{duplicates} duplicate timestamp records in last 24 hours',
                    severity='medium',
                    count=duplicates
                ))
            
            # Check tariff configuration integrity
            cur.execute("""
                SELECT COUNT(*) FROM tariff_configs 
                WHERE is_active = TRUE 
                AND effective_from <= NOW() 
                AND (effective_to IS NULL OR effective_to > NOW())
            """)
            
            active_tariffs = cur.fetchone()[0]
            if active_tariffs == 0:
                issues.append(DataQualityIssue(
                    table='tariff_configs',
                    issue_type='no_active_tariffs',
                    description='No active tariff configurations found',
                    severity='critical',
                    count=0
                ))
            
            conn.close()
            
        except Exception as e:
            logger.error(f"Error in data quality check: {e}")
            issues.append(DataQualityIssue(
                table='database',
                issue_type='connection_error',
                description=f'Database connection error: {str(e)}',
                severity='critical',
                count=1
            ))
        
        return issues

    def send_alert_email(self, subject: str, body: str):
        """Send alert email"""
        try:
            msg = MimeMultipart()
            msg['From'] = self.email_config['from_email']
            msg['To'] = ', '.join(self.email_config['to_emails'])
            msg['Subject'] = f"[Solar System Alert] {subject}"
            
            msg.attach(MimeText(body, 'html'))
            
            server = smtplib.SMTP(self.email_config['smtp_server'], self.email_config['smtp_port'])
            if self.email_config['username']:
                server.starttls()
                server.login(self.email_config['username'], self.email_config['password'])
            
            server.send_message(msg)
            server.quit()
            
            logger.info(f"Alert email sent: {subject}")
            
        except Exception as e:
            logger.error(f"Failed to send alert email: {e}")

    def generate_monitoring_report(self, health_checks: List[HealthCheck], 
                                 roi_alerts: List[ROIAlert], 
                                 data_issues: List[DataQualityIssue]) -> str:
        """Generate HTML monitoring report"""
        
        # Count issues by severity
        critical_issues = len([issue for issue in data_issues if issue.severity == 'critical'])
        high_issues = len([issue for issue in data_issues if issue.severity == 'high'])
        unhealthy_services = len([check for check in health_checks if check.status != 'healthy'])
        
        html = f"""
        <html>
        <head>
            <title>Solar System Monitoring Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #f0f0f0; padding: 10px; border-radius: 5px; }}
                .section {{ margin: 20px 0; }}
                .healthy {{ color: green; }}
                .warning {{ color: orange; }}
                .error {{ color: red; }}
                .critical {{ color: red; font-weight: bold; }}
                table {{ border-collapse: collapse; width: 100%; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🌞 Solar System Monitoring Report</h1>
                <p><strong>Generated:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                <p><strong>Status:</strong> 
                    {'<span class="critical">CRITICAL</span>' if critical_issues > 0 else 
                     '<span class="error">WARNING</span>' if high_issues > 0 or unhealthy_services > 0 else 
                     '<span class="healthy">HEALTHY</span>'}
                </p>
            </div>
            
            <div class="section">
                <h2>📊 Summary</h2>
                <ul>
                    <li>Services: {len([c for c in health_checks if c.status == 'healthy'])}/{len(health_checks)} healthy</li>
                    <li>ROI Alerts: {len(roi_alerts)}</li>
                    <li>Data Quality Issues: {len(data_issues)} ({critical_issues} critical, {high_issues} high)</li>
                </ul>
            </div>
            
            <div class="section">
                <h2>🚀 Service Health</h2>
                <table>
                    <tr><th>Service</th><th>Status</th><th>Response Time</th><th>Error</th></tr>
        """
        
        for check in health_checks:
            status_class = 'healthy' if check.status == 'healthy' else 'error'
            html += f"""
                    <tr>
                        <td>{check.service}</td>
                        <td class="{status_class}">{check.status.upper()}</td>
                        <td>{check.response_time:.1f}ms</td>
                        <td>{check.error or '-'}</td>
                    </tr>
            """
        
        html += """
                </table>
            </div>
        """
        
        if roi_alerts:
            html += """
            <div class="section">
                <h2>📈 ROI Alerts</h2>
                <table>
                    <tr><th>System</th><th>Current ROI</th><th>Alert Type</th><th>Threshold</th></tr>
            """
            
            for alert in roi_alerts:
                html += f"""
                    <tr>
                        <td>{alert.system_id}</td>
                        <td>{alert.current_roi:.2f}%</td>
                        <td class="warning">{alert.alert_type.replace('_', ' ').title()}</td>
                        <td>{alert.threshold}</td>
                    </tr>
                """
            
            html += """
                </table>
            </div>
            """
        
        if data_issues:
            html += """
            <div class="section">
                <h2>🔍 Data Quality Issues</h2>
                <table>
                    <tr><th>Table</th><th>Issue Type</th><th>Description</th><th>Severity</th><th>Count</th></tr>
            """
            
            for issue in data_issues:
                severity_class = issue.severity
                html += f"""
                    <tr>
                        <td>{issue.table}</td>
                        <td>{issue.issue_type.replace('_', ' ').title()}</td>
                        <td>{issue.description}</td>
                        <td class="{severity_class}">{issue.severity.upper()}</td>
                        <td>{issue.count}</td>
                    </tr>
                """
            
            html += """
                </table>
            </div>
            """
        
        html += """
            <div class="section">
                <h2>🔧 Recommended Actions</h2>
                <ul>
        """
        
        if unhealthy_services > 0:
            html += "<li>Restart unhealthy services and check logs</li>"
        
        if critical_issues > 0:
            html += "<li>Address critical data quality issues immediately</li>"
        
        if len(roi_alerts) > 0:
            html += "<li>Review ROI calculation parameters and data sources</li>"
        
        if len(data_issues) > 0:
            html += "<li>Check data collection processes and fix quality issues</li>"
        
        if unhealthy_services == 0 and critical_issues == 0 and len(roi_alerts) == 0:
            html += "<li>System is healthy - continue regular monitoring</li>"
        
        html += """
                </ul>
            </div>
            
            <div class="section">
                <p><em>This report was generated automatically by the Solar System Monitoring Service.</em></p>
            </div>
        </body>
        </html>
        """
        
        return html

    async def run_monitoring_cycle(self):
        """Run complete monitoring cycle"""
        logger.info("Starting monitoring cycle...")
        
        # Perform all checks
        health_checks = await self.check_service_health()
        roi_alerts = self.check_roi_anomalies()
        data_issues = self.check_data_quality()
        
        # Log results
        healthy_services = len([c for c in health_checks if c.status == 'healthy'])
        logger.info(f"Health check: {healthy_services}/{len(health_checks)} services healthy")
        logger.info(f"ROI alerts: {len(roi_alerts)}")
        logger.info(f"Data quality issues: {len(data_issues)}")
        
        # Generate report
        report = self.generate_monitoring_report(health_checks, roi_alerts, data_issues)
        
        # Save report
        report_file = f"/var/log/solar-prediction/monitoring_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
        with open(report_file, 'w') as f:
            f.write(report)
        
        # Send alerts if needed
        critical_issues = [issue for issue in data_issues if issue.severity == 'critical']
        unhealthy_services = [check for check in health_checks if check.status != 'healthy']
        
        if critical_issues or unhealthy_services or roi_alerts:
            subject = f"Solar System Alert - {len(critical_issues)} critical issues, {len(unhealthy_services)} service issues"
            self.send_alert_email(subject, report)
        
        logger.info("Monitoring cycle completed")
        
        return {
            'health_checks': health_checks,
            'roi_alerts': roi_alerts,
            'data_issues': data_issues,
            'report_file': report_file
        }

async def main():
    """Main monitoring function"""
    monitoring = SolarMonitoringSystem()
    
    # Create monitoring table if it doesn't exist
    try:
        conn = psycopg2.connect(**monitoring.db_config)
        cur = conn.cursor()
        
        cur.execute("""
            CREATE TABLE IF NOT EXISTS roi_monitoring (
                id SERIAL PRIMARY KEY,
                system_id VARCHAR(20) NOT NULL,
                roi_percentage NUMERIC(8,4),
                payback_years NUMERIC(8,2),
                check_timestamp TIMESTAMP DEFAULT NOW(),
                UNIQUE(system_id, DATE(check_timestamp))
            )
        """)
        
        conn.commit()
        conn.close()
        
    except Exception as e:
        logger.error(f"Error creating monitoring table: {e}")
    
    # Run monitoring cycle
    results = await monitoring.run_monitoring_cycle()
    
    # Print summary
    print("\n🌞 SOLAR SYSTEM MONITORING SUMMARY")
    print("=" * 50)
    print(f"Services: {len([c for c in results['health_checks'] if c.status == 'healthy'])}/{len(results['health_checks'])} healthy")
    print(f"ROI Alerts: {len(results['roi_alerts'])}")
    print(f"Data Issues: {len(results['data_issues'])}")
    print(f"Report: {results['report_file']}")
    
    return results

if __name__ == "__main__":
    asyncio.run(main())
