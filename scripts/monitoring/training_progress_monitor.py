#!/usr/bin/env python3
"""
Training Progress Monitor
========================

Real-time monitoring για training progress των enhanced models.

Δημιουργήθηκε: 2025-06-05
"""

import time
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TrainingProgressMonitor:
    """
    Real-time monitor για training progress
    """
    
    def __init__(self):
        self.monitor_start = datetime.now()
        
        # Paths to monitor
        self.trained_models_dir = Path("models/trained_seasonal")
        self.enhanced_suite_dir = Path("models/enhanced_suite")
        
        # Expected models
        self.expected_seasonal_models = [
            'spring_system1_enhanced', 'spring_system2_enhanced',
            'summer_system1_enhanced', 'summer_system2_enhanced',
            'autumn_system1_enhanced', 'autumn_system2_enhanced',
            'winter_system1_enhanced', 'winter_system2_enhanced'
        ]
        
        logger.info("📊 Initialized TrainingProgressMonitor")
    
    def check_training_progress(self) -> Dict[str, Any]:
        """Check current training progress"""
        
        progress = {
            'timestamp': datetime.now().isoformat(),
            'models_completed': 0,
            'models_in_progress': 0,
            'models_pending': 0,
            'completed_models': [],
            'training_status': 'unknown'
        }
        
        # Check trained models directory
        if self.trained_models_dir.exists():
            for model_name in self.expected_seasonal_models:
                model_dir = self.trained_models_dir / model_name
                
                if model_dir.exists() and (model_dir / "metadata.json").exists():
                    progress['models_completed'] += 1
                    progress['completed_models'].append(model_name)
        
        # Calculate remaining
        progress['models_pending'] = len(self.expected_seasonal_models) - progress['models_completed']
        
        # Determine status
        if progress['models_completed'] == 0:
            progress['training_status'] = 'starting'
        elif progress['models_completed'] == len(self.expected_seasonal_models):
            progress['training_status'] = 'completed'
        else:
            progress['training_status'] = 'in_progress'
        
        return progress
    
    def display_progress_dashboard(self, progress: Dict[str, Any]):
        """Display real-time progress dashboard"""
        
        total_models = len(self.expected_seasonal_models)
        completed = progress['models_completed']
        pending = progress['models_pending']
        completion_rate = (completed / total_models) * 100
        
        print(f"\n📊 SEASONAL MODELS TRAINING PROGRESS")
        print(f"=" * 60)
        print(f"🕐 Time: {datetime.now().strftime('%H:%M:%S')}")
        print(f"📈 Progress: {completed}/{total_models} ({completion_rate:.1f}%)")
        print(f"✅ Completed: {completed}")
        print(f"⏳ Pending: {pending}")
        print(f"🎯 Status: {progress['training_status'].upper()}")
        
        # Progress bar
        bar_length = 40
        filled_length = int(bar_length * completed / total_models)
        bar = '█' * filled_length + '░' * (bar_length - filled_length)
        print(f"📊 [{bar}] {completion_rate:.1f}%")
        
        # Completed models
        if progress['completed_models']:
            print(f"\n✅ COMPLETED MODELS:")
            for model in progress['completed_models']:
                print(f"   ✅ {model}")
        
        # Pending models
        pending_models = [m for m in self.expected_seasonal_models if m not in progress['completed_models']]
        if pending_models:
            print(f"\n⏳ PENDING MODELS:")
            for model in pending_models[:3]:  # Show first 3
                print(f"   ⏳ {model}")
            if len(pending_models) > 3:
                print(f"   ... and {len(pending_models) - 3} more")
        
        print(f"=" * 60)
    
    def check_model_performance(self) -> Dict[str, Any]:
        """Check performance of completed models"""
        
        performance_summary = {
            'models_analyzed': 0,
            'average_r2': 0,
            'average_mae': 0,
            'targets_achieved': 0,
            'model_details': {}
        }
        
        if not self.trained_models_dir.exists():
            return performance_summary
        
        total_r2 = 0
        total_mae = 0
        models_with_performance = 0
        
        for model_name in self.expected_seasonal_models:
            model_dir = self.trained_models_dir / model_name
            metadata_file = model_dir / "metadata.json"
            
            if metadata_file.exists():
                try:
                    import json
                    with open(metadata_file, 'r') as f:
                        metadata = json.load(f)
                    
                    performance = metadata.get('performance', {})
                    r2 = performance.get('r2', 0)
                    mae = performance.get('mae', 0)
                    target_achieved = metadata.get('target_achieved', False)
                    
                    if r2 > 0 and mae > 0:  # Valid performance data
                        total_r2 += r2
                        total_mae += mae
                        models_with_performance += 1
                        
                        if target_achieved:
                            performance_summary['targets_achieved'] += 1
                        
                        performance_summary['model_details'][model_name] = {
                            'r2': r2,
                            'mae': mae,
                            'target_achieved': target_achieved
                        }
                
                except Exception as e:
                    logger.warning(f"⚠️ Could not read performance για {model_name}: {e}")
        
        # Calculate averages
        if models_with_performance > 0:
            performance_summary['average_r2'] = total_r2 / models_with_performance
            performance_summary['average_mae'] = total_mae / models_with_performance
        
        performance_summary['models_analyzed'] = models_with_performance
        
        return performance_summary
    
    def run_continuous_monitoring(self, duration_minutes: int = 30, interval_seconds: int = 30):
        """Run continuous monitoring για training progress"""
        
        logger.info(f"🔄 Starting continuous training monitoring...")
        logger.info(f"📊 Duration: {duration_minutes} minutes")
        logger.info(f"⏱️ Check interval: {interval_seconds} seconds")
        
        start_time = datetime.now()
        end_time = start_time + timedelta(minutes=duration_minutes)
        
        last_completed = 0
        
        while datetime.now() < end_time:
            try:
                # Check progress
                progress = self.check_training_progress()
                
                # Display dashboard
                self.display_progress_dashboard(progress)
                
                # Check για new completions
                current_completed = progress['models_completed']
                if current_completed > last_completed:
                    logger.info(f"🎉 New model completed! Total: {current_completed}/8")
                    
                    # Check performance of completed models
                    performance = self.check_model_performance()
                    if performance['models_analyzed'] > 0:
                        print(f"\n📈 PERFORMANCE SUMMARY:")
                        print(f"   Average R²: {performance['average_r2']:.4f}")
                        print(f"   Average MAE: {performance['average_mae']:.3f}")
                        print(f"   Targets achieved: {performance['targets_achieved']}/{performance['models_analyzed']}")
                
                last_completed = current_completed
                
                # Check if training completed
                if progress['training_status'] == 'completed':
                    logger.info("🎉 All seasonal models training completed!")
                    break
                
                # Wait για next check
                time.sleep(interval_seconds)
                
            except KeyboardInterrupt:
                logger.info("⏹️ Monitoring stopped by user")
                break
            except Exception as e:
                logger.error(f"❌ Monitoring error: {e}")
                time.sleep(interval_seconds)
        
        # Final summary
        final_progress = self.check_training_progress()
        final_performance = self.check_model_performance()
        
        print(f"\n🏁 FINAL TRAINING SUMMARY")
        print(f"=" * 50)
        print(f"✅ Models completed: {final_progress['models_completed']}/8")
        print(f"📈 Average R²: {final_performance['average_r2']:.4f}")
        print(f"📈 Average MAE: {final_performance['average_mae']:.3f}")
        print(f"🎯 Targets achieved: {final_performance['targets_achieved']}/{final_performance['models_analyzed']}")
        
        return {
            'final_progress': final_progress,
            'final_performance': final_performance
        }

def main():
    """Main monitoring function"""
    try:
        monitor = TrainingProgressMonitor()
        
        print("\n📊 TRAINING PROGRESS MONITORING")
        print("=" * 50)
        print("Options:")
        print("1. Single progress check")
        print("2. Continuous monitoring (30 minutes)")
        print("3. Quick status")
        
        choice = input("\nChoose option (1-3): ").strip()
        
        if choice == "1":
            print("\n📊 Single progress check...")
            progress = monitor.check_training_progress()
            monitor.display_progress_dashboard(progress)
            
            performance = monitor.check_model_performance()
            if performance['models_analyzed'] > 0:
                print(f"\n📈 PERFORMANCE SUMMARY:")
                print(f"   Models analyzed: {performance['models_analyzed']}")
                print(f"   Average R²: {performance['average_r2']:.4f}")
                print(f"   Average MAE: {performance['average_mae']:.3f}")
                print(f"   Targets achieved: {performance['targets_achieved']}")
        
        elif choice == "2":
            print("\n🔄 Starting continuous monitoring...")
            results = monitor.run_continuous_monitoring(duration_minutes=30, interval_seconds=30)
        
        elif choice == "3":
            print("\n⚡ Quick status check...")
            progress = monitor.check_training_progress()
            
            print(f"\n📊 QUICK STATUS:")
            print(f"   Progress: {progress['models_completed']}/8")
            print(f"   Status: {progress['training_status'].upper()}")
        
        else:
            # Default: single check
            print("\n📊 Default progress check...")
            progress = monitor.check_training_progress()
            monitor.display_progress_dashboard(progress)
        
        return True
        
    except Exception as e:
        print(f"❌ Training monitoring failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
