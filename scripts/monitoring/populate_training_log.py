#!/usr/bin/env python3
"""
Populate Training Log
====================

Script to populate the model_training_log table with sample training records
to demonstrate the model retrain monitoring system.

Created: June 12, 2025
"""

import asyncio
import asyncpg
import os
import json
from datetime import datetime, timedelta
import random

async def populate_training_log():
    """Populate training log with sample data"""
    db_url = f"postgresql://{os.getenv('DB_USER', 'postgres')}:{os.getenv('DB_PASSWORD', '')}@{os.getenv('DB_HOST', 'localhost')}:{os.getenv('DB_PORT', 5432)}/{os.getenv('DB_NAME', 'solar_prediction')}"
    
    # Sample training records
    training_records = [
        # Hybrid ML Ensemble - Recent training (3 days ago)
        {
            "model_name": "Hybrid_ML_Ensemble",
            "model_version": "5.0.0",
            "system_id": "both",
            "training_date": datetime.now() - timedelta(days=3),
            "training_duration_minutes": 45,
            "dataset_size": 50000,
            "training_accuracy": 0.951,
            "validation_accuracy": 0.943,
            "test_accuracy": 0.941,
            "r2_score": 0.9431,
            "mae": 2.1,
            "rmse": 3.2,
            "feature_count": 25,
            "hyperparameters": {
                "n_estimators": 100,
                "max_depth": 8,
                "learning_rate": 0.1
            },
            "model_path": "models/production/hybrid_ml_ensemble_v5.pkl",
            "training_script": "scripts/training/ensemble_model_trainer.py",
            "training_status": "completed",
            "performance_notes": "Excellent performance, production ready",
            "created_by": "automated_system"
        },
        
        # Hybrid ML Ensemble - Previous training (10 days ago)
        {
            "model_name": "Hybrid_ML_Ensemble",
            "model_version": "4.9.0",
            "system_id": "both",
            "training_date": datetime.now() - timedelta(days=10),
            "training_duration_minutes": 42,
            "dataset_size": 48000,
            "training_accuracy": 0.948,
            "validation_accuracy": 0.940,
            "test_accuracy": 0.938,
            "r2_score": 0.9401,
            "mae": 2.3,
            "rmse": 3.4,
            "feature_count": 25,
            "hyperparameters": {
                "n_estimators": 95,
                "max_depth": 7,
                "learning_rate": 0.1
            },
            "model_path": "models/production/hybrid_ml_ensemble_v4.9.pkl",
            "training_script": "scripts/training/ensemble_model_trainer.py",
            "training_status": "completed",
            "performance_notes": "Good performance, minor improvements needed",
            "created_by": "automated_system"
        },
        
        # Seasonal Models - Overdue training (35 days ago)
        {
            "model_name": "Seasonal_Models",
            "model_version": "2.1.0",
            "system_id": "both",
            "training_date": datetime.now() - timedelta(days=35),
            "training_duration_minutes": 120,
            "dataset_size": 75000,
            "training_accuracy": 0.892,
            "validation_accuracy": 0.885,
            "test_accuracy": 0.881,
            "r2_score": 0.8851,
            "mae": 3.8,
            "rmse": 5.2,
            "feature_count": 30,
            "hyperparameters": {
                "seasonal_periods": [24, 168, 8760],
                "trend_components": 3,
                "seasonal_strength": 0.8
            },
            "model_path": "models/production/seasonal_models_v2.1.pkl",
            "training_script": "scripts/training/seasonal_models_trainer.py",
            "training_status": "completed",
            "performance_notes": "Seasonal patterns captured well, needs monthly update",
            "created_by": "data_scientist"
        },
        
        # GPU Models - Recent training (5 days ago)
        {
            "model_name": "GPU_Models",
            "model_version": "3.2.0",
            "system_id": "system1",
            "training_date": datetime.now() - timedelta(days=5),
            "training_duration_minutes": 25,
            "dataset_size": 45000,
            "training_accuracy": 0.955,
            "validation_accuracy": 0.948,
            "test_accuracy": 0.945,
            "r2_score": 0.9481,
            "mae": 1.9,
            "rmse": 2.8,
            "feature_count": 22,
            "hyperparameters": {
                "gpu_acceleration": True,
                "batch_size": 1024,
                "epochs": 50
            },
            "model_path": "models/production/gpu_models_system1_v3.2.pkl",
            "training_script": "scripts/production/gpu_prediction_service.py",
            "training_status": "completed",
            "performance_notes": "GPU acceleration improved training speed by 3x",
            "created_by": "gpu_trainer"
        },
        
        # GPU Models - System 2
        {
            "model_name": "GPU_Models",
            "model_version": "3.2.0",
            "system_id": "system2",
            "training_date": datetime.now() - timedelta(days=5),
            "training_duration_minutes": 27,
            "dataset_size": 43000,
            "training_accuracy": 0.952,
            "validation_accuracy": 0.945,
            "test_accuracy": 0.942,
            "r2_score": 0.9451,
            "mae": 2.0,
            "rmse": 2.9,
            "feature_count": 22,
            "hyperparameters": {
                "gpu_acceleration": True,
                "batch_size": 1024,
                "epochs": 50
            },
            "model_path": "models/production/gpu_models_system2_v3.2.pkl",
            "training_script": "scripts/production/gpu_prediction_service.py",
            "training_status": "completed",
            "performance_notes": "Excellent performance for System 2",
            "created_by": "gpu_trainer"
        },
        
        # Hourly Models - Old training (20 days ago) - Performance degraded
        {
            "model_name": "Hourly_Models",
            "model_version": "1.8.0",
            "system_id": "both",
            "training_date": datetime.now() - timedelta(days=20),
            "training_duration_minutes": 90,
            "dataset_size": 60000,
            "training_accuracy": 0.875,
            "validation_accuracy": 0.868,
            "test_accuracy": 0.862,
            "r2_score": 0.8621,
            "mae": 4.2,
            "rmse": 6.1,
            "feature_count": 28,
            "hyperparameters": {
                "hourly_resolution": True,
                "lookback_hours": 72,
                "forecast_horizon": 24
            },
            "model_path": "models/production/hourly_models_v1.8.pkl",
            "training_script": "scripts/training/enhanced_model_trainer.py",
            "training_status": "completed",
            "performance_notes": "Performance below threshold, needs retraining",
            "created_by": "hourly_trainer"
        }
    ]
    
    try:
        conn = await asyncpg.connect(db_url)
        
        # Clear existing records (for demo purposes)
        await conn.execute("DELETE FROM model_training_log")
        print("🗑️ Cleared existing training log records")
        
        # Insert sample records
        insert_sql = """
        INSERT INTO model_training_log (
            model_name, model_version, system_id, training_date,
            training_duration_minutes, dataset_size, training_accuracy,
            validation_accuracy, test_accuracy, r2_score, mae, rmse,
            feature_count, hyperparameters, model_path, training_script,
            training_status, performance_notes, created_by
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19)
        """
        
        for record in training_records:
            await conn.execute(
                insert_sql,
                record["model_name"],
                record["model_version"],
                record["system_id"],
                record["training_date"],
                record["training_duration_minutes"],
                record["dataset_size"],
                record["training_accuracy"],
                record["validation_accuracy"],
                record["test_accuracy"],
                record["r2_score"],
                record["mae"],
                record["rmse"],
                record["feature_count"],
                json.dumps(record["hyperparameters"]),
                record["model_path"],
                record["training_script"],
                record["training_status"],
                record["performance_notes"],
                record["created_by"]
            )
        
        await conn.close()
        
        print(f"✅ Successfully inserted {len(training_records)} training records")
        print("\nSample data includes:")
        print("- Hybrid_ML_Ensemble: Recent training (3 days ago) - ON SCHEDULE")
        print("- Seasonal_Models: Old training (35 days ago) - OVERDUE")
        print("- GPU_Models: Recent training (5 days ago) - ON SCHEDULE")
        print("- Hourly_Models: Old training (20 days ago) - PERFORMANCE DEGRADED")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to populate training log: {e}")
        return False

async def main():
    """Main function"""
    print("📊 Populating Model Training Log with Sample Data")
    print("=" * 50)
    
    success = await populate_training_log()
    
    if success:
        print("\n🎉 Training log populated successfully!")
        print("You can now run the model retrain monitor to see the results:")
        print("python scripts/monitoring/model_retrain_monitor.py")
    else:
        print("\n❌ Failed to populate training log")

if __name__ == "__main__":
    asyncio.run(main())
