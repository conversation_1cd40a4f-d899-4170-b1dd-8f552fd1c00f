#!/usr/bin/env python3
"""
Simple Data Status Check
Quick check of all data tables and their status
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import psycopg2
from datetime import datetime, timedelta
import subprocess

def check_table_status(table_name):
    """Check status of a specific table"""
    
    try:
        conn = psycopg2.connect(
            host='localhost',
            database='solar_prediction',
            user='postgres',
            password='postgres'
        )
        
        with conn.cursor() as cur:
            # Check if table exists
            cur.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = %s
                );
            """, (table_name,))
            
            if not cur.fetchone()[0]:
                return {'exists': False}
            
            # Get basic count
            cur.execute(f"SELECT COUNT(*) FROM {table_name}")
            total_count = cur.fetchone()[0]
            
            # Try to get timestamp info
            try:
                cur.execute(f"""
                    SELECT 
                        MIN(timestamp) as earliest,
                        MAX(timestamp) as latest
                    FROM {table_name}
                    WHERE timestamp IS NOT NULL
                """)
                
                result = cur.fetchone()
                earliest = result[0] if result else None
                latest = result[1] if result else None
                
                # Get recent records
                cur.execute(f"""
                    SELECT COUNT(*) 
                    FROM {table_name}
                    WHERE timestamp >= %s
                """, (datetime.now() - timedelta(hours=24),))
                
                last_24h = cur.fetchone()[0]
                
            except:
                earliest = None
                latest = None
                last_24h = 0
            
            conn.close()
            
            return {
                'exists': True,
                'total_records': total_count,
                'earliest': earliest,
                'latest': latest,
                'last_24h': last_24h
            }
            
    except Exception as e:
        return {'exists': False, 'error': str(e)}

def check_schedules():
    """Check for scheduled tasks"""
    
    schedules = {}
    
    # Check crontab
    try:
        result = subprocess.run(['crontab', '-l'], capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            for line in lines:
                if 'solar-prediction-project' in line and not line.startswith('#'):
                    if 'nasa_power' in line:
                        schedules['NASA POWER'] = {'type': 'crontab', 'command': line}
                    elif 'solax' in line:
                        schedules['SolaX'] = {'type': 'crontab', 'command': line}
                    elif 'weather' in line:
                        schedules['Weather'] = {'type': 'crontab', 'command': line}
    except:
        pass
    
    # Check database schedules
    try:
        conn = psycopg2.connect(
            host='localhost',
            database='solar_prediction',
            user='postgres',
            password='postgres'
        )
        
        with conn.cursor() as cur:
            # Check if schedule table exists
            cur.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'task_schedules'
                );
            """)
            
            if cur.fetchone()[0]:
                cur.execute("SELECT COUNT(*) FROM task_schedules")
                schedule_count = cur.fetchone()[0]
                schedules['Database Schedules'] = {'type': 'database', 'count': schedule_count}
        
        conn.close()
    except:
        pass
    
    return schedules

def main():
    """Main status check"""
    
    print("📊 SIMPLE DATA STATUS CHECK")
    print("=" * 50)
    print(f"📅 Check Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Define key data tables
    data_tables = {
        'nasa_power_data': 'NASA POWER API Data',
        'solax_data': 'SolaX System 1 Data',
        'solax_data2': 'SolaX System 2 Data',
        'weather_data': 'Open-Meteo Weather Data',
        'cams_radiation_data': 'CAMS Solar Radiation Data',
        'era5_data': 'ERA5 Reanalysis Data',
        'predictions': 'Solar Predictions',
        'cams_aerosol_data': 'CAMS Aerosol Data',
        'satellite_data': 'Satellite Data'
    }
    
    print(f"\n🔍 Checking {len(data_tables)} data tables...")
    print("-" * 50)
    
    total_records = 0
    active_tables = 0
    
    for table_name, description in data_tables.items():
        print(f"\n📊 {description} ({table_name})")
        
        status = check_table_status(table_name)
        
        if not status.get('exists', False):
            print(f"   ❌ Table not found")
            if 'error' in status:
                print(f"   Error: {status['error']}")
        else:
            records = status.get('total_records', 0)
            total_records += records
            
            print(f"   📈 Records: {records:,}")
            
            if status.get('earliest'):
                print(f"   📅 Earliest: {status['earliest']}")
            
            if status.get('latest'):
                latest = status['latest']
                print(f"   📅 Latest: {latest}")
                
                # Calculate freshness
                if latest:
                    hours_ago = (datetime.now() - latest.replace(tzinfo=None)).total_seconds() / 3600
                    
                    if hours_ago < 1:
                        print(f"   🟢 Status: ACTIVE ({hours_ago*60:.0f} min ago)")
                        active_tables += 1
                    elif hours_ago < 24:
                        print(f"   🟡 Status: RECENT ({hours_ago:.1f} hours ago)")
                        active_tables += 1
                    elif hours_ago < 168:
                        print(f"   🟠 Status: STALE ({hours_ago/24:.1f} days ago)")
                    else:
                        print(f"   🔴 Status: VERY STALE ({hours_ago/24:.1f} days ago)")
            else:
                print(f"   ⚪ Status: NO TIMESTAMP DATA")
            
            last_24h = status.get('last_24h', 0)
            if last_24h > 0:
                print(f"   📊 Last 24h: {last_24h:,} records")
    
    print(f"\n" + "=" * 50)
    print("📊 SUMMARY")
    print("=" * 50)
    print(f"📋 Total Tables Checked: {len(data_tables)}")
    print(f"✅ Active/Recent Tables: {active_tables}")
    print(f"📊 Total Records: {total_records:,}")
    print(f"📈 Health Score: {(active_tables/len(data_tables)*100):.1f}%")
    
    # Check schedules
    print(f"\n⏰ SCHEDULE STATUS")
    print("-" * 30)
    
    schedules = check_schedules()
    
    if schedules:
        for name, info in schedules.items():
            print(f"📅 {name}: {info['type']}")
            if 'count' in info:
                print(f"   Count: {info['count']}")
            elif 'command' in info:
                print(f"   Command: {info['command'][:60]}...")
    else:
        print("❌ No schedules found")
    
    # Recommendations
    print(f"\n🎯 RECOMMENDATIONS")
    print("-" * 30)
    
    if active_tables == 0:
        print("🚨 CRITICAL: No active data sources")
        print("   → Check all data collectors")
        print("   → Verify API connections")
        print("   → Setup automated collection")
    elif active_tables < len(data_tables) * 0.5:
        print("⚠️ WARNING: Limited active data sources")
        print("   → Activate more collectors")
        print("   → Check stale data sources")
    else:
        print("✅ Good data coverage")
        print("   → Monitor data freshness")
        print("   → Maintain collection schedules")
    
    if not schedules:
        print("🚨 No automated schedules found")
        print("   → Setup crontab or database schedules")
        print("   → Automate data collection")

if __name__ == "__main__":
    main()
