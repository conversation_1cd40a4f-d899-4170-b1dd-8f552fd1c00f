#!/usr/bin/env python3
"""
PERFORMANCE ALERTING SYSTEM
Alert system for seasonal models performance issues
Created: June 4, 2025
"""

import os
import sys
import json
import requests
import psycopg2
from datetime import datetime, timedelta
from pathlib import Path
import logging
from typing import Dict, List, Any

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PerformanceAlerting:
    """Performance alerting system for seasonal models"""
    
    def __init__(self):
        self.db_configs = [
            "postgresql://grlv:Gr1234@localhost:5433/solar_prediction",
            "postgresql://postgres:postgres@localhost:5433/solar_prediction"
        ]
        
        # Alert thresholds
        self.thresholds = {
            'min_success_rate': 90.0,  # %
            'max_response_time': 500.0,  # ms
            'min_confidence': 0.80,  # confidence score
            'max_error_rate': 10.0,  # %
            'check_period_minutes': 60  # Check last hour
        }
        
        # Telegram configuration
        self.telegram_config = {
            'bot_token': '**********************************************',
            'chat_id': '1510889515'
        }
        
        logger.info("🚨 Performance Alerting System initialized")
    
    def connect_database(self):
        """Connect to PostgreSQL database"""
        for config in self.db_configs:
            try:
                conn = psycopg2.connect(config)
                return conn
            except Exception as e:
                continue
        return None
    
    def check_performance_metrics(self) -> Dict[str, Any]:
        """Check recent performance metrics against thresholds"""
        conn = self.connect_database()
        if not conn:
            logger.error("❌ Cannot connect to database")
            return {}
        
        try:
            cursor = conn.cursor()
            
            # Check metrics from last hour
            check_time = datetime.now() - timedelta(minutes=self.thresholds['check_period_minutes'])
            
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_tests,
                    COUNT(*) FILTER (WHERE status = 'success') as successful_tests,
                    COUNT(*) FILTER (WHERE status != 'success') as failed_tests,
                    AVG(response_time_ms) FILTER (WHERE status = 'success') as avg_response_time,
                    AVG(confidence_score) FILTER (WHERE status = 'success') as avg_confidence,
                    MAX(response_time_ms) FILTER (WHERE status = 'success') as max_response_time,
                    MIN(confidence_score) FILTER (WHERE status = 'success') as min_confidence
                FROM performance_metrics 
                WHERE timestamp > %s
            """, (check_time,))
            
            result = cursor.fetchone()
            
            if not result or result[0] == 0:
                return {
                    'status': 'no_data',
                    'message': 'No performance data found in the last hour',
                    'alerts': []
                }
            
            total, successful, failed, avg_response, avg_confidence, max_response, min_confidence = result
            
            success_rate = (successful / total * 100) if total > 0 else 0
            error_rate = (failed / total * 100) if total > 0 else 0
            
            metrics = {
                'check_time': datetime.now().isoformat(),
                'period_checked': f"Last {self.thresholds['check_period_minutes']} minutes",
                'total_tests': total,
                'successful_tests': successful,
                'failed_tests': failed,
                'success_rate': success_rate,
                'error_rate': error_rate,
                'avg_response_time_ms': avg_response or 0,
                'max_response_time_ms': max_response or 0,
                'avg_confidence': avg_confidence or 0,
                'min_confidence': min_confidence or 0,
                'alerts': []
            }
            
            # Check thresholds and generate alerts
            if success_rate < self.thresholds['min_success_rate']:
                metrics['alerts'].append({
                    'type': 'low_success_rate',
                    'severity': 'high',
                    'message': f"Success rate {success_rate:.1f}% below threshold {self.thresholds['min_success_rate']}%",
                    'current_value': success_rate,
                    'threshold': self.thresholds['min_success_rate']
                })
            
            if avg_response and avg_response > self.thresholds['max_response_time']:
                metrics['alerts'].append({
                    'type': 'high_response_time',
                    'severity': 'medium',
                    'message': f"Average response time {avg_response:.1f}ms above threshold {self.thresholds['max_response_time']}ms",
                    'current_value': avg_response,
                    'threshold': self.thresholds['max_response_time']
                })
            
            if avg_confidence and avg_confidence < self.thresholds['min_confidence']:
                metrics['alerts'].append({
                    'type': 'low_confidence',
                    'severity': 'medium',
                    'message': f"Average confidence {avg_confidence:.3f} below threshold {self.thresholds['min_confidence']}",
                    'current_value': avg_confidence,
                    'threshold': self.thresholds['min_confidence']
                })
            
            if error_rate > self.thresholds['max_error_rate']:
                metrics['alerts'].append({
                    'type': 'high_error_rate',
                    'severity': 'high',
                    'message': f"Error rate {error_rate:.1f}% above threshold {self.thresholds['max_error_rate']}%",
                    'current_value': error_rate,
                    'threshold': self.thresholds['max_error_rate']
                })
            
            cursor.close()
            conn.close()
            
            return metrics
            
        except Exception as e:
            logger.error(f"❌ Failed to check performance metrics: {e}")
            return {}
    
    def send_telegram_alert(self, alert_data: Dict[str, Any]) -> bool:
        """Send alert via Telegram"""
        try:
            # Format alert message
            severity_emoji = {
                'high': '🚨',
                'medium': '⚠️',
                'low': '💡'
            }
            
            message = f"🌟 SEASONAL MODELS ALERT\n\n"
            message += f"📊 Performance Check: {alert_data['check_time'][:19]}\n"
            message += f"⏰ Period: {alert_data['period_checked']}\n\n"
            
            if alert_data['alerts']:
                message += f"🚨 ALERTS DETECTED ({len(alert_data['alerts'])}):\n\n"
                
                for alert in alert_data['alerts']:
                    emoji = severity_emoji.get(alert['severity'], '❓')
                    message += f"{emoji} {alert['type'].upper()}\n"
                    message += f"   {alert['message']}\n\n"
                
                message += f"📈 CURRENT METRICS:\n"
                message += f"   Success Rate: {alert_data['success_rate']:.1f}%\n"
                message += f"   Avg Response: {alert_data['avg_response_time_ms']:.1f}ms\n"
                message += f"   Avg Confidence: {alert_data['avg_confidence']:.3f}\n"
                message += f"   Total Tests: {alert_data['total_tests']}\n"
                
            else:
                message += f"✅ ALL SYSTEMS NORMAL\n\n"
                message += f"📈 METRICS:\n"
                message += f"   Success Rate: {alert_data['success_rate']:.1f}%\n"
                message += f"   Avg Response: {alert_data['avg_response_time_ms']:.1f}ms\n"
                message += f"   Avg Confidence: {alert_data['avg_confidence']:.3f}\n"
                message += f"   Total Tests: {alert_data['total_tests']}\n"
            
            # Send to Telegram
            url = f"https://api.telegram.org/bot{self.telegram_config['bot_token']}/sendMessage"
            payload = {
                'chat_id': self.telegram_config['chat_id'],
                'text': message,
                'parse_mode': 'HTML'
            }
            
            response = requests.post(url, json=payload, timeout=10)
            
            if response.status_code == 200:
                logger.info("✅ Telegram alert sent successfully")
                return True
            else:
                logger.error(f"❌ Failed to send Telegram alert: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error sending Telegram alert: {e}")
            return False
    
    def check_api_health(self) -> Dict[str, Any]:
        """Check if the seasonal API is responding"""
        try:
            response = requests.get("http://localhost:8100/api/v1/models/seasonal/status", timeout=5)
            
            if response.status_code == 200:
                status_data = response.json()
                return {
                    'api_healthy': True,
                    'response_time_ms': response.elapsed.total_seconds() * 1000,
                    'models_loaded': status_data.get('total_models', 0),
                    'status': status_data.get('status', 'unknown')
                }
            else:
                return {
                    'api_healthy': False,
                    'error': f"HTTP {response.status_code}",
                    'response_time_ms': response.elapsed.total_seconds() * 1000
                }
                
        except Exception as e:
            return {
                'api_healthy': False,
                'error': str(e),
                'response_time_ms': None
            }
    
    def run_alert_check(self, send_alerts: bool = True) -> Dict[str, Any]:
        """Run complete alert check"""
        logger.info("🔍 Running performance alert check")
        
        # Check API health
        api_health = self.check_api_health()
        
        if not api_health['api_healthy']:
            # Critical alert - API is down
            critical_alert = {
                'check_time': datetime.now().isoformat(),
                'period_checked': 'API Health Check',
                'alerts': [{
                    'type': 'api_down',
                    'severity': 'critical',
                    'message': f"Seasonal API is not responding: {api_health['error']}",
                    'current_value': 'DOWN',
                    'threshold': 'UP'
                }],
                'api_health': api_health
            }
            
            if send_alerts:
                self.send_telegram_alert(critical_alert)
            
            return critical_alert
        
        # Check performance metrics
        metrics = self.check_performance_metrics()
        
        if not metrics:
            return {'status': 'error', 'message': 'Failed to check metrics'}
        
        # Add API health info
        metrics['api_health'] = api_health
        
        # Send alerts if any issues found
        if send_alerts and (metrics.get('alerts') or metrics.get('status') == 'no_data'):
            self.send_telegram_alert(metrics)
        
        # Log results
        if metrics.get('alerts'):
            logger.warning(f"⚠️  {len(metrics['alerts'])} alerts detected")
            for alert in metrics['alerts']:
                logger.warning(f"   {alert['type']}: {alert['message']}")
        else:
            logger.info("✅ No performance issues detected")
        
        return metrics
    
    def start_continuous_alerting(self, check_interval_minutes: int = 15):
        """Start continuous alerting"""
        import time
        
        logger.info(f"🚀 Starting continuous performance alerting")
        logger.info(f"   Check interval: {check_interval_minutes} minutes")
        
        try:
            while True:
                self.run_alert_check()
                
                logger.info(f"⏰ Waiting {check_interval_minutes} minutes until next check...")
                time.sleep(check_interval_minutes * 60)
                
        except KeyboardInterrupt:
            logger.info("🛑 Alerting stopped by user")
        except Exception as e:
            logger.error(f"❌ Alerting error: {e}")


def main():
    """Main alerting function"""
    alerting = PerformanceAlerting()
    
    if len(sys.argv) > 1 and sys.argv[1] == "once":
        # Run once
        result = alerting.run_alert_check()
        print(json.dumps(result, indent=2, default=str))
    elif len(sys.argv) > 1 and sys.argv[1] == "test":
        # Test without sending alerts
        result = alerting.run_alert_check(send_alerts=False)
        print(json.dumps(result, indent=2, default=str))
    else:
        # Run continuously
        alerting.start_continuous_alerting()


if __name__ == "__main__":
    main()
