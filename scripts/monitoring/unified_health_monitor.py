#!/usr/bin/env python3
"""
Unified Health Monitor Service
=============================

Centralized health monitoring service that consolidates health checks from all services.
Provides unified system health endpoint and monitoring dashboard.

Port: 8130 (Unified Health Monitor)
Created: June 12, 2025
"""

import asyncio
import aiohttp
import asyncpg
import psutil
import time
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
import uvicorn

# Configuration
app = FastAPI(
    title="Unified Health Monitor",
    description="Centralized health monitoring for Solar Prediction System",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Application start time
start_time = time.time()

# Service definitions
SERVICES_CONFIG = {
    "Production Scripts API": {"port": 8100, "health_path": "/health"},
    "GPU Prediction Service": {"port": 8105, "health_path": "/health"},
    "Enhanced Billing System": {"port": 8110, "health_path": "/health"},
    "Unified Forecast API": {"port": 8120, "health_path": "/health"},
    "Charts API": {"port": 8103, "health_path": "/health"}
}

# Database tables to monitor (only active production tables)
DATABASE_TABLES = {
    "solax_data": {"threshold_hours": 2, "time_column": "timestamp"},
    "solax_data2": {"threshold_hours": 2, "time_column": "timestamp"},
    "weather_data": {"threshold_hours": 6, "time_column": "timestamp"},
    "prediction_cache": {"threshold_hours": 1, "time_column": "updated_at"}
}

# Model retrain configuration (disabled - production models are self-managing)
# Production models don't require retrain monitoring as they are:
# - Production Scripts API: Self-contained with 94.31% R² accuracy
# - GPU Prediction Service: Real-time optimized with GPU acceleration
MODELS_CONFIG = {}

async def check_service_health(service_name: str, config: Dict) -> Dict[str, Any]:
    """Check health of a specific service"""
    url = f"http://localhost:{config['port']}{config['health_path']}"
    start_time_check = datetime.now()
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                end_time = datetime.now()
                response_time = (end_time - start_time_check).total_seconds() * 1000
                
                if response.status == 200:
                    data = await response.json()
                    return {
                        "service_name": service_name,
                        "port": config['port'],
                        "status": "healthy",
                        "response_time_ms": response_time,
                        "version": data.get("version"),
                        "details": data,
                        "last_check": end_time.isoformat()
                    }
                else:
                    return {
                        "service_name": service_name,
                        "port": config['port'],
                        "status": "degraded",
                        "response_time_ms": response_time,
                        "error": f"HTTP {response.status}",
                        "last_check": end_time.isoformat()
                    }
                    
    except Exception as e:
        end_time = datetime.now()
        return {
            "service_name": service_name,
            "port": config['port'],
            "status": "error",
            "error": str(e),
            "last_check": end_time.isoformat()
        }

async def check_database_freshness() -> List[Dict[str, Any]]:
    """Check freshness of data in all important tables"""
    freshness_checks = []
    
    try:
        # Database connection
        db_url = f"postgresql://{os.getenv('DB_USER', 'postgres')}:{os.getenv('DB_PASSWORD', '')}@{os.getenv('DB_HOST', 'localhost')}:{os.getenv('DB_PORT', 5432)}/{os.getenv('DB_NAME', 'solar_prediction')}"
        
        conn = await asyncpg.connect(db_url)
        
        for table_name, config in DATABASE_TABLES.items():
            try:
                # Check if table exists
                table_exists = await conn.fetchval(
                    "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = $1)",
                    table_name
                )
                
                if not table_exists:
                    freshness_checks.append({
                        "table_name": table_name,
                        "records_count": 0,
                        "freshness_status": "critical",
                        "alert_message": f"Table {table_name} does not exist"
                    })
                    continue
                
                # Get latest record and count
                latest_record = await conn.fetchrow(
                    f"SELECT MAX({config['time_column']}) as latest_time, COUNT(*) as record_count FROM {table_name}"
                )
                
                latest_time = latest_record['latest_time']
                record_count = latest_record['record_count']
                
                if latest_time:
                    hours_since = (datetime.now() - latest_time).total_seconds() / 3600
                    
                    if hours_since <= config['threshold_hours']:
                        status = "fresh"
                        alert_message = None
                    elif hours_since <= config['threshold_hours'] * 2:
                        status = "stale"
                        alert_message = f"{table_name.upper()} STALE - {hours_since:.1f}h old"
                    else:
                        status = "critical"
                        alert_message = f"{table_name.upper()} CRITICAL - {hours_since:.1f}h old"
                else:
                    hours_since = None
                    status = "critical"
                    alert_message = f"{table_name.upper()} EMPTY - No records found"
                
                freshness_checks.append({
                    "table_name": table_name,
                    "last_record_time": latest_time.isoformat() if latest_time else None,
                    "records_count": record_count,
                    "freshness_status": status,
                    "hours_since_last_record": hours_since,
                    "alert_message": alert_message
                })
                
            except Exception as e:
                freshness_checks.append({
                    "table_name": table_name,
                    "records_count": 0,
                    "freshness_status": "critical",
                    "alert_message": f"Error checking {table_name}: {str(e)}"
                })
        
        await conn.close()
        
    except Exception as e:
        freshness_checks.append({
            "table_name": "database_connection",
            "records_count": 0,
            "freshness_status": "critical",
            "alert_message": f"Database connection failed: {str(e)}"
        })
    
    return freshness_checks

async def check_model_retrain_status() -> List[Dict[str, Any]]:
    """Check if model retraining is on schedule"""
    retrain_checks = []
    
    try:
        db_url = f"postgresql://{os.getenv('DB_USER', 'postgres')}:{os.getenv('DB_PASSWORD', '')}@{os.getenv('DB_HOST', 'localhost')}:{os.getenv('DB_PORT', 5432)}/{os.getenv('DB_NAME', 'solar_prediction')}"
        
        conn = await asyncpg.connect(db_url)
        
        for model_name, config in MODELS_CONFIG.items():
            try:
                # Check if training log table exists
                table_exists = await conn.fetchval(
                    "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = $1)",
                    config['table']
                )
                
                if table_exists:
                    # Get latest training time for this model
                    latest_training = await conn.fetchval(
                        f"SELECT MAX(training_date) FROM {config['table']} WHERE model_name ILIKE $1",
                        f"%{model_name}%"
                    )
                    
                    if latest_training:
                        hours_since = (datetime.now() - latest_training).total_seconds() / 3600
                        
                        if hours_since <= config['interval_hours']:
                            status = "on_schedule"
                            alert_message = None
                        elif hours_since <= config['interval_hours'] * 1.5:
                            status = "overdue"
                            alert_message = f"MODEL RETRAIN OVERDUE - {model_name} ({hours_since:.1f}h since last training)"
                        else:
                            status = "overdue"
                            alert_message = f"MODEL RETRAIN CRITICAL - {model_name} ({hours_since:.1f}h since last training)"
                    else:
                        hours_since = None
                        status = "unknown"
                        alert_message = f"MODEL RETRAIN UNKNOWN - No training records for {model_name}"
                else:
                    latest_training = None
                    hours_since = None
                    status = "unknown"
                    alert_message = f"MODEL RETRAIN UNKNOWN - Training log table not found"
                
                retrain_checks.append({
                    "model_name": model_name,
                    "last_retrain_time": latest_training.isoformat() if latest_training else None,
                    "retrain_status": status,
                    "hours_since_retrain": hours_since,
                    "scheduled_interval_hours": config['interval_hours'],
                    "alert_message": alert_message
                })
                
            except Exception as e:
                retrain_checks.append({
                    "model_name": model_name,
                    "retrain_status": "unknown",
                    "scheduled_interval_hours": config['interval_hours'],
                    "alert_message": f"Error checking {model_name}: {str(e)}"
                })
        
        await conn.close()
        
    except Exception as e:
        for model_name, config in MODELS_CONFIG.items():
            retrain_checks.append({
                "model_name": model_name,
                "retrain_status": "unknown",
                "scheduled_interval_hours": config['interval_hours'],
                "alert_message": f"Database connection failed: {str(e)}"
            })
    
    return retrain_checks

@app.get("/health")
async def health_check():
    """Basic health check for this service"""
    return {
        "status": "healthy",
        "service": "Unified Health Monitor",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat(),
        "uptime_seconds": time.time() - start_time
    }

@app.get("/api/v2/system/health")
async def unified_system_health():
    """Unified system health check that consolidates all health information"""
    try:
        # Check all services concurrently
        service_tasks = [
            check_service_health(service_name, config) 
            for service_name, config in SERVICES_CONFIG.items()
        ]
        services_health = await asyncio.gather(*service_tasks, return_exceptions=True)
        
        # Handle any exceptions in service checks
        services_status = []
        for i, result in enumerate(services_health):
            if isinstance(result, Exception):
                service_name = list(SERVICES_CONFIG.keys())[i]
                services_status.append({
                    "service_name": service_name,
                    "port": SERVICES_CONFIG[service_name]["port"],
                    "status": "error",
                    "error": str(result),
                    "last_check": datetime.now().isoformat()
                })
            else:
                services_status.append(result)
        
        # Check database freshness
        database_freshness = await check_database_freshness()
        
        # Check model retrain status
        model_retrain = await check_model_retrain_status()
        
        # Collect all alerts
        alerts = []
        for db_check in database_freshness:
            if db_check.get("alert_message"):
                alerts.append(db_check["alert_message"])
        
        for model_check in model_retrain:
            if model_check.get("alert_message"):
                alerts.append(model_check["alert_message"])
        
        # Calculate summary statistics
        service_statuses = [s["status"] for s in services_status]
        summary = {
            "healthy_services": service_statuses.count("healthy"),
            "degraded_services": service_statuses.count("degraded"),
            "error_services": service_statuses.count("error"),
            "total_services": len(services_status),
            "fresh_tables": len([d for d in database_freshness if d["freshness_status"] == "fresh"]),
            "stale_tables": len([d for d in database_freshness if d["freshness_status"] == "stale"]),
            "critical_tables": len([d for d in database_freshness if d["freshness_status"] == "critical"]),
            "models_on_schedule": len([m for m in model_retrain if m["retrain_status"] == "on_schedule"]),
            "models_overdue": len([m for m in model_retrain if m["retrain_status"] == "overdue"])
        }
        
        # Determine overall status
        if summary["error_services"] > 0 or summary["critical_tables"] > 0:
            overall_status = "critical"
        elif summary["degraded_services"] > 0 or summary["stale_tables"] > 0 or summary["models_overdue"] > 0:
            overall_status = "degraded"
        else:
            overall_status = "healthy"
        
        # Get system metrics
        memory = psutil.virtual_memory()
        cpu_percent = psutil.cpu_percent(interval=0.1)
        
        system_metrics = {
            "cpu_usage_percent": cpu_percent,
            "memory_usage_percent": memory.percent,
            "memory_used_gb": round(memory.used / (1024**3), 2),
            "memory_total_gb": round(memory.total / (1024**3), 2),
            "uptime_seconds": time.time() - start_time
        }
        
        return {
            "overall_status": overall_status,
            "timestamp": datetime.now().isoformat(),
            "services": services_status,
            "database_freshness": database_freshness,
            "model_retrain": model_retrain,
            "alerts": alerts,
            "system_metrics": system_metrics,
            "summary": summary
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Unified system health check failed: {e}")

if __name__ == "__main__":
    print("🔍 Starting Unified Health Monitor Service")
    print("Port: 8130")
    print("Endpoint: /api/v2/system/health")
    uvicorn.run(app, host="0.0.0.0", port=8130, log_level="info")
