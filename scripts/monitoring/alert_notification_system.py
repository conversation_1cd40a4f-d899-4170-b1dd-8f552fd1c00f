#!/usr/bin/env python3
"""
Alert Notification System
=========================

Advanced alert and notification system for model monitoring:
- Multi-channel notifications (email, Telegram, webhook)
- Alert severity levels and escalation
- Rate limiting and deduplication
- Alert history and analytics
- Custom alert rules and conditions

Target: Immediate notification of critical model issues
Created: June 6, 2025
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import numpy as np
import pandas as pd
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Any, Optional, Union
import json
import smtplib
import requests
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from dataclasses import dataclass
from enum import Enum
import threading
import time
import hashlib

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AlertSeverity(Enum):
    """Alert severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class AlertType(Enum):
    """Alert types"""
    PERFORMANCE_DEGRADATION = "performance_degradation"
    DATA_DRIFT = "data_drift"
    MODEL_ERROR = "model_error"
    SYSTEM_ERROR = "system_error"
    RETRAINING_REQUIRED = "retraining_required"
    CALIBRATION_DRIFT = "calibration_drift"

@dataclass
class Alert:
    """Alert data structure"""
    id: str
    type: AlertType
    severity: AlertSeverity
    model: str
    message: str
    details: Dict[str, Any]
    timestamp: datetime
    resolved: bool = False
    resolved_timestamp: Optional[datetime] = None
    acknowledged: bool = False
    acknowledged_by: Optional[str] = None
    acknowledged_timestamp: Optional[datetime] = None

class NotificationChannel:
    """Base class for notification channels"""
    
    def __init__(self, name: str, config: Dict[str, Any]):
        self.name = name
        self.config = config
        self.enabled = config.get('enabled', True)
        self.rate_limit_seconds = config.get('rate_limit_seconds', 300)  # 5 minutes
        self.last_sent = {}
    
    def can_send(self, alert_hash: str) -> bool:
        """Check if alert can be sent (rate limiting)"""
        if not self.enabled:
            return False
        
        last_time = self.last_sent.get(alert_hash, datetime.min)
        return (datetime.now() - last_time).total_seconds() > self.rate_limit_seconds
    
    def send(self, alert: Alert) -> bool:
        """Send alert notification"""
        raise NotImplementedError
    
    def mark_sent(self, alert_hash: str):
        """Mark alert as sent for rate limiting"""
        self.last_sent[alert_hash] = datetime.now()

class EmailNotificationChannel(NotificationChannel):
    """Email notification channel"""
    
    def send(self, alert: Alert) -> bool:
        """Send email notification"""
        try:
            # Create message
            msg = MIMEMultipart()
            msg['From'] = self.config['from_email']
            msg['To'] = ', '.join(self.config['to_emails'])
            msg['Subject'] = f"[{alert.severity.value.upper()}] Solar Prediction Alert: {alert.type.value}"
            
            # Create email body
            body = self._create_email_body(alert)
            msg.attach(MIMEText(body, 'html'))
            
            # Send email
            server = smtplib.SMTP(self.config['smtp_server'], self.config['smtp_port'])
            if self.config.get('use_tls', True):
                server.starttls()
            
            if 'username' in self.config and 'password' in self.config:
                server.login(self.config['username'], self.config['password'])
            
            server.send_message(msg)
            server.quit()
            
            logger.info(f"📧 Email alert sent: {alert.id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Email notification failed: {e}")
            return False
    
    def _create_email_body(self, alert: Alert) -> str:
        """Create HTML email body"""
        severity_colors = {
            AlertSeverity.LOW: '#28a745',
            AlertSeverity.MEDIUM: '#ffc107',
            AlertSeverity.HIGH: '#fd7e14',
            AlertSeverity.CRITICAL: '#dc3545'
        }
        
        color = severity_colors.get(alert.severity, '#6c757d')
        
        html = f"""
        <html>
        <body>
            <div style="font-family: Arial, sans-serif; max-width: 600px;">
                <div style="background-color: {color}; color: white; padding: 20px; border-radius: 5px 5px 0 0;">
                    <h2 style="margin: 0;">🚨 Solar Prediction Alert</h2>
                    <p style="margin: 5px 0 0 0; font-size: 18px;">{alert.severity.value.upper()} - {alert.type.value.replace('_', ' ').title()}</p>
                </div>
                
                <div style="background-color: #f8f9fa; padding: 20px; border: 1px solid #dee2e6; border-top: none; border-radius: 0 0 5px 5px;">
                    <h3 style="color: #495057; margin-top: 0;">Alert Details</h3>
                    
                    <table style="width: 100%; border-collapse: collapse;">
                        <tr>
                            <td style="padding: 8px; border-bottom: 1px solid #dee2e6; font-weight: bold;">Model:</td>
                            <td style="padding: 8px; border-bottom: 1px solid #dee2e6;">{alert.model}</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; border-bottom: 1px solid #dee2e6; font-weight: bold;">Timestamp:</td>
                            <td style="padding: 8px; border-bottom: 1px solid #dee2e6;">{alert.timestamp.strftime('%Y-%m-%d %H:%M:%S')}</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; border-bottom: 1px solid #dee2e6; font-weight: bold;">Message:</td>
                            <td style="padding: 8px; border-bottom: 1px solid #dee2e6;">{alert.message}</td>
                        </tr>
                    </table>
                    
                    <h4 style="color: #495057; margin-bottom: 10px;">Additional Information</h4>
                    <pre style="background-color: #e9ecef; padding: 10px; border-radius: 3px; font-size: 12px; overflow-x: auto;">
{json.dumps(alert.details, indent=2)}
                    </pre>
                    
                    <p style="margin-top: 20px; font-size: 12px; color: #6c757d;">
                        Alert ID: {alert.id}<br>
                        Generated by Solar Prediction Monitoring System
                    </p>
                </div>
            </div>
        </body>
        </html>
        """
        
        return html

class TelegramNotificationChannel(NotificationChannel):
    """Telegram notification channel"""
    
    def send(self, alert: Alert) -> bool:
        """Send Telegram notification"""
        try:
            bot_token = self.config['bot_token']
            chat_id = self.config['chat_id']
            
            # Create message
            message = self._create_telegram_message(alert)
            
            # Send message
            url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
            payload = {
                'chat_id': chat_id,
                'text': message,
                'parse_mode': 'Markdown'
            }
            
            response = requests.post(url, json=payload, timeout=10)
            response.raise_for_status()
            
            logger.info(f"📱 Telegram alert sent: {alert.id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Telegram notification failed: {e}")
            return False
    
    def _create_telegram_message(self, alert: Alert) -> str:
        """Create Telegram message"""
        severity_emojis = {
            AlertSeverity.LOW: '🟢',
            AlertSeverity.MEDIUM: '🟡',
            AlertSeverity.HIGH: '🟠',
            AlertSeverity.CRITICAL: '🔴'
        }
        
        emoji = severity_emojis.get(alert.severity, '⚪')
        
        message = f"""
{emoji} *Solar Prediction Alert*

*Severity:* {alert.severity.value.upper()}
*Type:* {alert.type.value.replace('_', ' ').title()}
*Model:* {alert.model}
*Time:* {alert.timestamp.strftime('%Y-%m-%d %H:%M:%S')}

*Message:* {alert.message}

*Alert ID:* `{alert.id}`
        """.strip()
        
        return message

class WebhookNotificationChannel(NotificationChannel):
    """Webhook notification channel"""
    
    def send(self, alert: Alert) -> bool:
        """Send webhook notification"""
        try:
            url = self.config['webhook_url']
            
            # Create payload
            payload = {
                'alert_id': alert.id,
                'type': alert.type.value,
                'severity': alert.severity.value,
                'model': alert.model,
                'message': alert.message,
                'details': alert.details,
                'timestamp': alert.timestamp.isoformat(),
                'resolved': alert.resolved,
                'acknowledged': alert.acknowledged
            }
            
            # Add custom headers if specified
            headers = self.config.get('headers', {})
            headers['Content-Type'] = 'application/json'
            
            # Send webhook
            response = requests.post(
                url, 
                json=payload, 
                headers=headers,
                timeout=self.config.get('timeout', 10)
            )
            response.raise_for_status()
            
            logger.info(f"🔗 Webhook alert sent: {alert.id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Webhook notification failed: {e}")
            return False

class AlertNotificationSystem:
    """
    Comprehensive alert notification system
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize alert notification system
        
        Args:
            config: Configuration for notification channels
        """
        self.config = config
        self.channels = {}
        self.alerts = []
        self.alert_rules = []
        
        # Initialize notification channels
        self._initialize_channels()
        
        # Alert processing
        self.processing_active = False
        self.processing_thread = None
        self.alert_queue = []
        self.alert_lock = threading.Lock()
        
        logger.info("🚨 Alert notification system initialized")
    
    def _initialize_channels(self):
        """Initialize notification channels"""
        channel_configs = self.config.get('channels', {})
        
        for channel_name, channel_config in channel_configs.items():
            channel_type = channel_config.get('type')
            
            if channel_type == 'email':
                self.channels[channel_name] = EmailNotificationChannel(channel_name, channel_config)
            elif channel_type == 'telegram':
                self.channels[channel_name] = TelegramNotificationChannel(channel_name, channel_config)
            elif channel_type == 'webhook':
                self.channels[channel_name] = WebhookNotificationChannel(channel_name, channel_config)
            else:
                logger.warning(f"⚠️ Unknown channel type: {channel_type}")
        
        logger.info(f"📡 Initialized {len(self.channels)} notification channels")
    
    def create_alert(self, alert_type: AlertType, severity: AlertSeverity, 
                    model: str, message: str, details: Dict[str, Any] = None) -> Alert:
        """Create a new alert"""
        alert_id = self._generate_alert_id(alert_type, model, message)
        
        alert = Alert(
            id=alert_id,
            type=alert_type,
            severity=severity,
            model=model,
            message=message,
            details=details or {},
            timestamp=datetime.now()
        )
        
        return alert
    
    def _generate_alert_id(self, alert_type: AlertType, model: str, message: str) -> str:
        """Generate unique alert ID"""
        content = f"{alert_type.value}_{model}_{message}_{datetime.now().strftime('%Y%m%d_%H')}"
        return hashlib.md5(content.encode()).hexdigest()[:12]
    
    def send_alert(self, alert: Alert):
        """Send alert through appropriate channels"""
        with self.alert_lock:
            self.alert_queue.append(alert)
            self.alerts.append(alert)
        
        # Process immediately for critical alerts
        if alert.severity == AlertSeverity.CRITICAL:
            self._process_alert(alert)
    
    def _process_alert(self, alert: Alert):
        """Process individual alert"""
        # Check if this alert should be sent based on rules
        if not self._should_send_alert(alert):
            return
        
        # Generate alert hash for deduplication
        alert_hash = self._get_alert_hash(alert)
        
        # Send through appropriate channels based on severity
        channels_to_use = self._get_channels_for_severity(alert.severity)
        
        sent_count = 0
        for channel_name in channels_to_use:
            if channel_name in self.channels:
                channel = self.channels[channel_name]
                
                if channel.can_send(alert_hash):
                    if channel.send(alert):
                        channel.mark_sent(alert_hash)
                        sent_count += 1
        
        logger.info(f"📤 Alert {alert.id} sent through {sent_count} channels")
    
    def _should_send_alert(self, alert: Alert) -> bool:
        """Check if alert should be sent based on rules"""
        # Check for duplicate recent alerts
        recent_cutoff = datetime.now() - timedelta(minutes=30)
        recent_similar = [
            a for a in self.alerts 
            if (a.type == alert.type and 
                a.model == alert.model and 
                a.timestamp > recent_cutoff and
                a.id != alert.id)
        ]
        
        # Don't send if we have too many similar recent alerts
        if len(recent_similar) > 3:
            logger.info(f"🔇 Suppressing alert {alert.id} due to recent similar alerts")
            return False
        
        return True
    
    def _get_alert_hash(self, alert: Alert) -> str:
        """Get hash for alert deduplication"""
        content = f"{alert.type.value}_{alert.model}_{alert.severity.value}"
        return hashlib.md5(content.encode()).hexdigest()[:8]
    
    def _get_channels_for_severity(self, severity: AlertSeverity) -> List[str]:
        """Get notification channels for alert severity"""
        severity_channels = self.config.get('severity_channels', {})
        
        return severity_channels.get(severity.value, list(self.channels.keys()))
    
    def start_processing(self):
        """Start alert processing thread"""
        if self.processing_active:
            return
        
        self.processing_active = True
        self.processing_thread = threading.Thread(
            target=self._processing_loop,
            daemon=True
        )
        self.processing_thread.start()
        
        logger.info("🚀 Alert processing started")
    
    def stop_processing(self):
        """Stop alert processing"""
        self.processing_active = False
        if self.processing_thread:
            self.processing_thread.join(timeout=5)
        
        logger.info("⏹️ Alert processing stopped")
    
    def _processing_loop(self):
        """Main alert processing loop"""
        while self.processing_active:
            try:
                with self.alert_lock:
                    alerts_to_process = self.alert_queue.copy()
                    self.alert_queue.clear()
                
                for alert in alerts_to_process:
                    self._process_alert(alert)
                
                time.sleep(5)  # Check every 5 seconds
                
            except Exception as e:
                logger.error(f"❌ Alert processing error: {e}")
                time.sleep(10)
    
    def get_alert_statistics(self) -> Dict[str, Any]:
        """Get alert statistics"""
        if not self.alerts:
            return {'total_alerts': 0}
        
        total = len(self.alerts)
        by_severity = {}
        by_type = {}
        
        for alert in self.alerts:
            # Count by severity
            severity = alert.severity.value
            by_severity[severity] = by_severity.get(severity, 0) + 1
            
            # Count by type
            alert_type = alert.type.value
            by_type[alert_type] = by_type.get(alert_type, 0) + 1
        
        # Recent alerts (last 24 hours)
        recent_cutoff = datetime.now() - timedelta(hours=24)
        recent_alerts = [a for a in self.alerts if a.timestamp > recent_cutoff]
        
        return {
            'total_alerts': total,
            'recent_alerts_24h': len(recent_alerts),
            'by_severity': by_severity,
            'by_type': by_type,
            'resolved_alerts': len([a for a in self.alerts if a.resolved]),
            'acknowledged_alerts': len([a for a in self.alerts if a.acknowledged])
        }

def main():
    """Test alert notification system"""
    logger.info("🚨 Testing Alert Notification System")
    logger.info("=" * 60)
    
    # Configuration
    config = {
        'channels': {
            'telegram': {
                'type': 'telegram',
                'enabled': True,
                'bot_token': '8060881816:AAFBhGADTAAcUkbom_FEFSkOHoT5N6t5png',
                'chat_id': '1510889515',
                'rate_limit_seconds': 60
            },
            'webhook': {
                'type': 'webhook',
                'enabled': False,  # Disabled for testing
                'webhook_url': 'https://httpbin.org/post',
                'timeout': 10
            }
        },
        'severity_channels': {
            'low': ['webhook'],
            'medium': ['telegram'],
            'high': ['telegram'],
            'critical': ['telegram', 'webhook']
        }
    }
    
    # Initialize system
    alert_system = AlertNotificationSystem(config)
    alert_system.start_processing()
    
    # Test alerts
    test_alerts = [
        (AlertType.PERFORMANCE_DEGRADATION, AlertSeverity.MEDIUM, 'xgboost_ensemble', 
         'Model performance degraded by 12%', {'degradation': {'rmse': 12.5, 'r2': -8.2}}),
        
        (AlertType.DATA_DRIFT, AlertSeverity.HIGH, 'lightgbm_ensemble',
         'Significant data drift detected in weather features', {'drift_features': ['temperature', 'humidity']}),
        
        (AlertType.RETRAINING_REQUIRED, AlertSeverity.CRITICAL, 'random_forest_ensemble',
         'Model requires immediate retraining due to severe performance degradation', 
         {'degradation': {'rmse': 28.3, 'r2': -25.1}})
    ]
    
    logger.info("📤 Sending test alerts...")
    
    for alert_type, severity, model, message, details in test_alerts:
        alert = alert_system.create_alert(alert_type, severity, model, message, details)
        alert_system.send_alert(alert)
        
        logger.info(f"   Created alert: {alert.id} ({severity.value})")
        time.sleep(2)  # Small delay between alerts
    
    # Wait for processing
    time.sleep(10)
    
    # Get statistics
    stats = alert_system.get_alert_statistics()
    
    logger.info("\n📊 Alert Statistics:")
    logger.info(f"   Total alerts: {stats['total_alerts']}")
    logger.info(f"   Recent alerts (24h): {stats['recent_alerts_24h']}")
    logger.info(f"   By severity: {stats['by_severity']}")
    logger.info(f"   By type: {stats['by_type']}")
    
    # Stop processing
    alert_system.stop_processing()
    
    logger.info("\n✅ Alert Notification System test completed!")
    
    return alert_system

if __name__ == "__main__":
    alert_system = main()
