#!/usr/bin/env python3
"""
Data Collection Status Monitor
Comprehensive monitoring of all data sources and collection status
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import psycopg2
import pandas as pd
from datetime import datetime, timedelta
import json
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class DataCollectionMonitor:
    """Monitor all data collection sources and status"""
    
    def __init__(self):
        self.db_config = {
            'host': 'localhost',
            'database': 'solar_prediction',
            'user': 'postgres',
            'password': 'postgres'
        }
        
        # Define all data sources
        self.data_sources = {
            'nasa_power_data': {
                'name': 'NASA POWER',
                'type': 'Weather/Solar',
                'update_frequency': 'Daily',
                'parameters': ['GHI', 'Temperature', 'Wind Speed', 'Humidity', 'Pressure', 'Clear Sky GHI', 'Clearness Index']
            },
            'solax_data': {
                'name': 'SolaX System 1',
                'type': 'Solar System',
                'update_frequency': 'Real-time (30s)',
                'parameters': ['AC Power', 'Yield Today', 'Battery SOC', 'Battery Power', 'Grid Power', 'Load Power']
            },
            'weather_data': {
                'name': 'Open-Meteo Weather',
                'type': 'Weather',
                'update_frequency': 'Hourly',
                'parameters': ['Solar Radiation', 'Temperature', 'Humidity', 'Wind Speed', 'Cloud Cover', 'Precipitation']
            },
            'cams_radiation_data': {
                'name': 'CAMS Solar Radiation',
                'type': 'Solar/Atmospheric',
                'update_frequency': 'Daily',
                'parameters': ['GHI', 'DNI', 'DHI', 'Cloud Cover', 'Temperature']
            },
            'era5_data': {
                'name': 'ERA5 Reanalysis',
                'type': 'Reanalysis',
                'update_frequency': 'Daily',
                'parameters': ['Temperature', 'Solar Radiation', 'Cloud Cover', 'Wind Speed', 'Pressure', 'Humidity']
            },
            'cams_aerosol_data': {
                'name': 'CAMS Aerosol/Cloud',
                'type': 'Atmospheric',
                'update_frequency': 'Daily',
                'parameters': ['Cloud Cover', 'Total AOD', 'Dust AOD', 'Sea Salt AOD', 'Organic Matter AOD']
            },
            'satellite_data': {
                'name': 'Satellite Data',
                'type': 'Satellite',
                'update_frequency': 'Variable',
                'parameters': ['Cloud Mask', 'Solar Irradiance', 'Product Metadata']
            }
        }
    
    def get_table_status(self, table_name: str) -> dict:
        """Get status for a specific table"""
        
        try:
            conn = psycopg2.connect(**self.db_config)
            
            # Check if table exists
            with conn.cursor() as cur:
                cur.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_name = %s
                    );
                """, (table_name,))
                
                table_exists = cur.fetchone()[0]
                
                if not table_exists:
                    return {
                        'exists': False,
                        'records': 0,
                        'earliest': None,
                        'latest': None,
                        'last_24h': 0,
                        'status': 'Table not found'
                    }
                
                # Get basic statistics
                cur.execute(f"""
                    SELECT 
                        COUNT(*) as total_records,
                        MIN(timestamp) as earliest,
                        MAX(timestamp) as latest
                    FROM {table_name}
                """)
                
                result = cur.fetchone()
                total_records = result[0] if result[0] else 0
                earliest = result[1]
                latest = result[2]
                
                # Get records in last 24 hours
                cur.execute(f"""
                    SELECT COUNT(*) 
                    FROM {table_name}
                    WHERE timestamp >= NOW() - INTERVAL '24 hours'
                """)
                
                last_24h = cur.fetchone()[0] if cur.fetchone() else 0
                
                # Determine status
                if total_records == 0:
                    status = 'No data'
                elif latest and latest > datetime.now() - timedelta(days=2):
                    status = 'Active'
                elif latest and latest > datetime.now() - timedelta(days=7):
                    status = 'Recent'
                else:
                    status = 'Stale'
                
                conn.close()
                
                return {
                    'exists': True,
                    'records': total_records,
                    'earliest': earliest,
                    'latest': latest,
                    'last_24h': last_24h,
                    'status': status
                }
                
        except Exception as e:
            logger.error(f"Error checking table {table_name}: {e}")
            return {
                'exists': False,
                'records': 0,
                'earliest': None,
                'latest': None,
                'last_24h': 0,
                'status': f'Error: {e}'
            }
    
    def generate_comprehensive_report(self) -> dict:
        """Generate comprehensive data collection report"""
        
        logger.info("📊 Generating comprehensive data collection report...")
        
        report = {
            'report_date': datetime.now().isoformat(),
            'data_sources': {},
            'summary': {
                'total_sources': len(self.data_sources),
                'active_sources': 0,
                'total_records': 0,
                'sources_with_recent_data': 0
            }
        }
        
        # Check each data source
        for table_name, source_info in self.data_sources.items():
            logger.info(f"Checking {source_info['name']}...")
            
            status = self.get_table_status(table_name)
            
            report['data_sources'][table_name] = {
                'name': source_info['name'],
                'type': source_info['type'],
                'update_frequency': source_info['update_frequency'],
                'parameters': source_info['parameters'],
                'status': status
            }
            
            # Update summary
            if status['status'] == 'Active':
                report['summary']['active_sources'] += 1
            
            if status['latest'] and status['latest'] > datetime.now() - timedelta(days=7):
                report['summary']['sources_with_recent_data'] += 1
            
            report['summary']['total_records'] += status['records']
        
        return report
    
    def print_status_report(self, report: dict):
        """Print formatted status report"""
        
        print("📊 COMPREHENSIVE DATA COLLECTION STATUS REPORT")
        print("=" * 60)
        print(f"📅 Report Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🔢 Total Data Sources: {report['summary']['total_sources']}")
        print(f"✅ Active Sources: {report['summary']['active_sources']}")
        print(f"📈 Sources with Recent Data: {report['summary']['sources_with_recent_data']}")
        print(f"📊 Total Records: {report['summary']['total_records']:,}")
        
        print("\n" + "=" * 60)
        print("📋 DETAILED SOURCE STATUS")
        print("=" * 60)
        
        # Group by status
        active_sources = []
        recent_sources = []
        stale_sources = []
        no_data_sources = []
        error_sources = []
        
        for table_name, source_data in report['data_sources'].items():
            status = source_data['status']['status']
            
            if status == 'Active':
                active_sources.append((table_name, source_data))
            elif status == 'Recent':
                recent_sources.append((table_name, source_data))
            elif status == 'Stale':
                stale_sources.append((table_name, source_data))
            elif status == 'No data':
                no_data_sources.append((table_name, source_data))
            else:
                error_sources.append((table_name, source_data))
        
        # Print active sources
        if active_sources:
            print("\n✅ ACTIVE SOURCES (Updated within 2 days)")
            print("-" * 50)
            for table_name, source_data in active_sources:
                status = source_data['status']
                print(f"🟢 {source_data['name']}")
                print(f"   Type: {source_data['type']}")
                print(f"   Records: {status['records']:,}")
                print(f"   Latest: {status['latest']}")
                print(f"   Last 24h: {status['last_24h']:,}")
                print(f"   Parameters: {len(source_data['parameters'])}")
                print()
        
        # Print recent sources
        if recent_sources:
            print("\n⚠️ RECENT SOURCES (Updated within 7 days)")
            print("-" * 50)
            for table_name, source_data in recent_sources:
                status = source_data['status']
                print(f"🟡 {source_data['name']}")
                print(f"   Records: {status['records']:,}")
                print(f"   Latest: {status['latest']}")
                print()
        
        # Print stale sources
        if stale_sources:
            print("\n⚠️ STALE SOURCES (Not updated recently)")
            print("-" * 50)
            for table_name, source_data in stale_sources:
                status = source_data['status']
                print(f"🟠 {source_data['name']}")
                print(f"   Records: {status['records']:,}")
                print(f"   Latest: {status['latest']}")
                print()
        
        # Print no data sources
        if no_data_sources:
            print("\n❌ NO DATA SOURCES")
            print("-" * 50)
            for table_name, source_data in no_data_sources:
                print(f"⚪ {source_data['name']}")
                print(f"   Status: {source_data['status']['status']}")
                print(f"   Expected: {source_data['type']} data")
                print()
        
        # Print error sources
        if error_sources:
            print("\n🚨 ERROR SOURCES")
            print("-" * 50)
            for table_name, source_data in error_sources:
                print(f"🔴 {source_data['name']}")
                print(f"   Error: {source_data['status']['status']}")
                print()
        
        # Summary and recommendations
        print("\n" + "=" * 60)
        print("🎯 RECOMMENDATIONS")
        print("=" * 60)
        
        if len(active_sources) >= 3:
            print("✅ Good data coverage - multiple active sources")
        else:
            print("⚠️ Limited active sources - consider activating more collectors")
        
        if no_data_sources:
            print(f"🔧 {len(no_data_sources)} sources need data collection setup")
        
        if error_sources:
            print(f"🚨 {len(error_sources)} sources have errors - need troubleshooting")
        
        print(f"\n📈 Overall Health: {(len(active_sources) + len(recent_sources)) / len(self.data_sources) * 100:.1f}%")
    
    def save_report(self, report: dict) -> str:
        """Save report to file"""
        
        report_path = f"/home/<USER>/solar-prediction-project/data_collection_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        return report_path


def main():
    """Main monitoring function"""
    
    monitor = DataCollectionMonitor()
    
    # Generate comprehensive report
    report = monitor.generate_comprehensive_report()
    
    # Print status report
    monitor.print_status_report(report)
    
    # Save report
    report_path = monitor.save_report(report)
    print(f"\n📄 Report saved: {report_path}")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
