#!/usr/bin/env python3
"""
Database Freshness Dashboard
===========================

Web-based dashboard for monitoring database freshness in real-time.
Provides visual status indicators, historical trends, and alert management.

Features:
- Real-time status display
- Historical freshness trends
- Alert history and management
- Configuration management
- Export capabilities

Created: June 12, 2025
"""

import asyncio
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path
from fastapi import FastAPI, Request
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
import uvicorn

from database_freshness_monitor import DatabaseFreshnessMonitor
from database_alert_system import DatabaseAlertSystem

app = FastAPI(
    title="Database Freshness Dashboard",
    description="Real-time monitoring dashboard for database freshness",
    version="1.0.0"
)

# Initialize monitoring components
monitor = DatabaseFreshnessMonitor()
alert_system = DatabaseAlertSystem()

@app.get("/", response_class=HTMLResponse)
async def dashboard_home():
    """Main dashboard page"""
    html_content = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Freshness Dashboard</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #eee;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            color: #7f8c8d;
            margin: 10px 0 0 0;
            font-size: 1.1em;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .status-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #3498db;
        }
        .status-card.fresh {
            border-left-color: #27ae60;
        }
        .status-card.stale {
            border-left-color: #f39c12;
        }
        .status-card.critical {
            border-left-color: #e74c3c;
        }
        .status-card.error {
            border-left-color: #9b59b6;
        }
        .status-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }
        .status-title {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 5px;
            text-transform: uppercase;
        }
        .status-description {
            color: #7f8c8d;
            margin-bottom: 15px;
        }
        .status-details {
            font-size: 0.9em;
        }
        .status-details div {
            margin: 5px 0;
        }
        .refresh-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1em;
            margin: 10px;
            transition: background 0.3s;
        }
        .refresh-btn:hover {
            background: #2980b9;
        }
        .summary-bar {
            background: #34495e;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        .summary-item {
            display: inline-block;
            margin: 0 20px;
            font-weight: bold;
        }
        .alerts-section {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }
        .alert-item {
            background: #fff;
            border-left: 4px solid #e74c3c;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }
        .timestamp {
            text-align: center;
            color: #7f8c8d;
            font-size: 0.9em;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Database Freshness Dashboard</h1>
            <p>Real-time monitoring of database table freshness</p>
            <button class="refresh-btn" onclick="refreshData()">🔄 Refresh Data</button>
            <button class="refresh-btn" onclick="toggleAutoRefresh()">⏰ Auto Refresh: <span id="auto-status">OFF</span></button>
        </div>
        
        <div id="summary-bar" class="summary-bar">
            <div class="loading">Loading database status...</div>
        </div>
        
        <div id="status-grid" class="status-grid">
            <div class="loading">Loading table status...</div>
        </div>
        
        <div id="alerts-section" class="alerts-section" style="display: none;">
            <h3>🚨 Active Alerts</h3>
            <div id="alerts-list"></div>
        </div>
        
        <div id="timestamp" class="timestamp"></div>
    </div>

    <script>
        let autoRefresh = false;
        let refreshInterval;

        async function fetchDatabaseStatus() {
            try {
                const response = await fetch('/api/database/status');
                const data = await response.json();
                updateDashboard(data);
            } catch (error) {
                console.error('Error fetching data:', error);
                document.getElementById('status-grid').innerHTML = 
                    '<div class="loading">❌ Error loading data. Please try again.</div>';
            }
        }

        function updateDashboard(data) {
            // Update summary bar
            const summaryBar = document.getElementById('summary-bar');
            summaryBar.innerHTML = `
                <div class="summary-item">📊 Overall: <strong>${data.overall_status.toUpperCase()}</strong></div>
                <div class="summary-item">✅ Fresh: <strong>${data.fresh_tables}</strong></div>
                <div class="summary-item">⚠️ Stale: <strong>${data.stale_tables}</strong></div>
                <div class="summary-item">❌ Critical: <strong>${data.critical_tables}</strong></div>
                <div class="summary-item">💥 Error: <strong>${data.error_tables || 0}</strong></div>
            `;

            // Update status grid
            const statusGrid = document.getElementById('status-grid');
            let gridHTML = '';

            for (const [tableName, details] of Object.entries(data.tables)) {
                const statusIcons = {
                    'fresh': '✅',
                    'stale': '⚠️',
                    'critical': '❌',
                    'error': '💥'
                };

                const icon = statusIcons[details.status] || '❓';
                
                let detailsHTML = '';
                if (details.error) {
                    detailsHTML = `<div><strong>Error:</strong> ${details.error}</div>`;
                } else {
                    detailsHTML = `
                        <div><strong>Records:</strong> ${(details.records_count || 0).toLocaleString()}</div>
                        <div><strong>Last Record:</strong> ${details.hours_since_last_record ? details.hours_since_last_record.toFixed(1) + 'h ago' : 'Unknown'}</div>
                        <div><strong>Recent Activity:</strong> ${details.recent_records_24h || 0} records (24h)</div>
                        <div><strong>Business Impact:</strong> ${details.business_impact}</div>
                    `;
                }

                gridHTML += `
                    <div class="status-card ${details.status}">
                        <div class="status-icon">${icon}</div>
                        <div class="status-title">${tableName}</div>
                        <div class="status-description">${details.description}</div>
                        <div class="status-details">
                            ${detailsHTML}
                        </div>
                    </div>
                `;
            }

            statusGrid.innerHTML = gridHTML;

            // Update alerts
            const alertsSection = document.getElementById('alerts-section');
            const alertsList = document.getElementById('alerts-list');
            
            if (data.alerts && data.alerts.length > 0) {
                alertsSection.style.display = 'block';
                alertsList.innerHTML = data.alerts.map(alert => 
                    `<div class="alert-item">${alert}</div>`
                ).join('');
            } else {
                alertsSection.style.display = 'none';
            }

            // Update timestamp
            document.getElementById('timestamp').innerHTML = 
                `Last updated: ${new Date(data.timestamp).toLocaleString()}`;
        }

        function refreshData() {
            fetchDatabaseStatus();
        }

        function toggleAutoRefresh() {
            autoRefresh = !autoRefresh;
            const statusElement = document.getElementById('auto-status');
            
            if (autoRefresh) {
                statusElement.textContent = 'ON';
                refreshInterval = setInterval(fetchDatabaseStatus, 30000); // 30 seconds
            } else {
                statusElement.textContent = 'OFF';
                if (refreshInterval) {
                    clearInterval(refreshInterval);
                }
            }
        }

        // Initial load
        fetchDatabaseStatus();
    </script>
</body>
</html>
    """
    return HTMLResponse(content=html_content)

@app.get("/api/database/status")
async def get_database_status():
    """API endpoint for database status"""
    try:
        summary = await monitor.check_all_tables()
        return JSONResponse(content=summary)
    except Exception as e:
        return JSONResponse(
            content={"error": f"Failed to get database status: {str(e)}"},
            status_code=500
        )

@app.get("/api/database/alerts")
async def get_recent_alerts():
    """API endpoint for recent alerts"""
    try:
        alert_log_path = Path("logs/monitoring/database_alerts.log")
        
        if not alert_log_path.exists():
            return JSONResponse(content={"alerts": []})
        
        # Read last 50 alerts
        alerts = []
        with open(alert_log_path, "r") as f:
            lines = f.readlines()
            for line in lines[-50:]:
                try:
                    alert_data = json.loads(line.strip())
                    alerts.append(alert_data)
                except:
                    continue
        
        return JSONResponse(content={"alerts": alerts})
        
    except Exception as e:
        return JSONResponse(
            content={"error": f"Failed to get alerts: {str(e)}"},
            status_code=500
        )

@app.get("/api/database/config")
async def get_database_config():
    """API endpoint for database configuration"""
    try:
        config_path = Path("database_config.json")
        
        if config_path.exists():
            with open(config_path, "r") as f:
                config = json.load(f)
            return JSONResponse(content=config)
        else:
            return JSONResponse(content={"error": "Configuration file not found"})
            
    except Exception as e:
        return JSONResponse(
            content={"error": f"Failed to get config: {str(e)}"},
            status_code=500
        )

@app.post("/api/database/test-alert")
async def send_test_alert():
    """API endpoint to send test alert"""
    try:
        result = await alert_system.send_test_alert()
        return JSONResponse(content={"success": result})
    except Exception as e:
        return JSONResponse(
            content={"error": f"Failed to send test alert: {str(e)}"},
            status_code=500
        )

if __name__ == "__main__":
    print("🌐 Starting Database Freshness Dashboard")
    print("Dashboard URL: http://localhost:8131")
    uvicorn.run(app, host="0.0.0.0", port=8131, log_level="info")
