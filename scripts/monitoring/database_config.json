{"database_freshness_config": {"global_settings": {"default_check_interval_minutes": 5, "default_alert_suppression_minutes": 30, "timezone": "Europe/Athens"}, "tables": {"solax_data": {"enabled": true, "threshold_hours": 2, "critical_threshold_hours": 4, "time_column": "timestamp", "description": "System 1 SolaX real-time data", "expected_frequency_minutes": 0.5, "business_impact": "high", "alert_channels": ["console", "telegram", "log"], "custom_thresholds": {"business_hours": {"start_hour": 6, "end_hour": 22, "threshold_hours": 1}, "night_hours": {"start_hour": 22, "end_hour": 6, "threshold_hours": 4}}}, "solax_data2": {"enabled": true, "threshold_hours": 2, "critical_threshold_hours": 4, "time_column": "timestamp", "description": "System 2 SolaX real-time data", "expected_frequency_minutes": 0.5, "business_impact": "high", "alert_channels": ["console", "telegram", "log"], "custom_thresholds": {"business_hours": {"start_hour": 6, "end_hour": 22, "threshold_hours": 1}, "night_hours": {"start_hour": 22, "end_hour": 6, "threshold_hours": 4}}}, "weather_data": {"enabled": true, "threshold_hours": 6, "critical_threshold_hours": 12, "time_column": "timestamp", "description": "Weather and radiation data", "expected_frequency_minutes": 60, "business_impact": "medium", "alert_channels": ["console", "log"], "custom_thresholds": {"daytime": {"start_hour": 6, "end_hour": 20, "threshold_hours": 3}, "nighttime": {"start_hour": 20, "end_hour": 6, "threshold_hours": 8}}}, "predictions": {"enabled": true, "threshold_hours": 24, "critical_threshold_hours": 48, "time_column": "timestamp", "description": "ML prediction results", "expected_frequency_minutes": 60, "business_impact": "high", "alert_channels": ["console", "telegram", "log"], "escalation_rules": {"24h": "warning", "48h": "critical", "72h": "urgent"}}, "prediction_cache": {"enabled": true, "threshold_hours": 1, "critical_threshold_hours": 2, "time_column": "created_at", "description": "Cached prediction results", "expected_frequency_minutes": 60, "business_impact": "medium", "alert_channels": ["console", "log"], "auto_refresh": {"enabled": true, "trigger_threshold_hours": 1.5, "refresh_command": "python scripts/production/initialize_prediction_cache.py"}}, "model_training_log": {"enabled": true, "threshold_hours": 168, "critical_threshold_hours": 336, "time_column": "training_date", "description": "Model training history", "expected_frequency_minutes": 10080, "business_impact": "low", "alert_channels": ["console", "log"], "create_if_missing": true, "missing_table_sql": "CREATE TABLE IF NOT EXISTS model_training_log (id SERIAL PRIMARY KEY, model_name VARCHAR(255), training_date TIMESTAMP, accuracy FLOAT, notes TEXT);"}}}, "alert_configuration": {"telegram": {"enabled": true, "bot_token": "**********************************************", "chat_id": "1510889515", "rate_limit_seconds": 5, "max_message_length": 4000}, "email": {"enabled": false, "smtp_server": "smtp.gmail.com", "smtp_port": 587, "username": "", "password": "", "recipients": []}, "webhook": {"enabled": false, "url": "", "headers": {}, "timeout_seconds": 10}}, "monitoring_schedule": {"continuous_monitoring": {"enabled": false, "check_interval_minutes": 5}, "scheduled_checks": [{"name": "morning_check", "time": "08:00", "enabled": true, "full_report": true}, {"name": "afternoon_check", "time": "14:00", "enabled": true, "full_report": false}, {"name": "evening_check", "time": "20:00", "enabled": true, "full_report": true}]}, "reporting": {"daily_summary": {"enabled": true, "time": "23:00", "include_charts": false, "recipients": ["telegram"]}, "weekly_report": {"enabled": true, "day": "sunday", "time": "09:00", "include_trends": true, "recipients": ["telegram"]}, "export_formats": ["json", "csv", "html"], "retention_days": 30}}