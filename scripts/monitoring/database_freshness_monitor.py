#!/usr/bin/env python3
"""
Database Freshness Monitor
=========================

Enhanced database freshness monitoring with configurable thresholds,
historical tracking, and automated alerts for stale data.

Features:
- Real-time freshness monitoring for all critical tables
- Configurable thresholds per table
- Historical freshness tracking
- Automated alerts for stale data
- Database health dashboard
- Export capabilities for analysis

Created: June 12, 2025
"""

import asyncio
import asyncpg
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path

class DatabaseFreshnessMonitor:
    def __init__(self):
        self.db_url = f"postgresql://{os.getenv('DB_USER', 'postgres')}:{os.getenv('DB_PASSWORD', '')}@{os.getenv('DB_HOST', 'localhost')}:{os.getenv('DB_PORT', 5432)}/{os.getenv('DB_NAME', 'solar_prediction')}"
        
        # Enhanced table configuration with detailed thresholds
        self.table_config = {
            "solax_data": {
                "threshold_hours": 2,
                "time_column": "timestamp",
                "description": "System 1 SolaX real-time data",
                "critical_threshold_hours": 4,
                "expected_frequency_minutes": 0.5,  # Every 30 seconds
                "business_impact": "high"
            },
            "solax_data2": {
                "threshold_hours": 2,
                "time_column": "timestamp", 
                "description": "System 2 SolaX real-time data",
                "critical_threshold_hours": 4,
                "expected_frequency_minutes": 0.5,
                "business_impact": "high"
            },
            "weather_data": {
                "threshold_hours": 6,
                "time_column": "timestamp",
                "description": "Weather and radiation data",
                "critical_threshold_hours": 12,
                "expected_frequency_minutes": 60,  # Every hour
                "business_impact": "medium"
            },

            "prediction_cache": {
                "threshold_hours": 1,
                "time_column": "updated_at",
                "description": "Cached prediction results",
                "critical_threshold_hours": 2,
                "expected_frequency_minutes": 60,
                "business_impact": "medium"
            },
            "model_training_log": {
                "threshold_hours": 168,  # 1 week
                "time_column": "training_date",
                "description": "Model training history",
                "critical_threshold_hours": 336,  # 2 weeks
                "expected_frequency_minutes": 10080,  # Weekly
                "business_impact": "low"
            }
        }
        
        self.monitoring_log_path = Path("logs/monitoring/database_freshness.log")
        self.monitoring_log_path.parent.mkdir(parents=True, exist_ok=True)

    async def check_table_freshness(self, table_name: str, config: Dict) -> Dict[str, Any]:
        """Check freshness of a specific table with enhanced details"""
        try:
            conn = await asyncpg.connect(self.db_url)
            
            # Check if table exists
            table_exists = await conn.fetchval(
                "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = $1)",
                table_name
            )
            
            if not table_exists:
                await conn.close()
                return {
                    "table_name": table_name,
                    "status": "critical",
                    "error": f"Table {table_name} does not exist",
                    "records_count": 0,
                    "last_record_time": None,
                    "hours_since_last_record": None,
                    "expected_frequency_minutes": config["expected_frequency_minutes"],
                    "business_impact": config["business_impact"],
                    "description": config["description"]
                }
            
            # Get comprehensive table statistics
            stats_query = f"""
            SELECT 
                MAX({config['time_column']}) as latest_time,
                MIN({config['time_column']}) as earliest_time,
                COUNT(*) as record_count,
                COUNT(DISTINCT DATE({config['time_column']})) as unique_days
            FROM {table_name}
            """
            
            stats = await conn.fetchrow(stats_query)
            
            # Get recent activity (last 24 hours)
            recent_activity_query = f"""
            SELECT COUNT(*) as recent_records
            FROM {table_name}
            WHERE {config['time_column']} >= NOW() - INTERVAL '24 hours'
            """
            
            recent_activity = await conn.fetchval(recent_activity_query)
            
            await conn.close()
            
            latest_time = stats['latest_time']
            earliest_time = stats['earliest_time']
            record_count = stats['record_count']
            unique_days = stats['unique_days']
            
            if latest_time:
                hours_since = (datetime.now() - latest_time).total_seconds() / 3600
                
                # Determine status based on thresholds
                if hours_since <= config['threshold_hours']:
                    status = "fresh"
                    alert_level = "none"
                elif hours_since <= config['critical_threshold_hours']:
                    status = "stale"
                    alert_level = "warning"
                else:
                    status = "critical"
                    alert_level = "critical"
                
                # Calculate expected vs actual frequency
                if earliest_time and latest_time != earliest_time:
                    total_hours = (latest_time - earliest_time).total_seconds() / 3600
                    actual_frequency_minutes = (total_hours * 60) / record_count if record_count > 0 else 0
                else:
                    actual_frequency_minutes = 0
                
                return {
                    "table_name": table_name,
                    "status": status,
                    "alert_level": alert_level,
                    "last_record_time": latest_time.isoformat(),
                    "earliest_record_time": earliest_time.isoformat() if earliest_time else None,
                    "records_count": record_count,
                    "unique_days": unique_days,
                    "recent_records_24h": recent_activity,
                    "hours_since_last_record": round(hours_since, 2),
                    "expected_frequency_minutes": config["expected_frequency_minutes"],
                    "actual_frequency_minutes": round(actual_frequency_minutes, 2),
                    "frequency_deviation": round(abs(actual_frequency_minutes - config["expected_frequency_minutes"]), 2),
                    "business_impact": config["business_impact"],
                    "description": config["description"],
                    "threshold_hours": config["threshold_hours"],
                    "critical_threshold_hours": config["critical_threshold_hours"]
                }
            else:
                return {
                    "table_name": table_name,
                    "status": "critical",
                    "alert_level": "critical",
                    "error": f"No records found in {table_name}",
                    "records_count": record_count,
                    "recent_records_24h": recent_activity,
                    "business_impact": config["business_impact"],
                    "description": config["description"]
                }
                
        except Exception as e:
            return {
                "table_name": table_name,
                "status": "error",
                "alert_level": "critical",
                "error": f"Error checking {table_name}: {str(e)}",
                "business_impact": config["business_impact"],
                "description": config["description"]
            }

    async def check_all_tables(self) -> Dict[str, Any]:
        """Check freshness of all configured tables"""
        results = {}
        alerts = []
        
        for table_name, config in self.table_config.items():
            result = await self.check_table_freshness(table_name, config)
            results[table_name] = result
            
            # Generate alerts for stale/critical tables
            if result["status"] in ["stale", "critical"]:
                if "error" in result:
                    alert_msg = f"{table_name.upper()} {result['status'].upper()}: {result['error']}"
                else:
                    hours = result.get("hours_since_last_record", 0)
                    alert_msg = f"{table_name.upper()} {result['status'].upper()}: {hours}h since last record"
                alerts.append(alert_msg)
        
        # Calculate summary statistics
        total_tables = len(results)
        fresh_tables = len([r for r in results.values() if r["status"] == "fresh"])
        stale_tables = len([r for r in results.values() if r["status"] == "stale"])
        critical_tables = len([r for r in results.values() if r["status"] == "critical"])
        error_tables = len([r for r in results.values() if r["status"] == "error"])
        
        # Determine overall database health
        if critical_tables > 0 or error_tables > 0:
            overall_status = "critical"
        elif stale_tables > 0:
            overall_status = "degraded"
        else:
            overall_status = "healthy"
        
        summary = {
            "overall_status": overall_status,
            "timestamp": datetime.now().isoformat(),
            "total_tables": total_tables,
            "fresh_tables": fresh_tables,
            "stale_tables": stale_tables,
            "critical_tables": critical_tables,
            "error_tables": error_tables,
            "alerts": alerts,
            "tables": results
        }
        
        return summary

    async def log_freshness_check(self, summary: Dict[str, Any]):
        """Log freshness check results for historical tracking"""
        log_entry = {
            "timestamp": summary["timestamp"],
            "overall_status": summary["overall_status"],
            "summary": {
                "total_tables": summary["total_tables"],
                "fresh_tables": summary["fresh_tables"],
                "stale_tables": summary["stale_tables"],
                "critical_tables": summary["critical_tables"],
                "error_tables": summary["error_tables"]
            },
            "alerts": summary["alerts"]
        }
        
        try:
            with open(self.monitoring_log_path, "a") as f:
                f.write(json.dumps(log_entry) + "\n")
        except Exception as e:
            print(f"Failed to log freshness check: {e}")

    async def generate_freshness_report(self) -> str:
        """Generate a detailed freshness report"""
        summary = await self.check_all_tables()
        await self.log_freshness_check(summary)
        
        report = f"""
DATABASE FRESHNESS MONITORING REPORT
====================================
Generated: {summary['timestamp']}
Overall Status: {summary['overall_status'].upper()}

SUMMARY STATISTICS:
- Total Tables: {summary['total_tables']}
- Fresh Tables: {summary['fresh_tables']}
- Stale Tables: {summary['stale_tables']}
- Critical Tables: {summary['critical_tables']}
- Error Tables: {summary['error_tables']}

ALERTS ({len(summary['alerts'])}):
"""
        
        for alert in summary['alerts']:
            report += f"- {alert}\n"
        
        report += "\nDETAILED TABLE STATUS:\n"
        report += "=" * 50 + "\n"
        
        for table_name, details in summary['tables'].items():
            status_icon = {
                "fresh": "✅",
                "stale": "⚠️",
                "critical": "❌",
                "error": "💥"
            }.get(details['status'], "❓")
            
            report += f"\n{status_icon} {table_name.upper()} ({details['status'].upper()})\n"
            report += f"   Description: {details['description']}\n"
            report += f"   Business Impact: {details['business_impact']}\n"
            
            if 'error' in details:
                report += f"   Error: {details['error']}\n"
            else:
                report += f"   Records: {details.get('records_count', 0):,}\n"
                if 'hours_since_last_record' in details:
                    report += f"   Last Record: {details['hours_since_last_record']}h ago\n"
                if 'recent_records_24h' in details:
                    report += f"   Recent Activity (24h): {details['recent_records_24h']} records\n"
        
        return report

async def main():
    """Main function for standalone execution"""
    monitor = DatabaseFreshnessMonitor()
    
    print("🔍 Database Freshness Monitor")
    print("=" * 40)
    
    # Generate and display report
    report = await monitor.generate_freshness_report()
    print(report)
    
    # Also save to file
    report_path = Path("logs/monitoring/database_freshness_report.txt")
    report_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(report_path, "w") as f:
        f.write(report)
    
    print(f"\n📄 Report saved to: {report_path}")

if __name__ == "__main__":
    asyncio.run(main())
