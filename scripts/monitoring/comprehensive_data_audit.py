#!/usr/bin/env python3
"""
Comprehensive Data Audit System
Complete analysis of all tables, schedules, and data collection status
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import psycopg2
import pandas as pd
from datetime import datetime, timedelta
import json
import subprocess
import logging
from typing import Dict, List, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ComprehensiveDataAuditor:
    """Complete audit of all data sources, tables, and schedules"""
    
    def __init__(self):
        self.db_config = {
            'host': 'localhost',
            'database': 'solar_prediction',
            'user': 'postgres',
            'password': 'postgres'
        }
        
        # Define all data-related tables and their expected sources
        self.data_tables = {
            'nasa_power_data': {
                'source': 'NASA POWER API',
                'type': 'Weather/Solar Data',
                'expected_frequency': 'Daily',
                'parameters': ['GHI', 'Temperature', 'Wind Speed', 'Humidity', 'Pressure', 'Clear Sky GHI', 'Clearness Index'],
                'collector_script': 'scripts/data/nasa_power_collector.py',
                'schedule_name': 'NASA POWER Daily Collection'
            },
            'solax_data': {
                'source': 'SolaX Cloud API - System 1',
                'type': 'Solar System Data',
                'expected_frequency': 'Real-time (30s)',
                'parameters': ['AC Power', 'Yield Today', 'Battery SOC', 'Battery Power', 'Grid Power', 'Load Power'],
                'collector_script': 'scripts/data/solax_collector.py',
                'schedule_name': 'SolaX Real-time Collection'
            },
            'solax_data2': {
                'source': 'SolaX Cloud API - System 2',
                'type': 'Solar System Data',
                'expected_frequency': 'Real-time (30s)',
                'parameters': ['AC Power', 'Yield Today', 'Battery SOC', 'Battery Power', 'Grid Power', 'Load Power'],
                'collector_script': 'scripts/data/solax_collector.py',
                'schedule_name': 'SolaX System 2 Collection'
            },
            'weather_data': {
                'source': 'Open-Meteo Weather API',
                'type': 'Weather Data',
                'expected_frequency': 'Hourly',
                'parameters': ['Solar Radiation', 'Temperature', 'Humidity', 'Wind Speed', 'Cloud Cover', 'Precipitation'],
                'collector_script': 'scripts/data/weather_collector.py',
                'schedule_name': 'Weather Hourly Collection'
            },
            'cams_radiation_data': {
                'source': 'CAMS Solar Radiation API',
                'type': 'Solar/Atmospheric Data',
                'expected_frequency': 'Daily',
                'parameters': ['GHI', 'DNI', 'DHI', 'Cloud Cover', 'Temperature'],
                'collector_script': 'scripts/data/cams_solar_collector.py',
                'schedule_name': 'CAMS Solar Daily Collection'
            },
            'era5_data': {
                'source': 'ERA5 Reanalysis API',
                'type': 'Reanalysis Data',
                'expected_frequency': 'Daily',
                'parameters': ['Temperature', 'Solar Radiation', 'Cloud Cover', 'Wind Speed', 'Pressure', 'Humidity'],
                'collector_script': 'scripts/data/era5_data_collector.py',
                'schedule_name': 'ERA5 Daily Collection'
            },
            'cams_aerosol_data': {
                'source': 'CAMS Aerosol/Cloud API',
                'type': 'Atmospheric Composition',
                'expected_frequency': 'Daily',
                'parameters': ['Cloud Cover', 'Total AOD', 'Dust AOD', 'Sea Salt AOD', 'Organic Matter AOD'],
                'collector_script': 'scripts/data/cams_aerosol_collector.py',
                'schedule_name': 'CAMS Aerosol Daily Collection'
            },
            'satellite_data': {
                'source': 'EUMETSAT/NOAA GOES APIs',
                'type': 'Satellite Data',
                'expected_frequency': 'Variable',
                'parameters': ['Cloud Mask', 'Solar Irradiance', 'Product Metadata'],
                'collector_script': 'scripts/data/satellite_collector.py',
                'schedule_name': 'Satellite Data Collection'
            }
        }
    
    def get_all_data_tables(self) -> List[str]:
        """Get all data-related tables from database"""
        
        try:
            conn = psycopg2.connect(**self.db_config)
            
            with conn.cursor() as cur:
                # Get all tables that contain data (exclude system tables)
                cur.execute("""
                    SELECT tablename 
                    FROM pg_tables 
                    WHERE schemaname = 'public' 
                    AND tablename LIKE '%data%'
                    OR tablename IN ('solax_data', 'solax_data2', 'predictions', 'weather_data')
                    ORDER BY tablename;
                """)
                
                tables = [row[0] for row in cur.fetchall()]
            
            conn.close()
            return tables
            
        except Exception as e:
            logger.error(f"Error getting tables: {e}")
            return []
    
    def analyze_table_status(self, table_name: str) -> Dict:
        """Analyze status of a specific table"""
        
        try:
            conn = psycopg2.connect(**self.db_config)
            
            with conn.cursor() as cur:
                # Check if table exists
                cur.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_name = %s
                    );
                """, (table_name,))
                
                if not cur.fetchone()[0]:
                    return {'exists': False, 'error': 'Table not found'}
                
                # Get table statistics
                cur.execute(f"""
                    SELECT 
                        COUNT(*) as total_records,
                        MIN(timestamp) as earliest_record,
                        MAX(timestamp) as latest_record
                    FROM {table_name}
                    WHERE timestamp IS NOT NULL
                """)
                
                result = cur.fetchone()
                total_records = result[0] if result[0] else 0
                earliest = result[1]
                latest = result[2]
                
                # Get recent activity
                cur.execute(f"""
                    SELECT COUNT(*) 
                    FROM {table_name}
                    WHERE timestamp >= NOW() - INTERVAL '24 hours'
                """)
                
                last_24h = cur.fetchone()[0] if cur.fetchone() else 0
                
                cur.execute(f"""
                    SELECT COUNT(*) 
                    FROM {table_name}
                    WHERE timestamp >= NOW() - INTERVAL '1 hour'
                """)
                
                last_1h = cur.fetchone()[0] if cur.fetchone() else 0
                
                # Calculate data freshness
                if latest:
                    hours_since_update = (datetime.now() - latest.replace(tzinfo=None)).total_seconds() / 3600
                else:
                    hours_since_update = None
                
                # Determine status
                if total_records == 0:
                    status = 'NO_DATA'
                elif hours_since_update is None:
                    status = 'NO_TIMESTAMP'
                elif hours_since_update <= 2:
                    status = 'ACTIVE'
                elif hours_since_update <= 24:
                    status = 'RECENT'
                elif hours_since_update <= 168:  # 1 week
                    status = 'STALE'
                else:
                    status = 'VERY_STALE'
                
                conn.close()
                
                return {
                    'exists': True,
                    'total_records': total_records,
                    'earliest_record': earliest,
                    'latest_record': latest,
                    'last_24h_records': last_24h,
                    'last_1h_records': last_1h,
                    'hours_since_update': hours_since_update,
                    'status': status
                }
                
        except Exception as e:
            logger.error(f"Error analyzing table {table_name}: {e}")
            return {'exists': False, 'error': str(e)}
    
    def get_schedule_status(self) -> Dict:
        """Get status of all scheduled tasks"""
        
        try:
            conn = psycopg2.connect(**self.db_config)
            
            # Check if schedule tables exist
            with conn.cursor() as cur:
                cur.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_name = 'task_schedules'
                    );
                """)
                
                if not cur.fetchone()[0]:
                    return {'schedules_exist': False, 'schedules': {}}
                
                # Get all schedules
                cur.execute("""
                    SELECT 
                        task_name,
                        schedule_expression,
                        is_active,
                        last_run,
                        next_run,
                        created_at,
                        updated_at
                    FROM task_schedules
                    ORDER BY task_name
                """)
                
                schedules = {}
                for row in cur.fetchall():
                    schedules[row[0]] = {
                        'schedule_expression': row[1],
                        'is_active': row[2],
                        'last_run': row[3],
                        'next_run': row[4],
                        'created_at': row[5],
                        'updated_at': row[6]
                    }
                
                # Get execution history
                cur.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_name = 'task_executions'
                    );
                """)
                
                if cur.fetchone()[0]:
                    cur.execute("""
                        SELECT 
                            task_name,
                            COUNT(*) as total_executions,
                            MAX(started_at) as last_execution,
                            SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as successful_executions
                        FROM task_executions
                        GROUP BY task_name
                    """)
                    
                    for row in cur.fetchall():
                        task_name = row[0]
                        if task_name in schedules:
                            schedules[task_name].update({
                                'total_executions': row[1],
                                'last_execution': row[2],
                                'successful_executions': row[3],
                                'success_rate': (row[3] / row[1] * 100) if row[1] > 0 else 0
                            })
            
            conn.close()
            return {'schedules_exist': True, 'schedules': schedules}
            
        except Exception as e:
            logger.error(f"Error getting schedule status: {e}")
            return {'schedules_exist': False, 'error': str(e)}
    
    def check_crontab_schedules(self) -> Dict:
        """Check system crontab for scheduled tasks"""
        
        try:
            # Get current user's crontab
            result = subprocess.run(['crontab', '-l'], capture_output=True, text=True)
            
            if result.returncode == 0:
                crontab_lines = result.stdout.strip().split('\n')
                
                schedules = {}
                for line in crontab_lines:
                    if line.strip() and not line.startswith('#'):
                        parts = line.split()
                        if len(parts) >= 6:
                            schedule_expr = ' '.join(parts[:5])
                            command = ' '.join(parts[5:])
                            
                            # Extract task name from command
                            if 'solar-prediction-project' in command:
                                task_name = 'Unknown'
                                if 'nasa_power' in command:
                                    task_name = 'NASA POWER Collection'
                                elif 'solax' in command:
                                    task_name = 'SolaX Collection'
                                elif 'weather' in command:
                                    task_name = 'Weather Collection'
                                elif 'era5' in command:
                                    task_name = 'ERA5 Collection'
                                elif 'cams' in command:
                                    task_name = 'CAMS Collection'
                                
                                schedules[task_name] = {
                                    'schedule_expression': schedule_expr,
                                    'command': command,
                                    'type': 'crontab'
                                }
                
                return {'crontab_exists': True, 'schedules': schedules}
            else:
                return {'crontab_exists': False, 'error': 'No crontab found'}
                
        except Exception as e:
            logger.error(f"Error checking crontab: {e}")
            return {'crontab_exists': False, 'error': str(e)}
    
    def generate_comprehensive_audit(self) -> Dict:
        """Generate complete audit report"""
        
        logger.info("🔍 Starting comprehensive data audit...")
        
        audit_report = {
            'audit_timestamp': datetime.now().isoformat(),
            'tables': {},
            'schedules': {},
            'crontab': {},
            'summary': {
                'total_tables': 0,
                'active_tables': 0,
                'total_records': 0,
                'tables_with_recent_data': 0,
                'scheduled_tasks': 0,
                'active_schedules': 0
            }
        }
        
        # Analyze all data tables
        logger.info("📊 Analyzing data tables...")
        all_tables = self.get_all_data_tables()
        
        for table_name in all_tables:
            logger.info(f"Checking table: {table_name}")
            
            table_status = self.analyze_table_status(table_name)
            
            # Add metadata if we have it
            if table_name in self.data_tables:
                table_status.update(self.data_tables[table_name])
            
            audit_report['tables'][table_name] = table_status
            
            # Update summary
            audit_report['summary']['total_tables'] += 1
            if table_status.get('status') == 'ACTIVE':
                audit_report['summary']['active_tables'] += 1
            if table_status.get('hours_since_update', float('inf')) <= 24:
                audit_report['summary']['tables_with_recent_data'] += 1
            audit_report['summary']['total_records'] += table_status.get('total_records', 0)
        
        # Analyze schedules
        logger.info("⏰ Analyzing scheduled tasks...")
        schedule_status = self.get_schedule_status()
        audit_report['schedules'] = schedule_status
        
        if schedule_status.get('schedules_exist'):
            audit_report['summary']['scheduled_tasks'] = len(schedule_status['schedules'])
            audit_report['summary']['active_schedules'] = sum(
                1 for s in schedule_status['schedules'].values() 
                if s.get('is_active', False)
            )
        
        # Check crontab
        logger.info("📅 Checking crontab schedules...")
        crontab_status = self.check_crontab_schedules()
        audit_report['crontab'] = crontab_status
        
        return audit_report
    
    def print_audit_report(self, audit_report: Dict):
        """Print formatted audit report"""
        
        print("🔍 COMPREHENSIVE DATA AUDIT REPORT")
        print("=" * 80)
        print(f"📅 Audit Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🔢 Total Tables: {audit_report['summary']['total_tables']}")
        print(f"✅ Active Tables: {audit_report['summary']['active_tables']}")
        print(f"📊 Total Records: {audit_report['summary']['total_records']:,}")
        print(f"📈 Recent Data: {audit_report['summary']['tables_with_recent_data']} tables")
        print(f"⏰ Scheduled Tasks: {audit_report['summary']['scheduled_tasks']}")
        print(f"🟢 Active Schedules: {audit_report['summary']['active_schedules']}")
        
        print("\n" + "=" * 80)
        print("📋 DETAILED TABLE ANALYSIS")
        print("=" * 80)
        
        # Group tables by status
        status_groups = {
            'ACTIVE': [],
            'RECENT': [],
            'STALE': [],
            'VERY_STALE': [],
            'NO_DATA': [],
            'ERROR': []
        }
        
        for table_name, table_data in audit_report['tables'].items():
            status = table_data.get('status', 'ERROR')
            if not table_data.get('exists', False):
                status = 'ERROR'
            
            status_groups[status].append((table_name, table_data))
        
        # Print each status group
        for status, tables in status_groups.items():
            if not tables:
                continue
                
            status_emoji = {
                'ACTIVE': '🟢',
                'RECENT': '🟡', 
                'STALE': '🟠',
                'VERY_STALE': '🔴',
                'NO_DATA': '⚪',
                'ERROR': '🚨'
            }
            
            print(f"\n{status_emoji[status]} {status} TABLES ({len(tables)})")
            print("-" * 60)
            
            for table_name, table_data in tables:
                print(f"📊 {table_name}")
                
                if table_data.get('source'):
                    print(f"   Source: {table_data['source']}")
                
                if table_data.get('exists', False):
                    print(f"   Records: {table_data.get('total_records', 0):,}")
                    
                    if table_data.get('latest_record'):
                        print(f"   Latest: {table_data['latest_record']}")
                    
                    if table_data.get('hours_since_update') is not None:
                        hours = table_data['hours_since_update']
                        if hours < 1:
                            print(f"   Updated: {hours*60:.0f} minutes ago")
                        elif hours < 24:
                            print(f"   Updated: {hours:.1f} hours ago")
                        else:
                            print(f"   Updated: {hours/24:.1f} days ago")
                    
                    print(f"   Last 24h: {table_data.get('last_24h_records', 0):,} records")
                    print(f"   Last 1h: {table_data.get('last_1h_records', 0):,} records")
                    
                    if table_data.get('expected_frequency'):
                        print(f"   Expected: {table_data['expected_frequency']}")
                else:
                    print(f"   Error: {table_data.get('error', 'Unknown error')}")
                
                print()
        
        # Print schedule information
        print("\n" + "=" * 80)
        print("⏰ SCHEDULE ANALYSIS")
        print("=" * 80)
        
        if audit_report['schedules'].get('schedules_exist'):
            schedules = audit_report['schedules']['schedules']
            
            if schedules:
                print(f"\n📅 DATABASE SCHEDULES ({len(schedules)})")
                print("-" * 60)
                
                for task_name, schedule_data in schedules.items():
                    status_emoji = "🟢" if schedule_data.get('is_active') else "🔴"
                    print(f"{status_emoji} {task_name}")
                    print(f"   Schedule: {schedule_data.get('schedule_expression', 'Unknown')}")
                    print(f"   Active: {schedule_data.get('is_active', False)}")
                    
                    if schedule_data.get('last_run'):
                        print(f"   Last Run: {schedule_data['last_run']}")
                    
                    if schedule_data.get('next_run'):
                        print(f"   Next Run: {schedule_data['next_run']}")
                    
                    if schedule_data.get('total_executions'):
                        print(f"   Executions: {schedule_data['total_executions']}")
                        print(f"   Success Rate: {schedule_data.get('success_rate', 0):.1f}%")
                    
                    print()
            else:
                print("⚠️ No database schedules found")
        else:
            print("❌ No schedule system found in database")
        
        # Print crontab information
        if audit_report['crontab'].get('crontab_exists'):
            crontab_schedules = audit_report['crontab']['schedules']
            
            if crontab_schedules:
                print(f"\n📅 CRONTAB SCHEDULES ({len(crontab_schedules)})")
                print("-" * 60)
                
                for task_name, cron_data in crontab_schedules.items():
                    print(f"🟢 {task_name}")
                    print(f"   Schedule: {cron_data['schedule_expression']}")
                    print(f"   Command: {cron_data['command'][:80]}...")
                    print()
            else:
                print("⚠️ No relevant crontab schedules found")
        else:
            print("❌ No crontab found or accessible")
        
        # Print recommendations
        print("\n" + "=" * 80)
        print("🎯 RECOMMENDATIONS")
        print("=" * 80)
        
        active_tables = audit_report['summary']['active_tables']
        total_tables = audit_report['summary']['total_tables']
        
        if active_tables / total_tables >= 0.8:
            print("✅ Excellent data coverage - most tables are active")
        elif active_tables / total_tables >= 0.6:
            print("⚠️ Good data coverage - some tables need attention")
        else:
            print("🚨 Poor data coverage - many tables need immediate attention")
        
        no_data_tables = len(status_groups['NO_DATA'])
        if no_data_tables > 0:
            print(f"🔧 {no_data_tables} tables have no data - setup collection")
        
        stale_tables = len(status_groups['STALE']) + len(status_groups['VERY_STALE'])
        if stale_tables > 0:
            print(f"⚠️ {stale_tables} tables have stale data - check collectors")
        
        if audit_report['summary']['active_schedules'] == 0:
            print("🚨 No active schedules found - setup automated collection")
        
        print(f"\n📈 Overall Health Score: {(active_tables / total_tables * 100):.1f}%")


def main():
    """Main audit function"""
    
    auditor = ComprehensiveDataAuditor()
    
    # Generate comprehensive audit
    audit_report = auditor.generate_comprehensive_audit()
    
    # Print detailed report
    auditor.print_audit_report(audit_report)
    
    # Save report
    report_path = f"/home/<USER>/solar-prediction-project/comprehensive_audit_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    with open(report_path, 'w') as f:
        json.dump(audit_report, f, indent=2, default=str)
    
    print(f"\n📄 Detailed report saved: {report_path}")
    
    return audit_report


if __name__ == "__main__":
    audit_report = main()
    sys.exit(0)
