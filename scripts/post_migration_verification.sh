#!/bin/bash
# post_migration_verification.sh - Επαλήθευση Data Migration

echo "🔍 POST-MIGRATION VERIFICATION REPORT"
echo "====================================="
echo "Comparing Local PostgreSQL (5432) vs Docker PostgreSQL (5433)"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Database connection parameters
LOCAL_DB="postgresql://postgres:postgres@localhost:5432/solar_prediction"
DOCKER_DB_EXTERNAL="postgresql://postgres:postgres@localhost:5433/solar_prediction"

echo "📊 1. TABLE COUNT COMPARISON"
echo "============================"

# Get table counts
LOCAL_TABLES=$(psql "$LOCAL_DB" -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';")
DOCKER_TABLES=$(docker exec solar-prediction-db psql -U postgres -d solar_prediction -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';")

echo "Local Database Tables: $LOCAL_TABLES"
echo "Docker Database Tables: $DOCKER_TABLES"

if [ "$LOCAL_TABLES" -eq "$DOCKER_TABLES" ]; then
    echo -e "${GREEN}✅ Table count matches${NC}"
else
    echo -e "${RED}❌ Table count mismatch${NC}"
fi

echo ""
echo "📋 2. DETAILED TABLE COMPARISON"
echo "==============================="

# Create comprehensive table comparison
printf "%-30s %-15s %-15s %-10s\n" "TABLE NAME" "LOCAL ROWS" "DOCKER ROWS" "STATUS"
printf "%-30s %-15s %-15s %-10s\n" "------------------------------" "---------------" "---------------" "----------"

# Get all table names from local database
psql "$LOCAL_DB" -t -c "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' ORDER BY table_name;" | while read table; do
    if [ -n "$table" ]; then
        # Remove whitespace
        table=$(echo "$table" | tr -d ' ')
        
        # Get row counts
        LOCAL_ROWS=$(psql "$LOCAL_DB" -t -c "SELECT COUNT(*) FROM $table;" 2>/dev/null | tr -d ' ')
        DOCKER_ROWS=$(docker exec solar-prediction-db psql -U postgres -d solar_prediction -t -c "SELECT COUNT(*) FROM $table;" 2>/dev/null | tr -d ' ')
        
        # Determine status
        if [ "$LOCAL_ROWS" = "$DOCKER_ROWS" ]; then
            STATUS="${GREEN}✅ SYNCED${NC}"
        else
            STATUS="${RED}❌ DIFF${NC}"
        fi
        
        printf "%-30s %-15s %-15s " "$table" "$LOCAL_ROWS" "$DOCKER_ROWS"
        echo -e "$STATUS"
    fi
done

echo ""
echo "🔑 3. CRITICAL DATA TABLES VERIFICATION"
echo "======================================="

# Critical tables for Solar Prediction System
CRITICAL_TABLES=("nasa_power_data" "solax_data" "solax_data2" "weather_data" "cams_radiation_data" "predictions" "system_alerts")

for table in "${CRITICAL_TABLES[@]}"; do
    LOCAL_COUNT=$(psql "$LOCAL_DB" -t -c "SELECT COUNT(*) FROM $table;" 2>/dev/null | tr -d ' ')
    DOCKER_COUNT=$(docker exec solar-prediction-db psql -U postgres -d solar_prediction -t -c "SELECT COUNT(*) FROM $table;" 2>/dev/null | tr -d ' ')
    
    echo -n "📊 $table: Local=$LOCAL_COUNT, Docker=$DOCKER_COUNT"
    
    if [ "$LOCAL_COUNT" = "$DOCKER_COUNT" ] && [ "$LOCAL_COUNT" != "0" ]; then
        echo -e " ${GREEN}✅ SYNCED${NC}"
    elif [ "$LOCAL_COUNT" = "$DOCKER_COUNT" ] && [ "$LOCAL_COUNT" = "0" ]; then
        echo -e " ${YELLOW}⚠️ EMPTY${NC}"
    else
        echo -e " ${RED}❌ MISMATCH${NC}"
    fi
done

echo ""
echo "👥 4. USER AND PERMISSIONS VERIFICATION"
echo "======================================="

# Check database users
echo "Local Database Users:"
psql "$LOCAL_DB" -c "SELECT usename FROM pg_user;"

echo ""
echo "Docker Database Users:"
docker exec solar-prediction-db psql -U postgres -d solar_prediction -c "SELECT usename FROM pg_user;"

echo ""
echo "🔐 5. SCHEMA COMPARISON"
echo "======================"

# Compare table schemas for critical tables
for table in "nasa_power_data" "solax_data"; do
    echo "Schema comparison for $table:"
    echo "Local schema:"
    psql "$LOCAL_DB" -c "\d $table" 2>/dev/null | head -20
    
    echo "Docker schema:"
    docker exec solar-prediction-db psql -U postgres -d solar_prediction -c "\d $table" 2>/dev/null | head -20
    echo "---"
done

echo ""
echo "📈 6. DATA INTEGRITY CHECKS"
echo "==========================="

# Check data integrity for NASA POWER data
echo "NASA POWER Data Integrity Check:"
LOCAL_LATEST=$(psql "$LOCAL_DB" -t -c "SELECT MAX(timestamp) FROM nasa_power_data;" 2>/dev/null)
DOCKER_LATEST=$(docker exec solar-prediction-db psql -U postgres -d solar_prediction -t -c "SELECT MAX(timestamp) FROM nasa_power_data;" 2>/dev/null)

echo "Local latest timestamp: $LOCAL_LATEST"
echo "Docker latest timestamp: $DOCKER_LATEST"

if [ "$LOCAL_LATEST" = "$DOCKER_LATEST" ]; then
    echo -e "${GREEN}✅ Timestamps match${NC}"
else
    echo -e "${RED}❌ Timestamp mismatch${NC}"
fi

echo ""
echo "🔄 7. DOCKER SERVICES CONNECTIVITY TEST"
echo "======================================="

# Test if Docker services can now access the data
echo "Testing Docker services database connectivity:"
docker exec solar-prediction-main psql -h postgres -p 5432 -U postgres -d solar_prediction -c "SELECT COUNT(*) FROM nasa_power_data;" 2>/dev/null && echo -e "${GREEN}✅ Main service can access data${NC}" || echo -e "${RED}❌ Main service cannot access data${NC}"

echo ""
echo "📊 8. FINAL MIGRATION STATUS"
echo "============================"

# Calculate overall migration success
TOTAL_ISSUES=0

# Check critical tables
for table in "${CRITICAL_TABLES[@]}"; do
    LOCAL_COUNT=$(psql "$LOCAL_DB" -t -c "SELECT COUNT(*) FROM $table;" 2>/dev/null | tr -d ' ')
    DOCKER_COUNT=$(docker exec solar-prediction-db psql -U postgres -d solar_prediction -t -c "SELECT COUNT(*) FROM $table;" 2>/dev/null | tr -d ' ')
    
    if [ "$LOCAL_COUNT" != "$DOCKER_COUNT" ]; then
        ((TOTAL_ISSUES++))
    fi
done

if [ $TOTAL_ISSUES -eq 0 ]; then
    echo -e "${GREEN}🎉 MIGRATION SUCCESSFUL!${NC}"
    echo "✅ All critical tables synchronized"
    echo "✅ Data integrity verified"
    echo "✅ Docker services can access data"
    echo ""
    echo "🚀 Next Steps:"
    echo "1. Update data collection scripts to use port 5433"
    echo "2. Stop local PostgreSQL service"
    echo "3. Test end-to-end system functionality"
else
    echo -e "${RED}❌ MIGRATION ISSUES DETECTED${NC}"
    echo "Issues found in $TOTAL_ISSUES critical tables"
    echo "Manual intervention required"
fi

echo ""
echo "✅ Verification completed!"

