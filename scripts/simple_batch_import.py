#!/usr/bin/env python3
"""
Simple Batch Import - Απλό import historical data με batch processing
"""

import pandas as pd
import psycopg2
import json
import os
from datetime import datetime
import time

def simple_import_file(file_path, system_id, batch_size=1000):
    """Απλό import ενός Excel file με batch processing"""
    print(f"\n📥 IMPORTING: {os.path.basename(file_path)}")
    print(f"   🏠 System ID: {system_id}")
    print(f"   📦 Batch size: {batch_size}")
    
    try:
        # Διάβασμα Excel file
        df = pd.read_excel(file_path, skiprows=1)
        print(f"   📊 Loaded: {len(df)} rows")
        
        if len(df) == 0:
            print(f"   ⚠️  Empty file - skipping")
            return 0
        
        # Ελέγχω για timestamp column
        time_columns = [col for col in df.columns if 'time' in col.lower() or 'date' in col.lower()]
        
        if not time_columns:
            print(f"   ❌ No timestamp column found")
            return 0
        
        time_col = time_columns[0]
        print(f"   📅 Time column: {time_col}")
        
        # Date range
        min_date = df[time_col].min()
        max_date = df[time_col].max()
        print(f"   📅 Date range: {min_date} to {max_date}")
        
        # Database connection
        conn = psycopg2.connect(
            host="localhost",
            database="solar_prediction",
            user="postgres",
            password="postgres"
        )
        cursor = conn.cursor()
        
        # Target table
        target_table = "solax_data" if system_id == 1 else "solax_data2"
        print(f"   💾 Target table: {target_table}")
        
        # Batch import
        imported_count = 0
        error_count = 0
        
        for i in range(0, len(df), batch_size):
            batch_df = df.iloc[i:i+batch_size]
            batch_start = i + 1
            batch_end = min(i + batch_size, len(df))
            
            print(f"   📦 Batch {batch_start}-{batch_end}...")
            
            batch_imported = 0
            
            for _, row in batch_df.iterrows():
                try:
                    # Timestamp
                    timestamp = row[time_col]
                    if pd.isna(timestamp):
                        error_count += 1
                        continue
                    
                    # Απλό insert χωρίς ON CONFLICT
                    insert_sql = f"""
                        INSERT INTO {target_table} (
                            timestamp, ac_power, yield_today, feedin_energy, 
                            consume_energy, system_id, inverter_sn, wifi_sn, raw_data
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """
                    
                    # Mapping columns με defaults
                    ac_power = row.get('Export power(W)', 0)
                    yield_today = row.get('Daily PV Yield(kWh)', 0)
                    feedin_energy = row.get('Daily exported energy(kWh)', 0)
                    consume_energy = row.get('Daily imported energy(kWh)', 0)
                    
                    raw_data = json.dumps({
                        'source': 'historical_import',
                        'file': os.path.basename(file_path),
                        'system_id': system_id,
                        'imported_at': datetime.now().isoformat()
                    })
                    
                    cursor.execute(insert_sql, (
                        timestamp,
                        float(ac_power) if ac_power else 0.0,
                        float(yield_today) if yield_today else 0.0,
                        float(feedin_energy) if feedin_energy else 0.0,
                        float(consume_energy) if consume_energy else 0.0,
                        system_id,
                        f"HIST_SYS{system_id}",
                        f"HIST_WIFI{system_id}",
                        raw_data
                    ))
                    
                    batch_imported += 1
                    
                except Exception as e:
                    error_count += 1
                    if error_count <= 3:  # Show first 3 errors only
                        print(f"      ⚠️  Row error: {e}")
                    continue
            
            # Commit batch
            try:
                conn.commit()
                imported_count += batch_imported
                print(f"      ✅ Batch committed: {batch_imported} records")
            except Exception as e:
                conn.rollback()
                print(f"      ❌ Batch failed: {e}")
            
            # Small delay between batches
            time.sleep(0.1)
        
        cursor.close()
        conn.close()
        
        print(f"   📊 Import summary:")
        print(f"      ✅ Imported: {imported_count:,} records")
        print(f"      ❌ Errors: {error_count:,} records")
        print(f"      📈 Success rate: {(imported_count/(imported_count+error_count)*100):.1f}%")
        
        return imported_count
        
    except Exception as e:
        print(f"   ❌ File import failed: {e}")
        return 0

def import_system_folder(system_path, system_id, system_name):
    """Import όλων των files ενός system folder"""
    print(f"\n🏠 IMPORTING {system_name}")
    print(f"   📁 Path: {system_path}")
    
    if not os.path.exists(system_path):
        print(f"   ❌ Folder does not exist")
        return 0
    
    # Βρίσκω Excel files
    excel_files = [f for f in os.listdir(system_path) if f.endswith('.xlsx')]
    excel_files.sort()  # Χρονολογική σειρά
    
    print(f"   📄 Files found: {len(excel_files)}")
    
    total_imported = 0
    
    for file_name in excel_files:
        file_path = os.path.join(system_path, file_name)
        imported = simple_import_file(file_path, system_id, batch_size=500)  # Μικρότερα batches
        total_imported += imported
    
    print(f"\n   📊 {system_name} Total: {total_imported:,} records imported")
    return total_imported

def main():
    """Κύρια συνάρτηση"""
    print("📥 SIMPLE BATCH IMPORT")
    print("=" * 50)
    print("Στόχος: Import historical data με απλό batch processing")
    
    start_time = time.time()
    
    # Import System1
    system1_imported = import_system_folder("data/raw/System1", 1, "System1")
    
    # Import System2  
    system2_imported = import_system_folder("data/raw/System2", 2, "System2")
    
    total_imported = system1_imported + system2_imported
    elapsed_time = time.time() - start_time
    
    print(f"\n📊 FINAL SUMMARY:")
    print(f"   🏠 System1: {system1_imported:,} records")
    print(f"   🏠 System2: {system2_imported:,} records")
    print(f"   📊 Total: {total_imported:,} records")
    print(f"   ⏱️  Time: {elapsed_time:.1f} seconds")
    
    if total_imported > 0:
        print(f"\n✅ IMPORT SUCCESSFUL!")
        print(f"   📅 Historical data imported from March 2024")
        print(f"   🎯 Ready for Enhanced Model v3!")
        
        # Έλεγχος τελικής κατάστασης
        try:
            conn = psycopg2.connect(
                host="localhost",
                database="solar_prediction",
                user="postgres",
                password="postgres"
            )
            cursor = conn.cursor()
            
            for table in ['solax_data', 'solax_data2']:
                cursor.execute(f"SELECT COUNT(*), MIN(timestamp), MAX(timestamp) FROM {table}")
                count, min_date, max_date = cursor.fetchone()
                print(f"   📊 {table}: {count:,} records ({min_date} to {max_date})")
            
            cursor.close()
            conn.close()
            
        except Exception as e:
            print(f"   ⚠️  Could not verify final state: {e}")
    else:
        print(f"\n❌ IMPORT FAILED!")
        print(f"   🔧 Check database connection and permissions")
    
    return total_imported > 0

if __name__ == "__main__":
    main()
