#!/usr/bin/env python3
"""
Phase 1: Schema Enhancement for solax_data2
Adds missing columns and calculates derived values for complete ML pipeline compatibility
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import psycopg2
from psycopg2.extras import RealDictCursor
import pandas as pd
from datetime import datetime, timedelta
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SchemaEnhancer:
    """Schema enhancer for production database"""
    
    def __init__(self):
        self.db_config = {
            'host': 'localhost',
            'database': 'solar_prediction',
            'user': 'postgres',
            'password': ''
        }
        
        self.changes_made = []
    
    def connect_database(self):
        """Connect to database"""
        try:
            conn = psycopg2.connect(**self.db_config)
            logger.info("✅ Connected to database")
            return conn
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            return None
    
    def backup_table(self, table_name):
        """Create backup of table before modifications"""
        
        logger.info(f"💾 Creating backup of {table_name}...")
        
        conn = self.connect_database()
        if not conn:
            return False
        
        try:
            cur = conn.cursor()
            
            # Create backup table
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_table = f"{table_name}_backup_{timestamp}"
            
            cur.execute(f"CREATE TABLE {backup_table} AS SELECT * FROM {table_name}")
            
            # Get record count
            cur.execute(f"SELECT COUNT(*) FROM {backup_table}")
            backup_count = cur.fetchone()[0]
            
            conn.commit()
            conn.close()
            
            logger.info(f"✅ Backup created: {backup_table} ({backup_count:,} records)")
            self.changes_made.append(f"Created backup: {backup_table}")
            
            return backup_table
            
        except Exception as e:
            logger.error(f"❌ Backup failed: {e}")
            conn.rollback()
            conn.close()
            return False
    
    def add_missing_columns(self, table_name, columns_to_add):
        """Add missing columns to table"""
        
        logger.info(f"🔧 Adding missing columns to {table_name}...")
        
        conn = self.connect_database()
        if not conn:
            return False
        
        try:
            cur = conn.cursor()
            
            for column_name, column_type in columns_to_add.items():
                # Check if column already exists
                cur.execute(f"""
                    SELECT column_name 
                    FROM information_schema.columns 
                    WHERE table_name = '{table_name}' AND column_name = '{column_name}'
                """)
                
                if cur.fetchone():
                    logger.info(f"   ✅ {column_name} already exists")
                    continue
                
                # Add column
                cur.execute(f"ALTER TABLE {table_name} ADD COLUMN {column_name} {column_type}")
                logger.info(f"   ✅ Added {column_name} ({column_type})")
                self.changes_made.append(f"Added {column_name} to {table_name}")
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to add columns: {e}")
            conn.rollback()
            conn.close()
            return False
    
    def calculate_total_yield_for_solax_data2(self):
        """Calculate total_yield for solax_data2 based on yield_today patterns"""
        
        logger.info("📊 Calculating total_yield for solax_data2...")
        
        conn = self.connect_database()
        if not conn:
            return False
        
        try:
            cur = conn.cursor(cursor_factory=RealDictCursor)
            
            # Get data ordered by timestamp
            cur.execute("""
                SELECT id, timestamp, yield_today 
                FROM solax_data2 
                ORDER BY timestamp
            """)
            
            data = cur.fetchall()
            logger.info(f"📊 Processing {len(data):,} records...")
            
            # Calculate cumulative total_yield
            total_yield = 0
            previous_yield_today = 0
            updates = []
            
            for i, row in enumerate(data):
                current_yield_today = float(row['yield_today'] or 0)
                
                # If yield_today reset (new day), add previous day's yield to total
                if current_yield_today < previous_yield_today:
                    total_yield += previous_yield_today
                    logger.info(f"   📅 Day reset detected at {row['timestamp']}: adding {previous_yield_today:.1f} kWh")
                
                # Current total_yield is base total + today's yield
                current_total_yield = total_yield + current_yield_today
                
                updates.append((current_total_yield, row['id']))
                previous_yield_today = current_yield_today
                
                # Log progress every 10000 records
                if (i + 1) % 10000 == 0:
                    logger.info(f"   📊 Processed {i + 1:,}/{len(data):,} records...")
            
            # Add final day's yield
            total_yield += previous_yield_today
            
            logger.info(f"📊 Final total yield calculated: {total_yield:.1f} kWh")
            logger.info(f"🔄 Updating {len(updates):,} records...")
            
            # Batch update
            cur.executemany(
                "UPDATE solax_data2 SET total_yield = %s WHERE id = %s",
                updates
            )
            
            conn.commit()
            conn.close()
            
            logger.info(f"✅ total_yield calculated and updated for solax_data2")
            self.changes_made.append(f"Calculated total_yield for {len(updates):,} records in solax_data2")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ total_yield calculation failed: {e}")
            conn.rollback()
            conn.close()
            return False
    
    def estimate_ac_power_for_solax_data2(self):
        """Estimate ac_power for solax_data2 based on yield_today changes"""
        
        logger.info("⚡ Estimating ac_power for solax_data2...")
        
        conn = self.connect_database()
        if not conn:
            return False
        
        try:
            cur = conn.cursor(cursor_factory=RealDictCursor)
            
            # Get data with 5-minute intervals
            cur.execute("""
                SELECT id, timestamp, yield_today,
                       LAG(yield_today) OVER (ORDER BY timestamp) as prev_yield_today,
                       LAG(timestamp) OVER (ORDER BY timestamp) as prev_timestamp
                FROM solax_data2 
                ORDER BY timestamp
            """)
            
            data = cur.fetchall()
            logger.info(f"📊 Processing {len(data):,} records for ac_power estimation...")
            
            updates = []
            
            for i, row in enumerate(data):
                current_yield = float(row['yield_today'] or 0)
                prev_yield = float(row['prev_yield_today'] or 0) if row['prev_yield_today'] else 0
                
                # Calculate power based on yield increase
                if row['prev_timestamp'] and current_yield > prev_yield:
                    # Calculate time difference in hours
                    time_diff = (row['timestamp'] - row['prev_timestamp']).total_seconds() / 3600
                    
                    if time_diff > 0 and time_diff <= 1:  # Only for reasonable time intervals
                        # Power = Energy difference / Time difference
                        yield_diff = current_yield - prev_yield
                        estimated_power = (yield_diff / time_diff) * 1000  # Convert kWh to W
                        
                        # Reasonable limits for solar power (0-12000W)
                        estimated_power = max(0, min(12000, estimated_power))
                    else:
                        estimated_power = 0
                else:
                    estimated_power = 0
                
                updates.append((estimated_power, row['id']))
                
                # Log progress
                if (i + 1) % 10000 == 0:
                    logger.info(f"   📊 Processed {i + 1:,}/{len(data):,} records...")
            
            logger.info(f"🔄 Updating ac_power for {len(updates):,} records...")
            
            # Batch update
            cur.executemany(
                "UPDATE solax_data2 SET ac_power = %s WHERE id = %s",
                updates
            )
            
            conn.commit()
            conn.close()
            
            logger.info(f"✅ ac_power estimated and updated for solax_data2")
            self.changes_made.append(f"Estimated ac_power for {len(updates):,} records in solax_data2")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ ac_power estimation failed: {e}")
            conn.rollback()
            conn.close()
            return False
    
    def validate_enhancements(self):
        """Validate schema enhancements"""
        
        logger.info("✅ Validating schema enhancements...")
        
        conn = self.connect_database()
        if not conn:
            return False
        
        try:
            cur = conn.cursor(cursor_factory=RealDictCursor)
            
            # Check columns exist
            cur.execute("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'solax_data2' 
                AND column_name IN ('total_yield', 'ac_power')
                ORDER BY column_name
            """)
            
            columns = [row['column_name'] for row in cur.fetchall()]
            logger.info(f"📊 Columns found: {columns}")
            
            # Check data completeness
            cur.execute("""
                SELECT 
                    COUNT(*) as total_records,
                    COUNT(total_yield) as total_yield_populated,
                    COUNT(ac_power) as ac_power_populated,
                    AVG(total_yield) as avg_total_yield,
                    AVG(ac_power) as avg_ac_power
                FROM solax_data2
            """)
            
            stats = cur.fetchone()
            
            logger.info(f"📊 Validation Results:")
            logger.info(f"   Total records: {stats['total_records']:,}")
            logger.info(f"   total_yield populated: {stats['total_yield_populated']:,}")
            logger.info(f"   ac_power populated: {stats['ac_power_populated']:,}")
            logger.info(f"   Average total_yield: {stats['avg_total_yield']:.1f} kWh")
            logger.info(f"   Average ac_power: {stats['avg_ac_power']:.1f} W")
            
            # Sample recent data
            cur.execute("""
                SELECT timestamp, yield_today, total_yield, ac_power, soc, bat_power
                FROM solax_data2 
                ORDER BY timestamp DESC 
                LIMIT 5
            """)
            
            samples = cur.fetchall()
            logger.info(f"📋 Sample enhanced data:")
            for sample in samples:
                logger.info(f"   {sample['timestamp']}: yield_today={sample['yield_today']}, total_yield={sample['total_yield']:.1f}, ac_power={sample['ac_power']:.0f}")
            
            conn.close()
            
            # Validation criteria
            validation_passed = (
                'total_yield' in columns and
                'ac_power' in columns and
                stats['total_yield_populated'] > 0 and
                stats['ac_power_populated'] > 0
            )
            
            if validation_passed:
                logger.info("✅ Schema enhancement validation PASSED")
                return True
            else:
                logger.error("❌ Schema enhancement validation FAILED")
                return False
            
        except Exception as e:
            logger.error(f"❌ Validation failed: {e}")
            conn.close()
            return False
    
    def run_schema_enhancement(self):
        """Run complete schema enhancement process"""
        
        logger.info("🚀 Starting schema enhancement for solax_data2...")
        
        # Step 1: Create backup
        backup_table = self.backup_table('solax_data2')
        if not backup_table:
            logger.error("❌ Cannot proceed without backup")
            return False
        
        # Step 2: Add missing columns
        columns_to_add = {
            'total_yield': 'DOUBLE PRECISION',
            'ac_power': 'DOUBLE PRECISION'
        }
        
        if not self.add_missing_columns('solax_data2', columns_to_add):
            logger.error("❌ Failed to add columns")
            return False
        
        # Step 3: Calculate total_yield
        if not self.calculate_total_yield_for_solax_data2():
            logger.error("❌ Failed to calculate total_yield")
            return False
        
        # Step 4: Estimate ac_power
        if not self.estimate_ac_power_for_solax_data2():
            logger.error("❌ Failed to estimate ac_power")
            return False
        
        # Step 5: Validate enhancements
        if not self.validate_enhancements():
            logger.error("❌ Validation failed")
            return False
        
        logger.info("🎉 Schema enhancement completed successfully!")
        return True

def print_enhancement_summary(enhancer):
    """Print enhancement summary"""
    
    print("\n" + "="*80)
    print("🔧 SCHEMA ENHANCEMENT SUMMARY")
    print("="*80)
    print(f"📅 Enhancement Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🗄️ Target Table: solax_data2")
    print()
    
    print("🔧 CHANGES MADE:")
    for change in enhancer.changes_made:
        print(f"   ✅ {change}")
    print()
    
    print("📊 ENHANCED SCHEMA:")
    print("   📋 solax_data2 now includes:")
    print("      ✅ total_yield (calculated from yield_today patterns)")
    print("      ✅ ac_power (estimated from yield changes)")
    print("      ✅ Original columns (yield_today, soc, bat_power)")
    print()
    
    print("🎯 RESULT:")
    print("   ✅ solax_data2 now fully compatible with ML pipeline")
    print("   ✅ Both solar systems have complete feature sets")
    print("   ✅ Ready for Phase 2: Feature Engineering")
    
    print("="*80)

def main():
    """Main schema enhancement function"""
    
    print("🔧 PHASE 1: SCHEMA ENHANCEMENT")
    print("="*60)
    print("🔧 Enhancing solax_data2 for ML pipeline compatibility")
    print("📊 Adding missing total_yield and ac_power columns")
    print()
    
    try:
        # Initialize enhancer
        enhancer = SchemaEnhancer()
        
        # Run enhancement
        success = enhancer.run_schema_enhancement()
        
        if success:
            # Print summary
            print_enhancement_summary(enhancer)
            
            print(f"\n🎉 Schema enhancement completed successfully!")
            print("🚀 Ready to proceed to Phase 2: Feature Engineering")
            
            return True
        else:
            print("❌ Schema enhancement failed")
            return False
            
    except Exception as e:
        print(f"❌ Enhancement failed: {e}")
        logger.exception("Schema enhancement failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
