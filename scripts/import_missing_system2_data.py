#!/usr/bin/env python3
"""
Import Missing System 2 Data
Import the missing CSV file for System 2: 2024-06-28 to 2025-06-01
"""

import os
import csv
import psycopg2
from datetime import datetime

def get_db_connection():
    """Get database connection"""
    try:
        return psycopg2.connect(
            host="localhost",
            database="solar_prediction",
            user="postgres",
            password="postgres",
            port="5432"
        )
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return None

def import_csv_file(csv_file_path):
    """Import CSV file to database"""
    print(f"📊 IMPORTING: {os.path.basename(csv_file_path)}")
    print("=" * 50)
    
    if not os.path.exists(csv_file_path):
        print(f"❌ File not found: {csv_file_path}")
        return False
    
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        with open(csv_file_path, 'r', encoding='utf-8') as f:
            # Read first line to understand format
            first_line = f.readline().strip()
            print(f"📋 First line: {first_line}")

            # Reset file pointer
            f.seek(0)

            # The CSV has all data in one quoted string per line
            # Parse manually
            lines = f.readlines()

            # Parse header from first line
            header_line = lines[0].strip().replace('\ufeff', '').replace('"', '')
            clean_header = header_line.split(',')

            print(f"📋 Parsed header: {clean_header}")

            # Find column indices
            time_col_idx = None
            yield_col_idx = None

            for i, col in enumerate(clean_header):
                if 'Update time' in col:
                    time_col_idx = i
                elif 'Daily inverter output' in col and 'kWh' in col:
                    yield_col_idx = i
            
            if time_col_idx is None or yield_col_idx is None:
                print(f"❌ Required columns not found")
                print(f"   Time column index: {time_col_idx}")
                print(f"   Yield column index: {yield_col_idx}")
                return False
            
            print(f"✅ Found columns:")
            print(f"   Time: index {time_col_idx} ({clean_header[time_col_idx]})")
            print(f"   Yield: index {yield_col_idx} ({clean_header[yield_col_idx]})")
            
            # Process data lines (skip header)
            valid_records = []
            total_rows = 0

            for line in lines[1:]:  # Skip header
                total_rows += 1

                try:
                    # Parse the quoted line
                    line_clean = line.strip().replace('"', '')
                    parts = line_clean.split(',')

                    if len(parts) > max(time_col_idx, yield_col_idx):
                        # Parse timestamp
                        time_str = parts[time_col_idx].strip()
                        dt = datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')

                        # Parse yield
                        yield_str = parts[yield_col_idx].strip()
                        yield_val = float(yield_str)

                        # Validate data
                        if yield_val >= 0 and yield_val < 100:
                            valid_records.append((dt, yield_val))

                        if len(valid_records) % 10000 == 0:
                            print(f"   Processed {len(valid_records)} valid records...")

                except (ValueError, IndexError):
                    continue
            
            print(f"📊 Processing completed:")
            print(f"   Total rows: {total_rows}")
            print(f"   Valid records: {len(valid_records)}")
            
            if not valid_records:
                print("❌ No valid records found")
                return False
            
            print(f"   Date range: {valid_records[0][0]} to {valid_records[-1][0]}")
            
            # Import to database
            with conn.cursor() as cur:
                print(f"\n💾 Importing to database...")
                
                # Insert data in batches
                batch_size = 1000
                total_inserted = 0
                
                for i in range(0, len(valid_records), batch_size):
                    batch = valid_records[i:i + batch_size]
                    
                    # Prepare batch data
                    batch_data = []
                    for dt, yield_val in batch:
                        batch_data.append((
                            dt,
                            'SYSTEM2_EXTENDED',
                            'SYSTEM2_EXTENDED',
                            yield_val,
                            50.0,  # soc
                            0.0,   # bat_power
                            20.0   # temperature
                        ))
                    
                    # Insert batch (with conflict resolution)
                    insert_query = """
                        INSERT INTO solax_data2 (
                            timestamp, inverter_sn, wifi_sn, yield_today, 
                            soc, bat_power, temperature
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s)
                        ON CONFLICT (timestamp) DO UPDATE SET
                            yield_today = EXCLUDED.yield_today,
                            inverter_sn = EXCLUDED.inverter_sn
                    """
                    
                    cur.executemany(insert_query, batch_data)
                    total_inserted += len(batch_data)
                    
                    if total_inserted % 5000 == 0:
                        print(f"   Inserted {total_inserted} records...")
                
                conn.commit()
                
                print(f"✅ Import completed: {total_inserted} records")
                
                # Verify import
                cur.execute("""
                    SELECT 
                        COUNT(*) as total,
                        MIN(timestamp) as earliest,
                        MAX(timestamp) as latest,
                        ROUND(AVG(yield_today), 2) as avg_yield
                    FROM solax_data2
                """)
                
                result = cur.fetchone()
                print(f"\n📊 Final System 2 Data:")
                print(f"   Total records: {result[0]:,}")
                print(f"   Date range: {result[1]} to {result[2]}")
                print(f"   Average yield: {result[3]} kWh")
                
                return True
                
    except Exception as e:
        print(f"❌ Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        conn.close()

def analyze_final_data_coverage():
    """Analyze final data coverage for all systems"""
    print(f"\n📊 FINAL DATA COVERAGE ANALYSIS")
    print("=" * 40)
    
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        with conn.cursor() as cur:
            # System 1 analysis
            cur.execute("""
                SELECT 
                    COUNT(*) as records,
                    MIN(timestamp) as earliest,
                    MAX(timestamp) as latest,
                    COUNT(DISTINCT DATE(timestamp)) as unique_days
                FROM solax_data
                WHERE yield_today >= 0 AND yield_today < 100
            """)
            
            sys1_result = cur.fetchone()
            
            # System 2 analysis
            cur.execute("""
                SELECT 
                    COUNT(*) as records,
                    MIN(timestamp) as earliest,
                    MAX(timestamp) as latest,
                    COUNT(DISTINCT DATE(timestamp)) as unique_days
                FROM solax_data2
                WHERE yield_today >= 0 AND yield_today < 100
            """)
            
            sys2_result = cur.fetchone()
            
            # Weather analysis
            cur.execute("""
                SELECT 
                    COUNT(*) as records,
                    MIN(timestamp) as earliest,
                    MAX(timestamp) as latest,
                    COUNT(DISTINCT DATE(timestamp)) as unique_days
                FROM weather_data
            """)
            
            weather_result = cur.fetchone()
            
            print(f"🔋 SYSTEM 1:")
            print(f"   Records: {sys1_result[0]:,}")
            print(f"   Date range: {sys1_result[1]} to {sys1_result[2]}")
            print(f"   Unique days: {sys1_result[3]:,}")
            
            print(f"\n🔋 SYSTEM 2:")
            print(f"   Records: {sys2_result[0]:,}")
            print(f"   Date range: {sys2_result[1]} to {sys2_result[2]}")
            print(f"   Unique days: {sys2_result[3]:,}")
            
            print(f"\n🌤️  WEATHER DATA:")
            print(f"   Records: {weather_result[0]:,}")
            print(f"   Date range: {weather_result[1]} to {weather_result[2]}")
            print(f"   Unique days: {weather_result[3]:,}")
            
            # Check for gaps
            print(f"\n🔍 DATA GAPS ANALYSIS:")
            
            # Calculate expected days from March 2024 to today
            cur.execute("""
                SELECT 
                    (CURRENT_DATE - '2024-03-01'::date) + 1 as expected_days
            """)
            expected_days = cur.fetchone()[0]
            
            sys1_coverage = (sys1_result[3] / expected_days) * 100
            sys2_coverage = (sys2_result[3] / expected_days) * 100
            weather_coverage = (weather_result[3] / expected_days) * 100
            
            print(f"   Expected days (Mar 2024 - today): {expected_days}")
            print(f"   System 1 coverage: {sys1_coverage:.1f}%")
            print(f"   System 2 coverage: {sys2_coverage:.1f}%")
            print(f"   Weather coverage: {weather_coverage:.1f}%")
            
            # Check specific periods
            print(f"\n📅 SPECIFIC PERIODS CHECK:")
            
            periods = [
                ('April 2024', 2024, 4),
                ('April 2025', 2025, 4),
                ('Current Month', None, None)
            ]
            
            for period_name, year, month in periods:
                if year and month:
                    where_clause = f"EXTRACT(year FROM timestamp) = {year} AND EXTRACT(month FROM timestamp) = {month}"
                else:
                    where_clause = "EXTRACT(year FROM timestamp) = EXTRACT(year FROM CURRENT_DATE) AND EXTRACT(month FROM timestamp) = EXTRACT(month FROM CURRENT_DATE)"
                
                cur.execute(f"""
                    SELECT 
                        (SELECT COUNT(*) FROM solax_data WHERE {where_clause} AND yield_today >= 0) as sys1,
                        (SELECT COUNT(*) FROM solax_data2 WHERE {where_clause} AND yield_today >= 0) as sys2,
                        (SELECT COUNT(*) FROM weather_data WHERE {where_clause}) as weather
                """)
                
                result = cur.fetchone()
                print(f"   {period_name}: Sys1={result[0]}, Sys2={result[1]}, Weather={result[2]}")
            
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
    
    finally:
        conn.close()

def main():
    """Main import function"""
    print("🚀 IMPORT MISSING SYSTEM 2 DATA")
    print("=" * 40)
    print(f"Started: {datetime.now()}")
    
    # Import the missing CSV file
    csv_file = "data/raw/System2/Plant Reports 2024-06-28-2025-06-01.csv"
    
    success = import_csv_file(csv_file)
    
    if success:
        print(f"\n🎉 IMPORT COMPLETED SUCCESSFULLY!")
        
        # Analyze final coverage
        analyze_final_data_coverage()
        
        print(f"\n✅ System 2 data is now complete!")
        print("🔄 Ready for comprehensive analysis of both systems")
    else:
        print(f"\n❌ IMPORT FAILED!")
    
    return success

if __name__ == "__main__":
    main()
