#!/usr/bin/env python3
"""
Daily NASA POWER Data Collection
Scheduled script to collect latest NASA POWER data daily
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import psycopg2
import time
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/home/<USER>/solar-prediction-project/logs/nasa_power_daily.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class DailyNASAPowerCollector:
    """Daily NASA POWER data collection"""
    
    def __init__(self):
        self.base_url = "https://power.larc.nasa.gov/api/temporal/hourly/point"
        self.latitude = 38.141348260997596
        self.longitude = 24.0071653937747
        
        # Parameters to collect
        self.parameters = [
            'ALLSKY_SFC_SW_DWN',    # GHI (W/m²)
            'T2M',                   # Temperature (°C)
            'WS10M',                 # Wind Speed (m/s)
            'RH2M',                  # Relative Humidity (%)
            'PS',                    # Surface Pressure (kPa)
            'CLRSKY_SFC_SW_DWN',    # Clear Sky GHI (W/m²)
            'ALLSKY_KT'             # Clearness Index
        ]
        
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Solar-Prediction-Project/1.0 (Daily Collection)'
        })
    
    def get_latest_data_date(self) -> datetime.date:
        """Get the latest date we have NASA POWER data for"""
        
        try:
            conn = psycopg2.connect(
                host='localhost',
                database='solar_prediction',
                user='postgres',
                password='postgres'
            )
            
            with conn.cursor() as cur:
                cur.execute("""
                    SELECT DATE(MAX(timestamp)) 
                    FROM nasa_power_data 
                    WHERE source LIKE 'nasa_power%'
                """)
                result = cur.fetchone()
                
                if result and result[0]:
                    latest_date = result[0]
                    logger.info(f"📅 Latest NASA POWER data: {latest_date}")
                    return latest_date
                else:
                    logger.warning("No existing NASA POWER data found")
                    return datetime(2024, 3, 1).date()  # Default start
            
            conn.close()
            
        except Exception as e:
            logger.error(f"Error getting latest date: {e}")
            return datetime(2024, 3, 1).date()
    
    def collect_recent_data(self, days_back: int = 5) -> dict:
        """Collect recent NASA POWER data"""
        
        logger.info(f"🚀 Starting daily NASA POWER collection...")
        
        # Get latest data date
        latest_date = self.get_latest_data_date()
        
        # Calculate target dates (NASA has 2-day delay)
        end_date = (datetime.now() - timedelta(days=2)).date()
        start_date = max(latest_date + timedelta(days=1), end_date - timedelta(days=days_back))
        
        logger.info(f"🎯 Target range: {start_date} to {end_date}")
        
        if start_date > end_date:
            logger.info("✅ No new data to collect")
            return {
                'status': 'up_to_date',
                'message': 'Data is up to date',
                'days_processed': 0,
                'records_saved': 0
            }
        
        # Collect data for each day
        successful_days = 0
        failed_days = 0
        total_records = 0
        
        current_date = start_date
        while current_date <= end_date:
            try:
                logger.info(f"📥 Collecting data for {current_date}")
                
                # Request data
                result = self._request_day_data(current_date)
                
                if result['status'] == 'success':
                    # Save to database
                    records_saved = self._save_day_data(current_date, result['data'])
                    successful_days += 1
                    total_records += records_saved
                    logger.info(f"✅ {current_date}: {records_saved} records saved")
                else:
                    failed_days += 1
                    logger.error(f"❌ {current_date}: {result.get('error', 'Unknown error')}")
                
                # Rate limiting delay
                time.sleep(5)
                
                current_date += timedelta(days=1)
                
            except Exception as e:
                logger.error(f"❌ Error processing {current_date}: {e}")
                failed_days += 1
                current_date += timedelta(days=1)
                continue
        
        # Calculate success rate
        total_days = successful_days + failed_days
        success_rate = (successful_days / total_days) * 100 if total_days > 0 else 0
        
        result = {
            'status': 'completed',
            'days_processed': total_days,
            'successful_days': successful_days,
            'failed_days': failed_days,
            'records_saved': total_records,
            'success_rate': success_rate
        }
        
        logger.info(f"📊 Collection completed: {successful_days}/{total_days} days successful")
        logger.info(f"📈 Success rate: {success_rate:.1f}%")
        logger.info(f"💾 Records saved: {total_records}")
        
        return result
    
    def _request_day_data(self, date: datetime.date) -> dict:
        """Request NASA POWER data for a single day"""
        
        date_str = date.strftime('%Y%m%d')
        
        params = {
            'parameters': ','.join(self.parameters),
            'community': 'RE',
            'longitude': self.longitude,
            'latitude': self.latitude,
            'start': date_str,
            'end': date_str,
            'format': 'JSON'
        }
        
        try:
            response = self.session.get(
                self.base_url,
                params=params,
                timeout=60
            )
            
            if response.status_code == 200:
                data = response.json()
                return {'status': 'success', 'data': data}
            else:
                return {'status': 'failed', 'error': f"HTTP {response.status_code}"}
                
        except Exception as e:
            return {'status': 'error', 'error': str(e)}
    
    def _save_day_data(self, date: datetime.date, api_data: dict) -> int:
        """Save NASA POWER data for a day to database"""
        
        try:
            conn = psycopg2.connect(
                host='localhost',
                database='solar_prediction',
                user='postgres',
                password='postgres'
            )
            
            if 'properties' not in api_data or 'parameter' not in api_data['properties']:
                return 0
            
            parameters_data = api_data['properties']['parameter']
            records_saved = 0
            
            # Process hourly data
            for hour in range(24):
                hour_str = f"{hour:02d}"
                
                # Create timestamp
                timestamp = datetime.combine(date, datetime.min.time()) + timedelta(hours=hour)
                
                # Check if record already exists
                with conn.cursor() as cur:
                    cur.execute("""
                        SELECT COUNT(*) FROM nasa_power_data 
                        WHERE timestamp = %s AND source LIKE 'nasa_power%'
                    """, (timestamp,))
                    
                    if cur.fetchone()[0] > 0:
                        continue  # Skip existing records
                
                # Extract values for this hour
                record = {
                    'timestamp': timestamp,
                    'latitude': self.latitude,
                    'longitude': self.longitude,
                    'source': 'nasa_power_daily',
                    'ingestion_run_id': f"daily_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                }
                
                # Map parameters
                param_mapping = {
                    'ghi': 'ALLSKY_SFC_SW_DWN',
                    'temperature': 'T2M',
                    'wind_speed': 'WS10M',
                    'relative_humidity': 'RH2M',
                    'surface_pressure': 'PS',
                    'clear_sky_ghi': 'CLRSKY_SFC_SW_DWN',
                    'clearness_index': 'ALLSKY_KT'
                }
                
                for db_field, api_param in param_mapping.items():
                    if api_param in parameters_data and hour_str in parameters_data[api_param]:
                        value = parameters_data[api_param][hour_str]
                        record[db_field] = value if value != -999.0 else None
                    else:
                        record[db_field] = None
                
                # Insert record
                with conn.cursor() as cur:
                    cur.execute("""
                        INSERT INTO nasa_power_data (
                            timestamp, latitude, longitude, ghi, temperature, 
                            wind_speed, relative_humidity, surface_pressure, 
                            clear_sky_ghi, clearness_index, source, ingestion_run_id
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        record['timestamp'],
                        record['latitude'],
                        record['longitude'],
                        record['ghi'],
                        record['temperature'],
                        record['wind_speed'],
                        record['relative_humidity'],
                        record['surface_pressure'],
                        record['clear_sky_ghi'],
                        record['clearness_index'],
                        record['source'],
                        record['ingestion_run_id']
                    ))
                
                records_saved += 1
            
            conn.commit()
            conn.close()
            
            return records_saved
            
        except Exception as e:
            logger.error(f"Error saving data for {date}: {e}")
            return 0
    
    def send_notification(self, result: dict):
        """Send notification about collection results"""
        
        try:
            if result['status'] == 'up_to_date':
                logger.info("📧 No notification needed - data up to date")
                return
            
            success_rate = result.get('success_rate', 0)
            
            if success_rate >= 90:
                status_emoji = "✅"
                status_text = "SUCCESS"
            elif success_rate >= 70:
                status_emoji = "⚠️"
                status_text = "PARTIAL"
            else:
                status_emoji = "❌"
                status_text = "FAILED"
            
            message = f"""
{status_emoji} NASA POWER Daily Collection {status_text}

📊 Summary:
• Days processed: {result['days_processed']}
• Successful: {result['successful_days']}
• Failed: {result['failed_days']}
• Records saved: {result['records_saved']:,}
• Success rate: {success_rate:.1f}%

🕐 Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            """.strip()
            
            logger.info(f"📧 Collection summary:\n{message}")
            
        except Exception as e:
            logger.error(f"Error sending notification: {e}")


def main():
    """Main daily collection function"""
    
    print("🌟 Daily NASA POWER Collection")
    print("=" * 40)
    print(f"🕐 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Initialize collector
    collector = DailyNASAPowerCollector()
    
    try:
        # Collect recent data
        result = collector.collect_recent_data()
        
        # Send notification
        collector.send_notification(result)
        
        # Return success if collection was successful
        if result['status'] == 'up_to_date':
            print("✅ Data is up to date")
            return True
        elif result.get('success_rate', 0) >= 70:
            print(f"✅ Collection successful ({result['success_rate']:.1f}% success rate)")
            return True
        else:
            print(f"⚠️ Collection completed with issues ({result['success_rate']:.1f}% success rate)")
            return False
            
    except Exception as e:
        logger.error(f"❌ Daily collection failed: {e}")
        print(f"❌ Collection failed: {e}")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
