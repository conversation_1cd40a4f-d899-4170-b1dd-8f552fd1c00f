#!/usr/bin/env python3
import sys
sys.path.append('/home/<USER>/solar-prediction-project')
from scripts.data.cams_solar_collector import CAMSSolarCollector
from datetime import datetime, timedelta

collector = CAMSSolarCollector()
yesterday = datetime.now() - timedelta(days=1)
result = collector.collect_cams_solar_for_period(yesterday.date(), yesterday.date())
print(f"CAMS collection: {result['status']}")
