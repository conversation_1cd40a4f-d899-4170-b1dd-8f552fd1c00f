#!/usr/bin/env python3
"""
Comprehensive Data Validation
Check for missing records, weather correlation, and proper analysis
"""

import os
import subprocess
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class DataValidator:
    """Comprehensive data validator"""
    
    def __init__(self):
        pass
    
    def check_missing_records(self):
        """Check for missing records between systems"""
        print("🔍 CHECKING FOR MISSING RECORDS")
        print("=" * 35)
        
        # Check System 1 monthly coverage
        try:
            cmd = ['psql', '-h', 'localhost', '-U', 'postgres', '-d', 'solar_prediction', '-c', 
                   """SELECT 
                        EXTRACT(year FROM timestamp) as year,
                        EXTRACT(month FROM timestamp) as month,
                        COUNT(*) as records,
                        COUNT(DISTINCT DATE(timestamp)) as unique_days,
                        MIN(DATE(timestamp)) as first_date,
                        MAX(DATE(timestamp)) as last_date
                    FROM solax_data 
                    WHERE yield_today >= 0 AND yield_today < 100
                    GROUP BY EXTRACT(year FROM timestamp), EXTRACT(month FROM timestamp) 
                    ORDER BY year, month;"""]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print("✅ System 1 Monthly Coverage:")
                print(result.stdout)
            else:
                print("❌ Failed to get System 1 coverage")
        
        except Exception as e:
            print(f"❌ Error checking System 1: {e}")
        
        # Check System 2 monthly coverage
        try:
            cmd = ['psql', '-h', 'localhost', '-U', 'postgres', '-d', 'solar_prediction', '-c', 
                   """SELECT 
                        EXTRACT(year FROM timestamp) as year,
                        EXTRACT(month FROM timestamp) as month,
                        COUNT(*) as records,
                        COUNT(DISTINCT DATE(timestamp)) as unique_days,
                        MIN(DATE(timestamp)) as first_date,
                        MAX(DATE(timestamp)) as last_date
                    FROM solax_data2 
                    WHERE yield_today >= 0 AND yield_today < 100
                    GROUP BY EXTRACT(year FROM timestamp), EXTRACT(month FROM timestamp) 
                    ORDER BY year, month;"""]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print("\n✅ System 2 Monthly Coverage:")
                print(result.stdout)
            else:
                print("❌ Failed to get System 2 coverage")
        
        except Exception as e:
            print(f"❌ Error checking System 2: {e}")
    
    def check_weather_correlation(self):
        """Check weather conditions for year-over-year comparison"""
        print(f"\n🌤️  CHECKING WEATHER CONDITIONS")
        print("=" * 35)
        
        # April weather comparison
        try:
            cmd = ['psql', '-h', 'localhost', '-U', 'postgres', '-d', 'solar_prediction', '-c', 
                   """SELECT 
                        EXTRACT(year FROM timestamp) as year,
                        EXTRACT(month FROM timestamp) as month,
                        COUNT(*) as weather_records,
                        ROUND(AVG(temperature_2m), 2) as avg_temperature,
                        ROUND(AVG(global_horizontal_irradiance), 2) as avg_ghi,
                        ROUND(AVG(cloud_cover), 2) as avg_cloud_cover,
                        MIN(DATE(timestamp)) as first_date,
                        MAX(DATE(timestamp)) as last_date
                    FROM weather_data 
                    WHERE EXTRACT(month FROM timestamp) = 4
                    GROUP BY EXTRACT(year FROM timestamp), EXTRACT(month FROM timestamp) 
                    ORDER BY year;"""]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print("✅ April Weather Conditions:")
                print(result.stdout)
            else:
                print("❌ Failed to get weather data")
        
        except Exception as e:
            print(f"❌ Error checking weather: {e}")
        
        # Monthly weather summary
        try:
            cmd = ['psql', '-h', 'localhost', '-U', 'postgres', '-d', 'solar_prediction', '-c', 
                   """SELECT 
                        EXTRACT(year FROM timestamp) as year,
                        EXTRACT(month FROM timestamp) as month,
                        COUNT(*) as records,
                        ROUND(AVG(temperature_2m), 1) as avg_temp,
                        ROUND(AVG(global_horizontal_irradiance), 1) as avg_ghi
                    FROM weather_data 
                    GROUP BY EXTRACT(year FROM timestamp), EXTRACT(month FROM timestamp) 
                    ORDER BY year, month;"""]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print("\n✅ Monthly Weather Summary:")
                print(result.stdout)
            else:
                print("❌ Failed to get monthly weather")
        
        except Exception as e:
            print(f"❌ Error checking monthly weather: {e}")
    
    def create_annual_performance_table(self):
        """Create annual performance comparison table"""
        print(f"\n📊 ANNUAL PERFORMANCE TABLE")
        print("=" * 30)
        
        # System 1 annual performance
        try:
            cmd = ['psql', '-h', 'localhost', '-U', 'postgres', '-d', 'solar_prediction', '-c', 
                   """SELECT 
                        'System 1' as system,
                        EXTRACT(year FROM timestamp) as year,
                        COUNT(*) as total_records,
                        COUNT(DISTINCT DATE(timestamp)) as unique_days,
                        ROUND(AVG(yield_today), 2) as avg_daily_yield,
                        ROUND(SUM(yield_today), 2) as total_yield,
                        ROUND(MIN(yield_today), 2) as min_yield,
                        ROUND(MAX(yield_today), 2) as max_yield,
                        ROUND(STDDEV(yield_today), 2) as std_yield
                    FROM solax_data 
                    WHERE yield_today >= 0 AND yield_today < 100
                    GROUP BY EXTRACT(year FROM timestamp) 
                    ORDER BY year;"""]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print("✅ System 1 Annual Performance:")
                print(result.stdout)
            else:
                print("❌ Failed to get System 1 annual data")
        
        except Exception as e:
            print(f"❌ Error getting System 1 annual: {e}")
        
        # System 2 annual performance
        try:
            cmd = ['psql', '-h', 'localhost', '-U', 'postgres', '-d', 'solar_prediction', '-c', 
                   """SELECT 
                        'System 2' as system,
                        EXTRACT(year FROM timestamp) as year,
                        COUNT(*) as total_records,
                        COUNT(DISTINCT DATE(timestamp)) as unique_days,
                        ROUND(AVG(yield_today), 2) as avg_daily_yield,
                        ROUND(SUM(yield_today), 2) as total_yield,
                        ROUND(MIN(yield_today), 2) as min_yield,
                        ROUND(MAX(yield_today), 2) as max_yield,
                        ROUND(STDDEV(yield_today), 2) as std_yield
                    FROM solax_data2 
                    WHERE yield_today >= 0 AND yield_today < 100
                    GROUP BY EXTRACT(year FROM timestamp) 
                    ORDER BY year;"""]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print("\n✅ System 2 Annual Performance:")
                print(result.stdout)
            else:
                print("❌ Failed to get System 2 annual data")
        
        except Exception as e:
            print(f"❌ Error getting System 2 annual: {e}")
    
    def create_monthly_performance_table(self):
        """Create monthly performance comparison table"""
        print(f"\n📅 MONTHLY PERFORMANCE TABLE")
        print("=" * 32)
        
        # Combined monthly performance
        try:
            cmd = ['psql', '-h', 'localhost', '-U', 'postgres', '-d', 'solar_prediction', '-c', 
                   """WITH system1_monthly AS (
                        SELECT 
                            'System 1' as system,
                            EXTRACT(year FROM timestamp) as year,
                            EXTRACT(month FROM timestamp) as month,
                            COUNT(*) as records,
                            ROUND(AVG(yield_today), 2) as avg_yield,
                            ROUND(SUM(yield_today), 2) as total_yield
                        FROM solax_data 
                        WHERE yield_today >= 0 AND yield_today < 100
                        GROUP BY EXTRACT(year FROM timestamp), EXTRACT(month FROM timestamp)
                    ),
                    system2_monthly AS (
                        SELECT 
                            'System 2' as system,
                            EXTRACT(year FROM timestamp) as year,
                            EXTRACT(month FROM timestamp) as month,
                            COUNT(*) as records,
                            ROUND(AVG(yield_today), 2) as avg_yield,
                            ROUND(SUM(yield_today), 2) as total_yield
                        FROM solax_data2 
                        WHERE yield_today >= 0 AND yield_today < 100
                        GROUP BY EXTRACT(year FROM timestamp), EXTRACT(month FROM timestamp)
                    )
                    SELECT * FROM system1_monthly
                    UNION ALL
                    SELECT * FROM system2_monthly
                    ORDER BY year, month, system;"""]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print("✅ Monthly Performance Comparison:")
                print(result.stdout)
            else:
                print("❌ Failed to get monthly performance data")
        
        except Exception as e:
            print(f"❌ Error getting monthly performance: {e}")
    
    def create_current_week_table(self):
        """Create current week daily performance table"""
        print(f"\n📆 CURRENT WEEK DAILY PERFORMANCE")
        print("=" * 38)
        
        # Last 7 days performance
        try:
            cmd = ['psql', '-h', 'localhost', '-U', 'postgres', '-d', 'solar_prediction', '-c', 
                   """WITH last_week AS (
                        SELECT CURRENT_DATE - INTERVAL '6 days' as start_date,
                               CURRENT_DATE as end_date
                    ),
                    system1_week AS (
                        SELECT 
                            'System 1' as system,
                            DATE(timestamp) as date,
                            EXTRACT(dow FROM timestamp) as day_of_week,
                            COUNT(*) as records,
                            ROUND(MAX(yield_today), 2) as daily_yield
                        FROM solax_data, last_week
                        WHERE DATE(timestamp) BETWEEN start_date AND end_date
                            AND yield_today >= 0 AND yield_today < 100
                        GROUP BY DATE(timestamp), EXTRACT(dow FROM timestamp)
                    ),
                    system2_week AS (
                        SELECT 
                            'System 2' as system,
                            DATE(timestamp) as date,
                            EXTRACT(dow FROM timestamp) as day_of_week,
                            COUNT(*) as records,
                            ROUND(MAX(yield_today), 2) as daily_yield
                        FROM solax_data2, last_week
                        WHERE DATE(timestamp) BETWEEN start_date AND end_date
                            AND yield_today >= 0 AND yield_today < 100
                        GROUP BY DATE(timestamp), EXTRACT(dow FROM timestamp)
                    )
                    SELECT * FROM system1_week
                    UNION ALL
                    SELECT * FROM system2_week
                    ORDER BY date, system;"""]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print("✅ Last 7 Days Performance:")
                print(result.stdout)
            else:
                print("❌ Failed to get weekly performance data")
        
        except Exception as e:
            print(f"❌ Error getting weekly performance: {e}")
    
    def analyze_april_deviations(self):
        """Analyze April deviations with weather correlation"""
        print(f"\n🔍 APRIL DEVIATION ANALYSIS")
        print("=" * 30)
        
        # April performance with weather
        try:
            cmd = ['psql', '-h', 'localhost', '-U', 'postgres', '-d', 'solar_prediction', '-c', 
                   """WITH april_solar AS (
                        SELECT 
                            'System 1' as system,
                            EXTRACT(year FROM s.timestamp) as year,
                            COUNT(*) as records,
                            ROUND(AVG(s.yield_today), 2) as avg_yield,
                            ROUND(SUM(s.yield_today), 2) as total_yield
                        FROM solax_data s
                        WHERE EXTRACT(month FROM s.timestamp) = 4 
                            AND s.yield_today >= 0 AND s.yield_today < 100
                        GROUP BY EXTRACT(year FROM s.timestamp)
                        
                        UNION ALL
                        
                        SELECT 
                            'System 2' as system,
                            EXTRACT(year FROM s.timestamp) as year,
                            COUNT(*) as records,
                            ROUND(AVG(s.yield_today), 2) as avg_yield,
                            ROUND(SUM(s.yield_today), 2) as total_yield
                        FROM solax_data2 s
                        WHERE EXTRACT(month FROM s.timestamp) = 4 
                            AND s.yield_today >= 0 AND s.yield_today < 100
                        GROUP BY EXTRACT(year FROM s.timestamp)
                    ),
                    april_weather AS (
                        SELECT 
                            EXTRACT(year FROM timestamp) as year,
                            COUNT(*) as weather_records,
                            ROUND(AVG(temperature_2m), 2) as avg_temp,
                            ROUND(AVG(global_horizontal_irradiance), 2) as avg_ghi,
                            ROUND(AVG(cloud_cover), 2) as avg_clouds
                        FROM weather_data 
                        WHERE EXTRACT(month FROM timestamp) = 4
                        GROUP BY EXTRACT(year FROM timestamp)
                    )
                    SELECT 
                        s.system,
                        s.year,
                        s.records,
                        s.avg_yield,
                        s.total_yield,
                        w.avg_temp,
                        w.avg_ghi,
                        w.avg_clouds
                    FROM april_solar s
                    LEFT JOIN april_weather w ON s.year = w.year
                    ORDER BY s.system, s.year;"""]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print("✅ April Performance with Weather Correlation:")
                print(result.stdout)
            else:
                print("❌ Failed to get April correlation data")
        
        except Exception as e:
            print(f"❌ Error getting April correlation: {e}")

def main():
    """Main validation function"""
    print("🔍 COMPREHENSIVE DATA VALIDATION & ANALYSIS")
    print("=" * 55)
    print(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🎯 Checking for missing records, weather correlation, and proper analysis")
    
    try:
        validator = DataValidator()
        
        # Step 1: Check for missing records
        validator.check_missing_records()
        
        # Step 2: Check weather correlation
        validator.check_weather_correlation()
        
        # Step 3: Create annual performance table
        validator.create_annual_performance_table()
        
        # Step 4: Create monthly performance table
        validator.create_monthly_performance_table()
        
        # Step 5: Create current week table
        validator.create_current_week_table()
        
        # Step 6: Analyze April deviations
        validator.analyze_april_deviations()
        
        print(f"\n🎉 COMPREHENSIVE VALIDATION COMPLETED!")
        print("✅ All data validated and analyzed")
        print("📊 Performance tables generated")
        print("🔄 Ready for proper model training")
        
        return True
        
    except Exception as e:
        print(f"❌ Validation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
