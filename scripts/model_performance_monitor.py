#!/usr/bin/env python3
"""
Model Performance Monitor
Παρακολουθεί την απόδοση των μοντέλων και στέλνει alerts
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project/src')

import psycopg2
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import logging
import requests
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import schedule
import time

from models.multi_horizon_predictor import MultiHorizonPredictor

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/home/<USER>/solar-prediction-project/logs/model_monitoring.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ModelPerformanceMonitor:
    """
    Παρακολουθεί την απόδοση των μοντέλων σε πραγματικό χρόνο
    """
    
    def __init__(self):
        self.predictor = MultiHorizonPredictor()
        self.db_connection_string = "postgresql://postgres:postgres@localhost:5433/solar_prediction"
        self.alerts_dir = Path("/home/<USER>/solar-prediction-project/logs/alerts")
        self.alerts_dir.mkdir(exist_ok=True)
        
        # Performance thresholds for alerts
        self.alert_thresholds = {
            'daily': {
                'critical_r2': 0.80,
                'warning_r2': 0.85,
                'critical_mae': 10.0,
                'warning_mae': 8.0
            },
            'monthly': {
                'critical_r2': 0.85,
                'warning_r2': 0.90,
                'critical_mae': 6.0,
                'warning_mae': 5.0
            },
            'yearly': {
                'critical_r2': 0.70,
                'warning_r2': 0.75,
                'critical_mae': 4.0,
                'warning_mae': 3.0
            }
        }
        
        # Telegram configuration (if available)
        self.telegram_config = self.load_telegram_config()
    
    def load_telegram_config(self) -> Optional[Dict]:
        """Φορτώνει Telegram configuration"""
        try:
            config_file = Path("/home/<USER>/solar-prediction-project/config/telegram_config.json")
            if config_file.exists():
                with open(config_file, 'r') as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"Could not load Telegram config: {e}")
        return None
    
    def connect_database(self):
        """Σύνδεση στη βάση δεδομένων"""
        try:
            conn = psycopg2.connect(self.db_connection_string)
            return conn
        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            return None
    
    def get_recent_actual_data(self, system_id: int, horizon: str, days: int = 30) -> pd.DataFrame:
        """Παίρνει πρόσφατα πραγματικά δεδομένα για σύγκριση"""
        conn = self.connect_database()
        if not conn:
            return pd.DataFrame()
        
        try:
            table = 'solax_data' if system_id == 1 else 'solax_data2'
            
            if horizon == 'daily':
                query = f"""
                SELECT 
                    DATE(timestamp) as date,
                    MAX(yield_today) as actual_yield
                FROM {table}
                WHERE timestamp >= CURRENT_DATE - INTERVAL '{days} days'
                AND yield_today IS NOT NULL
                AND yield_today > 5
                GROUP BY DATE(timestamp)
                ORDER BY date DESC
                """
            elif horizon == 'monthly':
                query = f"""
                SELECT 
                    EXTRACT(year FROM timestamp) as year,
                    EXTRACT(month FROM timestamp) as month,
                    AVG(yield_today) as actual_yield
                FROM {table}
                WHERE timestamp >= CURRENT_DATE - INTERVAL '{days*30} days'
                AND yield_today IS NOT NULL
                AND yield_today > 5
                GROUP BY EXTRACT(year FROM timestamp), EXTRACT(month FROM timestamp)
                ORDER BY year DESC, month DESC
                """
            elif horizon == 'yearly':
                query = f"""
                SELECT 
                    EXTRACT(year FROM timestamp) as year,
                    AVG(yield_today) as actual_yield
                FROM {table}
                WHERE timestamp >= CURRENT_DATE - INTERVAL '{days*365} days'
                AND yield_today IS NOT NULL
                AND yield_today > 5
                GROUP BY EXTRACT(year FROM timestamp)
                ORDER BY year DESC
                """
            
            df = pd.read_sql(query, conn)
            return df
            
        except Exception as e:
            logger.error(f"Failed to get actual data: {e}")
            return pd.DataFrame()
        finally:
            conn.close()
    
    def calculate_prediction_accuracy(self, system_id: int, horizon: str) -> Dict[str, float]:
        """Υπολογίζει την ακρίβεια των προβλέψεων"""
        try:
            # Get recent actual data
            actual_df = self.get_recent_actual_data(system_id, horizon, days=30)
            
            if len(actual_df) == 0:
                return {'r2': 0.0, 'mae': 999.0, 'mape': 999.0, 'samples': 0}
            
            predictions = []
            actuals = []
            
            # Generate predictions for the same periods
            for _, row in actual_df.iterrows():
                try:
                    if horizon == 'daily':
                        date = datetime.strptime(str(row['date']), '%Y-%m-%d')
                        result = self.predictor.predict_daily(date, system_id)
                    elif horizon == 'monthly':
                        result = self.predictor.predict_monthly(int(row['year']), int(row['month']), system_id)
                    elif horizon == 'yearly':
                        result = self.predictor.predict_yearly(int(row['year']), system_id)
                    
                    if 'error' not in result and result['prediction'] is not None:
                        predictions.append(result['prediction'])
                        actuals.append(row['actual_yield'])
                        
                except Exception as e:
                    logger.warning(f"Prediction failed for {horizon} {row}: {e}")
                    continue
            
            if len(predictions) < 3:
                return {'r2': 0.0, 'mae': 999.0, 'mape': 999.0, 'samples': len(predictions)}
            
            # Calculate metrics
            from sklearn.metrics import r2_score, mean_absolute_error, mean_absolute_percentage_error
            
            r2 = r2_score(actuals, predictions)
            mae = mean_absolute_error(actuals, predictions)
            mape = mean_absolute_percentage_error(actuals, predictions) * 100
            
            return {
                'r2': r2,
                'mae': mae,
                'mape': mape,
                'samples': len(predictions),
                'avg_actual': np.mean(actuals),
                'avg_predicted': np.mean(predictions),
                'bias': np.mean(predictions) - np.mean(actuals)
            }
            
        except Exception as e:
            logger.error(f"Accuracy calculation failed: {e}")
            return {'r2': 0.0, 'mae': 999.0, 'mape': 999.0, 'samples': 0}
    
    def check_model_health(self, system_id: int, horizon: str) -> Dict[str, any]:
        """Ελέγχει την υγεία ενός μοντέλου"""
        try:
            # Calculate current performance
            performance = self.calculate_prediction_accuracy(system_id, horizon)
            thresholds = self.alert_thresholds[horizon]
            
            # Determine alert level
            alert_level = "OK"
            issues = []
            
            if performance['r2'] < thresholds['critical_r2']:
                alert_level = "CRITICAL"
                issues.append(f"R² below critical threshold: {performance['r2']:.4f} < {thresholds['critical_r2']}")
            elif performance['r2'] < thresholds['warning_r2']:
                alert_level = "WARNING"
                issues.append(f"R² below warning threshold: {performance['r2']:.4f} < {thresholds['warning_r2']}")
            
            if performance['mae'] > thresholds['critical_mae']:
                alert_level = "CRITICAL"
                issues.append(f"MAE above critical threshold: {performance['mae']:.2f} > {thresholds['critical_mae']}")
            elif performance['mae'] > thresholds['warning_mae']:
                if alert_level != "CRITICAL":
                    alert_level = "WARNING"
                issues.append(f"MAE above warning threshold: {performance['mae']:.2f} > {thresholds['warning_mae']}")
            
            if performance['samples'] < 5:
                alert_level = "WARNING"
                issues.append(f"Insufficient samples for reliable assessment: {performance['samples']}")
            
            return {
                'system_id': system_id,
                'horizon': horizon,
                'alert_level': alert_level,
                'performance': performance,
                'issues': issues,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return {
                'system_id': system_id,
                'horizon': horizon,
                'alert_level': "ERROR",
                'performance': {},
                'issues': [f"Health check failed: {str(e)}"],
                'timestamp': datetime.now().isoformat()
            }
    
    def send_telegram_alert(self, alert: Dict):
        """Στέλνει alert στο Telegram"""
        if not self.telegram_config:
            return False
        
        try:
            bot_token = self.telegram_config.get('bot_token')
            chat_id = self.telegram_config.get('chat_id')
            
            if not bot_token or not chat_id:
                return False
            
            # Format message
            emoji = "🚨" if alert['alert_level'] == "CRITICAL" else "⚠️" if alert['alert_level'] == "WARNING" else "ℹ️"
            
            message = f"{emoji} Solar Prediction Model Alert\n\n"
            message += f"System: {alert['system_id']}\n"
            message += f"Horizon: {alert['horizon']}\n"
            message += f"Alert Level: {alert['alert_level']}\n"
            message += f"Time: {alert['timestamp']}\n\n"
            
            if alert['performance']:
                perf = alert['performance']
                message += f"Performance:\n"
                message += f"• R²: {perf.get('r2', 0):.4f}\n"
                message += f"• MAE: {perf.get('mae', 0):.2f} kWh\n"
                message += f"• MAPE: {perf.get('mape', 0):.1f}%\n"
                message += f"• Samples: {perf.get('samples', 0)}\n\n"
            
            if alert['issues']:
                message += "Issues:\n"
                for issue in alert['issues']:
                    message += f"• {issue}\n"
            
            # Send message
            url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
            data = {
                'chat_id': chat_id,
                'text': message,
                'parse_mode': 'HTML'
            }
            
            response = requests.post(url, data=data, timeout=10)
            
            if response.status_code == 200:
                logger.info(f"Telegram alert sent for {alert['system_id']} {alert['horizon']}")
                return True
            else:
                logger.error(f"Failed to send Telegram alert: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Telegram alert failed: {e}")
            return False
    
    def save_alert(self, alert: Dict):
        """Αποθηκεύει alert σε αρχείο"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            alert_file = self.alerts_dir / f"alert_{alert['system_id']}_{alert['horizon']}_{timestamp}.json"
            
            with open(alert_file, 'w') as f:
                json.dump(alert, f, indent=2)
            
            logger.info(f"Alert saved to {alert_file}")
            
        except Exception as e:
            logger.error(f"Failed to save alert: {e}")
    
    def run_monitoring_check(self):
        """Εκτελεί έλεγχο παρακολούθησης"""
        logger.info("Starting model performance monitoring check")
        
        try:
            # Load current models
            self.predictor.load_models()
            
            alerts = []
            
            # Check all models
            for system_id in [1, 2]:
                for horizon in ['daily', 'monthly', 'yearly']:
                    try:
                        health_check = self.check_model_health(system_id, horizon)
                        
                        logger.info(f"System {system_id} {horizon}: {health_check['alert_level']} - "
                                  f"R²={health_check['performance'].get('r2', 0):.4f}, "
                                  f"MAE={health_check['performance'].get('mae', 0):.2f}")
                        
                        # Send alerts for warnings and critical issues
                        if health_check['alert_level'] in ['WARNING', 'CRITICAL']:
                            alerts.append(health_check)
                            self.save_alert(health_check)
                            
                            # Send Telegram alert for critical issues
                            if health_check['alert_level'] == 'CRITICAL':
                                self.send_telegram_alert(health_check)
                        
                    except Exception as e:
                        logger.error(f"Error checking System {system_id} {horizon}: {e}")
                        continue
            
            # Save monitoring summary
            summary = {
                'timestamp': datetime.now().isoformat(),
                'total_models_checked': 6,
                'alerts_generated': len(alerts),
                'critical_alerts': len([a for a in alerts if a['alert_level'] == 'CRITICAL']),
                'warning_alerts': len([a for a in alerts if a['alert_level'] == 'WARNING']),
                'alerts': alerts
            }
            
            summary_file = Path("/home/<USER>/solar-prediction-project/logs/monitoring_summary.json")
            with open(summary_file, 'w') as f:
                json.dump(summary, f, indent=2)
            
            logger.info(f"Monitoring check completed. {len(alerts)} alerts generated.")
            
        except Exception as e:
            logger.error(f"Monitoring check failed: {e}")

def schedule_monitoring():
    """Προγραμματίζει την περιοδική παρακολούθηση"""
    monitor = ModelPerformanceMonitor()
    
    # Schedule monitoring every 6 hours
    schedule.every(6).hours.do(monitor.run_monitoring_check)
    
    # Schedule daily comprehensive check at 6 AM
    schedule.every().day.at("06:00").do(monitor.run_monitoring_check)
    
    logger.info("Model performance monitoring scheduled")
    logger.info("Every 6 hours + daily at 6:00 AM")
    
    while True:
        schedule.run_pending()
        time.sleep(1800)  # Check every 30 minutes

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--run-now":
        # Run immediately for testing
        monitor = ModelPerformanceMonitor()
        monitor.run_monitoring_check()
    else:
        # Start scheduled monitoring
        schedule_monitoring()
