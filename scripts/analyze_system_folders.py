#!/usr/bin/env python3
"""
Ανάλυση System1 και System2 φακέλων - Έλεγχος κάλυψης δεδομένων
"""

import pandas as pd
import os
from datetime import datetime

def analyze_system_folder(system_path, system_name):
    """Ανάλυση ενός system φακέλου"""
    print(f"\n🏠 {system_name.upper()}:")
    
    if not os.path.exists(system_path):
        print(f"   ❌ Φάκελος δεν υπάρχει: {system_path}")
        return []
    
    # Βρίσκω όλα τα Excel files
    excel_files = []
    for file in os.listdir(system_path):
        if file.endswith('.xlsx'):
            file_path = os.path.join(system_path, file)
            file_size = os.path.getsize(file_path) / 1024 / 1024  # MB
            excel_files.append({
                'path': file_path,
                'name': file,
                'size_mb': file_size
            })
    
    excel_files.sort(key=lambda x: x['name'])  # Ταξινόμηση ανά όνομα
    
    print(f"   📁 Βρέθηκαν {len(excel_files)} Excel files:")
    
    total_records = 0
    date_ranges = []
    
    for file_info in excel_files:
        print(f"\n   📄 {file_info['name']} ({file_info['size_mb']:.1f} MB)")
        
        try:
            # Διάβασμα Excel file
            df = pd.read_excel(file_info['path'], skiprows=1)
            
            print(f"      📊 Shape: {df.shape}")
            print(f"      📋 Columns: {list(df.columns)[:5]}...")  # Πρώτες 5 στήλες
            
            # Ελέγχω για timestamp column
            time_columns = [col for col in df.columns if 'time' in col.lower() or 'date' in col.lower()]
            
            if time_columns:
                time_col = time_columns[0]
                print(f"      📅 Time column: {time_col}")
                
                # Date range
                min_date = df[time_col].min()
                max_date = df[time_col].max()
                
                print(f"      📅 Date range: {min_date} to {max_date}")
                
                date_ranges.append({
                    'file': file_info['name'],
                    'start': min_date,
                    'end': max_date,
                    'records': len(df)
                })
                
                total_records += len(df)
            else:
                print(f"      ❌ Δεν βρέθηκε timestamp column")
            
            # Ελέγχω για Plant Name
            if 'Plant Name' in df.columns:
                plants = df['Plant Name'].unique()
                print(f"      🏠 Plants: {plants}")
                for plant in plants:
                    plant_count = len(df[df['Plant Name'] == plant])
                    print(f"         {plant}: {plant_count} records")
            
            # Sample data
            print(f"      📋 Sample data:")
            print(f"         {df.head(1).to_dict('records')[0] if len(df) > 0 else 'No data'}")
            
        except Exception as e:
            print(f"      ❌ Error reading file: {e}")
    
    print(f"\n   📊 ΣΥΝΟΨΗ {system_name}:")
    print(f"      📈 Συνολικά records: {total_records:,}")
    print(f"      📁 Files: {len(excel_files)}")
    
    if date_ranges:
        overall_start = min(dr['start'] for dr in date_ranges)
        overall_end = max(dr['end'] for dr in date_ranges)
        print(f"      📅 Συνολικό εύρος: {overall_start} to {overall_end}")
        
        # Ελέγχω για gaps
        print(f"      📋 Χρονολογική σειρά:")
        for dr in sorted(date_ranges, key=lambda x: x['start']):
            print(f"         📄 {dr['file']}: {dr['start']} to {dr['end']} ({dr['records']:,} records)")
    
    return date_ranges

def check_coverage_gaps(system1_ranges, system2_ranges):
    """Έλεγχος για gaps στην κάλυψη"""
    print(f"\n🔍 ΕΛΕΓΧΟΣ ΚΑΛΥΨΗΣ & GAPS:")
    
    # Στόχος: March 2024 to June 2025
    target_start = datetime(2024, 3, 1)
    target_end = datetime(2025, 6, 1)
    
    print(f"   🎯 Στόχος κάλυψης: {target_start.date()} to {target_end.date()}")
    
    for system_name, ranges in [("System 1", system1_ranges), ("System 2", system2_ranges)]:
        print(f"\n   🏠 {system_name}:")
        
        if not ranges:
            print(f"      ❌ Δεν υπάρχουν δεδομένα")
            continue
        
        # Βρίσκω το συνολικό εύρος
        actual_start = min(dr['start'] for dr in ranges)
        actual_end = max(dr['end'] for dr in ranges)
        
        print(f"      📅 Πραγματικό εύρος: {actual_start.date()} to {actual_end.date()}")
        
        # Ελέγχω αν καλύπτει τον στόχο
        if actual_start <= target_start and actual_end >= target_end:
            print(f"      ✅ ΠΛΗΡΗΣ ΚΑΛΥΨΗ του στόχου!")
        else:
            if actual_start > target_start:
                missing_start = (actual_start - target_start).days
                print(f"      ❌ Λείπουν {missing_start} ημέρες από την αρχή")
            
            if actual_end < target_end:
                missing_end = (target_end - actual_end).days
                print(f"      ❌ Λείπουν {missing_end} ημέρες από το τέλος")
        
        # Ελέγχω για gaps μεταξύ των files
        sorted_ranges = sorted(ranges, key=lambda x: x['start'])
        
        print(f"      🔍 Έλεγχος για gaps μεταξύ files:")
        for i in range(len(sorted_ranges) - 1):
            current_end = sorted_ranges[i]['end']
            next_start = sorted_ranges[i + 1]['start']
            
            gap_days = (next_start - current_end).days
            
            if gap_days > 1:  # Περισσότερο από 1 ημέρα gap
                print(f"         ⚠️  GAP: {gap_days} ημέρες μεταξύ {sorted_ranges[i]['file']} και {sorted_ranges[i+1]['file']}")
            else:
                print(f"         ✅ Συνεχόμενα: {sorted_ranges[i]['file']} → {sorted_ranges[i+1]['file']}")

def main():
    """Κύρια συνάρτηση"""
    print("🔍 ΑΝΑΛΥΣΗ SYSTEM FOLDERS - ΕΛΕΓΧΟΣ ΚΑΛΥΨΗΣ")
    print("=" * 60)
    print("Στόχος: Έλεγχος δεδομένων από March 2024 έως June 2025")
    
    # Paths των φακέλων
    system1_path = "data/raw/System1"
    system2_path = "data/raw/System2"
    
    # Ανάλυση κάθε συστήματος
    system1_ranges = analyze_system_folder(system1_path, "System 1")
    system2_ranges = analyze_system_folder(system2_path, "System 2")
    
    # Έλεγχος κάλυψης
    check_coverage_gaps(system1_ranges, system2_ranges)
    
    # Τελική σύνοψη
    print(f"\n🎯 ΤΕΛΙΚΗ ΣΥΝΟΨΗ:")
    
    total_system1 = sum(dr['records'] for dr in system1_ranges)
    total_system2 = sum(dr['records'] for dr in system2_ranges)
    
    print(f"   🏠 System 1: {total_system1:,} records από {len(system1_ranges)} files")
    print(f"   🏠 System 2: {total_system2:,} records από {len(system2_ranges)} files")
    print(f"   📊 Συνολικά: {total_system1 + total_system2:,} records")
    
    if system1_ranges and system2_ranges:
        print(f"\n✅ ΕΤΟΙΜΟΙ για import historical data!")
        print(f"   📥 Επόμενο βήμα: Import στους πίνακες solax_data και solax_data2")
    else:
        print(f"\n❌ Προβλήματα με τα δεδομένα - χρειάζεται έλεγχος")

if __name__ == "__main__":
    main()
