#!/usr/bin/env python3
"""
Seasonal Pattern Matcher - Find similar historical days for better predictions

This script implements the seasonal pattern matching approach requested by the user:
1. Find same period last year (e.g., June 2024 vs June 2025)
2. Match weather conditions (temperature, radiation, cloud cover)
3. Analyze consumption patterns by system
4. Identify temperature vs radiation impact on deviations
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from src.database.connection import DatabaseConnection
from src.utils.logging_config import setup_logging

logger = setup_logging(__name__)

class SeasonalPatternMatcher:
    """Find and analyze seasonal patterns for solar prediction"""
    
    def __init__(self, db_connection):
        self.db = db_connection
    
    def find_similar_historical_days(
        self, 
        target_date: datetime, 
        weather_conditions: Dict[str, float],
        system_id: int,
        days_window: int = 7,
        weather_tolerance: Dict[str, float] = None
    ) -> pd.DataFrame:
        """
        Find similar historical days based on date and weather conditions
        
        Args:
            target_date: Date to find similar days for
            weather_conditions: Current weather conditions
            system_id: System ID (1 or 2)
            days_window: Days around target date to search
            weather_tolerance: Tolerance for weather matching
        
        Returns:
            DataFrame with similar historical days
        """
        if weather_tolerance is None:
            weather_tolerance = {
                'temperature': 5.0,
                'ghi': 200.0,
                'cloud_cover': 20.0
            }
        
        # Calculate search window for last year
        last_year_start = target_date.replace(year=target_date.year - 1) - timedelta(days=days_window)
        last_year_end = target_date.replace(year=target_date.year - 1) + timedelta(days=days_window)
        
        table_name = 'solax_data' if system_id == 1 else 'solax_data2'
        
        query = f"""
        WITH daily_data AS (
            SELECT 
                DATE(s.timestamp) as date,
                AVG(s.ac_power) as avg_ac_power,
                MAX(s.yield_today) as daily_yield,
                AVG(s.soc) as avg_soc,
                AVG(s.bat_power) as avg_bat_power,
                AVG(w.temperature) as avg_temperature,
                AVG(w.ghi) as avg_ghi,
                AVG(w.dni) as avg_dni,
                AVG(w.dhi) as avg_dhi,
                AVG(w.cloud_cover) as avg_cloud_cover,
                COUNT(s.id) as record_count
            FROM {table_name} s
            LEFT JOIN cams_radiation_data w ON DATE_TRUNC('hour', s.timestamp) = DATE_TRUNC('hour', w.timestamp)
            WHERE DATE(s.timestamp) BETWEEN %s AND %s
            AND s.ac_power IS NOT NULL
            AND w.ghi IS NOT NULL
            GROUP BY DATE(s.timestamp)
            HAVING COUNT(s.id) >= 10  -- Ensure sufficient data points per day
        ),
        weather_filtered AS (
            SELECT *,
                ABS(avg_temperature - %s) as temp_diff,
                ABS(avg_ghi - %s) as ghi_diff,
                ABS(avg_cloud_cover - %s) as cloud_diff,
                (ABS(avg_temperature - %s) / %s + 
                 ABS(avg_ghi - %s) / %s + 
                 ABS(avg_cloud_cover - %s) / %s) as similarity_score
            FROM daily_data
            WHERE ABS(avg_temperature - %s) <= %s
            AND ABS(avg_ghi - %s) <= %s
            AND ABS(avg_cloud_cover - %s) <= %s
        )
        SELECT *
        FROM weather_filtered
        ORDER BY similarity_score ASC
        LIMIT 10
        """
        
        params = [
            last_year_start.date(), last_year_end.date(),
            weather_conditions['temperature'], weather_conditions['ghi'], weather_conditions['cloud_cover'],
            weather_conditions['temperature'], weather_tolerance['temperature'],
            weather_conditions['ghi'], weather_tolerance['ghi'],
            weather_conditions['cloud_cover'], weather_tolerance['cloud_cover'],
            weather_conditions['temperature'], weather_tolerance['temperature'],
            weather_conditions['ghi'], weather_tolerance['ghi'],
            weather_conditions['cloud_cover'], weather_tolerance['cloud_cover']
        ]
        
        df = pd.read_sql(query, self.db.get_connection(), params=params)
        logger.info(f"Found {len(df)} similar historical days for System {system_id}")
        
        return df
    
    def analyze_seasonal_patterns(self, start_date: datetime, end_date: datetime) -> Dict[str, pd.DataFrame]:
        """
        Analyze seasonal patterns for both systems
        
        Args:
            start_date: Start date for analysis
            end_date: End date for analysis
            
        Returns:
            Dictionary with analysis results for both systems
        """
        logger.info(f"Analyzing seasonal patterns from {start_date.date()} to {end_date.date()}")
        
        results = {}
        
        for system_id in [1, 2]:
            table_name = 'solax_data' if system_id == 1 else 'solax_data2'
            system_name = 'Σπίτι Πάνω' if system_id == 1 else 'Σπίτι Κάτω'
            
            query = f"""
            WITH daily_stats AS (
                SELECT 
                    DATE(s.timestamp) as date,
                    EXTRACT(month FROM s.timestamp) as month,
                    EXTRACT(day FROM s.timestamp) as day,
                    EXTRACT(year FROM s.timestamp) as year,
                    MAX(s.yield_today) as daily_yield,
                    AVG(s.ac_power) as avg_ac_power,
                    AVG(s.soc) as avg_soc,
                    AVG(s.bat_power) as avg_bat_power,
                    AVG(w.temperature) as avg_temperature,
                    AVG(w.ghi) as avg_ghi,
                    AVG(w.dni) as avg_dni,
                    AVG(w.cloud_cover) as avg_cloud_cover,
                    CASE 
                        WHEN AVG(w.cloud_cover) < 30 THEN 'sunny'
                        WHEN AVG(w.cloud_cover) < 70 THEN 'partly_cloudy'
                        ELSE 'cloudy'
                    END as weather_category,
                    CASE 
                        WHEN AVG(w.temperature) < 15 THEN 'cold'
                        WHEN AVG(w.temperature) < 25 THEN 'mild'
                        ELSE 'hot'
                    END as temperature_category
                FROM {table_name} s
                LEFT JOIN cams_radiation_data w ON DATE_TRUNC('hour', s.timestamp) = DATE_TRUNC('hour', w.timestamp)
                WHERE DATE(s.timestamp) BETWEEN %s AND %s
                AND s.ac_power IS NOT NULL
                AND w.ghi IS NOT NULL
                GROUP BY DATE(s.timestamp), EXTRACT(month FROM s.timestamp), 
                         EXTRACT(day FROM s.timestamp), EXTRACT(year FROM s.timestamp)
                HAVING COUNT(s.id) >= 5
            )
            SELECT *
            FROM daily_stats
            ORDER BY date
            """
            
            df = pd.read_sql(query, self.db.get_connection(), params=[start_date.date(), end_date.date()])
            
            if len(df) > 0:
                logger.info(f"System {system_id} ({system_name}): {len(df)} days analyzed")
                results[f'system_{system_id}'] = df
            else:
                logger.warning(f"No data found for System {system_id}")
        
        return results
    
    def compare_temperature_vs_radiation_impact(self, seasonal_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """
        Compare temperature vs radiation impact on production
        
        Args:
            seasonal_data: Seasonal analysis data from analyze_seasonal_patterns
            
        Returns:
            Analysis results comparing temperature vs radiation impact
        """
        logger.info("Analyzing temperature vs radiation impact...")
        
        results = {}
        
        for system_key, df in seasonal_data.items():
            if len(df) < 10:
                logger.warning(f"Insufficient data for {system_key}")
                continue
            
            # Correlation analysis
            correlations = df[['daily_yield', 'avg_temperature', 'avg_ghi', 'avg_dni', 'avg_cloud_cover']].corr()['daily_yield']
            
            # Weather category analysis
            weather_analysis = df.groupby('weather_category').agg({
                'daily_yield': ['mean', 'std', 'count'],
                'avg_temperature': 'mean',
                'avg_ghi': 'mean',
                'avg_cloud_cover': 'mean'
            }).round(2)
            
            # Temperature category analysis
            temp_analysis = df.groupby('temperature_category').agg({
                'daily_yield': ['mean', 'std', 'count'],
                'avg_temperature': 'mean',
                'avg_ghi': 'mean',
                'avg_cloud_cover': 'mean'
            }).round(2)
            
            # Combined weather + temperature analysis
            combined_analysis = df.groupby(['weather_category', 'temperature_category']).agg({
                'daily_yield': ['mean', 'count'],
                'avg_temperature': 'mean',
                'avg_ghi': 'mean'
            }).round(2)
            
            results[system_key] = {
                'correlations': correlations.to_dict(),
                'weather_analysis': weather_analysis,
                'temperature_analysis': temp_analysis,
                'combined_analysis': combined_analysis,
                'total_days': len(df)
            }
            
            # Log key insights
            logger.info(f"{system_key} correlations:")
            logger.info(f"  Temperature: {correlations['avg_temperature']:.3f}")
            logger.info(f"  GHI: {correlations['avg_ghi']:.3f}")
            logger.info(f"  Cloud Cover: {correlations['avg_cloud_cover']:.3f}")
        
        return results
    
    def predict_using_seasonal_patterns(
        self, 
        target_date: datetime, 
        weather_forecast: Dict[str, float],
        system_id: int
    ) -> Dict[str, Any]:
        """
        Make prediction using seasonal pattern matching
        
        Args:
            target_date: Date to predict for
            weather_forecast: Weather forecast for target date
            system_id: System ID (1 or 2)
            
        Returns:
            Prediction results with confidence metrics
        """
        logger.info(f"Making seasonal pattern prediction for System {system_id} on {target_date.date()}")
        
        # Find similar historical days
        similar_days = self.find_similar_historical_days(target_date, weather_forecast, system_id)
        
        if len(similar_days) == 0:
            logger.warning("No similar historical days found")
            return {
                'predicted_yield': None,
                'confidence': 0.0,
                'similar_days_count': 0,
                'method': 'seasonal_pattern_matching'
            }
        
        # Calculate weighted prediction based on similarity
        weights = 1 / (similar_days['similarity_score'] + 0.1)  # Add small value to avoid division by zero
        weights = weights / weights.sum()  # Normalize weights
        
        predicted_yield = (similar_days['daily_yield'] * weights).sum()
        
        # Calculate confidence based on consistency of similar days
        yield_std = similar_days['daily_yield'].std()
        yield_mean = similar_days['daily_yield'].mean()
        confidence = max(0.0, min(1.0, 1.0 - (yield_std / yield_mean) if yield_mean > 0 else 0.0))
        
        # Additional metrics
        weather_consistency = {
            'temperature_std': similar_days['avg_temperature'].std(),
            'ghi_std': similar_days['avg_ghi'].std(),
            'cloud_cover_std': similar_days['avg_cloud_cover'].std()
        }
        
        result = {
            'predicted_yield': float(predicted_yield),
            'confidence': float(confidence),
            'similar_days_count': len(similar_days),
            'similar_days_yield_range': [float(similar_days['daily_yield'].min()), float(similar_days['daily_yield'].max())],
            'weather_consistency': weather_consistency,
            'method': 'seasonal_pattern_matching',
            'similar_days': similar_days.to_dict('records')
        }
        
        logger.info(f"Prediction: {predicted_yield:.1f} kWh (confidence: {confidence:.2f})")
        
        return result

def main():
    """Main function for testing seasonal pattern matching"""
    logger.info("🔍 Testing Seasonal Pattern Matcher")
    
    try:
        # Initialize
        db = DatabaseConnection()
        matcher = SeasonalPatternMatcher(db)
        
        # Test 1: Analyze seasonal patterns for last 3 months
        logger.info("Test 1: Analyzing seasonal patterns...")
        end_date = datetime.now()
        start_date = end_date - timedelta(days=90)
        
        seasonal_data = matcher.analyze_seasonal_patterns(start_date, end_date)
        
        # Test 2: Compare temperature vs radiation impact
        logger.info("Test 2: Comparing temperature vs radiation impact...")
        impact_analysis = matcher.compare_temperature_vs_radiation_impact(seasonal_data)
        
        # Test 3: Make prediction using seasonal patterns
        logger.info("Test 3: Making seasonal pattern prediction...")
        target_date = datetime.now() + timedelta(days=1)
        weather_forecast = {
            'temperature': 25.0,
            'ghi': 600.0,
            'cloud_cover': 30.0
        }
        
        for system_id in [1, 2]:
            prediction = matcher.predict_using_seasonal_patterns(target_date, weather_forecast, system_id)
            logger.info(f"System {system_id} prediction: {prediction}")
        
        logger.info("✅ Seasonal Pattern Matcher testing completed!")
        
    except Exception as e:
        logger.error(f"❌ Testing failed: {e}")
        raise

if __name__ == "__main__":
    main()
