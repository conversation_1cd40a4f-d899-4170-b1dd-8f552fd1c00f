#!/usr/bin/env python3
"""
Cleanup Unused API Sources
Remove mining and crypto APIs that are not used by Solar Prediction System
"""

import psycopg2
from datetime import datetime
import sys

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'user': 'postgres',
    'password': 'postgres',
    'database': 'solar_prediction'
}

def analyze_current_apis():
    """Analyze current API sources"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()

        # Get all API sources
        cursor.execute("""
            SELECT id, name, category, enabled, endpoint, last_status
            FROM api_sources
            ORDER BY category, id;
        """)

        all_apis = cursor.fetchall()
        conn.close()

        print("📡 CURRENT API SOURCES ANALYSIS")
        print("=" * 50)
        print(f"Total API Sources: {len(all_apis)}")
        print()

        # Categorize APIs
        solar_apis = []
        unused_apis = []

        for api in all_apis:
            id, name, category, enabled, endpoint, status = api

            # Solar/Weather APIs that we keep
            if name in ['solax_cloud', 'open_meteo_historical', 'open_meteo_forecast', 'cams_radiation']:
                solar_apis.append(api)
            else:
                unused_apis.append(api)

        print("✅ SOLAR/WEATHER APIs (KEEP):")
        for api in solar_apis:
            id, name, category, enabled, endpoint, status = api
            print(f"   {id:2d}. {name} ({category})")

        print()
        print("🗑️  UNUSED APIs (DELETE):")
        for api in unused_apis:
            id, name, category, enabled, endpoint, status = api
            print(f"   {id:2d}. {name} ({category})")

        print()
        print(f"📊 Summary: Keep {len(solar_apis)}, Delete {len(unused_apis)}")

        return solar_apis, unused_apis

    except Exception as e:
        print(f"❌ Error analyzing APIs: {e}")
        return [], []

def check_dependencies():
    """Check if unused APIs have any data dependencies"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()

        print("🔍 CHECKING DATA DEPENDENCIES")
        print("-" * 40)

        # Check api_data table for references
        cursor.execute("""
            SELECT api_source_id, COUNT(*) as record_count
            FROM api_data
            GROUP BY api_source_id
            ORDER BY api_source_id;
        """)

        api_data_refs = cursor.fetchall()

        if api_data_refs:
            print("📊 API Data References:")
            for api_id, count in api_data_refs:
                cursor.execute("SELECT name FROM api_sources WHERE id = %s;", (api_id,))
                name_result = cursor.fetchone()
                name = name_result[0] if name_result else f"ID-{api_id}"
                print(f"   API {api_id} ({name}): {count:,} records")
        else:
            print("✅ No data dependencies found in api_data table")

        conn.close()
        return api_data_refs

    except Exception as e:
        print(f"❌ Error checking dependencies: {e}")
        return []

def delete_unused_apis(unused_apis, confirm=True):
    """Delete unused API sources"""
    if not unused_apis:
        print("ℹ️  No APIs to delete")
        return True

    print("🗑️  DELETING UNUSED API SOURCES")
    print("-" * 40)

    if confirm:
        print("⚠️  WARNING: This will permanently delete the following APIs:")
        for api in unused_apis:
            id, name, category, enabled, endpoint, status = api
            print(f"   • {name} ({category})")

        print()
        try:
            response = input("Are you sure you want to delete these APIs? (yes/no): ").strip().lower()
            if response not in ['yes', 'y']:
                print("⏹️  Deletion cancelled")
                return False
        except KeyboardInterrupt:
            print("\n⏹️  Deletion cancelled by user")
            return False

    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()

        deleted_count = 0

        for api in unused_apis:
            id, name, category, enabled, endpoint, status = api

            try:
                # First, delete any related data in api_data table
                cursor.execute("DELETE FROM api_data WHERE api_source_id = %s;", (id,))
                deleted_data = cursor.rowcount

                # Then delete from api_sources
                cursor.execute("DELETE FROM api_sources WHERE id = %s;", (id,))

                if deleted_data > 0:
                    print(f"✅ Deleted: {name} (ID: {id}) + {deleted_data:,} data records")
                else:
                    print(f"✅ Deleted: {name} (ID: {id})")
                deleted_count += 1

            except Exception as e:
                print(f"❌ Failed to delete {name}: {e}")
                # Rollback this transaction and continue
                conn.rollback()

        conn.commit()
        conn.close()

        print()
        print(f"🎉 Successfully deleted {deleted_count} unused API sources")
        return True

    except Exception as e:
        print(f"❌ Error during deletion: {e}")
        return False

def verify_cleanup():
    """Verify cleanup was successful"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()

        print("✅ VERIFYING CLEANUP")
        print("-" * 30)

        # Check remaining APIs
        cursor.execute("""
            SELECT id, name, category
            FROM api_sources
            ORDER BY category, id;
        """)

        remaining_apis = cursor.fetchall()

        print(f"📊 Remaining API Sources: {len(remaining_apis)}")

        for api in remaining_apis:
            id, name, category = api
            print(f"   {id:2d}. {name} ({category})")

        # Verify only solar APIs remain
        expected_apis = ['solax_cloud', 'open_meteo_historical', 'open_meteo_forecast', 'cams_radiation']
        remaining_names = [api[1] for api in remaining_apis]

        print()
        if all(name in remaining_names for name in expected_apis):
            print("✅ All required Solar/Weather APIs are present")
        else:
            missing = [name for name in expected_apis if name not in remaining_names]
            print(f"⚠️  Missing required APIs: {missing}")

        unexpected = [name for name in remaining_names if name not in expected_apis]
        if unexpected:
            print(f"⚠️  Unexpected APIs still present: {unexpected}")
        else:
            print("✅ No unexpected APIs remaining")

        conn.close()
        return len(remaining_apis) == 4  # Should have exactly 4 solar APIs

    except Exception as e:
        print(f"❌ Error verifying cleanup: {e}")
        return False

def show_data_storage_mapping():
    """Show where each API stores its data"""
    print("📊 API DATA STORAGE MAPPING")
    print("=" * 50)

    mapping = {
        'solax_cloud': {
            'table': 'solax_data',
            'description': 'Real-time SolaX inverter data',
            'key_fields': 'ac_power, soc, bat_power, yield_today'
        },
        'open_meteo_historical': {
            'table': 'weather_data',
            'description': 'Historical weather data',
            'key_fields': 'direct_radiation, temperature_2m, cloud_cover'
        },
        'open_meteo_forecast': {
            'table': 'weather_data',
            'description': 'Weather forecast data',
            'key_fields': 'direct_radiation, temperature_2m, cloud_cover (is_forecast=true)'
        },
        'cams_radiation': {
            'table': 'cams_radiation_data',
            'description': 'CAMS radiation data (currently unused)',
            'key_fields': 'ghi, dni, dhi, temperature'
        }
    }

    for api_name, info in mapping.items():
        print(f"📡 {api_name}:")
        print(f"   → Table: {info['table']}")
        print(f"   → Description: {info['description']}")
        print(f"   → Key Fields: {info['key_fields']}")
        print()

def main():
    """Main cleanup function"""
    print("🧹 SOLAR PREDICTION SYSTEM - API CLEANUP")
    print("=" * 60)
    print(f"🕐 Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    try:
        # Show data storage mapping first
        show_data_storage_mapping()

        # Analyze current APIs
        solar_apis, unused_apis = analyze_current_apis()

        if not solar_apis and not unused_apis:
            print("❌ Failed to analyze APIs")
            return

        print()

        # Check dependencies
        dependencies = check_dependencies()

        print()

        # Delete unused APIs
        if unused_apis:
            success = delete_unused_apis(unused_apis, confirm=False)  # Auto-confirm for now

            if success:
                print()
                verify_cleanup()

                print()
                print("🎉 API CLEANUP COMPLETED SUCCESSFULLY!")
                print()
                print("📊 Final Status:")
                print("   ✅ Kept 4 Solar/Weather APIs")
                print("   🗑️  Removed 13 Mining/Crypto APIs")
                print("   🎯 Clean, focused API configuration")
            else:
                print("❌ API cleanup failed")
        else:
            print("ℹ️  No unused APIs found - system is already clean")

    except KeyboardInterrupt:
        print("\n⏹️  Cleanup cancelled by user")
    except Exception as e:
        print(f"\n❌ Cleanup failed: {e}")

if __name__ == "__main__":
    main()
