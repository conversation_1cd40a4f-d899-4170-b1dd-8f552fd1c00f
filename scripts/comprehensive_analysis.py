#!/usr/bin/env python3
"""
ΠΛΗΡΗΣ ΑΝΑΛΥΣΗ ΣΥΣΤΗΜΑΤΟΣ ΠΡΟΒΛΕΨΗΣ
Ελέγχει ιστορικά δεδομένα, τρέχουσες προβλέψεις και βρίσκει ασυνέπειες
"""

import psycopg2
import pandas as pd
import requests
import json
from datetime import datetime, <PERSON><PERSON><PERSON>

def analyze_historical_data():
    """Ανάλυση ιστορικών δεδομένων"""
    print("📊 ΑΝΑΛΥΣΗ ΙΣΤΟΡΙΚΩΝ ΔΕΔΟΜΕΝΩΝ")
    print("=" * 60)
    
    try:
        conn = psycopg2.connect('postgresql://postgres:postgres@localhost/solar_prediction')
        
        # 1. Εύρος δεδομένων
        print("\n🗓️ ΕΥΡΟΣ ΔΕΔΟΜΕΝΩΝ:")
        query = "SELECT MIN(timestamp), MAX(timestamp), COUNT(*) FROM solax_data"
        result = pd.read_sql(query, conn)
        print(f"   System 1: {result.iloc[0]['min']} έως {result.iloc[0]['max']}")
        print(f"   Συνολικά records: {result.iloc[0]['count']:,}")
        
        # 2. Μέγιστες τιμές ισχύος
        print("\n⚡ ΜΕΓΙΣΤΕΣ ΤΙΜΕΣ ΙΣΧΥΟΣ:")
        query = "SELECT MAX(ac_power) as max_power FROM solax_data WHERE ac_power > 0"
        result = pd.read_sql(query, conn)
        print(f"   System 1 Max Power: {result.iloc[0]['max_power']:.0f}W")
        
        query = "SELECT MAX(ac_power) as max_power FROM solax_data2 WHERE ac_power > 0"
        result = pd.read_sql(query, conn)
        print(f"   System 2 Max Power: {result.iloc[0]['max_power']:.0f}W")
        
        # 3. Ημερήσιες παραγωγές τελευταίων ημερών
        print("\n📈 ΤΕΛΕΥΤΑΙΕΣ ΗΜΕΡΗΣΙΕΣ ΠΑΡΑΓΩΓΕΣ:")
        
        # System 1
        query = """
        SELECT 
            DATE(timestamp) as date,
            COUNT(*) as records,
            MAX(ac_power) as max_power,
            SUM(CASE WHEN ac_power > 0 THEN ac_power * 5.0/60/1000 ELSE 0 END) as daily_kwh
        FROM solax_data 
        WHERE timestamp >= CURRENT_DATE - INTERVAL '7 days'
        GROUP BY DATE(timestamp)
        ORDER BY date DESC
        LIMIT 5
        """
        
        df1 = pd.read_sql(query, conn)
        print(f"\n   SYSTEM 1:")
        for _, row in df1.iterrows():
            print(f"   {row['date']}: {row['daily_kwh']:.1f} kWh (max: {row['max_power']:.0f}W, records: {row['records']})")
        
        # System 2
        query = """
        SELECT 
            DATE(timestamp) as date,
            COUNT(*) as records,
            MAX(ac_power) as max_power,
            SUM(CASE WHEN ac_power > 0 THEN ac_power * 5.0/60/1000 ELSE 0 END) as daily_kwh
        FROM solax_data2 
        WHERE timestamp >= CURRENT_DATE - INTERVAL '7 days'
        GROUP BY DATE(timestamp)
        ORDER BY date DESC
        LIMIT 5
        """
        
        df2 = pd.read_sql(query, conn)
        print(f"\n   SYSTEM 2:")
        for _, row in df2.iterrows():
            print(f"   {row['date']}: {row['daily_kwh']:.1f} kWh (max: {row['max_power']:.0f}W, records: {row['records']})")
        
        conn.close()
        return df1, df2
        
    except Exception as e:
        print(f"❌ Database Error: {e}")
        return None, None

def analyze_current_predictions():
    """Ανάλυση τρεχουσών προβλέψεων"""
    print("\n🔮 ΤΡΕΧΟΥΣΕΣ ΠΡΟΒΛΕΨΕΙΣ")
    print("=" * 60)
    
    try:
        # System 1 προβλέψεις
        response1 = requests.get('http://localhost:8100/api/v1/forecast/72h/system1', timeout=15)
        if response1.status_code == 200:
            data1 = response1.json()
            print(f"\n📊 SYSTEM 1 ΠΡΟΒΛΕΨΕΙΣ:")
            print(f"   Σήμερα: {data1['daily_summaries']['day_1']['total_kwh']:.1f} kWh")
            print(f"   Αύριο: {data1['daily_summaries']['day_2']['total_kwh']:.1f} kWh")
            print(f"   Μεθαύριο: {data1['daily_summaries']['day_3']['total_kwh']:.1f} kWh")
            
            # Ωριαίες προβλέψεις σήμερα
            print(f"\n   ΩΡΙΑΙΕΣ ΠΡΟΒΛΕΨΕΙΣ ΣΗΜΕΡΑ:")
            hourly = data1['daily_summaries']['day_1']['hourly_breakdown']
            for i, power in enumerate(hourly):
                if power > 0:
                    print(f"   {i:02d}:00 → {power:.0f}W")
        else:
            print(f"❌ System 1 API Error: {response1.status_code}")
            return None, None
        
        # System 2 προβλέψεις
        response2 = requests.get('http://localhost:8100/api/v1/forecast/72h/system2', timeout=15)
        if response2.status_code == 200:
            data2 = response2.json()
            print(f"\n📊 SYSTEM 2 ΠΡΟΒΛΕΨΕΙΣ:")
            print(f"   Σήμερα: {data2['daily_summaries']['day_1']['total_kwh']:.1f} kWh")
            print(f"   Αύριο: {data2['daily_summaries']['day_2']['total_kwh']:.1f} kWh")
            print(f"   Μεθαύριο: {data2['daily_summaries']['day_3']['total_kwh']:.1f} kWh")
        else:
            print(f"❌ System 2 API Error: {response2.status_code}")
            return None, None
        
        # Συνολικές προβλέψεις
        total_today = data1['daily_summaries']['day_1']['total_kwh'] + data2['daily_summaries']['day_1']['total_kwh']
        total_tomorrow = data1['daily_summaries']['day_2']['total_kwh'] + data2['daily_summaries']['day_2']['total_kwh']
        total_day3 = data1['daily_summaries']['day_3']['total_kwh'] + data2['daily_summaries']['day_3']['total_kwh']
        
        print(f"\n🔮 ΣΥΝΟΛΙΚΕΣ ΠΡΟΒΛΕΨΕΙΣ:")
        print(f"   Σήμερα: {total_today:.1f} kWh")
        print(f"   Αύριο: {total_tomorrow:.1f} kWh")
        print(f"   Μεθαύριο: {total_day3:.1f} kWh")
        
        return data1, data2
        
    except Exception as e:
        print(f"❌ API Error: {e}")
        return None, None

def analyze_seasonal_comparison():
    """Σύγκριση εποχιακών δεδομένων"""
    print("\n🗓️ ΕΠΟΧΙΑΚΗ ΣΥΓΚΡΙΣΗ")
    print("=" * 60)
    
    try:
        conn = psycopg2.connect('postgresql://postgres:postgres@localhost/solar_prediction')
        
        # Ιούνιος 2024 vs 2025
        print(f"\n📊 ΣΥΓΚΡΙΣΗ ΙΟΥΝΙΟΥ:")
        
        # 2024 data
        query_2024 = """
        SELECT 
            AVG(CASE WHEN ac_power > 0 THEN ac_power ELSE NULL END) as avg_power,
            MAX(ac_power) as max_power,
            COUNT(CASE WHEN ac_power > 0 THEN 1 END) as active_records
        FROM solax_data 
        WHERE timestamp >= '2024-06-01' AND timestamp < '2024-07-01'
        """
        
        result_2024 = pd.read_sql(query_2024, conn)
        if result_2024.iloc[0]['active_records'] > 0:
            print(f"   2024 System 1: Μέσος όρος {result_2024.iloc[0]['avg_power']:.0f}W, Max {result_2024.iloc[0]['max_power']:.0f}W")
        else:
            print(f"   2024 System 1: Δεν υπάρχουν δεδομένα")
        
        # 2025 data
        query_2025 = """
        SELECT 
            AVG(CASE WHEN ac_power > 0 THEN ac_power ELSE NULL END) as avg_power,
            MAX(ac_power) as max_power,
            COUNT(CASE WHEN ac_power > 0 THEN 1 END) as active_records
        FROM solax_data 
        WHERE timestamp >= '2025-06-01' AND timestamp < '2025-07-01'
        """
        
        result_2025 = pd.read_sql(query_2025, conn)
        if result_2025.iloc[0]['active_records'] > 0:
            print(f"   2025 System 1: Μέσος όρος {result_2025.iloc[0]['avg_power']:.0f}W, Max {result_2025.iloc[0]['max_power']:.0f}W")
        else:
            print(f"   2025 System 1: Δεν υπάρχουν δεδομένα")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Seasonal Analysis Error: {e}")

def main():
    """Κύρια συνάρτηση ανάλυσης"""
    print("🔍 ΠΛΗΡΗΣ ΑΝΑΛΥΣΗ ΣΥΣΤΗΜΑΤΟΣ ΠΡΟΒΛΕΨΗΣ")
    print("=" * 80)
    
    # 1. Ιστορικά δεδομένα
    hist_data1, hist_data2 = analyze_historical_data()
    
    # 2. Τρέχουσες προβλέψεις
    pred_data1, pred_data2 = analyze_current_predictions()
    
    # 3. Εποχιακή σύγκριση
    analyze_seasonal_comparison()
    
    # 4. Σύνοψη και ανάλυση ασυνεπειών
    print("\n🎯 ΑΝΑΛΥΣΗ ΑΣΥΝΕΠΕΙΩΝ")
    print("=" * 60)
    
    if hist_data1 is not None and pred_data1 is not None:
        # Σύγκριση πρόσφατων ιστορικών με προβλέψεις
        if len(hist_data1) > 0:
            recent_avg1 = hist_data1['daily_kwh'].mean()
            predicted1 = pred_data1['daily_summaries']['day_1']['total_kwh']
            
            print(f"\n📊 SYSTEM 1:")
            print(f"   Πρόσφατος μέσος όρος: {recent_avg1:.1f} kWh/ημέρα")
            print(f"   Πρόβλεψη σήμερα: {predicted1:.1f} kWh")
            print(f"   Διαφορά: {predicted1 - recent_avg1:.1f} kWh ({((predicted1/recent_avg1-1)*100):.1f}%)")
        
        if len(hist_data2) > 0:
            recent_avg2 = hist_data2['daily_kwh'].mean()
            predicted2 = pred_data2['daily_summaries']['day_1']['total_kwh']
            
            print(f"\n📊 SYSTEM 2:")
            print(f"   Πρόσφατος μέσος όρος: {recent_avg2:.1f} kWh/ημέρα")
            print(f"   Πρόβλεψη σήμερα: {predicted2:.1f} kWh")
            print(f"   Διαφορά: {predicted2 - recent_avg2:.1f} kWh ({((predicted2/recent_avg2-1)*100):.1f}%)")
    
    print(f"\n✅ Ανάλυση ολοκληρώθηκε!")

if __name__ == "__main__":
    main()
