#!/usr/bin/env python3
"""
ML Training Scheduler for Docker Container
Schedules weekly ML model training with database storage
"""

import os
import sys
import time
import logging
import schedule
import threading
from datetime import datetime, timedelta
from pathlib import Path

# Add project root to Python path
sys.path.append('/app')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MLTrainingScheduler:
    """Scheduler for ML model training"""
    
    def __init__(self):
        self.running = False
        self.last_training = None
        self.next_training = None
        
    def train_models(self):
        """Execute ML model training"""
        logger.info("🤖 Starting scheduled ML model training...")
        
        try:
            # Import and run the database training script
            import subprocess
            
            result = subprocess.run([
                'python3', '/app/scripts/training/train_all_models_unified_database.py'
            ], capture_output=True, text=True, timeout=3600)  # 1 hour timeout
            
            if result.returncode == 0:
                logger.info("✅ ML model training completed successfully")
                logger.info(f"Training output: {result.stdout[-500:]}")  # Last 500 chars
                self.last_training = datetime.now()
            else:
                logger.error(f"❌ ML model training failed: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            logger.error("❌ ML model training timed out after 1 hour")
        except Exception as e:
            logger.error(f"❌ ML model training error: {e}")
    
    def schedule_training(self):
        """Schedule ML training for Monday at 2:00 AM"""
        # Schedule weekly training on Monday at 2:00 AM
        schedule.every().monday.at("02:00").do(self.train_models)
        
        # For testing, also schedule daily at 3:00 AM (can be removed in production)
        # schedule.every().day.at("03:00").do(self.train_models)
        
        logger.info("📅 ML training scheduled for Monday at 2:00 AM")
        
        # Calculate next run time
        next_run = schedule.next_run()
        if next_run:
            self.next_training = next_run
            logger.info(f"⏰ Next ML training: {next_run}")
    
    def run_scheduler(self):
        """Run the scheduler loop"""
        self.running = True
        logger.info("🚀 ML Training Scheduler started")
        
        while self.running:
            try:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
            except Exception as e:
                logger.error(f"❌ Scheduler error: {e}")
                time.sleep(60)
    
    def start(self):
        """Start the scheduler in a separate thread"""
        self.schedule_training()
        
        scheduler_thread = threading.Thread(target=self.run_scheduler, daemon=True)
        scheduler_thread.start()
        
        return scheduler_thread
    
    def stop(self):
        """Stop the scheduler"""
        self.running = False
        logger.info("🛑 ML Training Scheduler stopped")
    
    def get_status(self):
        """Get scheduler status"""
        return {
            "running": self.running,
            "last_training": self.last_training.isoformat() if self.last_training else None,
            "next_training": self.next_training.isoformat() if self.next_training else None,
            "scheduled_jobs": len(schedule.jobs)
        }
    
    def force_training(self):
        """Force immediate training (for testing)"""
        logger.info("🔧 Forcing immediate ML model training...")
        threading.Thread(target=self.train_models, daemon=True).start()

def main():
    """Main function for standalone execution"""
    logger.info("🤖 Starting ML Training Scheduler Service")
    
    scheduler = MLTrainingScheduler()
    
    try:
        # Start scheduler
        scheduler.start()
        
        # Keep the main thread alive
        while True:
            time.sleep(10)
            
    except KeyboardInterrupt:
        logger.info("🛑 Received interrupt signal")
        scheduler.stop()
    except Exception as e:
        logger.error(f"❌ Scheduler service error: {e}")
        scheduler.stop()

if __name__ == "__main__":
    main()
