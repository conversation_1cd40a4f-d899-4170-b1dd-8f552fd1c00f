#!/usr/bin/env python3
"""
Standalone ML Training Scheduler
Simple scheduler that runs ML training weekly without database dependencies
"""

import os
import sys
import time
import logging
import subprocess
import threading
from datetime import datetime, timedelta
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class StandaloneMLScheduler:
    """Simple ML training scheduler"""
    
    def __init__(self):
        self.running = False
        self.last_training = None
        self.next_training = None
        self.training_day = 1  # Monday (0=Monday, 6=Sunday)
        self.training_hour = 2  # 2:00 AM
        self.training_minute = 0
        
        # Load state from file if exists
        self.state_file = '/tmp/ml_scheduler_state.json'
        self.load_state()
        
    def load_state(self):
        """Load scheduler state from file"""
        try:
            if os.path.exists(self.state_file):
                with open(self.state_file, 'r') as f:
                    state = json.load(f)
                    if state.get('last_training'):
                        self.last_training = datetime.fromisoformat(state['last_training'])
                    if state.get('next_training'):
                        self.next_training = datetime.fromisoformat(state['next_training'])
                logger.info(f"📁 Loaded state: last={self.last_training}, next={self.next_training}")
        except Exception as e:
            logger.warning(f"⚠️ Could not load state: {e}")
    
    def save_state(self):
        """Save scheduler state to file"""
        try:
            state = {
                'last_training': self.last_training.isoformat() if self.last_training else None,
                'next_training': self.next_training.isoformat() if self.next_training else None
            }
            with open(self.state_file, 'w') as f:
                json.dump(state, f)
        except Exception as e:
            logger.warning(f"⚠️ Could not save state: {e}")
    
    def calculate_next_training(self):
        """Calculate next training time (Monday at 2:00 AM)"""
        now = datetime.now()
        
        # Find next Monday at 2:00 AM
        days_ahead = self.training_day - now.weekday()
        if days_ahead <= 0:  # Target day already happened this week
            days_ahead += 7
        
        next_training = now + timedelta(days=days_ahead)
        next_training = next_training.replace(
            hour=self.training_hour, 
            minute=self.training_minute, 
            second=0, 
            microsecond=0
        )
        
        # If it's Monday and before 2:00 AM, schedule for today
        if now.weekday() == self.training_day and now.hour < self.training_hour:
            next_training = now.replace(
                hour=self.training_hour, 
                minute=self.training_minute, 
                second=0, 
                microsecond=0
            )
        
        self.next_training = next_training
        self.save_state()
        logger.info(f"⏰ Next ML training scheduled for: {next_training}")
        
        return next_training
    
    def should_run_training(self):
        """Check if it's time to run training"""
        if not self.next_training:
            self.calculate_next_training()
            return False
        
        now = datetime.now()
        
        # Check if it's time to train (within 5 minutes of scheduled time)
        if now >= self.next_training and now <= self.next_training + timedelta(minutes=5):
            return True
        
        # If we missed the window, reschedule
        if now > self.next_training + timedelta(minutes=5):
            logger.warning(f"⚠️ Missed training window, rescheduling...")
            self.calculate_next_training()
        
        return False
    
    def run_training(self):
        """Execute ML model training"""
        logger.info("🤖 Starting ML model training...")
        
        try:
            # Run the database training script
            result = subprocess.run([
                'python3', '/app/scripts/training/train_all_models_unified_database.py'
            ], capture_output=True, text=True, timeout=3600)  # 1 hour timeout
            
            if result.returncode == 0:
                logger.info("✅ ML model training completed successfully")
                logger.info("📊 Training output (last 500 chars):")
                logger.info(result.stdout[-500:])
                
                self.last_training = datetime.now()
                self.calculate_next_training()  # Schedule next training
                self.save_state()
                
                return True
            else:
                logger.error(f"❌ ML model training failed with return code {result.returncode}")
                logger.error(f"Error output: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("❌ ML model training timed out after 1 hour")
            return False
        except Exception as e:
            logger.error(f"❌ ML model training error: {e}")
            return False
    
    def run_scheduler_loop(self):
        """Main scheduler loop"""
        self.running = True
        logger.info("🚀 Standalone ML Scheduler started")
        
        # Calculate initial next training time
        if not self.next_training:
            self.calculate_next_training()
        
        while self.running:
            try:
                if self.should_run_training():
                    logger.info("⏰ Time for ML training!")
                    self.run_training()
                
                # Sleep for 1 minute before checking again
                time.sleep(60)
                
            except Exception as e:
                logger.error(f"❌ Scheduler loop error: {e}")
                time.sleep(60)
    
    def start(self):
        """Start the scheduler in a separate thread"""
        scheduler_thread = threading.Thread(target=self.run_scheduler_loop, daemon=True)
        scheduler_thread.start()
        logger.info("📅 ML Training Scheduler started in background")
        return scheduler_thread
    
    def stop(self):
        """Stop the scheduler"""
        self.running = False
        logger.info("🛑 ML Training Scheduler stopped")
    
    def get_status(self):
        """Get scheduler status"""
        return {
            "running": self.running,
            "last_training": self.last_training.isoformat() if self.last_training else None,
            "next_training": self.next_training.isoformat() if self.next_training else None,
            "training_schedule": f"Every Monday at {self.training_hour:02d}:{self.training_minute:02d}"
        }
    
    def force_training(self):
        """Force immediate training"""
        logger.info("🔧 Forcing immediate ML model training...")
        threading.Thread(target=self.run_training, daemon=True).start()

def main():
    """Main function for standalone execution"""
    logger.info("🤖 Starting Standalone ML Training Scheduler")
    logger.info("=" * 50)
    
    scheduler = StandaloneMLScheduler()
    
    # Show current status
    status = scheduler.get_status()
    logger.info(f"📊 Current status: {status}")
    
    try:
        # Start scheduler
        scheduler.start()
        
        # Keep the main thread alive
        while True:
            time.sleep(10)
            
    except KeyboardInterrupt:
        logger.info("🛑 Received interrupt signal")
        scheduler.stop()
    except Exception as e:
        logger.error(f"❌ Scheduler service error: {e}")
        scheduler.stop()

if __name__ == "__main__":
    main()
