#!/usr/bin/env python3
"""
Add ML Training Task to Existing Scheduler
Adds weekly ML training task to the current scheduler database
"""

import os
import sys
import logging
from datetime import datetime, timedelta

# Add project root to Python path
sys.path.append('/app')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def add_ml_training_task():
    """Add ML training task to scheduler database"""
    try:
        # Import database modules
        from src.database.database import get_db_session
        from src.database.models import ScheduleTask
        
        with get_db_session() as db:
            # Check if ML training task already exists
            existing_task = db.query(ScheduleTask).filter(
                ScheduleTask.task_name == 'ml_model_training'
            ).first()
            
            if existing_task:
                logger.info("✅ ML training task already exists")
                logger.info(f"   Status: {existing_task.enabled}")
                logger.info(f"   Interval: {existing_task.interval_seconds}s")
                logger.info(f"   Next run: {existing_task.next_run}")
                return True
            
            # Create new ML training task
            # Weekly = 7 * 24 * 60 * 60 = 604800 seconds
            ml_task = ScheduleTask(
                task_name='ml_model_training',
                task_type='script',
                description='Weekly ML model training with database storage',
                interval_seconds=604800,  # Weekly
                enabled=True,
                status='pending',
                next_run=datetime.now() + timedelta(days=1),  # Start tomorrow
                task_config={
                    'script_path': '/app/scripts/training/train_all_models_unified_database.py',
                    'timeout_seconds': 3600,  # 1 hour timeout
                    'retry_attempts': 2,
                    'description': 'Train enhanced ML models for both solar systems'
                }
            )
            
            db.add(ml_task)
            db.commit()
            
            logger.info("✅ ML training task added successfully")
            logger.info(f"   Task ID: {ml_task.id}")
            logger.info(f"   Next run: {ml_task.next_run}")
            logger.info(f"   Interval: {ml_task.interval_seconds}s (weekly)")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ Failed to add ML training task: {e}")
        return False

def list_all_tasks():
    """List all scheduled tasks"""
    try:
        from src.database.database import get_db_session
        from src.database.models import ScheduleTask
        
        with get_db_session() as db:
            tasks = db.query(ScheduleTask).all()
            
            logger.info("📋 All Scheduled Tasks:")
            for task in tasks:
                logger.info(f"   {task.task_name}: {task.task_type} - {task.enabled} - {task.interval_seconds}s")
                logger.info(f"      Next run: {task.next_run}")
                logger.info(f"      Status: {task.status}")
                logger.info("")
            
            return len(tasks)
            
    except Exception as e:
        logger.error(f"❌ Failed to list tasks: {e}")
        return 0

def main():
    """Main function"""
    logger.info("🤖 Adding ML Training Task to Scheduler")
    logger.info("=" * 50)
    
    # List current tasks
    task_count = list_all_tasks()
    logger.info(f"📊 Found {task_count} existing tasks")
    
    # Add ML training task
    if add_ml_training_task():
        logger.info("✅ ML training task setup completed")
        
        # List tasks again to confirm
        logger.info("\n📋 Updated task list:")
        list_all_tasks()
        
    else:
        logger.error("❌ Failed to setup ML training task")

if __name__ == "__main__":
    main()
