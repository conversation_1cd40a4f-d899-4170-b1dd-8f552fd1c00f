#!/usr/bin/env python3
"""
Start ML Training Scheduler as Background Service
"""

import os
import sys
import time
import logging
import signal
import atexit

# Add project root to Python path
sys.path.append('/app')

from scripts.scheduling.standalone_ml_scheduler import StandaloneMLScheduler

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Global scheduler instance
ml_scheduler = None

def signal_handler(signum, frame):
    """Handle shutdown signals"""
    global ml_scheduler
    logger.info(f"🛑 Received signal {signum}, shutting down...")
    if ml_scheduler:
        ml_scheduler.stop()
    sys.exit(0)

def cleanup():
    """Cleanup function"""
    global ml_scheduler
    if ml_scheduler:
        ml_scheduler.stop()

def main():
    """Main function"""
    global ml_scheduler
    
    logger.info("🤖 Starting ML Training Scheduler Service")
    logger.info("=" * 50)
    
    # Register signal handlers
    signal.signal(signal.SIGTERM, signal_handler)
    signal.signal(signal.SIGINT, signal_handler)
    atexit.register(cleanup)
    
    try:
        # Create and start scheduler
        ml_scheduler = StandaloneMLScheduler()
        
        # Show initial status
        status = ml_scheduler.get_status()
        logger.info("📊 Initial status:")
        for key, value in status.items():
            logger.info(f"   {key}: {value}")
        
        # Start scheduler
        scheduler_thread = ml_scheduler.start()
        
        logger.info("✅ ML Training Scheduler started successfully")
        logger.info("🔄 Running in background, press Ctrl+C to stop")
        
        # Keep main thread alive
        while True:
            time.sleep(30)  # Check every 30 seconds
            
            # Log status periodically (every 30 minutes)
            if int(time.time()) % 1800 == 0:
                status = ml_scheduler.get_status()
                logger.info(f"📊 Status update: next_training={status['next_training']}")
        
    except KeyboardInterrupt:
        logger.info("🛑 Received keyboard interrupt")
    except Exception as e:
        logger.error(f"❌ Service error: {e}")
    finally:
        if ml_scheduler:
            ml_scheduler.stop()
        logger.info("🏁 ML Training Scheduler service stopped")

if __name__ == "__main__":
    main()
