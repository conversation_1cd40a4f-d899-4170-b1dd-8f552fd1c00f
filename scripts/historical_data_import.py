#!/usr/bin/env python3
"""
Historical Data Import & Gap Analysis - Enhanced Model v3
"""

import os
import pandas as pd
import psycopg2
import json
from datetime import datetime, timedelta
from pathlib import Path

def analyze_backup_files():
    """Ανάλυση διαθέσιμων backup files"""
    print("🔍 ΑΝΑΛΥΣΗ BACKUP FILES")
    print("=" * 50)
    
    backup_dirs = [
        "/home/<USER>/Downloads/new bot/backup_old_files/data_exports/",
        "/home/<USER>/Downloads/new bot/solax/"
    ]
    
    excel_files = []
    
    for backup_dir in backup_dirs:
        if os.path.exists(backup_dir):
            print(f"\n📁 Checking: {backup_dir}")
            
            for file in os.listdir(backup_dir):
                if file.endswith('.xlsx') and 'Plant Reports' in file:
                    file_path = os.path.join(backup_dir, file)
                    file_size = os.path.getsize(file_path) / 1024 / 1024  # MB
                    
                    print(f"   ✅ {file} ({file_size:.1f} MB)")
                    excel_files.append({
                        'path': file_path,
                        'name': file,
                        'size_mb': file_size
                    })
        else:
            print(f"\n❌ Directory not found: {backup_dir}")
    
    return excel_files

def analyze_current_data_gaps():
    """Ανάλυση gaps στα τρέχοντα δεδομένα"""
    print("\n🔍 ΑΝΑΛΥΣΗ GAPS ΣΤΑ ΤΡΕΧΟΝΤΑ ΔΕΔΟΜΕΝΑ")
    print("=" * 50)
    
    try:
        conn = psycopg2.connect(
            host="localhost",
            database="solar_prediction",
            user="postgres",
            password="postgres"
        )
        cursor = conn.cursor()
        
        # Ανάλυση για κάθε σύστημα
        systems = [
            ('solax_data', 'System 1 (Σπίτι Πάνω)'),
            ('solax_data2', 'System 2 (Σπίτι Κάτω)')
        ]
        
        gaps_found = []
        
        for table_name, description in systems:
            print(f"\n📊 {description}:")
            
            # Εύρος δεδομένων
            cursor.execute(f"SELECT MIN(timestamp), MAX(timestamp), COUNT(*) FROM {table_name}")
            min_date, max_date, total_count = cursor.fetchone()
            
            print(f"   📅 Εύρος: {min_date} έως {max_date}")
            print(f"   📈 Συνολικά: {total_count:,} records")
            
            # Ελέγχω για gaps ανά μήνα από March 2024
            target_periods = [
                ('2024-03-01', '2024-04-01', 'March 2024'),
                ('2024-04-01', '2024-05-01', 'April 2024'),
                ('2024-05-01', '2024-06-01', 'May 2024'),
                ('2024-06-01', '2024-07-01', 'June 2024'),
                ('2024-07-01', '2024-08-01', 'July 2024'),
                ('2024-08-01', '2024-09-01', 'August 2024'),
                ('2024-09-01', '2024-10-01', 'September 2024'),
                ('2024-10-01', '2024-11-01', 'October 2024'),
                ('2024-11-01', '2024-12-01', 'November 2024'),
                ('2024-12-01', '2025-01-01', 'December 2024'),
                ('2025-01-01', '2025-02-01', 'January 2025'),
                ('2025-02-01', '2025-03-01', 'February 2025'),
                ('2025-03-01', '2025-04-01', 'March 2025'),
                ('2025-04-01', '2025-05-01', 'April 2025'),
                ('2025-05-01', '2025-06-01', 'May 2025'),
                ('2025-06-01', '2025-07-01', 'June 2025 (partial)')
            ]
            
            print(f"   📋 Ανάλυση ανά μήνα:")
            
            for start_date, end_date, period_name in target_periods:
                cursor.execute(f"""
                    SELECT COUNT(*) FROM {table_name} 
                    WHERE timestamp >= '{start_date}' AND timestamp < '{end_date}'
                """)
                count = cursor.fetchone()[0]
                
                if count > 0:
                    print(f"      ✅ {period_name}: {count:,} records")
                else:
                    print(f"      ❌ {period_name}: 0 records - GAP!")
                    gaps_found.append({
                        'system': description,
                        'table': table_name,
                        'period': period_name,
                        'start_date': start_date,
                        'end_date': end_date
                    })
        
        cursor.close()
        conn.close()
        
        # Σύνοψη gaps
        if gaps_found:
            print(f"\n❌ GAPS ΒΡΕΘΗΚΑΝ:")
            for gap in gaps_found:
                print(f"   🔴 {gap['system']} - {gap['period']}")
        else:
            print(f"\n✅ ΔΕΝ ΒΡΕΘΗΚΑΝ GAPS!")
        
        return gaps_found
        
    except Exception as e:
        print(f"❌ Σφάλμα: {e}")
        return []

def import_excel_file(file_path, system_mapping):
    """Import ενός Excel file"""
    print(f"\n📥 Importing: {os.path.basename(file_path)}")
    
    try:
        # Διάβασμα Excel file
        df = pd.read_excel(file_path, skiprows=1)
        print(f"   📊 Loaded {len(df)} rows")
        print(f"   📋 Columns: {list(df.columns)}")
        
        # Ομαδοποίηση ανά plant name για να διαχωρίσω τα συστήματα
        if 'Plant Name' in df.columns:
            plants = df['Plant Name'].unique()
            print(f"   🏠 Plants found: {plants}")
            
            for plant in plants:
                plant_df = df[df['Plant Name'] == plant]
                system_id = system_mapping.get(plant, None)
                
                if system_id:
                    print(f"      📊 {plant}: {len(plant_df)} records → System {system_id}")
                else:
                    print(f"      ⚠️  {plant}: Unknown system mapping")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error importing {file_path}: {e}")
        return False

def main():
    """Κύρια συνάρτηση"""
    print("🚀 HISTORICAL DATA IMPORT & GAP ANALYSIS")
    print("=" * 60)
    print("Στόχος: Import historical data και εύρεση gaps")
    
    # 1. Ανάλυση backup files
    excel_files = analyze_backup_files()
    
    # 2. Ανάλυση τρεχόντων gaps
    gaps = analyze_current_data_gaps()
    
    # 3. System mapping (θα χρειαστεί να το προσαρμόσω βάσει των plant names)
    system_mapping = {
        'Σπίτι Πάνω': 1,
        'Σπίτι Κάτω': 2,
        # Θα προσθέσω περισσότερα αν χρειάζεται
    }
    
    # 4. Import process (για τώρα μόνο analysis)
    if excel_files:
        print(f"\n📥 ΔΙΑΘΕΣΙΜΑ EXCEL FILES ΓΙΑ IMPORT:")
        for file_info in excel_files:
            print(f"   📄 {file_info['name']} ({file_info['size_mb']:.1f} MB)")
        
        print(f"\n🎯 ΕΠΟΜΕΝΑ ΒΗΜΑΤΑ:")
        print(f"   1. Επιλογή των κατάλληλων Excel files")
        print(f"   2. Mapping των plant names στα systems")
        print(f"   3. Import των δεδομένων στους σωστούς πίνακες")
        print(f"   4. Επαλήθευση ότι καλύφθηκαν τα gaps")
    else:
        print(f"\n❌ ΔΕΝ ΒΡΕΘΗΚΑΝ EXCEL FILES!")
    
    # 5. Σύνοψη gaps
    if gaps:
        print(f"\n📋 ΣΥΝΟΨΗ GAPS ΠΟΥ ΧΡΕΙΑΖΟΝΤΑΙ ΚΑΛΥΨΗ:")
        
        system1_gaps = [g for g in gaps if 'System 1' in g['system']]
        system2_gaps = [g for g in gaps if 'System 2' in g['system']]
        
        print(f"   🏠 System 1 (Σπίτι Πάνω): {len(system1_gaps)} gaps")
        for gap in system1_gaps:
            print(f"      ❌ {gap['period']}")
            
        print(f"   🏠 System 2 (Σπίτι Κάτω): {len(system2_gaps)} gaps")
        for gap in system2_gaps:
            print(f"      ❌ {gap['period']}")
    
    return True

if __name__ == "__main__":
    main()
