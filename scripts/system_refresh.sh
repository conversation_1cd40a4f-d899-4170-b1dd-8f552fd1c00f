#!/bin/bash
# System Refresh Script for Solar Prediction System
# Comprehensive system maintenance and optimization

echo "🔄 Solar Prediction System - System Refresh"
echo "==========================================="

cd /home/<USER>/solar-prediction-project

echo ""
echo "1. 📊 Current System Status"
echo "---------------------------"

# Check current server status
if pgrep -f "python.*production_app.py" > /dev/null; then
    SERVER_PID=$(pgrep -f "python.*production_app.py")
    echo "✅ Production Server: Running (PID: $SERVER_PID)"

    # Get server memory usage
    if command -v ps > /dev/null; then
        SERVER_MEMORY=$(ps -p $SERVER_PID -o rss= 2>/dev/null | awk '{print $1/1024 "MB"}' 2>/dev/null || echo "Unknown")
        echo "   Memory Usage: $SERVER_MEMORY"
    fi
else
    echo "⚠️ Production Server: Not running"
    SERVER_PID=""
fi

# Check database
if psql -h localhost -U postgres -d solar_prediction -c "SELECT 1" >/dev/null 2>&1; then
    echo "✅ Database: Connected"

    # Get record counts
    PRED_COUNT=$(psql -h localhost -U postgres -d solar_prediction -t -c "SELECT COUNT(*) FROM predictions;" 2>/dev/null | tr -d ' ')
    WEATHER_COUNT=$(psql -h localhost -U postgres -d solar_prediction -t -c "SELECT COUNT(*) FROM weather_data;" 2>/dev/null | tr -d ' ')
    echo "   Predictions: $PRED_COUNT records"
    echo "   Weather Data: $WEATHER_COUNT records"
else
    echo "❌ Database: Connection failed"
fi

# Check system resources
echo "📈 System Resources:"
echo "   Memory: $(free -h | awk '/^Mem:/ {print $3 "/" $2}')"
echo "   Disk: $(df -h . | awk 'NR==2 {print $3 "/" $2 " (" $5 ")"}')"

echo ""
echo "2. 🧹 Database Maintenance"
echo "-------------------------"

echo "Performing database maintenance..."

# Clean old logs (keep last 7 days)
psql -h localhost -U postgres -d solar_prediction -c "
DELETE FROM system_logs WHERE timestamp < NOW() - INTERVAL '7 days';
" >/dev/null 2>&1 && echo "✅ Cleaned old system logs" || echo "⚠️ Log cleanup failed"

# Clean old weather data (keep last 30 days)
psql -h localhost -U postgres -d solar_prediction -c "
DELETE FROM weather_data WHERE timestamp < NOW() - INTERVAL '30 days' AND is_forecast = false;
" >/dev/null 2>&1 && echo "✅ Cleaned old weather data" || echo "⚠️ Weather data cleanup failed"

# Update table statistics
psql -h localhost -U postgres -d solar_prediction -c "
ANALYZE predictions;
ANALYZE weather_data;
ANALYZE solax_data;
" >/dev/null 2>&1 && echo "✅ Updated table statistics" || echo "⚠️ Statistics update failed"

# Vacuum tables
psql -h localhost -U postgres -d solar_prediction -c "
VACUUM predictions;
VACUUM weather_data;
VACUUM solax_data;
" >/dev/null 2>&1 && echo "✅ Vacuumed tables" || echo "⚠️ Vacuum failed"

echo ""
echo "3. 🔧 Service Optimization"
echo "--------------------------"

# Stop current server gracefully
if [ -n "$SERVER_PID" ]; then
    echo "Stopping current server (PID: $SERVER_PID)..."
    kill $SERVER_PID 2>/dev/null
    sleep 5

    # Force kill if still running
    if ps -p $SERVER_PID > /dev/null 2>&1; then
        echo "Force stopping server..."
        kill -9 $SERVER_PID 2>/dev/null
        sleep 2
    fi

    echo "✅ Server stopped"
else
    echo "ℹ️ No server to stop"
fi

# Clean up any orphaned processes
pkill -f "python.*production_app.py" 2>/dev/null || true
sleep 2

echo ""
echo "4. 📦 Dependencies Check"
echo "------------------------"

# Check critical Python packages
echo "Checking Python dependencies..."

PACKAGES=("fastapi" "uvicorn" "psycopg2" "requests" "pydantic")
for package in "${PACKAGES[@]}"; do
    if python3 -c "import $package" 2>/dev/null; then
        VERSION=$(python3 -c "import $package; print(getattr($package, '__version__', 'unknown'))" 2>/dev/null || echo "unknown")
        echo "✅ $package: $VERSION"
    else
        echo "❌ $package: Missing"
    fi
done

echo ""
echo "5. 🚀 Service Restart"
echo "---------------------"

echo "Starting optimized production server..."

# Start with optimized settings
nohup python3 scripts/production_app.py > production_refresh.log 2>&1 &
NEW_SERVER_PID=$!

echo "New server PID: $NEW_SERVER_PID"
echo "Waiting for server to initialize..."
sleep 8

# Verify new server
if ps -p $NEW_SERVER_PID > /dev/null; then
    echo "✅ New server: Running"

    # Test endpoints
    echo "Testing endpoints..."

    # Health check
    if curl -s http://localhost:8100/health >/dev/null 2>&1; then
        echo "✅ Health endpoint: OK"
    else
        echo "⚠️ Health endpoint: Failed"
    fi

    # Web interface
    if curl -s http://localhost:8100/ >/dev/null 2>&1; then
        echo "✅ Web interface: OK"
    else
        echo "⚠️ Web interface: Failed"
    fi

    # API test
    if curl -s http://localhost:8100/api/v1/weather/current >/dev/null 2>&1; then
        echo "✅ Weather API: OK"
    else
        echo "⚠️ Weather API: Failed"
    fi

else
    echo "❌ New server: Failed to start"
    echo "Log output:"
    tail -20 production_refresh.log
fi

echo ""
echo "6. 📊 Post-Refresh Status"
echo "-------------------------"

# Final status check
if ps -p $NEW_SERVER_PID > /dev/null 2>&1; then
    NEW_MEMORY=$(ps -p $NEW_SERVER_PID -o rss= 2>/dev/null | awk '{print $1/1024 "MB"}' 2>/dev/null || echo "Unknown")
    echo "🟢 Server Status: Running (PID: $NEW_SERVER_PID)"
    echo "   Memory Usage: $NEW_MEMORY"
    echo "   Log File: production_refresh.log"
else
    echo "🔴 Server Status: Failed"
fi

# Database status
if psql -h localhost -U postgres -d solar_prediction -c "SELECT 1" >/dev/null 2>&1; then
    NEW_PRED_COUNT=$(psql -h localhost -U postgres -d solar_prediction -t -c "SELECT COUNT(*) FROM predictions;" 2>/dev/null | tr -d ' ')
    RECENT_PRED_COUNT=$(psql -h localhost -U postgres -d solar_prediction -t -c "SELECT COUNT(*) FROM predictions WHERE timestamp > NOW() - INTERVAL '1 hour';" 2>/dev/null | tr -d ' ')
    echo "🟢 Database Status: Connected"
    echo "   Total Predictions: $NEW_PRED_COUNT"
    echo "   Recent Predictions: $RECENT_PRED_COUNT"
else
    echo "🔴 Database Status: Failed"
fi

# Test prediction generation
echo "Testing prediction generation..."
PRED_TEST=$(curl -s -H "Content-Type: application/json" \
    -d '{"temperature": 26, "cloud_cover": 45, "soc": 80}' \
    http://localhost:8100/api/v1/predict 2>/dev/null)

if echo "$PRED_TEST" | grep -q "predicted_power"; then
    PREDICTED_POWER=$(echo "$PRED_TEST" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    print(f\"{data['predicted_power']:.1f}W\")
except:
    print('N/A')
" 2>/dev/null)
    echo "🟢 Prediction Test: $PREDICTED_POWER"
else
    echo "🔴 Prediction Test: Failed"
fi

echo ""
echo "7. 🎯 Refresh Summary"
echo "--------------------"

# Count successful operations
SUCCESS_COUNT=0
TOTAL_COUNT=6

# Check each component
if ps -p $NEW_SERVER_PID > /dev/null 2>&1; then
    echo "✅ Server Restart: Success"
    ((SUCCESS_COUNT++))
else
    echo "❌ Server Restart: Failed"
fi

if psql -h localhost -U postgres -d solar_prediction -c "SELECT 1" >/dev/null 2>&1; then
    echo "✅ Database Maintenance: Success"
    ((SUCCESS_COUNT++))
else
    echo "❌ Database Maintenance: Failed"
fi

if curl -s http://localhost:8100/health >/dev/null 2>&1; then
    echo "✅ API Endpoints: Success"
    ((SUCCESS_COUNT++))
else
    echo "❌ API Endpoints: Failed"
fi

if curl -s http://localhost:8100/ >/dev/null 2>&1; then
    echo "✅ Web Interface: Success"
    ((SUCCESS_COUNT++))
else
    echo "❌ Web Interface: Failed"
fi

if echo "$PRED_TEST" | grep -q "predicted_power"; then
    echo "✅ Prediction Engine: Success"
    ((SUCCESS_COUNT++))
else
    echo "❌ Prediction Engine: Failed"
fi

if python3 -c "import fastapi, uvicorn, psycopg2, requests" 2>/dev/null; then
    echo "✅ Dependencies: Success"
    ((SUCCESS_COUNT++))
else
    echo "❌ Dependencies: Failed"
fi

echo ""
echo "Refresh Status: $SUCCESS_COUNT/$TOTAL_COUNT operations successful"

if [ $SUCCESS_COUNT -eq $TOTAL_COUNT ]; then
    echo "🎉 SYSTEM REFRESH COMPLETED SUCCESSFULLY"
    echo ""
    echo "🌐 Access Points:"
    echo "   Web Dashboard: http://localhost:8100/"
    echo "   API Root: http://localhost:8100/api"
    echo "   Health Check: http://localhost:8100/health"
    echo ""
    echo "🔧 Management:"
    echo "   Server PID: $NEW_SERVER_PID"
    echo "   Log File: production_refresh.log"
    echo "   Stop Server: kill $NEW_SERVER_PID"

elif [ $SUCCESS_COUNT -ge 4 ]; then
    echo "⚠️ SYSTEM REFRESH MOSTLY SUCCESSFUL"
    echo "Some components need attention but core functionality works"

else
    echo "❌ SYSTEM REFRESH FAILED"
    echo "Multiple components failed, manual intervention required"
    echo ""
    echo "Check logs:"
    echo "   tail -f production_refresh.log"
fi

echo ""
echo "==========================================="
echo "System refresh completed at $(date)"
echo "==========================================="
