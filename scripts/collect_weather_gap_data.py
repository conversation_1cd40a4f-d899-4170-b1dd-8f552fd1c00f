#!/usr/bin/env python3
"""
Collect weather data for the gap period (2025-04-16 to 2025-05-30) from Open-Meteo Archive API.
"""

import requests
import pandas as pd
import asyncio
import sys
import os
from datetime import datetime, timedelta

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

async def collect_weather_data():
    """Collect weather data from Open-Meteo Archive API."""
    print("🌤️  Collecting weather data from Open-Meteo Archive API...")
    
    # API configuration
    url = "https://archive-api.open-meteo.com/v1/archive"
    params = {
        "latitude": 38.141367951893024,
        "longitude": 24.00715534164505,
        "start_date": "2025-04-16",
        "end_date": "2025-05-30",
        "hourly": "temperature_2m,direct_radiation,diffuse_radiation,shortwave_radiation"
    }
    
    print(f"📍 Location: {params['latitude']}, {params['longitude']}")
    print(f"📅 Date range: {params['start_date']} to {params['end_date']}")
    print(f"📊 Parameters: {params['hourly']}")
    
    try:
        # Make API request
        print("\n🔄 Making API request...")
        response = requests.get(url, params=params, timeout=30)
        response.raise_for_status()
        
        data = response.json()
        print(f"✅ API request successful")
        
        # Extract hourly data
        if 'hourly' not in data:
            print("❌ No hourly data in response")
            return None
            
        hourly_data = data['hourly']
        
        # Create DataFrame
        df = pd.DataFrame({
            'timestamp': pd.to_datetime(hourly_data['time']),
            'temperature_2m': hourly_data['temperature_2m'],
            'direct_radiation': hourly_data['direct_radiation'], 
            'diffuse_radiation': hourly_data['diffuse_radiation'],
            'shortwave_radiation': hourly_data['shortwave_radiation']
        })
        
        print(f"📊 Data shape: {df.shape}")
        print(f"📅 Date range: {df['timestamp'].min()} to {df['timestamp'].max()}")
        
        # Data quality check
        print(f"\n🔍 Data Quality Check:")
        for col in df.columns:
            if col != 'timestamp':
                null_count = df[col].isnull().sum()
                null_pct = (null_count / len(df)) * 100
                print(f"   {col}: {null_count} nulls ({null_pct:.1f}%)")
        
        # Show sample data
        print(f"\n📋 Sample Data (first 5 records):")
        print(df.head().to_string(index=False))
        
        # Save to CSV for inspection
        output_file = "data/processed/weather_gap_data_2025-04-16_to_2025-05-30.csv"
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        df.to_csv(output_file, index=False)
        print(f"\n💾 Data saved to: {output_file}")
        
        return df
        
    except requests.exceptions.RequestException as e:
        print(f"❌ API request failed: {e}")
        return None
    except Exception as e:
        print(f"❌ Error processing data: {e}")
        import traceback
        traceback.print_exc()
        return None

async def save_to_database(df):
    """Save weather data to database (placeholder for now)."""
    if df is None or df.empty:
        print("❌ No data to save")
        return
        
    print(f"\n💾 Preparing to save {len(df)} weather records to database...")
    
    # For now, just show what would be saved
    print("📋 Database format preview:")
    
    # Convert to database format
    db_records = []
    for _, row in df.iterrows():
        record = {
            'timestamp': row['timestamp'],
            'temperature_2m': row['temperature_2m'],
            'shortwave_radiation': row['shortwave_radiation'],  # GHI equivalent
            'direct_radiation': row['direct_radiation'],        # DNI equivalent  
            'diffuse_radiation': row['diffuse_radiation'],      # DHI equivalent
            'source': 'open-meteo-archive'
        }
        db_records.append(record)
    
    # Show sample records
    print("Sample database records:")
    for i, record in enumerate(db_records[:3]):
        print(f"  Record {i+1}: {record}")
    
    print(f"✅ Ready to insert {len(db_records)} records into weather_data table")
    
    # TODO: Implement actual database insertion
    # This will be done after we set up the database connection properly
    
    return db_records

async def main():
    """Main function to collect weather gap data."""
    print("🌤️  Weather Data Gap Collection")
    print("=" * 50)
    
    # Collect weather data
    weather_df = await collect_weather_data()
    
    if weather_df is not None:
        # Prepare for database insertion
        db_records = await save_to_database(weather_df)
        
        print(f"\n🎯 SUMMARY:")
        print(f"✅ Weather data collected: {len(weather_df)} records")
        print(f"📅 Period covered: 2025-04-16 to 2025-05-30")
        print(f"⏰ Frequency: Hourly")
        print(f"📊 Parameters: temperature, GHI, DNI, DHI")
        print(f"💾 Data ready for database insertion")
        
        print(f"\n📋 NEXT STEPS:")
        print(f"1. ✅ Weather data collected")
        print(f"2. ⏳ Process SolaX Excel data")
        print(f"3. ⏳ Insert data into database")
        print(f"4. ⏳ Run integration pipeline")
        print(f"5. ⏳ Setup CAMS API")
        
    else:
        print("❌ Failed to collect weather data")

if __name__ == "__main__":
    asyncio.run(main())
