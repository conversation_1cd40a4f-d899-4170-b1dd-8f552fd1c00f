#!/usr/bin/env python3
"""
Phase 1: Final Validation
Confirms that all requirements for Phase 1 are met with real data
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Phase1Validator:
    """Final validator for Phase 1 completion"""
    
    def __init__(self):
        self.db_config = {
            'host': 'localhost',
            'database': 'solar_prediction',
            'user': 'postgres',
            'password': ''
        }
        
        self.validation_results = {}
    
    def connect_database(self):
        """Connect to database"""
        try:
            conn = psycopg2.connect(**self.db_config)
            return conn
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            return None
    
    def validate_table_structure(self, table_name, required_columns):
        """Validate table structure"""
        
        logger.info(f"🔍 Validating {table_name} structure...")
        
        conn = self.connect_database()
        if not conn:
            return False
        
        try:
            cur = conn.cursor(cursor_factory=RealDictCursor)
            
            # Get table columns
            cur.execute(f"""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = '{table_name}'
                ORDER BY ordinal_position
            """)
            
            columns = [row['column_name'] for row in cur.fetchall()]
            
            # Check required columns
            missing_columns = []
            for col in required_columns:
                if col not in columns:
                    missing_columns.append(col)
                else:
                    logger.info(f"   ✅ {col}")
            
            if missing_columns:
                logger.error(f"   ❌ Missing columns: {missing_columns}")
                conn.close()
                return False
            
            # Get record count
            cur.execute(f"SELECT COUNT(*) as count FROM {table_name}")
            record_count = cur.fetchone()['count']
            
            # Check data completeness for required columns
            completeness = {}
            for col in required_columns:
                cur.execute(f"SELECT COUNT(*) as populated FROM {table_name} WHERE {col} IS NOT NULL")
                populated = cur.fetchone()['populated']
                completeness[col] = (populated / record_count) * 100 if record_count > 0 else 0
                logger.info(f"   📊 {col}: {populated:,}/{record_count:,} ({completeness[col]:.1f}%)")
            
            conn.close()
            
            self.validation_results[table_name] = {
                'columns_present': True,
                'record_count': record_count,
                'completeness': completeness,
                'missing_columns': missing_columns
            }
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Validation failed for {table_name}: {e}")
            conn.close()
            return False
    
    def validate_data_quality(self, table_name):
        """Validate data quality"""
        
        logger.info(f"📈 Validating {table_name} data quality...")
        
        conn = self.connect_database()
        if not conn:
            return False
        
        try:
            cur = conn.cursor(cursor_factory=RealDictCursor)
            
            # Check recent data (last 24 hours)
            cur.execute(f"""
                SELECT COUNT(*) as recent_count 
                FROM {table_name} 
                WHERE timestamp >= NOW() - INTERVAL '24 hours'
            """)
            recent_count = cur.fetchone()['recent_count']
            
            # Check date range
            cur.execute(f"SELECT MIN(timestamp) as earliest, MAX(timestamp) as latest FROM {table_name}")
            date_range = cur.fetchone()
            
            # Sample recent data
            if table_name == 'solax_data':
                sample_query = "SELECT timestamp, yield_today, yield_total, ac_power, soc FROM solax_data ORDER BY timestamp DESC LIMIT 3"
            elif table_name == 'solax_data2':
                sample_query = "SELECT timestamp, yield_today, total_yield, ac_power, soc FROM solax_data2 ORDER BY timestamp DESC LIMIT 3"
            else:  # weather_data
                sample_query = "SELECT timestamp, temperature_2m, cloud_cover FROM weather_data ORDER BY timestamp DESC LIMIT 3"
            
            cur.execute(sample_query)
            sample_data = cur.fetchall()
            
            conn.close()
            
            logger.info(f"   📊 Recent data (24h): {recent_count:,} records")
            logger.info(f"   📅 Date range: {date_range['earliest']} to {date_range['latest']}")
            logger.info(f"   📋 Sample data:")
            for sample in sample_data:
                logger.info(f"      {sample['timestamp']}: {dict(sample)}")
            
            self.validation_results[table_name].update({
                'recent_count': recent_count,
                'date_range': date_range,
                'sample_data': sample_data,
                'data_fresh': recent_count > 0
            })
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Data quality validation failed for {table_name}: {e}")
            conn.close()
            return False
    
    def validate_ml_readiness(self):
        """Validate ML pipeline readiness"""
        
        logger.info("🤖 Validating ML pipeline readiness...")
        
        # Check if all required data is available
        required_tables = ['solax_data', 'solax_data2', 'weather_data']
        
        all_tables_ready = True
        total_records = 0
        
        for table_name in required_tables:
            if table_name in self.validation_results:
                table_info = self.validation_results[table_name]
                
                if table_info['record_count'] == 0:
                    logger.error(f"   ❌ {table_name}: No data")
                    all_tables_ready = False
                elif not table_info['data_fresh']:
                    logger.warning(f"   ⚠️ {table_name}: No recent data")
                else:
                    logger.info(f"   ✅ {table_name}: {table_info['record_count']:,} records, fresh data available")
                    total_records += table_info['record_count']
            else:
                logger.error(f"   ❌ {table_name}: Not validated")
                all_tables_ready = False
        
        # Check schema compatibility
        schema_compatible = True
        
        # solax_data requirements
        if 'solax_data' in self.validation_results:
            solax1_completeness = self.validation_results['solax_data']['completeness']
            required_completeness = 50  # At least 50% data completeness
            
            for col in ['yield_total', 'ac_power', 'soc']:
                if solax1_completeness.get(col, 0) < required_completeness:
                    logger.error(f"   ❌ solax_data.{col}: {solax1_completeness.get(col, 0):.1f}% < {required_completeness}%")
                    schema_compatible = False
                else:
                    logger.info(f"   ✅ solax_data.{col}: {solax1_completeness.get(col, 0):.1f}% completeness")
        
        # solax_data2 requirements
        if 'solax_data2' in self.validation_results:
            solax2_completeness = self.validation_results['solax_data2']['completeness']
            
            for col in ['total_yield', 'ac_power', 'soc']:
                if solax2_completeness.get(col, 0) < required_completeness:
                    logger.error(f"   ❌ solax_data2.{col}: {solax2_completeness.get(col, 0):.1f}% < {required_completeness}%")
                    schema_compatible = False
                else:
                    logger.info(f"   ✅ solax_data2.{col}: {solax2_completeness.get(col, 0):.1f}% completeness")
        
        # weather_data requirements
        if 'weather_data' in self.validation_results:
            weather_completeness = self.validation_results['weather_data']['completeness']
            
            for col in ['temperature_2m', 'cloud_cover']:
                if weather_completeness.get(col, 0) < required_completeness:
                    logger.error(f"   ❌ weather_data.{col}: {weather_completeness.get(col, 0):.1f}% < {required_completeness}%")
                    schema_compatible = False
                else:
                    logger.info(f"   ✅ weather_data.{col}: {weather_completeness.get(col, 0):.1f}% completeness")
        
        ml_ready = all_tables_ready and schema_compatible
        
        self.validation_results['ml_readiness'] = {
            'all_tables_ready': all_tables_ready,
            'schema_compatible': schema_compatible,
            'total_records': total_records,
            'ready_for_phase2': ml_ready
        }
        
        return ml_ready
    
    def run_full_validation(self):
        """Run complete Phase 1 validation"""
        
        logger.info("🚀 Starting Phase 1 final validation...")
        
        # Define requirements
        table_requirements = {
            'solax_data': ['timestamp', 'yield_today', 'yield_total', 'ac_power', 'soc', 'bat_power'],
            'solax_data2': ['timestamp', 'yield_today', 'total_yield', 'ac_power', 'soc', 'bat_power'],
            'weather_data': ['timestamp', 'temperature_2m', 'cloud_cover', 'global_horizontal_irradiance']
        }
        
        # Validate each table
        validation_success = True
        
        for table_name, required_columns in table_requirements.items():
            if not self.validate_table_structure(table_name, required_columns):
                validation_success = False
                continue
            
            if not self.validate_data_quality(table_name):
                validation_success = False
                continue
        
        # Validate ML readiness
        if validation_success:
            ml_ready = self.validate_ml_readiness()
            validation_success = ml_ready
        
        return validation_success

def print_validation_summary(validator):
    """Print validation summary"""
    
    print("\n" + "="*80)
    print("✅ PHASE 1 FINAL VALIDATION SUMMARY")
    print("="*80)
    print(f"📅 Validation Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🗄️ Database: solar_prediction@localhost")
    print()
    
    # Table validation results
    print("📊 TABLE VALIDATION RESULTS:")
    for table_name, results in validator.validation_results.items():
        if table_name == 'ml_readiness':
            continue
        
        print(f"   📋 {table_name}:")
        print(f"      Records: {results['record_count']:,}")
        print(f"      Recent data: {'✅' if results['data_fresh'] else '❌'}")
        print(f"      Schema: {'✅ Complete' if results['columns_present'] else '❌ Missing columns'}")
        
        # Show completeness for key columns
        key_completeness = []
        for col, percentage in results['completeness'].items():
            if percentage >= 50:
                key_completeness.append(f"{col}: {percentage:.1f}%")
        
        if key_completeness:
            print(f"      Data quality: {', '.join(key_completeness)}")
    print()
    
    # ML readiness
    if 'ml_readiness' in validator.validation_results:
        ml_info = validator.validation_results['ml_readiness']
        
        print("🤖 ML PIPELINE READINESS:")
        print(f"   Tables ready: {'✅' if ml_info['all_tables_ready'] else '❌'}")
        print(f"   Schema compatible: {'✅' if ml_info['schema_compatible'] else '❌'}")
        print(f"   Total records: {ml_info['total_records']:,}")
        print(f"   Ready for Phase 2: {'✅ YES' if ml_info['ready_for_phase2'] else '❌ NO'}")
        print()
    
    # Overall status
    if validator.validation_results.get('ml_readiness', {}).get('ready_for_phase2', False):
        print("🎉 PHASE 1 COMPLETED SUCCESSFULLY!")
        print("🚀 Ready to proceed to Phase 2: Feature Engineering")
    else:
        print("❌ PHASE 1 VALIDATION FAILED")
        print("🔧 Please address issues before proceeding")
    
    print("="*80)

def main():
    """Main validation function"""
    
    print("✅ PHASE 1: FINAL VALIDATION")
    print("="*60)
    print("🔍 Validating real database with actual solar data")
    print("📊 Confirming ML pipeline readiness")
    print()
    
    try:
        # Initialize validator
        validator = Phase1Validator()
        
        # Run full validation
        success = validator.run_full_validation()
        
        # Print summary
        print_validation_summary(validator)
        
        if success:
            print(f"\n🎉 Phase 1 validation completed successfully!")
            print("🚀 Ready to proceed to Phase 2: Feature Engineering")
            return True
        else:
            print("❌ Phase 1 validation failed")
            return False
            
    except Exception as e:
        print(f"❌ Validation failed: {e}")
        logger.exception("Phase 1 validation failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
