#!/usr/bin/env python3
"""
Micro Batch Import - Πολύ μικρά batches (100 records) για σταθερό import
"""

import pandas as pd
import psycopg2
import json
import os
from datetime import datetime
import time

def test_database_connection():
    """Test database connection"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            database="solar_prediction",
            user="postgres",
            password="postgres",
            connect_timeout=10
        )
        cursor = conn.cursor()
        cursor.execute("SELECT 1")
        cursor.close()
        conn.close()
        return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def check_table_structure(table_name):
    """Έλεγχος δομής πίνακα"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            database="solar_prediction",
            user="postgres",
            password="postgres"
        )
        cursor = conn.cursor()
        
        # Ελέγχω columns
        cursor.execute(f"""
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = '{table_name}'
            ORDER BY ordinal_position
        """)
        
        columns = cursor.fetchall()
        cursor.close()
        conn.close()
        
        return [col[0] for col in columns]
        
    except Exception as e:
        print(f"❌ Table structure check failed: {e}")
        return []

def micro_import_file(file_path, system_id, batch_size=100):
    """Micro batch import με πολύ μικρά batches"""
    print(f"\n📥 MICRO IMPORT: {os.path.basename(file_path)}")
    print(f"   🏠 System ID: {system_id}")
    print(f"   📦 Micro batch size: {batch_size}")
    
    try:
        # Διάβασμα Excel file
        print(f"   📖 Reading Excel file...")
        df = pd.read_excel(file_path, skiprows=1)
        print(f"   📊 Loaded: {len(df)} rows")
        
        if len(df) == 0:
            print(f"   ⚠️  Empty file - skipping")
            return 0
        
        # Timestamp column
        time_columns = [col for col in df.columns if 'time' in col.lower() or 'date' in col.lower()]
        if not time_columns:
            print(f"   ❌ No timestamp column found")
            return 0
        
        time_col = time_columns[0]
        print(f"   📅 Time column: {time_col}")
        
        # Target table
        target_table = "solax_data" if system_id == 1 else "solax_data2"
        print(f"   💾 Target table: {target_table}")
        
        # Έλεγχος table structure
        table_columns = check_table_structure(target_table)
        if not table_columns:
            print(f"   ❌ Could not check table structure")
            return 0
        
        print(f"   📋 Table has {len(table_columns)} columns")
        
        # Database connection
        conn = psycopg2.connect(
            host="localhost",
            database="solar_prediction",
            user="postgres",
            password="postgres"
        )
        cursor = conn.cursor()
        
        # Micro batch processing
        imported_count = 0
        error_count = 0
        total_batches = (len(df) + batch_size - 1) // batch_size
        
        print(f"   📦 Processing {total_batches} micro batches...")
        
        for batch_num in range(total_batches):
            start_idx = batch_num * batch_size
            end_idx = min(start_idx + batch_size, len(df))
            batch_df = df.iloc[start_idx:end_idx]
            
            print(f"   📦 Batch {batch_num + 1}/{total_batches} ({len(batch_df)} records)...")
            
            batch_imported = 0
            
            for _, row in batch_df.iterrows():
                try:
                    # Timestamp
                    timestamp = row[time_col]
                    if pd.isna(timestamp):
                        error_count += 1
                        continue
                    
                    # Απλό insert με βασικά fields μόνο
                    insert_sql = f"""
                        INSERT INTO {target_table} (timestamp, system_id, raw_data) 
                        VALUES (%s, %s, %s)
                    """
                    
                    raw_data = json.dumps({
                        'source': 'micro_import',
                        'file': os.path.basename(file_path),
                        'system_id': system_id,
                        'batch': batch_num + 1,
                        'imported_at': datetime.now().isoformat(),
                        'original_data': {k: str(v) for k, v in row.to_dict().items() if not pd.isna(v)}
                    })
                    
                    cursor.execute(insert_sql, (timestamp, system_id, raw_data))
                    batch_imported += 1
                    
                except Exception as e:
                    error_count += 1
                    if error_count <= 3:
                        print(f"      ⚠️  Row error: {e}")
                    continue
            
            # Commit micro batch
            try:
                conn.commit()
                imported_count += batch_imported
                print(f"      ✅ Committed: {batch_imported} records")
            except Exception as e:
                conn.rollback()
                print(f"      ❌ Batch commit failed: {e}")
                break
            
            # Μικρή παύση μεταξύ batches
            time.sleep(0.2)
        
        cursor.close()
        conn.close()
        
        print(f"   📊 File summary:")
        print(f"      ✅ Imported: {imported_count:,} records")
        print(f"      ❌ Errors: {error_count:,} records")
        
        return imported_count
        
    except Exception as e:
        print(f"   ❌ File import failed: {e}")
        import traceback
        traceback.print_exc()
        return 0

def main():
    """Κύρια συνάρτηση"""
    print("📥 MICRO BATCH IMPORT")
    print("=" * 50)
    print("Στόχος: Import με πολύ μικρά batches (100 records)")
    
    # 1. Test database connection
    print(f"\n🔍 TESTING DATABASE CONNECTION...")
    if not test_database_connection():
        print(f"❌ Cannot connect to database!")
        return False
    
    print(f"✅ Database connection OK")
    
    # 2. Test με ένα μικρό file πρώτα
    test_file = "data/raw/System1/Plant Reports 2024-03-01-2024-06-28.xlsx"
    
    if not os.path.exists(test_file):
        print(f"❌ Test file not found: {test_file}")
        return False
    
    print(f"\n🧪 TESTING WITH SMALL SAMPLE...")
    
    # Import μόνο το πρώτο file για test
    imported = micro_import_file(test_file, 1, batch_size=50)  # Ακόμα μικρότερα batches για test
    
    if imported > 0:
        print(f"\n✅ TEST SUCCESSFUL!")
        print(f"   📊 Imported {imported:,} records from test file")
        print(f"   🎯 Ready to import all files")
        
        # Ερώτηση για συνέχεια
        print(f"\n❓ Continue with full import? (This will take time)")
        print(f"   📁 4 files per system = 8 files total")
        print(f"   📊 ~250,000 records expected")
        
        return True
    else:
        print(f"\n❌ TEST FAILED!")
        print(f"   🔧 Need to investigate database issues")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n🎯 NEXT STEPS:")
        print(f"   1. Test completed successfully")
        print(f"   2. Ready for full import")
        print(f"   3. Use micro batches (50-100 records)")
    else:
        print(f"\n❌ ISSUES FOUND:")
        print(f"   1. Database connection problems")
        print(f"   2. Table structure issues")
        print(f"   3. Need manual investigation")
