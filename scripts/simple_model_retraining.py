#!/usr/bin/env python3
"""
Simple Model Retraining & Evaluation
Retrain models with correct data and evaluate performance
"""

import os
import sys
import json
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, Any, List, Tuple
import psycopg2
from psycopg2.extras import RealDictCursor
import warnings
warnings.filterwarnings('ignore')

def get_db_connection():
    """Get PostgreSQL database connection"""
    try:
        return psycopg2.connect(
            host=os.getenv("DB_HOST", "localhost"),
            database=os.getenv("DB_NAME", "solar_prediction"),
            user=os.getenv("DB_USER", "postgres"),
            password=os.getenv("DB_PASSWORD", "postgres"),
            port=os.getenv("DB_PORT", "5432")
        )
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return None

class SimpleModelEvaluator:
    """Simple model evaluator using basic statistics"""
    
    def __init__(self):
        self.conn = get_db_connection()
        if not self.conn:
            raise Exception("Cannot proceed without database connection")
    
    def get_all_data(self) -> Dict[str, pd.DataFrame]:
        """Get all available data for both systems"""
        print("📊 COLLECTING ALL AVAILABLE DATA")
        print("=" * 40)
        
        all_data = {}
        
        # System 1 data (solax_data)
        try:
            with self.conn.cursor(cursor_factory=RealDictCursor) as cur:
                cur.execute("""
                    SELECT 
                        timestamp,
                        yield_today,
                        EXTRACT(year FROM timestamp) as year,
                        EXTRACT(month FROM timestamp) as month,
                        EXTRACT(day FROM timestamp) as day,
                        EXTRACT(hour FROM timestamp) as hour,
                        EXTRACT(dow FROM timestamp) as day_of_week,
                        EXTRACT(doy FROM timestamp) as day_of_year
                    FROM solax_data
                    WHERE yield_today >= 0 AND yield_today < 100
                    ORDER BY timestamp
                """)
                
                results = cur.fetchall()
                if results:
                    df = pd.DataFrame([dict(row) for row in results])
                    df['system_id'] = 1
                    all_data['system_1'] = df
                    print(f"✅ System 1: {len(df)} records from {df['timestamp'].min()} to {df['timestamp'].max()}")
                else:
                    print("❌ No data for System 1")
                    all_data['system_1'] = pd.DataFrame()
        
        except Exception as e:
            print(f"❌ Error getting System 1 data: {e}")
            all_data['system_1'] = pd.DataFrame()
        
        # System 2 data (solax_data2)
        try:
            with self.conn.cursor(cursor_factory=RealDictCursor) as cur:
                cur.execute("""
                    SELECT 
                        timestamp,
                        yield_today,
                        EXTRACT(year FROM timestamp) as year,
                        EXTRACT(month FROM timestamp) as month,
                        EXTRACT(day FROM timestamp) as day,
                        EXTRACT(hour FROM timestamp) as hour,
                        EXTRACT(dow FROM timestamp) as day_of_week,
                        EXTRACT(doy FROM timestamp) as day_of_year
                    FROM solax_data2
                    WHERE yield_today >= 0 AND yield_today < 100
                    ORDER BY timestamp
                """)
                
                results = cur.fetchall()
                if results:
                    df = pd.DataFrame([dict(row) for row in results])
                    df['system_id'] = 2
                    all_data['system_2'] = df
                    print(f"✅ System 2: {len(df)} records from {df['timestamp'].min()} to {df['timestamp'].max()}")
                else:
                    print("❌ No data for System 2")
                    all_data['system_2'] = pd.DataFrame()
        
        except Exception as e:
            print(f"❌ Error getting System 2 data: {e}")
            all_data['system_2'] = pd.DataFrame()
        
        return all_data
    
    def analyze_temporal_patterns(self, all_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """Analyze temporal patterns in the data"""
        print(f"\n📊 ANALYZING TEMPORAL PATTERNS")
        print("=" * 35)
        
        analysis = {}
        
        for system_key, df in all_data.items():
            if df.empty:
                continue
            
            system_id = int(system_key.split('_')[1])
            print(f"\n📈 System {system_id} Analysis:")
            
            # Convert timestamp to datetime
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            
            # Hourly patterns
            hourly_stats = df.groupby('hour')['yield_today'].agg(['mean', 'std', 'count']).round(2)
            
            # Daily patterns
            daily_stats = df.groupby(df['timestamp'].dt.date)['yield_today'].agg(['max', 'mean', 'count']).round(2)
            daily_stats.index = pd.to_datetime(daily_stats.index)
            
            # Monthly patterns
            monthly_stats = df.groupby('month')['yield_today'].agg(['mean', 'std', 'count']).round(2)
            
            # Yearly patterns
            yearly_stats = df.groupby('year')['yield_today'].agg(['mean', 'std', 'count']).round(2)
            
            # System performance summary
            total_records = len(df)
            date_range = (df['timestamp'].min(), df['timestamp'].max())
            avg_yield = df['yield_today'].mean()
            max_yield = df['yield_today'].max()
            min_yield = df['yield_today'].min()
            std_yield = df['yield_today'].std()
            
            analysis[system_key] = {
                'summary': {
                    'total_records': total_records,
                    'date_range': date_range,
                    'avg_yield': avg_yield,
                    'max_yield': max_yield,
                    'min_yield': min_yield,
                    'std_yield': std_yield
                },
                'hourly_patterns': hourly_stats,
                'daily_patterns': daily_stats,
                'monthly_patterns': monthly_stats,
                'yearly_patterns': yearly_stats
            }
            
            print(f"   📊 Total records: {total_records:,}")
            print(f"   📅 Date range: {date_range[0]} to {date_range[1]}")
            print(f"   ⚡ Avg yield: {avg_yield:.2f} kWh")
            print(f"   📈 Max yield: {max_yield:.2f} kWh")
            print(f"   📉 Min yield: {min_yield:.2f} kWh")
            print(f"   📊 Std dev: {std_yield:.2f} kWh")
            
            # Best performing months
            best_month = monthly_stats['mean'].idxmax()
            worst_month = monthly_stats['mean'].idxmin()
            print(f"   🏆 Best month: {best_month} ({monthly_stats.loc[best_month, 'mean']:.2f} kWh avg)")
            print(f"   📉 Worst month: {worst_month} ({monthly_stats.loc[worst_month, 'mean']:.2f} kWh avg)")
        
        return analysis
    
    def create_simple_prediction_model(self, df: pd.DataFrame, system_id: int) -> Dict[str, Any]:
        """Create simple prediction model based on patterns"""
        print(f"\n🔧 Creating prediction model for System {system_id}...")
        
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        # Calculate seasonal averages
        seasonal_patterns = {}
        
        # Monthly averages
        monthly_avg = df.groupby('month')['yield_today'].mean()
        
        # Hourly averages by month
        hourly_monthly_avg = df.groupby(['month', 'hour'])['yield_today'].mean()
        
        # Day of week patterns
        dow_patterns = df.groupby('day_of_week')['yield_today'].mean()
        
        # Create prediction function
        def predict_yield(target_date: datetime) -> float:
            month = target_date.month
            hour = target_date.hour
            dow = target_date.weekday()
            
            # Base prediction from monthly average
            base_yield = monthly_avg.get(month, monthly_avg.mean())
            
            # Adjust for hour if available
            if (month, hour) in hourly_monthly_avg.index:
                hourly_factor = hourly_monthly_avg[(month, hour)] / monthly_avg[month]
                base_yield *= hourly_factor
            
            # Adjust for day of week
            dow_factor = dow_patterns.get(dow, 1.0) / dow_patterns.mean()
            base_yield *= dow_factor
            
            return max(0, base_yield)
        
        model = {
            'system_id': system_id,
            'monthly_avg': monthly_avg.to_dict(),
            'hourly_monthly_avg': hourly_monthly_avg.to_dict(),
            'dow_patterns': dow_patterns.to_dict(),
            'predict_function': predict_yield,
            'training_data_size': len(df),
            'training_date_range': (df['timestamp'].min(), df['timestamp'].max())
        }
        
        print(f"✅ Model created with {len(df)} training samples")
        
        return model
    
    def evaluate_model_accuracy(self, model: Dict[str, Any], test_data: pd.DataFrame) -> Dict[str, float]:
        """Evaluate model accuracy against test data"""
        print(f"📊 Evaluating model accuracy...")
        
        test_data['timestamp'] = pd.to_datetime(test_data['timestamp'])
        
        predictions = []
        actuals = []
        
        for _, row in test_data.iterrows():
            pred = model['predict_function'](row['timestamp'])
            predictions.append(pred)
            actuals.append(row['yield_today'])
        
        predictions = np.array(predictions)
        actuals = np.array(actuals)
        
        # Calculate metrics
        mae = np.mean(np.abs(predictions - actuals))
        rmse = np.sqrt(np.mean((predictions - actuals) ** 2))
        mape = np.mean(np.abs((predictions - actuals) / (actuals + 1e-8))) * 100
        
        # R-squared
        ss_res = np.sum((actuals - predictions) ** 2)
        ss_tot = np.sum((actuals - np.mean(actuals)) ** 2)
        r2 = 1 - (ss_res / (ss_tot + 1e-8))
        
        accuracy_pct = max(0, 100 - mape)
        
        metrics = {
            'mae': mae,
            'rmse': rmse,
            'mape': mape,
            'r2': r2,
            'accuracy_percent': accuracy_pct,
            'test_samples': len(test_data)
        }
        
        print(f"   MAE: {mae:.3f} kWh")
        print(f"   RMSE: {rmse:.3f} kWh")
        print(f"   MAPE: {mape:.1f}%")
        print(f"   R²: {r2:.3f}")
        print(f"   Accuracy: {accuracy_pct:.1f}%")
        
        return metrics
    
    def compare_systems(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Compare performance between systems"""
        print(f"\n🔍 COMPARING SYSTEMS")
        print("=" * 22)
        
        if 'system_1' not in analysis or 'system_2' not in analysis:
            print("❌ Cannot compare - missing data for one or both systems")
            return {}
        
        sys1 = analysis['system_1']['summary']
        sys2 = analysis['system_2']['summary']
        
        comparison = {
            'avg_yield_diff': sys1['avg_yield'] - sys2['avg_yield'],
            'avg_yield_diff_pct': ((sys1['avg_yield'] - sys2['avg_yield']) / sys2['avg_yield']) * 100,
            'max_yield_diff': sys1['max_yield'] - sys2['max_yield'],
            'records_diff': sys1['total_records'] - sys2['total_records'],
            'better_system': 'System 1' if sys1['avg_yield'] > sys2['avg_yield'] else 'System 2'
        }
        
        print(f"📊 System Comparison:")
        print(f"   System 1 avg: {sys1['avg_yield']:.2f} kWh")
        print(f"   System 2 avg: {sys2['avg_yield']:.2f} kWh")
        print(f"   Difference: {comparison['avg_yield_diff']:+.2f} kWh ({comparison['avg_yield_diff_pct']:+.1f}%)")
        print(f"   Better system: {comparison['better_system']}")
        
        return comparison

def main():
    """Main evaluation function"""
    print("🚀 SIMPLE MODEL RETRAINING & EVALUATION")
    print("=" * 50)
    print(f"Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        evaluator = SimpleModelEvaluator()
        
        # Step 1: Get all data
        all_data = evaluator.get_all_data()
        
        if not any(not df.empty for df in all_data.values()):
            print("❌ No data available for evaluation")
            return False
        
        # Step 2: Analyze temporal patterns
        analysis = evaluator.analyze_temporal_patterns(all_data)
        
        # Step 3: Create simple models and evaluate
        models = {}
        for system_key, df in all_data.items():
            if df.empty:
                continue
            
            system_id = int(system_key.split('_')[1])
            
            # Split data for testing (last 20%)
            split_idx = int(len(df) * 0.8)
            train_data = df.iloc[:split_idx]
            test_data = df.iloc[split_idx:]
            
            # Create model
            model = evaluator.create_simple_prediction_model(train_data, system_id)
            
            # Evaluate model
            metrics = evaluator.evaluate_model_accuracy(model, test_data)
            
            model['metrics'] = metrics
            models[system_key] = model
        
        # Step 4: Compare systems
        comparison = evaluator.compare_systems(analysis)
        
        print(f"\n🎉 EVALUATION COMPLETED!")
        print("✅ All available data analyzed")
        print("📊 Models created and evaluated")
        
        return {
            'analysis': analysis,
            'models': models,
            'comparison': comparison
        }
        
    except Exception as e:
        print(f"❌ Evaluation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
