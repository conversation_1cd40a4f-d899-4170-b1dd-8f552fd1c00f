#!/bin/bash

# Complete Solar Prediction System Startup Script
# Starts all services in the correct order with proper dependencies

echo "🌞 COMPLETE SOLAR PREDICTION SYSTEM STARTUP"
echo "============================================================"
echo "🚀 Starting all services with enhanced features..."
echo

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to check if port is available
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null ; then
        echo -e "${YELLOW}⚠️  Port $port is already in use${NC}"
        return 1
    else
        return 0
    fi
}

# Function to cleanup port
cleanup_port() {
    local port=$1
    local service_name=$2

    echo "🧹 Cleaning up port $port ($service_name)..."
    local pids=$(lsof -ti:$port 2>/dev/null)

    if [ -n "$pids" ]; then
        echo "   Stopping processes: $pids"
        for pid in $pids; do
            if ps -p $pid > /dev/null 2>&1; then
                kill $pid 2>/dev/null
                sleep 1
                if ps -p $pid > /dev/null 2>&1; then
                    kill -9 $pid 2>/dev/null
                fi
            fi
        done
        echo "   ✅ Port $port cleaned"
    fi
}

# Function to wait for service to be ready
wait_for_service() {
    local url=$1
    local name=$2
    local max_attempts=30
    local attempt=1
    
    echo -n "⏳ Waiting for $name to be ready..."
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url" > /dev/null 2>&1; then
            echo -e " ${GREEN}✅ Ready!${NC}"
            return 0
        fi
        echo -n "."
        sleep 2
        ((attempt++))
    done
    echo -e " ${RED}❌ Failed to start${NC}"
    return 1
}

# Change to project directory
cd /home/<USER>/solar-prediction-project

echo "📋 PHASE 1: Database & Core Infrastructure"
echo "------------------------------------------------------------"

# Check PostgreSQL
echo -n "🗄️  Checking PostgreSQL... "
if systemctl is-active --quiet postgresql; then
    echo -e "${GREEN}✅ Running${NC}"
else
    echo -e "${RED}❌ Not running${NC}"
    echo "Please start PostgreSQL: sudo systemctl start postgresql"
    exit 1
fi

echo
echo "📋 PHASE 2: Cleanup & Preparation"
echo "------------------------------------------------------------"

# Use centralized port manager for cleanup
echo "🧹 Using Port Manager for cleanup..."
./scripts/port_manager.sh cleanup

sleep 3

echo
echo "📋 PHASE 3: Data Collection Services"
echo "------------------------------------------------------------"

# Start SolaX Data Collector (one-time run)
echo "🔌 Running SolaX Data Collection..."
python3 scripts/data/solax_collector.py
echo -e "${GREEN}✅ SolaX Data collected${NC}"

# Start Weather Data Collector (one-time run)
echo "🌤️  Running Weather Data Collection..."
python3 scripts/data/weather_collector.py
echo -e "${GREEN}✅ Weather Data collected${NC}"

sleep 2

echo
echo "📋 PHASE 4: Core APIs"
echo "------------------------------------------------------------"

# Start Production Scripts API (Official 94.31% R² accuracy)
echo "🔮 Starting Production Scripts API..."
python3 scripts/production_scripts_api.py &
API_PID=$!
echo -e "${GREEN}✅ Production Scripts API started (PID: $API_PID)${NC}"

# Start GPU Prediction Service (Ultra-fast with caching)
echo "🚀 Starting GPU Prediction Service..."
python3 scripts/production/gpu_prediction_service.py &
GPU_PID=$!
echo -e "${GREEN}✅ GPU Prediction Service started (PID: $GPU_PID)${NC}"

# Start Prediction Scheduler (Daily automation)
echo "📅 Starting Prediction Scheduler..."
python3 scripts/production/prediction_scheduler.py &
SCHEDULER_PID=$!
echo -e "${GREEN}✅ Prediction Scheduler started (PID: $SCHEDULER_PID)${NC}"

# Start Charts API
echo "📊 Starting Charts API..."
python3 scripts/frontend_system/charts_api.py &
CHARTS_PID=$!
echo -e "${GREEN}✅ Charts API started (PID: $CHARTS_PID)${NC}"

# Start Unified Forecast API (Port 8120) with gpu_env
echo "🔮 Starting Unified Forecast API..."
if [ -d "gpu_env" ]; then
    echo "   📁 Found gpu_env directory"
    if [ -f "gpu_env/bin/activate" ]; then
        echo "   🔧 Activating gpu_env for Unified Forecast API..."
        source gpu_env/bin/activate && python scripts/frontend_system/unified_forecast_api.py &
        UNIFIED_PID=$!
        echo -e "${GREEN}✅ Unified Forecast API started with gpu_env (PID: $UNIFIED_PID)${NC}"
        echo -e "${GREEN}   🌐 Unified API URL: http://localhost:8120/health${NC}"
        echo -e "${GREEN}   🚀 GPU Integration: ENABLED${NC}"
    else
        echo -e "${YELLOW}⚠️ gpu_env/bin/activate not found - starting without GPU support${NC}"
        python3 scripts/frontend_system/unified_forecast_api.py &
        UNIFIED_PID=$!
        echo -e "${YELLOW}⚠️ Unified Forecast API started without gpu_env (PID: $UNIFIED_PID)${NC}"
    fi
else
    echo -e "${YELLOW}⚠️ gpu_env directory not found - starting without GPU support${NC}"
    python3 scripts/frontend_system/unified_forecast_api.py &
    UNIFIED_PID=$!
    echo -e "${YELLOW}⚠️ Unified Forecast API started without gpu_env (PID: $UNIFIED_PID)${NC}"
    echo -e "${YELLOW}   💡 For GPU support, create gpu_env: python3 -m venv gpu_env${NC}"
fi

# Start GPU Prediction Service (Port 8105) with gpu_env
echo "🚀 Starting GPU Prediction Service..."
if [ -d "gpu_env" ]; then
    echo "   📁 Found gpu_env directory"
    if [ -f "gpu_env/bin/activate" ]; then
        echo "   🔧 Activating gpu_env..."
        source gpu_env/bin/activate && python scripts/production/gpu_prediction_service.py &
        GPU_PID=$!
        echo -e "${GREEN}✅ GPU Prediction Service started with gpu_env (PID: $GPU_PID)${NC}"
        echo -e "${GREEN}   🌐 GPU Service URL: http://localhost:8105/health${NC}"
    else
        echo -e "${YELLOW}⚠️ gpu_env/bin/activate not found - skipping GPU service${NC}"
    fi
else
    echo -e "${YELLOW}⚠️ gpu_env directory not found - skipping GPU service${NC}"
    echo -e "${YELLOW}   💡 Run: python3 -m venv gpu_env && source gpu_env/bin/activate && pip install aiohttp asyncpg${NC}"
fi

# Start Prediction Scheduler (Port 8106) with gpu_env
echo "📅 Starting Prediction Scheduler..."
if [ -d "gpu_env" ]; then
    echo "   📁 Found gpu_env directory"
    if [ -f "gpu_env/bin/activate" ]; then
        echo "   🔧 Activating gpu_env..."
        source gpu_env/bin/activate && python scripts/production/prediction_scheduler.py &
        SCHEDULER_PID=$!
        echo -e "${GREEN}✅ Prediction Scheduler started with gpu_env (PID: $SCHEDULER_PID)${NC}"
        echo -e "${GREEN}   🌐 Scheduler URL: http://localhost:8106/health${NC}"
    else
        echo -e "${YELLOW}⚠️ gpu_env/bin/activate not found - skipping Prediction Scheduler${NC}"
    fi
else
    echo -e "${YELLOW}⚠️ gpu_env directory not found - skipping Prediction Scheduler${NC}"
fi

# Initialize Prediction Cache (Background) with gpu_env
echo "🚀 Initializing Prediction Cache..."
if [ -d "gpu_env" ]; then
    source gpu_env/bin/activate && python scripts/production/initialize_prediction_cache.py &
    CACHE_INIT_PID=$!
    echo -e "${GREEN}✅ Prediction Cache initialization started (PID: $CACHE_INIT_PID)${NC}"
else
    python3 scripts/production/initialize_prediction_cache.py &
    CACHE_INIT_PID=$!
    echo -e "${YELLOW}⚠️ Cache initialization with system python (PID: $CACHE_INIT_PID)${NC}"
fi

sleep 3

echo
echo "📋 PHASE 5: Enhanced Billing & Configuration"
echo "------------------------------------------------------------"

# Start Enhanced Billing System
echo "💰 Starting Enhanced Billing System..."
python3 scripts/frontend_system/enhanced_billing_system.py &
BILLING_PID=$!
echo -e "${GREEN}✅ Enhanced Billing started (PID: $BILLING_PID)${NC}"

# Start Configuration Manager
echo "⚙️  Starting Configuration Manager..."
python3 scripts/frontend_system/configuration_manager.py &
CONFIG_PID=$!
echo -e "${GREEN}✅ Configuration Manager started (PID: $CONFIG_PID)${NC}"

# Start Alert System
echo "🚨 Starting Alert System..."
python3 scripts/frontend_system/alert_system.py &
ALERT_PID=$!
echo -e "${GREEN}✅ Alert System started (PID: $ALERT_PID)${NC}"

sleep 3

echo
echo "📋 PHASE 6: Frontend & Communication"
echo "------------------------------------------------------------"

# Start Web Server for Frontends
echo "🌐 Starting Web Server..."
cd scripts/frontend_system
python3 -m http.server 8080 &
WEB_PID=$!
cd ../..
echo -e "${GREEN}✅ Web Server started (PID: $WEB_PID)${NC}"

# Start Telegram Bot
echo "🤖 Starting Telegram Bot..."
./telegram_bot_env/bin/python scripts/frontend_system/greek_telegram_bot.py &
TELEGRAM_PID=$!
echo -e "${GREEN}✅ Telegram Bot started (PID: $TELEGRAM_PID)${NC}"

sleep 5

echo
echo "📋 PHASE 7: Service Health Checks"
echo "------------------------------------------------------------"

# Health checks
wait_for_service "http://localhost:8100/health" "Enhanced Production API"
wait_for_service "http://localhost:8103/api/charts/statistics" "Charts API"
wait_for_service "http://localhost:8110/" "Enhanced Billing"
wait_for_service "http://localhost:8108/" "Configuration Manager"
wait_for_service "http://localhost:8080/" "Web Server"

echo
echo "🎉 SYSTEM STARTUP COMPLETE!"
echo "============================================================"
echo -e "${GREEN}✅ All services started successfully!${NC}"
echo
echo "📱 AVAILABLE SERVICES:"
echo "• 🌐 Web Frontends: http://localhost:8080/"
echo "• 📊 Charts Dashboard: http://localhost:8080/charts_dashboard.html"
echo "• 🏠 Production Frontend: http://localhost:8080/production_frontend.html"
echo "• 🔧 Advanced Monitoring: http://localhost:8080/advanced_monitoring_dashboard.html"
echo "• 💰 Enhanced Billing API: http://localhost:8110/"
echo "• ⚙️  Configuration Manager: http://localhost:8108/"
echo "• 🚨 Alert System: http://localhost:8105/"
echo "• 🤖 Telegram Bot: Active with enhanced financial commands"
echo
echo "🔧 ENHANCED FEATURES:"
echo "• ✅ Time-based tariffs (Winter/Summer schedules)"
echo "• ✅ Network charge tiers (3 consumption levels)"
echo "• ✅ Net Metering support (zero feed-in tariff)"
echo "• ✅ Alert Management UI with threshold configuration"
echo "• ✅ SOC → Φόρτιση Μπαταρίας (Greek terminology)"
echo "• ✅ Real-time temperature & GHI data collection"
echo "• ✅ Enhanced Telegram commands with financial analysis"
echo
echo "💡 To stop all services: pkill -f 'python3.*solar'"
echo "📝 Logs are available in each service terminal"
echo
echo "🌞 Solar Prediction System is ready for production use!"
echo
echo "🔍 GPU SERVICES STATUS:"
echo "======================"
if [ -d "gpu_env" ]; then
    echo -e "${GREEN}✅ GPU Environment: Available${NC}"
    if curl -s http://localhost:8105/health >/dev/null 2>&1; then
        echo -e "${GREEN}✅ GPU Prediction Service: Running (Port 8105)${NC}"
    else
        echo -e "${YELLOW}⚠️ GPU Prediction Service: Not responding${NC}"
    fi
    if curl -s http://localhost:8106/health >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Prediction Scheduler: Running (Port 8106)${NC}"
    else
        echo -e "${YELLOW}⚠️ Prediction Scheduler: Not responding${NC}"
    fi
    if curl -s http://localhost:8120/health >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Unified Forecast API: Running (Port 8120)${NC}"
        echo -e "${GREEN}   🚀 GPU Integration: ENABLED${NC}"
    else
        echo -e "${YELLOW}⚠️ Unified Forecast API: Not responding${NC}"
    fi
else
    echo -e "${YELLOW}⚠️ GPU Environment: Not found${NC}"
    echo -e "${YELLOW}   💡 To enable GPU services:${NC}"
    echo -e "${YELLOW}      python3 -m venv gpu_env${NC}"
    echo -e "${YELLOW}      source gpu_env/bin/activate${NC}"
    echo -e "${YELLOW}      pip install aiohttp asyncpg schedule requests fastapi uvicorn${NC}"
fi
