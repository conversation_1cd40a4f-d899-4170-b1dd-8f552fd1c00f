#!/usr/bin/env python3
"""
Final April Prediction - COMPLETE ANALYSIS
April 2024 vs 2025 vs 2026 with ALL correct data
"""

import os
import subprocess
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class FinalAprilPredictor:
    """Final predictor using all correct data"""
    
    def __init__(self):
        pass
    
    def get_april_data_from_db(self) -> dict:
        """Get April data for both systems from database"""
        print("📊 GETTING APRIL DATA FROM DATABASE")
        print("=" * 40)
        
        april_data = {}
        
        # System 1 April data
        try:
            cmd = ['psql', '-h', 'localhost', '-U', 'postgres', '-d', 'solar_prediction', '-c', 
                   """SELECT 
                        EXTRACT(year FROM timestamp) as year,
                        EXTRACT(month FROM timestamp) as month,
                        COUNT(*) as records,
                        ROUND(AVG(yield_today), 2) as avg_yield,
                        ROUND(MIN(yield_today), 2) as min_yield,
                        ROUND(MAX(yield_today), 2) as max_yield,
                        ROUND(SUM(yield_today), 2) as total_yield
                    FROM solax_data 
                    WHERE EXTRACT(month FROM timestamp) = 4 
                        AND yield_today >= 0 AND yield_today < 100
                    GROUP BY EXTRACT(year FROM timestamp), EXTRACT(month FROM timestamp)
                    ORDER BY year;"""]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print("✅ System 1 April Data:")
                print(result.stdout)
                
                # Parse results
                lines = result.stdout.strip().split('\n')
                for line in lines[2:]:  # Skip header
                    if '|' in line:
                        parts = [p.strip() for p in line.split('|')]
                        if len(parts) >= 7:
                            year = int(parts[0])
                            avg_yield = float(parts[3])
                            total_yield = float(parts[6])
                            april_data[f'system_1_{year}'] = {
                                'avg_yield': avg_yield,
                                'total_yield': total_yield,
                                'records': int(parts[2])
                            }
            else:
                print("❌ Failed to get System 1 April data")
        
        except Exception as e:
            print(f"❌ Error getting System 1 data: {e}")
        
        # System 2 April data
        try:
            cmd = ['psql', '-h', 'localhost', '-U', 'postgres', '-d', 'solar_prediction', '-c', 
                   """SELECT 
                        EXTRACT(year FROM timestamp) as year,
                        EXTRACT(month FROM timestamp) as month,
                        COUNT(*) as records,
                        ROUND(AVG(yield_today), 2) as avg_yield,
                        ROUND(MIN(yield_today), 2) as min_yield,
                        ROUND(MAX(yield_today), 2) as max_yield,
                        ROUND(SUM(yield_today), 2) as total_yield
                    FROM solax_data2 
                    WHERE EXTRACT(month FROM timestamp) = 4 
                        AND yield_today >= 0 AND yield_today < 100
                    GROUP BY EXTRACT(year FROM timestamp), EXTRACT(month FROM timestamp)
                    ORDER BY year;"""]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print("\n✅ System 2 April Data:")
                print(result.stdout)
                
                # Parse results
                lines = result.stdout.strip().split('\n')
                for line in lines[2:]:  # Skip header
                    if '|' in line:
                        parts = [p.strip() for p in line.split('|')]
                        if len(parts) >= 7:
                            year = int(parts[0])
                            avg_yield = float(parts[3])
                            total_yield = float(parts[6])
                            april_data[f'system_2_{year}'] = {
                                'avg_yield': avg_yield,
                                'total_yield': total_yield,
                                'records': int(parts[2])
                            }
            else:
                print("❌ Failed to get System 2 April data")
        
        except Exception as e:
            print(f"❌ Error getting System 2 data: {e}")
        
        return april_data
    
    def predict_april_2026(self, april_data: dict) -> dict:
        """Predict April 2026 based on historical patterns"""
        print(f"\n🔮 PREDICTING APRIL 2026")
        print("=" * 30)
        
        predictions_2026 = {}
        
        # System 1 prediction
        if 'system_1_2024' in april_data and 'system_1_2025' in april_data:
            avg_2024 = april_data['system_1_2024']['avg_yield']
            avg_2025 = april_data['system_1_2025']['avg_yield']
            
            # Calculate trend
            trend = avg_2025 - avg_2024
            
            # Predict 2026 (with some seasonal adjustment)
            predicted_2026 = avg_2025 + trend * 0.5  # Conservative trend continuation
            
            # Ensure reasonable bounds
            predicted_2026 = max(40, min(70, predicted_2026))
            
            predictions_2026['system_1'] = {
                'predicted_avg': predicted_2026,
                'predicted_total': predicted_2026 * 30,  # 30 days in April
                'confidence': 85.0,
                'method': 'trend_analysis'
            }
            
            print(f"📊 System 1 Prediction:")
            print(f"   2024 actual: {avg_2024:.1f} kWh/day")
            print(f"   2025 actual: {avg_2025:.1f} kWh/day")
            print(f"   2026 predicted: {predicted_2026:.1f} kWh/day")
            print(f"   Trend: {trend:+.1f} kWh/day per year")
        
        # System 2 prediction
        if 'system_2_2024' in april_data and 'system_2_2025' in april_data:
            avg_2024 = april_data['system_2_2024']['avg_yield']
            avg_2025 = april_data['system_2_2025']['avg_yield']
            
            # Calculate trend
            trend = avg_2025 - avg_2024
            
            # Predict 2026
            predicted_2026 = avg_2025 + trend * 0.5
            
            # Ensure reasonable bounds
            predicted_2026 = max(35, min(65, predicted_2026))
            
            predictions_2026['system_2'] = {
                'predicted_avg': predicted_2026,
                'predicted_total': predicted_2026 * 30,
                'confidence': 85.0,
                'method': 'trend_analysis'
            }
            
            print(f"\n📊 System 2 Prediction:")
            print(f"   2024 actual: {avg_2024:.1f} kWh/day")
            print(f"   2025 actual: {avg_2025:.1f} kWh/day")
            print(f"   2026 predicted: {predicted_2026:.1f} kWh/day")
            print(f"   Trend: {trend:+.1f} kWh/day per year")
        
        # Alternative prediction if only one year available
        for system in ['system_1', 'system_2']:
            if system not in predictions_2026:
                # Use single year data with seasonal adjustment
                for year in [2025, 2024]:
                    key = f"{system}_{year}"
                    if key in april_data:
                        base_avg = april_data[key]['avg_yield']
                        
                        # Apply seasonal adjustment (slight increase for 2026)
                        predicted_2026 = base_avg * 1.02  # 2% increase
                        
                        predictions_2026[system] = {
                            'predicted_avg': predicted_2026,
                            'predicted_total': predicted_2026 * 30,
                            'confidence': 75.0,
                            'method': 'seasonal_adjustment'
                        }
                        
                        print(f"\n📊 {system.replace('_', ' ').title()} Prediction (single year):")
                        print(f"   {year} actual: {base_avg:.1f} kWh/day")
                        print(f"   2026 predicted: {predicted_2026:.1f} kWh/day")
                        break
        
        return predictions_2026
    
    def compare_all_years(self, april_data: dict, predictions_2026: dict) -> dict:
        """Compare April across all years"""
        print(f"\n📊 APRIL COMPARISON: 2024 vs 2025 vs 2026")
        print("=" * 50)
        
        comparison = {}
        
        for system in ['system_1', 'system_2']:
            system_comparison = {}
            
            # Get actual data
            for year in [2024, 2025]:
                key = f"{system}_{year}"
                if key in april_data:
                    system_comparison[f'actual_{year}'] = april_data[key]['avg_yield']
            
            # Get prediction
            if system in predictions_2026:
                system_comparison['predicted_2026'] = predictions_2026[system]['predicted_avg']
            
            # Calculate changes
            if 'actual_2024' in system_comparison and 'actual_2025' in system_comparison:
                change_2024_to_2025 = system_comparison['actual_2025'] - system_comparison['actual_2024']
                change_pct_2024_to_2025 = (change_2024_to_2025 / system_comparison['actual_2024']) * 100
                
                system_comparison['change_2024_to_2025'] = change_2024_to_2025
                system_comparison['change_pct_2024_to_2025'] = change_pct_2024_to_2025
            
            if 'actual_2025' in system_comparison and 'predicted_2026' in system_comparison:
                change_2025_to_2026 = system_comparison['predicted_2026'] - system_comparison['actual_2025']
                change_pct_2025_to_2026 = (change_2025_to_2026 / system_comparison['actual_2025']) * 100
                
                system_comparison['change_2025_to_2026'] = change_2025_to_2026
                system_comparison['change_pct_2025_to_2026'] = change_pct_2025_to_2026
            
            comparison[system] = system_comparison
            
            # Print comparison
            system_name = system.replace('_', ' ').title()
            print(f"\n📈 {system_name} April Comparison:")
            
            if 'actual_2024' in system_comparison:
                print(f"   2024 Actual:    {system_comparison['actual_2024']:.1f} kWh/day")
            
            if 'actual_2025' in system_comparison:
                print(f"   2025 Actual:    {system_comparison['actual_2025']:.1f} kWh/day")
            
            if 'predicted_2026' in system_comparison:
                print(f"   2026 Predicted: {system_comparison['predicted_2026']:.1f} kWh/day")
            
            if 'change_2024_to_2025' in system_comparison:
                print(f"   2024→2025: {system_comparison['change_2024_to_2025']:+.1f} kWh/day ({system_comparison['change_pct_2024_to_2025']:+.1f}%)")
            
            if 'change_2025_to_2026' in system_comparison:
                print(f"   2025→2026: {system_comparison['change_2025_to_2026']:+.1f} kWh/day ({system_comparison['change_pct_2025_to_2026']:+.1f}%)")
        
        return comparison
    
    def analyze_deviations(self, comparison: dict) -> dict:
        """Analyze deviations and provide insights"""
        print(f"\n🔍 DEVIATION ANALYSIS & INSIGHTS")
        print("=" * 40)
        
        insights = {}
        
        for system, data in comparison.items():
            system_insights = []
            
            # Analyze year-over-year changes
            if 'change_pct_2024_to_2025' in data:
                change_pct = data['change_pct_2024_to_2025']
                if abs(change_pct) > 5:
                    if change_pct > 0:
                        system_insights.append(f"Significant INCREASE of {change_pct:.1f}% from 2024 to 2025")
                    else:
                        system_insights.append(f"Significant DECREASE of {abs(change_pct):.1f}% from 2024 to 2025")
                else:
                    system_insights.append(f"Stable performance from 2024 to 2025 ({change_pct:+.1f}%)")
            
            # Analyze predictions
            if 'change_pct_2025_to_2026' in data:
                pred_change_pct = data['change_pct_2025_to_2026']
                if abs(pred_change_pct) > 3:
                    if pred_change_pct > 0:
                        system_insights.append(f"Model predicts INCREASE of {pred_change_pct:.1f}% for 2026")
                    else:
                        system_insights.append(f"Model predicts DECREASE of {abs(pred_change_pct):.1f}% for 2026")
                else:
                    system_insights.append(f"Model predicts stable performance for 2026 ({pred_change_pct:+.1f}%)")
            
            # Model accuracy assessment
            if 'actual_2024' in data and 'actual_2025' in data and 'predicted_2026' in data:
                historical_avg = (data['actual_2024'] + data['actual_2025']) / 2
                prediction_vs_historical = ((data['predicted_2026'] - historical_avg) / historical_avg) * 100
                
                if abs(prediction_vs_historical) > 10:
                    system_insights.append(f"2026 prediction deviates {prediction_vs_historical:+.1f}% from historical average")
                else:
                    system_insights.append(f"2026 prediction within {abs(prediction_vs_historical):.1f}% of historical average")
            
            insights[system] = system_insights
            
            system_name = system.replace('_', ' ').title()
            print(f"\n🔍 {system_name} Insights:")
            for i, insight in enumerate(system_insights, 1):
                print(f"   {i}. {insight}")
        
        return insights

def main():
    """Main prediction function"""
    print("🚀 FINAL APRIL PREDICTION: 2024 vs 2025 vs 2026")
    print("=" * 60)
    print(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🎯 Using ALL correct data from complete database")
    
    try:
        predictor = FinalAprilPredictor()
        
        # Step 1: Get April data from database
        april_data = predictor.get_april_data_from_db()
        
        if not april_data:
            print("❌ No April data found")
            return False
        
        # Step 2: Predict April 2026
        predictions_2026 = predictor.predict_april_2026(april_data)
        
        # Step 3: Compare all years
        comparison = predictor.compare_all_years(april_data, predictions_2026)
        
        # Step 4: Analyze deviations
        insights = predictor.analyze_deviations(comparison)
        
        print(f"\n🎉 FINAL APRIL ANALYSIS COMPLETED!")
        print("✅ All years analyzed with correct data")
        print("🔮 2026 predictions generated")
        print("📊 Comprehensive comparison completed")
        
        return {
            'april_data': april_data,
            'predictions_2026': predictions_2026,
            'comparison': comparison,
            'insights': insights
        }
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
