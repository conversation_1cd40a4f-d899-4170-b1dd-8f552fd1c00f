#!/usr/bin/env python3
"""
Real CSV Import - Import ALL actual data from CSV
"""

import csv
import os
import subprocess
from datetime import datetime

def process_csv_and_create_sql():
    """Process CSV and create SQL with ALL real data"""
    print("🔄 PROCESSING REAL CSV DATA")
    print("=" * 40)
    
    csv_file = "data/raw/System2/Plant Reports 2024-03-01-2024-06-28.csv"
    
    if not os.path.exists(csv_file):
        print(f"❌ CSV file not found: {csv_file}")
        return None
    
    sql_statements = []
    
    # Add table setup
    sql_statements.append("""
-- Real CSV Import for System 2
CREATE TABLE IF NOT EXISTS solax_data2 (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMP NOT NULL,
    inverter_sn VARCHAR(50),
    wifi_sn VARCHAR(50),
    yield_today DECIMAL(10,2),
    soc DECIMAL(5,2),
    bat_power DECIMAL(10,2),
    temperature DECIMAL(5,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_solax_data2_timestamp ON solax_data2(timestamp);

-- Clear existing CSV import data
DELETE FROM solax_data2 WHERE inverter_sn = 'SYSTEM2_CSV_IMPORT';
""")
    
    try:
        with open(csv_file, 'r', encoding='utf-8') as f:
            # Read all lines
            lines = f.readlines()
        
        print(f"Total lines in CSV: {len(lines)}")
        
        # Process header
        header_line = lines[0].strip()
        print(f"Header: {header_line}")
        
        # Process data lines
        data_lines = lines[1:]  # Skip header
        
        valid_records = 0
        batch_size = 100
        current_batch = []
        
        for line_num, line in enumerate(data_lines, 2):  # Start from line 2
            line = line.strip()
            if not line:
                continue
            
            try:
                # Parse CSV line manually
                # Format: "No,Update time,Daily PV Yield(kWh),Daily inverter output (kWh),..."
                parts = line.split(',')
                
                if len(parts) >= 4:
                    # Extract timestamp (index 1) and yield (index 3)
                    timestamp_str = parts[1].strip('"')
                    yield_str = parts[3].strip('"')
                    
                    # Validate and parse
                    dt = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
                    yield_val = float(yield_str)
                    
                    # Add to batch
                    current_batch.append((dt, yield_val))
                    valid_records += 1
                    
                    # Process batch when full
                    if len(current_batch) >= batch_size:
                        sql_statements.append(create_batch_insert(current_batch))
                        current_batch = []
                    
                    if valid_records % 1000 == 0:
                        print(f"   Processed {valid_records} valid records...")
                
            except (ValueError, IndexError) as e:
                # Skip invalid lines
                continue
        
        # Process remaining batch
        if current_batch:
            sql_statements.append(create_batch_insert(current_batch))
        
        print(f"✅ Processing completed:")
        print(f"   Valid records: {valid_records}")
        
        # Write SQL file
        sql_file = "real_system2_import.sql"
        with open(sql_file, 'w') as f:
            f.write('\n'.join(sql_statements))
        
        print(f"✅ SQL file created: {sql_file}")
        
        return sql_file, valid_records
        
    except Exception as e:
        print(f"❌ Error processing CSV: {e}")
        return None, 0

def create_batch_insert(batch):
    """Create batch INSERT statement"""
    values = []
    for dt, yield_val in batch:
        values.append(f"('{dt}', 'SYSTEM2_CSV_IMPORT', 'SYSTEM2_CSV_IMPORT', {yield_val}, 50.0, 0.0, 20.0)")
    
    return f"""
INSERT INTO solax_data2 (timestamp, inverter_sn, wifi_sn, yield_today, soc, bat_power, temperature) VALUES
{', '.join(values)};
"""

def execute_sql_import(sql_file):
    """Execute SQL import"""
    print(f"\n💾 EXECUTING REAL DATA IMPORT")
    print("=" * 35)
    
    try:
        cmd = ['psql', '-h', 'localhost', '-U', 'postgres', '-d', 'solar_prediction', '-f', sql_file]
        
        print(f"Executing: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ SQL execution successful!")
            return True
        else:
            print(f"❌ SQL execution failed!")
            print("Error:", result.stderr[-500:] if result.stderr else "No error")
            return False
            
    except Exception as e:
        print(f"❌ Error executing SQL: {e}")
        return False

def verify_import():
    """Verify the import"""
    print(f"\n📊 VERIFYING REAL DATA IMPORT")
    print("=" * 35)
    
    try:
        cmd = ['psql', '-h', 'localhost', '-U', 'postgres', '-d', 'solar_prediction', '-c', 
               """SELECT 
                    COUNT(*) as total_records,
                    MIN(DATE(timestamp)) as earliest_date,
                    MAX(DATE(timestamp)) as latest_date,
                    COUNT(DISTINCT DATE(timestamp)) as unique_days,
                    MIN(yield_today) as min_yield,
                    MAX(yield_today) as max_yield
                FROM solax_data2 
                WHERE inverter_sn = 'SYSTEM2_CSV_IMPORT';"""]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ Verification successful!")
            print("Results:")
            print(result.stdout)
            
            # Check April 2024 specifically
            cmd2 = ['psql', '-h', 'localhost', '-U', 'postgres', '-d', 'solar_prediction', '-c', 
                   """SELECT 
                        COUNT(*) as april_2024_records,
                        MIN(DATE(timestamp)) as first_april_date,
                        MAX(DATE(timestamp)) as last_april_date
                    FROM solax_data2 
                    WHERE inverter_sn = 'SYSTEM2_CSV_IMPORT'
                        AND EXTRACT(year FROM timestamp) = 2024
                        AND EXTRACT(month FROM timestamp) = 4;"""]
            
            result2 = subprocess.run(cmd2, capture_output=True, text=True, timeout=30)
            
            if result2.returncode == 0:
                print("\n📅 April 2024 Data:")
                print(result2.stdout)
            
            return True
        else:
            print(f"❌ Verification failed!")
            print("Error:", result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Error verifying data: {e}")
        return False

def main():
    """Main import function"""
    print("🚀 REAL CSV IMPORT - ALL ACTUAL DATA")
    print("=" * 50)
    print(f"Import started: {datetime.now()}")
    
    # Step 1: Process CSV and create SQL
    sql_file, record_count = process_csv_and_create_sql()
    
    if not sql_file or record_count == 0:
        print("❌ Failed to process CSV")
        return False
    
    # Step 2: Execute SQL import
    if not execute_sql_import(sql_file):
        print("❌ Failed to execute SQL import")
        return False
    
    # Step 3: Verify import
    if not verify_import():
        print("❌ Failed to verify import")
        return False
    
    print(f"\n🎉 REAL DATA IMPORT COMPLETED!")
    print(f"✅ {record_count} actual records imported")
    print("🔄 Ready for real April 2024 vs 2025 vs 2026 comparison")
    
    # Clean up
    try:
        os.remove(sql_file)
        print(f"🗑️  Cleaned up SQL file")
    except:
        pass
    
    return True

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🚀 NOW RUNNING REAL APRIL COMPARISON...")
        os.system("python3 scripts/final_april_comparison.py")
    
    exit(0 if success else 1)
