#!/usr/bin/env python3
"""
Basic Evaluation - No dependencies
Evaluate model performance using basic Python only
"""

import os
import psycopg2
from datetime import datetime
from collections import defaultdict
import statistics

def get_db_connection():
    """Get database connection"""
    try:
        return psycopg2.connect(
            host="localhost",
            database="solar_prediction",
            user="postgres",
            password="postgres",
            port="5432"
        )
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return None

def analyze_system_data():
    """Analyze data for both systems"""
    print("🚀 BASIC MODEL EVALUATION")
    print("=" * 30)
    
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        with conn.cursor() as cur:
            # System 1 analysis
            print("\n📊 SYSTEM 1 ANALYSIS")
            print("=" * 25)
            
            cur.execute("""
                SELECT 
                    COUNT(*) as total_records,
                    MIN(timestamp) as earliest,
                    MAX(timestamp) as latest,
                    AVG(yield_today) as avg_yield,
                    MIN(yield_today) as min_yield,
                    MAX(yield_today) as max_yield,
                    STDDEV(yield_today) as std_yield
                FROM solax_data
                WHERE yield_today >= 0 AND yield_today < 100
            """)
            
            result = cur.fetchone()
            if result and result[0] > 0:
                print(f"✅ System 1 Data:")
                print(f"   Total records: {result[0]:,}")
                print(f"   Date range: {result[1]} to {result[2]}")
                print(f"   Average yield: {result[3]:.2f} kWh")
                print(f"   Min yield: {result[4]:.2f} kWh")
                print(f"   Max yield: {result[5]:.2f} kWh")
                print(f"   Std deviation: {result[6]:.2f} kWh" if result[6] else "N/A")
                
                sys1_data = {
                    'total_records': result[0],
                    'date_range': (result[1], result[2]),
                    'avg_yield': result[3],
                    'min_yield': result[4],
                    'max_yield': result[5],
                    'std_yield': result[6] if result[6] else 0
                }
            else:
                print("❌ No data for System 1")
                sys1_data = None
            
            # System 2 analysis
            print("\n📊 SYSTEM 2 ANALYSIS")
            print("=" * 25)
            
            cur.execute("""
                SELECT 
                    COUNT(*) as total_records,
                    MIN(timestamp) as earliest,
                    MAX(timestamp) as latest,
                    AVG(yield_today) as avg_yield,
                    MIN(yield_today) as min_yield,
                    MAX(yield_today) as max_yield,
                    STDDEV(yield_today) as std_yield
                FROM solax_data2
                WHERE yield_today >= 0 AND yield_today < 100
            """)
            
            result = cur.fetchone()
            if result and result[0] > 0:
                print(f"✅ System 2 Data:")
                print(f"   Total records: {result[0]:,}")
                print(f"   Date range: {result[1]} to {result[2]}")
                print(f"   Average yield: {result[3]:.2f} kWh")
                print(f"   Min yield: {result[4]:.2f} kWh")
                print(f"   Max yield: {result[5]:.2f} kWh")
                print(f"   Std deviation: {result[6]:.2f} kWh" if result[6] else "N/A")
                
                sys2_data = {
                    'total_records': result[0],
                    'date_range': (result[1], result[2]),
                    'avg_yield': result[3],
                    'min_yield': result[4],
                    'max_yield': result[5],
                    'std_yield': result[6] if result[6] else 0
                }
            else:
                print("❌ No data for System 2")
                sys2_data = None
            
            # Monthly patterns for System 1
            if sys1_data:
                print("\n📅 SYSTEM 1 MONTHLY PATTERNS")
                print("=" * 35)
                
                cur.execute("""
                    SELECT 
                        EXTRACT(month FROM timestamp) as month,
                        COUNT(*) as records,
                        AVG(yield_today) as avg_yield,
                        MIN(yield_today) as min_yield,
                        MAX(yield_today) as max_yield
                    FROM solax_data
                    WHERE yield_today >= 0 AND yield_today < 100
                    GROUP BY EXTRACT(month FROM timestamp)
                    ORDER BY month
                """)
                
                monthly_results = cur.fetchall()
                for row in monthly_results:
                    month_name = ["", "Jan", "Feb", "Mar", "Apr", "May", "Jun", 
                                 "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"][int(row[0])]
                    print(f"   {month_name}: {row[2]:.1f} kWh avg ({row[1]} records)")
            
            # Monthly patterns for System 2
            if sys2_data:
                print("\n📅 SYSTEM 2 MONTHLY PATTERNS")
                print("=" * 35)
                
                cur.execute("""
                    SELECT 
                        EXTRACT(month FROM timestamp) as month,
                        COUNT(*) as records,
                        AVG(yield_today) as avg_yield,
                        MIN(yield_today) as min_yield,
                        MAX(yield_today) as max_yield
                    FROM solax_data2
                    WHERE yield_today >= 0 AND yield_today < 100
                    GROUP BY EXTRACT(month FROM timestamp)
                    ORDER BY month
                """)
                
                monthly_results = cur.fetchall()
                for row in monthly_results:
                    month_name = ["", "Jan", "Feb", "Mar", "Apr", "May", "Jun", 
                                 "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"][int(row[0])]
                    print(f"   {month_name}: {row[2]:.1f} kWh avg ({row[1]} records)")
            
            # System comparison
            if sys1_data and sys2_data:
                print("\n🔍 SYSTEM COMPARISON")
                print("=" * 22)
                
                diff_avg = sys1_data['avg_yield'] - sys2_data['avg_yield']
                diff_pct = (diff_avg / sys2_data['avg_yield']) * 100
                
                print(f"📊 Average Yield Comparison:")
                print(f"   System 1: {sys1_data['avg_yield']:.2f} kWh")
                print(f"   System 2: {sys2_data['avg_yield']:.2f} kWh")
                print(f"   Difference: {diff_avg:+.2f} kWh ({diff_pct:+.1f}%)")
                
                if diff_avg > 0:
                    print(f"   🏆 System 1 performs {abs(diff_pct):.1f}% better")
                else:
                    print(f"   🏆 System 2 performs {abs(diff_pct):.1f}% better")
                
                print(f"\n📊 Data Coverage Comparison:")
                print(f"   System 1: {sys1_data['total_records']:,} records")
                print(f"   System 2: {sys2_data['total_records']:,} records")
                
                # Check for April 2024 data specifically
                print(f"\n📅 APRIL 2024 DATA CHECK")
                print("=" * 27)
                
                # System 1 April 2024
                cur.execute("""
                    SELECT COUNT(*), AVG(yield_today), MIN(DATE(timestamp)), MAX(DATE(timestamp))
                    FROM solax_data
                    WHERE EXTRACT(year FROM timestamp) = 2024
                        AND EXTRACT(month FROM timestamp) = 4
                        AND yield_today >= 0 AND yield_today < 100
                """)
                
                result = cur.fetchone()
                if result and result[0] > 0:
                    print(f"✅ System 1 April 2024: {result[0]} records, {result[1]:.1f} kWh avg")
                    print(f"   Date range: {result[2]} to {result[3]}")
                else:
                    print("❌ No System 1 data for April 2024")
                
                # System 2 April 2024
                cur.execute("""
                    SELECT COUNT(*), AVG(yield_today), MIN(DATE(timestamp)), MAX(DATE(timestamp))
                    FROM solax_data2
                    WHERE EXTRACT(year FROM timestamp) = 2024
                        AND EXTRACT(month FROM timestamp) = 4
                        AND yield_today >= 0 AND yield_today < 100
                """)
                
                result = cur.fetchone()
                if result and result[0] > 0:
                    print(f"✅ System 2 April 2024: {result[0]} records, {result[1]:.1f} kWh avg")
                    print(f"   Date range: {result[2]} to {result[3]}")
                else:
                    print("❌ No System 2 data for April 2024")
                
                # Check for April 2025 data
                print(f"\n📅 APRIL 2025 DATA CHECK")
                print("=" * 27)
                
                # System 1 April 2025
                cur.execute("""
                    SELECT COUNT(*), AVG(yield_today), MIN(DATE(timestamp)), MAX(DATE(timestamp))
                    FROM solax_data
                    WHERE EXTRACT(year FROM timestamp) = 2025
                        AND EXTRACT(month FROM timestamp) = 4
                        AND yield_today >= 0 AND yield_today < 100
                """)
                
                result = cur.fetchone()
                if result and result[0] > 0:
                    print(f"✅ System 1 April 2025: {result[0]} records, {result[1]:.1f} kWh avg")
                    print(f"   Date range: {result[2]} to {result[3]}")
                else:
                    print("❌ No System 1 data for April 2025")
                
                # System 2 April 2025
                cur.execute("""
                    SELECT COUNT(*), AVG(yield_today), MIN(DATE(timestamp)), MAX(DATE(timestamp))
                    FROM solax_data2
                    WHERE EXTRACT(year FROM timestamp) = 2025
                        AND EXTRACT(month FROM timestamp) = 4
                        AND yield_today >= 0 AND yield_today < 100
                """)
                
                result = cur.fetchone()
                if result and result[0] > 0:
                    print(f"✅ System 2 April 2025: {result[0]} records, {result[1]:.1f} kWh avg")
                    print(f"   Date range: {result[2]} to {result[3]}")
                else:
                    print("❌ No System 2 data for April 2025")
            
            print(f"\n🎉 BASIC EVALUATION COMPLETED!")
            print("✅ Data analysis finished")
            
            return True
            
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        conn.close()

def main():
    """Main function"""
    print("🚀 BASIC MODEL EVALUATION")
    print("=" * 30)
    print(f"Started: {datetime.now()}")
    
    success = analyze_system_data()
    
    if success:
        print("\n✅ Evaluation completed successfully!")
    else:
        print("\n❌ Evaluation failed!")
    
    return success

if __name__ == "__main__":
    main()
