#!/usr/bin/env python3
"""
Database Schema Fix Script
Adds missing columns to existing tables
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine, text
from src.core.config import settings
from loguru import logger

def fix_schedule_tasks_table():
    """Add missing error_details column to schedule_tasks table"""
    engine = create_engine(settings.DATABASE_URL)

    try:
        with engine.connect() as conn:
            # Check if column exists
            result = conn.execute(text("""
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name = 'schedule_tasks'
                AND column_name = 'error_details'
            """))

            if not result.fetchone():
                logger.info("Adding error_details column to schedule_tasks table...")
                conn.execute(text("ALTER TABLE schedule_tasks ADD COLUMN error_details JSON"))
                conn.commit()
                logger.success("✅ Added error_details column")
            else:
                logger.info("✅ error_details column already exists")

    except Exception as e:
        logger.error(f"❌ Failed to fix schedule_tasks table: {e}")
        return False

    return True

def fix_json_serialization_issues():
    """Fix JSON serialization issues in raw_data columns"""
    engine = create_engine(settings.DATABASE_URL)

    try:
        with engine.connect() as conn:
            # Check if we need to update the raw_data column type
            logger.info("Checking raw_data column types...")

            # For SolaX data table
            result = conn.execute(text("""
                SELECT data_type
                FROM information_schema.columns
                WHERE table_name = 'solax_data'
                AND column_name = 'raw_data'
            """))

            data_type = result.fetchone()
            if data_type and data_type[0] != 'json':
                logger.info("Updating solax_data.raw_data column type to JSON...")
                conn.execute(text("ALTER TABLE solax_data ALTER COLUMN raw_data TYPE JSON USING raw_data::JSON"))
                conn.commit()
                logger.success("✅ Updated solax_data.raw_data column type")

            # For Weather data table
            result = conn.execute(text("""
                SELECT data_type
                FROM information_schema.columns
                WHERE table_name = 'weather_data'
                AND column_name = 'raw_data'
            """))

            data_type = result.fetchone()
            if data_type and data_type[0] != 'json':
                logger.info("Updating weather_data.raw_data column type to JSON...")
                conn.execute(text("ALTER TABLE weather_data ALTER COLUMN raw_data TYPE JSON USING raw_data::JSON"))
                conn.commit()
                logger.success("✅ Updated weather_data.raw_data column type")

    except Exception as e:
        logger.error(f"❌ Failed to fix JSON serialization: {e}")
        return False

    return True

def main():
    """Run all database fixes"""
    logger.info("🔧 Starting database schema fixes...")

    success = True

    # Fix schedule_tasks table
    if not fix_schedule_tasks_table():
        success = False

    # Fix JSON serialization issues
    if not fix_json_serialization_issues():
        success = False

    if success:
        logger.success("🎉 All database schema fixes completed successfully!")
    else:
        logger.error("❌ Some database fixes failed")
        sys.exit(1)

if __name__ == "__main__":
    main()
