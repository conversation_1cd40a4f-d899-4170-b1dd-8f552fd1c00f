#!/usr/bin/env python3
"""
Database Consolidation Script
Consolidate all data into single solar_prediction database
"""

import psycopg2
import subprocess
import os
import sys
from datetime import datetime

# Database configurations
MAIN_DB = {
    'host': 'localhost',
    'user': 'postgres',
    'password': 'postgres',
    'database': 'solar_prediction'
}

OLD_DB = {
    'host': 'localhost',
    'user': 'mining_user',
    'password': 'postgres',
    'database': 'solarpredict_db'
}

BACKUP_FILE = "data/Backup/performance_optimization_backup_20250416_160924_full.sql"

def check_current_status():
    """Check current database status"""
    print("🔍 CHECKING CURRENT DATABASE STATUS")
    print("=" * 50)
    
    try:
        # Check main database
        conn = psycopg2.connect(**MAIN_DB)
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM solax_data;")
        main_solax = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM weather_data;")
        main_weather = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM normalized_training_data;")
        main_training = cursor.fetchone()[0]
        
        conn.close()
        
        print(f"📊 Main Database (solar_prediction):")
        print(f"   SolaX data: {main_solax:,}")
        print(f"   Weather data: {main_weather:,}")
        print(f"   Training data: {main_training:,}")
        
        # Check old database
        try:
            conn = psycopg2.connect(**OLD_DB)
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM normalized_training_data;")
            old_training = cursor.fetchone()[0]
            
            conn.close()
            
            print(f"📊 Old Database (solarpredict_db):")
            print(f"   Training data: {old_training:,}")
            
        except Exception as e:
            print(f"⚠️  Old database not accessible: {e}")
            old_training = 0
        
        # Check backup file
        backup_exists = os.path.exists(BACKUP_FILE)
        backup_size = os.path.getsize(BACKUP_FILE) / (1024*1024) if backup_exists else 0
        
        print(f"📄 Backup File:")
        print(f"   Exists: {'✅' if backup_exists else '❌'}")
        print(f"   Size: {backup_size:.1f} MB")
        
        return {
            'main_solax': main_solax,
            'main_weather': main_weather,
            'main_training': main_training,
            'old_training': old_training,
            'backup_exists': backup_exists
        }
        
    except Exception as e:
        print(f"❌ Error checking status: {e}")
        return None

def backup_current_data():
    """Create backup of current main database"""
    print("💾 CREATING BACKUP OF CURRENT DATA")
    print("-" * 40)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_filename = f"data/current_backup_{timestamp}.sql"
    
    try:
        cmd = [
            'pg_dump',
            '-h', 'localhost',
            '-U', 'postgres',
            '-d', 'solar_prediction',
            '-f', backup_filename,
            '--verbose'
        ]
        
        print(f"📄 Creating backup: {backup_filename}")
        result = subprocess.run(cmd, capture_output=True, text=True, 
                              env={**os.environ, 'PGPASSWORD': 'postgres'})
        
        if result.returncode == 0:
            size = os.path.getsize(backup_filename) / (1024*1024)
            print(f"✅ Backup created successfully ({size:.1f} MB)")
            return backup_filename
        else:
            print(f"❌ Backup failed: {result.stderr}")
            return None
            
    except Exception as e:
        print(f"❌ Backup error: {e}")
        return None

def drop_old_database():
    """Drop the old solarpredict_db database"""
    print("🗑️  DROPPING OLD DATABASE")
    print("-" * 40)
    
    try:
        # Connect to postgres database to drop solarpredict_db
        conn = psycopg2.connect(
            host='localhost',
            user='postgres',
            password='postgres',
            database='postgres'
        )
        conn.autocommit = True
        cursor = conn.cursor()
        
        # Terminate connections to the database
        cursor.execute("""
            SELECT pg_terminate_backend(pid)
            FROM pg_stat_activity
            WHERE datname = 'solarpredict_db' AND pid <> pg_backend_pid();
        """)
        
        # Drop the database
        cursor.execute("DROP DATABASE IF EXISTS solarpredict_db;")
        
        conn.close()
        
        print("✅ Old database dropped successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error dropping old database: {e}")
        return False

def restore_historical_data():
    """Restore historical data from backup to main database"""
    print("📥 RESTORING HISTORICAL DATA FROM BACKUP")
    print("-" * 40)
    
    if not os.path.exists(BACKUP_FILE):
        print(f"❌ Backup file not found: {BACKUP_FILE}")
        return False
    
    try:
        # First, let's extract only the data we need
        print("🔍 Extracting relevant data from backup...")
        
        # Create a temporary SQL file with only the data we want
        temp_sql = "temp_historical_data.sql"
        
        with open(BACKUP_FILE, 'r') as backup, open(temp_sql, 'w') as temp:
            in_solax_data = False
            in_weather_data = False
            in_training_data = False
            
            for line in backup:
                # Check for table data sections
                if "COPY public.solax_data" in line:
                    in_solax_data = True
                    temp.write(line)
                elif "COPY public.weather_data" in line:
                    in_weather_data = True
                    temp.write(line)
                elif "COPY public.normalized_training_data" in line:
                    in_training_data = True
                    temp.write(line)
                elif line.strip() == "\\." and (in_solax_data or in_weather_data or in_training_data):
                    temp.write(line)
                    in_solax_data = False
                    in_weather_data = False
                    in_training_data = False
                elif in_solax_data or in_weather_data or in_training_data:
                    temp.write(line)
        
        print(f"✅ Extracted data to {temp_sql}")
        
        # Restore the extracted data
        print("📥 Importing historical data...")
        
        cmd = [
            'psql',
            '-h', 'localhost',
            '-U', 'postgres',
            '-d', 'solar_prediction',
            '-f', temp_sql,
            '-v', 'ON_ERROR_STOP=1'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True,
                              env={**os.environ, 'PGPASSWORD': 'postgres'})
        
        # Clean up temp file
        os.remove(temp_sql)
        
        if result.returncode == 0:
            print("✅ Historical data imported successfully")
            return True
        else:
            print(f"❌ Import failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error restoring data: {e}")
        return False

def verify_consolidation():
    """Verify the consolidation was successful"""
    print("✅ VERIFYING CONSOLIDATION")
    print("-" * 40)
    
    try:
        conn = psycopg2.connect(**MAIN_DB)
        cursor = conn.cursor()
        
        # Check final counts
        cursor.execute("SELECT COUNT(*) FROM solax_data;")
        final_solax = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM weather_data;")
        final_weather = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM normalized_training_data;")
        final_training = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM predictions;")
        final_predictions = cursor.fetchone()[0]
        
        # Check date ranges
        cursor.execute("SELECT MIN(timestamp), MAX(timestamp) FROM solax_data;")
        solax_range = cursor.fetchone()
        
        cursor.execute("SELECT MIN(timestamp), MAX(timestamp) FROM weather_data;")
        weather_range = cursor.fetchone()
        
        conn.close()
        
        print(f"📊 Final Database Status:")
        print(f"   SolaX data: {final_solax:,} records")
        print(f"   Weather data: {final_weather:,} records")
        print(f"   Training data: {final_training:,} records")
        print(f"   Predictions: {final_predictions:,} records")
        print()
        
        if solax_range[0]:
            solax_days = (solax_range[1] - solax_range[0]).days
            print(f"📅 SolaX Coverage: {solax_days} days ({solax_range[0]} → {solax_range[1]})")
        
        if weather_range[0]:
            weather_days = (weather_range[1] - weather_range[0]).days
            print(f"📅 Weather Coverage: {weather_days} days ({weather_range[0]} → {weather_range[1]})")
        
        # Check if we have only one database now
        conn = psycopg2.connect(
            host='localhost',
            user='postgres',
            password='postgres',
            database='postgres'
        )
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT datname FROM pg_database 
            WHERE datname IN ('solar_prediction', 'solarpredict_db')
            ORDER BY datname;
        """)
        
        databases = [row[0] for row in cursor.fetchall()]
        conn.close()
        
        print(f"🗄️  Active Databases: {databases}")
        
        if len(databases) == 1 and databases[0] == 'solar_prediction':
            print("✅ Database consolidation successful!")
            return True
        else:
            print("⚠️  Consolidation may not be complete")
            return False
            
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        return False

def main():
    """Main consolidation function"""
    print("🔧 SOLAR PREDICTION DATABASE CONSOLIDATION")
    print("=" * 60)
    print(f"🕐 Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Step 1: Check current status
    status = check_current_status()
    if not status:
        print("❌ Failed to check current status")
        return
    
    print()
    
    # Step 2: Create backup of current data
    backup_file = backup_current_data()
    if not backup_file:
        print("❌ Failed to create backup - aborting")
        return
    
    print()
    
    # Step 3: Ask for confirmation
    print("⚠️  CONSOLIDATION PLAN")
    print("-" * 30)
    print("This will:")
    print("1. ✅ Keep main database (solar_prediction)")
    print("2. 🗑️  Drop old database (solarpredict_db)")
    print("3. 📥 Import historical data from backup")
    print("4. ✅ Verify consolidation")
    print()
    print(f"📄 Current backup saved: {backup_file}")
    print()
    
    try:
        confirm = input("Proceed with consolidation? (yes/no): ").strip().lower()
        if confirm not in ['yes', 'y']:
            print("⏹️  Consolidation cancelled")
            return
        
        print()
        
        # Step 4: Drop old database
        if not drop_old_database():
            print("❌ Failed to drop old database")
            return
        
        print()
        
        # Step 5: Restore historical data
        if not restore_historical_data():
            print("❌ Failed to restore historical data")
            return
        
        print()
        
        # Step 6: Verify consolidation
        if verify_consolidation():
            print()
            print("🎉 DATABASE CONSOLIDATION COMPLETED SUCCESSFULLY!")
            print()
            print("📊 Summary:")
            print("   ✅ Single database: solar_prediction")
            print("   ✅ Historical data imported")
            print("   ✅ Current system unchanged")
            print("   ✅ Backup created for safety")
        else:
            print("⚠️  Consolidation completed with warnings")
            
    except KeyboardInterrupt:
        print("\n⏹️  Consolidation cancelled by user")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"\n❌ Consolidation failed: {e}")
        sys.exit(1)
