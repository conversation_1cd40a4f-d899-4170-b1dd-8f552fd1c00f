#!/usr/bin/env python3
"""
Weather Data Collector for Solar Systems
Collects weather data from Open-Meteo API every hour
"""

import sys
import os
import time
import json
import requests
import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime, timedelta
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

# Weather API configuration
WEATHER_CONFIG = {
    'url': 'https://api.open-meteo.com/v1/forecast',
    'latitude': 38.141348,
    'longitude': 24.007165,
    'timezone': 'Europe/Athens'
}

class WeatherDataCollector:
    """Weather data collector for Open-Meteo API"""
    
    def __init__(self):
        self.db_config = DB_CONFIG
        self.weather_config = WEATHER_CONFIG
        self.collection_stats = {
            'success': 0,
            'errors': 0,
            'last_success': None,
            'last_error': None
        }
        self.is_running = False
    
    def get_db_connection(self):
        """Get database connection"""
        try:
            return psycopg2.connect(**self.db_config)
        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            return None
    
    def ensure_weather_table(self):
        """Check weather_data table exists"""
        conn = self.get_db_connection()
        if not conn:
            return False

        try:
            cur = conn.cursor()

            # Check if table exists
            cur.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_name = 'weather_data'
                )
            """)

            exists = cur.fetchone()[0]

            if exists:
                logger.info("✅ Weather table exists")
                return True
            else:
                logger.error("❌ Weather table does not exist")
                return False

        except Exception as e:
            logger.error(f"Error checking weather table: {e}")
            return False
        finally:
            conn.close()
    
    def fetch_weather_data(self) -> dict:
        """Fetch current weather data from Open-Meteo API"""
        try:
            params = {
                'latitude': self.weather_config['latitude'],
                'longitude': self.weather_config['longitude'],
                'current': 'temperature_2m,relative_humidity_2m,cloud_cover,wind_speed_10m,wind_direction_10m',
                'timezone': self.weather_config['timezone']
            }
            
            response = requests.get(
                self.weather_config['url'],
                params=params,
                timeout=15
            )
            
            if response.status_code == 200:
                data = response.json()
                
                # Extract current weather
                current = data.get('current', {})
                current_time = datetime.now()

                # Create weather data object
                weather_data = {
                    'timestamp': current_time,
                    'temperature_2m': current.get('temperature_2m'),
                    'relative_humidity_2m': current.get('relative_humidity_2m'),
                    'cloud_cover': current.get('cloud_cover'),
                    'wind_speed_10m': current.get('wind_speed_10m'),
                    'wind_direction_10m': current.get('wind_direction_10m'),
                    'global_horizontal_irradiance': None,  # Will be added later
                    'direct_normal_irradiance': None,
                    'diffuse_horizontal_irradiance': None
                }
                
                return weather_data
                
            else:
                logger.error(f"Weather API HTTP error {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Error fetching weather data: {e}")
            return None
    
    def save_weather_data(self, data: dict) -> bool:
        """Save weather data to database"""
        if not data:
            return False
        
        conn = self.get_db_connection()
        if not conn:
            return False
        
        try:
            cur = conn.cursor()
            
            # Insert weather data (using existing table structure)
            insert_query = """
                INSERT INTO weather_data
                (timestamp, temperature_2m, relative_humidity_2m, cloud_cover,
                 global_horizontal_irradiance, direct_normal_irradiance,
                 data_source, is_forecast)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """

            cur.execute(insert_query, (
                data['timestamp'],
                data.get('temperature_2m'),
                data.get('relative_humidity_2m'),
                data.get('cloud_cover'),
                data.get('global_horizontal_irradiance'),
                data.get('direct_normal_irradiance'),
                'open-meteo',  # data_source
                False  # is_forecast (current data)
            ))
            
            conn.commit()
            
            logger.info(f"✅ Weather: Temp={data.get('temperature_2m', 'N/A')}°C, "
                       f"Humidity={data.get('relative_humidity_2m', 'N/A')}%, "
                       f"Cloud={data.get('cloud_cover', 'N/A')}%, "
                       f"GHI={data.get('global_horizontal_irradiance', 'N/A')} W/m²")
            
            # Update stats
            self.collection_stats['success'] += 1
            self.collection_stats['last_success'] = data['timestamp']
            
            return True
            
        except Exception as e:
            logger.error(f"Error saving weather data: {e}")
            conn.rollback()
            self.collection_stats['errors'] += 1
            self.collection_stats['last_error'] = datetime.now()
            return False
        finally:
            conn.close()
    
    def collect_weather(self) -> bool:
        """Collect and save weather data"""
        logger.info("🌤️ Collecting weather data...")
        
        # Fetch data
        data = self.fetch_weather_data()
        
        # Save data
        success = self.save_weather_data(data)
        
        return success
    
    def run_continuous_collection(self, interval_minutes: int = 60):
        """Run continuous weather data collection"""
        self.is_running = True
        interval_seconds = interval_minutes * 60
        
        logger.info(f"🚀 Starting continuous weather collection (interval: {interval_minutes} minutes)")
        logger.info("="*70)
        
        # Ensure table exists
        if not self.ensure_weather_table():
            logger.error("❌ Failed to ensure weather table exists")
            return False
        
        try:
            while self.is_running:
                start_time = time.time()
                
                # Collect weather data
                success = self.collect_weather()
                
                # Log result
                if success:
                    logger.info("📈 Weather collection cycle completed successfully")
                else:
                    logger.warning("⚠️ Weather collection cycle failed")
                
                # Show stats every 6 cycles (6 hours)
                if self.collection_stats['success'] % 6 == 0 and self.collection_stats['success'] > 0:
                    self.log_stats()
                
                # Wait for next cycle
                elapsed = time.time() - start_time
                sleep_time = max(0, interval_seconds - elapsed)
                
                if sleep_time > 0:
                    logger.info(f"⏰ Waiting {sleep_time/60:.1f} minutes for next weather collection...")
                    time.sleep(sleep_time)
                
        except KeyboardInterrupt:
            logger.info("🛑 Weather collection stopped by user")
        except Exception as e:
            logger.error(f"❌ Weather collection error: {e}")
        finally:
            self.is_running = False
            self.log_final_stats()
    
    def log_stats(self):
        """Log collection statistics"""
        logger.info("📊 WEATHER COLLECTION STATISTICS:")
        success_rate = 0
        if self.collection_stats['success'] + self.collection_stats['errors'] > 0:
            success_rate = (self.collection_stats['success'] / 
                          (self.collection_stats['success'] + self.collection_stats['errors'])) * 100
        
        logger.info(f"   Success: {self.collection_stats['success']}")
        logger.info(f"   Errors: {self.collection_stats['errors']}")
        logger.info(f"   Success Rate: {success_rate:.1f}%")
        logger.info(f"   Last Success: {self.collection_stats['last_success']}")
    
    def log_final_stats(self):
        """Log final statistics"""
        logger.info("="*70)
        logger.info("🎯 FINAL WEATHER COLLECTION STATISTICS:")
        self.log_stats()
    
    def test_single_collection(self):
        """Test single weather collection cycle"""
        logger.info("🧪 TESTING SINGLE WEATHER COLLECTION")
        logger.info("="*50)
        
        # Ensure table exists
        if not self.ensure_weather_table():
            logger.error("❌ Failed to ensure weather table exists")
            return False
        
        success = self.collect_weather()
        
        logger.info("📊 Test Results:")
        status = "✅ Success" if success else "❌ Failed"
        logger.info(f"   Weather Collection: {status}")
        
        if self.collection_stats['last_success']:
            logger.info(f"   Last Success: {self.collection_stats['last_success']}")
        
        return success

def main():
    """Main function"""
    
    print("🌤️ WEATHER DATA COLLECTOR")
    print("="*60)
    print("📊 Real-time weather data collection from Open-Meteo")
    print("🔄 Collects data every hour")
    print("💾 Saves to PostgreSQL database")
    print()
    
    try:
        collector = WeatherDataCollector()
        
        # Test database connection
        conn = collector.get_db_connection()
        if not conn:
            print("❌ Database connection failed!")
            return False
        conn.close()
        print("✅ Database connection successful")
        
        # Test single collection
        print("\n🧪 Testing single collection cycle...")
        test_result = collector.test_single_collection()
        
        if not test_result:
            print("❌ Weather collection test failed!")
            return False
        
        print(f"\n✅ Test successful!")
        
        # Ask user if they want to start continuous collection
        print("\n🚀 Ready to start continuous weather collection!")
        print("Press Ctrl+C to stop at any time")
        print()
        
        # Start continuous collection (every hour)
        collector.run_continuous_collection(interval_minutes=60)
        
        return True
        
    except Exception as e:
        print(f"❌ Weather collector failed: {e}")
        logger.exception("Weather collector failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
