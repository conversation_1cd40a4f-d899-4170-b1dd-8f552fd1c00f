#!/usr/bin/env python3
"""
WebSocket Server for Real-time Solar Data Streaming
Provides real-time data updates to frontend clients
"""

from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import psycopg2
from psycopg2.extras import RealDictCursor
import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Set
import uvicorn

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

app = FastAPI(title="Solar WebSocket Server", version="1.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class ConnectionManager:
    """WebSocket connection manager"""
    
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.client_subscriptions: Dict[WebSocket, Set[str]] = {}
        
    async def connect(self, websocket: WebSocket):
        """Accept new WebSocket connection"""
        await websocket.accept()
        self.active_connections.append(websocket)
        self.client_subscriptions[websocket] = set()
        logger.info(f"✅ New WebSocket connection. Total: {len(self.active_connections)}")
        
    def disconnect(self, websocket: WebSocket):
        """Remove WebSocket connection"""
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        if websocket in self.client_subscriptions:
            del self.client_subscriptions[websocket]
        logger.info(f"❌ WebSocket disconnected. Total: {len(self.active_connections)}")
        
    async def send_personal_message(self, message: str, websocket: WebSocket):
        """Send message to specific client"""
        try:
            await websocket.send_text(message)
        except:
            self.disconnect(websocket)
            
    async def broadcast(self, message: str, subscription_type: str = None):
        """Broadcast message to all connected clients"""
        disconnected = []
        
        for connection in self.active_connections:
            try:
                # Check if client is subscribed to this type of data
                if subscription_type is None or subscription_type in self.client_subscriptions.get(connection, set()):
                    await connection.send_text(message)
            except:
                disconnected.append(connection)
        
        # Clean up disconnected clients
        for connection in disconnected:
            self.disconnect(connection)
    
    def subscribe_client(self, websocket: WebSocket, subscription_type: str):
        """Subscribe client to specific data type"""
        if websocket in self.client_subscriptions:
            self.client_subscriptions[websocket].add(subscription_type)
            logger.info(f"📡 Client subscribed to: {subscription_type}")
    
    def unsubscribe_client(self, websocket: WebSocket, subscription_type: str):
        """Unsubscribe client from specific data type"""
        if websocket in self.client_subscriptions:
            self.client_subscriptions[websocket].discard(subscription_type)
            logger.info(f"📡 Client unsubscribed from: {subscription_type}")

# Initialize connection manager
manager = ConnectionManager()

def get_db_connection():
    """Get database connection"""
    try:
        return psycopg2.connect(**DB_CONFIG)
    except Exception as e:
        logger.error(f"Database connection failed: {e}")
        return None

async def get_latest_solar_data() -> Dict:
    """Get latest solar data from database"""
    conn = get_db_connection()
    if not conn:
        return {}
    
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        
        # Get latest data from both systems
        data = {
            'timestamp': datetime.now().isoformat(),
            'systems': {}
        }
        
        for system_id, table_name in [('system1', 'solax_data'), ('system2', 'solax_data2')]:
            cur.execute(f"""
                SELECT timestamp, yield_today, ac_power, soc, bat_power, temperature
                FROM {table_name} 
                ORDER BY timestamp DESC 
                LIMIT 1
            """)
            
            latest = cur.fetchone()
            if latest:
                data['systems'][system_id] = {
                    'timestamp': latest['timestamp'].isoformat(),
                    'yield_today': float(latest['yield_today'] or 0),
                    'ac_power': float(latest['ac_power'] or 0),
                    'soc': float(latest['soc'] or 0),
                    'bat_power': float(latest['bat_power'] or 0),
                    'temperature': float(latest['temperature'] or 0)
                }
        
        # Calculate combined data
        if 'system1' in data['systems'] and 'system2' in data['systems']:
            s1 = data['systems']['system1']
            s2 = data['systems']['system2']
            
            data['combined'] = {
                'total_yield': s1['yield_today'] + s2['yield_today'],
                'total_power': s1['ac_power'] + s2['ac_power'],
                'avg_soc': (s1['soc'] + s2['soc']) / 2,
                'total_bat_power': s1['bat_power'] + s2['bat_power']
            }
        
        conn.close()
        return data
        
    except Exception as e:
        logger.error(f"Error getting solar data: {e}")
        conn.close()
        return {}

async def get_latest_weather_data() -> Dict:
    """Get latest weather data from database"""
    conn = get_db_connection()
    if not conn:
        return {}
    
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        
        cur.execute("""
            SELECT timestamp, temperature_2m, relative_humidity_2m, cloud_cover,
                   global_horizontal_irradiance, direct_normal_irradiance
            FROM weather_data 
            ORDER BY timestamp DESC 
            LIMIT 1
        """)
        
        latest = cur.fetchone()
        if latest:
            data = {
                'timestamp': latest['timestamp'].isoformat(),
                'temperature': float(latest['temperature_2m'] or 0),
                'humidity': float(latest['relative_humidity_2m'] or 0),
                'cloud_cover': float(latest['cloud_cover'] or 0),
                'ghi': float(latest['global_horizontal_irradiance'] or 0),
                'dni': float(latest['direct_normal_irradiance'] or 0)
            }
        else:
            data = {}
        
        conn.close()
        return data
        
    except Exception as e:
        logger.error(f"Error getting weather data: {e}")
        conn.close()
        return {}

async def get_system_status() -> Dict:
    """Get system status information"""
    conn = get_db_connection()
    if not conn:
        return {'status': 'database_error'}
    
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        
        # Check data freshness
        now = datetime.now()
        recent_threshold = now - timedelta(minutes=5)
        
        status = {
            'timestamp': now.isoformat(),
            'database': 'connected',
            'systems': {}
        }
        
        for system_id, table_name in [('system1', 'solax_data'), ('system2', 'solax_data2')]:
            cur.execute(f"""
                SELECT COUNT(*) as recent_count, MAX(timestamp) as latest_timestamp
                FROM {table_name} 
                WHERE timestamp > %s
            """, (recent_threshold,))
            
            result = cur.fetchone()
            
            status['systems'][system_id] = {
                'recent_records': result['recent_count'],
                'latest_timestamp': result['latest_timestamp'].isoformat() if result['latest_timestamp'] else None,
                'status': 'healthy' if result['recent_count'] >= 5 else 'stale'
            }
        
        # Overall system status
        all_healthy = all(s['status'] == 'healthy' for s in status['systems'].values())
        status['overall_status'] = 'healthy' if all_healthy else 'warning'
        
        conn.close()
        return status
        
    except Exception as e:
        logger.error(f"Error getting system status: {e}")
        conn.close()
        return {'status': 'error', 'error': str(e)}

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """Main WebSocket endpoint"""
    await manager.connect(websocket)
    
    try:
        # Send initial data
        await websocket.send_text(json.dumps({
            'type': 'connection',
            'message': 'Connected to Solar WebSocket Server',
            'timestamp': datetime.now().isoformat()
        }))
        
        while True:
            # Wait for client messages
            try:
                data = await asyncio.wait_for(websocket.receive_text(), timeout=1.0)
                message = json.loads(data)
                
                # Handle client requests
                if message.get('type') == 'subscribe':
                    subscription_type = message.get('data_type')
                    if subscription_type:
                        manager.subscribe_client(websocket, subscription_type)
                        await websocket.send_text(json.dumps({
                            'type': 'subscription_confirmed',
                            'data_type': subscription_type,
                            'timestamp': datetime.now().isoformat()
                        }))
                
                elif message.get('type') == 'unsubscribe':
                    subscription_type = message.get('data_type')
                    if subscription_type:
                        manager.unsubscribe_client(websocket, subscription_type)
                        await websocket.send_text(json.dumps({
                            'type': 'unsubscription_confirmed',
                            'data_type': subscription_type,
                            'timestamp': datetime.now().isoformat()
                        }))
                
                elif message.get('type') == 'get_current_data':
                    # Send current data immediately
                    solar_data = await get_latest_solar_data()
                    if solar_data:
                        await websocket.send_text(json.dumps({
                            'type': 'solar_data',
                            'data': solar_data,
                            'timestamp': datetime.now().isoformat()
                        }))
                
            except asyncio.TimeoutError:
                # No message received, continue with periodic updates
                pass
            except json.JSONDecodeError:
                await websocket.send_text(json.dumps({
                    'type': 'error',
                    'message': 'Invalid JSON format',
                    'timestamp': datetime.now().isoformat()
                }))
            
    except WebSocketDisconnect:
        manager.disconnect(websocket)

async def broadcast_solar_data():
    """Periodically broadcast solar data to all connected clients"""
    while True:
        try:
            if manager.active_connections:
                solar_data = await get_latest_solar_data()
                
                if solar_data:
                    message = json.dumps({
                        'type': 'solar_data',
                        'data': solar_data,
                        'timestamp': datetime.now().isoformat()
                    })
                    
                    await manager.broadcast(message, 'solar_data')
            
            await asyncio.sleep(30)  # Broadcast every 30 seconds
            
        except Exception as e:
            logger.error(f"Error broadcasting solar data: {e}")
            await asyncio.sleep(30)

async def broadcast_weather_data():
    """Periodically broadcast weather data to all connected clients"""
    while True:
        try:
            if manager.active_connections:
                weather_data = await get_latest_weather_data()
                
                if weather_data:
                    message = json.dumps({
                        'type': 'weather_data',
                        'data': weather_data,
                        'timestamp': datetime.now().isoformat()
                    })
                    
                    await manager.broadcast(message, 'weather_data')
            
            await asyncio.sleep(300)  # Broadcast every 5 minutes
            
        except Exception as e:
            logger.error(f"Error broadcasting weather data: {e}")
            await asyncio.sleep(300)

async def broadcast_system_status():
    """Periodically broadcast system status to all connected clients"""
    while True:
        try:
            if manager.active_connections:
                status_data = await get_system_status()
                
                message = json.dumps({
                    'type': 'system_status',
                    'data': status_data,
                    'timestamp': datetime.now().isoformat()
                })
                
                await manager.broadcast(message, 'system_status')
            
            await asyncio.sleep(60)  # Broadcast every minute
            
        except Exception as e:
            logger.error(f"Error broadcasting system status: {e}")
            await asyncio.sleep(60)

@app.get("/ws/stats")
async def websocket_stats():
    """Get WebSocket server statistics"""
    return {
        'active_connections': len(manager.active_connections),
        'total_subscriptions': sum(len(subs) for subs in manager.client_subscriptions.values()),
        'subscription_types': {
            'solar_data': sum(1 for subs in manager.client_subscriptions.values() if 'solar_data' in subs),
            'weather_data': sum(1 for subs in manager.client_subscriptions.values() if 'weather_data' in subs),
            'system_status': sum(1 for subs in manager.client_subscriptions.values() if 'system_status' in subs)
        },
        'server_status': 'running',
        'timestamp': datetime.now().isoformat()
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "Solar WebSocket Server",
        "active_connections": len(manager.active_connections),
        "timestamp": datetime.now().isoformat()
    }

@app.on_event("startup")
async def startup_event():
    """Start background tasks"""
    logger.info("🚀 Starting WebSocket server background tasks...")
    
    # Start background broadcasting tasks
    asyncio.create_task(broadcast_solar_data())
    asyncio.create_task(broadcast_weather_data())
    asyncio.create_task(broadcast_system_status())
    
    logger.info("✅ WebSocket server ready")

if __name__ == "__main__":
    print("🔌 Starting Solar WebSocket Server on port 8107...")
    uvicorn.run(app, host="0.0.0.0", port=8107)
