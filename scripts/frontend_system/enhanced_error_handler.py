#!/usr/bin/env python3
"""
Enhanced <PERSON>rro<PERSON> for Telegram Bot
Provides robust error handling, retry mechanisms, and user-friendly feedback
"""

import asyncio
import logging
import time
from datetime import datetime
from typing import Dict, Any, Optional, Callable
from functools import wraps
import aiohttp
import requests
from telegram import Update
from telegram.ext import ContextTypes

logger = logging.getLogger(__name__)

class EnhancedErrorHandler:
    """Enhanced error handling for Telegram bot operations"""
    
    def __init__(self):
        self.retry_delays = [1, 2, 5, 10, 30]  # Exponential backoff
        self.max_retries = 3
        self.timeout_seconds = 15
        
        # Error messages in Greek and English
        self.error_messages = {
            'el': {
                'service_unavailable': '⚠️ Η υπηρεσία δεν είναι διαθέσιμη προσωρινά',
                'network_error': '🌐 Πρόβλημα δικτύου - δοκιμάστε ξανά',
                'timeout_error': '⏱️ Η αίτηση έληξε - δοκιμάστε ξανά',
                'data_error': '📊 Σφάλμα δεδομένων - δοκιμάστε αργότερα',
                'unknown_error': '❌ Άγνωστο σφάλμα - επικοινωνήστε με τον διαχειριστή',
                'try_again': '🔄 Δοκιμάστε ξανά σε λίγο',
                'fallback_mode': '🔧 Λειτουργία εφεδρείας ενεργή',
                'no_data': '📭 Δεν υπάρχουν διαθέσιμα δεδομένα',
                'api_error': '🔌 Σφάλμα API - ελέγξτε τη σύνδεση'
            },
            'en': {
                'service_unavailable': '⚠️ Service temporarily unavailable',
                'network_error': '🌐 Network error - please try again',
                'timeout_error': '⏱️ Request timed out - please try again',
                'data_error': '📊 Data error - please try later',
                'unknown_error': '❌ Unknown error - contact administrator',
                'try_again': '🔄 Please try again in a moment',
                'fallback_mode': '🔧 Fallback mode active',
                'no_data': '📭 No data available',
                'api_error': '🔌 API error - check connection'
            }
        }
    
    def get_error_message(self, error_type: str, language: str = 'el') -> str:
        """Get localized error message"""
        return self.error_messages.get(language, self.error_messages['el']).get(
            error_type, self.error_messages['el']['unknown_error']
        )
    
    async def safe_api_call(self, 
                           api_func: Callable, 
                           *args, 
                           fallback_value: Any = None,
                           error_context: str = "API call",
                           **kwargs) -> Any:
        """
        Safely execute API call with retry logic and error handling
        """
        last_error = None
        
        for attempt in range(self.max_retries + 1):
            try:
                if asyncio.iscoroutinefunction(api_func):
                    result = await api_func(*args, **kwargs)
                else:
                    result = api_func(*args, **kwargs)
                
                if result is not None:
                    return result
                    
            except asyncio.TimeoutError:
                last_error = "timeout"
                logger.warning(f"{error_context} timeout on attempt {attempt + 1}")
            except aiohttp.ClientError:
                last_error = "network"
                logger.warning(f"{error_context} network error on attempt {attempt + 1}")
            except requests.RequestException:
                last_error = "network"
                logger.warning(f"{error_context} request error on attempt {attempt + 1}")
            except Exception as e:
                last_error = "unknown"
                logger.error(f"{error_context} error on attempt {attempt + 1}: {e}")
            
            # Wait before retry (except on last attempt)
            if attempt < self.max_retries:
                await asyncio.sleep(self.retry_delays[min(attempt, len(self.retry_delays) - 1)])
        
        logger.error(f"{error_context} failed after {self.max_retries + 1} attempts")
        return fallback_value
    
    async def safe_database_operation(self, 
                                    db_func: Callable, 
                                    *args, 
                                    fallback_value: Any = None,
                                    **kwargs) -> Any:
        """
        Safely execute database operation with error handling
        """
        try:
            return db_func(*args, **kwargs)
        except Exception as e:
            logger.error(f"Database operation failed: {e}")
            return fallback_value
    
    async def handle_telegram_error(self, 
                                  update: Update, 
                                  context: ContextTypes.DEFAULT_TYPE,
                                  error_type: str = "unknown_error",
                                  language: str = 'el',
                                  custom_message: str = None):
        """
        Handle Telegram bot errors with user-friendly messages
        """
        try:
            message = custom_message or self.get_error_message(error_type, language)
            
            if update and update.effective_message:
                await update.effective_message.reply_text(
                    message,
                    parse_mode='Markdown'
                )
        except Exception as e:
            logger.error(f"Failed to send error message: {e}")
    
    def with_error_handling(self, 
                           error_type: str = "unknown_error",
                           fallback_message: str = None,
                           language: str = 'el'):
        """
        Decorator for Telegram command handlers with automatic error handling
        """
        def decorator(func):
            @wraps(func)
            async def wrapper(self_instance, update: Update, context: ContextTypes.DEFAULT_TYPE):
                try:
                    return await func(self_instance, update, context)
                except Exception as e:
                    logger.error(f"Command {func.__name__} failed: {e}")
                    
                    # Get user language if available
                    user_id = update.effective_user.id if update.effective_user else None
                    user_language = language
                    if hasattr(self_instance, 'get_user_language') and user_id:
                        user_language = self_instance.get_user_language(user_id)
                    
                    await self.handle_telegram_error(
                        update, context, error_type, user_language, fallback_message
                    )
            return wrapper
        return decorator

# Global error handler instance
error_handler = EnhancedErrorHandler()

# Convenience decorators
def with_api_error_handling(func):
    """Decorator for API-related operations"""
    return error_handler.with_error_handling("api_error")(func)

def with_data_error_handling(func):
    """Decorator for data-related operations"""
    return error_handler.with_error_handling("data_error")(func)

def with_network_error_handling(func):
    """Decorator for network-related operations"""
    return error_handler.with_error_handling("network_error")(func)

# Utility functions for common error scenarios
async def safe_get_system_data(api_url: str, timeout: int = 15) -> Optional[Dict]:
    """Safely get system data with error handling"""
    async def api_call():
        async with aiohttp.ClientSession() as session:
            async with session.get(api_url, timeout=timeout) as response:
                if response.status == 200:
                    return await response.json()
                return None
    
    return await error_handler.safe_api_call(
        api_call,
        error_context="System data API"
    )

async def safe_get_weather_data(api_url: str, timeout: int = 15) -> Optional[Dict]:
    """Safely get weather data with error handling"""
    async def api_call():
        async with aiohttp.ClientSession() as session:
            async with session.get(api_url, timeout=timeout) as response:
                if response.status == 200:
                    return await response.json()
                return None
    
    return await error_handler.safe_api_call(
        api_call,
        error_context="Weather data API"
    )

async def safe_get_predictions(api_url: str, timeout: int = 30) -> Optional[Dict]:
    """Safely get predictions with error handling"""
    async def api_call():
        async with aiohttp.ClientSession() as session:
            async with session.get(api_url, timeout=timeout) as response:
                if response.status == 200:
                    return await response.json()
                return None
    
    return await error_handler.safe_api_call(
        api_call,
        error_context="Predictions API"
    )

def format_error_response(error_type: str, details: str = None, language: str = 'el') -> str:
    """Format a user-friendly error response"""
    base_message = error_handler.get_error_message(error_type, language)
    
    if details:
        return f"{base_message}\n\n📝 {details}"
    
    return base_message
