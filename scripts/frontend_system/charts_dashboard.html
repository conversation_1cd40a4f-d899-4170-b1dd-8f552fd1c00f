<!DOCTYPE html>
<html lang="el">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Solar Charts Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }

        .dashboard-container {
            max-width: 1600px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .control-group {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .control-group select,
        .control-group input {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 8px 12px;
            color: white;
            font-size: 14px;
        }

        .control-group select option {
            background: #333;
            color: white;
        }

        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(600px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .chart-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .chart-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .chart-title {
            font-size: 1.3em;
            font-weight: bold;
        }

        .chart-status {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            background: rgba(76, 175, 80, 0.3);
            border: 1px solid rgba(76, 175, 80, 0.5);
        }

        .chart-container {
            position: relative;
            height: 300px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 10px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .stat-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9em;
            opacity: 0.8;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .refresh-timer {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.7);
            padding: 10px 15px;
            border-radius: 25px;
            font-size: 0.9em;
        }

        @media (max-width: 768px) {
            .charts-grid {
                grid-template-columns: 1fr;
            }
            
            .controls {
                flex-direction: column;
                align-items: center;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Header -->
        <div class="header">
            <h1>📊 Solar Charts Dashboard</h1>
            <div class="subtitle">Real-time visualization με 100% πραγματικά δεδομένα</div>
        </div>

        <!-- Refresh Timer -->
        <div class="refresh-timer">
            <span id="refreshTimer">Ενημέρωση σε: 30s</span>
        </div>

        <!-- Controls -->
        <div class="controls">
            <div class="control-group">
                <label for="timeRange">Χρονικό Εύρος:</label>
                <select id="timeRange">
                    <option value="1h">Τελευταία 1 ώρα</option>
                    <option value="6h">Τελευταίες 6 ώρες</option>
                    <option value="24h" selected>Τελευταίες 24 ώρες</option>
                    <option value="7d">Τελευταίες 7 ημέρες</option>
                </select>
            </div>
            
            <div class="control-group">
                <label for="systemFilter">Σύστημα:</label>
                <select id="systemFilter">
                    <option value="both" selected>Αμφότερα</option>
                    <option value="system1">Σπίτι Πάνω</option>
                    <option value="system2">Σπίτι Κάτω</option>
                </select>
            </div>
            
            <div class="control-group">
                <label for="chartType">Τύπος Γραφήματος:</label>
                <select id="chartType">
                    <option value="line" selected>Γραμμή</option>
                    <option value="bar">Μπάρες</option>
                    <option value="area">Περιοχή</option>
                </select>
            </div>
        </div>

        <!-- Statistics Grid -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="totalYieldToday">--</div>
                <div class="stat-label">Συνολική Παραγωγή Σήμερα (kWh)</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="currentPower">--</div>
                <div class="stat-label">Τρέχουσα Ισχύς (W)</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="averageSOC">--</div>
                <div class="stat-label">Μέση Φόρτιση (%)</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="efficiency">--</div>
                <div class="stat-label">Απόδοση Συλλογής (%)</div>
            </div>
        </div>

        <!-- Charts Grid -->
        <div class="charts-grid">
            <!-- Power Chart -->
            <div class="chart-card">
                <div class="chart-header">
                    <div class="chart-title">⚡ Ισχύς AC (Watts)</div>
                    <div class="chart-status">Live</div>
                </div>
                <div class="chart-container">
                    <canvas id="powerChart"></canvas>
                </div>
            </div>

            <!-- Yield Chart -->
            <div class="chart-card">
                <div class="chart-header">
                    <div class="chart-title">🔋 Παραγωγή Ενέργειας (kWh)</div>
                    <div class="chart-status">Live</div>
                </div>
                <div class="chart-container">
                    <canvas id="yieldChart"></canvas>
                </div>
            </div>

            <!-- SOC Chart -->
            <div class="chart-card">
                <div class="chart-header">
                    <div class="chart-title">🔋 Φόρτιση Μπαταρίας (%)</div>
                    <div class="chart-status">Live</div>
                </div>
                <div class="chart-container">
                    <canvas id="socChart"></canvas>
                </div>
            </div>

            <!-- Weather Chart -->
            <div class="chart-card">
                <div class="chart-header">
                    <div class="chart-title">🌤️ Καιρικές Συνθήκες</div>
                    <div class="chart-status">Live</div>
                </div>
                <div class="chart-container">
                    <canvas id="weatherChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <script>
        class SolarChartsManager {
            constructor() {
                this.charts = {};
                this.refreshInterval = 30; // seconds
                this.currentCountdown = this.refreshInterval;
                
                this.init();
            }
            
            init() {
                this.setupCharts();
                this.loadData();
                this.startRefreshTimer();
                this.startCountdown();
                this.setupEventListeners();
            }
            
            setupCharts() {
                // Chart.js default configuration
                Chart.defaults.color = '#ffffff';
                Chart.defaults.borderColor = 'rgba(255, 255, 255, 0.1)';
                Chart.defaults.backgroundColor = 'rgba(255, 255, 255, 0.05)';
                
                // Power Chart
                this.charts.power = new Chart(document.getElementById('powerChart'), {
                    type: 'line',
                    data: {
                        labels: [],
                        datasets: [
                            {
                                label: 'Σπίτι Πάνω',
                                data: [],
                                borderColor: '#FF6B6B',
                                backgroundColor: 'rgba(255, 107, 107, 0.1)',
                                tension: 0.4,
                                fill: true
                            },
                            {
                                label: 'Σπίτι Κάτω',
                                data: [],
                                borderColor: '#4ECDC4',
                                backgroundColor: 'rgba(78, 205, 196, 0.1)',
                                tension: 0.4,
                                fill: true
                            }
                        ]
                    },
                    options: this.getChartOptions('Watts')
                });
                
                // Yield Chart
                this.charts.yield = new Chart(document.getElementById('yieldChart'), {
                    type: 'line',
                    data: {
                        labels: [],
                        datasets: [
                            {
                                label: 'Σπίτι Πάνω',
                                data: [],
                                borderColor: '#FFD93D',
                                backgroundColor: 'rgba(255, 217, 61, 0.1)',
                                tension: 0.4,
                                fill: true
                            },
                            {
                                label: 'Σπίτι Κάτω',
                                data: [],
                                borderColor: '#6BCF7F',
                                backgroundColor: 'rgba(107, 207, 127, 0.1)',
                                tension: 0.4,
                                fill: true
                            }
                        ]
                    },
                    options: this.getChartOptions('kWh')
                });
                
                // SOC Chart
                this.charts.soc = new Chart(document.getElementById('socChart'), {
                    type: 'line',
                    data: {
                        labels: [],
                        datasets: [
                            {
                                label: 'Σπίτι Πάνω',
                                data: [],
                                borderColor: '#4D96FF',
                                backgroundColor: 'rgba(77, 150, 255, 0.1)',
                                tension: 0.4,
                                fill: true
                            },
                            {
                                label: 'Σπίτι Κάτω',
                                data: [],
                                borderColor: '#9775FA',
                                backgroundColor: 'rgba(151, 117, 250, 0.1)',
                                tension: 0.4,
                                fill: true
                            }
                        ]
                    },
                    options: this.getChartOptions('%')
                });
                
                // Weather Chart
                this.charts.weather = new Chart(document.getElementById('weatherChart'), {
                    type: 'line',
                    data: {
                        labels: [],
                        datasets: [
                            {
                                label: 'Θερμοκρασία (°C)',
                                data: [],
                                borderColor: '#FF8A80',
                                backgroundColor: 'rgba(255, 138, 128, 0.1)',
                                tension: 0.4,
                                yAxisID: 'y'
                            },
                            {
                                label: 'Υγρασία (%)',
                                data: [],
                                borderColor: '#80D8FF',
                                backgroundColor: 'rgba(128, 216, 255, 0.1)',
                                tension: 0.4,
                                yAxisID: 'y1'
                            }
                        ]
                    },
                    options: this.getWeatherChartOptions()
                });
            }
            
            getChartOptions(unit) {
                return {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                color: '#ffffff',
                                usePointStyle: true,
                                padding: 20
                            }
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            },
                            ticks: {
                                color: '#ffffff'
                            }
                        },
                        y: {
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            },
                            ticks: {
                                color: '#ffffff',
                                callback: function(value) {
                                    return value + ' ' + unit;
                                }
                            }
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    }
                };
            }
            
            getWeatherChartOptions() {
                return {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                color: '#ffffff',
                                usePointStyle: true,
                                padding: 20
                            }
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            },
                            ticks: {
                                color: '#ffffff'
                            }
                        },
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            },
                            ticks: {
                                color: '#ffffff',
                                callback: function(value) {
                                    return value + '°C';
                                }
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            grid: {
                                drawOnChartArea: false,
                            },
                            ticks: {
                                color: '#ffffff',
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    }
                };
            }
            
            async loadData() {
                try {
                    console.log('🔄 Loading chart data...');

                    // Load real data from APIs
                    const solarData = await this.fetchSolarData();
                    const weatherData = await this.fetchWeatherData();

                    console.log('📊 Solar data points:', solarData.timestamps.length);
                    console.log('🌤️ Weather data points:', weatherData.timestamps.length);

                    this.updateCharts(solarData, weatherData);
                    await this.updateStatistics();

                    console.log('✅ Chart data loaded successfully');

                } catch (error) {
                    console.error('❌ Error loading chart data:', error);
                    this.showErrorMessage('Σφάλμα φόρτωσης δεδομένων: ' + error.message);
                }
            }
            
            async fetchSolarData() {
                try {
                    const timeRange = document.getElementById('timeRange').value;
                    const hours = this.getHoursFromRange(timeRange);

                    console.log(`🔍 Fetching solar data for ${hours} hours...`);

                    const response = await fetch(`http://localhost:8103/api/charts/solar-data?hours=${hours}`);
                    if (!response.ok) {
                        throw new Error(`Charts API error: ${response.status} ${response.statusText}`);
                    }

                    const data = await response.json();

                    console.log(`📊 Received ${data.timestamps.length} solar data points`);
                    console.log('Sample data:', {
                        system1_power: data.system1.power.slice(0, 3),
                        system2_power: data.system2.power.slice(0, 3)
                    });

                    // Format timestamps for display
                    data.timestamps = data.timestamps.map(ts => {
                        const date = new Date(ts);
                        return date.toLocaleTimeString('el-GR', { hour: '2-digit', minute: '2-digit' });
                    });

                    return data;

                } catch (error) {
                    console.error('❌ Error fetching solar data:', error);
                    // Return empty data structure on error instead of throwing
                    return {
                        timestamps: [],
                        system1: { power: [], yield: [], soc: [] },
                        system2: { power: [], yield: [], soc: [] }
                    };
                }
            }
            
            async fetchWeatherData() {
                try {
                    const timeRange = document.getElementById('timeRange').value;
                    const hours = this.getHoursFromRange(timeRange);

                    const response = await fetch(`http://localhost:8103/api/charts/weather-data?hours=${hours}`);
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const data = await response.json();

                    // Format timestamps for display
                    data.timestamps = data.timestamps.map(ts => {
                        const date = new Date(ts);
                        return date.toLocaleTimeString('el-GR', { hour: '2-digit', minute: '2-digit' });
                    });

                    return data;

                } catch (error) {
                    console.error('❌ Error fetching weather data:', error);
                    // Return empty data structure on error
                    return {
                        timestamps: [],
                        temperature: [],
                        humidity: [],
                        cloud_cover: []
                    };
                }
            }
            
            updateCharts(solarData, weatherData) {
                // Update Power Chart
                this.charts.power.data.labels = solarData.timestamps;
                this.charts.power.data.datasets[0].data = solarData.system1.power;
                this.charts.power.data.datasets[1].data = solarData.system2.power;
                this.charts.power.update('none');
                
                // Update Yield Chart
                this.charts.yield.data.labels = solarData.timestamps;
                this.charts.yield.data.datasets[0].data = solarData.system1.yield;
                this.charts.yield.data.datasets[1].data = solarData.system2.yield;
                this.charts.yield.update('none');
                
                // Update SOC Chart
                this.charts.soc.data.labels = solarData.timestamps;
                this.charts.soc.data.datasets[0].data = solarData.system1.soc;
                this.charts.soc.data.datasets[1].data = solarData.system2.soc;
                this.charts.soc.update('none');
                
                // Update Weather Chart
                this.charts.weather.data.labels = weatherData.timestamps;
                this.charts.weather.data.datasets[0].data = weatherData.temperature;
                this.charts.weather.data.datasets[1].data = weatherData.humidity;
                this.charts.weather.update('none');
            }
            
            async updateStatistics() {
                try {
                    console.log('📈 Fetching statistics...');

                    const response = await fetch('http://localhost:8103/api/charts/statistics');
                    if (!response.ok) {
                        throw new Error(`Statistics API error: ${response.status} ${response.statusText}`);
                    }

                    const stats = await response.json();

                    console.log('📊 Statistics received:', stats);

                    document.getElementById('totalYieldToday').textContent = stats.total_yield_today;
                    document.getElementById('currentPower').textContent = stats.current_power.toLocaleString();
                    document.getElementById('averageSOC').textContent = stats.average_soc;
                    document.getElementById('efficiency').textContent = stats.collection_efficiency;

                    console.log('✅ Statistics updated successfully');

                } catch (error) {
                    console.error('❌ Error fetching statistics:', error);
                    this.showErrorMessage('Σφάλμα φόρτωσης στατιστικών: ' + error.message);
                }
            }

            getHoursFromRange(range) {
                switch(range) {
                    case '1h': return 1;
                    case '6h': return 6;
                    case '24h': return 24;
                    case '7d': return 168; // 7 * 24
                    default: return 24;
                }
            }
            
            setupEventListeners() {
                document.getElementById('timeRange').addEventListener('change', () => {
                    this.loadData();
                });
                
                document.getElementById('systemFilter').addEventListener('change', () => {
                    this.updateChartVisibility();
                });
                
                document.getElementById('chartType').addEventListener('change', () => {
                    this.updateChartTypes();
                });
            }
            
            updateChartVisibility() {
                const filter = document.getElementById('systemFilter').value;
                
                Object.values(this.charts).forEach(chart => {
                    if (chart.data.datasets.length >= 2) {
                        chart.data.datasets[0].hidden = filter === 'system2';
                        chart.data.datasets[1].hidden = filter === 'system1';
                        chart.update('none');
                    }
                });
            }
            
            updateChartTypes() {
                const selectedType = document.getElementById('chartType').value;

                // Map area to line with fill
                let chartType = selectedType;
                let fillOption = false;

                if (selectedType === 'area') {
                    chartType = 'line';
                    fillOption = true;
                }

                ['power', 'yield', 'soc'].forEach(chartName => {
                    if (this.charts[chartName]) {
                        // Update chart type and fill option
                        this.charts[chartName].config.type = chartType;

                        // Update fill option for all datasets
                        this.charts[chartName].data.datasets.forEach(dataset => {
                            dataset.fill = fillOption;

                            // Adjust styling based on type
                            if (chartType === 'bar') {
                                dataset.tension = 0;
                                dataset.borderWidth = 1;
                            } else {
                                dataset.tension = 0.4;
                                dataset.borderWidth = 2;
                            }
                        });

                        // Update the chart
                        this.charts[chartName].update('active');
                    }
                });
            }
            
            startRefreshTimer() {
                setInterval(() => {
                    this.loadData();
                    this.currentCountdown = this.refreshInterval;
                }, this.refreshInterval * 1000);
            }
            
            startCountdown() {
                setInterval(() => {
                    this.currentCountdown--;
                    document.getElementById('refreshTimer').textContent = `Ενημέρωση σε: ${this.currentCountdown}s`;

                    if (this.currentCountdown <= 0) {
                        this.currentCountdown = this.refreshInterval;
                    }
                }, 1000);
            }

            showErrorMessage(message) {
                // Create error notification
                const errorDiv = document.createElement('div');
                errorDiv.style.cssText = `
                    position: fixed;
                    top: 70px;
                    right: 20px;
                    background: rgba(220, 53, 69, 0.9);
                    color: white;
                    padding: 15px 20px;
                    border-radius: 10px;
                    z-index: 1000;
                    max-width: 400px;
                    backdrop-filter: blur(10px);
                `;
                errorDiv.textContent = message;

                document.body.appendChild(errorDiv);

                // Remove after 5 seconds
                setTimeout(() => {
                    if (errorDiv.parentNode) {
                        errorDiv.parentNode.removeChild(errorDiv);
                    }
                }, 5000);
            }
        }
        
        // Initialize dashboard when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new SolarChartsManager();
        });
    </script>
</body>
</html>
