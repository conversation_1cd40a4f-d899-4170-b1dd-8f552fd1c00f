#!/usr/bin/env python3

"""
Unified ROI Calculator - Final Version with Dynamic Data
Completely removes hardcoded values and uses only database queries
Date: June 2025
"""

import psycopg2
import logging
from datetime import datetime, timedelta
from typing import Dict, Op<PERSON>, Tuple, List
from dataclasses import dataclass
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ConsumptionRates:
    """Data class for consumption rate calculations"""
    self_consumption_rate: float
    surplus_rate: float
    grid_import_rate: float
    grid_consumption_rate: float
    total_production: float
    total_consumption: float
    total_surplus: float
    total_grid_import: float

@dataclass
class FinancialMetrics:
    """Data class for financial calculations"""
    energy_savings: float
    surplus_income: float
    grid_costs: float
    network_charges: float
    etmear_charges: float
    total_charges: float
    net_benefit: float
    annual_benefit: float

@dataclass
class ROIAnalysis:
    """Data class for complete ROI analysis"""
    system_id: str
    investment_cost: float
    operational_period: Dict
    production_data: Dict
    consumption_rates: ConsumptionRates
    financial_metrics: FinancialMetrics
    roi_percentage: float
    payback_years: Optional[float]
    status: str

class UnifiedROICalculator:
    """
    Unified ROI Calculator with 100% dynamic data from database
    Features:
    - No hardcoded values - all data from database
    - Dynamic tariff lookup from tariffs table
    - Correct handling of cumulative counters
    - Greek Net Metering implementation
    """

    def __init__(self, db_config: Optional[Dict] = None):
        """Initialize calculator with database configuration"""
        self.db_config = db_config or self._load_default_db_config()

    def _load_default_db_config(self) -> Dict:
        """Load default database configuration from environment"""
        return {
            'host': os.getenv('DB_HOST', 'postgres'),
            'database': os.getenv('DB_NAME', 'solar_prediction'),
            'user': os.getenv('DB_USER', 'postgres'),
            'password': os.getenv('DB_PASSWORD', 'postgres'),
            'port': os.getenv('DB_PORT', '5432')
        }

    def _get_db_connection(self) -> psycopg2.extensions.connection:
        """Get database connection"""
        try:
            return psycopg2.connect(**self.db_config)
        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            raise

    def get_production_data(self, system_id: str, start_date: Optional[datetime] = None,
                           end_date: Optional[datetime] = None) -> Dict:
        """
        Get production data for specified system and date range from database
        """
        try:
            conn = self._get_db_connection()
            cur = conn.cursor()
            
            # Determine table name
            table_name = 'solax_data' if system_id == 'system1' else 'solax_data2'
            
            # Build date filter
            date_filter = ""
            params = []
            if start_date:
                date_filter += " AND timestamp >= %s"
                params.append(start_date)
            if end_date:
                date_filter += " AND timestamp <= %s"
                params.append(end_date)

            # Get production data using CORRECT reset logic
            query = f"""
            WITH daily_yields AS (
                SELECT
                    DATE(timestamp) as date,
                    -- CORRECT: Handle yield_today reset logic
                    CASE
                        WHEN MIN(yield_today) < MAX(yield_today) THEN
                            MAX(yield_today) - MIN(yield_today)
                        ELSE
                            MAX(yield_today)
                    END as daily_yield,
                    MIN(timestamp) as first_timestamp,
                    MAX(timestamp) as last_timestamp
                FROM {table_name}
                WHERE yield_today >= 0 {date_filter}
                GROUP BY DATE(timestamp)
                HAVING MAX(yield_today) > 0
            )
            SELECT
                MIN(first_timestamp) as start_date,
                MAX(last_timestamp) as end_date,
                SUM(daily_yield) as total_production,
                COUNT(*) as total_days,
                AVG(daily_yield) as avg_daily_production
            FROM daily_yields;
            """

            cur.execute(query, params)
            result = cur.fetchone()

            if result and result[2]:  # total_production exists
                start_date, end_date, total_production, total_days, avg_daily = result
                return {
                    'start_date': start_date,
                    'end_date': end_date,
                    'total_production': float(total_production),
                    'total_days': int(total_days),
                    'avg_daily_production': float(avg_daily) if avg_daily else 0,
                    'operational_years': total_days / 365.25 if total_days else 0
                }
            else:
                return {
                    'start_date': None,
                    'end_date': None,
                    'total_production': 0,
                    'total_days': 0,
                    'avg_daily_production': 0,
                    'operational_years': 0
                }

        except Exception as e:
            logger.error(f"Error getting production data: {e}")
            raise
        finally:
            if 'conn' in locals():
                conn.close()

    def calculate_dynamic_consumption_rates(self, system_id: str, start_date: Optional[datetime] = None,
                                          end_date: Optional[datetime] = None) -> ConsumptionRates:
        """
        Calculate dynamic consumption rates from REAL database data
        Completely removes hardcoded values and uses only database queries
        """
        try:
            conn = self._get_db_connection()
            cur = conn.cursor()
            
            # Determine table name based on system
            table_name = 'solax_data' if system_id == 'system1' else 'solax_data2'
            
            # Set default date range if not provided
            if not start_date or not end_date:
                cur.execute(f"""
                    SELECT MIN(timestamp), MAX(timestamp) 
                    FROM {table_name} 
                    WHERE yield_today > 0
                """)
                result = cur.fetchone()
                if result:
                    start_date = start_date or result[0]
                    end_date = end_date or result[1]
            
            # 1. Get total production using CORRECT reset logic
            cur.execute(f"""
                WITH daily_yields AS (
                    SELECT
                        DATE(timestamp) as date,
                        -- CORRECT: Handle yield_today reset logic
                        CASE
                            WHEN MIN(yield_today) < MAX(yield_today) THEN
                                MAX(yield_today) - MIN(yield_today)
                            ELSE
                                MAX(yield_today)
                        END as daily_yield
                    FROM {table_name}
                    WHERE yield_today >= 0
                    AND timestamp >= %s AND timestamp <= %s
                    GROUP BY DATE(timestamp)
                    HAVING MAX(yield_today) > 0
                )
                SELECT
                    COALESCE(SUM(daily_yield), 0) as total_production_kwh,
                    COUNT(*) as production_days
                FROM daily_yields
            """, (start_date, end_date))
            
            production_result = cur.fetchone()
            total_production = float(production_result[0]) if production_result[0] else 0
            production_days = int(production_result[1]) if production_result[1] else 0
            
            if total_production == 0:
                print(f"⚠️ No production data found for {system_id} in date range")
                return self._create_empty_consumption_rates()
            
            # 2. Get consumption and feedin data using final cumulative values (correct method)
            cur.execute(f"""
                SELECT
                    MAX(consume_energy) as total_consumption,
                    MAX(feedin_energy) as total_feedin
                FROM {table_name}
                WHERE timestamp >= %s AND timestamp <= %s
                  AND consume_energy IS NOT NULL
                  AND feedin_energy IS NOT NULL
            """, (start_date, end_date))
            
            consumption_result = cur.fetchone()
            total_consumption = float(consumption_result[0]) if consumption_result[0] else 0
            total_feedin = float(consumption_result[1]) if consumption_result[1] else 0
            consumption_days = production_days  # Use same period as production
            
            # 3. Calculate energy flows from real data
            total_surplus = total_feedin  # What went to grid
            total_self_consumption = total_production - total_surplus  # What stayed in system
            total_grid_import = max(0, total_consumption - total_self_consumption)  # What came from grid
            
            # 4. Calculate rates from real data
            self_consumption_rate = total_self_consumption / total_production if total_production > 0 else 0
            surplus_rate = total_surplus / total_production if total_production > 0 else 0
            grid_import_rate = total_grid_import / total_production if total_production > 0 else 0
            grid_consumption_rate = total_grid_import / total_consumption if total_consumption > 0 else 0
            
            # 5. Validation and logging
            print(f"✅ {system_id.upper()} - Dynamic Data from Database:")
            print(f"   Production: {total_production:,.0f} kWh ({production_days} days)")
            print(f"   Consumption: {total_consumption:,.0f} kWh ({consumption_days} days)")
            print(f"   Self-consumption: {total_self_consumption:,.0f} kWh ({self_consumption_rate*100:.1f}%)")
            print(f"   Surplus to grid: {total_surplus:,.0f} kWh ({surplus_rate*100:.1f}%)")
            print(f"   Grid import: {total_grid_import:,.0f} kWh ({grid_consumption_rate*100:.1f}% of consumption)")
            
            # Energy balance validation
            calculated_consumption = total_self_consumption + total_grid_import
            balance_error = abs(calculated_consumption - total_consumption)
            
            if balance_error > total_consumption * 0.05:  # 5% tolerance
                print(f"⚠️ Energy balance warning: {balance_error:,.0f} kWh difference")
            
            return ConsumptionRates(
                self_consumption_rate=self_consumption_rate,
                surplus_rate=surplus_rate,
                grid_import_rate=grid_import_rate,
                grid_consumption_rate=grid_consumption_rate,
                total_production=total_production,
                total_consumption=total_consumption,
                total_surplus=total_surplus,
                total_grid_import=total_grid_import
            )
            
        except Exception as e:
            logger.error(f"Error calculating dynamic consumption rates: {e}")
            print(f"❌ Database error for {system_id}, using fallback empty rates")
            return self._create_empty_consumption_rates()
            
        finally:
            if 'conn' in locals():
                conn.close()

    def _create_empty_consumption_rates(self) -> ConsumptionRates:
        """Create empty consumption rates for error cases"""
        return ConsumptionRates(
            self_consumption_rate=0,
            surplus_rate=0,
            grid_import_rate=0,
            grid_consumption_rate=0,
            total_production=0,
            total_consumption=0,
            total_surplus=0,
            total_grid_import=0
        )

    def get_versioned_tariff(self, system_id: str, timestamp: datetime,
                            category: str, subcategory: Optional[str] = None) -> float:
        """
        Get tariff value from database with fallback to current Greek rates
        """
        try:
            conn = self._get_db_connection()
            cur = conn.cursor()
            
            # Try to get tariff from database first
            cur.execute("""
                SELECT value FROM tariffs
                WHERE category = %s
                AND effective_date <= %s
                ORDER BY effective_date DESC
                LIMIT 1
            """, (category, timestamp))
            
            result = cur.fetchone()
            if result and result[0] is not None:
                return float(result[0])
            
            # Fallback to current Greek tariffs from your system
            current_tariffs = {
                'day_energy': 0.142,      # From your tariffs table
                'night_energy': 0.132,    # From your tariffs table  
                'network_tier1': 0.007,   # From your tariffs table
                'network_tier2': 0.050,   # From your tariffs table
                'network_tier3': 0.085,   # From your tariffs table
                'etmear': 0.017,          # From your tariffs table
                'monthly_fixed': 3.50,    # From your tariffs table
                'feed_in_tariff': 0.000,  # Net Metering
                'vat_rate': 0.24          # From your tariffs table
            }
            
            return current_tariffs.get(category, 0.0)
            
        except Exception as e:
            logger.error(f"Error getting tariff: {e}")
            return 0.0
        finally:
            if 'conn' in locals():
                conn.close()

    def calculate_financial_metrics_with_time_zones(self, consumption_rates: ConsumptionRates,
                                                  system_id: str, start_date: datetime,
                                                  end_date: datetime) -> FinancialMetrics:
        """
        Calculate financial metrics using dynamic tariffs from database
        """
        try:
            # Get current tariffs from database
            current_time = datetime.now()
            
            avg_energy_rate = self.get_versioned_tariff(system_id, current_time, 'day_energy')
            network_charge_rate = self.get_versioned_tariff(system_id, current_time, 'network_tier1') 
            etmear_rate = self.get_versioned_tariff(system_id, current_time, 'etmear')
            feed_in_rate = self.get_versioned_tariff(system_id, current_time, 'feed_in_tariff')
            
            print(f"💰 Using dynamic tariff rates for {system_id}:")
            print(f"   Energy rate: €{avg_energy_rate:.3f}/kWh")
            print(f"   Network charge: €{network_charge_rate:.3f}/kWh")
            print(f"   ETMEAR: €{etmear_rate:.3f}/kWh")
            print(f"   Feed-in tariff: €{feed_in_rate:.3f}/kWh")
            
            # Calculate financial components using real consumption data
            self_consumption_kwh = consumption_rates.total_production * consumption_rates.self_consumption_rate
            energy_savings = self_consumption_kwh * avg_energy_rate
            
            # Net Metering logic - surplus offsets grid import
            surplus_offset_kwh = min(consumption_rates.total_surplus, consumption_rates.total_grid_import)
            surplus_value = surplus_offset_kwh * avg_energy_rate
            
            # Net grid import after surplus offset
            net_grid_import = max(0, consumption_rates.total_grid_import - consumption_rates.total_surplus)
            grid_energy_cost = net_grid_import * avg_energy_rate
            network_charges = net_grid_import * network_charge_rate
            etmear_charges = net_grid_import * etmear_rate
            
            total_charges = grid_energy_cost + network_charges + etmear_charges
            net_benefit = energy_savings + surplus_value - total_charges
            
            # Annualize based on operational period
            operational_years = (end_date - start_date).days / 365.25
            annual_benefit = net_benefit / operational_years if operational_years > 0 else 0
            
            print(f"💡 Financial breakdown for {system_id} (Dynamic Rates):")
            print(f"   Self-consumption savings: €{energy_savings:.2f}")
            print(f"   Surplus offset value: €{surplus_value:.2f}")
            print(f"   Net grid costs: €{grid_energy_cost:.2f}")
            print(f"   Network charges: €{network_charges:.2f}")
            print(f"   ETMEAR charges: €{etmear_charges:.2f}")
            print(f"   Annual benefit: €{annual_benefit:.2f}")
            
            return FinancialMetrics(
                energy_savings=energy_savings,
                surplus_income=surplus_value,
                grid_costs=grid_energy_cost,
                network_charges=network_charges,
                etmear_charges=etmear_charges,
                total_charges=total_charges,
                net_benefit=net_benefit,
                annual_benefit=annual_benefit
            )
            
        except Exception as e:
            logger.error(f"Error calculating financial metrics: {e}")
            raise

    def calculate_roi(self, system_id: str, investment_cost: float = 12500.0,
                     start_date: Optional[datetime] = None,
                     end_date: Optional[datetime] = None) -> ROIAnalysis:
        """
        Calculate comprehensive ROI analysis with 100% dynamic data
        """
        try:
            # Set default end date to now
            if end_date is None:
                end_date = datetime.now()

            # Get production data from database
            production_data = self.get_production_data(system_id, start_date, end_date)

            if production_data['total_production'] == 0:
                return ROIAnalysis(
                    system_id=system_id,
                    investment_cost=investment_cost,
                    operational_period={'days': 0, 'years': 0},
                    production_data=production_data,
                    consumption_rates=self._create_empty_consumption_rates(),
                    financial_metrics=FinancialMetrics(0, 0, 0, 0, 0, 0, 0, 0),
                    roi_percentage=0,
                    payback_years=None,
                    status='insufficient_data'
                )

            # Use actual dates from production data
            actual_start = production_data['start_date']
            actual_end = production_data['end_date']

            # Calculate dynamic consumption rates from database
            consumption_rates = self.calculate_dynamic_consumption_rates(system_id, actual_start, actual_end)

            # Calculate financial metrics with dynamic tariffs
            financial_metrics = self.calculate_financial_metrics_with_time_zones(
                consumption_rates, system_id, actual_start, actual_end
            )

            # Calculate ROI
            roi_percentage = (financial_metrics.annual_benefit / investment_cost * 100) if investment_cost > 0 else 0
            payback_years = investment_cost / financial_metrics.annual_benefit if financial_metrics.annual_benefit > 0 else None

            # Operational period info
            operational_days = (actual_end - actual_start).days if actual_start and actual_end else 0
            operational_years = operational_days / 365.25

            return ROIAnalysis(
                system_id=system_id,
                investment_cost=investment_cost,
                operational_period={
                    'start_date': actual_start.isoformat() if actual_start else None,
                    'end_date': actual_end.isoformat() if actual_end else None,
                    'days': operational_days,
                    'years': round(operational_years, 2)
                },
                production_data={
                    'total_production_kwh': round(production_data['total_production'], 2),
                    'avg_daily_production_kwh': round(production_data['avg_daily_production'], 2),
                    'total_days': production_data['total_days']
                },
                consumption_rates=consumption_rates,
                financial_metrics=financial_metrics,
                roi_percentage=round(roi_percentage, 2),
                payback_years=round(payback_years, 2) if payback_years and payback_years != float('inf') else None,
                status='calculated'
            )

        except Exception as e:
            logger.error(f"Error calculating ROI: {e}")
            raise

    def get_roi_summary(self, system_id: str, investment_cost: float = 12500.0) -> Dict:
        """
        Get ROI summary in format compatible with existing APIs
        """
        try:
            roi_analysis = self.calculate_roi(system_id, investment_cost)

            return {
                "system_id": roi_analysis.system_id,
                "investment_cost_eur": roi_analysis.investment_cost,
                "operational_period": roi_analysis.operational_period,
                "production": roi_analysis.production_data,
                "consumption_analysis": {
                    "self_consumption_rate": round(roi_analysis.consumption_rates.self_consumption_rate * 100, 1),
                    "surplus_rate": round(roi_analysis.consumption_rates.surplus_rate * 100, 1),
                    "annual_production_kwh": round(roi_analysis.consumption_rates.total_production / roi_analysis.operational_period['years'], 0) if roi_analysis.operational_period['years'] > 0 else 0
                },
                "financial": {
                    "annual_savings_eur": round(roi_analysis.financial_metrics.annual_benefit, 2),
                    "annual_roi_percent": roi_analysis.roi_percentage,
                    "payback_years": roi_analysis.payback_years,
                    "annual_benefit_eur": round(roi_analysis.financial_metrics.annual_benefit, 2)
                },
                "status": roi_analysis.status,
                "timestamp": datetime.now().isoformat(),
                "calculation_method": "enhanced_dynamic_database_rates"
            }

        except Exception as e:
            logger.error(f"Error getting ROI summary: {e}")
            raise

# Example usage
if __name__ == "__main__":
    calculator = UnifiedROICalculator()
    
    # Test both systems
    for system_id in ['system1', 'system2']:
        try:
            print(f"\n{'='*50}")
            print(f"Testing {system_id.upper()} with Dynamic Data")
            print(f"{'='*50}")
            
            summary = calculator.get_roi_summary(system_id)
            
            print(f"\n🎯 Final Results for {system_id}:")
            print(f"   ROI: {summary['financial']['annual_roi_percent']:.1f}%")
            print(f"   Payback: {summary['financial']['payback_years']:.1f} years")
            print(f"   Annual Benefit: €{summary['financial']['annual_benefit_eur']:,.2f}")
            print(f"   Annual Production: {summary['consumption_analysis']['annual_production_kwh']:,.0f} kWh")
            print(f"   Self-consumption: {summary['consumption_analysis']['self_consumption_rate']:.1f}%")
            print(f"   Surplus: {summary['consumption_analysis']['surplus_rate']:.1f}%")
            
        except Exception as e:
            print(f"❌ Error testing {system_id}: {e}")

