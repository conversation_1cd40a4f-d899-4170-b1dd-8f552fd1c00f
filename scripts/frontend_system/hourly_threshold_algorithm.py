#!/usr/bin/env python3
"""
Hourly Threshold Algorithm for Solar Production Alerts
=====================================================

This module implements an intelligent algorithm that calculates expected
solar production for each hour based on historical data and current weather
conditions, replacing the fixed 30 kWh threshold with dynamic, context-aware
thresholds.

Author: Solar Prediction System
Date: June 23, 2025
"""

import logging
import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import statistics
import numpy as np

logger = logging.getLogger(__name__)

class HourlyThresholdCalculator:
    """
    Calculates dynamic thresholds for solar production alerts based on:
    1. Historical data for the same hour from previous days
    2. Current weather conditions
    3. Seasonal adjustments
    4. System-specific patterns
    """
    
    def __init__(self, db_config: Dict):
        self.db_config = db_config
        self.historical_days = 14  # Look back 14 days
        self.min_data_points = 5   # Minimum historical data points needed
        
    def get_historical_data_for_hour(self, hour: int, system_id: str, 
                                   reference_date: datetime) -> List[float]:
        """
        Get historical yield data for the same hour from previous days
        
        Args:
            hour: Hour of day (0-23)
            system_id: 'system1' or 'system2'
            reference_date: Date to look back from
            
        Returns:
            List of yield values for the same hour from previous days
        """
        try:
            conn = psycopg2.connect(**self.db_config)
            cur = conn.cursor(cursor_factory=RealDictCursor)
            
            table_name = 'solax_data' if system_id == 'system1' else 'solax_data2'
            
            # Get data from same hour for last N days
            start_date = reference_date - timedelta(days=self.historical_days)
            
            query = f"""
            WITH hourly_yields AS (
                SELECT 
                    DATE(timestamp) as date,
                    EXTRACT(hour FROM timestamp) as hour,
                    MAX(yield_today) as daily_yield_at_hour
                FROM {table_name}
                WHERE timestamp >= %s 
                AND timestamp < %s
                AND EXTRACT(hour FROM timestamp) = %s
                AND yield_today IS NOT NULL
                AND yield_today > 0
                GROUP BY DATE(timestamp), EXTRACT(hour FROM timestamp)
                HAVING COUNT(*) > 5  -- Ensure enough data points per hour
            )
            SELECT daily_yield_at_hour
            FROM hourly_yields
            WHERE daily_yield_at_hour BETWEEN 1 AND 100  -- Filter outliers
            ORDER BY date DESC
            """
            
            cur.execute(query, (start_date, reference_date, hour))
            results = cur.fetchall()
            
            conn.close()
            
            historical_yields = [float(row['daily_yield_at_hour']) for row in results]
            logger.debug(f"Found {len(historical_yields)} historical data points for {system_id} hour {hour}")
            
            return historical_yields
            
        except Exception as e:
            logger.error(f"Error getting historical data: {e}")
            return []
    
    def get_current_weather_conditions(self, reference_time: datetime) -> Dict:
        """
        Get current weather conditions for threshold calculation
        
        Args:
            reference_time: Time to get weather for
            
        Returns:
            Dictionary with weather parameters
        """
        try:
            conn = psycopg2.connect(**self.db_config)
            cur = conn.cursor(cursor_factory=RealDictCursor)
            
            # Get latest weather data within 2 hours of reference time
            query = """
            SELECT 
                global_horizontal_irradiance as ghi,
                temperature_2m as temperature,
                cloud_cover,
                direct_radiation,
                diffuse_radiation
            FROM weather_data
            WHERE timestamp BETWEEN %s AND %s
            ORDER BY timestamp DESC
            LIMIT 1
            """
            
            start_time = reference_time - timedelta(hours=1)
            end_time = reference_time + timedelta(hours=1)
            
            cur.execute(query, (start_time, end_time))
            result = cur.fetchone()
            
            conn.close()
            
            if result:
                return {
                    'ghi': float(result['ghi'] or 500),
                    'temperature': float(result['temperature'] or 25),
                    'cloud_cover': float(result['cloud_cover'] or 30),
                    'direct_radiation': float(result['direct_radiation'] or 300),
                    'diffuse_radiation': float(result['diffuse_radiation'] or 200)
                }
            else:
                # Default values if no weather data
                logger.warning("No weather data found, using defaults")
                return {
                    'ghi': 500,
                    'temperature': 25,
                    'cloud_cover': 30,
                    'direct_radiation': 300,
                    'diffuse_radiation': 200
                }
                
        except Exception as e:
            logger.error(f"Error getting weather conditions: {e}")
            return {
                'ghi': 500,
                'temperature': 25,
                'cloud_cover': 30,
                'direct_radiation': 300,
                'diffuse_radiation': 200
            }
    
    def calculate_weather_factor(self, current_weather: Dict, 
                               historical_weather_avg: Dict) -> float:
        """
        Calculate weather adjustment factor for expected yield
        
        Args:
            current_weather: Current weather conditions
            historical_weather_avg: Average weather for historical period
            
        Returns:
            Weather adjustment factor (0.2 to 1.5)
        """
        try:
            # GHI factor (primary driver of solar production)
            ghi_current = current_weather.get('ghi', 500)
            ghi_historical = historical_weather_avg.get('ghi', 500)
            ghi_factor = ghi_current / ghi_historical if ghi_historical > 0 else 1.0
            
            # Cloud cover factor (reduces production)
            cloud_current = current_weather.get('cloud_cover', 30)
            cloud_factor = 1.0 - (cloud_current / 100) * 0.4  # Max 40% reduction
            
            # Temperature factor (panels less efficient when hot)
            temp_current = current_weather.get('temperature', 25)
            temp_factor = 1.0 - max(0, temp_current - 25) * 0.004  # 0.4% per degree above 25°C
            
            # Combined factor with safety limits
            combined_factor = ghi_factor * cloud_factor * temp_factor
            
            # Limit to reasonable range
            return max(0.2, min(1.5, combined_factor))
            
        except Exception as e:
            logger.error(f"Error calculating weather factor: {e}")
            return 1.0
    
    def filter_outliers(self, data: List[float], method: str = 'iqr') -> List[float]:
        """
        Filter outliers from historical data
        
        Args:
            data: List of values
            method: 'iqr' or 'zscore'
            
        Returns:
            Filtered list without outliers
        """
        if len(data) < 3:
            return data
            
        try:
            if method == 'iqr':
                q1 = np.percentile(data, 25)
                q3 = np.percentile(data, 75)
                iqr = q3 - q1
                lower_bound = q1 - 1.5 * iqr
                upper_bound = q3 + 1.5 * iqr
                
                filtered = [x for x in data if lower_bound <= x <= upper_bound]
                
            else:  # zscore method
                mean = statistics.mean(data)
                stdev = statistics.stdev(data) if len(data) > 1 else 0
                
                if stdev == 0:
                    return data
                    
                filtered = [x for x in data if abs((x - mean) / stdev) < 2.5]
            
            logger.debug(f"Filtered {len(data) - len(filtered)} outliers from {len(data)} data points")
            return filtered if len(filtered) >= 3 else data
            
        except Exception as e:
            logger.error(f"Error filtering outliers: {e}")
            return data

    def calculate_expected_yield_for_hour(self, hour: int, system_id: str,
                                        reference_time: datetime) -> Tuple[float, float, Dict]:
        """
        Calculate expected yield and threshold for a specific hour

        Args:
            hour: Hour of day (0-23)
            system_id: 'system1' or 'system2'
            reference_time: Current time for calculation

        Returns:
            Tuple of (threshold, expected_yield, metadata)
        """
        try:
            # 1. Get historical data for this hour
            historical_yields = self.get_historical_data_for_hour(hour, system_id, reference_time.date())

            if len(historical_yields) < self.min_data_points:
                logger.warning(f"Insufficient historical data for {system_id} hour {hour}: {len(historical_yields)} points")
                # Fallback to simple hour-based estimates
                return self._get_fallback_threshold(hour, system_id)

            # 2. Filter outliers
            filtered_yields = self.filter_outliers(historical_yields)

            # 3. Calculate base expected yield (use median for robustness)
            base_expected = statistics.median(filtered_yields)

            # 4. Get current weather conditions
            current_weather = self.get_current_weather_conditions(reference_time)

            # 5. Get historical weather average (simplified for now)
            historical_weather_avg = {
                'ghi': 600,  # Average summer GHI
                'temperature': 26,  # Average summer temperature
                'cloud_cover': 25   # Average cloud cover
            }

            # 6. Calculate weather adjustment factor
            weather_factor = self.calculate_weather_factor(current_weather, historical_weather_avg)

            # 7. Calculate adjusted expected yield
            expected_yield = base_expected * weather_factor

            # 8. Calculate threshold (70% of expected, but with minimum values)
            threshold_percentage = 0.7
            threshold = expected_yield * threshold_percentage

            # 9. Apply hour-specific minimum thresholds to avoid false positives
            min_thresholds = {
                6: 2, 7: 5, 8: 10, 9: 18, 10: 25, 11: 30, 12: 35,
                13: 38, 14: 35, 15: 30, 16: 25, 17: 18, 18: 10
            }

            min_threshold = min_thresholds.get(hour, 5)
            threshold = max(threshold, min_threshold)

            # 10. Prepare metadata
            metadata = {
                'historical_data_points': len(historical_yields),
                'filtered_data_points': len(filtered_yields),
                'base_expected': base_expected,
                'weather_factor': weather_factor,
                'current_weather': current_weather,
                'threshold_percentage': threshold_percentage,
                'min_threshold_applied': threshold == min_threshold
            }

            logger.debug(f"Hour {hour} {system_id}: expected={expected_yield:.1f}, threshold={threshold:.1f}, weather_factor={weather_factor:.2f}")

            return threshold, expected_yield, metadata

        except Exception as e:
            logger.error(f"Error calculating expected yield for hour {hour}: {e}")
            return self._get_fallback_threshold(hour, system_id)

    def _get_fallback_threshold(self, hour: int, system_id: str) -> Tuple[float, float, Dict]:
        """
        Fallback threshold calculation when insufficient historical data

        Args:
            hour: Hour of day
            system_id: System identifier

        Returns:
            Tuple of (threshold, expected_yield, metadata)
        """
        # Simple hour-based estimates based on typical solar production curve
        hourly_estimates = {
            6: 3, 7: 8, 8: 15, 9: 25, 10: 35, 11: 42, 12: 45,
            13: 45, 14: 42, 15: 35, 16: 25, 17: 15, 18: 8, 19: 3
        }

        expected = hourly_estimates.get(hour, 0)
        threshold = expected * 0.6  # More conservative for fallback

        metadata = {
            'fallback_used': True,
            'reason': 'insufficient_historical_data'
        }

        return threshold, expected, metadata

    def should_send_alert(self, current_yield: float, expected_yield: float,
                         threshold: float, hour: int, weather: Dict,
                         system_id: str) -> Tuple[bool, str]:
        """
        Determine if an alert should be sent based on intelligent criteria

        Args:
            current_yield: Current production
            expected_yield: Expected production for this hour
            threshold: Calculated threshold
            hour: Current hour
            weather: Current weather conditions
            system_id: System identifier

        Returns:
            Tuple of (should_alert, reason)
        """
        try:
            # 1. Basic threshold check
            if current_yield >= threshold:
                return False, "above_threshold"

            # 2. Very early hours (6-7) - be more lenient
            if 6 <= hour <= 7 and current_yield > 3:
                return False, "early_morning_acceptable"

            # 3. Late hours (18-19) - be more lenient
            if 18 <= hour <= 19 and current_yield > expected_yield * 0.8:
                return False, "late_afternoon_acceptable"

            # 4. Extreme weather conditions - don't alert
            cloud_cover = weather.get('cloud_cover', 0)
            ghi = weather.get('ghi', 500)

            if cloud_cover > 85:  # Very cloudy
                return False, "very_cloudy_conditions"

            if ghi < 150:  # Very low solar irradiance
                return False, "very_low_irradiance"

            # 5. Calculate deviation from expected
            if expected_yield > 0:
                deviation_percentage = (expected_yield - current_yield) / expected_yield
            else:
                deviation_percentage = 0

            # 6. Only alert if significantly below expected (>25% deviation)
            if deviation_percentage > 0.25:
                severity = "critical" if deviation_percentage > 0.5 else "warning"
                reason = f"production_{deviation_percentage*100:.0f}%_below_expected"
                return True, reason

            # 7. Check for absolute low production during peak hours
            if 10 <= hour <= 15 and current_yield < 20:
                return True, "very_low_production_peak_hours"

            return False, "within_acceptable_range"

        except Exception as e:
            logger.error(f"Error in should_send_alert: {e}")
            return False, "error_in_calculation"

    def get_alert_message(self, system_id: str, hour: int, current_yield: float,
                         expected_yield: float, threshold: float, reason: str,
                         weather: Dict) -> str:
        """
        Generate intelligent alert message with context

        Args:
            system_id: System identifier
            hour: Current hour
            current_yield: Current production
            expected_yield: Expected production
            threshold: Threshold value
            reason: Reason for alert
            weather: Weather conditions

        Returns:
            Formatted alert message
        """
        try:
            deviation = ((expected_yield - current_yield) / expected_yield * 100) if expected_yield > 0 else 0

            # Base message
            message = f"🌞 {system_id.upper()}: Low solar production at {hour:02d}:00\n\n"

            # Production details
            message += f"📊 Current: {current_yield:.1f} kWh\n"
            message += f"📈 Expected: {expected_yield:.1f} kWh\n"
            message += f"📉 Deviation: {deviation:.0f}% below expected\n\n"

            # Weather context
            ghi = weather.get('ghi', 0)
            cloud_cover = weather.get('cloud_cover', 0)
            temperature = weather.get('temperature', 0)

            message += f"🌤️ Weather:\n"
            message += f"   ☀️ Solar irradiance: {ghi:.0f} W/m²\n"
            message += f"   ☁️ Cloud cover: {cloud_cover:.0f}%\n"
            message += f"   🌡️ Temperature: {temperature:.1f}°C\n\n"

            # Contextual advice
            if "peak_hours" in reason:
                message += "⚠️ Very low production during peak solar hours - check system status"
            elif deviation > 50:
                message += "🚨 Critical: Production significantly below normal - immediate inspection recommended"
            elif deviation > 25:
                message += "⚠️ Warning: Production below normal - monitor system performance"
            else:
                message += "ℹ️ Info: Slight production decrease detected"

            return message

        except Exception as e:
            logger.error(f"Error generating alert message: {e}")
            return f"{system_id}: Low production detected at {hour:02d}:00 ({current_yield:.1f} kWh)"


# Example usage and testing functions
def test_algorithm():
    """Test the hourly threshold algorithm with current data"""

    # Database configuration
    db_config = {
        'host': 'localhost',
        'port': 5433,
        'database': 'solar_prediction',
        'user': 'postgres',
        'password': 'postgres'
    }

    calculator = HourlyThresholdCalculator(db_config)

    # Test with current time
    current_time = datetime.now()
    current_hour = current_time.hour

    print(f"🧪 Testing Hourly Threshold Algorithm")
    print(f"📅 Time: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"⏰ Hour: {current_hour}")
    print("="*60)

    for system_id in ['system1', 'system2']:
        print(f"\n🔍 Testing {system_id.upper()}:")

        # Calculate threshold and expected yield
        threshold, expected_yield, metadata = calculator.calculate_expected_yield_for_hour(
            current_hour, system_id, current_time
        )

        print(f"   📊 Expected yield: {expected_yield:.1f} kWh")
        print(f"   🎯 Threshold: {threshold:.1f} kWh")
        print(f"   📈 Weather factor: {metadata.get('weather_factor', 1.0):.2f}")
        print(f"   📋 Historical points: {metadata.get('historical_data_points', 0)}")

        # Get current actual yield (simulate)
        try:
            conn = psycopg2.connect(**db_config)
            cur = conn.cursor(cursor_factory=RealDictCursor)

            table_name = 'solax_data' if system_id == 'system1' else 'solax_data2'
            cur.execute(f"""
                SELECT MAX(yield_today) as current_yield
                FROM {table_name}
                WHERE DATE(timestamp) = CURRENT_DATE
            """)

            result = cur.fetchone()
            current_yield = float(result['current_yield'] or 0)
            conn.close()

            print(f"   ⚡ Current yield: {current_yield:.1f} kWh")

            # Test alert logic
            weather = metadata.get('current_weather', {})
            should_alert, reason = calculator.should_send_alert(
                current_yield, expected_yield, threshold, current_hour, weather, system_id
            )

            print(f"   🚨 Should alert: {'YES' if should_alert else 'NO'}")
            if should_alert:
                print(f"   📝 Reason: {reason}")
                message = calculator.get_alert_message(
                    system_id, current_hour, current_yield, expected_yield,
                    threshold, reason, weather
                )
                print(f"   💬 Alert message preview:")
                print("   " + "\n   ".join(message.split('\n')[:5]) + "...")

        except Exception as e:
            print(f"   ❌ Error getting current data: {e}")


if __name__ == "__main__":
    test_algorithm()
