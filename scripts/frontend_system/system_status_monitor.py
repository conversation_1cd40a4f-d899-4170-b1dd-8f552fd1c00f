#!/usr/bin/env python3
"""
System Status Monitor for Solar Prediction System
Monitors all services and provides comprehensive status reports
"""

import sys
import os
import time
import json
import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime, timedelta
import subprocess
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

class SystemStatusMonitor:
    """Comprehensive system status monitor"""
    
    def __init__(self):
        self.db_config = DB_CONFIG
        self.start_time = datetime.now()
        
    def get_db_connection(self):
        """Get database connection"""
        try:
            return psycopg2.connect(**self.db_config)
        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            return None
    
    def check_database_status(self) -> dict:
        """Check database connectivity and data freshness"""
        status = {
            'service': 'PostgreSQL Database',
            'status': 'unknown',
            'details': {},
            'last_check': datetime.now()
        }
        
        try:
            conn = self.get_db_connection()
            if not conn:
                status['status'] = 'error'
                status['details']['error'] = 'Connection failed'
                return status
            
            cur = conn.cursor(cursor_factory=RealDictCursor)
            
            # Check data freshness
            now = datetime.now()
            recent_threshold = now - timedelta(minutes=10)
            
            # System 1 data freshness
            cur.execute('''
                SELECT COUNT(*) as recent_count, MAX(timestamp) as latest_timestamp
                FROM solax_data 
                WHERE timestamp > %s
            ''', (recent_threshold,))
            system1_data = cur.fetchone()
            
            # System 2 data freshness
            cur.execute('''
                SELECT COUNT(*) as recent_count, MAX(timestamp) as latest_timestamp
                FROM solax_data2 
                WHERE timestamp > %s
            ''', (recent_threshold,))
            system2_data = cur.fetchone()
            
            # Weather data freshness
            cur.execute('''
                SELECT COUNT(*) as recent_count, MAX(timestamp) as latest_timestamp
                FROM weather_data 
                WHERE timestamp > %s
            ''', (now - timedelta(hours=2),))  # Weather updates hourly
            weather_data = cur.fetchone()
            
            # Total record counts
            cur.execute('SELECT COUNT(*) as total FROM solax_data')
            system1_total = cur.fetchone()['total']
            
            cur.execute('SELECT COUNT(*) as total FROM solax_data2')
            system2_total = cur.fetchone()['total']
            
            cur.execute('SELECT COUNT(*) as total FROM weather_data')
            weather_total = cur.fetchone()['total']
            
            # Determine overall status
            system1_fresh = system1_data['recent_count'] >= 10
            system2_fresh = system2_data['recent_count'] >= 10
            weather_fresh = weather_data['recent_count'] >= 1
            
            if system1_fresh and system2_fresh and weather_fresh:
                status['status'] = 'healthy'
            elif system1_fresh and system2_fresh:
                status['status'] = 'warning'  # Weather might be delayed
            else:
                status['status'] = 'error'
            
            status['details'] = {
                'connection': 'successful',
                'system1': {
                    'recent_records': system1_data['recent_count'],
                    'latest_timestamp': str(system1_data['latest_timestamp']),
                    'total_records': system1_total,
                    'status': 'healthy' if system1_fresh else 'stale'
                },
                'system2': {
                    'recent_records': system2_data['recent_count'],
                    'latest_timestamp': str(system2_data['latest_timestamp']),
                    'total_records': system2_total,
                    'status': 'healthy' if system2_fresh else 'stale'
                },
                'weather': {
                    'recent_records': weather_data['recent_count'],
                    'latest_timestamp': str(weather_data['latest_timestamp']),
                    'total_records': weather_total,
                    'status': 'healthy' if weather_fresh else 'stale'
                }
            }
            
            conn.close()
            
        except Exception as e:
            status['status'] = 'error'
            status['details']['error'] = str(e)
        
        return status
    
    def check_process_status(self) -> dict:
        """Check running processes"""
        status = {
            'service': 'System Processes',
            'status': 'unknown',
            'details': {},
            'last_check': datetime.now()
        }
        
        try:
            # Get all python processes
            result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
            processes = result.stdout
            
            # Look for our specific processes
            services = {
                'data_collector': 'simple_data_collector.py',
                'weather_collector': 'weather_data_collector.py',
                'telegram_bot': 'greek_telegram_bot.py',
                'production_api': 'enhanced_production_app.py',
                'web_server': 'http.server'
            }
            
            running_services = {}
            
            for service_name, process_pattern in services.items():
                if process_pattern in processes:
                    running_services[service_name] = 'running'
                else:
                    running_services[service_name] = 'stopped'
            
            # Determine overall status
            critical_services = ['data_collector', 'production_api']
            critical_running = all(running_services.get(s) == 'running' for s in critical_services)
            
            if critical_running:
                status['status'] = 'healthy'
            else:
                status['status'] = 'warning'
            
            status['details'] = running_services
            
        except Exception as e:
            status['status'] = 'error'
            status['details']['error'] = str(e)
        
        return status
    
    def check_api_endpoints(self) -> dict:
        """Check API endpoint availability"""
        status = {
            'service': 'API Endpoints',
            'status': 'unknown',
            'details': {},
            'last_check': datetime.now()
        }
        
        try:
            import requests
            
            endpoints = {
                'production_api': 'http://localhost:8100/health',
                'frontend': 'http://localhost:8080',
                'api_info': 'http://localhost:8102/api'
            }
            
            endpoint_status = {}
            
            for endpoint_name, url in endpoints.items():
                try:
                    response = requests.get(url, timeout=5)
                    if response.status_code == 200:
                        endpoint_status[endpoint_name] = 'healthy'
                    else:
                        endpoint_status[endpoint_name] = f'error_{response.status_code}'
                except:
                    endpoint_status[endpoint_name] = 'unreachable'
            
            # Determine overall status
            healthy_count = sum(1 for s in endpoint_status.values() if s == 'healthy')
            total_count = len(endpoint_status)
            
            if healthy_count == total_count:
                status['status'] = 'healthy'
            elif healthy_count >= total_count // 2:
                status['status'] = 'warning'
            else:
                status['status'] = 'error'
            
            status['details'] = endpoint_status
            
        except Exception as e:
            status['status'] = 'error'
            status['details']['error'] = str(e)
        
        return status
    
    def get_system_performance(self) -> dict:
        """Get system performance metrics"""
        performance = {
            'service': 'System Performance',
            'status': 'healthy',
            'details': {},
            'last_check': datetime.now()
        }
        
        try:
            # Data collection rate analysis
            conn = self.get_db_connection()
            if conn:
                cur = conn.cursor(cursor_factory=RealDictCursor)
                
                # Last 10 minutes data collection rate
                now = datetime.now()
                recent_threshold = now - timedelta(minutes=10)
                
                cur.execute('''
                    SELECT COUNT(*) as count 
                    FROM solax_data 
                    WHERE timestamp > %s
                ''', (recent_threshold,))
                system1_recent = cur.fetchone()['count']
                
                cur.execute('''
                    SELECT COUNT(*) as count 
                    FROM solax_data2 
                    WHERE timestamp > %s
                ''', (recent_threshold,))
                system2_recent = cur.fetchone()['count']
                
                # Expected: 20 records in 10 minutes (30s interval)
                expected_records = 20
                system1_rate = (system1_recent / expected_records) * 100
                system2_rate = (system2_recent / expected_records) * 100
                overall_rate = (system1_rate + system2_rate) / 2
                
                performance['details'] = {
                    'data_collection_rate': f"{overall_rate:.1f}%",
                    'system1_rate': f"{system1_rate:.1f}%",
                    'system2_rate': f"{system2_rate:.1f}%",
                    'expected_records_10min': expected_records,
                    'actual_records_10min': {
                        'system1': system1_recent,
                        'system2': system2_recent
                    },
                    'uptime': str(datetime.now() - self.start_time)
                }
                
                # Determine performance status
                if overall_rate >= 80:
                    performance['status'] = 'excellent'
                elif overall_rate >= 60:
                    performance['status'] = 'good'
                elif overall_rate >= 40:
                    performance['status'] = 'warning'
                else:
                    performance['status'] = 'poor'
                
                conn.close()
            
        except Exception as e:
            performance['status'] = 'error'
            performance['details']['error'] = str(e)
        
        return performance
    
    def generate_comprehensive_report(self) -> dict:
        """Generate comprehensive system status report"""
        
        logger.info("🔍 Generating comprehensive system status report...")
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'system_name': 'Solar Prediction System',
            'version': '1.0.0',
            'uptime': str(datetime.now() - self.start_time),
            'checks': {}
        }
        
        # Run all checks
        checks = [
            ('database', self.check_database_status),
            ('processes', self.check_process_status),
            ('api_endpoints', self.check_api_endpoints),
            ('performance', self.get_system_performance)
        ]
        
        overall_status = 'healthy'
        
        for check_name, check_function in checks:
            try:
                result = check_function()
                report['checks'][check_name] = result
                
                # Update overall status
                if result['status'] in ['error', 'poor']:
                    overall_status = 'error'
                elif result['status'] in ['warning', 'good'] and overall_status != 'error':
                    overall_status = 'warning'
                    
            except Exception as e:
                report['checks'][check_name] = {
                    'service': check_name,
                    'status': 'error',
                    'details': {'error': str(e)},
                    'last_check': datetime.now()
                }
                overall_status = 'error'
        
        report['overall_status'] = overall_status
        
        return report
    
    def print_status_report(self, report: dict):
        """Print formatted status report"""
        
        print("🌞 SOLAR PREDICTION SYSTEM - STATUS REPORT")
        print("="*70)
        print(f"📅 Timestamp: {report['timestamp']}")
        print(f"⏰ Uptime: {report['uptime']}")
        print(f"🎯 Overall Status: {self.format_status(report['overall_status'])}")
        print()
        
        for check_name, check_data in report['checks'].items():
            print(f"🔧 {check_data['service']}:")
            print(f"   Status: {self.format_status(check_data['status'])}")
            
            if 'details' in check_data:
                for key, value in check_data['details'].items():
                    if isinstance(value, dict):
                        print(f"   {key}:")
                        for sub_key, sub_value in value.items():
                            print(f"     {sub_key}: {sub_value}")
                    else:
                        print(f"   {key}: {value}")
            print()
    
    def format_status(self, status: str) -> str:
        """Format status with colors"""
        status_map = {
            'healthy': '✅ Healthy',
            'excellent': '🌟 Excellent',
            'good': '✅ Good',
            'warning': '⚠️ Warning',
            'error': '❌ Error',
            'poor': '🔴 Poor',
            'unknown': '❓ Unknown'
        }
        return status_map.get(status, status)

def main():
    """Main function"""
    
    print("🔍 SYSTEM STATUS MONITOR")
    print("="*60)
    print("📊 Comprehensive monitoring for Solar Prediction System")
    print()
    
    try:
        monitor = SystemStatusMonitor()
        
        # Generate and display report
        report = monitor.generate_comprehensive_report()
        monitor.print_status_report(report)
        
        # Save report to file
        os.makedirs('data/status', exist_ok=True)
        report_file = f"data/status/system_status_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        print(f"📄 Report saved to: {report_file}")
        
        return report['overall_status'] in ['healthy', 'excellent', 'good']
        
    except Exception as e:
        print(f"❌ Status monitor failed: {e}")
        logger.exception("Status monitor failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
