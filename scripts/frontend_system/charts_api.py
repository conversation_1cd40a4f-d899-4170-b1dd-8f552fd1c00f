#!/usr/bin/env python3
"""
Charts API for Solar Dashboard
Provides real-time data endpoints for charts and visualizations
"""

from fastapi import FastAPI, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime, timed<PERSON><PERSON>
from typing import List, Dict, Any, Optional
import uvicorn
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

app = FastAPI(title="Solar Charts API", version="1.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

def get_db_connection():
    """Get database connection"""
    try:
        return psycopg2.connect(**DB_CONFIG)
    except Exception as e:
        logger.error(f"Database connection failed: {e}")
        return None

@app.get("/api/charts/solar-data")
async def get_solar_data(
    hours: int = Query(24, description="Hours of data to retrieve"),
    system: str = Query("both", description="System filter: both, system1, system2")
):
    """Get solar data for charts"""
    
    conn = get_db_connection()
    if not conn:
        raise HTTPException(status_code=500, detail="Database connection failed")
    
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        
        # Calculate time range
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=hours)
        
        result = {
            "timestamps": [],
            "system1": {"power": [], "yield": [], "soc": []},
            "system2": {"power": [], "yield": [], "soc": []}
        }
        
        # Get System 1 data
        if system in ["both", "system1"]:
            cur.execute("""
                SELECT timestamp, ac_power, yield_today, soc
                FROM solax_data 
                WHERE timestamp >= %s AND timestamp <= %s
                ORDER BY timestamp
            """, (start_time, end_time))
            
            system1_data = cur.fetchall()
            
            for row in system1_data:
                if not result["timestamps"] or row["timestamp"] not in [datetime.fromisoformat(ts) for ts in result["timestamps"]]:
                    result["timestamps"].append(row["timestamp"].isoformat())
                
                result["system1"]["power"].append(float(row["ac_power"] or 0))
                result["system1"]["yield"].append(float(row["yield_today"] or 0))
                result["system1"]["soc"].append(float(row["soc"] or 0))
        
        # Get System 2 data
        if system in ["both", "system2"]:
            cur.execute("""
                SELECT timestamp, ac_power, yield_today, soc
                FROM solax_data2 
                WHERE timestamp >= %s AND timestamp <= %s
                ORDER BY timestamp
            """, (start_time, end_time))
            
            system2_data = cur.fetchall()
            
            # If we only have system2 data, populate timestamps
            if system == "system2":
                result["timestamps"] = [row["timestamp"].isoformat() for row in system2_data]
            
            for row in system2_data:
                result["system2"]["power"].append(float(row["ac_power"] or 0))
                result["system2"]["yield"].append(float(row["yield_today"] or 0))
                result["system2"]["soc"].append(float(row["soc"] or 0))
        
        # Ensure equal length arrays
        max_length = len(result["timestamps"])
        for system_key in ["system1", "system2"]:
            for metric in ["power", "yield", "soc"]:
                while len(result[system_key][metric]) < max_length:
                    result[system_key][metric].append(0)
        
        conn.close()
        return result
        
    except Exception as e:
        logger.error(f"Error fetching solar data: {e}")
        conn.close()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/charts/weather-data")
async def get_weather_data(
    hours: int = Query(24, description="Hours of weather data to retrieve")
):
    """Get weather data for charts"""
    
    conn = get_db_connection()
    if not conn:
        raise HTTPException(status_code=500, detail="Database connection failed")
    
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        
        # Calculate time range
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=hours)
        
        cur.execute("""
            SELECT timestamp, temperature_2m, relative_humidity_2m, cloud_cover
            FROM weather_data 
            WHERE timestamp >= %s AND timestamp <= %s
            ORDER BY timestamp
        """, (start_time, end_time))
        
        weather_data = cur.fetchall()
        
        result = {
            "timestamps": [],
            "temperature": [],
            "humidity": [],
            "cloud_cover": []
        }
        
        for row in weather_data:
            result["timestamps"].append(row["timestamp"].isoformat())
            result["temperature"].append(float(row["temperature_2m"] or 0))
            result["humidity"].append(float(row["relative_humidity_2m"] or 0))
            result["cloud_cover"].append(float(row["cloud_cover"] or 0))
        
        conn.close()
        return result
        
    except Exception as e:
        logger.error(f"Error fetching weather data: {e}")
        conn.close()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/charts/statistics")
async def get_statistics():
    """Get current statistics for dashboard"""
    
    conn = get_db_connection()
    if not conn:
        raise HTTPException(status_code=500, detail="Database connection failed")
    
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        
        # Get latest data from both systems
        cur.execute("""
            SELECT yield_today, ac_power, soc, timestamp
            FROM solax_data 
            ORDER BY timestamp DESC 
            LIMIT 1
        """)
        system1_latest = cur.fetchone()
        
        cur.execute("""
            SELECT yield_today, ac_power, soc, timestamp
            FROM solax_data2 
            ORDER BY timestamp DESC 
            LIMIT 1
        """)
        system2_latest = cur.fetchone()
        
        # Calculate improved collection efficiency based on actual solar performance
        now = datetime.now()

        # Get today's data for efficiency calculation
        today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)

        # Calculate theoretical maximum yield based on system capacity and sun hours
        # System capacity: ~10.5kW per system, peak sun hours in Greece: ~5-6h
        theoretical_max_daily = 10.5 * 5.5 * 2  # 115.5 kWh for both systems

        # Get actual yield today
        total_yield_today = (float(system1_latest["yield_today"] or 0) +
                            float(system2_latest["yield_today"] or 0))

        # Calculate efficiency as percentage of theoretical maximum
        if theoretical_max_daily > 0:
            efficiency = min(100, (total_yield_today / theoretical_max_daily) * 100)
        else:
            efficiency = 0

        # Data freshness check (for system health)
        recent_threshold = now - timedelta(minutes=10)

        cur.execute("""
            SELECT COUNT(*) as count
            FROM solax_data
            WHERE timestamp > %s
        """, (recent_threshold,))
        system1_recent = cur.fetchone()["count"]

        cur.execute("""
            SELECT COUNT(*) as count
            FROM solax_data2
            WHERE timestamp > %s
        """, (recent_threshold,))
        system2_recent = cur.fetchone()["count"]

        # Expected: 20 records in 10 minutes (30s interval)
        expected_records = 20
        
        # Calculate statistics
        total_yield = 0
        total_power = 0
        avg_soc = 0
        
        if system1_latest and system2_latest:
            total_yield = float(system1_latest["yield_today"] or 0) + float(system2_latest["yield_today"] or 0)
            total_power = float(system1_latest["ac_power"] or 0) + float(system2_latest["ac_power"] or 0)
            avg_soc = (float(system1_latest["soc"] or 0) + float(system2_latest["soc"] or 0)) / 2
        
        result = {
            "total_yield_today": round(total_yield, 1),
            "current_power": round(total_power),
            "average_soc": round(avg_soc, 1),
            "collection_efficiency": round(efficiency, 1),
            "system1_yield_today": round(float(system1_latest["yield_today"] or 0), 1) if system1_latest else 0,
            "system2_yield_today": round(float(system2_latest["yield_today"] or 0), 1) if system2_latest else 0,
            "last_update": {
                "system1": system1_latest["timestamp"].isoformat() if system1_latest else None,
                "system2": system2_latest["timestamp"].isoformat() if system2_latest else None
            },
            "data_freshness": {
                "system1_records_10min": system1_recent,
                "system2_records_10min": system2_recent,
                "expected_records_10min": expected_records
            }
        }
        
        conn.close()
        return result
        
    except Exception as e:
        logger.error(f"Error fetching statistics: {e}")
        conn.close()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/charts/energy-flow")
async def get_energy_flow_data(
    hours: int = Query(24, description="Hours of energy flow data to retrieve")
):
    """Get energy flow data for production/consumption/battery chart"""

    conn = get_db_connection()
    if not conn:
        raise HTTPException(status_code=500, detail="Database connection failed")

    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)

        # Calculate time range
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=hours)

        result = {
            "timestamps": [],
            "production": [],
            "consumption": [],
            "battery_power": [],
            "grid_power": []
        }

        # Get data from system 1 first
        cur.execute("""
            SELECT
                timestamp,
                ac_power as production,
                soc,
                yield_today
            FROM solax_data
            WHERE timestamp >= %s AND timestamp <= %s
            ORDER BY timestamp
        """, (start_time, end_time))

        system1_data = cur.fetchall()

        # Get data from system 2
        cur.execute("""
            SELECT
                timestamp,
                ac_power as production,
                soc,
                yield_today
            FROM solax_data2
            WHERE timestamp >= %s AND timestamp <= %s
            ORDER BY timestamp
        """, (start_time, end_time))

        system2_data = cur.fetchall()

        # Combine data from both systems
        all_timestamps = set()
        system1_dict = {}
        system2_dict = {}

        for row in system1_data:
            ts = row["timestamp"].isoformat()
            all_timestamps.add(ts)
            system1_dict[ts] = row

        for row in system2_data:
            ts = row["timestamp"].isoformat()
            all_timestamps.add(ts)
            system2_dict[ts] = row

        # Process combined data
        for timestamp in sorted(all_timestamps):
            result["timestamps"].append(timestamp)

            # Get data from both systems
            s1_data = system1_dict.get(timestamp, {})
            s2_data = system2_dict.get(timestamp, {})

            # Production (AC power from solar)
            production1 = float(s1_data.get("production", 0) or 0)
            production2 = float(s2_data.get("production", 0) or 0)
            total_production = production1 + production2
            result["production"].append(total_production)

            # Estimate battery power based on SOC changes
            soc1 = float(s1_data.get("soc", 0) or 0)
            soc2 = float(s2_data.get("soc", 0) or 0)
            avg_soc = (soc1 + soc2) / 2
            # Simulate battery power based on SOC (simplified)
            battery_power = (avg_soc - 50) * 100  # Rough estimate
            result["battery_power"].append(battery_power)

            # Estimate grid power (simplified)
            # When production > consumption, negative (export)
            # When production < consumption, positive (import)
            estimated_consumption = total_production * 0.3  # Typical self-consumption
            grid_power = estimated_consumption - total_production
            result["grid_power"].append(grid_power)

            # Consumption estimate
            consumption = max(0, estimated_consumption)
            result["consumption"].append(consumption)

        conn.close()
        return result

    except Exception as e:
        logger.error(f"Error fetching energy flow data: {e}")
        conn.close()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/charts/performance")
async def get_performance_data(
    days: int = Query(7, description="Days of performance data")
):
    """Get performance data for trends"""

    conn = get_db_connection()
    if not conn:
        raise HTTPException(status_code=500, detail="Database connection failed")

    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)

        # Calculate time range
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days)

        # Get daily yield totals
        cur.execute("""
            SELECT
                DATE(timestamp) as date,
                MAX(yield_today) as max_yield_system1
            FROM solax_data
            WHERE timestamp >= %s AND timestamp <= %s
            GROUP BY DATE(timestamp)
            ORDER BY date
        """, (start_time, end_time))

        system1_daily = cur.fetchall()

        cur.execute("""
            SELECT
                DATE(timestamp) as date,
                MAX(yield_today) as max_yield_system2
            FROM solax_data2
            WHERE timestamp >= %s AND timestamp <= %s
            GROUP BY DATE(timestamp)
            ORDER BY date
        """, (start_time, end_time))
        
        system2_daily = cur.fetchall()
        
        # Combine data
        result = {
            "dates": [],
            "daily_yield": {
                "system1": [],
                "system2": [],
                "combined": []
            }
        }
        
        # Create date mapping
        system2_dict = {row["date"]: row["max_yield_system2"] for row in system2_daily}
        
        for row in system1_daily:
            date = row["date"]
            system1_yield = float(row["max_yield_system1"] or 0)
            system2_yield = float(system2_dict.get(date, 0))
            
            result["dates"].append(date.isoformat())
            result["daily_yield"]["system1"].append(system1_yield)
            result["daily_yield"]["system2"].append(system2_yield)
            result["daily_yield"]["combined"].append(system1_yield + system2_yield)
        
        conn.close()
        return result
        
    except Exception as e:
        logger.error(f"Error fetching performance data: {e}")
        conn.close()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    
    conn = get_db_connection()
    if not conn:
        return JSONResponse(
            status_code=503,
            content={"status": "unhealthy", "database": "disconnected"}
        )
    
    try:
        cur = conn.cursor()
        cur.execute("SELECT 1")
        conn.close()
        
        return {
            "status": "healthy",
            "database": "connected",
            "timestamp": datetime.now().isoformat(),
            "service": "Charts API"
        }
        
    except Exception as e:
        conn.close()
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy", 
                "database": "error",
                "error": str(e)
            }
        )

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "service": "Solar Charts API",
        "version": "1.0.0",
        "status": "running",
        "endpoints": {
            "solar_data": "/api/charts/solar-data",
            "weather_data": "/api/charts/weather-data",
            "statistics": "/api/charts/statistics",
            "energy_flow": "/api/charts/energy-flow",
            "performance": "/api/charts/performance",
            "health": "/health"
        }
    }

if __name__ == "__main__":
    print("🚀 Starting Solar Charts API on port 8103...")
    uvicorn.run(app, host="0.0.0.0", port=8103)
