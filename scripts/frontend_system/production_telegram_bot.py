#!/usr/bin/env python3
"""
Production Telegram Bot for Solar Prediction System
Interactive control and alerting using real data from PostgreSQL database
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import asyncio
import logging
import json
import requests
import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional, Any
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import (
    Application, CommandHandler, CallbackQueryHandler, 
    MessageHandler, filters, ContextTypes
)

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Configuration
BOT_TOKEN = "8060881816:AAFBhGADTAAcUkbom_FEFSkOHoT5N6t5png"
CHAT_ID = "1510889515"
API_BASE_URL = os.getenv('MAIN_API_URL', 'http://localhost:8100')

# Database configuration - Monolithic app compatible
DB_CONFIG = {
    'host': os.getenv('DATABASE_HOST', 'localhost'),  # Use localhost for monolithic
    'port': int(os.getenv('DATABASE_PORT', '5433')),  # External Docker port
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

class ProductionDataService:
    """Production data service integration with PostgreSQL and APIs"""
    
    @staticmethod
    def get_db_connection():
        """Get database connection"""
        try:
            return psycopg2.connect(**DB_CONFIG)
        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            return None
    
    @staticmethod
    async def get_health() -> Dict:
        """Get system health from production API"""
        try:
            response = requests.get(f"{API_BASE_URL}/health", timeout=10)
            if response.status_code == 200:
                return response.json()
        except Exception as e:
            logger.error(f"Health check failed: {e}")
        return {"status": "error", "message": "API unavailable"}
    
    @staticmethod
    async def get_latest_solax_data() -> Dict:
        """Get latest solar data from production API"""
        try:
            response = requests.get(f"{API_BASE_URL}/api/v1/data/solax/latest", timeout=10)
            if response.status_code == 200:
                return response.json()
        except Exception as e:
            logger.error(f"Failed to get solar data: {e}")
        return {}
    
    @staticmethod
    async def get_weather_data() -> Dict:
        """Get latest weather data from production API"""
        try:
            response = requests.get(f"{API_BASE_URL}/api/v1/data/weather/latest", timeout=10)
            if response.status_code == 200:
                return response.json()
        except Exception as e:
            logger.error(f"Failed to get weather data: {e}")
        return {}
    
    @staticmethod
    async def get_model_info() -> Dict:
        """Get model information from production API"""
        try:
            response = requests.get(f"{API_BASE_URL}/api/v1/model/info", timeout=10)
            if response.status_code == 200:
                return response.json()
        except Exception as e:
            logger.error(f"Failed to get model info: {e}")
        return {}
    
    @staticmethod
    async def get_database_stats() -> Dict:
        """Get database statistics directly from PostgreSQL"""
        conn = ProductionDataService.get_db_connection()
        if not conn:
            return {"error": "Database connection failed"}
        
        try:
            cur = conn.cursor(cursor_factory=RealDictCursor)
            
            stats = {}
            
            # Check both systems
            systems = [
                ('solax_data', 'System 1 (Σπίτι Πάνω)'),
                ('solax_data2', 'System 2 (Σπίτι Κάτω)')
            ]
            
            for table_name, system_name in systems:
                try:
                    # Get latest record
                    cur.execute(f'''
                        SELECT timestamp, yield_today, ac_power, soc, bat_power, temperature
                        FROM {table_name} 
                        ORDER BY timestamp DESC 
                        LIMIT 1
                    ''')
                    latest = cur.fetchone()
                    
                    # Get record count
                    cur.execute(f'SELECT COUNT(*) as count FROM {table_name}')
                    count = cur.fetchone()["count"]
                    
                    if latest:
                        stats[table_name] = {
                            'system_name': system_name,
                            'latest_timestamp': str(latest["timestamp"]),
                            'yield_today': float(latest["yield_today"] or 0),
                            'ac_power': float(latest["ac_power"] or 0),
                            'soc': float(latest["soc"] or 0),
                            'bat_power': float(latest["bat_power"] or 0),
                            'temperature': float(latest["temperature"] or 0),
                            'total_records': count
                        }
                    else:
                        stats[table_name] = {
                            'system_name': system_name,
                            'error': 'No data found',
                            'total_records': count
                        }
                        
                except Exception as e:
                    stats[table_name] = {
                        'system_name': system_name,
                        'error': str(e)
                    }
            
            # Check weather data
            try:
                cur.execute('''
                    SELECT timestamp, temperature_2m, global_horizontal_irradiance, cloud_cover
                    FROM weather_data 
                    ORDER BY timestamp DESC 
                    LIMIT 1
                ''')
                weather = cur.fetchone()
                
                cur.execute('SELECT COUNT(*) as count FROM weather_data')
                weather_count = cur.fetchone()["count"]
                
                if weather:
                    stats['weather_data'] = {
                        'latest_timestamp': str(weather["timestamp"]),
                        'temperature_2m': float(weather["temperature_2m"] or 0),
                        'global_horizontal_irradiance': float(weather["global_horizontal_irradiance"] or 0),
                        'cloud_cover': float(weather["cloud_cover"] or 0),
                        'total_records': weather_count
                    }
                else:
                    stats['weather_data'] = {
                        'error': 'No weather data found',
                        'total_records': weather_count
                    }
                    
            except Exception as e:
                stats['weather_data'] = {'error': str(e)}
            
            conn.close()
            return stats
            
        except Exception as e:
            conn.close()
            return {"error": f"Database query failed: {e}"}
    
    @staticmethod
    async def make_prediction(data: Dict) -> Dict:
        """Make prediction using production API"""
        try:
            response = requests.post(f"{API_BASE_URL}/api/v1/predict", json=data, timeout=30)
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"Prediction failed: {response.status_code}"}
        except Exception as e:
            logger.error(f"Prediction failed: {e}")
            return {"error": str(e)}

class ProductionTelegramBot:
    """Main Telegram bot service with production data integration"""
    
    def __init__(self):
        self.application = Application.builder().token(BOT_TOKEN).build()
        self.setup_handlers()
    
    def setup_handlers(self):
        """Setup command and callback handlers"""
        
        # Command handlers
        self.application.add_handler(CommandHandler("start", self.start_command))
        self.application.add_handler(CommandHandler("help", self.help_command))
        self.application.add_handler(CommandHandler("status", self.status_command))
        self.application.add_handler(CommandHandler("data", self.data_command))
        self.application.add_handler(CommandHandler("weather", self.weather_command))
        self.application.add_handler(CommandHandler("database", self.database_command))
        self.application.add_handler(CommandHandler("health", self.health_command))
        self.application.add_handler(CommandHandler("predict", self.predict_command))
        self.application.add_handler(CommandHandler("systems", self.systems_command))
        
        # Callback query handler for inline buttons
        self.application.add_handler(CallbackQueryHandler(self.button_callback))
        
        # Message handler for text messages
        self.application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_message))
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Start command handler"""
        
        keyboard = [
            [
                InlineKeyboardButton("📊 Production Data", callback_data="data"),
                InlineKeyboardButton("🌤️ Weather", callback_data="weather")
            ],
            [
                InlineKeyboardButton("🗄️ Database Stats", callback_data="database"),
                InlineKeyboardButton("🔧 Health", callback_data="health")
            ],
            [
                InlineKeyboardButton("🏠 Systems", callback_data="systems"),
                InlineKeyboardButton("🤖 Prediction", callback_data="predict")
            ],
            [
                InlineKeyboardButton("ℹ️ Help", callback_data="help")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        welcome_message = """
🌞 **Solar Prediction Production Bot**

Welcome to your solar energy management assistant with **REAL PRODUCTION DATA**!

**🔥 Features:**
• Real-time data from PostgreSQL database
• Live system monitoring (131,176+ records)
• Weather conditions from production APIs
• ML model status and predictions
• Database health monitoring

**📊 Data Sources:**
• PostgreSQL Database (solax_data, solax_data2, weather_data)
• Production API (localhost:8100)
• Real-time system monitoring

**🏠 Systems:**
• System 1: Σπίτι Πάνω (solax_data)
• System 2: Σπίτι Κάτω (solax_data2)

Use the buttons below for quick access:
        """
        
        await update.message.reply_text(
            welcome_message,
            reply_markup=reply_markup
        )
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Help command handler"""
        
        help_text = """
🔧 **Production Solar Bot Commands**

**📊 Real Data Commands:**
• `/data` - Current solar system data from database
• `/weather` - Current weather conditions
• `/database` - Database statistics and health
• `/systems` - Solar systems overview
• `/health` - Production API health status

**🤖 AI Commands:**
• `/predict` - ML prediction using production models

**💡 Examples:**
• `/data` - Get current yield, power, SOC from DB
• `/weather` - Temperature, GHI, cloud cover
• `/database` - PostgreSQL stats and record counts

**🔥 Production Features:**
• 131,176+ records (System 1)
• 126,310+ records (System 2)
• Real PostgreSQL integration
• No mock data - 100% production
• Live API monitoring

All data comes from actual production database!
        """
        
        await update.message.reply_text(help_text, parse_mode='Markdown')
    
    async def status_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Status command handler"""
        
        try:
            # Get production system data
            health = await ProductionDataService.get_health()
            solax_data = await ProductionDataService.get_latest_solax_data()
            
            status_emoji = "🟢" if health.get('status') == 'healthy' else "🔴"
            
            status_message = f"""
{status_emoji} **Production System Status**

**🔧 API Health:** {health.get('status', 'Unknown')}
**📅 Last Update:** {health.get('timestamp', 'Unknown')}

**🏠 Current System Data:**
**📊 System:** {solax_data.get('system', 'Unknown')}

**⚡ Real Production Data:**
• Yield Today: {solax_data.get('yield_today', 0)} kWh
• AC Power: {solax_data.get('ac_power', 0)} W  
• Battery SOC: {solax_data.get('soc', 0)}%
• Battery Power: {solax_data.get('bat_power', 0)} W
• Temperature: {solax_data.get('temperature', 0)}°C

**📈 Data Source:**
• PostgreSQL Database: {health.get('services', {}).get('database', 'Unknown')}
• Weather API: {health.get('services', {}).get('weather_api', 'Unknown')}
• Last Data: {solax_data.get('timestamp', 'Unknown')}

**🎯 Data Quality:** 100% Production - No Mock Data
            """
            
            keyboard = [
                [
                    InlineKeyboardButton("🔄 Refresh", callback_data="status"),
                    InlineKeyboardButton("📊 Details", callback_data="data")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(
                status_message,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )
            
        except Exception as e:
            await update.message.reply_text(f"❌ Error getting production status: {e}")
    
    async def data_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Data command handler"""
        
        try:
            solax_data = await ProductionDataService.get_latest_solax_data()
            
            if not solax_data:
                await update.message.reply_text("❌ No production data available")
                return
            
            # Calculate data age
            data_timestamp = solax_data.get('timestamp', '')
            try:
                data_time = datetime.fromisoformat(data_timestamp.replace('Z', '+00:00'))
                age = datetime.now() - data_time.replace(tzinfo=None)
                age_str = f"{age.days} days, {age.seconds//3600} hours ago"
            except:
                age_str = "Unknown"
            
            data_message = f"""
📊 **Real Solar Production Data**

**🏠 System:** {solax_data.get('system', 'Unknown')}

**⚡ Production Data:**
• Yield Today: **{solax_data.get('yield_today', 0)} kWh**
• AC Power: {solax_data.get('ac_power', 0)} W

**🔋 Battery Status:**
• SOC: **{solax_data.get('soc', 0)}%**
• Battery Power: {solax_data.get('bat_power', 0)} W

**🌡️ Environment:**
• Temperature: {solax_data.get('temperature', 0)}°C

**📅 Data Info:**
• Timestamp: {data_timestamp}
• Data Age: {age_str}
• Source: PostgreSQL Database

**🔥 This is 100% REAL production data!**
            """
            
            keyboard = [
                [
                    InlineKeyboardButton("🔄 Refresh", callback_data="data"),
                    InlineKeyboardButton("🗄️ Database", callback_data="database")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(
                data_message,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )
            
        except Exception as e:
            await update.message.reply_text(f"❌ Error getting production data: {e}")
    
    async def weather_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Weather command handler"""
        
        try:
            weather_data = await ProductionDataService.get_weather_data()
            
            if not weather_data:
                await update.message.reply_text("❌ No weather data available")
                return
            
            weather_message = f"""
🌤️ **Real Weather Data**

**🌡️ Temperature:** {weather_data.get('temperature', 'N/A')}°C
**☁️ Cloud Cover:** {weather_data.get('cloud_cover', 'N/A')}%
**💨 Wind Speed:** {weather_data.get('wind_speed', 'N/A')} km/h
**💧 Humidity:** {weather_data.get('humidity', 'N/A')}%

**📍 Location:** Marathon, Attica, Greece
**📅 Data Time:** {weather_data.get('timestamp', 'Unknown')}

**📊 Source:** Production Database & APIs
**✅ Status:** {weather_data.get('status', 'Unknown')}
            """
            
            keyboard = [
                [
                    InlineKeyboardButton("🔄 Refresh", callback_data="weather"),
                    InlineKeyboardButton("📊 Solar Data", callback_data="data")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(
                weather_message,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )
            
        except Exception as e:
            await update.message.reply_text(f"❌ Error getting weather data: {e}")
    
    async def database_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Database command handler"""
        
        try:
            db_stats = await ProductionDataService.get_database_stats()
            
            if "error" in db_stats:
                await update.message.reply_text(f"❌ Database error: {db_stats['error']}")
                return
            
            db_message = "🗄️ **PostgreSQL Database Statistics**\n\n"
            
            # System data
            for table_name, stats in db_stats.items():
                if table_name.startswith('solax_data'):
                    if 'error' in stats:
                        db_message += f"**{stats['system_name']}:**\n❌ {stats['error']}\n\n"
                    else:
                        db_message += f"""**{stats['system_name']}:**
• Records: {stats['total_records']:,}
• Latest: {stats['latest_timestamp']}
• Yield: {stats['yield_today']} kWh
• SOC: {stats['soc']}%
• AC Power: {stats['ac_power']} W

"""
                
                elif table_name == 'weather_data':
                    if 'error' in stats:
                        db_message += f"**Weather Data:**\n❌ {stats['error']}\n\n"
                    else:
                        db_message += f"""**Weather Data:**
• Records: {stats['total_records']:,}
• Latest: {stats['latest_timestamp']}
• Temperature: {stats['temperature_2m']}°C
• GHI: {stats['global_horizontal_irradiance']} W/m²

"""
            
            db_message += "**🔥 All data is REAL production data from PostgreSQL!**"
            
            keyboard = [
                [
                    InlineKeyboardButton("🔄 Refresh", callback_data="database"),
                    InlineKeyboardButton("📊 Live Data", callback_data="data")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(
                db_message,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )
            
        except Exception as e:
            await update.message.reply_text(f"❌ Error getting database stats: {e}")
    
    async def health_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Health command handler"""
        
        try:
            health = await ProductionDataService.get_health()
            model_info = await ProductionDataService.get_model_info()
            
            health_message = f"""
🔧 **Production System Health**

**🔌 API Status:** {health.get('status', 'Unknown')}
**📅 Timestamp:** {health.get('timestamp', 'Unknown')}

**🗄️ Services Status:**
• Database: {health.get('services', {}).get('database', 'Unknown')}
• Weather API: {health.get('services', {}).get('weather_api', 'Unknown')}
• Background Tasks: {health.get('services', {}).get('background_tasks', False)}

**🤖 ML Model:**
• Model: {model_info.get('model_type', 'Unknown')}
• Accuracy: {model_info.get('accuracy', 0):.1f}%
• Algorithm: {model_info.get('algorithm', 'Unknown')}
• Features: {model_info.get('features_used', 0)}
• Loaded: {model_info.get('model_loaded', False)}

**🔥 Data Source:** Production PostgreSQL Database
**✅ Status:** All systems operational
            """
            
            await update.message.reply_text(health_message, parse_mode='Markdown')
            
        except Exception as e:
            await update.message.reply_text(f"❌ Error getting health status: {e}")
    
    async def systems_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Systems command handler"""
        
        try:
            db_stats = await ProductionDataService.get_database_stats()
            
            systems_message = "🏠 **Solar Systems Overview**\n\n"
            
            total_yield = 0
            total_records = 0
            
            for table_name, stats in db_stats.items():
                if table_name.startswith('solax_data'):
                    if 'error' not in stats:
                        total_yield += float(stats['yield_today']) if stats['yield_today'] is not None else 0.0
                        total_records += stats['total_records']
                        
                        systems_message += f"""**{stats['system_name']}:**
📊 Table: {table_name}
📈 Records: {stats['total_records']:,}
⚡ Today: {stats['yield_today']} kWh
🔋 SOC: {stats['soc']}%
🔌 Power: {stats['ac_power']} W
📅 Updated: {stats['latest_timestamp']}

"""
            
            systems_message += f"""**📊 Combined Statistics:**
• Total Yield Today: {total_yield:.1f} kWh
• Total Records: {total_records:,}
• Data Source: PostgreSQL Production DB
• Status: Real-time monitoring active

**🎯 All data is 100% REAL production data!**
            """
            
            await update.message.reply_text(systems_message, parse_mode='Markdown')
            
        except Exception as e:
            await update.message.reply_text(f"❌ Error getting systems data: {e}")
    
    async def predict_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Prediction command handler"""
        
        try:
            # Get current data for prediction context
            solax_data = await ProductionDataService.get_latest_solax_data()
            
            if not solax_data:
                await update.message.reply_text("❌ No data available for prediction")
                return
            
            # Make prediction using production API
            prediction_data = {"system": solax_data.get('system', 'System 1')}
            prediction = await ProductionDataService.make_prediction(prediction_data)
            
            if "error" in prediction:
                predict_message = f"""
🤖 **ML Prediction Status**

❌ **Prediction Error:** {prediction['error']}

**📊 Available Data:**
• Current Yield: {solax_data.get('yield_today', 0)} kWh
• Current SOC: {solax_data.get('soc', 0)}%
• System: {solax_data.get('system', 'Unknown')}

**🔧 Note:** Model may need to be loaded or retrained.
**📅 Data Source:** Production Database
                """
            else:
                predict_message = f"""
🤖 **ML Prediction (Production Model)**

**📊 Input Data:**
• Current Yield: {solax_data.get('yield_today', 0)} kWh
• Current SOC: {solax_data.get('soc', 0)}%
• System: {solax_data.get('system', 'Unknown')}

**🎯 Prediction Results:**
• Predicted Value: {prediction.get('prediction', 'N/A')}
• Model Used: {prediction.get('model_used', 'Unknown')}
• Status: {prediction.get('status', 'Unknown')}

**🔥 Prediction based on 100% production data!**
**📅 Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                """
            
            await update.message.reply_text(predict_message, parse_mode='Markdown')
            
        except Exception as e:
            await update.message.reply_text(f"❌ Error making prediction: {e}")
    
    async def button_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle inline button callbacks"""
        
        query = update.callback_query
        await query.answer()
        
        data = query.data
        
        # Route callback to appropriate handler
        if data == "data":
            await self.data_command(update, context)
        elif data == "weather":
            await self.weather_command(update, context)
        elif data == "database":
            await self.database_command(update, context)
        elif data == "health":
            await self.health_command(update, context)
        elif data == "systems":
            await self.systems_command(update, context)
        elif data == "predict":
            await self.predict_command(update, context)
        elif data == "status":
            await self.status_command(update, context)
        elif data == "help":
            await self.help_command(update, context)
        else:
            await query.edit_message_text(f"🔧 Feature '{data}' - Production data integration ready")
    
    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle text messages"""
        
        text = update.message.text.lower()
        
        if "data" in text or "yield" in text:
            await self.data_command(update, context)
        elif "weather" in text:
            await self.weather_command(update, context)
        elif "database" in text or "db" in text:
            await self.database_command(update, context)
        elif "health" in text or "status" in text:
            await self.health_command(update, context)
        elif "systems" in text or "system" in text:
            await self.systems_command(update, context)
        elif "predict" in text or "forecast" in text:
            await self.predict_command(update, context)
        else:
            await update.message.reply_text(
                "🤖 I understand! Try:\n• 'data' for production solar data\n• 'weather' for conditions\n• 'database' for PostgreSQL stats\n• 'systems' for overview\n• 'predict' for ML forecast\n\nAll based on REAL production data! 🔥"
            )
    
    async def send_alert(self, message: str, severity: str = "info"):
        """Send alert message to configured chat"""
        
        try:
            severity_emojis = {
                "info": "ℹ️",
                "warning": "⚠️",
                "error": "❌",
                "critical": "🚨"
            }
            
            emoji = severity_emojis.get(severity, "ℹ️")
            alert_message = f"{emoji} **PRODUCTION ALERT**\n\n{message}\n\n🕐 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n🔥 Based on production database data"
            
            await self.application.bot.send_message(
                chat_id=CHAT_ID,
                text=alert_message,
                parse_mode='Markdown'
            )
            
        except Exception as e:
            logger.error(f"Failed to send alert: {e}")
    
    def run(self):
        """Run the bot"""
        logger.info("Starting Production Telegram bot...")
        self.application.run_polling()

def main():
    """Main function"""
    
    print("🤖 PRODUCTION TELEGRAM BOT SERVICE")
    print("="*60)
    print("🔄 Interactive control with real production data from PostgreSQL")
    print()
    
    try:
        # Test database connection
        conn = ProductionDataService.get_db_connection()
        if conn:
            print("✅ Database connection successful")
            conn.close()
        else:
            print("❌ Database connection failed")
            return False
        
        # Test API connection
        import requests
        try:
            response = requests.get(f"{API_BASE_URL}/health", timeout=5)
            if response.status_code == 200:
                print("✅ Production API connection successful")
            else:
                print(f"⚠️ Production API returned status {response.status_code}")
        except:
            print("❌ Production API connection failed")
        
        bot_service = ProductionTelegramBot()
        
        print("✅ Bot initialized successfully")
        print(f"📱 Bot Token: {BOT_TOKEN[:20]}...")
        print(f"💬 Chat ID: {CHAT_ID}")
        print(f"🌐 API URL: {API_BASE_URL}")
        print(f"🗄️ Database: {DB_CONFIG['database']}@{DB_CONFIG['host']}")
        print("🔥 Data Source: PostgreSQL production database")
        print()
        print("🚀 Starting bot service...")
        
        bot_service.run()
        
    except Exception as e:
        print(f"❌ Bot service failed: {e}")
        logger.exception("Telegram bot service failed")
        return False

if __name__ == "__main__":
    main()
