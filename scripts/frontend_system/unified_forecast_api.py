#!/usr/bin/env python3
"""
Unified Forecast API
Consolidates all forecast endpoints for frontend and Telegram integration
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import requests
import psycopg2
import psycopg2.extras
from fastapi import FastAPI, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware

# Import database prediction cache
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

try:
    from scripts.production.database_prediction_cache import get_prediction as get_cached_prediction
    DATABASE_CACHE_AVAILABLE = True
    print("✅ Database prediction cache imported successfully")
except ImportError as e:
    print(f"⚠️ Database prediction cache not available: {e}")
    DATABASE_CACHE_AVAILABLE = False

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

# API endpoints - USE PRODUCTION SCRIPTS API (THE ONLY OFFICIAL PREDICTION SYSTEM)
PRODUCTION_SCRIPTS_API = os.getenv('MAIN_API_URL', 'http://solar-prediction-main:8100')  # Production Scripts API with Hybrid ML Ensemble (94.31% R²)
BILLING_API = os.getenv('BILLING_API_URL', 'http://solar-prediction-billing:8110')

class UnifiedForecastService:
    """Unified forecast service that consolidates all prediction sources"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.cache = {}
        self.cache_ttl = 60  # Cache for 60 seconds
    
    def get_db_connection(self):
        """Get database connection"""
        try:
            return psycopg2.connect(**DB_CONFIG)
        except Exception as e:
            self.logger.error(f"Database connection failed: {e}")
            return None
    
    async def get_current_system_data(self, system_id: str) -> Dict:
        """Get current system data from Production Scripts API"""
        try:
            response = requests.get(f"{PRODUCTION_SCRIPTS_API}/api/v1/data/solax/latest?system_id={system_id}", timeout=10)
            if response.status_code == 200:
                return response.json()
        except Exception as e:
            self.logger.error(f"Failed to get current data for {system_id}: {e}")
        return {}
    
    async def get_weather_forecast(self, hours: int = 72) -> Dict:
        """Get weather forecast from Production Scripts API"""
        try:
            response = requests.get(f"{PRODUCTION_SCRIPTS_API}/api/v1/weather/current", timeout=10)
            if response.status_code == 200:
                return response.json()
        except Exception as e:
            self.logger.error(f"Failed to get weather forecast: {e}")
        return {}
    
    async def get_ml_predictions(self, system_id: str, hours: int = 72) -> Dict:
        """Get ML predictions with GPU acceleration and ultra-fast caching"""
        try:
            # Try GPU Prediction Service first for ultra-fast predictions
            try:
                import aiohttp
                async with aiohttp.ClientSession() as session:
                    gpu_request = {
                        "system_id": system_id,
                        "hours": hours
                    }
                    gpu_api_url = os.getenv('GPU_API_URL', 'http://solar-prediction-gpu:8105')
                    async with session.post(f'{gpu_api_url}/predict/gpu',
                                          json=gpu_request, timeout=10) as response:
                        if response.status == 200:
                            gpu_result = await response.json()
                            if gpu_result.get('status') == 'success':
                                prediction = gpu_result.get('prediction', {})

                                self.logger.info(f"🚀 GPU prediction: {system_id} {hours}h ({gpu_result.get('processing_time_ms', 0):.1f}ms, GPU: {gpu_result.get('gpu_used', False)})")

                                return {
                                    'status': 'success',
                                    'total_predicted_kwh': prediction.get('total_predicted_kwh', 0),
                                    'average_confidence': prediction.get('average_confidence', 0.95),
                                    'forecast_type': 'GPU_Accelerated_Production_Scripts_Hybrid_ML_Ensemble',
                                    'model_accuracy': '94.31% R² (GPU-Enhanced)',
                                    'processing_time_ms': gpu_result.get('processing_time_ms', 0),
                                    'gpu_used': gpu_result.get('gpu_used', False),
                                    'cached': gpu_result.get('cached', False),
                                    'cache_source': 'gpu_service'
                                }
                            else:
                                self.logger.warning(f"GPU prediction failed for {system_id}, trying database cache")
                        else:
                            self.logger.warning(f"GPU service unavailable for {system_id}, trying database cache")
            except ImportError as e:
                self.logger.warning(f"GPU service dependencies missing for {system_id}: {e}, trying database cache")
            except Exception as e:
                self.logger.warning(f"GPU service error for {system_id}: {e}, trying database cache")

            # Try database cache second for ultra-fast predictions
            if DATABASE_CACHE_AVAILABLE:
                cache_result = get_cached_prediction(system_id, hours)
                if cache_result and cache_result.get('status') == 'success':
                    prediction = cache_result.get('prediction', {})

                    self.logger.info(f"🚀 Database cache hit: {system_id} {hours}h ({cache_result.get('processing_time_ms', 0.5):.1f}ms)")

                    return {
                        'status': 'success',
                        'total_predicted_kwh': prediction.get('total_energy_kwh', 0),
                        'average_confidence': cache_result.get('confidence', 0.94),
                        'forecast_type': 'Database_Cached_Production_Scripts_Hybrid_ML_Ensemble',
                        'model_accuracy': '94.31% R²',
                        'processing_time_ms': cache_result.get('processing_time_ms', 0.5),
                        'gpu_used': False,
                        'cached': True,
                        'cache_source': cache_result.get('source', 'database_cache')
                    }
                else:
                    self.logger.info(f"🔄 Database cache miss: {system_id} {hours}h - generating fresh")

            # Fallback to Production Scripts API for fresh predictions
            return await self._fallback_ml_predictions(system_id, hours)

        except Exception as e:
            self.logger.error(f"Failed to get predictions for {system_id}: {e}, falling back to Production Scripts")
            return await self._fallback_ml_predictions(system_id, hours)

    async def _fallback_ml_predictions(self, system_id: str, hours: int = 72) -> Dict:
        """Fallback to Production Scripts API for ML predictions"""
        try:
            # Use Production Scripts API with Hybrid ML Ensemble Model (94.31% R²)
            if system_id == 'system1':
                response = requests.get(f"{PRODUCTION_SCRIPTS_API}/api/v1/forecast/24h/system1", timeout=30)
            elif system_id == 'system2':
                response = requests.get(f"{PRODUCTION_SCRIPTS_API}/api/v1/forecast/24h/system2", timeout=30)
            else:
                return {}

            if response.status_code == 200:
                forecast_data = response.json()

                # Get today's forecast from daily summaries
                daily_summaries = forecast_data.get('daily_summaries', {})
                day_0 = daily_summaries.get('day_0', {})
                total_predicted_kwh = day_0.get('total_energy_kwh', 0)

                # Get current yield to calculate remaining forecast
                current_yield = 0  # Production Scripts API doesn't provide current yield
                remaining_forecast = total_predicted_kwh

                # Scale based on requested hours (if less than 24h)
                if hours < 24:
                    # Estimate remaining hours in day
                    current_hour = datetime.now().hour
                    remaining_hours = max(1, 24 - current_hour)
                    scale_factor = min(1.0, hours / remaining_hours)
                    remaining_forecast *= scale_factor

                return {
                    'status': 'success',
                    'total_predicted_kwh': remaining_forecast,
                    'daily_total_kwh': total_predicted_kwh,
                    'current_yield_kwh': current_yield,
                    'forecast_type': 'Production_Scripts_Hybrid_ML_Ensemble_Fallback',
                    'model_accuracy': '94.31% R²',
                    'confidence': 0.943,  # Production Scripts confidence
                    'gpu_used': False
                }
        except Exception as e:
            self.logger.error(f"Failed to get fallback ML predictions for {system_id}: {e}")
        return {}

    async def get_current_data(self, system_id: str) -> Dict:
        """Get current system data for YIELD-ONLY features"""
        try:
            # Get latest solar data
            response = requests.get(f"{ENHANCED_PRODUCTION_API}/api/v1/data/solax/latest?system_id={system_id}", timeout=10)
            if response.status_code == 200:
                solar_data = response.json()

                # Get latest weather data
                weather_response = requests.get(f"{ENHANCED_PRODUCTION_API}/api/v1/data/weather/latest", timeout=10)
                weather_data = {}
                if weather_response.status_code == 200:
                    weather_data = weather_response.json()

                # YIELD-ONLY MODEL: NO AC POWER
                return {
                    'soc': solar_data.get('soc', 80),
                    'bat_power': solar_data.get('bat_power', 0),
                    'yield_today': solar_data.get('yield_today', 0),  # YIELD ONLY
                    'temperature': weather_data.get('temperature_2m', 25),
                    'ghi': weather_data.get('global_horizontal_irradiance', 600),
                    'cloud_cover': weather_data.get('cloud_cover', 20)
                }
        except Exception as e:
            self.logger.error(f"Failed to get current data for {system_id}: {e}")
        return {}
    
    async def get_financial_forecast(self, system_id: str, days: int = 7) -> Dict:
        """Get financial forecast using billing API"""
        try:
            # Get current financial summary
            response = requests.get(f"{BILLING_API}/billing/enhanced/summary/{system_id}", timeout=10)
            if response.status_code == 200:
                current_data = response.json()
                
                # Calculate daily averages for forecasting
                if current_data.get('status') == 'calculated':
                    daily_production = current_data.get('production', {}).get('total_kwh', 0)
                    daily_savings = current_data.get('savings', {}).get('direct_savings_eur', 0)
                    
                    # Simple forecast based on current performance
                    forecast = {
                        'period_days': days,
                        'forecasted_production_kwh': daily_production * days,
                        'forecasted_savings_eur': daily_savings * days,
                        'net_metering_status': current_data.get('net_metering', {}).get('status', 'unknown')
                    }
                    return forecast
        except Exception as e:
            self.logger.error(f"Failed to get financial forecast for {system_id}: {e}")
        return {}
    
    async def generate_unified_forecast(self, system_id: str, hours: int = 72) -> Dict:
        """Generate unified forecast combining all sources with caching"""
        try:
            # Check cache first
            cache_key = f"{system_id}_{hours}"
            now = time.time()

            if cache_key in self.cache:
                cached_data, timestamp = self.cache[cache_key]
                if now - timestamp < self.cache_ttl:
                    self.logger.info(f"🚀 Cache hit for {cache_key}")
                    return cached_data

            # Get all data in parallel
            current_data, weather_data, ml_predictions, financial_forecast = await asyncio.gather(
                self.get_current_system_data(system_id),
                self.get_weather_forecast(hours),
                self.get_ml_predictions(system_id, hours),
                self.get_financial_forecast(system_id, hours // 24),
                return_exceptions=True
            )
            
            # Handle exceptions
            if isinstance(current_data, Exception):
                current_data = {}
            if isinstance(weather_data, Exception):
                weather_data = {}
            if isinstance(ml_predictions, Exception):
                ml_predictions = {}
            if isinstance(financial_forecast, Exception):
                financial_forecast = {}
            
            # Generate unified response
            unified_forecast = {
                'system_id': system_id,
                'forecast_timestamp': datetime.now().isoformat(),
                'forecast_hours': hours,
                'status': 'success',
                
                # Current system status - YIELD-ONLY MODEL
                'current_status': {
                    'yield_today': current_data.get('yield_today', 0),  # YIELD ONLY
                    'soc': current_data.get('soc', 0),
                    'bat_power': current_data.get('bat_power', 0),
                    'timestamp': current_data.get('timestamp', datetime.now().isoformat())
                    # NO AC POWER - YIELD-ONLY MODELS
                },
                
                # Weather forecast
                'weather_forecast': weather_data,
                
                # ML predictions
                'ml_predictions': ml_predictions,
                
                # Financial forecast
                'financial_forecast': financial_forecast,
                
                # Summary metrics
                'summary': {
                    'total_predicted_kwh': ml_predictions.get('total_predicted_kwh', 0),
                    'average_confidence': ml_predictions.get('average_confidence', 0),
                    'peak_production_hour': ml_predictions.get('peak_hour', 12),
                    'forecasted_savings_eur': financial_forecast.get('forecasted_savings_eur', 0)
                }
            }

            # Cache the result
            self.cache[cache_key] = (unified_forecast, now)
            self.logger.info(f"🚀 Cached result for {cache_key}")

            return unified_forecast
            
        except Exception as e:
            self.logger.error(f"Failed to generate unified forecast: {e}")
            return {
                'system_id': system_id,
                'forecast_timestamp': datetime.now().isoformat(),
                'status': 'error',
                'error': str(e)
            }

# FastAPI app
app = FastAPI(title="Unified Solar Forecast API", version="1.0.0")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global service instance
forecast_service = UnifiedForecastService()

@app.get("/")
async def root():
    return {
        "service": "Unified Solar Forecast API",
        "version": "1.0.0",
        "features": [
            "Unified forecast integration",
            "ML predictions",
            "Weather forecasting",
            "Financial forecasting",
            "Multi-system support"
        ],
        "status": "operational",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/forecast/{system_id}")
async def get_unified_forecast(
    system_id: str,
    hours: int = Query(72, description="Forecast hours (1-168)")
):
    """Get unified forecast for a system"""

    if system_id not in ['system1', 'system2']:
        raise HTTPException(status_code=400, detail=f"Invalid system_id: {system_id}. Must be 'system1' or 'system2'")
    
    if not 1 <= hours <= 168:
        raise HTTPException(status_code=400, detail="Hours must be between 1 and 168")
    
    try:
        forecast = await forecast_service.generate_unified_forecast(system_id, hours)
        return forecast
    except Exception as e:
        logger.error(f"Error generating forecast: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/combined-forecast")
async def get_combined_forecast(
    hours: int = Query(72, description="Forecast hours (1-168)")
):
    """Get combined forecast for both systems"""
    
    if not 1 <= hours <= 168:
        raise HTTPException(status_code=400, detail="Hours must be between 1 and 168")
    
    try:
        # Get forecasts for both systems
        system1_forecast, system2_forecast = await asyncio.gather(
            forecast_service.generate_unified_forecast('system1', hours),
            forecast_service.generate_unified_forecast('system2', hours),
            return_exceptions=True
        )
        
        # Handle exceptions
        if isinstance(system1_forecast, Exception):
            system1_forecast = {'status': 'error', 'error': str(system1_forecast)}
        if isinstance(system2_forecast, Exception):
            system2_forecast = {'status': 'error', 'error': str(system2_forecast)}
        
        # Combine forecasts
        combined_forecast = {
            'forecast_timestamp': datetime.now().isoformat(),
            'forecast_hours': hours,
            'status': 'success',
            'systems': {
                'system1': system1_forecast,
                'system2': system2_forecast
            },
            'combined_summary': {
                'total_predicted_kwh': (
                    system1_forecast.get('summary', {}).get('total_predicted_kwh', 0) +
                    system2_forecast.get('summary', {}).get('total_predicted_kwh', 0)
                ),
                'total_forecasted_savings_eur': (
                    system1_forecast.get('summary', {}).get('forecasted_savings_eur', 0) +
                    system2_forecast.get('summary', {}).get('forecasted_savings_eur', 0)
                ),
                'average_confidence': (
                    (system1_forecast.get('summary', {}).get('average_confidence', 0) +
                     system2_forecast.get('summary', {}).get('average_confidence', 0)) / 2
                )
            }
        }
        
        return combined_forecast
        
    except Exception as e:
        logger.error(f"Error generating combined forecast: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "Unified Solar Forecast API",
        "timestamp": datetime.now().isoformat(),
        "dependencies": {
            "production_scripts_api": PRODUCTION_SCRIPTS_API,
            "billing_api": BILLING_API,
            "database": "postgresql://localhost:5433/solar_prediction"
        }
    }

if __name__ == "__main__":
    import uvicorn
    
    logger.info("🔮 Starting Unified Solar Forecast API...")
    logger.info("🌟 Features: ML predictions, Weather, Financial forecasting")
    logger.info("🔧 Integration: Production API, Billing API, Database")
    
    uvicorn.run(app, host="0.0.0.0", port=8120, reload=False)
