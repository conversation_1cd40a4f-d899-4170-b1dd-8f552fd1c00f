#!/usr/bin/env python3
"""
Configuration Management System for Solar Prediction System
Centralized configuration with PostgreSQL storage and REST API
"""

from fastapi import FastAP<PERSON>, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime
from typing import Dict, List, Any, Optional
import uvicorn
import logging
import json
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Database configuration
DB_CONFIG = {
    'host': os.getenv('DATABASE_HOST', 'localhost'),
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

app = FastAPI(title="Solar Configuration Manager", version="1.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models
class ConfigItem(BaseModel):
    key: str
    value: str
    category: str
    description: str
    data_type: str = "string"
    is_sensitive: bool = False

class ConfigUpdate(BaseModel):
    value: str
    description: Optional[str] = None

def get_db_connection():
    """Get database connection"""
    try:
        return psycopg2.connect(**DB_CONFIG)
    except Exception as e:
        logger.error(f"Database connection failed: {e}")
        return None

def init_config_tables():
    """Initialize configuration tables"""
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        cur = conn.cursor()
        
        # Create configurations table
        cur.execute("""
            CREATE TABLE IF NOT EXISTS system_configurations (
                id SERIAL PRIMARY KEY,
                config_key VARCHAR(100) UNIQUE NOT NULL,
                config_value TEXT NOT NULL,
                category VARCHAR(50) NOT NULL,
                description TEXT,
                data_type VARCHAR(20) DEFAULT 'string',
                is_sensitive BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_by VARCHAR(50) DEFAULT 'system'
            )
        """)
        
        # Create configuration history table
        cur.execute("""
            CREATE TABLE IF NOT EXISTS configuration_history (
                id SERIAL PRIMARY KEY,
                config_key VARCHAR(100) NOT NULL,
                old_value TEXT,
                new_value TEXT,
                changed_by VARCHAR(50),
                changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                change_reason TEXT
            )
        """)
        
        # Insert default configurations if not exist
        default_configs = [
            # System Configuration
            ('system.name', 'Solar Prediction System', 'system', 'System name', 'string', False),
            ('system.version', '1.0.0', 'system', 'System version', 'string', False),
            ('system.location', 'Marathon, Attica, Greece', 'system', 'System location', 'string', False),
            ('system.coordinates.lat', '38.141348260997596', 'system', 'Latitude coordinate', 'float', False),
            ('system.coordinates.lon', '24.0071653937747', 'system', 'Longitude coordinate', 'float', False),
            
            # Solar Systems Configuration
            ('solar.system1.name', 'Σπίτι Πάνω', 'solar', 'System 1 display name', 'string', False),
            ('solar.system1.capacity', '10.5', 'solar', 'System 1 capacity (kWp)', 'float', False),
            ('solar.system1.battery', '12', 'solar', 'System 1 battery capacity (kWh)', 'float', False),
            ('solar.system1.wifi_sn', 'SRFQDPDN9W', 'solar', 'System 1 WiFi SN', 'string', False),
            ('solar.system2.name', 'Σπίτι Κάτω', 'solar', 'System 2 display name', 'string', False),
            ('solar.system2.capacity', '10.5', 'solar', 'System 2 capacity (kWp)', 'float', False),
            ('solar.system2.battery', '12', 'solar', 'System 2 battery capacity (kWh)', 'float', False),
            ('solar.system2.wifi_sn', 'SRCV9TUD6S', 'solar', 'System 2 WiFi SN', 'string', False),
            
            # API Configuration
            ('api.solax.token', '20250410220826567911082', 'api', 'SolaX API token', 'string', True),
            ('api.solax.url', 'https://www.solaxcloud.com:9443/proxy/api/getRealtimeInfo.do', 'api', 'SolaX API URL', 'string', False),
            ('api.weather.provider', 'Open-Meteo', 'api', 'Weather API provider', 'string', False),
            ('api.weather.url', 'https://api.open-meteo.com/v1/forecast', 'api', 'Weather API URL', 'string', False),
            ('api.cams.key', 'b083d899-cb39-4015-914c-ab4b4f5f3d92', 'api', 'CAMS API key', 'string', True),
            
            # Data Collection Configuration
            ('data.collection.solar.interval', '30', 'data', 'Solar data collection interval (seconds)', 'integer', False),
            ('data.collection.weather.interval', '3600', 'data', 'Weather data collection interval (seconds)', 'integer', False),
            ('data.retention.days', '365', 'data', 'Data retention period (days)', 'integer', False),
            
            # Telegram Configuration
            ('telegram.bot.token', '**********************************************', 'telegram', 'Telegram bot token', 'string', True),
            ('telegram.chat.id', '**********', 'telegram', 'Default chat ID', 'string', False),
            ('telegram.language.default', 'el', 'telegram', 'Default language (el/en)', 'string', False),
            
            # Alert Configuration
            ('alerts.enabled', 'true', 'alerts', 'Enable alert system', 'boolean', False),
            ('alerts.cooldown.minutes', '30', 'alerts', 'Alert cooldown period (minutes)', 'integer', False),
            ('alerts.thresholds.low_soc', '20', 'alerts', 'Low SOC threshold (%)', 'integer', False),
            ('alerts.thresholds.high_temp', '60', 'alerts', 'High temperature threshold (°C)', 'integer', False),
            
            # Frontend Configuration
            ('frontend.refresh.interval', '30', 'frontend', 'Frontend refresh interval (seconds)', 'integer', False),
            ('frontend.theme', 'glassmorphism', 'frontend', 'Frontend theme', 'string', False),
            ('frontend.charts.enabled', 'true', 'frontend', 'Enable charts', 'boolean', False)
        ]
        
        for config in default_configs:
            cur.execute("""
                INSERT INTO system_configurations 
                (config_key, config_value, category, description, data_type, is_sensitive)
                VALUES (%s, %s, %s, %s, %s, %s)
                ON CONFLICT (config_key) DO NOTHING
            """, config)
        
        conn.commit()
        logger.info("✅ Configuration tables initialized with default values")
        return True
        
    except Exception as e:
        logger.error(f"Error initializing config tables: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "service": "Solar Configuration Manager",
        "version": "1.0.0",
        "status": "running",
        "endpoints": {
            "get_all_configs": "/config",
            "get_by_category": "/config/category/{category}",
            "get_config": "/config/{key}",
            "update_config": "/config/{key}",
            "categories": "/config/categories",
            "health": "/health"
        },
        "categories": [
            "system", "solar", "api", "data", 
            "telegram", "alerts", "frontend"
        ]
    }

@app.get("/config")
async def get_all_configurations(
    category: Optional[str] = Query(None, description="Filter by category"),
    include_sensitive: bool = Query(False, description="Include sensitive values")
):
    """Get all configurations"""
    
    conn = get_db_connection()
    if not conn:
        raise HTTPException(status_code=500, detail="Database connection failed")
    
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        
        query = """
            SELECT config_key, config_value, category, description, 
                   data_type, is_sensitive, updated_at
            FROM system_configurations
        """
        params = []
        
        if category:
            query += " WHERE category = %s"
            params.append(category)
        
        query += " ORDER BY category, config_key"
        
        cur.execute(query, params)
        configs = cur.fetchall()
        
        # Filter sensitive values if not requested
        if not include_sensitive:
            for config in configs:
                if config['is_sensitive']:
                    config['config_value'] = '***HIDDEN***'
        
        conn.close()
        
        return {
            "configurations": [dict(config) for config in configs],
            "total_count": len(configs),
            "categories": list(set(config['category'] for config in configs))
        }
        
    except Exception as e:
        conn.close()
        logger.error(f"Error getting configurations: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/config/categories")
async def get_categories():
    """Get all configuration categories"""
    
    conn = get_db_connection()
    if not conn:
        raise HTTPException(status_code=500, detail="Database connection failed")
    
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        
        cur.execute("""
            SELECT category, COUNT(*) as config_count,
                   COUNT(CASE WHEN is_sensitive THEN 1 END) as sensitive_count
            FROM system_configurations
            GROUP BY category
            ORDER BY category
        """)
        
        categories = cur.fetchall()
        conn.close()
        
        return {
            "categories": [dict(cat) for cat in categories],
            "total_categories": len(categories)
        }
        
    except Exception as e:
        conn.close()
        logger.error(f"Error getting categories: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/config/{key}")
async def get_configuration(key: str):
    """Get specific configuration"""
    
    conn = get_db_connection()
    if not conn:
        raise HTTPException(status_code=500, detail="Database connection failed")
    
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        
        cur.execute("""
            SELECT config_key, config_value, category, description, 
                   data_type, is_sensitive, updated_at, updated_by
            FROM system_configurations
            WHERE config_key = %s
        """, (key,))
        
        config = cur.fetchone()
        
        if not config:
            raise HTTPException(status_code=404, detail="Configuration not found")
        
        # Hide sensitive values
        if config['is_sensitive']:
            config['config_value'] = '***HIDDEN***'
        
        conn.close()
        return dict(config)
        
    except Exception as e:
        conn.close()
        logger.error(f"Error getting configuration {key}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.put("/config/{key}")
async def update_configuration(key: str, update: ConfigUpdate):
    """Update configuration value"""
    
    conn = get_db_connection()
    if not conn:
        raise HTTPException(status_code=500, detail="Database connection failed")
    
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        
        # Get current value for history
        cur.execute("""
            SELECT config_value FROM system_configurations
            WHERE config_key = %s
        """, (key,))
        
        current = cur.fetchone()
        if not current:
            raise HTTPException(status_code=404, detail="Configuration not found")
        
        old_value = current['config_value']
        
        # Update configuration
        cur.execute("""
            UPDATE system_configurations
            SET config_value = %s, 
                description = COALESCE(%s, description),
                updated_at = CURRENT_TIMESTAMP,
                updated_by = 'api'
            WHERE config_key = %s
        """, (update.value, update.description, key))
        
        # Log change in history
        cur.execute("""
            INSERT INTO configuration_history 
            (config_key, old_value, new_value, changed_by, change_reason)
            VALUES (%s, %s, %s, %s, %s)
        """, (key, old_value, update.value, 'api', 'Updated via API'))
        
        conn.commit()
        conn.close()
        
        logger.info(f"✅ Configuration updated: {key}")
        
        return {
            "message": "Configuration updated successfully",
            "key": key,
            "old_value": old_value,
            "new_value": update.value,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        conn.rollback()
        conn.close()
        logger.error(f"Error updating configuration {key}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/config/category/{category}")
async def get_configurations_by_category(category: str):
    """Get configurations by category"""
    
    conn = get_db_connection()
    if not conn:
        raise HTTPException(status_code=500, detail="Database connection failed")
    
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        
        cur.execute("""
            SELECT config_key, config_value, category, description, 
                   data_type, is_sensitive, updated_at
            FROM system_configurations
            WHERE category = %s
            ORDER BY config_key
        """, (category,))
        
        configs = cur.fetchall()
        
        # Hide sensitive values
        for config in configs:
            if config['is_sensitive']:
                config['config_value'] = '***HIDDEN***'
        
        conn.close()
        
        return {
            "category": category,
            "configurations": [dict(config) for config in configs],
            "count": len(configs)
        }
        
    except Exception as e:
        conn.close()
        logger.error(f"Error getting configurations for category {category}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    
    conn = get_db_connection()
    if not conn:
        return {
            "status": "unhealthy",
            "service": "Solar Configuration Manager",
            "database": "disconnected",
            "timestamp": datetime.now().isoformat()
        }
    
    try:
        cur = conn.cursor()
        cur.execute("SELECT COUNT(*) FROM system_configurations")
        config_count = cur.fetchone()[0]
        conn.close()
        
        return {
            "status": "healthy",
            "service": "Solar Configuration Manager",
            "database": "connected",
            "total_configurations": config_count,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        conn.close()
        return {
            "status": "unhealthy",
            "service": "Solar Configuration Manager",
            "database": "error",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

@app.on_event("startup")
async def startup_event():
    """Initialize configuration system on startup"""
    logger.info("⚙️ Initializing Configuration Management System...")
    if init_config_tables():
        logger.info("✅ Configuration system ready")
    else:
        logger.error("❌ Failed to initialize configuration system")

if __name__ == "__main__":
    print("⚙️ Starting Solar Configuration Manager on port 8108...")
    uvicorn.run(app, host="0.0.0.0", port=8108)
