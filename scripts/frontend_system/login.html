<!DOCTYPE html>
<html lang="el">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Solar System - Login</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            width: 100%;
            max-width: 400px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .logo {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .logo p {
            opacity: 0.9;
            font-size: 1.1em;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            opacity: 0.9;
        }

        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: rgba(255, 215, 0, 0.8);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
        }

        .form-group input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .login-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            border: none;
            border-radius: 10px;
            color: #333;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .demo-credentials {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .demo-credentials h4 {
            margin-bottom: 10px;
            color: #FFD700;
        }

        .demo-credentials p {
            font-size: 0.9em;
            opacity: 0.8;
            margin-bottom: 5px;
        }

        .error-message {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid rgba(244, 67, 54, 0.5);
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 15px;
            color: #ffcdd2;
            font-size: 0.9em;
            display: none;
        }

        .success-message {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.5);
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 15px;
            color: #c8e6c9;
            font-size: 0.9em;
            display: none;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .footer {
            text-align: center;
            margin-top: 20px;
            opacity: 0.7;
            font-size: 0.9em;
        }

        .register-link {
            text-align: center;
            margin-top: 15px;
        }

        .register-link a {
            color: #FFD700;
            text-decoration: none;
            font-weight: bold;
        }

        .register-link a:hover {
            text-decoration: underline;
        }

        @media (max-width: 480px) {
            .login-container {
                padding: 30px 20px;
            }
            
            .logo h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <h1>🌞</h1>
            <h1>Solar System</h1>
            <p>Authentication Portal</p>
        </div>

        <div class="demo-credentials">
            <h4>🔑 Demo Credentials</h4>
            <p><strong>Username:</strong> admin</p>
            <p><strong>Password:</strong> admin123</p>
            <p><strong>Role:</strong> Administrator</p>
        </div>

        <div class="error-message" id="errorMessage"></div>
        <div class="success-message" id="successMessage"></div>

        <form id="loginForm">
            <div class="form-group">
                <label for="username">Username:</label>
                <input type="text" id="username" name="username" placeholder="Enter your username" required>
            </div>

            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" placeholder="Enter your password" required>
            </div>

            <button type="submit" class="login-btn" id="loginBtn">
                <span id="loginText">🔐 Login</span>
            </button>
        </form>

        <div class="register-link">
            <a href="#" onclick="showRegisterForm()">📝 Create New Account</a>
        </div>

        <div class="footer">
            <p>Solar Prediction System v1.0.0</p>
            <p>Secure Authentication Portal</p>
        </div>
    </div>

    <script>
        class AuthManager {
            constructor() {
                this.apiUrl = 'http://localhost:8104';
                this.init();
            }
            
            init() {
                this.setupEventListeners();
                this.checkExistingAuth();
            }
            
            setupEventListeners() {
                document.getElementById('loginForm').addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.handleLogin();
                });
                
                // Auto-fill demo credentials
                document.getElementById('username').value = 'admin';
                document.getElementById('password').value = 'admin123';
            }
            
            checkExistingAuth() {
                const token = localStorage.getItem('solar_auth_token');
                if (token) {
                    this.verifyToken(token);
                }
            }
            
            async verifyToken(token) {
                try {
                    const response = await fetch(`${this.apiUrl}/auth/me`, {
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    });
                    
                    if (response.ok) {
                        const user = await response.json();
                        this.showSuccess(`Welcome back, ${user.full_name}! Redirecting...`);
                        setTimeout(() => {
                            window.location.href = 'dashboard_index.html';
                        }, 1500);
                    } else {
                        localStorage.removeItem('solar_auth_token');
                        localStorage.removeItem('solar_user_info');
                    }
                } catch (error) {
                    console.error('Token verification failed:', error);
                    localStorage.removeItem('solar_auth_token');
                    localStorage.removeItem('solar_user_info');
                }
            }
            
            async handleLogin() {
                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;
                
                if (!username || !password) {
                    this.showError('Please enter both username and password');
                    return;
                }
                
                this.setLoading(true);
                this.hideMessages();
                
                try {
                    const response = await fetch(`${this.apiUrl}/auth/login`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            username: username,
                            password: password
                        })
                    });
                    
                    const data = await response.json();
                    
                    if (response.ok) {
                        // Store authentication data
                        localStorage.setItem('solar_auth_token', data.access_token);
                        localStorage.setItem('solar_user_info', JSON.stringify(data.user_info));
                        
                        this.showSuccess(`Welcome, ${data.user_info.full_name}! Redirecting to dashboard...`);
                        
                        // Redirect to dashboard
                        setTimeout(() => {
                            window.location.href = 'dashboard_index.html';
                        }, 1500);
                        
                    } else {
                        this.showError(data.detail || 'Login failed');
                    }
                    
                } catch (error) {
                    console.error('Login error:', error);
                    this.showError('Connection error. Please check if the authentication service is running.');
                } finally {
                    this.setLoading(false);
                }
            }
            
            setLoading(loading) {
                const btn = document.getElementById('loginBtn');
                const text = document.getElementById('loginText');
                
                if (loading) {
                    btn.disabled = true;
                    text.innerHTML = '<span class="loading"></span>Logging in...';
                } else {
                    btn.disabled = false;
                    text.innerHTML = '🔐 Login';
                }
            }
            
            showError(message) {
                const errorDiv = document.getElementById('errorMessage');
                errorDiv.textContent = message;
                errorDiv.style.display = 'block';
                
                // Hide success message
                document.getElementById('successMessage').style.display = 'none';
            }
            
            showSuccess(message) {
                const successDiv = document.getElementById('successMessage');
                successDiv.textContent = message;
                successDiv.style.display = 'block';
                
                // Hide error message
                document.getElementById('errorMessage').style.display = 'none';
            }
            
            hideMessages() {
                document.getElementById('errorMessage').style.display = 'none';
                document.getElementById('successMessage').style.display = 'none';
            }
        }
        
        function showRegisterForm() {
            alert('🔧 Registration Feature\n\nTo create a new account, please contact the system administrator.\n\nFor demo purposes, use:\nUsername: admin\nPassword: admin123');
        }
        
        // Initialize auth manager when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new AuthManager();
        });
    </script>
</body>
</html>
