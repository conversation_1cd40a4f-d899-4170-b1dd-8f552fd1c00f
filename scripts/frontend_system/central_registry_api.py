#!/usr/bin/env python3
"""
Central Configuration Registry API
Enterprise-grade configuration management with versioning and audit trails
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any, Union
import psycopg2
from psycopg2.extras import RealDictCursor
import json
from datetime import datetime
import logging
from enum import Enum

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# FastAPI app
app = FastAPI(
    title="Solar Prediction Central Registry API",
    description="Enterprise configuration management with versioning and audit trails",
    version="2.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': ''
}

class ConfigScope(str, Enum):
    """Configuration scope levels"""
    GLOBAL = "global"
    SYSTEM = "system"
    USER = "user"
    SESSION = "session"

class ConfigType(str, Enum):
    """Configuration value types"""
    STRING = "string"
    NUMBER = "number"
    BOOLEAN = "boolean"
    JSON = "json"
    ARRAY = "array"

class ConfigEntry(BaseModel):
    """Configuration entry model"""
    key: str = Field(..., min_length=1, max_length=100)
    value: Union[str, int, float, bool, Dict, List]
    type: ConfigType
    scope: ConfigScope = ConfigScope.GLOBAL
    category: str = Field(..., min_length=1, max_length=50)
    description: Optional[str] = None
    is_sensitive: bool = False
    is_readonly: bool = False
    validation_rules: Optional[Dict[str, Any]] = None
    tags: Optional[List[str]] = None

class ConfigUpdate(BaseModel):
    """Configuration update model"""
    value: Union[str, int, float, bool, Dict, List]
    description: Optional[str] = None
    reason: Optional[str] = None

class ConfigResponse(ConfigEntry):
    """Configuration response model"""
    id: int
    version: int
    created_at: datetime
    updated_at: datetime
    created_by: str
    updated_by: str

class ConfigHistoryEntry(BaseModel):
    """Configuration history entry"""
    id: int
    config_id: int
    version: int
    old_value: Union[str, int, float, bool, Dict, List]
    new_value: Union[str, int, float, bool, Dict, List]
    changed_by: str
    change_reason: Optional[str]
    changed_at: datetime

def get_db_connection():
    """Get database connection"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        return conn
    except Exception as e:
        logger.error(f"Database connection failed: {e}")
        raise HTTPException(status_code=500, detail="Database connection failed")

def init_registry_schema():
    """Initialize registry database schema"""
    
    schema_sql = """
    -- Central configuration registry table
    CREATE TABLE IF NOT EXISTS config_registry (
        id SERIAL PRIMARY KEY,
        key VARCHAR(100) UNIQUE NOT NULL,
        value JSONB NOT NULL,
        type VARCHAR(20) NOT NULL,
        scope VARCHAR(20) NOT NULL DEFAULT 'global',
        category VARCHAR(50) NOT NULL,
        description TEXT,
        is_sensitive BOOLEAN DEFAULT FALSE,
        is_readonly BOOLEAN DEFAULT FALSE,
        validation_rules JSONB,
        tags TEXT[],
        version INTEGER DEFAULT 1,
        created_at TIMESTAMPTZ DEFAULT NOW(),
        updated_at TIMESTAMPTZ DEFAULT NOW(),
        created_by VARCHAR(50) DEFAULT 'system',
        updated_by VARCHAR(50) DEFAULT 'system',
        
        CONSTRAINT valid_type CHECK (type IN ('string', 'number', 'boolean', 'json', 'array')),
        CONSTRAINT valid_scope CHECK (scope IN ('global', 'system', 'user', 'session'))
    );
    
    -- Configuration change history
    CREATE TABLE IF NOT EXISTS config_history (
        id SERIAL PRIMARY KEY,
        config_id INTEGER REFERENCES config_registry(id) ON DELETE CASCADE,
        version INTEGER NOT NULL,
        old_value JSONB,
        new_value JSONB NOT NULL,
        changed_by VARCHAR(50) NOT NULL,
        change_reason TEXT,
        changed_at TIMESTAMPTZ DEFAULT NOW()
    );
    
    -- Configuration categories for organization
    CREATE TABLE IF NOT EXISTS config_categories (
        id SERIAL PRIMARY KEY,
        name VARCHAR(50) UNIQUE NOT NULL,
        description TEXT,
        parent_category VARCHAR(50),
        display_order INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMPTZ DEFAULT NOW()
    );
    
    -- Indexes for performance
    CREATE INDEX IF NOT EXISTS idx_config_registry_key ON config_registry(key);
    CREATE INDEX IF NOT EXISTS idx_config_registry_category ON config_registry(category);
    CREATE INDEX IF NOT EXISTS idx_config_registry_scope ON config_registry(scope);
    CREATE INDEX IF NOT EXISTS idx_config_registry_tags ON config_registry USING GIN(tags);
    CREATE INDEX IF NOT EXISTS idx_config_history_config_id ON config_history(config_id);
    CREATE INDEX IF NOT EXISTS idx_config_history_changed_at ON config_history(changed_at);
    
    -- Trigger for automatic version increment and history logging
    CREATE OR REPLACE FUNCTION update_config_version_and_history()
    RETURNS TRIGGER AS $$
    BEGIN
        -- Insert history record
        INSERT INTO config_history (
            config_id, version, old_value, new_value, changed_by, change_reason
        ) VALUES (
            NEW.id, OLD.version, OLD.value, NEW.value, NEW.updated_by, 
            COALESCE(NEW.description, 'Configuration updated')
        );
        
        -- Increment version
        NEW.version = OLD.version + 1;
        NEW.updated_at = NOW();
        
        RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;
    
    DROP TRIGGER IF EXISTS config_version_trigger ON config_registry;
    CREATE TRIGGER config_version_trigger
        BEFORE UPDATE ON config_registry
        FOR EACH ROW
        EXECUTE FUNCTION update_config_version_and_history();
    """
    
    try:
        conn = get_db_connection()
        cur = conn.cursor()
        cur.execute(schema_sql)
        conn.commit()
        conn.close()
        logger.info("Registry schema initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize registry schema: {e}")
        raise

def seed_default_configuration():
    """Seed default configuration values"""
    
    default_configs = [
        # System Configuration
        {
            "key": "system.name",
            "value": "Solar Prediction System",
            "type": "string",
            "category": "system",
            "description": "System display name"
        },
        {
            "key": "system.version",
            "value": "2.0.0",
            "type": "string",
            "category": "system",
            "description": "System version"
        },
        {
            "key": "system.timezone",
            "value": "Europe/Athens",
            "type": "string",
            "category": "system",
            "description": "Default system timezone"
        },
        
        # Geographic Configuration
        {
            "key": "location.default_latitude",
            "value": 38.141348,
            "type": "number",
            "category": "geographic",
            "description": "Default system latitude"
        },
        {
            "key": "location.default_longitude",
            "value": 24.007165,
            "type": "number",
            "category": "geographic",
            "description": "Default system longitude"
        },
        {
            "key": "location.country_code",
            "value": "GR",
            "type": "string",
            "category": "geographic",
            "description": "Default country code"
        },
        
        # API Configuration
        {
            "key": "api.base_url",
            "value": "http://localhost:8100",
            "type": "string",
            "category": "api",
            "description": "Base API URL"
        },
        {
            "key": "api.version",
            "value": "v1",
            "type": "string",
            "category": "api",
            "description": "API version"
        },
        {
            "key": "api.timeout_seconds",
            "value": 30,
            "type": "number",
            "category": "api",
            "description": "API request timeout"
        },
        
        # Weather API Configuration
        {
            "key": "weather.provider",
            "value": "open-meteo",
            "type": "string",
            "category": "weather",
            "description": "Primary weather data provider"
        },
        {
            "key": "weather.update_interval_minutes",
            "value": 60,
            "type": "number",
            "category": "weather",
            "description": "Weather data update interval"
        },
        {
            "key": "weather.forecast_days",
            "value": 7,
            "type": "number",
            "category": "weather",
            "description": "Weather forecast days"
        },
        
        # ML Model Configuration
        {
            "key": "ml.default_model",
            "value": "enhanced_model_v3",
            "type": "string",
            "category": "ml",
            "description": "Default ML model"
        },
        {
            "key": "ml.confidence_threshold",
            "value": 0.7,
            "type": "number",
            "category": "ml",
            "description": "Minimum confidence threshold"
        },
        {
            "key": "ml.retraining_threshold_mae",
            "value": 2.5,
            "type": "number",
            "category": "ml",
            "description": "MAE threshold for retraining"
        },
        
        # UI Configuration
        {
            "key": "ui.theme",
            "value": "dark",
            "type": "string",
            "category": "ui",
            "description": "Default UI theme"
        },
        {
            "key": "ui.refresh_interval_seconds",
            "value": 30,
            "type": "number",
            "category": "ui",
            "description": "UI auto-refresh interval"
        },
        {
            "key": "ui.chart_colors",
            "value": ["#4facfe", "#00f2fe", "#667eea", "#764ba2"],
            "type": "array",
            "category": "ui",
            "description": "Default chart color palette"
        },
        
        # Telegram Configuration
        {
            "key": "telegram.bot_token",
            "value": "**********************************************",
            "type": "string",
            "category": "telegram",
            "description": "Telegram bot token",
            "is_sensitive": True
        },
        {
            "key": "telegram.chat_id",
            "value": "1510889515",
            "type": "string",
            "category": "telegram",
            "description": "Default Telegram chat ID"
        },
        {
            "key": "telegram.alerts_enabled",
            "value": True,
            "type": "boolean",
            "category": "telegram",
            "description": "Enable Telegram alerts"
        },
        
        # Alert Configuration
        {
            "key": "alerts.enabled",
            "value": True,
            "type": "boolean",
            "category": "alerts",
            "description": "Enable system alerts"
        },
        {
            "key": "alerts.severity_levels",
            "value": ["info", "warning", "error", "critical"],
            "type": "array",
            "category": "alerts",
            "description": "Alert severity levels"
        },
        {
            "key": "alerts.escalation_timeout_minutes",
            "value": 30,
            "type": "number",
            "category": "alerts",
            "description": "Alert escalation timeout"
        }
    ]
    
    try:
        conn = get_db_connection()
        cur = conn.cursor()
        
        for config in default_configs:
            # Check if config already exists
            cur.execute("SELECT id FROM config_registry WHERE key = %s", (config["key"],))
            if cur.fetchone():
                continue
            
            # Insert new config
            cur.execute("""
                INSERT INTO config_registry (
                    key, value, type, category, description, is_sensitive
                ) VALUES (%s, %s, %s, %s, %s, %s)
            """, (
                config["key"],
                json.dumps(config["value"]),
                config["type"],
                config["category"],
                config["description"],
                config.get("is_sensitive", False)
            ))
        
        conn.commit()
        conn.close()
        logger.info("Default configuration seeded successfully")
        
    except Exception as e:
        logger.error(f"Failed to seed default configuration: {e}")
        raise

# API Endpoints

@app.on_event("startup")
async def startup_event():
    """Initialize registry on startup"""
    init_registry_schema()
    seed_default_configuration()

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Solar Prediction Central Registry API",
        "version": "2.0.0",
        "endpoints": {
            "config": "/config",
            "categories": "/categories",
            "health": "/health"
        }
    }

@app.get("/config", response_model=List[ConfigResponse])
async def list_configurations(
    category: Optional[str] = None,
    scope: Optional[ConfigScope] = None,
    include_sensitive: bool = False
):
    """List all configurations"""
    
    conn = get_db_connection()
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        
        query = "SELECT * FROM config_registry WHERE 1=1"
        params = []
        
        if category:
            query += " AND category = %s"
            params.append(category)
        
        if scope:
            query += " AND scope = %s"
            params.append(scope.value)
        
        if not include_sensitive:
            query += " AND is_sensitive = FALSE"
        
        query += " ORDER BY category, key"
        
        cur.execute(query, params)
        configs = cur.fetchall()
        
        # Parse JSON values
        for config in configs:
            config['value'] = json.loads(config['value'])
        
        return [ConfigResponse(**dict(config)) for config in configs]
        
    finally:
        conn.close()

@app.get("/config/{key}", response_model=ConfigResponse)
async def get_configuration(key: str):
    """Get specific configuration"""
    
    conn = get_db_connection()
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        cur.execute("SELECT * FROM config_registry WHERE key = %s", (key,))
        config = cur.fetchone()
        
        if not config:
            raise HTTPException(status_code=404, detail="Configuration not found")
        
        # Parse JSON value
        config['value'] = json.loads(config['value'])
        
        return ConfigResponse(**dict(config))
        
    finally:
        conn.close()

@app.put("/config/{key}", response_model=ConfigResponse)
async def update_configuration(
    key: str, 
    update: ConfigUpdate,
    background_tasks: BackgroundTasks,
    updated_by: str = "api_user"
):
    """Update configuration"""
    
    conn = get_db_connection()
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        
        # Check if config exists and is not readonly
        cur.execute("SELECT * FROM config_registry WHERE key = %s", (key,))
        existing = cur.fetchone()
        
        if not existing:
            raise HTTPException(status_code=404, detail="Configuration not found")
        
        if existing['is_readonly']:
            raise HTTPException(status_code=403, detail="Configuration is readonly")
        
        # Update configuration
        cur.execute("""
            UPDATE config_registry 
            SET value = %s, updated_by = %s, description = COALESCE(%s, description)
            WHERE key = %s
            RETURNING *
        """, (
            json.dumps(update.value),
            updated_by,
            update.description,
            key
        ))
        
        updated_config = cur.fetchone()
        conn.commit()
        
        # Parse JSON value
        updated_config['value'] = json.loads(updated_config['value'])
        
        # Schedule background tasks
        background_tasks.add_task(notify_config_change, key, update.value)
        
        return ConfigResponse(**dict(updated_config))
        
    finally:
        conn.close()

@app.post("/config", response_model=ConfigResponse)
async def create_configuration(
    config: ConfigEntry,
    background_tasks: BackgroundTasks,
    created_by: str = "api_user"
):
    """Create new configuration"""
    
    conn = get_db_connection()
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        
        # Check if key already exists
        cur.execute("SELECT id FROM config_registry WHERE key = %s", (config.key,))
        if cur.fetchone():
            raise HTTPException(status_code=400, detail="Configuration key already exists")
        
        # Insert new configuration
        cur.execute("""
            INSERT INTO config_registry (
                key, value, type, scope, category, description, 
                is_sensitive, is_readonly, validation_rules, tags, created_by, updated_by
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            RETURNING *
        """, (
            config.key,
            json.dumps(config.value),
            config.type.value,
            config.scope.value,
            config.category,
            config.description,
            config.is_sensitive,
            config.is_readonly,
            json.dumps(config.validation_rules) if config.validation_rules else None,
            config.tags,
            created_by,
            created_by
        ))
        
        new_config = cur.fetchone()
        conn.commit()
        
        # Parse JSON value
        new_config['value'] = json.loads(new_config['value'])
        
        # Schedule background tasks
        background_tasks.add_task(notify_config_change, config.key, config.value)
        
        return ConfigResponse(**dict(new_config))
        
    finally:
        conn.close()

@app.delete("/config/{key}")
async def delete_configuration(key: str, background_tasks: BackgroundTasks):
    """Delete configuration"""
    
    conn = get_db_connection()
    try:
        cur = conn.cursor()
        
        # Check if config exists and is not readonly
        cur.execute("SELECT is_readonly FROM config_registry WHERE key = %s", (key,))
        config = cur.fetchone()
        
        if not config:
            raise HTTPException(status_code=404, detail="Configuration not found")
        
        if config[0]:  # is_readonly
            raise HTTPException(status_code=403, detail="Configuration is readonly")
        
        # Delete configuration
        cur.execute("DELETE FROM config_registry WHERE key = %s", (key,))
        conn.commit()
        
        # Schedule background tasks
        background_tasks.add_task(notify_config_change, key, None)
        
        return {"message": f"Configuration {key} deleted successfully"}
        
    finally:
        conn.close()

@app.get("/config/{key}/history", response_model=List[ConfigHistoryEntry])
async def get_configuration_history(key: str, limit: int = 50):
    """Get configuration change history"""
    
    conn = get_db_connection()
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        
        cur.execute("""
            SELECT h.* FROM config_history h
            JOIN config_registry c ON h.config_id = c.id
            WHERE c.key = %s
            ORDER BY h.changed_at DESC
            LIMIT %s
        """, (key, limit))
        
        history = cur.fetchall()
        
        # Parse JSON values
        for entry in history:
            if entry['old_value']:
                entry['old_value'] = json.loads(entry['old_value'])
            entry['new_value'] = json.loads(entry['new_value'])
        
        return [ConfigHistoryEntry(**dict(entry)) for entry in history]
        
    finally:
        conn.close()

@app.get("/categories")
async def list_categories():
    """List configuration categories"""
    
    conn = get_db_connection()
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        cur.execute("""
            SELECT category, COUNT(*) as config_count
            FROM config_registry 
            GROUP BY category 
            ORDER BY category
        """)
        
        categories = cur.fetchall()
        return [dict(cat) for cat in categories]
        
    finally:
        conn.close()

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    
    try:
        conn = get_db_connection()
        cur = conn.cursor()
        cur.execute("SELECT COUNT(*) FROM config_registry")
        config_count = cur.fetchone()[0]
        conn.close()
        
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "config_count": config_count,
            "version": "2.0.0"
        }
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"Health check failed: {e}")

# Background tasks
async def notify_config_change(key: str, value: Any):
    """Notify other services of configuration changes"""
    logger.info(f"Configuration changed: {key} = {value}")
    # Implementation would send webhooks, update caches, etc.

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8002)
