#!/usr/bin/env python3
"""
Historical Reports Generator for Solar Prediction System
Generates comprehensive PDF reports with charts and analytics
"""

from fastapi import FastAPI, HTTPException, Query, Response
from fastapi.middleware.cors import CORSMiddleware
import psycopg2
from psycopg2.extras import RealDictCursor
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import uvicorn
import logging
import io
import base64
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image, Table, TableStyle, PageBreak
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
import numpy as np
import seaborn as sns

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Set matplotlib style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

app = FastAPI(title="Solar Historical Reports API", version="1.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

def get_db_connection():
    """Get database connection"""
    try:
        return psycopg2.connect(**DB_CONFIG)
    except Exception as e:
        logger.error(f"Database connection failed: {e}")
        return None

class ReportGenerator:
    """Historical reports generator"""
    
    def __init__(self):
        self.styles = getSampleStyleSheet()
        self.title_style = ParagraphStyle(
            'CustomTitle',
            parent=self.styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            textColor=colors.darkblue
        )
        self.heading_style = ParagraphStyle(
            'CustomHeading',
            parent=self.styles['Heading2'],
            fontSize=16,
            spaceAfter=12,
            textColor=colors.darkgreen
        )
    
    def get_historical_data(self, days: int = 30) -> Dict[str, pd.DataFrame]:
        """Get historical data for report"""
        
        conn = get_db_connection()
        if not conn:
            raise Exception("Database connection failed")
        
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            # Get System 1 data
            system1_query = """
                SELECT timestamp, yield_today, ac_power, soc, bat_power, temperature
                FROM solax_data 
                WHERE timestamp >= %s AND timestamp <= %s
                ORDER BY timestamp
            """
            system1_df = pd.read_sql(system1_query, conn, params=(start_date, end_date))
            
            # Get System 2 data
            system2_query = """
                SELECT timestamp, yield_today, ac_power, soc, bat_power, temperature
                FROM solax_data2 
                WHERE timestamp >= %s AND timestamp <= %s
                ORDER BY timestamp
            """
            system2_df = pd.read_sql(system2_query, conn, params=(start_date, end_date))
            
            # Get Weather data
            weather_query = """
                SELECT timestamp, temperature_2m, relative_humidity_2m, cloud_cover,
                       global_horizontal_irradiance, direct_normal_irradiance
                FROM weather_data 
                WHERE timestamp >= %s AND timestamp <= %s
                ORDER BY timestamp
            """
            weather_df = pd.read_sql(weather_query, conn, params=(start_date, end_date))
            
            conn.close()
            
            # Convert timestamps
            for df in [system1_df, system2_df, weather_df]:
                if not df.empty:
                    df['timestamp'] = pd.to_datetime(df['timestamp'])
            
            return {
                'system1': system1_df,
                'system2': system2_df,
                'weather': weather_df,
                'period': {'start': start_date, 'end': end_date, 'days': days}
            }
            
        except Exception as e:
            conn.close()
            raise Exception(f"Data retrieval error: {e}")
    
    def create_chart(self, data: Dict, chart_type: str, title: str) -> str:
        """Create chart and return base64 encoded image"""
        
        fig, ax = plt.subplots(figsize=(12, 6))
        
        if chart_type == 'daily_yield':
            # Daily yield comparison
            system1_daily = data['system1'].groupby(data['system1']['timestamp'].dt.date)['yield_today'].max()
            system2_daily = data['system2'].groupby(data['system2']['timestamp'].dt.date)['yield_today'].max()
            
            ax.plot(system1_daily.index, system1_daily.values, label='Σπίτι Πάνω', marker='o', linewidth=2)
            ax.plot(system2_daily.index, system2_daily.values, label='Σπίτι Κάτω', marker='s', linewidth=2)
            ax.set_ylabel('Daily Yield (kWh)')
            ax.legend()
            
        elif chart_type == 'power_profile':
            # Average hourly power profile
            system1_hourly = data['system1'].groupby(data['system1']['timestamp'].dt.hour)['ac_power'].mean()
            system2_hourly = data['system2'].groupby(data['system2']['timestamp'].dt.hour)['ac_power'].mean()
            
            ax.plot(system1_hourly.index, system1_hourly.values, label='Σπίτι Πάνω', linewidth=3)
            ax.plot(system2_hourly.index, system2_hourly.values, label='Σπίτι Κάτω', linewidth=3)
            ax.set_xlabel('Hour of Day')
            ax.set_ylabel('Average AC Power (W)')
            ax.set_xlim(0, 23)
            ax.legend()
            
        elif chart_type == 'soc_trends':
            # SOC trends
            system1_soc = data['system1'].set_index('timestamp')['soc'].resample('H').mean()
            system2_soc = data['system2'].set_index('timestamp')['soc'].resample('H').mean()
            
            ax.plot(system1_soc.index, system1_soc.values, label='Σπίτι Πάνω', alpha=0.8)
            ax.plot(system2_soc.index, system2_soc.values, label='Σπίτι Κάτω', alpha=0.8)
            ax.set_ylabel('Battery SOC (%)')
            ax.legend()
            
        elif chart_type == 'weather_correlation':
            # Weather vs Production correlation
            if not data['weather'].empty:
                weather_daily = data['weather'].groupby(data['weather']['timestamp'].dt.date).agg({
                    'temperature_2m': 'mean',
                    'cloud_cover': 'mean'
                })
                
                system1_daily = data['system1'].groupby(data['system1']['timestamp'].dt.date)['yield_today'].max()
                
                # Create subplot for temperature and cloud cover
                ax2 = ax.twinx()
                
                ax.plot(weather_daily.index, weather_daily['temperature_2m'], 'r-', label='Temperature (°C)', linewidth=2)
                ax2.plot(weather_daily.index, weather_daily['cloud_cover'], 'b--', label='Cloud Cover (%)', linewidth=2)
                
                ax.set_ylabel('Temperature (°C)', color='r')
                ax2.set_ylabel('Cloud Cover (%)', color='b')
                ax.legend(loc='upper left')
                ax2.legend(loc='upper right')
        
        ax.set_title(title, fontsize=14, fontweight='bold')
        ax.grid(True, alpha=0.3)
        
        # Format x-axis for date charts
        if chart_type in ['daily_yield', 'weather_correlation']:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%m/%d'))
            ax.xaxis.set_major_locator(mdates.DayLocator(interval=max(1, data['period']['days']//10)))
            plt.xticks(rotation=45)
        
        plt.tight_layout()
        
        # Convert to base64
        buffer = io.BytesIO()
        plt.savefig(buffer, format='png', dpi=300, bbox_inches='tight')
        buffer.seek(0)
        image_base64 = base64.b64encode(buffer.getvalue()).decode()
        plt.close()
        
        return image_base64
    
    def calculate_statistics(self, data: Dict) -> Dict:
        """Calculate comprehensive statistics"""
        
        stats = {
            'period': data['period'],
            'system1': {},
            'system2': {},
            'combined': {},
            'weather': {}
        }
        
        # System 1 statistics
        if not data['system1'].empty:
            s1_daily = data['system1'].groupby(data['system1']['timestamp'].dt.date)['yield_today'].max()
            stats['system1'] = {
                'total_yield': s1_daily.sum(),
                'avg_daily_yield': s1_daily.mean(),
                'max_daily_yield': s1_daily.max(),
                'min_daily_yield': s1_daily.min(),
                'peak_power': data['system1']['ac_power'].max(),
                'avg_soc': data['system1']['soc'].mean(),
                'min_soc': data['system1']['soc'].min(),
                'data_points': len(data['system1'])
            }
        
        # System 2 statistics
        if not data['system2'].empty:
            s2_daily = data['system2'].groupby(data['system2']['timestamp'].dt.date)['yield_today'].max()
            stats['system2'] = {
                'total_yield': s2_daily.sum(),
                'avg_daily_yield': s2_daily.mean(),
                'max_daily_yield': s2_daily.max(),
                'min_daily_yield': s2_daily.min(),
                'peak_power': data['system2']['ac_power'].max(),
                'avg_soc': data['system2']['soc'].mean(),
                'min_soc': data['system2']['soc'].min(),
                'data_points': len(data['system2'])
            }
        
        # Combined statistics
        if stats['system1'] and stats['system2']:
            stats['combined'] = {
                'total_yield': stats['system1']['total_yield'] + stats['system2']['total_yield'],
                'avg_daily_yield': stats['system1']['avg_daily_yield'] + stats['system2']['avg_daily_yield'],
                'peak_power': stats['system1']['peak_power'] + stats['system2']['peak_power'],
                'avg_soc': (stats['system1']['avg_soc'] + stats['system2']['avg_soc']) / 2
            }
        
        # Weather statistics
        if not data['weather'].empty:
            stats['weather'] = {
                'avg_temperature': data['weather']['temperature_2m'].mean(),
                'max_temperature': data['weather']['temperature_2m'].max(),
                'min_temperature': data['weather']['temperature_2m'].min(),
                'avg_cloud_cover': data['weather']['cloud_cover'].mean(),
                'avg_humidity': data['weather']['relative_humidity_2m'].mean(),
                'data_points': len(data['weather'])
            }
        
        return stats
    
    def generate_pdf_report(self, data: Dict, stats: Dict) -> bytes:
        """Generate comprehensive PDF report"""
        
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4)
        story = []
        
        # Title
        title = Paragraph("Solar Prediction System - Historical Report", self.title_style)
        story.append(title)
        story.append(Spacer(1, 20))
        
        # Report info
        report_info = f"""
        <b>Report Period:</b> {stats['period']['start'].strftime('%Y-%m-%d')} to {stats['period']['end'].strftime('%Y-%m-%d')}<br/>
        <b>Duration:</b> {stats['period']['days']} days<br/>
        <b>Generated:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}<br/>
        <b>System:</b> Solar Prediction System v1.0.0
        """
        story.append(Paragraph(report_info, self.styles['Normal']))
        story.append(Spacer(1, 20))
        
        # Executive Summary
        story.append(Paragraph("Executive Summary", self.heading_style))
        
        if stats['combined']:
            summary_text = f"""
            During the {stats['period']['days']}-day reporting period, the combined solar systems generated 
            <b>{stats['combined']['total_yield']:.1f} kWh</b> of energy, with an average daily production of 
            <b>{stats['combined']['avg_daily_yield']:.1f} kWh</b>. Peak combined power output reached 
            <b>{stats['combined']['peak_power']:.0f} W</b>, and the average battery SOC was maintained at 
            <b>{stats['combined']['avg_soc']:.1f}%</b>.
            """
            story.append(Paragraph(summary_text, self.styles['Normal']))
        
        story.append(Spacer(1, 20))
        
        # Performance Statistics Table
        story.append(Paragraph("Performance Statistics", self.heading_style))
        
        table_data = [
            ['Metric', 'Σπίτι Πάνω', 'Σπίτι Κάτω', 'Combined'],
            ['Total Yield (kWh)', f"{stats['system1'].get('total_yield', 0):.1f}", 
             f"{stats['system2'].get('total_yield', 0):.1f}", 
             f"{stats['combined'].get('total_yield', 0):.1f}"],
            ['Avg Daily Yield (kWh)', f"{stats['system1'].get('avg_daily_yield', 0):.1f}", 
             f"{stats['system2'].get('avg_daily_yield', 0):.1f}", 
             f"{stats['combined'].get('avg_daily_yield', 0):.1f}"],
            ['Peak Power (W)', f"{stats['system1'].get('peak_power', 0):.0f}", 
             f"{stats['system2'].get('peak_power', 0):.0f}", 
             f"{stats['combined'].get('peak_power', 0):.0f}"],
            ['Avg SOC (%)', f"{stats['system1'].get('avg_soc', 0):.1f}", 
             f"{stats['system2'].get('avg_soc', 0):.1f}", 
             f"{stats['combined'].get('avg_soc', 0):.1f}"]
        ]
        
        table = Table(table_data)
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(table)
        story.append(Spacer(1, 20))
        
        # Charts
        story.append(Paragraph("Performance Charts", self.heading_style))
        
        # Daily Yield Chart
        daily_yield_chart = self.create_chart(data, 'daily_yield', 'Daily Energy Production')
        img_data = base64.b64decode(daily_yield_chart)
        img = Image(io.BytesIO(img_data), width=6*inch, height=3*inch)
        story.append(img)
        story.append(Spacer(1, 10))
        
        # Power Profile Chart
        power_profile_chart = self.create_chart(data, 'power_profile', 'Average Hourly Power Profile')
        img_data = base64.b64decode(power_profile_chart)
        img = Image(io.BytesIO(img_data), width=6*inch, height=3*inch)
        story.append(img)
        story.append(Spacer(1, 10))
        
        # Page break for more charts
        story.append(PageBreak())
        
        # SOC Trends Chart
        story.append(Paragraph("Battery and Weather Analysis", self.heading_style))
        soc_chart = self.create_chart(data, 'soc_trends', 'Battery SOC Trends')
        img_data = base64.b64decode(soc_chart)
        img = Image(io.BytesIO(img_data), width=6*inch, height=3*inch)
        story.append(img)
        story.append(Spacer(1, 10))
        
        # Weather Correlation Chart
        if stats['weather']:
            weather_chart = self.create_chart(data, 'weather_correlation', 'Weather Conditions')
            img_data = base64.b64decode(weather_chart)
            img = Image(io.BytesIO(img_data), width=6*inch, height=3*inch)
            story.append(img)
            story.append(Spacer(1, 20))
        
        # Weather Statistics
        if stats['weather']:
            story.append(Paragraph("Weather Summary", self.heading_style))
            weather_text = f"""
            <b>Average Temperature:</b> {stats['weather']['avg_temperature']:.1f}°C<br/>
            <b>Temperature Range:</b> {stats['weather']['min_temperature']:.1f}°C to {stats['weather']['max_temperature']:.1f}°C<br/>
            <b>Average Cloud Cover:</b> {stats['weather']['avg_cloud_cover']:.1f}%<br/>
            <b>Average Humidity:</b> {stats['weather']['avg_humidity']:.1f}%<br/>
            <b>Weather Data Points:</b> {stats['weather']['data_points']:,}
            """
            story.append(Paragraph(weather_text, self.styles['Normal']))
        
        # Footer
        story.append(Spacer(1, 30))
        footer_text = """
        <i>This report was automatically generated by the Solar Prediction System. 
        All data is sourced from real-time monitoring systems and weather APIs. 
        For questions or additional analysis, please contact the system administrator.</i>
        """
        story.append(Paragraph(footer_text, self.styles['Normal']))
        
        # Build PDF
        doc.build(story)
        buffer.seek(0)
        
        return buffer.getvalue()

# Initialize report generator
report_generator = ReportGenerator()

@app.get("/reports/generate")
async def generate_report(
    days: int = Query(30, description="Number of days for the report"),
    format: str = Query("pdf", description="Report format: pdf or json")
):
    """Generate historical report"""
    
    try:
        # Get historical data
        data = report_generator.get_historical_data(days)
        
        # Calculate statistics
        stats = report_generator.calculate_statistics(data)
        
        if format == "json":
            return {
                "report_type": "historical_analysis",
                "period": stats['period'],
                "statistics": stats,
                "data_summary": {
                    "system1_records": len(data['system1']),
                    "system2_records": len(data['system2']),
                    "weather_records": len(data['weather'])
                }
            }
        
        elif format == "pdf":
            # Generate PDF
            pdf_data = report_generator.generate_pdf_report(data, stats)
            
            # Return PDF as response
            return Response(
                content=pdf_data,
                media_type="application/pdf",
                headers={
                    "Content-Disposition": f"attachment; filename=solar_report_{datetime.now().strftime('%Y%m%d')}.pdf"
                }
            )
        
        else:
            raise HTTPException(status_code=400, detail="Invalid format. Use 'pdf' or 'json'")
            
    except Exception as e:
        logger.error(f"Report generation error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/reports/quick-stats")
async def quick_stats(days: int = Query(7, description="Number of days")):
    """Get quick statistics"""
    
    try:
        data = report_generator.get_historical_data(days)
        stats = report_generator.calculate_statistics(data)
        
        return {
            "period": stats['period'],
            "summary": {
                "total_yield_kwh": stats['combined'].get('total_yield', 0),
                "avg_daily_yield_kwh": stats['combined'].get('avg_daily_yield', 0),
                "peak_power_w": stats['combined'].get('peak_power', 0),
                "avg_soc_percent": stats['combined'].get('avg_soc', 0),
                "avg_temperature_c": stats['weather'].get('avg_temperature', 0),
                "avg_cloud_cover_percent": stats['weather'].get('avg_cloud_cover', 0)
            },
            "data_quality": {
                "system1_records": len(data['system1']),
                "system2_records": len(data['system2']),
                "weather_records": len(data['weather']),
                "data_completeness": min(100, (len(data['system1']) + len(data['system2'])) / (days * 48) * 100)  # 48 = 24h * 2 systems
            }
        }
        
    except Exception as e:
        logger.error(f"Quick stats error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "service": "Solar Historical Reports API",
        "version": "1.0.0",
        "status": "running",
        "endpoints": {
            "generate_report": "/reports/generate?days=30&format=pdf",
            "quick_stats": "/reports/quick-stats?days=7",
            "health": "/health"
        },
        "report_formats": ["pdf", "json"],
        "chart_types": [
            "Daily yield comparison",
            "Hourly power profile",
            "SOC trends",
            "Weather correlation"
        ],
        "statistics": [
            "Performance metrics",
            "Weather analysis",
            "Efficiency reports",
            "Trend analysis"
        ]
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "Solar Historical Reports API",
        "timestamp": datetime.now().isoformat()
    }

if __name__ == "__main__":
    print("📊 Starting Solar Historical Reports API on port 8106...")
    uvicorn.run(app, host="0.0.0.0", port=8106)
