#!/usr/bin/env python3
"""
Real Data Checker for Frontend System
Verifies actual data from database before frontend implementation
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime, timedelta
import json
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RealDataChecker:
    """Check real data from database"""
    
    def __init__(self):
        self.conn = None
        self.systems_config = {
            'system_1': {
                'table': 'solax_data',
                'name': 'Σπίτι Πάνω',
                'expected_yield': 14.3,
                'expected_soc': 99
            },
            'system_2': {
                'table': 'solax_data2', 
                'name': 'Σπίτι Κάτω',
                'expected_yield': 17.10,
                'expected_soc': 99
            }
        }
    
    def connect_database(self):
        """Connect to PostgreSQL database"""
        
        # Try different password combinations
        password_options = ['postgres', '', 'password']
        
        for password in password_options:
            try:
                self.conn = psycopg2.connect(
                    host='localhost',
                    database='solar_prediction',
                    user='postgres',
                    password=password
                )
                logger.info(f"✅ Database connected (password: {'postgres' if password else 'empty'})")
                return True
            except Exception as e:
                logger.debug(f"Failed with password '{password}': {e}")
                continue
        
        logger.error("❌ Could not connect to database with any password")
        return False
    
    def check_table_exists(self, table_name):
        """Check if table exists"""
        try:
            cur = self.conn.cursor()
            cur.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = %s
                );
            """, (table_name,))
            
            exists = cur.fetchone()[0]
            cur.close()
            return exists
        except Exception as e:
            logger.error(f"Error checking table {table_name}: {e}")
            return False
    
    def get_latest_system_data(self, system_id):
        """Get latest data for a specific system"""
        
        system_config = self.systems_config[system_id]
        table_name = system_config['table']
        
        if not self.check_table_exists(table_name):
            return None
        
        try:
            cur = self.conn.cursor(cursor_factory=RealDictCursor)
            
            # Get latest record with all relevant fields
            query = f"""
                SELECT 
                    timestamp,
                    yield_today,
                    ac_power,
                    soc,
                    bat_power,
                    powerdc1,
                    powerdc2,
                    temperature,
                    inverter_sn,
                    wifi_sn,
                    feedin_power,
                    consume_energy
                FROM {table_name}
                WHERE timestamp IS NOT NULL
                ORDER BY timestamp DESC
                LIMIT 1
            """
            
            cur.execute(query)
            latest = cur.fetchone()
            cur.close()
            
            if latest:
                # Convert to dict and add metadata
                data = dict(latest)
                data['system_id'] = system_id
                data['system_name'] = system_config['name']
                data['table_name'] = table_name
                
                # Calculate data age
                if data['timestamp']:
                    age = datetime.now() - data['timestamp']
                    data['data_age_minutes'] = age.total_seconds() / 60
                
                return data
            else:
                logger.warning(f"No data found in {table_name}")
                return None
                
        except Exception as e:
            logger.error(f"Error getting latest data from {table_name}: {e}")
            return None
    
    def get_record_counts(self):
        """Get record counts for all tables"""
        
        counts = {}
        
        for system_id, config in self.systems_config.items():
            table_name = config['table']
            
            if self.check_table_exists(table_name):
                try:
                    cur = self.conn.cursor()
                    cur.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cur.fetchone()[0]
                    counts[system_id] = count
                    cur.close()
                except Exception as e:
                    logger.error(f"Error counting records in {table_name}: {e}")
                    counts[system_id] = 0
            else:
                counts[system_id] = 0
        
        # Check weather data
        if self.check_table_exists('weather_data'):
            try:
                cur = self.conn.cursor()
                cur.execute("SELECT COUNT(*) FROM weather_data")
                counts['weather_data'] = cur.fetchone()[0]
                cur.close()
            except Exception as e:
                counts['weather_data'] = 0
        
        return counts
    
    def get_weather_data(self):
        """Get latest weather data"""
        
        if not self.check_table_exists('weather_data'):
            return None
        
        try:
            cur = self.conn.cursor(cursor_factory=RealDictCursor)
            
            query = """
                SELECT 
                    timestamp,
                    temperature_2m,
                    global_horizontal_irradiance,
                    cloud_cover,
                    wind_speed_10m,
                    relative_humidity_2m
                FROM weather_data
                WHERE timestamp IS NOT NULL
                ORDER BY timestamp DESC
                LIMIT 1
            """
            
            cur.execute(query)
            weather = cur.fetchone()
            cur.close()
            
            if weather:
                data = dict(weather)
                if data['timestamp']:
                    age = datetime.now() - data['timestamp']
                    data['data_age_minutes'] = age.total_seconds() / 60
                return data
            else:
                return None
                
        except Exception as e:
            logger.error(f"Error getting weather data: {e}")
            return None
    
    def verify_expected_values(self, system_data, system_id):
        """Verify if data matches expected values"""
        
        if not system_data:
            return False
        
        config = self.systems_config[system_id]
        expected_yield = config['expected_yield']
        expected_soc = config['expected_soc']
        
        actual_yield = system_data.get('yield_today', 0) or 0
        actual_soc = system_data.get('soc', 0) or 0
        
        # Allow small tolerance
        yield_match = abs(actual_yield - expected_yield) < 0.5
        soc_match = abs(actual_soc - expected_soc) < 5
        
        return {
            'yield_match': yield_match,
            'soc_match': soc_match,
            'expected_yield': expected_yield,
            'actual_yield': actual_yield,
            'expected_soc': expected_soc,
            'actual_soc': actual_soc
        }
    
    def generate_report(self):
        """Generate comprehensive real data report"""
        
        if not self.connect_database():
            return None
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'database_status': 'connected',
            'systems': {},
            'weather': None,
            'record_counts': {},
            'verification_results': {}
        }
        
        try:
            # Get record counts
            report['record_counts'] = self.get_record_counts()
            
            # Check each system
            for system_id in self.systems_config.keys():
                system_data = self.get_latest_system_data(system_id)
                report['systems'][system_id] = system_data
                
                if system_data:
                    verification = self.verify_expected_values(system_data, system_id)
                    report['verification_results'][system_id] = verification
            
            # Get weather data
            report['weather'] = self.get_weather_data()
            
            self.conn.close()
            
        except Exception as e:
            logger.error(f"Error generating report: {e}")
            report['error'] = str(e)
        
        return report
    
    def print_report(self, report):
        """Print formatted report"""
        
        print("\n" + "="*80)
        print("🔍 REAL DATA VERIFICATION REPORT")
        print("="*80)
        print(f"📅 Generated: {report['timestamp']}")
        print(f"🗄️ Database: {report['database_status']}")
        print()
        
        # Record counts
        print("📊 RECORD COUNTS:")
        counts = report['record_counts']
        for system_id, config in self.systems_config.items():
            count = counts.get(system_id, 0)
            print(f"   {config['name']}: {count:,} records")
        
        weather_count = counts.get('weather_data', 0)
        print(f"   Weather Data: {weather_count:,} records")
        print()
        
        # System data
        print("🏠 SYSTEM DATA:")
        for system_id, config in self.systems_config.items():
            system_data = report['systems'].get(system_id)
            verification = report['verification_results'].get(system_id)
            
            print(f"\n   📡 {config['name']} ({system_id}):")
            
            if system_data:
                print(f"      📅 Latest: {system_data['timestamp']}")
                print(f"      ⚡ Yield Today: {system_data['yield_today']} kWh")
                print(f"      🔋 AC Power: {system_data['ac_power']} W")
                print(f"      🔋 SOC: {system_data['soc']}%")
                print(f"      🔋 Battery Power: {system_data['bat_power']} W")
                print(f"      🌡️ Temperature: {system_data['temperature']}°C")
                print(f"      🕐 Data Age: {system_data.get('data_age_minutes', 0):.1f} minutes")
                
                if verification:
                    yield_status = "✅" if verification['yield_match'] else "❌"
                    soc_status = "✅" if verification['soc_match'] else "❌"
                    
                    print(f"      🎯 Verification:")
                    print(f"         Yield: {yield_status} Expected {verification['expected_yield']} vs Actual {verification['actual_yield']}")
                    print(f"         SOC: {soc_status} Expected {verification['expected_soc']} vs Actual {verification['actual_soc']}")
            else:
                print(f"      ❌ No data available")
        
        # Weather data
        print(f"\n🌤️ WEATHER DATA:")
        weather = report['weather']
        if weather:
            print(f"   📅 Latest: {weather['timestamp']}")
            print(f"   🌡️ Temperature: {weather['temperature_2m']}°C")
            print(f"   ☀️ GHI: {weather['global_horizontal_irradiance']} W/m²")
            print(f"   ☁️ Cloud Cover: {weather['cloud_cover']}%")
            print(f"   🕐 Data Age: {weather.get('data_age_minutes', 0):.1f} minutes")
        else:
            print(f"   ❌ No weather data available")
        
        print("\n" + "="*80)

def main():
    """Main function"""
    
    print("🔍 REAL DATA CHECKER FOR FRONTEND SYSTEM")
    print("="*60)
    print("🎯 Verifying actual data from database...")
    print()
    
    try:
        checker = RealDataChecker()
        report = checker.generate_report()
        
        if report:
            checker.print_report(report)
            
            # Save report to file
            report_file = 'scripts/frontend_system/real_data_report.json'
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            
            print(f"📄 Report saved to: {report_file}")
            
            # Check if we have valid data for frontend
            systems_ok = all(
                report['systems'].get(system_id) is not None 
                for system_id in checker.systems_config.keys()
            )
            
            if systems_ok:
                print("\n✅ READY FOR FRONTEND IMPLEMENTATION WITH REAL DATA")
                return True
            else:
                print("\n❌ MISSING DATA - CANNOT PROCEED WITH FRONTEND")
                return False
        else:
            print("❌ Failed to generate report")
            return False
            
    except Exception as e:
        print(f"❌ Real data check failed: {e}")
        logger.exception("Real data check failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
