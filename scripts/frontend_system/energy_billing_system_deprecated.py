#!/usr/bin/env python3
"""
DEPRECATED: Energy Billing System (Port 8109)
This service has been deprecated and replaced by Enhanced Billing System (Port 8110)

Date: June 2025
Reason: Unified ROI calculation system with dynamic consumption rates

Migration Guide:
- Use Enhanced Billing System on port 8110 instead
- All endpoints have been migrated with improved functionality
- ROI calculations now use dynamic consumption rates instead of static 70%/30%
"""

import logging
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.WARNING)
logger = logging.getLogger(__name__)

app = FastAPI(title="DEPRECATED: Solar Energy Billing System", version="1.0.0-DEPRECATED")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

DEPRECATION_MESSAGE = {
    "status": "DEPRECATED",
    "message": "This service has been deprecated and replaced by Enhanced Billing System",
    "migration": {
        "old_service": "Energy Billing System (Port 8109)",
        "new_service": "Enhanced Billing System (Port 8110)",
        "migration_date": "June 2025",
        "reason": "Unified ROI calculation with dynamic consumption rates"
    },
    "endpoints": {
        "old_roi": "http://localhost:8109/billing/roi/{system_id}",
        "new_roi": "http://localhost:8110/billing/enhanced/roi/{system_id}",
        "new_comparison": "http://localhost:8110/billing/enhanced/roi/comparison/{system_id}",
        "new_tariffs": "http://localhost:8110/billing/enhanced/tariffs"
    },
    "improvements": [
        "Dynamic consumption rates based on real data",
        "Versioned tariff system with audit trail",
        "System-specific consumption patterns",
        "Enhanced financial analysis",
        "Backward compatibility maintained"
    ],
    "timestamp": datetime.now().isoformat()
}

@app.get("/")
async def root():
    """Root endpoint - shows deprecation notice"""
    logger.warning("DEPRECATED: Energy Billing System accessed. Please migrate to Enhanced Billing System (port 8110)")
    return DEPRECATION_MESSAGE

@app.get("/health")
async def health_check():
    """Health check - shows deprecation notice"""
    logger.warning("DEPRECATED: Health check accessed. Please migrate to Enhanced Billing System (port 8110)")
    return {
        "status": "DEPRECATED",
        "service": "Solar Energy Billing System",
        "message": "This service is deprecated. Use Enhanced Billing System on port 8110",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/billing/roi/{system_id}")
async def deprecated_roi_analysis(system_id: str):
    """Deprecated ROI endpoint - redirects to new service"""
    logger.warning(f"DEPRECATED: ROI analysis accessed for {system_id}. Please migrate to Enhanced Billing System (port 8110)")
    
    raise HTTPException(
        status_code=410,  # Gone
        detail={
            "error": "DEPRECATED_ENDPOINT",
            "message": "This endpoint has been deprecated",
            "migration": {
                "old_endpoint": f"/billing/roi/{system_id}",
                "new_endpoint": f"http://localhost:8110/billing/enhanced/roi/{system_id}",
                "comparison_endpoint": f"http://localhost:8110/billing/enhanced/roi/comparison/{system_id}"
            },
            "deprecation_info": DEPRECATION_MESSAGE
        }
    )

@app.get("/billing/tariffs")
async def deprecated_tariffs():
    """Deprecated tariffs endpoint - redirects to new service"""
    logger.warning("DEPRECATED: Tariffs endpoint accessed. Please migrate to Enhanced Billing System (port 8110)")
    
    raise HTTPException(
        status_code=410,  # Gone
        detail={
            "error": "DEPRECATED_ENDPOINT",
            "message": "This endpoint has been deprecated",
            "migration": {
                "old_endpoint": "/billing/tariffs",
                "new_endpoint": "http://localhost:8110/billing/enhanced/tariffs"
            },
            "deprecation_info": DEPRECATION_MESSAGE
        }
    )

@app.api_route("/{path:path}", methods=["GET", "POST", "PUT", "DELETE", "PATCH"])
async def catch_all(path: str):
    """Catch all other endpoints and show deprecation notice"""
    logger.warning(f"DEPRECATED: Endpoint /{path} accessed. Please migrate to Enhanced Billing System (port 8110)")
    
    raise HTTPException(
        status_code=410,  # Gone
        detail={
            "error": "DEPRECATED_SERVICE",
            "message": "This entire service has been deprecated",
            "requested_path": f"/{path}",
            "migration": {
                "old_service": "Energy Billing System (Port 8109)",
                "new_service": "Enhanced Billing System (Port 8110)",
                "base_url": "http://localhost:8110"
            },
            "deprecation_info": DEPRECATION_MESSAGE
        }
    )

if __name__ == "__main__":
    import uvicorn
    
    print("⚠️  DEPRECATED: Starting Solar Energy Billing System on port 8109...")
    print("🔄 This service has been deprecated and replaced by Enhanced Billing System (port 8110)")
    print("📋 Migration Guide:")
    print("   - Old service: http://localhost:8109")
    print("   - New service: http://localhost:8110")
    print("   - All endpoints migrated with improved functionality")
    print("   - ROI calculations now use dynamic consumption rates")
    print("")
    print("🚨 Please update your applications to use port 8110")
    print("📚 Documentation: Enhanced Billing System provides backward compatibility")
    
    uvicorn.run(app, host="0.0.0.0", port=8109, reload=False)
