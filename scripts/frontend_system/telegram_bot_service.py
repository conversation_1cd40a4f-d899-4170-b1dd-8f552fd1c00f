#!/usr/bin/env python3
"""
Telegram Bot Service for Solar Prediction System
Interactive control and alerting via Telegram
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import asyncio
import logging
import json
import requests
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import (
    Application, CommandHandler, CallbackQueryHandler,
    MessageHandler, filters, ContextTypes
)
import psycopg2
from psycopg2.extras import RealDictCursor
from fastapi import FastAPI
from fastapi.responses import JSONResponse
import uvicorn
import threading

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Configuration
BOT_TOKEN = "8060881816:AAFBhGADTAAcUkbom_FEFSkOHoT5N6t5png"
CHAT_ID = "1510889515"
API_BASE_URL = os.getenv('MAIN_API_URL', 'http://solar-prediction-main:8100')
CONFIG_API_URL = os.getenv('CONFIG_API_URL', 'http://solar-prediction-config:8108')

# Database configuration - Docker-compatible
DB_CONFIG = {
    'host': os.getenv('DATABASE_HOST', 'postgres'),  # Use Docker service name
    'port': int(os.getenv('DATABASE_PORT', '5432')),  # Internal Docker port
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

class ConfigService:
    """Configuration service integration"""
    
    @staticmethod
    async def get_config(key: str) -> Any:
        """Get configuration value"""
        try:
            response = requests.get(f"{CONFIG_API_URL}/config/{key}", timeout=10)
            if response.status_code == 200:
                return response.json()['value']
        except Exception as e:
            logger.error(f"Failed to get config {key}: {e}")
        return None
    
    @staticmethod
    async def update_config(key: str, value: Any, reason: str = None) -> bool:
        """Update configuration value"""
        try:
            payload = {"value": value}
            if reason:
                payload["reason"] = reason
            
            response = requests.put(
                f"{CONFIG_API_URL}/config/{key}",
                json=payload,
                timeout=10
            )
            return response.status_code == 200
        except Exception as e:
            logger.error(f"Failed to update config {key}: {e}")
            return False

class SolarAPIService:
    """Solar prediction API service"""

    @staticmethod
    async def get_health() -> Dict:
        """Get system health"""
        try:
            response = requests.get(f"{API_BASE_URL}/health", timeout=10)
            if response.status_code == 200:
                return response.json()
        except Exception as e:
            logger.error(f"Health check failed: {e}")
        return {"status": "error", "message": "API unavailable"}

    @staticmethod
    async def get_solar_systems() -> List[Dict]:
        """Get list of solar systems"""
        try:
            # Use enhanced production API for system data
            response = requests.get(f"{API_BASE_URL}/api/v1/data/solax/dual", timeout=10)
            if response.status_code == 200:
                data = response.json()
                systems = []
                for system_id, system_data in data.get('systems', {}).items():
                    systems.append({
                        'system_name': system_id,
                        'display_name': system_data.get('name', system_id),
                        'table': system_data.get('table', ''),
                        'status': 'active'
                    })
                return systems
        except Exception as e:
            logger.error(f"Failed to get solar systems: {e}")
        return [
            {'system_name': 'system1', 'display_name': 'Σπίτι Πάνω', 'table': 'solax_data', 'status': 'active'},
            {'system_name': 'system2', 'display_name': 'Σπίτι Κάτω', 'table': 'solax_data2', 'status': 'active'}
        ]

    @staticmethod
    async def get_latest_data(system_id: str = None) -> Dict:
        """Get latest solar data"""
        try:
            url = f"{API_BASE_URL}/api/v1/data/solax/latest"
            if system_id:
                url += f"?system_id={system_id}"
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                return response.json()
        except Exception as e:
            logger.error(f"Failed to get latest data: {e}")
        return {}

    @staticmethod
    async def get_system_stats(system_id: str) -> Dict:
        """Get system statistics"""
        try:
            response = requests.get(f"{API_BASE_URL}/api/v1/stats/system/{system_id}", timeout=10)
            if response.status_code == 200:
                return response.json()
        except Exception as e:
            logger.error(f"Failed to get system stats: {e}")
        return {}

    @staticmethod
    async def get_weather_data() -> Dict:
        """Get latest weather data"""
        try:
            response = requests.get(f"{API_BASE_URL}/api/v1/data/weather/latest", timeout=10)
            if response.status_code == 200:
                return response.json()
        except Exception as e:
            logger.error(f"Failed to get weather data: {e}")
        return {}

    @staticmethod
    async def get_model_info() -> Dict:
        """Get model information"""
        try:
            response = requests.get(f"{API_BASE_URL}/api/v1/model/info", timeout=10)
            if response.status_code == 200:
                return response.json()
        except Exception as e:
            logger.error(f"Failed to get model info: {e}")
        return {}

    @staticmethod
    async def get_production_forecast(system_id: str, days: int = 7) -> Dict:
        """Get production forecast using unified forecast API"""
        try:
            hours = days * 24
            response = requests.get(
                f"http://localhost:8120/forecast/{system_id}?hours={hours}",
                timeout=30
            )
            if response.status_code == 200:
                data = response.json()
                # Convert to expected format for Telegram bot
                return {
                    'tomorrow_prediction': data.get('summary', {}).get('total_predicted_kwh', 0) / days,
                    'total_predicted_kwh': data.get('summary', {}).get('total_predicted_kwh', 0),
                    'forecasted_savings_eur': data.get('summary', {}).get('forecasted_savings_eur', 0),
                    'status': data.get('status', 'unknown'),
                    'forecast_hours': data.get('forecast_hours', hours),
                    'current_status': data.get('current_status', {})
                }
        except Exception as e:
            logger.error(f"Production forecast failed: {e}")
        return {}

    @staticmethod
    async def get_ensemble_prediction(system_id: str, horizon: str = 'daily') -> Dict:
        """Get ensemble prediction"""
        try:
            response = requests.get(
                f"{API_BASE_URL}/api/v1/predict/ensemble/{system_id}?horizon={horizon}",
                timeout=30
            )
            if response.status_code == 200:
                return response.json()
        except Exception as e:
            logger.error(f"Ensemble prediction failed: {e}")
        return {}

    @staticmethod
    async def get_system_comparison() -> Dict:
        """Get system comparison statistics"""
        try:
            response = requests.get(f"{API_BASE_URL}/api/v1/stats/comparison", timeout=10)
            if response.status_code == 200:
                return response.json()
        except Exception as e:
            logger.error(f"System comparison failed: {e}")
        return {}

    @staticmethod
    async def make_prediction(data: Dict, system_id: str = None) -> Dict:
        """Make prediction"""
        try:
            url = f"{API_BASE_URL}/api/v1/predict"
            if system_id:
                url += f"/{system_id}"
            response = requests.post(url, json=data, timeout=30)
            if response.status_code == 200:
                return response.json()
        except Exception as e:
            logger.error(f"Prediction failed: {e}")
        return {}

class TelegramBotService:
    """Main Telegram bot service"""
    
    def __init__(self):
        self.application = Application.builder().token(BOT_TOKEN).build()
        self.setup_handlers()
    
    def setup_handlers(self):
        """Setup command and callback handlers"""
        
        # Command handlers
        self.application.add_handler(CommandHandler("start", self.start_command))
        self.application.add_handler(CommandHandler("help", self.help_command))
        self.application.add_handler(CommandHandler("systems", self.systems_command))
        self.application.add_handler(CommandHandler("status", self.status_command))
        self.application.add_handler(CommandHandler("forecast", self.forecast_command))
        self.application.add_handler(CommandHandler("unified_forecast", self.unified_forecast_command))
        self.application.add_handler(CommandHandler("production", self.production_command))
        self.application.add_handler(CommandHandler("compare", self.compare_command))
        self.application.add_handler(CommandHandler("weather", self.weather_command))
        self.application.add_handler(CommandHandler("config", self.config_command))
        self.application.add_handler(CommandHandler("alerts", self.alerts_command))
        self.application.add_handler(CommandHandler("location", self.location_command))
        
        # Callback query handler for inline buttons
        self.application.add_handler(CallbackQueryHandler(self.button_callback))
        
        # Message handler for text messages
        self.application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_message))
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Start command handler"""
        
        keyboard = [
            [
                InlineKeyboardButton("📊 Dashboard", callback_data="dashboard"),
                InlineKeyboardButton("📈 Forecast", callback_data="forecast")
            ],
            [
                InlineKeyboardButton("🌤️ Weather", callback_data="weather"),
                InlineKeyboardButton("⚙️ Settings", callback_data="settings")
            ],
            [
                InlineKeyboardButton("🔧 Status", callback_data="status"),
                InlineKeyboardButton("📍 Location", callback_data="location")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        welcome_message = """
🌞 **Solar Prediction System Bot**

Welcome to your solar energy management assistant!

**Available Commands:**
• `/status` - System health and current data
• `/forecast` - Solar production forecast
• `/production` - Today's production summary
• `/weather` - Current weather conditions
• `/config` - Configuration management
• `/alerts` - Alert settings
• `/location` - Geographic settings

Use the buttons below for quick access:
        """
        
        await update.message.reply_text(
            welcome_message,
            reply_markup=reply_markup,
            parse_mode='Markdown'
        )
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Help command handler"""
        
        help_text = """
🔧 **Solar Prediction Bot Commands**

**📊 Data & Monitoring:**
• `/status` - System health and current status
• `/production` - Today's production summary
• `/weather` - Current weather conditions

**📈 Predictions:**
• `/forecast [days]` - Solar forecast (default: 7 days)
• `/unified_forecast [hours]` - Unified forecast with weather & financial (default: 72h)

**⚙️ Configuration:**
• `/config show` - Show current configuration
• `/config set key=value` - Update configuration
• `/location set lat=X lon=Y` - Update location

**🚨 Alerts:**
• `/alerts show` - Show alert settings
• `/alerts mute [duration]` - Mute alerts temporarily
• `/alerts enable/disable` - Toggle alerts

**💡 Examples:**
• `/forecast 3` - 3-day forecast
• `/config set ml.confidence_threshold=0.8`
• `/location set lat=38.141 lon=24.007`
• `/alerts mute 1h` - Mute for 1 hour

Use inline buttons for quick navigation!
        """
        
        await update.message.reply_text(help_text, parse_mode='Markdown')

    async def systems_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Systems command handler"""

        try:
            systems = await SolarAPIService.get_solar_systems()

            if not systems:
                await update.message.reply_text("❌ No solar systems found")
                return

            systems_message = "🏠 **Available Solar Systems**\n\n"

            for i, system in enumerate(systems, 1):
                # Get latest data for each system
                latest_data = await SolarAPIService.get_latest_data(system['system_name'])
                stats = await SolarAPIService.get_system_stats(system['system_name'])

                systems_message += f"**{i}. {system['display_name']}**\n"
                systems_message += f"• System ID: `{system['system_name']}`\n"
                systems_message += f"• Location: {system['latitude']:.4f}, {system['longitude']:.4f}\n"
                systems_message += f"• Peak Power: {system['peak_power_kw']} kW\n"
                systems_message += f"• Today's Yield: {latest_data.get('yield_today', 0):.1f} kWh\n"
                systems_message += f"• Current Power: {latest_data.get('ac_power', 0)} W\n"
                systems_message += f"• Battery SOC: {latest_data.get('soc', 0)}%\n"
                systems_message += f"• Status: {system.get('status', 'Unknown')}\n\n"

            systems_message += "**Commands with System ID:**\n"
            systems_message += "• `/status system_1` - Status for specific system\n"
            systems_message += "• `/forecast system_1 7` - 7-day forecast\n"
            systems_message += "• `/production system_1` - Production summary\n"

            # Create inline keyboard for system selection
            keyboard = []
            for system in systems[:5]:  # Limit to 5 systems for keyboard
                keyboard.append([
                    InlineKeyboardButton(
                        f"📊 {system['display_name']}",
                        callback_data=f"system_{system['system_name']}"
                    )
                ])

            keyboard.append([
                InlineKeyboardButton("📈 Compare All", callback_data="compare_all")
            ])

            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                systems_message,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )

        except Exception as e:
            await update.message.reply_text(f"❌ Error getting systems: {e}")
    
    async def status_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Status command handler"""

        try:
            # Parse system ID from arguments
            system_id = None
            if context.args and len(context.args) > 0:
                system_id = context.args[0]

            # Get system health
            health = await SolarAPIService.get_health()
            model_info = await SolarAPIService.get_model_info()

            if system_id:
                # Get specific system status
                latest_data = await SolarAPIService.get_latest_data(system_id)
                stats = await SolarAPIService.get_system_stats(system_id)
                systems = await SolarAPIService.get_solar_systems()

                # Find system info
                system_info = next((s for s in systems if s['system_name'] == system_id), None)
                if not system_info:
                    await update.message.reply_text(f"❌ System '{system_id}' not found")
                    return

                status_emoji = "🟢" if health.get('status') == 'healthy' else "🔴"

                status_message = f"""
{status_emoji} **Status: {system_info['display_name']}**

**🏠 System Info:**
• System ID: `{system_id}`
• Location: {system_info['latitude']:.4f}, {system_info['longitude']:.4f}
• Peak Power: {system_info['peak_power_kw']} kW

**⚡ Current Production:**
• Power: {latest_data.get('ac_power', 0)} W
• Today's Yield: {latest_data.get('yield_today', 0):.1f} kWh
• Total Yield: {latest_data.get('yield_total', 0):.1f} kWh
• Battery SOC: {latest_data.get('soc', 0)}%
• Battery Power: {latest_data.get('bat_power', 0)} W

**📊 Performance (Last 7 Days):**
• Average Daily: {stats.get('avg_daily_yield', 0):.1f} kWh
• Peak Power: {stats.get('peak_power', 0)} W
• Efficiency: {stats.get('efficiency', 0):.1f}%

**🕐 Last Update:** {latest_data.get('timestamp', 'Unknown')}
                """

            else:
                # Get overall system status
                systems = await SolarAPIService.get_solar_systems()
                comparison = await SolarAPIService.get_system_comparison()

                status_emoji = "🟢" if health.get('status') == 'healthy' else "🔴"

                total_yield = sum(s.get('today_yield', 0) for s in comparison.get('systems', []))
                total_power = sum(s.get('current_power', 0) for s in comparison.get('systems', []))
                avg_soc = sum(s.get('soc', 0) for s in comparison.get('systems', [])) / len(comparison.get('systems', [1]))

                status_message = f"""
{status_emoji} **Overall System Status**

**🔧 API Health:** {health.get('status', 'Unknown')}
**📅 Last Update:** {health.get('timestamp', 'Unknown')}

**🏠 Systems Overview:**
• Active Systems: {len(systems)}
• Total Peak Power: {sum(s.get('peak_power_kw', 0) for s in systems)} kW

**⚡ Combined Production:**
• Total Power: {total_power} W
• Total Yield Today: {total_yield:.1f} kWh
• Average SOC: {avg_soc:.0f}%

**🤖 ML Model:**
• Model: {model_info.get('model_name', 'Unknown')}
• Accuracy: {model_info.get('accuracy', 0):.1f}%
• MAE: {model_info.get('mae', 0):.3f} kWh

**📊 Database:**
• Records: {health.get('database_records', 'Unknown')}
                """

            keyboard = [
                [
                    InlineKeyboardButton("🔄 Refresh", callback_data="status"),
                    InlineKeyboardButton("📈 Forecast", callback_data="forecast")
                ],
                [
                    InlineKeyboardButton("🏠 Systems", callback_data="systems"),
                    InlineKeyboardButton("📊 Compare", callback_data="compare")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                status_message,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )

        except Exception as e:
            await update.message.reply_text(f"❌ Error getting status: {e}")
    
    async def forecast_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Forecast command handler using Production ML Pipeline"""

        try:
            # Parse system ID and days parameters
            system_id = None
            days = 7

            if context.args:
                if len(context.args) >= 1:
                    # Check if first arg is system ID or days
                    if context.args[0].isdigit():
                        days = int(context.args[0])
                        days = max(1, min(days, 14))  # Limit to 1-14 days
                    else:
                        system_id = context.args[0]

                if len(context.args) >= 2:
                    try:
                        days = int(context.args[1])
                        days = max(1, min(days, 14))
                    except ValueError:
                        pass

            if system_id:
                # Get forecast for specific system using Production ML Pipeline
                forecast_data = await SolarAPIService.get_production_forecast(system_id, days)
                ensemble_data = await SolarAPIService.get_ensemble_prediction(system_id, 'daily')
                latest_data = await SolarAPIService.get_latest_data(system_id)

                # Get system info
                systems = await SolarAPIService.get_solar_systems()
                system_info = next((s for s in systems if s['system_name'] == system_id), None)

                if not system_info:
                    await update.message.reply_text(f"❌ System '{system_id}' not found")
                    return

                forecast_message = f"""
📈 **{days}-Day ML Forecast: {system_info['display_name']}**

**🤖 Production ML Pipeline Results:**
• Tomorrow: {forecast_data.get('tomorrow_prediction', 0):.1f} kWh
• {days}-Day Total: {forecast_data.get('total_prediction', 0):.1f} kWh
• Daily Average: {forecast_data.get('daily_average', 0):.1f} kWh

**🎯 Ensemble Model:**
• Prediction: {ensemble_data.get('prediction', 0):.1f} kWh
• Confidence: {ensemble_data.get('confidence', 0):.1%}
• Model Used: {ensemble_data.get('model_name', 'Unknown')}
• Features: {ensemble_data.get('features_count', 0)}

**📊 Prediction Interval:**
• Lower Bound: {ensemble_data.get('lower_bound', 0):.1f} kWh
• Upper Bound: {ensemble_data.get('upper_bound', 0):.1f} kWh

**⚡ Current Context:**
• Today's Yield: {latest_data.get('yield_today', 0):.1f} kWh
• Current Power: {latest_data.get('ac_power', 0)} W
• Battery SOC: {latest_data.get('soc', 0)}%

**🔄 Generated:** {datetime.now().strftime('%H:%M')}
                """

            else:
                # Get forecast for all systems
                systems = await SolarAPIService.get_solar_systems()
                total_forecast = 0
                total_confidence = 0
                system_forecasts = []

                for system in systems:
                    try:
                        forecast = await SolarAPIService.get_production_forecast(system['system_name'], days)
                        ensemble = await SolarAPIService.get_ensemble_prediction(system['system_name'], 'daily')

                        system_forecasts.append({
                            'name': system['display_name'],
                            'prediction': forecast.get('tomorrow_prediction', 0),
                            'confidence': ensemble.get('confidence', 0)
                        })

                        total_forecast += forecast.get('tomorrow_prediction', 0)
                        total_confidence += ensemble.get('confidence', 0)

                    except Exception as e:
                        logger.error(f"Forecast failed for {system['system_name']}: {e}")

                avg_confidence = total_confidence / len(systems) if systems else 0

                forecast_message = f"""
📈 **{days}-Day Combined ML Forecast**

**🏠 Total Production Forecast:**
• Tomorrow Total: {total_forecast:.1f} kWh
• {days}-Day Total: {total_forecast * days:.1f} kWh
• Average Confidence: {avg_confidence:.1%}

**🤖 Individual Systems:**
                """

                for sf in system_forecasts[:5]:  # Limit to 5 systems
                    forecast_message += f"• {sf['name']}: {sf['prediction']:.1f} kWh ({sf['confidence']:.0%})\n"

                forecast_message += f"""
**📊 ML Pipeline Status:**
• Models Active: {len(system_forecasts)}
• Prediction Method: Production Ensemble
• Features Used: 101 advanced features

**🔄 Generated:** {datetime.now().strftime('%H:%M')}
                """

            keyboard = [
                [
                    InlineKeyboardButton("📊 Details", callback_data="forecast_details"),
                    InlineKeyboardButton("🔄 Refresh", callback_data="forecast")
                ],
                [
                    InlineKeyboardButton("🏠 Systems", callback_data="systems"),
                    InlineKeyboardButton("📈 Compare", callback_data="compare")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                forecast_message,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )

        except Exception as e:
            await update.message.reply_text(f"❌ Error getting ML forecast: {e}")

    async def unified_forecast_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Unified forecast command using new unified API"""

        try:
            # Parse system ID and hours parameters
            system_id = None
            hours = 72

            if context.args:
                if len(context.args) >= 1:
                    # Check if first arg is system ID or hours
                    if context.args[0].isdigit():
                        hours = int(context.args[0])
                        hours = max(24, min(hours, 168))  # Limit to 24-168 hours
                    else:
                        system_id = context.args[0]

                if len(context.args) >= 2:
                    try:
                        hours = int(context.args[1])
                        hours = max(24, min(hours, 168))
                    except ValueError:
                        pass

            if system_id:
                # Get unified forecast for specific system
                response = requests.get(f"http://localhost:8120/forecast/{system_id}?hours={hours}", timeout=30)
                if response.status_code == 200:
                    forecast_data = response.json()

                    # Get system info
                    systems = await SolarAPIService.get_solar_systems()
                    system_info = next((s for s in systems if s['system_name'] == system_id), None)

                    if not system_info:
                        await update.message.reply_text(f"❌ System '{system_id}' not found")
                        return

                    forecast_message = f"""
🔮 **Unified {hours}h Forecast: {system_info['display_name']}**

**📊 Current Status:**
• Today's Yield: {forecast_data.get('current_status', {}).get('yield_today', 0):.1f} kWh
• Current Power: {forecast_data.get('current_status', {}).get('ac_power', 0)} W
• Battery SOC: {forecast_data.get('current_status', {}).get('soc', 0)}%

**🔮 Forecast Summary:**
• Total Predicted: {forecast_data.get('summary', {}).get('total_predicted_kwh', 0):.1f} kWh
• Average Confidence: {forecast_data.get('summary', {}).get('average_confidence', 0):.1%}
• Peak Hour: {forecast_data.get('summary', {}).get('peak_production_hour', 12)}:00

**💰 Financial Forecast:**
• Forecasted Savings: €{forecast_data.get('summary', {}).get('forecasted_savings_eur', 0):.2f}

**🌤️ Weather Integration:** {forecast_data.get('weather_forecast', {}).get('status', 'Available')}
**🤖 ML Predictions:** {forecast_data.get('ml_predictions', {}).get('status', 'Available')}

**🔄 Generated:** {datetime.now().strftime('%H:%M')}
                    """
                else:
                    forecast_message = f"❌ Failed to get unified forecast for {system_id}"

            else:
                # Get combined forecast for all systems
                response = requests.get(f"http://localhost:8120/combined-forecast?hours={hours}", timeout=30)
                if response.status_code == 200:
                    forecast_data = response.json()

                    forecast_message = f"""
🔮 **Combined Unified {hours}h Forecast**

**🏠 Total Systems Forecast:**
• Total Predicted: {forecast_data.get('combined_summary', {}).get('total_predicted_kwh', 0):.1f} kWh
• Total Savings: €{forecast_data.get('combined_summary', {}).get('total_forecasted_savings_eur', 0):.2f}
• Average Confidence: {forecast_data.get('combined_summary', {}).get('average_confidence', 0):.1%}

**📊 Individual Systems:**
                    """

                    systems_data = forecast_data.get('systems', {})
                    for sys_id, sys_data in systems_data.items():
                        if sys_data.get('status') == 'success':
                            forecast_message += f"• {sys_id.title()}: {sys_data.get('summary', {}).get('total_predicted_kwh', 0):.1f} kWh\n"

                    forecast_message += f"""
**🔮 Unified Features:**
• Weather Integration: ✅
• ML Predictions: ✅
• Financial Forecasting: ✅
• Multi-system Support: ✅

**🔄 Generated:** {datetime.now().strftime('%H:%M')}
                    """
                else:
                    forecast_message = "❌ Failed to get combined unified forecast"

            keyboard = [
                [
                    InlineKeyboardButton("📊 Details", callback_data="unified_forecast_details"),
                    InlineKeyboardButton("🔄 Refresh", callback_data="unified_forecast")
                ],
                [
                    InlineKeyboardButton("🏠 Systems", callback_data="systems"),
                    InlineKeyboardButton("📈 Compare", callback_data="compare")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                forecast_message,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )

        except Exception as e:
            await update.message.reply_text(f"❌ Error getting unified forecast: {e}")

    async def production_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Production command handler"""
        
        try:
            latest_data = await SolarAPIService.get_latest_data()
            
            production_message = f"""
⚡ **Today's Production Summary**

**🔋 Current Status:**
• Power Output: {latest_data.get('ac_power', 0)} W
• Today's Yield: {latest_data.get('yield_today', 0):.1f} kWh
• Total Yield: {latest_data.get('yield_total', 0):.1f} kWh

**🔋 Battery Status:**
• State of Charge: {latest_data.get('soc', 0)}%
• Battery Power: {latest_data.get('bat_power', 0)} W

**📊 Performance:**
• Peak Power Today: {latest_data.get('ac_power', 0)} W
• Efficiency: {(latest_data.get('yield_today', 0) / 10.5 * 100):.1f}%

**🕐 Last Update:** {latest_data.get('timestamp', 'Unknown')}
            """
            
            keyboard = [
                [
                    InlineKeyboardButton("📈 Trend", callback_data="production_trend"),
                    InlineKeyboardButton("🔄 Refresh", callback_data="production")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(
                production_message,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )
            
        except Exception as e:
            await update.message.reply_text(f"❌ Error getting production data: {e}")

    async def compare_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """System comparison command handler"""

        try:
            # Get comparison data
            comparison = await SolarAPIService.get_system_comparison()
            systems = await SolarAPIService.get_solar_systems()

            if not systems:
                await update.message.reply_text("❌ No systems available for comparison")
                return

            compare_message = "📊 **System Performance Comparison**\n\n"

            # Today's performance
            compare_message += "**⚡ Today's Performance:**\n"
            total_yield = 0
            total_power = 0

            for system in comparison.get('systems', []):
                system_info = next((s for s in systems if s['system_name'] == system['system_id']), None)
                if system_info:
                    name = system_info['display_name']
                    yield_today = system.get('today_yield', 0)
                    current_power = system.get('current_power', 0)
                    efficiency = (yield_today / system_info['peak_power_kw']) * 100 if system_info['peak_power_kw'] > 0 else 0

                    compare_message += f"• {name}: {yield_today:.1f} kWh ({efficiency:.1f}%)\n"
                    total_yield += yield_today
                    total_power += current_power

            compare_message += f"• **Total**: {total_yield:.1f} kWh ({total_power} W)\n\n"

            # Weekly performance
            compare_message += "**📈 Weekly Performance:**\n"
            for system in comparison.get('weekly_stats', []):
                system_info = next((s for s in systems if s['system_name'] == system['system_id']), None)
                if system_info:
                    name = system_info['display_name']
                    avg_daily = system.get('avg_daily_yield', 0)
                    total_weekly = system.get('total_weekly_yield', 0)

                    compare_message += f"• {name}: {avg_daily:.1f} kWh/day ({total_weekly:.1f} kWh total)\n"

            # Performance ranking
            compare_message += "\n**🏆 Performance Ranking (Today):**\n"
            ranked_systems = sorted(
                comparison.get('systems', []),
                key=lambda x: x.get('today_yield', 0),
                reverse=True
            )

            for i, system in enumerate(ranked_systems[:5], 1):
                system_info = next((s for s in systems if s['system_name'] == system['system_id']), None)
                if system_info:
                    medal = "🥇" if i == 1 else "🥈" if i == 2 else "🥉" if i == 3 else f"{i}."
                    name = system_info['display_name']
                    yield_today = system.get('today_yield', 0)
                    compare_message += f"{medal} {name}: {yield_today:.1f} kWh\n"

            # ML Model performance
            compare_message += "\n**🤖 ML Model Performance:**\n"
            for system in comparison.get('model_performance', []):
                system_info = next((s for s in systems if s['system_name'] == system['system_id']), None)
                if system_info:
                    name = system_info['display_name']
                    accuracy = system.get('accuracy', 0)
                    mae = system.get('mae', 0)
                    confidence = system.get('confidence', 0)

                    compare_message += f"• {name}: {accuracy:.1f}% acc, {mae:.2f} MAE, {confidence:.0%} conf\n"

            compare_message += f"\n**🔄 Updated:** {datetime.now().strftime('%H:%M')}"

            keyboard = [
                [
                    InlineKeyboardButton("📊 Details", callback_data="compare_details"),
                    InlineKeyboardButton("🔄 Refresh", callback_data="compare")
                ],
                [
                    InlineKeyboardButton("🏠 Systems", callback_data="systems"),
                    InlineKeyboardButton("📈 Forecast", callback_data="forecast")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                compare_message,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )

        except Exception as e:
            await update.message.reply_text(f"❌ Error getting comparison data: {e}")
    
    async def weather_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Weather command handler"""
        
        try:
            weather_data = await SolarAPIService.get_weather_data()
            
            weather_message = f"""
🌤️ **Current Weather Conditions**

**🌡️ Temperature:** {weather_data.get('temperature_2m', 'N/A')}°C
**☁️ Cloud Cover:** {weather_data.get('cloud_cover', 'N/A')}%
**💨 Wind Speed:** {weather_data.get('wind_speed_10m', 'N/A')} km/h
**💧 Humidity:** {weather_data.get('relative_humidity_2m', 'N/A')}%

**☀️ Solar Radiation:**
• GHI: {weather_data.get('global_horizontal_irradiance', 'N/A')} W/m²
• DNI: {weather_data.get('direct_normal_irradiance', 'N/A')} W/m²

**📍 Location:** Marathon, Attica, Greece
**🕐 Last Update:** {weather_data.get('timestamp', 'Unknown')}
            """
            
            keyboard = [
                [
                    InlineKeyboardButton("📈 Forecast", callback_data="weather_forecast"),
                    InlineKeyboardButton("🔄 Refresh", callback_data="weather")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(
                weather_message,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )
            
        except Exception as e:
            await update.message.reply_text(f"❌ Error getting weather data: {e}")
    
    async def config_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Configuration command handler"""
        
        try:
            if not context.args:
                # Show configuration menu
                keyboard = [
                    [
                        InlineKeyboardButton("📊 System", callback_data="config_system"),
                        InlineKeyboardButton("🌍 Location", callback_data="config_location")
                    ],
                    [
                        InlineKeyboardButton("🤖 ML Models", callback_data="config_ml"),
                        InlineKeyboardButton("🚨 Alerts", callback_data="config_alerts")
                    ]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)
                
                await update.message.reply_text(
                    "⚙️ **Configuration Management**\n\nSelect a category to view/edit:",
                    reply_markup=reply_markup,
                    parse_mode='Markdown'
                )
                return
            
            command = context.args[0].lower()
            
            if command == "show":
                # Show current configuration
                config_message = """
⚙️ **Current Configuration**

**🌍 Location:**
• Latitude: 38.141348
• Longitude: 24.007165
• Timezone: Europe/Athens

**🤖 ML Model:**
• Default Model: enhanced_model_v3
• Confidence Threshold: 70%
• Retraining MAE: 2.5 kWh

**🚨 Alerts:**
• Enabled: Yes
• Telegram: Enabled
• Escalation: 30 minutes
                """
                
                await update.message.reply_text(config_message, parse_mode='Markdown')
            
            elif command == "set" and len(context.args) > 1:
                # Set configuration
                param = context.args[1]
                if "=" in param:
                    key, value = param.split("=", 1)
                    
                    # Try to parse value
                    try:
                        if value.lower() in ['true', 'false']:
                            value = value.lower() == 'true'
                        elif value.replace('.', '').isdigit():
                            value = float(value) if '.' in value else int(value)
                    except:
                        pass
                    
                    # Update configuration
                    success = await ConfigService.update_config(key, value, "Updated via Telegram")
                    
                    if success:
                        await update.message.reply_text(f"✅ Configuration updated: {key} = {value}")
                    else:
                        await update.message.reply_text(f"❌ Failed to update configuration: {key}")
                else:
                    await update.message.reply_text("❌ Invalid format. Use: /config set key=value")
            else:
                await update.message.reply_text("❌ Invalid command. Use: /config show or /config set key=value")
                
        except Exception as e:
            await update.message.reply_text(f"❌ Error managing configuration: {e}")
    
    async def alerts_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Alerts command handler"""
        
        try:
            if not context.args:
                # Show alerts menu
                keyboard = [
                    [
                        InlineKeyboardButton("🔔 Enable", callback_data="alerts_enable"),
                        InlineKeyboardButton("🔕 Disable", callback_data="alerts_disable")
                    ],
                    [
                        InlineKeyboardButton("⏸️ Mute 1h", callback_data="alerts_mute_1h"),
                        InlineKeyboardButton("⏸️ Mute 24h", callback_data="alerts_mute_24h")
                    ]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)
                
                alerts_status = await ConfigService.get_config("alerts.enabled")
                status_text = "🔔 Enabled" if alerts_status else "🔕 Disabled"
                
                await update.message.reply_text(
                    f"🚨 **Alert Management**\n\nCurrent Status: {status_text}\n\nSelect an action:",
                    reply_markup=reply_markup,
                    parse_mode='Markdown'
                )
            else:
                command = context.args[0].lower()
                
                if command == "enable":
                    await ConfigService.update_config("alerts.enabled", True, "Enabled via Telegram")
                    await update.message.reply_text("🔔 Alerts enabled")
                elif command == "disable":
                    await ConfigService.update_config("alerts.enabled", False, "Disabled via Telegram")
                    await update.message.reply_text("🔕 Alerts disabled")
                elif command == "mute":
                    duration = context.args[1] if len(context.args) > 1 else "1h"
                    await update.message.reply_text(f"⏸️ Alerts muted for {duration}")
                else:
                    await update.message.reply_text("❌ Invalid command. Use: enable, disable, or mute")
                    
        except Exception as e:
            await update.message.reply_text(f"❌ Error managing alerts: {e}")
    
    async def location_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Location command handler"""
        
        try:
            if not context.args:
                # Show current location
                lat = await ConfigService.get_config("location.default_latitude")
                lon = await ConfigService.get_config("location.default_longitude")
                country = await ConfigService.get_config("location.country_code")
                
                location_message = f"""
📍 **Current Location**

**Coordinates:**
• Latitude: {lat}
• Longitude: {lon}
• Country: {country}

**To update location:**
`/location set lat=38.141 lon=24.007`
                """
                
                await update.message.reply_text(location_message, parse_mode='Markdown')
            else:
                command = context.args[0].lower()
                
                if command == "set" and len(context.args) > 1:
                    # Parse coordinates
                    coords = {}
                    for arg in context.args[1:]:
                        if "=" in arg:
                            key, value = arg.split("=", 1)
                            try:
                                coords[key] = float(value)
                            except ValueError:
                                await update.message.reply_text(f"❌ Invalid coordinate: {arg}")
                                return
                    
                    # Update coordinates
                    if "lat" in coords:
                        await ConfigService.update_config("location.default_latitude", coords["lat"])
                    if "lon" in coords:
                        await ConfigService.update_config("location.default_longitude", coords["lon"])
                    
                    await update.message.reply_text(f"📍 Location updated: {coords}")
                else:
                    await update.message.reply_text("❌ Invalid format. Use: /location set lat=X lon=Y")
                    
        except Exception as e:
            await update.message.reply_text(f"❌ Error managing location: {e}")
    
    async def button_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle inline button callbacks"""
        
        query = update.callback_query
        await query.answer()
        
        data = query.data
        
        # Route callback to appropriate handler
        if data == "dashboard" or data == "status":
            await self.status_command(update, context)
        elif data == "forecast":
            await self.forecast_command(update, context)
        elif data == "weather":
            await self.weather_command(update, context)
        elif data == "production":
            await self.production_command(update, context)
        elif data.startswith("alerts_"):
            action = data.replace("alerts_", "")
            if action == "enable":
                await ConfigService.update_config("alerts.enabled", True)
                await query.edit_message_text("🔔 Alerts enabled")
            elif action == "disable":
                await ConfigService.update_config("alerts.enabled", False)
                await query.edit_message_text("🔕 Alerts disabled")
            elif action.startswith("mute_"):
                duration = action.replace("mute_", "")
                await query.edit_message_text(f"⏸️ Alerts muted for {duration}")
        else:
            await query.edit_message_text(f"🔧 Feature '{data}' is under development")
    
    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle text messages"""
        
        text = update.message.text.lower()
        
        if "status" in text or "health" in text:
            await self.status_command(update, context)
        elif "forecast" in text or "prediction" in text:
            await self.forecast_command(update, context)
        elif "weather" in text:
            await self.weather_command(update, context)
        elif "production" in text or "yield" in text:
            await self.production_command(update, context)
        else:
            await update.message.reply_text(
                "🤖 I didn't understand that. Use /help to see available commands."
            )
    
    async def send_alert(self, message: str, severity: str = "info"):
        """Send alert message to configured chat"""
        
        try:
            severity_emojis = {
                "info": "ℹ️",
                "warning": "⚠️",
                "error": "❌",
                "critical": "🚨"
            }
            
            emoji = severity_emojis.get(severity, "ℹ️")
            alert_message = f"{emoji} **ALERT**\n\n{message}\n\n🕐 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            
            await self.application.bot.send_message(
                chat_id=CHAT_ID,
                text=alert_message,
                parse_mode='Markdown'
            )
            
        except Exception as e:
            logger.error(f"Failed to send alert: {e}")
    
    def run(self):
        """Run the bot"""
        logger.info("Starting Telegram bot...")
        self.application.run_polling()

def main():
    """Main function"""
    
    print("🤖 TELEGRAM BOT SERVICE")
    print("="*60)
    print("🔄 Interactive control and alerting for Solar Prediction System")
    print()
    
    try:
        bot_service = TelegramBotService()
        
        print("✅ Bot initialized successfully")
        print(f"📱 Bot Token: {BOT_TOKEN[:20]}...")
        print(f"💬 Chat ID: {CHAT_ID}")
        print()
        print("🚀 Starting bot service...")
        
        bot_service.run()
        
    except Exception as e:
        print(f"❌ Bot service failed: {e}")
        logger.exception("Telegram bot service failed")
        return False

# Health endpoint for Docker health check
health_app = FastAPI()

@health_app.get("/health")
async def health_check():
    """Health check endpoint for Telegram bot service"""
    try:
        # Check if bot is responsive
        health_status = {
            "status": "healthy",
            "service": "telegram_bot_service",
            "timestamp": datetime.now().isoformat(),
            "bot_token_configured": bool(BOT_TOKEN),
            "chat_id_configured": bool(CHAT_ID),
            "api_base_url": API_BASE_URL
        }

        # Test API connectivity
        try:
            response = requests.get(f"{API_BASE_URL}/health", timeout=5)
            health_status["api_connectivity"] = response.status_code == 200
        except:
            health_status["api_connectivity"] = False

        return JSONResponse(content=health_status)
    except Exception as e:
        return JSONResponse(
            content={
                "status": "unhealthy",
                "service": "telegram_bot_service",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            },
            status_code=500
        )

def run_health_server():
    """Run health check server in background"""
    uvicorn.run(health_app, host="0.0.0.0", port=8109, log_level="warning")

if __name__ == "__main__":
    # Start health server in background thread
    health_thread = threading.Thread(target=run_health_server, daemon=True)
    health_thread.start()

    # Start main bot
    main()
