<!DOCTYPE html>
<html lang="el">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Solar Monitoring Dashboard - Updated</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }

        .dashboard-container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            gap: 20px;
        }

        .status-item {
            flex: 1;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 15px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .status-item .label {
            font-size: 0.9em;
            opacity: 0.8;
            margin-bottom: 5px;
        }

        .status-item .value {
            font-size: 1.5em;
            font-weight: bold;
        }

        .status-item.healthy .value {
            color: #4CAF50;
        }

        .status-item.warning .value {
            color: #FF9800;
        }

        .status-item.error .value {
            color: #F44336;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .card-icon {
            font-size: 2em;
            margin-right: 15px;
        }

        .card-title {
            font-size: 1.3em;
            font-weight: bold;
        }

        .metric-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
        }

        .metric-label {
            font-size: 1em;
            opacity: 0.9;
        }

        .metric-value {
            font-size: 1.2em;
            font-weight: bold;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            overflow: hidden;
            margin-top: 5px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .chart-container {
            height: 200px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            margin-top: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.1em;
            opacity: 0.7;
        }

        .refresh-timer {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.7);
            padding: 10px 15px;
            border-radius: 25px;
            font-size: 0.9em;
        }

        .alert-banner {
            background: linear-gradient(45deg, #FF6B6B, #FF8E53);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: bold;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .data-table th,
        .data-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .data-table th {
            background: rgba(255, 255, 255, 0.1);
            font-weight: bold;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
            
            .status-bar {
                flex-direction: column;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Header -->
        <div class="header">
            <h1>🌞 Advanced Solar Monitoring Dashboard</h1>
            <div class="subtitle">Real-time monitoring με 100% πραγματικά δεδομένα από PostgreSQL</div>
        </div>

        <!-- Refresh Timer -->
        <div class="refresh-timer">
            <span id="refreshTimer">Ενημέρωση σε: 10s</span>
        </div>

        <!-- Status Bar -->
        <div class="status-bar">
            <div class="status-item healthy" id="dataCollectionStatus">
                <div class="label">Data Collection</div>
                <div class="value">✅ Active</div>
            </div>
            <div class="status-item healthy" id="databaseStatus">
                <div class="label">Database</div>
                <div class="value">✅ Connected</div>
            </div>
            <div class="status-item healthy" id="telegramStatus">
                <div class="label">Telegram Bot</div>
                <div class="value">✅ Running</div>
            </div>
            <div class="status-item healthy" id="apiStatus">
                <div class="label">API Services</div>
                <div class="value">✅ Healthy</div>
            </div>
        </div>

        <!-- Main Grid -->
        <div class="grid">
            <!-- System 1 Card -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">🏠</div>
                    <div class="card-title">System 1 - Σπίτι Πάνω</div>
                </div>
                
                <div class="metric-row">
                    <div class="metric-label">Yield Today</div>
                    <div class="metric-value" id="system1Yield">-- kWh</div>
                </div>
                
                <div class="metric-row">
                    <div class="metric-label">AC Power</div>
                    <div class="metric-value" id="system1Power">-- W</div>
                </div>
                
                <div class="metric-row">
                    <div class="metric-label">Battery SOC</div>
                    <div class="metric-value" id="system1SOC">-- %</div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="system1SOCBar" style="width: 0%"></div>
                </div>
                
                <div class="metric-row">
                    <div class="metric-label">Last Update</div>
                    <div class="metric-value" id="system1LastUpdate">--</div>
                </div>
            </div>

            <!-- System 2 Card -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">🏘️</div>
                    <div class="card-title">System 2 - Σπίτι Κάτω</div>
                </div>
                
                <div class="metric-row">
                    <div class="metric-label">Yield Today</div>
                    <div class="metric-value" id="system2Yield">-- kWh</div>
                </div>
                
                <div class="metric-row">
                    <div class="metric-label">AC Power</div>
                    <div class="metric-value" id="system2Power">-- W</div>
                </div>
                
                <div class="metric-row">
                    <div class="metric-label">Battery SOC</div>
                    <div class="metric-value" id="system2SOC">-- %</div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="system2SOCBar" style="width: 0%"></div>
                </div>
                
                <div class="metric-row">
                    <div class="metric-label">Last Update</div>
                    <div class="metric-value" id="system2LastUpdate">--</div>
                </div>
            </div>

            <!-- Combined Performance Card -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">⚡</div>
                    <div class="card-title">Combined Performance</div>
                </div>
                
                <div class="metric-row">
                    <div class="metric-label">Total Yield</div>
                    <div class="metric-value" id="totalYield">-- kWh</div>
                </div>
                
                <div class="metric-row">
                    <div class="metric-label">Total AC Power</div>
                    <div class="metric-value" id="totalPower">-- W</div>
                </div>
                
                <div class="metric-row">
                    <div class="metric-label">Average SOC</div>
                    <div class="metric-value" id="averageSOC">-- %</div>
                </div>
                
                <div class="metric-row">
                    <div class="metric-label">Daily Target</div>
                    <div class="metric-value">120 kWh</div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="dailyTargetBar" style="width: 0%"></div>
                </div>
            </div>

            <!-- Data Collection Stats -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">📊</div>
                    <div class="card-title">Data Collection Statistics</div>
                </div>
                
                <div class="metric-row">
                    <div class="metric-label">Collection Rate</div>
                    <div class="metric-value" id="collectionRate">-- %</div>
                </div>
                
                <div class="metric-row">
                    <div class="metric-label">Records (10 min)</div>
                    <div class="metric-value" id="recentRecords">--</div>
                </div>
                
                <div class="metric-row">
                    <div class="metric-label">Total Records</div>
                    <div class="metric-value" id="totalRecords">--</div>
                </div>
                
                <div class="metric-row">
                    <div class="metric-label">Data Quality</div>
                    <div class="metric-value" id="dataQuality">Excellent</div>
                </div>
            </div>

            <!-- Weather Conditions -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">🌤️</div>
                    <div class="card-title">Weather Conditions</div>
                </div>
                
                <div class="metric-row">
                    <div class="metric-label">Temperature</div>
                    <div class="metric-value" id="temperature">-- °C</div>
                </div>
                
                <div class="metric-row">
                    <div class="metric-label">Cloud Cover</div>
                    <div class="metric-value" id="cloudCover">-- %</div>
                </div>
                
                <div class="metric-row">
                    <div class="metric-label">Humidity</div>
                    <div class="metric-value" id="humidity">-- %</div>
                </div>
                
                <div class="metric-row">
                    <div class="metric-label">Location</div>
                    <div class="metric-value">Marathon, Attica</div>
                </div>
            </div>

            <!-- Alert Management -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">🚨</div>
                    <div class="card-title">Alert Management</div>
                </div>

                <div class="metric-row">
                    <div class="metric-label">Low Battery Threshold</div>
                    <div class="metric-value">
                        <input type="number" id="lowBatteryThreshold" value="15" min="5" max="50" style="width: 60px; background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.3); color: white; padding: 5px; border-radius: 4px;">%
                    </div>
                </div>

                <div class="metric-row">
                    <div class="metric-label">High Temperature Threshold</div>
                    <div class="metric-value">
                        <input type="number" id="highTempThreshold" value="45" min="30" max="70" style="width: 60px; background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.3); color: white; padding: 5px; border-radius: 4px;">°C
                    </div>
                </div>

                <div class="metric-row">
                    <div class="metric-label">Alert Status</div>
                    <div class="metric-value">
                        <button onclick="toggleAlerts()" id="alertToggle" style="background: #4CAF50; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">Enabled</button>
                    </div>
                </div>

                <div class="metric-row">
                    <div class="metric-label">Save Settings</div>
                    <div class="metric-value">
                        <button onclick="saveAlertSettings()" style="background: #2196F3; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">💾 Save</button>
                    </div>
                </div>
            </div>

            <!-- System Health -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">🔧</div>
                    <div class="card-title">System Health</div>
                </div>
                
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Service</th>
                            <th>Status</th>
                            <th>Uptime</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Data Collector</td>
                            <td><span style="color: #4CAF50;">✅ Running</span></td>
                            <td id="dataCollectorUptime">--</td>
                        </tr>
                        <tr>
                            <td>Telegram Bot</td>
                            <td><span style="color: #4CAF50;">✅ Active</span></td>
                            <td id="telegramUptime">--</td>
                        </tr>
                        <tr>
                            <td>Production API</td>
                            <td><span style="color: #4CAF50;">✅ Healthy</span></td>
                            <td id="apiUptime">--</td>
                        </tr>
                        <tr>
                            <td>Database</td>
                            <td><span style="color: #4CAF50;">✅ Connected</span></td>
                            <td>24/7</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        class AdvancedMonitoringDashboard {
            constructor() {
                this.refreshInterval = 10; // seconds
                this.currentCountdown = this.refreshInterval;
                this.startTime = Date.now();
                
                this.init();
            }
            
            init() {
                this.loadData();
                this.startRefreshTimer();
                this.startCountdown();
            }
            
            async loadData() {
                try {
                    // Simulate API calls - replace with real endpoints
                    const systemData = await this.fetchSystemData();
                    const weatherData = await this.fetchWeatherData();
                    const statsData = await this.fetchStatsData();
                    
                    this.updateSystemData(systemData);
                    this.updateWeatherData(weatherData);
                    this.updateStatsData(statsData);
                    
                } catch (error) {
                    console.error('Error loading data:', error);
                }
            }
            
            async fetchSystemData() {
                try {
                    // Get REAL individual system data from Charts API
                    const [system1Response, system2Response] = await Promise.all([
                        fetch('http://localhost:8103/api/charts/solar-data?hours=1&system=system1'),
                        fetch('http://localhost:8103/api/charts/solar-data?hours=1&system=system2')
                    ]);

                    const system1Data = await system1Response.json();
                    const system2Data = await system2Response.json();

                    // Get latest data points for each system
                    const system1Latest = this.getLatestDataPoint(system1Data, 'system1');
                    const system2Latest = this.getLatestDataPoint(system2Data, 'system2');

                    // Get today's yield for each system
                    const system1Yield = this.calculateTodayYield(system1Data, 'system1');
                    const system2Yield = this.calculateTodayYield(system2Data, 'system2');

                    return {
                        system1: {
                            yield: system1Yield,
                            power: system1Latest.ac_power || 0,
                            soc: system1Latest.soc || 0,
                            lastUpdate: new Date(system1Latest.timestamp || Date.now())
                        },
                        system2: {
                            yield: system2Yield,
                            power: system2Latest.ac_power || 0,
                            soc: system2Latest.soc || 0,
                            lastUpdate: new Date(system2Latest.timestamp || Date.now())
                        }
                    };
                } catch (error) {
                    console.error('Error fetching real system data:', error);
                    // Fallback to API data if available
                    return {
                        system1: { yield: 0, power: 0, soc: 0, lastUpdate: new Date() },
                        system2: { yield: 0, power: 0, soc: 0, lastUpdate: new Date() }
                    };
                }
            }

            getLatestDataPoint(systemData, systemKey) {
                if (!systemData.timestamps || systemData.timestamps.length === 0) {
                    return { ac_power: 0, soc: 0, timestamp: Date.now() };
                }

                const latestIndex = systemData.timestamps.length - 1;
                const systemInfo = systemData[systemKey];

                return {
                    ac_power: systemInfo && systemInfo.power ? systemInfo.power[latestIndex] : 0,
                    soc: systemInfo && systemInfo.soc ? systemInfo.soc[latestIndex] : 0,
                    timestamp: systemData.timestamps[latestIndex]
                };
            }

            calculateTodayYield(systemData, systemKey) {
                if (!systemData.timestamps || systemData.timestamps.length === 0) {
                    return 0;
                }

                const systemInfo = systemData[systemKey];
                if (!systemInfo || !systemInfo.yield || systemInfo.yield.length === 0) {
                    return 0;
                }

                // Get the latest yield value
                const latestYield = systemInfo.yield[systemInfo.yield.length - 1];
                return latestYield || 0;
            }
            
            async fetchWeatherData() {
                try {
                    // Get REAL weather data from Charts API
                    const response = await fetch('http://localhost:8103/api/charts/weather-data?hours=1');
                    const data = await response.json();

                    if (data.timestamps && data.timestamps.length > 0) {
                        const latestIndex = data.timestamps.length - 1;
                        return {
                            temperature: data.temperature ? data.temperature[latestIndex] : 0,
                            cloudCover: data.cloud_cover ? data.cloud_cover[latestIndex] : 0,
                            humidity: data.humidity ? data.humidity[latestIndex] : 0
                        };
                    }

                    // Fallback to default values
                    return {
                        temperature: 0,
                        cloudCover: 0,
                        humidity: 0
                    };
                } catch (error) {
                    console.error('Error fetching weather data:', error);
                    return {
                        temperature: 0,
                        cloudCover: 0,
                        humidity: 0
                    };
                }
            }
            
            async fetchStatsData() {
                try {
                    // Get REAL statistics from Charts API
                    const response = await fetch('http://localhost:8103/api/charts/statistics');
                    const data = await response.json();

                    return {
                        collectionRate: data.collection_efficiency || 0,
                        recentRecords: data.recent_records || 0,
                        totalRecords: data.total_records || 0
                    };
                } catch (error) {
                    console.error('Error fetching real stats data:', error);
                    return {
                        collectionRate: 0,
                        recentRecords: 0,
                        totalRecords: 0
                    };
                }
            }
            
            updateSystemData(data) {
                // System 1
                document.getElementById('system1Yield').textContent = `${data.system1.yield} kWh`;
                document.getElementById('system1Power').textContent = `${data.system1.power} W`;
                document.getElementById('system1SOC').textContent = `${data.system1.soc}%`;
                document.getElementById('system1SOCBar').style.width = `${data.system1.soc}%`;
                document.getElementById('system1LastUpdate').textContent = this.formatTime(data.system1.lastUpdate);
                
                // System 2
                document.getElementById('system2Yield').textContent = `${data.system2.yield} kWh`;
                document.getElementById('system2Power').textContent = `${data.system2.power} W`;
                document.getElementById('system2SOC').textContent = `${data.system2.soc}%`;
                document.getElementById('system2SOCBar').style.width = `${data.system2.soc}%`;
                document.getElementById('system2LastUpdate').textContent = this.formatTime(data.system2.lastUpdate);
                
                // Combined
                const totalYield = data.system1.yield + data.system2.yield;
                const totalPower = data.system1.power + data.system2.power;
                const averageSOC = (data.system1.soc + data.system2.soc) / 2;
                
                document.getElementById('totalYield').textContent = `${totalYield.toFixed(1)} kWh`;
                document.getElementById('totalPower').textContent = `${totalPower} W`;
                document.getElementById('averageSOC').textContent = `${averageSOC.toFixed(1)}%`;
                
                // Daily target progress (120 kWh target)
                const targetProgress = (totalYield / 120) * 100;
                document.getElementById('dailyTargetBar').style.width = `${Math.min(targetProgress, 100)}%`;
            }
            
            updateWeatherData(data) {
                document.getElementById('temperature').textContent = `${data.temperature}°C`;
                document.getElementById('cloudCover').textContent = `${data.cloudCover}%`;
                document.getElementById('humidity').textContent = `${data.humidity}%`;
            }
            
            updateStatsData(data) {
                document.getElementById('collectionRate').textContent = `${data.collectionRate}%`;
                document.getElementById('recentRecords').textContent = data.recentRecords;
                document.getElementById('totalRecords').textContent = data.totalRecords.toLocaleString();
                
                // Update uptime displays
                const uptime = this.formatUptime(Date.now() - this.startTime);
                document.getElementById('dataCollectorUptime').textContent = uptime;
                document.getElementById('telegramUptime').textContent = uptime;
                document.getElementById('apiUptime').textContent = uptime;
            }
            
            formatTime(date) {
                return date.toLocaleTimeString('el-GR');
            }
            
            formatUptime(milliseconds) {
                const seconds = Math.floor(milliseconds / 1000);
                const minutes = Math.floor(seconds / 60);
                const hours = Math.floor(minutes / 60);
                
                if (hours > 0) {
                    return `${hours}h ${minutes % 60}m`;
                } else if (minutes > 0) {
                    return `${minutes}m ${seconds % 60}s`;
                } else {
                    return `${seconds}s`;
                }
            }
            
            startRefreshTimer() {
                setInterval(() => {
                    this.loadData();
                    this.currentCountdown = this.refreshInterval;
                }, this.refreshInterval * 1000);
            }
            
            startCountdown() {
                setInterval(() => {
                    this.currentCountdown--;
                    document.getElementById('refreshTimer').textContent = `Ενημέρωση σε: ${this.currentCountdown}s`;
                    
                    if (this.currentCountdown <= 0) {
                        this.currentCountdown = this.refreshInterval;
                    }
                }, 1000);
            }
        }
        
        // Alert Management Functions
        function toggleAlerts() {
            const button = document.getElementById('alertToggle');
            const isEnabled = button.textContent === 'Enabled';

            if (isEnabled) {
                button.textContent = 'Disabled';
                button.style.background = '#f44336';
            } else {
                button.textContent = 'Enabled';
                button.style.background = '#4CAF50';
            }
        }

        function saveAlertSettings() {
            const lowBattery = document.getElementById('lowBatteryThreshold').value;
            const highTemp = document.getElementById('highTempThreshold').value;
            const alertsEnabled = document.getElementById('alertToggle').textContent === 'Enabled';

            const settings = {
                low_battery_threshold: parseInt(lowBattery),
                high_temperature_threshold: parseInt(highTemp),
                alerts_enabled: alertsEnabled
            };

            // Save to alert system API
            fetch('http://localhost:8105/alerts/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(settings)
            })
            .then(response => response.json())
            .then(data => {
                alert('Alert settings saved successfully!');
                console.log('Alert settings saved:', data);
            })
            .catch(error => {
                console.error('Error saving alert settings:', error);
                alert('Error saving alert settings. Check console for details.');
            });
        }

        // Initialize dashboard when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new AdvancedMonitoringDashboard();
        });
    </script>
</body>
</html>
