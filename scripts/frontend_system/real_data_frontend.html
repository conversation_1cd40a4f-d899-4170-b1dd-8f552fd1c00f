<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Solar Prediction Dashboard - Real Data</title>
    
    <!-- React and dependencies -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    
    <!-- Chart.js for visualizations -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    
    <!-- Material Design Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .app-container {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 280px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            overflow-y: auto;
        }
        
        .main-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .logo {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
            font-size: 24px;
            font-weight: 700;
            color: #4facfe;
        }
        
        .logo .material-icons {
            font-size: 32px;
            margin-right: 10px;
        }
        
        .nav-menu {
            list-style: none;
        }
        
        .nav-item {
            margin-bottom: 5px;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            text-decoration: none;
            color: #666;
            border-radius: 10px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .nav-link:hover, .nav-link.active {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            transform: translateX(5px);
        }
        
        .nav-link .material-icons {
            margin-right: 12px;
            font-size: 20px;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .card-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            display: flex;
            align-items: center;
        }
        
        .card-title .material-icons {
            margin-right: 10px;
            color: #4facfe;
        }
        
        .grid {
            display: grid;
            gap: 20px;
        }
        
        .grid-2 {
            grid-template-columns: 1fr 1fr;
        }
        
        .grid-3 {
            grid-template-columns: 1fr 1fr 1fr;
        }
        
        .grid-4 {
            grid-template-columns: repeat(4, 1fr);
        }
        
        .metric-card {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .metric-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
            animation: shimmer 3s infinite;
        }
        
        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }
        
        .metric-value {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 5px;
            position: relative;
            z-index: 1;
        }
        
        .metric-label {
            font-size: 14px;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }
        
        .metric-timestamp {
            font-size: 12px;
            opacity: 0.7;
            margin-top: 5px;
            position: relative;
            z-index: 1;
        }
        
        .status-indicator {
            display: inline-flex;
            align-items: center;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .status-online {
            background: #e8f5e8;
            color: #2e7d32;
        }
        
        .status-warning {
            background: #fff3e0;
            color: #f57c00;
        }
        
        .status-error {
            background: #ffebee;
            color: #d32f2f;
        }
        
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px;
            color: #666;
        }
        
        .loading .material-icons {
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        .alert-info {
            background: #e3f2fd;
            color: #1976d2;
            border-left: 4px solid #2196f3;
        }
        
        .alert-success {
            background: #e8f5e8;
            color: #2e7d32;
            border-left: 4px solid #4caf50;
        }
        
        .alert-warning {
            background: #fff3e0;
            color: #f57c00;
            border-left: 4px solid #ff9800;
        }
        
        .alert-error {
            background: #ffebee;
            color: #d32f2f;
            border-left: 4px solid #f44336;
        }
        
        .alert .material-icons {
            margin-right: 10px;
        }
        
        .data-source-badge {
            background: #4caf50;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .refresh-button {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            font-size: 14px;
            transition: transform 0.2s ease;
        }
        
        .refresh-button:hover {
            transform: translateY(-2px);
        }
        
        .refresh-button .material-icons {
            margin-right: 5px;
            font-size: 16px;
        }
        
        .system-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
        }
        
        .system-info h4 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .system-info .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        
        .system-info .info-label {
            color: #666;
            font-weight: 500;
        }
        
        .system-info .info-value {
            color: #333;
            font-weight: 600;
        }
        
        @media (max-width: 768px) {
            .app-container {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                height: auto;
            }
            
            .grid-2,
            .grid-3,
            .grid-4 {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect, useRef } = React;

        // API service for real data
        class RealDataAPIService {
            constructor() {
                this.baseUrl = 'http://localhost:8101';
            }

            async request(endpoint, options = {}) {
                try {
                    const response = await fetch(`${this.baseUrl}${endpoint}`, {
                        headers: {
                            'Content-Type': 'application/json',
                            ...options.headers,
                        },
                        ...options,
                    });
                    
                    if (response.ok) {
                        return await response.json();
                    } else {
                        throw new Error(`API request failed: ${response.status}`);
                    }
                } catch (error) {
                    console.error(`API request failed for ${endpoint}:`, error);
                    throw error;
                }
            }

            async getHealth() {
                return this.request('/health');
            }

            async getModelInfo() {
                return this.request('/api/v1/model/info');
            }

            async getLatestData(systemId = null) {
                const endpoint = systemId ? `/api/v1/data/solax/latest?system_id=${systemId}` : '/api/v1/data/solax/latest';
                return this.request(endpoint);
            }

            async getWeatherData() {
                return this.request('/api/v1/data/weather/latest');
            }

            async getSolarSystems() {
                return this.request('/systems');
            }

            async getSystemStats(systemId) {
                return this.request(`/api/v1/stats/system/${systemId}`);
            }

            async getSystemComparison() {
                return this.request('/api/v1/stats/comparison');
            }

            async makePrediction(data) {
                return this.request('/api/v1/predict', {
                    method: 'POST',
                    body: JSON.stringify(data),
                });
            }

            async getDebugReport() {
                return this.request('/debug/data-report');
            }
        }

        // Global API service
        const apiService = new RealDataAPIService();

        // Dashboard component
        function Dashboard() {
            const [data, setData] = useState({
                health: null,
                modelInfo: null,
                systems: [],
                latestData: null,
                weatherData: null,
                systemStats: null,
                debugReport: null,
            });
            const [loading, setLoading] = useState(true);
            const [error, setError] = useState(null);
            const [lastUpdate, setLastUpdate] = useState(null);

            useEffect(() => {
                loadDashboardData();
                const interval = setInterval(loadDashboardData, 30000); // Refresh every 30 seconds
                return () => clearInterval(interval);
            }, []);

            const loadDashboardData = async () => {
                try {
                    setLoading(true);
                    setError(null);

                    const [health, modelInfo, systems, latestData, weatherData, debugReport] = await Promise.all([
                        apiService.getHealth(),
                        apiService.getModelInfo(),
                        apiService.getSolarSystems(),
                        apiService.getLatestData(),
                        apiService.getWeatherData(),
                        apiService.getDebugReport(),
                    ]);

                    // Get system stats if we have systems
                    let systemStats = null;
                    if (latestData && latestData.system_id) {
                        try {
                            systemStats = await apiService.getSystemStats(latestData.system_id);
                        } catch (e) {
                            console.warn('Could not load system stats:', e);
                        }
                    }

                    setData({
                        health,
                        modelInfo,
                        systems,
                        latestData,
                        weatherData,
                        systemStats,
                        debugReport,
                    });

                    setLastUpdate(new Date());
                } catch (err) {
                    setError(err.message);
                } finally {
                    setLoading(false);
                }
            };

            const formatTimestamp = (timestamp) => {
                if (!timestamp) return 'N/A';
                try {
                    return new Date(timestamp).toLocaleString();
                } catch {
                    return timestamp;
                }
            };

            const getDataAge = (timestamp) => {
                if (!timestamp) return 'Unknown';
                try {
                    const age = Date.now() - new Date(timestamp).getTime();
                    const minutes = Math.floor(age / 60000);
                    const hours = Math.floor(minutes / 60);
                    const days = Math.floor(hours / 24);
                    
                    if (days > 0) return `${days} days ago`;
                    if (hours > 0) return `${hours} hours ago`;
                    if (minutes > 0) return `${minutes} minutes ago`;
                    return 'Just now';
                } catch {
                    return 'Unknown';
                }
            };

            if (loading && !data.health) {
                return (
                    <div className="loading">
                        <span className="material-icons">refresh</span>
                        Loading real solar data...
                    </div>
                );
            }

            if (error) {
                return (
                    <div className="alert alert-error">
                        <span className="material-icons">error</span>
                        Error loading real data: {error}
                    </div>
                );
            }

            return (
                <div>
                    {/* Data Source Alert */}
                    <div className="alert alert-success">
                        <span className="material-icons">verified</span>
                        <div>
                            <strong>Real Data Source Active</strong> - Displaying actual solar system data from CSV files
                            <div className="data-source-badge">LIVE DATA</div>
                        </div>
                    </div>

                    {/* System Information */}
                    {data.latestData && (
                        <div className="card">
                            <div className="card-header">
                                <h3 className="card-title">
                                    <span className="material-icons">home</span>
                                    {data.latestData.system_name}
                                </h3>
                                <button className="refresh-button" onClick={loadDashboardData}>
                                    <span className="material-icons">refresh</span>
                                    Refresh
                                </button>
                            </div>
                            <div className="system-info">
                                <h4>System Information</h4>
                                <div className="info-row">
                                    <span className="info-label">System ID:</span>
                                    <span className="info-value">{data.latestData.system_id}</span>
                                </div>
                                <div className="info-row">
                                    <span className="info-label">WiFi SN:</span>
                                    <span className="info-value">{data.latestData.wifi_sn}</span>
                                </div>
                                <div className="info-row">
                                    <span className="info-label">Last Update:</span>
                                    <span className="info-value">{formatTimestamp(data.latestData.timestamp)}</span>
                                </div>
                                <div className="info-row">
                                    <span className="info-label">Data Age:</span>
                                    <span className="info-value">{getDataAge(data.latestData.timestamp)}</span>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* KPI Cards */}
                    <div className="grid grid-4">
                        <div className="metric-card">
                            <div className="metric-value">
                                {data.latestData?.yield_today?.toFixed(1) || '0.0'}
                            </div>
                            <div className="metric-label">Today's Yield (kWh)</div>
                            <div className="metric-timestamp">
                                Real Data: {formatTimestamp(data.latestData?.timestamp)}
                            </div>
                        </div>
                        <div className="metric-card">
                            <div className="metric-value">
                                {data.latestData?.ac_power?.toFixed(0) || '0'}
                            </div>
                            <div className="metric-label">Current Power (W)</div>
                            <div className="metric-timestamp">
                                Live from {data.latestData?.system_name}
                            </div>
                        </div>
                        <div className="metric-card">
                            <div className="metric-value">
                                {data.latestData?.soc?.toFixed(0) || '0'}%
                            </div>
                            <div className="metric-label">Battery SOC</div>
                            <div className="metric-timestamp">
                                Battery: {data.latestData?.bat_power || 0}W
                            </div>
                        </div>
                        <div className="metric-card">
                            <div className="metric-value">
                                {data.modelInfo?.accuracy?.toFixed(1) || '0.0'}%
                            </div>
                            <div className="metric-label">Model Accuracy</div>
                            <div className="metric-timestamp">
                                {data.modelInfo?.model_name}
                            </div>
                        </div>
                    </div>

                    {/* System Status */}
                    <div className="card">
                        <div className="card-header">
                            <h3 className="card-title">
                                <span className="material-icons">dashboard</span>
                                System Status
                            </h3>
                        </div>
                        <div className="grid grid-2">
                            <div>
                                <h4>API Health</h4>
                                <div className={`status-indicator ${data.health?.status === 'healthy' ? 'status-online' : 'status-error'}`}>
                                    {data.health?.status || 'Unknown'}
                                </div>
                                <p>Records: {data.health?.database_records?.toLocaleString() || 'N/A'}</p>
                            </div>
                            <div>
                                <h4>Data Source</h4>
                                <div className="status-indicator status-online">
                                    CSV Files (Real Data)
                                </div>
                                <p>Last refresh: {lastUpdate ? lastUpdate.toLocaleTimeString() : 'Never'}</p>
                            </div>
                        </div>
                    </div>

                    {/* Weather Information */}
                    {data.weatherData && (
                        <div className="card">
                            <div className="card-header">
                                <h3 className="card-title">
                                    <span className="material-icons">wb_sunny</span>
                                    Weather Conditions
                                </h3>
                            </div>
                            <div className="grid grid-3">
                                <div>
                                    <strong>Temperature:</strong> {data.weatherData.temperature_2m}°C
                                </div>
                                <div>
                                    <strong>Cloud Cover:</strong> {data.weatherData.cloud_cover}%
                                </div>
                                <div>
                                    <strong>GHI:</strong> {data.weatherData.global_horizontal_irradiance} W/m²
                                </div>
                            </div>
                            <p style={{marginTop: '10px', fontSize: '12px', color: '#666'}}>
                                Weather data from: {formatTimestamp(data.weatherData.timestamp)}
                            </p>
                        </div>
                    )}

                    {/* System Statistics */}
                    {data.systemStats && (
                        <div className="card">
                            <div className="card-header">
                                <h3 className="card-title">
                                    <span className="material-icons">analytics</span>
                                    Performance Statistics
                                </h3>
                            </div>
                            <div className="grid grid-3">
                                <div>
                                    <strong>Avg Daily Yield:</strong> {data.systemStats.avg_daily_yield?.toFixed(1)} kWh
                                </div>
                                <div>
                                    <strong>Week Total:</strong> {data.systemStats.total_yield_week?.toFixed(1)} kWh
                                </div>
                                <div>
                                    <strong>Efficiency:</strong> {data.systemStats.efficiency?.toFixed(1)}%
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            );
        }

        // Main App component
        function App() {
            const [currentView, setCurrentView] = useState('dashboard');

            const menuItems = [
                { id: 'dashboard', label: 'Real Data Dashboard', icon: 'dashboard' },
                { id: 'systems', label: 'Solar Systems', icon: 'solar_power' },
                { id: 'weather', label: 'Weather Data', icon: 'wb_sunny' },
                { id: 'debug', label: 'Debug Info', icon: 'bug_report' },
            ];

            const renderContent = () => {
                switch (currentView) {
                    case 'dashboard':
                        return <Dashboard />;
                    default:
                        return (
                            <div className="card">
                                <div className="card-header">
                                    <h3 className="card-title">
                                        <span className="material-icons">construction</span>
                                        {menuItems.find(item => item.id === currentView)?.label || 'Page'}
                                    </h3>
                                </div>
                                <div className="alert alert-info">
                                    <span className="material-icons">info</span>
                                    This section will be implemented with real data integration.
                                </div>
                            </div>
                        );
                }
            };

            return (
                <div className="app-container">
                    <div className="sidebar">
                        <div className="logo">
                            <span className="material-icons">wb_sunny</span>
                            Solar Real Data
                        </div>
                        <nav>
                            <ul className="nav-menu">
                                {menuItems.map(item => (
                                    <li key={item.id} className="nav-item">
                                        <a
                                            className={`nav-link ${currentView === item.id ? 'active' : ''}`}
                                            onClick={() => setCurrentView(item.id)}
                                        >
                                            <span className="material-icons">{item.icon}</span>
                                            {item.label}
                                        </a>
                                    </li>
                                ))}
                            </ul>
                        </nav>
                    </div>
                    <div className="main-content">
                        {renderContent()}
                    </div>
                </div>
            );
        }

        // Render the app
        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
