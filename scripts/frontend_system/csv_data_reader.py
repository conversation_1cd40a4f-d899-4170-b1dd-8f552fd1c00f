#!/usr/bin/env python3
"""
CSV Data Reader for Real Solar Data
Reads actual data from CSV files for frontend implementation
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import csv
import json
from datetime import datetime, timedelta
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CSVDataReader:
    """Read real data from CSV files"""
    
    def __init__(self):
        self.base_path = '/home/<USER>/solar-prediction-project'
        self.csv_files = {
            'system_1': 'test/scripts/solar_prediction_complete_package/data/complete/complete_solax_data_system1.csv',
            'system_2': 'test/scripts/solar_prediction_complete_package/data/complete/complete_solax_data_system2.csv',
            'weather': 'test/scripts/solar_prediction_complete_package/data/complete/complete_weather_data.csv',
            'daily_system_1': 'test/scripts/solar_prediction_complete_package/data/complete/complete_daily_data.csv',
            'daily_system_2': 'test/scripts/solar_prediction_complete_package/data/complete/complete_daily_data_system2.csv'
        }
        
        self.systems_config = {
            'system_1': {
                'name': 'Σπίτι Πάνω',
                'wifi_sn': 'SRFQDPDN9W',
                'expected_yield': 14.3,
                'expected_soc': 99
            },
            'system_2': {
                'name': 'Σπίτι Κάτω', 
                'wifi_sn': 'SRCV9TUD6S',
                'expected_yield': 17.10,
                'expected_soc': 99
            }
        }
    
    def check_file_exists(self, file_key):
        """Check if CSV file exists"""
        if file_key not in self.csv_files:
            return False
        
        file_path = os.path.join(self.base_path, self.csv_files[file_key])
        exists = os.path.exists(file_path)
        
        if exists:
            # Get file size
            size = os.path.getsize(file_path)
            logger.info(f"✅ {file_key}: {file_path} ({size:,} bytes)")
        else:
            logger.warning(f"❌ {file_key}: {file_path} not found")
        
        return exists
    
    def read_latest_system_data(self, system_id):
        """Read latest data for a specific system from CSV"""
        
        file_key = f'system_{system_id}'
        if not self.check_file_exists(file_key):
            return None
        
        file_path = os.path.join(self.base_path, self.csv_files[file_key])
        
        try:
            latest_record = None
            record_count = 0
            
            with open(file_path, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                
                # Read all records to get the latest one
                for row in reader:
                    record_count += 1
                    latest_record = row
            
            if latest_record:
                # Parse and clean the data
                data = {
                    'system_id': system_id,
                    'system_name': self.systems_config[f'system_{system_id}']['name'],
                    'timestamp': latest_record.get('timestamp'),
                    'yield_today': float(latest_record.get('yield_today', 0) or 0),
                    'yield_total': float(latest_record.get('yield_total', 0) or 0),
                    'ac_power': float(latest_record.get('ac_power', 0) or 0),
                    'soc': float(latest_record.get('soc', 0) or 0),
                    'bat_power': float(latest_record.get('bat_power', 0) or 0),
                    'temperature': float(latest_record.get('temperature', 0) or 0),
                    'inverter_sn': latest_record.get('inverter_sn', ''),
                    'wifi_sn': latest_record.get('wifi_sn', ''),
                    'feedin_power': float(latest_record.get('feedin_power', 0) or 0),
                    'consume_energy': float(latest_record.get('consume_energy', 0) or 0),
                    'record_count': record_count
                }
                
                # Calculate data age (assuming latest record is recent)
                if data['timestamp']:
                    try:
                        timestamp = datetime.fromisoformat(data['timestamp'].replace('Z', '+00:00'))
                        age = datetime.now() - timestamp.replace(tzinfo=None)
                        data['data_age_minutes'] = age.total_seconds() / 60
                    except:
                        data['data_age_minutes'] = 0
                
                return data
            else:
                logger.warning(f"No records found in {file_path}")
                return None
                
        except Exception as e:
            logger.error(f"Error reading {file_path}: {e}")
            return None
    
    def read_latest_weather_data(self):
        """Read latest weather data from CSV"""
        
        if not self.check_file_exists('weather'):
            return None
        
        file_path = os.path.join(self.base_path, self.csv_files['weather'])
        
        try:
            latest_record = None
            record_count = 0
            
            with open(file_path, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                
                # Read all records to get the latest one
                for row in reader:
                    record_count += 1
                    latest_record = row
            
            if latest_record:
                data = {
                    'timestamp': latest_record.get('timestamp'),
                    'temperature_2m': float(latest_record.get('temperature_2m', 0) or 0),
                    'global_horizontal_irradiance': float(latest_record.get('global_horizontal_irradiance', 0) or 0),
                    'cloud_cover': float(latest_record.get('cloud_cover', 0) or 0),
                    'wind_speed_10m': float(latest_record.get('wind_speed_10m', 0) or 0),
                    'relative_humidity_2m': float(latest_record.get('relative_humidity_2m', 0) or 0),
                    'record_count': record_count
                }
                
                # Calculate data age
                if data['timestamp']:
                    try:
                        timestamp = datetime.fromisoformat(data['timestamp'].replace('Z', '+00:00'))
                        age = datetime.now() - timestamp.replace(tzinfo=None)
                        data['data_age_minutes'] = age.total_seconds() / 60
                    except:
                        data['data_age_minutes'] = 0
                
                return data
            else:
                return None
                
        except Exception as e:
            logger.error(f"Error reading weather data: {e}")
            return None
    
    def read_daily_summary(self, system_id):
        """Read daily summary data for a system"""
        
        file_key = f'daily_system_{system_id}'
        if not self.check_file_exists(file_key):
            return None
        
        file_path = os.path.join(self.base_path, self.csv_files[file_key])
        
        try:
            records = []
            
            with open(file_path, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                
                for row in reader:
                    if row.get('system_id') == str(system_id):
                        record = {
                            'date': row.get('date'),
                            'daily_yield_kwh': float(row.get('daily_yield_kwh', 0) or 0),
                            'avg_soc': float(row.get('avg_soc', 0) or 0),
                            'min_soc': float(row.get('min_soc', 0) or 0),
                            'max_soc': float(row.get('max_soc', 0) or 0),
                            'records_count': int(row.get('records_count', 0) or 0)
                        }
                        records.append(record)
            
            # Get last 7 days
            recent_records = records[-7:] if len(records) > 7 else records
            
            return {
                'system_id': system_id,
                'total_days': len(records),
                'recent_days': recent_records,
                'avg_daily_yield': sum(r['daily_yield_kwh'] for r in recent_records) / len(recent_records) if recent_records else 0,
                'total_yield_week': sum(r['daily_yield_kwh'] for r in recent_records)
            }
            
        except Exception as e:
            logger.error(f"Error reading daily summary: {e}")
            return None
    
    def verify_expected_values(self, system_data, system_id):
        """Verify if data matches expected values"""
        
        if not system_data:
            return False
        
        config = self.systems_config[f'system_{system_id}']
        expected_yield = config['expected_yield']
        expected_soc = config['expected_soc']
        
        actual_yield = system_data.get('yield_today', 0)
        actual_soc = system_data.get('soc', 0)
        
        # Allow tolerance for real data
        yield_match = abs(actual_yield - expected_yield) < 5.0  # 5 kWh tolerance
        soc_match = abs(actual_soc - expected_soc) < 10  # 10% tolerance
        
        return {
            'yield_match': yield_match,
            'soc_match': soc_match,
            'expected_yield': expected_yield,
            'actual_yield': actual_yield,
            'expected_soc': expected_soc,
            'actual_soc': actual_soc,
            'yield_diff': abs(actual_yield - expected_yield),
            'soc_diff': abs(actual_soc - expected_soc)
        }
    
    def generate_real_data_report(self):
        """Generate comprehensive real data report from CSV files"""
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'data_source': 'CSV files',
            'systems': {},
            'weather': None,
            'daily_summaries': {},
            'verification_results': {},
            'file_status': {}
        }
        
        # Check file availability
        for file_key in self.csv_files.keys():
            report['file_status'][file_key] = self.check_file_exists(file_key)
        
        # Read system data
        for system_id in [1, 2]:
            system_data = self.read_latest_system_data(system_id)
            report['systems'][f'system_{system_id}'] = system_data
            
            if system_data:
                verification = self.verify_expected_values(system_data, system_id)
                report['verification_results'][f'system_{system_id}'] = verification
            
            # Read daily summary
            daily_summary = self.read_daily_summary(system_id)
            report['daily_summaries'][f'system_{system_id}'] = daily_summary
        
        # Read weather data
        report['weather'] = self.read_latest_weather_data()
        
        return report
    
    def print_report(self, report):
        """Print formatted report"""
        
        print("\n" + "="*80)
        print("🔍 REAL DATA REPORT FROM CSV FILES")
        print("="*80)
        print(f"📅 Generated: {report['timestamp']}")
        print(f"📊 Data Source: {report['data_source']}")
        print()
        
        # File status
        print("📁 FILE STATUS:")
        for file_key, exists in report['file_status'].items():
            status = "✅" if exists else "❌"
            print(f"   {status} {file_key}")
        print()
        
        # System data
        print("🏠 SYSTEM DATA:")
        for system_key, system_data in report['systems'].items():
            if system_data:
                verification = report['verification_results'].get(system_key, {})
                daily = report['daily_summaries'].get(system_key, {})
                
                print(f"\n   📡 {system_data['system_name']} ({system_key}):")
                print(f"      📅 Latest: {system_data['timestamp']}")
                print(f"      ⚡ Yield Today: {system_data['yield_today']} kWh")
                print(f"      🔋 AC Power: {system_data['ac_power']} W")
                print(f"      🔋 SOC: {system_data['soc']}%")
                print(f"      🔋 Battery Power: {system_data['bat_power']} W")
                print(f"      🌡️ Temperature: {system_data['temperature']}°C")
                print(f"      📊 Total Records: {system_data['record_count']:,}")
                
                if verification:
                    yield_status = "✅" if verification['yield_match'] else "❌"
                    soc_status = "✅" if verification['soc_match'] else "❌"
                    
                    print(f"      🎯 Verification:")
                    print(f"         Yield: {yield_status} Expected {verification['expected_yield']} vs Actual {verification['actual_yield']} (diff: {verification['yield_diff']:.1f})")
                    print(f"         SOC: {soc_status} Expected {verification['expected_soc']} vs Actual {verification['actual_soc']} (diff: {verification['soc_diff']:.1f})")
                
                if daily:
                    print(f"      📈 Weekly Summary:")
                    print(f"         Avg Daily: {daily['avg_daily_yield']:.1f} kWh")
                    print(f"         Week Total: {daily['total_yield_week']:.1f} kWh")
                    print(f"         Total Days: {daily['total_days']}")
            else:
                print(f"\n   ❌ {system_key}: No data available")
        
        # Weather data
        print(f"\n🌤️ WEATHER DATA:")
        weather = report['weather']
        if weather:
            print(f"   📅 Latest: {weather['timestamp']}")
            print(f"   🌡️ Temperature: {weather['temperature_2m']}°C")
            print(f"   ☀️ GHI: {weather['global_horizontal_irradiance']} W/m²")
            print(f"   ☁️ Cloud Cover: {weather['cloud_cover']}%")
            print(f"   📊 Total Records: {weather['record_count']:,}")
        else:
            print(f"   ❌ No weather data available")
        
        print("\n" + "="*80)

def main():
    """Main function"""
    
    print("🔍 CSV DATA READER FOR REAL SOLAR DATA")
    print("="*60)
    print("📊 Reading actual data from CSV files...")
    print()
    
    try:
        reader = CSVDataReader()
        report = reader.generate_real_data_report()
        
        reader.print_report(report)
        
        # Save report to file
        report_file = 'scripts/frontend_system/csv_data_report.json'
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        print(f"📄 Report saved to: {report_file}")
        
        # Check if we have valid data for frontend
        systems_ok = all(
            report['systems'].get(f'system_{i}') is not None 
            for i in [1, 2]
        )
        
        weather_ok = report['weather'] is not None
        
        if systems_ok and weather_ok:
            print("\n✅ READY FOR FRONTEND IMPLEMENTATION WITH REAL CSV DATA")
            return True
        else:
            print(f"\n❌ MISSING DATA - Systems: {systems_ok}, Weather: {weather_ok}")
            return False
            
    except Exception as e:
        print(f"❌ CSV data read failed: {e}")
        logger.exception("CSV data read failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
