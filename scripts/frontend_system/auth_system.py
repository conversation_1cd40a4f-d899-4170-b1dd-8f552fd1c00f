#!/usr/bin/env python3
"""
Authentication System for Solar Prediction System
JWT-based authentication with user management and permissions
"""

from fastapi import FastAP<PERSON>, HTTPException, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import psycopg2
from psycopg2.extras import RealDictCursor
import jwt
import bcrypt
from datetime import datetime, timedelta
from typing import Optional, List
import uvicorn
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration
SECRET_KEY = "solar_prediction_secret_key_2025"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 1440  # 24 hours

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

app = FastAPI(title="Solar Auth API", version="1.0.0")
security = HTTPBearer()

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models
class UserCreate(BaseModel):
    username: str
    email: str
    password: str
    full_name: str
    role: str = "viewer"

class UserLogin(BaseModel):
    username: str
    password: str

class Token(BaseModel):
    access_token: str
    token_type: str
    user_info: dict

class User(BaseModel):
    id: int
    username: str
    email: str
    full_name: str
    role: str
    is_active: bool
    created_at: datetime

def get_db_connection():
    """Get database connection"""
    try:
        return psycopg2.connect(**DB_CONFIG)
    except Exception as e:
        logger.error(f"Database connection failed: {e}")
        return None

def init_auth_tables():
    """Initialize authentication tables"""
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        cur = conn.cursor()
        
        # Create users table
        cur.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id SERIAL PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                full_name VARCHAR(100) NOT NULL,
                role VARCHAR(20) DEFAULT 'viewer',
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP,
                login_count INTEGER DEFAULT 0
            )
        """)
        
        # Create user sessions table
        cur.execute("""
            CREATE TABLE IF NOT EXISTS user_sessions (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id),
                token_hash VARCHAR(255) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP NOT NULL,
                is_active BOOLEAN DEFAULT TRUE,
                ip_address VARCHAR(45),
                user_agent TEXT
            )
        """)
        
        # Create default admin user if not exists
        cur.execute("SELECT COUNT(*) FROM users WHERE role = 'admin'")
        admin_count = cur.fetchone()[0]
        
        if admin_count == 0:
            admin_password = bcrypt.hashpw("admin123".encode('utf-8'), bcrypt.gensalt())
            cur.execute("""
                INSERT INTO users (username, email, password_hash, full_name, role)
                VALUES (%s, %s, %s, %s, %s)
            """, ("admin", "<EMAIL>", admin_password.decode('utf-8'), "System Administrator", "admin"))
            logger.info("✅ Default admin user created (admin/admin123)")
        
        conn.commit()
        logger.info("✅ Authentication tables initialized")
        return True
        
    except Exception as e:
        logger.error(f"Error initializing auth tables: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """Create JWT access token"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Verify JWT token"""
    try:
        payload = jwt.decode(credentials.credentials, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )
        return username
    except jwt.PyJWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

def get_current_user(username: str = Depends(verify_token)):
    """Get current authenticated user"""
    conn = get_db_connection()
    if not conn:
        raise HTTPException(status_code=500, detail="Database connection failed")
    
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        cur.execute("""
            SELECT id, username, email, full_name, role, is_active, created_at
            FROM users 
            WHERE username = %s AND is_active = TRUE
        """, (username,))
        
        user = cur.fetchone()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found"
            )
        
        conn.close()
        return User(**user)
        
    except Exception as e:
        conn.close()
        raise HTTPException(status_code=500, detail=str(e))

def require_role(required_role: str):
    """Decorator to require specific role"""
    def role_checker(current_user: User = Depends(get_current_user)):
        role_hierarchy = {"viewer": 1, "operator": 2, "admin": 3}
        user_level = role_hierarchy.get(current_user.role, 0)
        required_level = role_hierarchy.get(required_role, 999)
        
        if user_level < required_level:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions"
            )
        return current_user
    return role_checker

@app.post("/auth/register", response_model=dict)
async def register_user(user: UserCreate):
    """Register new user"""
    conn = get_db_connection()
    if not conn:
        raise HTTPException(status_code=500, detail="Database connection failed")
    
    try:
        cur = conn.cursor()
        
        # Check if username or email already exists
        cur.execute("""
            SELECT COUNT(*) FROM users 
            WHERE username = %s OR email = %s
        """, (user.username, user.email))
        
        if cur.fetchone()[0] > 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Username or email already registered"
            )
        
        # Hash password
        password_hash = bcrypt.hashpw(user.password.encode('utf-8'), bcrypt.gensalt())
        
        # Insert user
        cur.execute("""
            INSERT INTO users (username, email, password_hash, full_name, role)
            VALUES (%s, %s, %s, %s, %s)
            RETURNING id
        """, (user.username, user.email, password_hash.decode('utf-8'), user.full_name, user.role))
        
        user_id = cur.fetchone()[0]
        conn.commit()
        
        logger.info(f"✅ New user registered: {user.username}")
        
        return {
            "message": "User registered successfully",
            "user_id": user_id,
            "username": user.username
        }
        
    except Exception as e:
        conn.rollback()
        logger.error(f"Registration error: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        conn.close()

@app.post("/auth/login", response_model=Token)
async def login_user(user_login: UserLogin):
    """User login"""
    conn = get_db_connection()
    if not conn:
        raise HTTPException(status_code=500, detail="Database connection failed")
    
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        
        # Get user
        cur.execute("""
            SELECT id, username, email, password_hash, full_name, role, is_active
            FROM users 
            WHERE username = %s AND is_active = TRUE
        """, (user_login.username,))
        
        user = cur.fetchone()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid username or password"
            )
        
        # Verify password
        if not bcrypt.checkpw(user_login.password.encode('utf-8'), user['password_hash'].encode('utf-8')):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid username or password"
            )
        
        # Update login stats
        cur.execute("""
            UPDATE users 
            SET last_login = CURRENT_TIMESTAMP, login_count = login_count + 1
            WHERE id = %s
        """, (user['id'],))
        
        # Create access token
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": user['username']}, expires_delta=access_token_expires
        )
        
        conn.commit()
        
        logger.info(f"✅ User logged in: {user['username']}")
        
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "user_info": {
                "id": user['id'],
                "username": user['username'],
                "email": user['email'],
                "full_name": user['full_name'],
                "role": user['role']
            }
        }
        
    except Exception as e:
        conn.rollback()
        logger.error(f"Login error: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        conn.close()

@app.get("/auth/me", response_model=User)
async def get_current_user_info(current_user: User = Depends(get_current_user)):
    """Get current user information"""
    return current_user

@app.get("/auth/users", response_model=List[User])
async def list_users(current_user: User = Depends(require_role("admin"))):
    """List all users (admin only)"""
    conn = get_db_connection()
    if not conn:
        raise HTTPException(status_code=500, detail="Database connection failed")
    
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        cur.execute("""
            SELECT id, username, email, full_name, role, is_active, created_at
            FROM users 
            ORDER BY created_at DESC
        """)
        
        users = cur.fetchall()
        conn.close()
        
        return [User(**user) for user in users]
        
    except Exception as e:
        conn.close()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/auth/protected-data")
async def get_protected_data(current_user: User = Depends(get_current_user)):
    """Example protected endpoint"""
    return {
        "message": f"Hello {current_user.full_name}!",
        "user_role": current_user.role,
        "access_level": "authenticated",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/auth/admin-data")
async def get_admin_data(current_user: User = Depends(require_role("admin"))):
    """Example admin-only endpoint"""
    return {
        "message": "Admin access granted",
        "sensitive_data": "This is admin-only information",
        "system_stats": {
            "total_users": "5",
            "active_sessions": "3",
            "system_uptime": "24h"
        }
    }

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "service": "Solar Authentication API",
        "version": "1.0.0",
        "status": "running",
        "endpoints": {
            "login": "/auth/login",
            "register": "/auth/register",
            "me": "/auth/me",
            "users": "/auth/users",
            "protected_data": "/auth/protected-data",
            "admin_data": "/auth/admin-data",
            "health": "/health"
        },
        "authentication": "JWT Bearer Token",
        "default_admin": {
            "username": "admin",
            "password": "admin123",
            "role": "admin"
        }
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "Solar Auth API",
        "timestamp": datetime.now().isoformat()
    }

@app.on_event("startup")
async def startup_event():
    """Initialize auth system on startup"""
    logger.info("🔐 Initializing Authentication System...")
    if init_auth_tables():
        logger.info("✅ Authentication system ready")
    else:
        logger.error("❌ Failed to initialize authentication system")

if __name__ == "__main__":
    print("🔐 Starting Solar Authentication API on port 8104...")
    uvicorn.run(app, host="0.0.0.0", port=8104)
