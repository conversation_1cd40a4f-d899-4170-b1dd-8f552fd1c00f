#!/bin/bash
"""
Enhanced Alert System Deployment Script
======================================

This script safely deploys the Enhanced Alert System v2.0 with hourly intelligence,
replacing the old fixed-threshold system.

Author: Solar Prediction System
Date: June 23, 2025
"""

set -e  # Exit on any error

echo "🚀 Enhanced Alert System Deployment v2.0"
echo "========================================"
echo "$(date): Starting deployment..."

# Configuration
PROJECT_DIR="/home/<USER>/solar-prediction-project"
ALERT_DIR="$PROJECT_DIR/scripts/frontend_system"
BACKUP_DIR="$PROJECT_DIR/backups/alert_system"
LOG_FILE="$PROJECT_DIR/logs/enhanced_alert_deployment.log"

# Create directories if they don't exist
mkdir -p "$BACKUP_DIR"
mkdir -p "$(dirname "$LOG_FILE")"

# Logging function
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S'): $1" | tee -a "$LOG_FILE"
}

log "🔍 Step 1: Pre-deployment checks"

# Check if enhanced alert system files exist
if [ ! -f "$ALERT_DIR/alert_system_enhanced.py" ]; then
    log "❌ ERROR: Enhanced alert system not found!"
    exit 1
fi

if [ ! -f "$ALERT_DIR/hourly_threshold_algorithm.py" ]; then
    log "❌ ERROR: Hourly threshold algorithm not found!"
    exit 1
fi

log "✅ Enhanced alert system files found"

# Check database connectivity
log "🔍 Testing database connectivity..."
if ! python3 -c "
import psycopg2
try:
    conn = psycopg2.connect(host='localhost', port=5433, database='solar_prediction', user='postgres', password='postgres')
    conn.close()
    print('✅ Database connection successful')
except Exception as e:
    print(f'❌ Database connection failed: {e}')
    exit(1)
"; then
    log "❌ ERROR: Database connectivity test failed!"
    exit 1
fi

log "✅ Database connectivity verified"

log "🔄 Step 2: Backup current system"

# Create timestamped backup
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/alert_system_backup_$TIMESTAMP.py"

if [ -f "$ALERT_DIR/alert_system.py" ]; then
    cp "$ALERT_DIR/alert_system.py" "$BACKUP_FILE"
    log "✅ Backup created: $BACKUP_FILE"
else
    log "⚠️ WARNING: Original alert_system.py not found"
fi

log "🛑 Step 3: Stop current alert system"

# Find and stop current alert system processes
ALERT_PIDS=$(pgrep -f "alert_system" || true)
if [ -n "$ALERT_PIDS" ]; then
    log "🛑 Stopping current alert system processes: $ALERT_PIDS"
    echo "$ALERT_PIDS" | xargs -r kill -TERM
    sleep 5
    
    # Force kill if still running
    REMAINING_PIDS=$(pgrep -f "alert_system" || true)
    if [ -n "$REMAINING_PIDS" ]; then
        log "🔨 Force killing remaining processes: $REMAINING_PIDS"
        echo "$REMAINING_PIDS" | xargs -r kill -KILL
    fi
    
    log "✅ Current alert system stopped"
else
    log "ℹ️ No alert system processes found running"
fi

log "🔧 Step 4: Deploy enhanced alert system"

# Test the enhanced system first
log "🧪 Testing enhanced alert system..."
cd "$PROJECT_DIR"

if python3 -c "
from scripts.frontend_system.alert_system_enhanced import EnhancedAlertSystem
import logging
logging.basicConfig(level=logging.WARNING)

try:
    alert_system = EnhancedAlertSystem()
    alert_system.check_enhanced_solar_production()
    print('✅ Enhanced alert system test successful')
except Exception as e:
    print(f'❌ Enhanced alert system test failed: {e}')
    exit(1)
"; then
    log "✅ Enhanced alert system test passed"
else
    log "❌ ERROR: Enhanced alert system test failed!"
    log "🔄 Rolling back..."
    
    # Restore backup if test fails
    if [ -f "$BACKUP_FILE" ]; then
        cp "$BACKUP_FILE" "$ALERT_DIR/alert_system.py"
        log "✅ Backup restored"
    fi
    exit 1
fi

log "🚀 Step 5: Start enhanced alert system"

# Start the enhanced alert system in background
cd "$PROJECT_DIR"
nohup python3 scripts/frontend_system/alert_system_enhanced.py > logs/enhanced_alert_system.log 2>&1 &
ENHANCED_PID=$!

log "🚀 Enhanced alert system started with PID: $ENHANCED_PID"

# Wait a moment for startup
sleep 10

# Verify it's running
if kill -0 "$ENHANCED_PID" 2>/dev/null; then
    log "✅ Enhanced alert system is running (PID: $ENHANCED_PID)"
else
    log "❌ ERROR: Enhanced alert system failed to start!"
    exit 1
fi

log "🔍 Step 6: Verify deployment"

# Test API endpoint
log "🧪 Testing enhanced alert system API..."
if curl -s "http://localhost:8108/health" | grep -q "enhanced_alert_system"; then
    log "✅ Enhanced alert system API is responding"
else
    log "❌ WARNING: Enhanced alert system API not responding on port 8108"
fi

# Test alert functionality
log "🧪 Testing alert functionality..."
if curl -s -X POST "http://localhost:8108/test/production-check" | grep -q "success"; then
    log "✅ Enhanced alert functionality test passed"
else
    log "⚠️ WARNING: Enhanced alert functionality test inconclusive"
fi

log "📊 Step 7: Deployment summary"

echo ""
echo "🎉 ENHANCED ALERT SYSTEM DEPLOYMENT COMPLETED!"
echo "=============================================="
echo "✅ Backup created: $BACKUP_FILE"
echo "✅ Enhanced Alert System v2.0 deployed"
echo "✅ API running on port 8108"
echo "✅ Hourly intelligence enabled"
echo "✅ Weather-adjusted thresholds active"
echo ""
echo "🔧 Key Improvements:"
echo "   • Smart hourly thresholds (vs fixed 30 kWh)"
echo "   • Historical data analysis (14 days)"
echo "   • Weather condition adjustments"
echo "   • False positive prevention"
echo "   • Context-aware alert messages"
echo ""
echo "📊 Monitoring:"
echo "   • Logs: $PROJECT_DIR/logs/enhanced_alert_system.log"
echo "   • Health: curl http://localhost:8108/health"
echo "   • Recent alerts: curl http://localhost:8108/alerts/recent"
echo ""
echo "🎯 Next Steps:"
echo "   • Monitor system for 24 hours"
echo "   • Verify alert accuracy"
echo "   • Update documentation"
echo ""

log "🎉 Deployment completed successfully at $(date)"
echo "Deployment log: $LOG_FILE"
