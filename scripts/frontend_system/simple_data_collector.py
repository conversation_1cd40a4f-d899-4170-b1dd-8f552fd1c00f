#!/usr/bin/env python3
"""
Simple Real-time Data Collector for Solar Systems
Collects data from both SolaX systems every 30 seconds
"""

import sys
import os
import time
import json
import requests
import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

# SolaX API configuration
SOLAX_CONFIG = {
    'token_id': '20250410220826567911082',
    'url': 'https://www.solaxcloud.com:9443/proxy/api/getRealtimeInfo.do',
    'systems': {
        'system1': {
            'wifi_sn': 'SRFQDPDN9W',
            'table': 'solax_data',
            'name': 'Σπίτι Πάνω'
        },
        'system2': {
            'wifi_sn': 'SRCV9TUD6S',
            'table': 'solax_data2',
            'name': 'Σπίτι Κάτω'
        }
    }
}

class SimpleSolarDataCollector:
    """Simple data collector for both solar systems"""
    
    def __init__(self):
        self.db_config = DB_CONFIG
        self.solax_config = SOLAX_CONFIG
        self.collection_stats = {
            'system1': {'success': 0, 'errors': 0, 'last_success': None},
            'system2': {'success': 0, 'errors': 0, 'last_success': None}
        }
        self.is_running = False
    
    def get_db_connection(self):
        """Get database connection"""
        try:
            return psycopg2.connect(**self.db_config)
        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            return None
    
    def fetch_solax_data(self, wifi_sn: str) -> dict:
        """Fetch data from SolaX API for specific system"""
        try:
            params = {
                'tokenId': self.solax_config['token_id'],
                'sn': wifi_sn
            }
            
            response = requests.get(
                self.solax_config['url'],
                params=params,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    return data.get('result', {})
                else:
                    logger.error(f"SolaX API error: {data.get('exception', 'Unknown error')}")
                    return None
            else:
                logger.error(f"HTTP error {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Error fetching SolaX data for {wifi_sn}: {e}")
            return None
    
    def save_system_data(self, system_id: str, data: dict) -> bool:
        """Save system data to database"""
        if not data:
            return False
        
        conn = self.get_db_connection()
        if not conn:
            return False
        
        try:
            cur = conn.cursor()
            
            system_config = self.solax_config['systems'][system_id]
            table_name = system_config['table']
            
            # Extract relevant data
            timestamp = datetime.now()
            yield_today = data.get('yieldtoday', 0)
            soc = data.get('soc', 0)
            ac_power = data.get('acpower', 0)
            bat_power = data.get('batPower', 0)
            temperature = data.get('temperature', 0)
            
            # Insert data
            insert_query = f"""
                INSERT INTO {table_name} 
                (timestamp, yield_today, soc, ac_power, bat_power, temperature)
                VALUES (%s, %s, %s, %s, %s, %s)
            """
            
            cur.execute(insert_query, (
                timestamp, yield_today, soc, ac_power, bat_power, temperature
            ))
            
            conn.commit()
            
            logger.info(f"✅ {system_config['name']}: Yield={yield_today} kWh, SOC={soc}%, AC={ac_power}W")
            
            # Update stats
            self.collection_stats[system_id]['success'] += 1
            self.collection_stats[system_id]['last_success'] = timestamp
            
            return True
            
        except Exception as e:
            logger.error(f"Error saving {system_id} data: {e}")
            conn.rollback()
            self.collection_stats[system_id]['errors'] += 1
            return False
        finally:
            conn.close()
    
    def collect_all_systems(self) -> dict:
        """Collect data from all systems"""
        results = {}
        
        for system_id, system_config in self.solax_config['systems'].items():
            logger.info(f"📊 Collecting data from {system_config['name']}...")
            
            # Fetch data
            data = self.fetch_solax_data(system_config['wifi_sn'])
            
            # Save data
            success = self.save_system_data(system_id, data)
            
            results[system_id] = {
                'success': success,
                'data': data,
                'system_name': system_config['name']
            }
        
        return results
    
    def run_continuous_collection(self, interval_seconds: int = 30):
        """Run continuous data collection"""
        self.is_running = True
        logger.info(f"🚀 Starting continuous data collection (interval: {interval_seconds}s)")
        logger.info("="*70)
        
        try:
            while self.is_running:
                start_time = time.time()
                
                # Collect from all systems
                results = self.collect_all_systems()
                
                # Log summary
                success_count = sum(1 for r in results.values() if r['success'])
                total_count = len(results)
                
                logger.info(f"📈 Collection cycle completed: {success_count}/{total_count} successful")
                
                # Show stats every 10 cycles
                total_cycles = sum(self.collection_stats[s]['success'] for s in self.collection_stats)
                if total_cycles % 10 == 0:
                    self.log_stats()
                
                # Wait for next cycle
                elapsed = time.time() - start_time
                sleep_time = max(0, interval_seconds - elapsed)
                
                if sleep_time > 0:
                    logger.debug(f"⏰ Waiting {sleep_time:.1f}s for next cycle...")
                    time.sleep(sleep_time)
                
        except KeyboardInterrupt:
            logger.info("🛑 Collection stopped by user")
        except Exception as e:
            logger.error(f"❌ Collection error: {e}")
        finally:
            self.is_running = False
            self.log_final_stats()
    
    def log_stats(self):
        """Log collection statistics"""
        logger.info("📊 COLLECTION STATISTICS:")
        for system_id, stats in self.collection_stats.items():
            system_name = self.solax_config['systems'][system_id]['name']
            success_rate = 0
            if stats['success'] + stats['errors'] > 0:
                success_rate = (stats['success'] / (stats['success'] + stats['errors'])) * 100
            
            logger.info(f"   {system_name}: {stats['success']} success, {stats['errors']} errors ({success_rate:.1f}%)")
    
    def log_final_stats(self):
        """Log final statistics"""
        logger.info("="*70)
        logger.info("🎯 FINAL COLLECTION STATISTICS:")
        
        total_success = sum(stats['success'] for stats in self.collection_stats.values())
        total_errors = sum(stats['errors'] for stats in self.collection_stats.values())
        total_attempts = total_success + total_errors
        
        if total_attempts > 0:
            overall_success_rate = (total_success / total_attempts) * 100
            logger.info(f"📈 Overall: {total_success} success, {total_errors} errors ({overall_success_rate:.1f}%)")
        
        for system_id, stats in self.collection_stats.items():
            system_name = self.solax_config['systems'][system_id]['name']
            logger.info(f"   {system_name}: Last success = {stats['last_success']}")
    
    def test_single_collection(self):
        """Test single data collection cycle"""
        logger.info("🧪 TESTING SINGLE DATA COLLECTION")
        logger.info("="*50)
        
        results = self.collect_all_systems()
        
        logger.info("📊 Test Results:")
        for system_id, result in results.items():
            status = "✅ Success" if result['success'] else "❌ Failed"
            logger.info(f"   {result['system_name']}: {status}")
            
            if result['data']:
                data = result['data']
                logger.info(f"      Yield: {data.get('yieldtoday', 0)} kWh")
                logger.info(f"      SOC: {data.get('soc', 0)}%")
                logger.info(f"      AC Power: {data.get('acpower', 0)} W")
        
        return results

def main():
    """Main function"""
    
    print("🌞 SIMPLE SOLAR DATA COLLECTOR")
    print("="*60)
    print("📊 Real-time data collection for both solar systems")
    print("🔄 Collects data every 30 seconds")
    print("💾 Saves to PostgreSQL database")
    print()
    
    try:
        collector = SimpleSolarDataCollector()
        
        # Test database connection
        conn = collector.get_db_connection()
        if not conn:
            print("❌ Database connection failed!")
            return False
        conn.close()
        print("✅ Database connection successful")
        
        # Test single collection
        print("\n🧪 Testing single collection cycle...")
        test_results = collector.test_single_collection()
        
        success_count = sum(1 for r in test_results.values() if r['success'])
        if success_count == 0:
            print("❌ No systems collected data successfully!")
            return False
        
        print(f"\n✅ Test successful! {success_count}/2 systems working")
        
        # Ask user if they want to start continuous collection
        print("\n🚀 Ready to start continuous collection!")
        print("Press Ctrl+C to stop at any time")
        print()
        
        # Start continuous collection
        collector.run_continuous_collection(interval_seconds=30)
        
        return True
        
    except Exception as e:
        print(f"❌ Collector failed: {e}")
        logger.exception("Data collector failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
