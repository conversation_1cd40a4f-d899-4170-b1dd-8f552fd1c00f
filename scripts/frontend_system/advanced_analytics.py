#!/usr/bin/env python3
"""
Advanced Analytics System for Solar Prediction
ML-powered analytics with predictions, trends, and insights
"""

from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
import psycopg2
from psycopg2.extras import RealDictCursor
import numpy as np
import pandas as pd
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Any, Optional
import uvicorn
import logging
import json
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_absolute_error, r2_score
import joblib
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

app = FastAPI(title="Solar Advanced Analytics API", version="1.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

def get_db_connection():
    """Get database connection"""
    try:
        return psycopg2.connect(**DB_CONFIG)
    except Exception as e:
        logger.error(f"Database connection failed: {e}")
        return None

class SolarAnalytics:
    """Advanced solar analytics engine"""
    
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.model_path = "data/models"
        os.makedirs(self.model_path, exist_ok=True)
        
    def prepare_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Prepare features for ML models"""
        
        # Time-based features
        data['hour'] = data['timestamp'].dt.hour
        data['day_of_year'] = data['timestamp'].dt.dayofyear
        data['month'] = data['timestamp'].dt.month
        data['weekday'] = data['timestamp'].dt.weekday
        
        # Solar position features (simplified)
        data['sun_elevation'] = np.sin(2 * np.pi * (data['hour'] - 6) / 12) * \
                               np.sin(2 * np.pi * data['day_of_year'] / 365)
        data['sun_elevation'] = np.maximum(0, data['sun_elevation'])
        
        # Weather features (if available)
        if 'temperature_2m' in data.columns:
            data['temp_normalized'] = (data['temperature_2m'] - 20) / 20  # Normalize around 20°C
        else:
            data['temp_normalized'] = 0
            
        if 'cloud_cover' in data.columns:
            data['clear_sky_factor'] = (100 - data['cloud_cover']) / 100
        else:
            data['clear_sky_factor'] = 1
        
        # Lag features
        for col in ['yield_today', 'ac_power', 'soc']:
            if col in data.columns:
                data[f'{col}_lag1'] = data[col].shift(1)
                data[f'{col}_lag2'] = data[col].shift(2)
        
        # Fill NaN values
        data = data.fillna(method='forward').fillna(0)
        
        return data
    
    def train_prediction_model(self, system_id: str) -> Dict[str, Any]:
        """Train ML model for specific system"""
        
        conn = get_db_connection()
        if not conn:
            return {"error": "Database connection failed"}
        
        try:
            # Get training data (last 30 days)
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)
            
            table_name = 'solax_data' if system_id == 'system1' else 'solax_data2'
            
            query = f"""
                SELECT s.timestamp, s.yield_today, s.ac_power, s.soc, s.bat_power, s.temperature,
                       w.temperature_2m, w.cloud_cover, w.relative_humidity_2m
                FROM {table_name} s
                LEFT JOIN weather_data w ON DATE_TRUNC('hour', s.timestamp) = DATE_TRUNC('hour', w.timestamp)
                WHERE s.timestamp >= %s AND s.timestamp <= %s
                ORDER BY s.timestamp
            """
            
            df = pd.read_sql(query, conn, params=(start_date, end_date))
            conn.close()
            
            if len(df) < 100:
                return {"error": "Insufficient training data"}
            
            # Prepare features
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df = self.prepare_features(df)
            
            # Feature columns
            feature_cols = [
                'hour', 'day_of_year', 'month', 'weekday', 'sun_elevation',
                'temp_normalized', 'clear_sky_factor', 'soc', 'bat_power',
                'yield_today_lag1', 'ac_power_lag1', 'soc_lag1'
            ]
            
            # Prepare training data
            X = df[feature_cols].fillna(0)
            y_power = df['ac_power'].fillna(0)
            y_yield = df['yield_today'].fillna(0)
            
            # Split data (80% train, 20% test)
            split_idx = int(len(X) * 0.8)
            X_train, X_test = X[:split_idx], X[split_idx:]
            y_power_train, y_power_test = y_power[:split_idx], y_power[split_idx:]
            y_yield_train, y_yield_test = y_yield[:split_idx], y_yield[split_idx:]
            
            # Scale features
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)
            
            # Train models
            power_model = RandomForestRegressor(n_estimators=100, random_state=42)
            yield_model = RandomForestRegressor(n_estimators=100, random_state=42)
            
            power_model.fit(X_train_scaled, y_power_train)
            yield_model.fit(X_train_scaled, y_yield_train)
            
            # Evaluate models
            power_pred = power_model.predict(X_test_scaled)
            yield_pred = yield_model.predict(X_test_scaled)
            
            power_mae = mean_absolute_error(y_power_test, power_pred)
            power_r2 = r2_score(y_power_test, power_pred)
            yield_mae = mean_absolute_error(y_yield_test, yield_pred)
            yield_r2 = r2_score(y_yield_test, yield_pred)
            
            # Save models
            model_data = {
                'power_model': power_model,
                'yield_model': yield_model,
                'scaler': scaler,
                'feature_cols': feature_cols,
                'trained_at': datetime.now(),
                'metrics': {
                    'power_mae': power_mae,
                    'power_r2': power_r2,
                    'yield_mae': yield_mae,
                    'yield_r2': yield_r2
                }
            }
            
            self.models[system_id] = model_data
            
            # Save to disk
            joblib.dump(model_data, f"{self.model_path}/{system_id}_model.pkl")
            
            logger.info(f"✅ Model trained for {system_id}: Power R²={power_r2:.3f}, Yield R²={yield_r2:.3f}")
            
            return {
                "status": "success",
                "system_id": system_id,
                "training_samples": len(X_train),
                "test_samples": len(X_test),
                "metrics": model_data['metrics'],
                "feature_importance": dict(zip(feature_cols, power_model.feature_importances_))
            }
            
        except Exception as e:
            logger.error(f"Model training error: {e}")
            return {"error": str(e)}
    
    def predict_next_hours(self, system_id: str, hours: int = 24) -> Dict[str, Any]:
        """Predict next N hours for system"""
        
        if system_id not in self.models:
            # Try to load from disk
            try:
                model_data = joblib.load(f"{self.model_path}/{system_id}_model.pkl")
                self.models[system_id] = model_data
            except:
                return {"error": "Model not found. Please train model first."}
        
        model_data = self.models[system_id]
        
        try:
            # Get latest data for context
            conn = get_db_connection()
            if not conn:
                return {"error": "Database connection failed"}
            
            table_name = 'solax_data' if system_id == 'system1' else 'solax_data2'
            
            query = f"""
                SELECT s.timestamp, s.yield_today, s.ac_power, s.soc, s.bat_power, s.temperature,
                       w.temperature_2m, w.cloud_cover, w.relative_humidity_2m
                FROM {table_name} s
                LEFT JOIN weather_data w ON DATE_TRUNC('hour', s.timestamp) = DATE_TRUNC('hour', w.timestamp)
                ORDER BY s.timestamp DESC
                LIMIT 10
            """
            
            df = pd.read_sql(query, conn)
            conn.close()
            
            if len(df) == 0:
                return {"error": "No recent data available"}
            
            # Get latest values
            latest = df.iloc[0]
            
            # Generate future timestamps
            start_time = datetime.now()
            future_times = [start_time + timedelta(hours=i) for i in range(1, hours + 1)]
            
            predictions = {
                "timestamps": [t.isoformat() for t in future_times],
                "ac_power": [],
                "yield_today": [],
                "confidence": []
            }
            
            # Predict each hour
            for future_time in future_times:
                # Create feature vector
                features = {
                    'hour': future_time.hour,
                    'day_of_year': future_time.timetuple().tm_yday,
                    'month': future_time.month,
                    'weekday': future_time.weekday(),
                    'sun_elevation': max(0, np.sin(2 * np.pi * (future_time.hour - 6) / 12) * 
                                       np.sin(2 * np.pi * future_time.timetuple().tm_yday / 365)),
                    'temp_normalized': (latest['temperature_2m'] or 25 - 20) / 20,
                    'clear_sky_factor': (100 - (latest['cloud_cover'] or 0)) / 100,
                    'soc': latest['soc'] or 95,
                    'bat_power': latest['bat_power'] or 0,
                    'yield_today_lag1': latest['yield_today'] or 0,
                    'ac_power_lag1': latest['ac_power'] or 0,
                    'soc_lag1': latest['soc'] or 95
                }
                
                # Create feature vector
                X = np.array([[features[col] for col in model_data['feature_cols']]])
                X_scaled = model_data['scaler'].transform(X)
                
                # Predict
                power_pred = model_data['power_model'].predict(X_scaled)[0]
                yield_pred = model_data['yield_model'].predict(X_scaled)[0]
                
                # Calculate confidence (simplified)
                confidence = min(95, 80 + features['sun_elevation'] * 15)
                
                predictions["ac_power"].append(max(0, power_pred))
                predictions["yield_today"].append(max(0, yield_pred))
                predictions["confidence"].append(confidence)
            
            return {
                "status": "success",
                "system_id": system_id,
                "prediction_hours": hours,
                "model_metrics": model_data['metrics'],
                "predictions": predictions
            }
            
        except Exception as e:
            logger.error(f"Prediction error: {e}")
            return {"error": str(e)}

# Initialize analytics engine
analytics = SolarAnalytics()

@app.post("/analytics/train-model/{system_id}")
async def train_model(system_id: str):
    """Train ML model for specific system"""
    if system_id not in ['system1', 'system2']:
        raise HTTPException(status_code=400, detail="Invalid system_id")
    
    result = analytics.train_prediction_model(system_id)
    
    if "error" in result:
        raise HTTPException(status_code=500, detail=result["error"])
    
    return result

@app.get("/analytics/predict/{system_id}")
async def predict_system(system_id: str, hours: int = 24):
    """Get predictions for specific system"""
    if system_id not in ['system1', 'system2']:
        raise HTTPException(status_code=400, detail="Invalid system_id")
    
    result = analytics.predict_next_hours(system_id, hours)
    
    if "error" in result:
        raise HTTPException(status_code=500, detail=result["error"])
    
    return result

@app.get("/analytics/performance-analysis")
async def performance_analysis():
    """Get comprehensive performance analysis"""
    
    conn = get_db_connection()
    if not conn:
        raise HTTPException(status_code=500, detail="Database connection failed")
    
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        
        # Get last 7 days performance
        end_date = datetime.now()
        start_date = end_date - timedelta(days=7)
        
        # Daily performance
        cur.execute("""
            SELECT 
                DATE(timestamp) as date,
                MAX(yield_today) as max_yield_system1,
                AVG(ac_power) as avg_power_system1,
                AVG(soc) as avg_soc_system1
            FROM solax_data 
            WHERE timestamp >= %s AND timestamp <= %s
            GROUP BY DATE(timestamp)
            ORDER BY date
        """, (start_date, end_date))
        
        system1_daily = cur.fetchall()
        
        cur.execute("""
            SELECT 
                DATE(timestamp) as date,
                MAX(yield_today) as max_yield_system2,
                AVG(ac_power) as avg_power_system2,
                AVG(soc) as avg_soc_system2
            FROM solax_data2 
            WHERE timestamp >= %s AND timestamp <= %s
            GROUP BY DATE(timestamp)
            ORDER BY date
        """, (start_date, end_date))
        
        system2_daily = cur.fetchall()
        
        # Weather correlation
        cur.execute("""
            SELECT 
                DATE(w.timestamp) as date,
                AVG(w.temperature_2m) as avg_temp,
                AVG(w.cloud_cover) as avg_cloud_cover,
                AVG(w.relative_humidity_2m) as avg_humidity
            FROM weather_data w
            WHERE w.timestamp >= %s AND w.timestamp <= %s
            GROUP BY DATE(w.timestamp)
            ORDER BY date
        """, (start_date, end_date))
        
        weather_daily = cur.fetchall()
        
        # Calculate insights
        total_yield_s1 = sum(row['max_yield_system1'] or 0 for row in system1_daily)
        total_yield_s2 = sum(row['max_yield_system2'] or 0 for row in system2_daily)
        avg_temp = np.mean([row['avg_temp'] or 25 for row in weather_daily])
        avg_cloud = np.mean([row['avg_cloud_cover'] or 0 for row in weather_daily])
        
        # Performance trends
        if len(system1_daily) >= 2:
            recent_yield_s1 = system1_daily[-1]['max_yield_system1'] or 0
            previous_yield_s1 = system1_daily[-2]['max_yield_system1'] or 0
            trend_s1 = ((recent_yield_s1 - previous_yield_s1) / max(previous_yield_s1, 1)) * 100
        else:
            trend_s1 = 0
        
        if len(system2_daily) >= 2:
            recent_yield_s2 = system2_daily[-1]['max_yield_system2'] or 0
            previous_yield_s2 = system2_daily[-2]['max_yield_system2'] or 0
            trend_s2 = ((recent_yield_s2 - previous_yield_s2) / max(previous_yield_s2, 1)) * 100
        else:
            trend_s2 = 0
        
        conn.close()
        
        return {
            "analysis_period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "days": 7
            },
            "performance_summary": {
                "total_yield_system1": round(total_yield_s1, 1),
                "total_yield_system2": round(total_yield_s2, 1),
                "total_combined": round(total_yield_s1 + total_yield_s2, 1),
                "daily_average": round((total_yield_s1 + total_yield_s2) / 7, 1)
            },
            "trends": {
                "system1_trend": round(trend_s1, 1),
                "system2_trend": round(trend_s2, 1),
                "trend_direction": "improving" if (trend_s1 + trend_s2) > 0 else "declining"
            },
            "weather_impact": {
                "avg_temperature": round(avg_temp, 1),
                "avg_cloud_cover": round(avg_cloud, 1),
                "weather_efficiency": round((100 - avg_cloud) * 0.8 + min(avg_temp / 30, 1) * 20, 1)
            },
            "insights": [
                f"Combined systems produced {total_yield_s1 + total_yield_s2:.1f} kWh in 7 days",
                f"Average daily production: {(total_yield_s1 + total_yield_s2) / 7:.1f} kWh",
                f"Weather efficiency: {(100 - avg_cloud) * 0.8 + min(avg_temp / 30, 1) * 20:.1f}%",
                f"System 1 trend: {trend_s1:+.1f}%",
                f"System 2 trend: {trend_s2:+.1f}%"
            ],
            "daily_data": {
                "system1": [dict(row) for row in system1_daily],
                "system2": [dict(row) for row in system2_daily],
                "weather": [dict(row) for row in weather_daily]
            }
        }
        
    except Exception as e:
        conn.close()
        logger.error(f"Performance analysis error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/analytics/predict/monthly/{system_id}")
async def predict_monthly(system_id: str):
    """Get monthly predictions for specific system"""
    if system_id not in ['system1', 'system2']:
        raise HTTPException(status_code=400, detail="Invalid system_id")

    try:
        conn = get_db_connection()
        if not conn:
            raise HTTPException(status_code=500, detail="Database connection failed")

        cur = conn.cursor(cursor_factory=RealDictCursor)

        # Get historical monthly data for pattern analysis
        cur.execute(f"""
            SELECT
                DATE_TRUNC('month', timestamp) as month,
                MAX(yield_today) as daily_max,
                AVG(ac_power) as avg_power,
                AVG(soc) as avg_soc
            FROM {'solax_data' if system_id == 'system1' else 'solax_data2'}
            WHERE timestamp >= NOW() - INTERVAL '12 months'
            GROUP BY DATE_TRUNC('month', timestamp)
            ORDER BY month
        """)

        historical_months = cur.fetchall()

        # Calculate monthly predictions based on seasonal patterns
        current_month = datetime.now().month
        monthly_predictions = []

        for i in range(12):  # Next 12 months
            month = ((current_month + i - 1) % 12) + 1

            # Find similar historical months
            similar_months = [m for m in historical_months if m['month'].month == month]

            if similar_months:
                avg_daily = np.mean([m['daily_max'] for m in similar_months])
                avg_power = np.mean([m['avg_power'] for m in similar_months])
            else:
                # Fallback to seasonal estimation
                seasonal_factor = 0.8 if month in [11, 12, 1, 2] else 1.2 if month in [5, 6, 7, 8] else 1.0
                avg_daily = 45 * seasonal_factor  # Base estimation
                avg_power = 3500 * seasonal_factor

            # Days in month estimation
            days_in_month = 31 if month in [1, 3, 5, 7, 8, 10, 12] else 30 if month != 2 else 28

            monthly_predictions.append({
                "month": month,
                "month_name": datetime(2024, month, 1).strftime('%B'),
                "predicted_total_yield": round(avg_daily * days_in_month, 1),
                "predicted_avg_daily": round(avg_daily, 1),
                "predicted_avg_power": round(avg_power, 0),
                "days_in_month": days_in_month,
                "confidence": 85 if similar_months else 65
            })

        conn.close()

        return {
            "system_id": system_id,
            "prediction_type": "monthly",
            "predictions": monthly_predictions,
            "total_annual_estimate": sum(p["predicted_total_yield"] for p in monthly_predictions),
            "generated_at": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Monthly prediction error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/analytics/predict/annual/{system_id}")
async def predict_annual(system_id: str):
    """Get annual predictions for specific system"""
    if system_id not in ['system1', 'system2']:
        raise HTTPException(status_code=400, detail="Invalid system_id")

    try:
        conn = get_db_connection()
        if not conn:
            raise HTTPException(status_code=500, detail="Database connection failed")

        cur = conn.cursor(cursor_factory=RealDictCursor)

        # Get historical annual data (simplified approach)
        cur.execute(f"""
            SELECT
                EXTRACT(YEAR FROM timestamp) as year,
                MAX(yield_today) as max_daily_yield,
                AVG(ac_power) as avg_power,
                COUNT(*) as data_points
            FROM {'solax_data' if system_id == 'system1' else 'solax_data2'}
            WHERE timestamp >= NOW() - INTERVAL '3 years'
            GROUP BY EXTRACT(YEAR FROM timestamp)
            ORDER BY year
        """)

        historical_years = cur.fetchall()

        # Calculate trend and predictions
        if len(historical_years) >= 2:
            yields = [float(y['max_daily_yield'] or 0) * 365 for y in historical_years]  # Estimate annual from max daily
            years = list(range(len(yields)))

            # Simple linear trend
            if len(yields) > 1:
                trend = (yields[-1] - yields[0]) / (len(yields) - 1)
            else:
                trend = 0
        else:
            trend = 0
            yields = [16000]  # Default estimate

        # Generate 5-year predictions
        current_year = datetime.now().year
        annual_predictions = []

        for i in range(5):
            year = current_year + i

            if historical_years and len(yields) > 0:
                base_yield = yields[-1] if yields else 16000
                predicted_yield = base_yield + (trend * (i + 1))
            else:
                # System capacity based estimation
                system_capacity = 10.5  # kWp
                predicted_yield = system_capacity * 1500 * (1 + i * 0.02)  # 2% improvement per year

            # Add some realistic variation
            confidence = max(60, 90 - i * 5)  # Decreasing confidence over time

            annual_predictions.append({
                "year": year,
                "predicted_total_yield": round(max(0, predicted_yield), 1),
                "predicted_monthly_avg": round(predicted_yield / 12, 1),
                "predicted_daily_avg": round(predicted_yield / 365, 1),
                "confidence": confidence,
                "trend_factor": round(trend, 1)
            })

        conn.close()

        return {
            "system_id": system_id,
            "prediction_type": "annual",
            "historical_data": [dict(h) for h in historical_years],
            "predictions": annual_predictions,
            "trend_analysis": {
                "annual_trend": round(trend, 1),
                "trend_direction": "increasing" if trend > 0 else "decreasing" if trend < 0 else "stable"
            },
            "generated_at": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Annual prediction error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/analytics/efficiency-report")
async def efficiency_report():
    """Get system efficiency report"""
    
    conn = get_db_connection()
    if not conn:
        raise HTTPException(status_code=500, detail="Database connection failed")
    
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        
        # Get efficiency metrics
        now = datetime.now()
        today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        
        # Today's peak performance
        cur.execute("""
            SELECT 
                MAX(ac_power) as peak_power_system1,
                MAX(yield_today) as total_yield_system1,
                AVG(soc) as avg_soc_system1
            FROM solax_data 
            WHERE timestamp >= %s
        """, (today_start,))
        
        system1_today = cur.fetchone()
        
        cur.execute("""
            SELECT 
                MAX(ac_power) as peak_power_system2,
                MAX(yield_today) as total_yield_system2,
                AVG(soc) as avg_soc_system2
            FROM solax_data2 
            WHERE timestamp >= %s
        """, (today_start,))
        
        system2_today = cur.fetchone()
        
        # Theoretical maximum (10.5kW per system)
        theoretical_max_power = 10500  # Watts
        theoretical_daily_max = 80  # kWh (rough estimate)
        
        # Calculate efficiencies
        power_efficiency_s1 = (system1_today['peak_power_system1'] or 0) / theoretical_max_power * 100
        power_efficiency_s2 = (system2_today['peak_power_system2'] or 0) / theoretical_max_power * 100
        
        yield_efficiency_s1 = (system1_today['total_yield_system1'] or 0) / theoretical_daily_max * 100
        yield_efficiency_s2 = (system2_today['total_yield_system2'] or 0) / theoretical_daily_max * 100
        
        conn.close()
        
        return {
            "report_date": now.isoformat(),
            "system1": {
                "peak_power": system1_today['peak_power_system1'] or 0,
                "total_yield": system1_today['total_yield_system1'] or 0,
                "avg_soc": round(system1_today['avg_soc_system1'] or 0, 1),
                "power_efficiency": round(power_efficiency_s1, 1),
                "yield_efficiency": round(yield_efficiency_s1, 1)
            },
            "system2": {
                "peak_power": system2_today['peak_power_system2'] or 0,
                "total_yield": system2_today['total_yield_system2'] or 0,
                "avg_soc": round(system2_today['avg_soc_system2'] or 0, 1),
                "power_efficiency": round(power_efficiency_s2, 1),
                "yield_efficiency": round(yield_efficiency_s2, 1)
            },
            "combined": {
                "total_peak_power": (system1_today['peak_power_system1'] or 0) + (system2_today['peak_power_system2'] or 0),
                "total_yield": (system1_today['total_yield_system1'] or 0) + (system2_today['total_yield_system2'] or 0),
                "overall_efficiency": round((power_efficiency_s1 + power_efficiency_s2) / 2, 1)
            },
            "benchmarks": {
                "theoretical_max_power_per_system": theoretical_max_power,
                "theoretical_daily_yield_per_system": theoretical_daily_max,
                "combined_theoretical_power": theoretical_max_power * 2,
                "combined_theoretical_yield": theoretical_daily_max * 2
            }
        }
        
    except Exception as e:
        conn.close()
        logger.error(f"Efficiency report error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "service": "Solar Advanced Analytics API",
        "version": "1.0.0",
        "status": "running",
        "endpoints": {
            "train_model": "/analytics/train-model/{system_id}",
            "predict": "/analytics/predict/{system_id}",
            "performance_analysis": "/analytics/performance-analysis",
            "efficiency_report": "/analytics/efficiency-report",
            "health": "/health"
        },
        "models_loaded": len(analytics.models),
        "ml_capabilities": [
            "RandomForest predictions",
            "Feature engineering",
            "Performance analysis",
            "Efficiency reporting",
            "Trend analysis"
        ]
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "Solar Advanced Analytics API",
        "timestamp": datetime.now().isoformat(),
        "models_loaded": len(analytics.models)
    }

if __name__ == "__main__":
    print("🧠 Starting Solar Advanced Analytics API on port 8105...")
    uvicorn.run(app, host="0.0.0.0", port=8105)
