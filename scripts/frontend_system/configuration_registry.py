#!/usr/bin/env python3
"""
Central Configuration Registry for Solar Prediction System
Manages all system configurations in PostgreSQL database
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import psycopg2
from psycopg2.extras import RealDictCursor
import json
from datetime import datetime
from typing import Dict, List, Any, Optional
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

class ConfigurationRegistry:
    """Central configuration management system"""
    
    def __init__(self):
        self.db_config = DB_CONFIG
        self.init_config_table()
    
    def get_db_connection(self):
        """Get database connection"""
        try:
            return psycopg2.connect(**self.db_config)
        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            return None
    
    def init_config_table(self):
        """Initialize configuration table if it doesn't exist"""
        conn = self.get_db_connection()
        if not conn:
            return False
        
        try:
            cur = conn.cursor()
            
            # Create configuration table
            cur.execute("""
                CREATE TABLE IF NOT EXISTS system_configuration (
                    id SERIAL PRIMARY KEY,
                    config_key VARCHAR(255) UNIQUE NOT NULL,
                    config_value JSONB NOT NULL,
                    description TEXT,
                    category VARCHAR(100),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT TRUE
                )
            """)
            
            # Create index for faster lookups
            cur.execute("""
                CREATE INDEX IF NOT EXISTS idx_config_key 
                ON system_configuration(config_key)
            """)
            
            cur.execute("""
                CREATE INDEX IF NOT EXISTS idx_config_category 
                ON system_configuration(category)
            """)
            
            conn.commit()
            logger.info("✅ Configuration table initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize config table: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()
    
    def set_config(self, key: str, value: Any, description: str = "", category: str = "general") -> bool:
        """Set configuration value"""
        conn = self.get_db_connection()
        if not conn:
            return False
        
        try:
            cur = conn.cursor()
            
            # Convert value to JSON string
            if isinstance(value, str):
                json_value = json.dumps(value)
            else:
                json_value = json.dumps(value)
            
            # Upsert configuration
            cur.execute("""
                INSERT INTO system_configuration (config_key, config_value, description, category, updated_at)
                VALUES (%s, %s, %s, %s, CURRENT_TIMESTAMP)
                ON CONFLICT (config_key) 
                DO UPDATE SET 
                    config_value = EXCLUDED.config_value,
                    description = EXCLUDED.description,
                    category = EXCLUDED.category,
                    updated_at = CURRENT_TIMESTAMP
            """, (key, json_value, description, category))
            
            conn.commit()
            logger.info(f"✅ Configuration set: {key} = {value}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to set config {key}: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()
    
    def get_config(self, key: str, default: Any = None) -> Any:
        """Get configuration value"""
        conn = self.get_db_connection()
        if not conn:
            return default
        
        try:
            cur = conn.cursor(cursor_factory=RealDictCursor)
            
            cur.execute("""
                SELECT config_value FROM system_configuration 
                WHERE config_key = %s AND is_active = TRUE
            """, (key,))
            
            result = cur.fetchone()
            
            if result:
                # PostgreSQL JSONB returns already parsed objects
                return result['config_value']
            else:
                return default
                
        except Exception as e:
            logger.error(f"Failed to get config {key}: {e}")
            return default
        finally:
            conn.close()
    
    def get_category_configs(self, category: str) -> Dict[str, Any]:
        """Get all configurations for a category"""
        conn = self.get_db_connection()
        if not conn:
            return {}
        
        try:
            cur = conn.cursor(cursor_factory=RealDictCursor)
            
            cur.execute("""
                SELECT config_key, config_value, description 
                FROM system_configuration 
                WHERE category = %s AND is_active = TRUE
                ORDER BY config_key
            """, (category,))
            
            results = cur.fetchall()
            
            configs = {}
            for row in results:
                configs[row['config_key']] = {
                    'value': row['config_value'],  # JSONB already parsed
                    'description': row['description']
                }
            
            return configs
            
        except Exception as e:
            logger.error(f"Failed to get category configs {category}: {e}")
            return {}
        finally:
            conn.close()
    
    def list_all_configs(self) -> Dict[str, Any]:
        """List all configurations"""
        conn = self.get_db_connection()
        if not conn:
            return {}
        
        try:
            cur = conn.cursor(cursor_factory=RealDictCursor)
            
            cur.execute("""
                SELECT config_key, config_value, description, category, updated_at
                FROM system_configuration 
                WHERE is_active = TRUE
                ORDER BY category, config_key
            """)
            
            results = cur.fetchall()
            
            configs = {}
            for row in results:
                configs[row['config_key']] = {
                    'value': row['config_value'],  # JSONB already parsed
                    'description': row['description'],
                    'category': row['category'],
                    'updated_at': str(row['updated_at'])
                }
            
            return configs
            
        except Exception as e:
            logger.error(f"Failed to list all configs: {e}")
            return {}
        finally:
            conn.close()
    
    def delete_config(self, key: str) -> bool:
        """Delete configuration (soft delete)"""
        conn = self.get_db_connection()
        if not conn:
            return False
        
        try:
            cur = conn.cursor()
            
            cur.execute("""
                UPDATE system_configuration 
                SET is_active = FALSE, updated_at = CURRENT_TIMESTAMP
                WHERE config_key = %s
            """, (key,))
            
            conn.commit()
            logger.info(f"✅ Configuration deleted: {key}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete config {key}: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()
    
    def initialize_default_configs(self):
        """Initialize default system configurations"""
        
        default_configs = {
            # Geographic Configuration
            'geographic.latitude': {
                'value': 38.141348,
                'description': 'System latitude for solar calculations',
                'category': 'geographic'
            },
            'geographic.longitude': {
                'value': 24.007165,
                'description': 'System longitude for solar calculations',
                'category': 'geographic'
            },
            'geographic.timezone': {
                'value': 'Europe/Athens',
                'description': 'System timezone',
                'category': 'geographic'
            },
            'geographic.location_name': {
                'value': 'Marathon, Attica, Greece',
                'description': 'Human-readable location name',
                'category': 'geographic'
            },
            
            # Solar Systems Configuration
            'systems.system1.name': {
                'value': 'Σπίτι Πάνω',
                'description': 'System 1 display name',
                'category': 'systems'
            },
            'systems.system1.table': {
                'value': 'solax_data',
                'description': 'System 1 database table',
                'category': 'systems'
            },
            'systems.system1.wifi_sn': {
                'value': 'SRFQDPDN9W',
                'description': 'System 1 WiFi serial number',
                'category': 'systems'
            },
            'systems.system1.capacity_kw': {
                'value': 10.5,
                'description': 'System 1 peak capacity in kW',
                'category': 'systems'
            },
            'systems.system2.name': {
                'value': 'Σπίτι Κάτω',
                'description': 'System 2 display name',
                'category': 'systems'
            },
            'systems.system2.table': {
                'value': 'solax_data2',
                'description': 'System 2 database table',
                'category': 'systems'
            },
            'systems.system2.wifi_sn': {
                'value': 'SRCV9TUD6S',
                'description': 'System 2 WiFi serial number',
                'category': 'systems'
            },
            'systems.system2.capacity_kw': {
                'value': 10.5,
                'description': 'System 2 peak capacity in kW',
                'category': 'systems'
            },
            
            # API Configuration
            'api.solax.token_id': {
                'value': '20250410220826567911082',
                'description': 'SolaX Cloud API token',
                'category': 'api'
            },
            'api.solax.url': {
                'value': 'https://www.solaxcloud.com:9443/proxy/api/getRealtimeInfo.do',
                'description': 'SolaX Cloud API URL',
                'category': 'api'
            },
            'api.weather.provider': {
                'value': 'open-meteo',
                'description': 'Weather data provider',
                'category': 'api'
            },
            'api.weather.url': {
                'value': 'https://api.open-meteo.com/v1/forecast',
                'description': 'Weather API URL',
                'category': 'api'
            },
            
            # Database Configuration
            'database.host': {
                'value': 'localhost',
                'description': 'Database host',
                'category': 'database'
            },
            'database.name': {
                'value': 'solar_prediction',
                'description': 'Database name',
                'category': 'database'
            },
            'database.user': {
                'value': 'postgres',
                'description': 'Database user',
                'category': 'database'
            },
            
            # Telegram Configuration
            'telegram.bot_token': {
                'value': '**********:AAFBhGADTAAcUkbom_FEFSkOHoT5N6t5png',
                'description': 'Telegram bot token',
                'category': 'telegram'
            },
            'telegram.chat_id': {
                'value': '1510889515',
                'description': 'Telegram chat ID for alerts',
                'category': 'telegram'
            },
            
            # ML Model Configuration
            'ml.model_type': {
                'value': 'Optimized Production Hourly Model',
                'description': 'Current ML model type',
                'category': 'ml'
            },
            'ml.accuracy': {
                'value': 90.7,
                'description': 'Model accuracy percentage',
                'category': 'ml'
            },
            'ml.algorithm': {
                'value': 'Random Forest',
                'description': 'ML algorithm used',
                'category': 'ml'
            },
            'ml.features_count': {
                'value': 4,
                'description': 'Number of features used',
                'category': 'ml'
            },
            
            # Monitoring Configuration
            'monitoring.data_collection_interval': {
                'value': 30,
                'description': 'Data collection interval in seconds',
                'category': 'monitoring'
            },
            'monitoring.weather_update_interval': {
                'value': 3600,
                'description': 'Weather update interval in seconds',
                'category': 'monitoring'
            },
            'monitoring.alert_thresholds.low_soc': {
                'value': 20,
                'description': 'Low SOC alert threshold percentage',
                'category': 'monitoring'
            },
            'monitoring.alert_thresholds.high_temperature': {
                'value': 60,
                'description': 'High temperature alert threshold in Celsius',
                'category': 'monitoring'
            }
        }
        
        success_count = 0
        for key, config in default_configs.items():
            if self.set_config(key, config['value'], config['description'], config['category']):
                success_count += 1
        
        logger.info(f"✅ Initialized {success_count}/{len(default_configs)} default configurations")
        return success_count == len(default_configs)

def main():
    """Main function for testing configuration registry"""
    
    print("🔧 CENTRAL CONFIGURATION REGISTRY")
    print("="*60)
    print("🗄️ Initializing configuration management system...")
    print()
    
    try:
        config_registry = ConfigurationRegistry()
        
        # Initialize default configurations
        print("📋 Setting up default configurations...")
        config_registry.initialize_default_configs()
        
        # Test configuration operations
        print("\n🧪 Testing configuration operations...")
        
        # Get geographic configuration
        latitude = config_registry.get_config('geographic.latitude')
        longitude = config_registry.get_config('geographic.longitude')
        print(f"📍 Location: {latitude}, {longitude}")
        
        # Get system configurations
        system1_name = config_registry.get_config('systems.system1.name')
        system2_name = config_registry.get_config('systems.system2.name')
        print(f"🏠 Systems: {system1_name}, {system2_name}")
        
        # Get all configurations by category
        print(f"\n📊 Geographic Configurations:")
        geo_configs = config_registry.get_category_configs('geographic')
        for key, config in geo_configs.items():
            print(f"   {key}: {config['value']} - {config['description']}")
        
        print(f"\n🏠 System Configurations:")
        system_configs = config_registry.get_category_configs('systems')
        for key, config in system_configs.items():
            print(f"   {key}: {config['value']} - {config['description']}")
        
        # List all configurations
        all_configs = config_registry.list_all_configs()
        print(f"\n📋 Total Configurations: {len(all_configs)}")
        
        categories = set(config['category'] for config in all_configs.values())
        print(f"📂 Categories: {', '.join(sorted(categories))}")
        
        print("\n✅ Configuration registry is working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Configuration registry failed: {e}")
        logger.exception("Configuration registry failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
