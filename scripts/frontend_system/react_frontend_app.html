<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Solar Prediction Dashboard</title>
    
    <!-- React and dependencies -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    
    <!-- Chart.js for visualizations -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    
    <!-- Leaflet for maps -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    
    <!-- Material Design Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .app-container {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 280px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            overflow-y: auto;
        }
        
        .main-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .logo {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
            font-size: 24px;
            font-weight: 700;
            color: #4facfe;
        }
        
        .logo .material-icons {
            font-size: 32px;
            margin-right: 10px;
        }
        
        .nav-menu {
            list-style: none;
        }
        
        .nav-item {
            margin-bottom: 5px;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            text-decoration: none;
            color: #666;
            border-radius: 10px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .nav-link:hover, .nav-link.active {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            transform: translateX(5px);
        }
        
        .nav-link .material-icons {
            margin-right: 12px;
            font-size: 20px;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            justify-content: between;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .card-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            display: flex;
            align-items: center;
        }
        
        .card-title .material-icons {
            margin-right: 10px;
            color: #4facfe;
        }
        
        .grid {
            display: grid;
            gap: 20px;
        }
        
        .grid-2 {
            grid-template-columns: 1fr 1fr;
        }
        
        .grid-3 {
            grid-template-columns: 1fr 1fr 1fr;
        }
        
        .grid-4 {
            grid-template-columns: repeat(4, 1fr);
        }
        
        .metric-card {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
        }
        
        .metric-value {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .metric-label {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }
        
        .map-container {
            height: 300px;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            text-decoration: none;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        }
        
        .btn .material-icons {
            margin-right: 8px;
            font-size: 18px;
        }
        
        .status-indicator {
            display: inline-flex;
            align-items: center;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .status-online {
            background: #e8f5e8;
            color: #2e7d32;
        }
        
        .status-warning {
            background: #fff3e0;
            color: #f57c00;
        }
        
        .status-error {
            background: #ffebee;
            color: #d32f2f;
        }
        
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px;
            color: #666;
        }
        
        .loading .material-icons {
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        .alert-info {
            background: #e3f2fd;
            color: #1976d2;
            border-left: 4px solid #2196f3;
        }
        
        .alert-warning {
            background: #fff3e0;
            color: #f57c00;
            border-left: 4px solid #ff9800;
        }
        
        .alert-error {
            background: #ffebee;
            color: #d32f2f;
            border-left: 4px solid #f44336;
        }
        
        .alert .material-icons {
            margin-right: 10px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        
        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }
        
        .form-select {
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 12px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .table th {
            background: #f5f5f5;
            font-weight: 600;
            color: #333;
        }
        
        .table tbody tr:hover {
            background: #f9f9f9;
        }
        
        @media (max-width: 768px) {
            .app-container {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                height: auto;
            }
            
            .grid-2,
            .grid-3,
            .grid-4 {
                grid-template-columns: 1fr;
            }
            
            .chart-container {
                height: 300px;
            }
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect, useRef } = React;

        // Configuration service
        class ConfigService {
            constructor() {
                this.baseUrl = 'http://localhost:8002';
                this.cache = new Map();
            }

            async getConfig(key) {
                if (this.cache.has(key)) {
                    return this.cache.get(key);
                }

                try {
                    const response = await fetch(`${this.baseUrl}/config/${key}`);
                    if (response.ok) {
                        const config = await response.json();
                        this.cache.set(key, config.value);
                        return config.value;
                    }
                } catch (error) {
                    console.error(`Failed to get config ${key}:`, error);
                }
                return null;
            }

            async getAllConfigs(category = null) {
                try {
                    const url = category ? `${this.baseUrl}/config?category=${category}` : `${this.baseUrl}/config`;
                    const response = await fetch(url);
                    if (response.ok) {
                        return await response.json();
                    }
                } catch (error) {
                    console.error('Failed to get configs:', error);
                }
                return [];
            }

            async updateConfig(key, value, reason = null) {
                try {
                    const response = await fetch(`${this.baseUrl}/config/${key}`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ value, reason }),
                    });
                    if (response.ok) {
                        this.cache.set(key, value);
                        return await response.json();
                    }
                } catch (error) {
                    console.error(`Failed to update config ${key}:`, error);
                }
                return null;
            }
        }

        // API service
        class ApiService {
            constructor() {
                this.configService = new ConfigService();
                this.baseUrl = null;
                this.init();
            }

            async init() {
                this.baseUrl = await this.configService.getConfig('api.base_url') || 'http://localhost:8100';
            }

            async request(endpoint, options = {}) {
                if (!this.baseUrl) await this.init();

                try {
                    const response = await fetch(`${this.baseUrl}${endpoint}`, {
                        headers: {
                            'Content-Type': 'application/json',
                            ...options.headers,
                        },
                        ...options,
                    });

                    if (response.ok) {
                        return await response.json();
                    } else {
                        throw new Error(`API request failed: ${response.status}`);
                    }
                } catch (error) {
                    console.error(`API request failed for ${endpoint}:`, error);
                    throw error;
                }
            }

            async getHealth() {
                return this.request('/health');
            }

            async getModelInfo() {
                return this.request('/api/v1/model/info');
            }

            async getLatestData(systemId = null) {
                const endpoint = systemId ? `/api/v1/data/solax/latest?system_id=${systemId}` : '/api/v1/data/solax/latest';
                return this.request(endpoint);
            }

            async getWeatherData() {
                return this.request('/api/v1/data/weather/latest');
            }

            async getSolarSystems() {
                return this.request('/systems');
            }

            async getSystemStats(systemId) {
                return this.request(`/api/v1/stats/system/${systemId}`);
            }

            async getSystemHistory(systemId, days = 7) {
                return this.request(`/api/v1/data/history/${systemId}?days=${days}`);
            }

            async makePrediction(data, systemId = null) {
                const endpoint = systemId ? `/api/v1/predict/${systemId}` : '/api/v1/predict';
                return this.request(endpoint, {
                    method: 'POST',
                    body: JSON.stringify(data),
                });
            }

            async getProductionMLForecast(systemId, days = 7) {
                return this.request(`/api/v1/forecast/production/${systemId}?days=${days}`);
            }

            async getEnsemblePrediction(systemId, horizon = 'daily') {
                return this.request(`/api/v1/predict/ensemble/${systemId}?horizon=${horizon}`);
            }

            async getModelPerformance(systemId) {
                return this.request(`/api/v1/model/performance/${systemId}`);
            }

            async getSystemComparison() {
                return this.request('/api/v1/stats/comparison');
            }
        }

        // Global services
        const configService = new ConfigService();
        const apiService = new ApiService();

        // Dashboard component
        function Dashboard() {
            const [data, setData] = useState({
                health: null,
                modelInfo: null,
                systems: [],
                systemsData: {},
                weatherData: null,
                predictions: {},
                comparison: null,
            });
            const [selectedSystem, setSelectedSystem] = useState('all');
            const [loading, setLoading] = useState(true);
            const [error, setError] = useState(null);

            useEffect(() => {
                loadDashboardData();
                const interval = setInterval(loadDashboardData, 30000); // Refresh every 30 seconds
                return () => clearInterval(interval);
            }, []);

            const loadDashboardData = async () => {
                try {
                    setLoading(true);
                    setError(null);

                    // Load basic system info
                    const [health, modelInfo, systems, weatherData, comparison] = await Promise.all([
                        apiService.getHealth(),
                        apiService.getModelInfo(),
                        apiService.getSolarSystems(),
                        apiService.getWeatherData(),
                        apiService.getSystemComparison(),
                    ]);

                    // Load data for each system
                    const systemsData = {};
                    const predictions = {};

                    for (const system of systems) {
                        try {
                            const [latestData, stats, forecast] = await Promise.all([
                                apiService.getLatestData(system.system_name),
                                apiService.getSystemStats(system.system_name),
                                apiService.getProductionMLForecast(system.system_name, 1),
                            ]);

                            systemsData[system.system_name] = {
                                latest: latestData,
                                stats: stats,
                                system: system,
                            };

                            predictions[system.system_name] = forecast;
                        } catch (err) {
                            console.error(`Failed to load data for system ${system.system_name}:`, err);
                        }
                    }

                    setData({
                        health,
                        modelInfo,
                        systems,
                        systemsData,
                        weatherData,
                        predictions,
                        comparison,
                    });
                } catch (err) {
                    setError(err.message);
                } finally {
                    setLoading(false);
                }
            };

            if (loading && !data.health) {
                return (
                    <div className="loading">
                        <span className="material-icons">refresh</span>
                        Loading dashboard data...
                    </div>
                );
            }

            if (error) {
                return (
                    <div className="alert alert-error">
                        <span className="material-icons">error</span>
                        Error loading dashboard: {error}
                    </div>
                );
            }

            // Calculate aggregated metrics
            const getAggregatedMetrics = () => {
                if (selectedSystem === 'all') {
                    const totalYield = Object.values(data.systemsData).reduce(
                        (sum, system) => sum + (system.latest?.yield_today || 0), 0
                    );
                    const totalPower = Object.values(data.systemsData).reduce(
                        (sum, system) => sum + (system.latest?.ac_power || 0), 0
                    );
                    const avgSOC = Object.values(data.systemsData).reduce(
                        (sum, system) => sum + (system.latest?.soc || 0), 0
                    ) / Object.keys(data.systemsData).length;

                    return {
                        yield_today: totalYield,
                        ac_power: totalPower,
                        soc: avgSOC,
                        system_count: Object.keys(data.systemsData).length
                    };
                } else {
                    const systemData = data.systemsData[selectedSystem];
                    return systemData?.latest || {};
                }
            };

            const metrics = getAggregatedMetrics();

            return (
                <div>
                    {/* System Selector */}
                    <div className="card">
                        <div className="card-header">
                            <h3 className="card-title">
                                <span className="material-icons">solar_power</span>
                                Solar System Dashboard
                            </h3>
                        </div>
                        <div className="form-group">
                            <label className="form-label">Select System:</label>
                            <select
                                className="form-control form-select"
                                value={selectedSystem}
                                onChange={(e) => setSelectedSystem(e.target.value)}
                            >
                                <option value="all">All Systems (Combined)</option>
                                {data.systems.map(system => (
                                    <option key={system.system_name} value={system.system_name}>
                                        {system.display_name} ({system.system_name})
                                    </option>
                                ))}
                            </select>
                        </div>
                    </div>

                    {/* KPI Cards */}
                    <div className="grid grid-4">
                        <div className="metric-card">
                            <div className="metric-value">
                                {metrics.yield_today?.toFixed(1) || '0.0'}
                            </div>
                            <div className="metric-label">
                                {selectedSystem === 'all' ? 'Total Yield Today (kWh)' : 'Today\'s Yield (kWh)'}
                            </div>
                        </div>
                        <div className="metric-card">
                            <div className="metric-value">
                                {metrics.ac_power?.toFixed(0) || '0'}
                            </div>
                            <div className="metric-label">
                                {selectedSystem === 'all' ? 'Total Power (W)' : 'Current Power (W)'}
                            </div>
                        </div>
                        <div className="metric-card">
                            <div className="metric-value">
                                {metrics.soc?.toFixed(0) || '0'}%
                            </div>
                            <div className="metric-label">
                                {selectedSystem === 'all' ? 'Average SOC' : 'Battery SOC'}
                            </div>
                        </div>
                        <div className="metric-card">
                            <div className="metric-value">
                                {selectedSystem === 'all' ? metrics.system_count : data.modelInfo?.accuracy?.toFixed(1) || '0.0'}
                            </div>
                            <div className="metric-label">
                                {selectedSystem === 'all' ? 'Active Systems' : 'Model Accuracy (%)'}
                            </div>
                        </div>
                    </div>

                    {/* System Status */}
                    <div className="card">
                        <div className="card-header">
                            <h3 className="card-title">
                                <span className="material-icons">dashboard</span>
                                System Status
                            </h3>
                        </div>
                        <div className="grid grid-2">
                            <div>
                                <h4>API Health</h4>
                                <div className={`status-indicator ${data.health?.status === 'healthy' ? 'status-online' : 'status-error'}`}>
                                    {data.health?.status || 'Unknown'}
                                </div>
                            </div>
                            <div>
                                <h4>Model Status</h4>
                                <div className="status-indicator status-online">
                                    {data.modelInfo?.model_name || 'Unknown'}
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Weather Information */}
                    {data.weatherData && (
                        <div className="card">
                            <div className="card-header">
                                <h3 className="card-title">
                                    <span className="material-icons">wb_sunny</span>
                                    Weather Conditions
                                </h3>
                            </div>
                            <div className="grid grid-3">
                                <div>
                                    <strong>Temperature:</strong> {data.weatherData.temperature_2m}°C
                                </div>
                                <div>
                                    <strong>Cloud Cover:</strong> {data.weatherData.cloud_cover}%
                                </div>
                                <div>
                                    <strong>GHI:</strong> {data.weatherData.global_horizontal_irradiance} W/m²
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            );
        }

        // Settings component
        function Settings() {
            const [configs, setConfigs] = useState([]);
            const [loading, setLoading] = useState(true);
            const [selectedCategory, setSelectedCategory] = useState('all');
            const [editingConfig, setEditingConfig] = useState(null);

            useEffect(() => {
                loadConfigs();
            }, [selectedCategory]);

            const loadConfigs = async () => {
                try {
                    setLoading(true);
                    const category = selectedCategory === 'all' ? null : selectedCategory;
                    const configList = await configService.getAllConfigs(category);
                    setConfigs(configList);
                } catch (error) {
                    console.error('Failed to load configs:', error);
                } finally {
                    setLoading(false);
                }
            };

            const handleUpdateConfig = async (key, value) => {
                try {
                    await configService.updateConfig(key, value, 'Updated via UI');
                    setEditingConfig(null);
                    loadConfigs();
                } catch (error) {
                    console.error('Failed to update config:', error);
                }
            };

            const categories = [...new Set(configs.map(c => c.category))];

            return (
                <div>
                    <div className="card">
                        <div className="card-header">
                            <h3 className="card-title">
                                <span className="material-icons">settings</span>
                                System Configuration
                            </h3>
                        </div>

                        <div className="form-group">
                            <label className="form-label">Filter by Category:</label>
                            <select 
                                className="form-control form-select"
                                value={selectedCategory}
                                onChange={(e) => setSelectedCategory(e.target.value)}
                            >
                                <option value="all">All Categories</option>
                                {categories.map(cat => (
                                    <option key={cat} value={cat}>{cat}</option>
                                ))}
                            </select>
                        </div>

                        {loading ? (
                            <div className="loading">
                                <span className="material-icons">refresh</span>
                                Loading configurations...
                            </div>
                        ) : (
                            <table className="table">
                                <thead>
                                    <tr>
                                        <th>Key</th>
                                        <th>Value</th>
                                        <th>Category</th>
                                        <th>Description</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {configs.map(config => (
                                        <tr key={config.id}>
                                            <td><code>{config.key}</code></td>
                                            <td>
                                                {editingConfig === config.id ? (
                                                    <input
                                                        type="text"
                                                        className="form-control"
                                                        defaultValue={JSON.stringify(config.value)}
                                                        onBlur={(e) => {
                                                            try {
                                                                const newValue = JSON.parse(e.target.value);
                                                                handleUpdateConfig(config.key, newValue);
                                                            } catch (err) {
                                                                handleUpdateConfig(config.key, e.target.value);
                                                            }
                                                        }}
                                                        onKeyPress={(e) => {
                                                            if (e.key === 'Enter') {
                                                                e.target.blur();
                                                            }
                                                        }}
                                                    />
                                                ) : (
                                                    <span onClick={() => !config.is_readonly && setEditingConfig(config.id)}>
                                                        {JSON.stringify(config.value)}
                                                    </span>
                                                )}
                                            </td>
                                            <td>{config.category}</td>
                                            <td>{config.description}</td>
                                            <td>
                                                {!config.is_readonly && (
                                                    <button
                                                        className="btn btn-primary"
                                                        onClick={() => setEditingConfig(config.id)}
                                                    >
                                                        <span className="material-icons">edit</span>
                                                    </button>
                                                )}
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        )}
                    </div>
                </div>
            );
        }

        // Main App component
        function App() {
            const [currentView, setCurrentView] = useState('dashboard');

            const menuItems = [
                { id: 'dashboard', label: 'Dashboard', icon: 'dashboard' },
                { id: 'predictions', label: 'Predictions', icon: 'trending_up' },
                { id: 'history', label: 'History', icon: 'history' },
                { id: 'settings', label: 'Settings', icon: 'settings' },
                { id: 'monitoring', label: 'Monitoring', icon: 'monitor_heart' },
            ];

            const renderContent = () => {
                switch (currentView) {
                    case 'dashboard':
                        return <Dashboard />;
                    case 'settings':
                        return <Settings />;
                    default:
                        return (
                            <div className="card">
                                <div className="card-header">
                                    <h3 className="card-title">
                                        <span className="material-icons">construction</span>
                                        {menuItems.find(item => item.id === currentView)?.label || 'Page'}
                                    </h3>
                                </div>
                                <div className="alert alert-info">
                                    <span className="material-icons">info</span>
                                    This section is under development.
                                </div>
                            </div>
                        );
                }
            };

            return (
                <div className="app-container">
                    <div className="sidebar">
                        <div className="logo">
                            <span className="material-icons">wb_sunny</span>
                            Solar Dashboard
                        </div>
                        <nav>
                            <ul className="nav-menu">
                                {menuItems.map(item => (
                                    <li key={item.id} className="nav-item">
                                        <a
                                            className={`nav-link ${currentView === item.id ? 'active' : ''}`}
                                            onClick={() => setCurrentView(item.id)}
                                        >
                                            <span className="material-icons">{item.icon}</span>
                                            {item.label}
                                        </a>
                                    </li>
                                ))}
                            </ul>
                        </nav>
                    </div>
                    <div className="main-content">
                        {renderContent()}
                    </div>
                </div>
            );
        }

        // Render the app
        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
