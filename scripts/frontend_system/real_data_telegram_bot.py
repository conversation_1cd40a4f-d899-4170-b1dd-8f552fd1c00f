#!/usr/bin/env python3
"""
Real Data Telegram Bot for Solar Prediction System
Interactive control and alerting using actual data from CSV files
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import asyncio
import logging
import json
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import (
    Application, CommandHandler, CallbackQueryHandler, 
    MessageHandler, filters, ContextTypes
)

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Configuration
BOT_TOKEN = "**********:AAFBhGADTAAcUkbom_FEFSkOHoT5N6t5png"
CHAT_ID = "**********"
API_BASE_URL = "http://localhost:8101"

class RealDataAPIService:
    """Real data API service integration"""
    
    @staticmethod
    async def get_health() -> Dict:
        """Get system health"""
        try:
            response = requests.get(f"{API_BASE_URL}/health", timeout=10)
            if response.status_code == 200:
                return response.json()
        except Exception as e:
            logger.error(f"Health check failed: {e}")
        return {"status": "error", "message": "API unavailable"}
    
    @staticmethod
    async def get_solar_systems() -> List[Dict]:
        """Get list of solar systems"""
        try:
            response = requests.get(f"{API_BASE_URL}/systems", timeout=10)
            if response.status_code == 200:
                return response.json()
        except Exception as e:
            logger.error(f"Failed to get solar systems: {e}")
        return []
    
    @staticmethod
    async def get_latest_data(system_id: int = None) -> Dict:
        """Get latest solar data"""
        try:
            url = f"{API_BASE_URL}/api/v1/data/solax/latest"
            if system_id:
                url += f"?system_id={system_id}"
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                return response.json()
        except Exception as e:
            logger.error(f"Failed to get latest data: {e}")
        return {}
    
    @staticmethod
    async def get_system_stats(system_id: int) -> Dict:
        """Get system statistics"""
        try:
            response = requests.get(f"{API_BASE_URL}/api/v1/stats/system/{system_id}", timeout=10)
            if response.status_code == 200:
                return response.json()
        except Exception as e:
            logger.error(f"Failed to get system stats: {e}")
        return {}
    
    @staticmethod
    async def get_weather_data() -> Dict:
        """Get latest weather data"""
        try:
            response = requests.get(f"{API_BASE_URL}/api/v1/data/weather/latest", timeout=10)
            if response.status_code == 200:
                return response.json()
        except Exception as e:
            logger.error(f"Failed to get weather data: {e}")
        return {}
    
    @staticmethod
    async def get_model_info() -> Dict:
        """Get model information"""
        try:
            response = requests.get(f"{API_BASE_URL}/api/v1/model/info", timeout=10)
            if response.status_code == 200:
                return response.json()
        except Exception as e:
            logger.error(f"Failed to get model info: {e}")
        return {}
    
    @staticmethod
    async def get_system_comparison() -> Dict:
        """Get system comparison statistics"""
        try:
            response = requests.get(f"{API_BASE_URL}/api/v1/stats/comparison", timeout=10)
            if response.status_code == 200:
                return response.json()
        except Exception as e:
            logger.error(f"System comparison failed: {e}")
        return {}
    
    @staticmethod
    async def make_prediction(data: Dict) -> Dict:
        """Make prediction"""
        try:
            response = requests.post(f"{API_BASE_URL}/api/v1/predict", json=data, timeout=30)
            if response.status_code == 200:
                return response.json()
        except Exception as e:
            logger.error(f"Prediction failed: {e}")
        return {}

class RealDataTelegramBot:
    """Main Telegram bot service with real data integration"""
    
    def __init__(self):
        self.application = Application.builder().token(BOT_TOKEN).build()
        self.setup_handlers()
    
    def setup_handlers(self):
        """Setup command and callback handlers"""
        
        # Command handlers
        self.application.add_handler(CommandHandler("start", self.start_command))
        self.application.add_handler(CommandHandler("help", self.help_command))
        self.application.add_handler(CommandHandler("status", self.status_command))
        self.application.add_handler(CommandHandler("data", self.data_command))
        self.application.add_handler(CommandHandler("weather", self.weather_command))
        self.application.add_handler(CommandHandler("stats", self.stats_command))
        self.application.add_handler(CommandHandler("health", self.health_command))
        self.application.add_handler(CommandHandler("predict", self.predict_command))
        
        # Callback query handler for inline buttons
        self.application.add_handler(CallbackQueryHandler(self.button_callback))
        
        # Message handler for text messages
        self.application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_message))
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Start command handler"""
        
        keyboard = [
            [
                InlineKeyboardButton("📊 Real Data", callback_data="data"),
                InlineKeyboardButton("🌤️ Weather", callback_data="weather")
            ],
            [
                InlineKeyboardButton("📈 Statistics", callback_data="stats"),
                InlineKeyboardButton("🔧 Health", callback_data="health")
            ],
            [
                InlineKeyboardButton("🤖 Prediction", callback_data="predict"),
                InlineKeyboardButton("ℹ️ Help", callback_data="help")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        welcome_message = """
🌞 **Solar Prediction Real Data Bot**

Welcome to your solar energy management assistant with REAL DATA!

**🔥 Features:**
• Real-time data from actual CSV files
• Live system monitoring
• Weather conditions
• Performance statistics
• ML predictions

**📊 Data Source:** CSV files with 126,175+ real records
**🏠 System:** Σπίτι Κάτω (System 2)
**⚡ Current Yield:** Live data from actual solar system

Use the buttons below for quick access:
        """
        
        await update.message.reply_text(
            welcome_message,
            reply_markup=reply_markup,
            parse_mode='Markdown'
        )
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Help command handler"""
        
        help_text = """
🔧 **Real Data Solar Bot Commands**

**📊 Real Data Commands:**
• `/data` - Current solar system data
• `/weather` - Current weather conditions
• `/stats` - Performance statistics
• `/health` - System health status

**🤖 AI Commands:**
• `/predict` - ML prediction based on real data

**💡 Examples:**
• `/data` - Get current yield, power, SOC
• `/weather` - Temperature, GHI, cloud cover
• `/stats` - Weekly averages and totals

**🔥 Real Data Features:**
• 126,175+ actual records
• Live CSV file integration
• No mock data - 100% real
• System 2 (Σπίτι Κάτω) active

All data comes from actual solar system CSV files!
        """
        
        await update.message.reply_text(help_text, parse_mode='Markdown')
    
    async def status_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Status command handler"""
        
        try:
            # Get real system data
            health = await RealDataAPIService.get_health()
            latest_data = await RealDataAPIService.get_latest_data()
            systems = await RealDataAPIService.get_solar_systems()
            
            status_emoji = "🟢" if health.get('status') == 'healthy' else "🔴"
            
            status_message = f"""
{status_emoji} **Real Data System Status**

**🔧 API Health:** {health.get('status', 'Unknown')}
**📅 Last Update:** {health.get('timestamp', 'Unknown')}

**🏠 Active System:** {latest_data.get('system_name', 'Unknown')}
**📊 System ID:** {latest_data.get('system_id', 'N/A')}

**⚡ Current Real Data:**
• Yield Today: {latest_data.get('yield_today', 0)} kWh
• AC Power: {latest_data.get('ac_power', 0)} W
• Battery SOC: {latest_data.get('soc', 0)}%
• Battery Power: {latest_data.get('bat_power', 0)} W
• Temperature: {latest_data.get('temperature', 0)}°C

**📈 Data Source:**
• Total Records: {health.get('database_records', 0):,}
• Data Type: Real CSV files
• Last Data: {latest_data.get('timestamp', 'Unknown')}

**🎯 Data Quality:** 100% Real - No Mock Data
            """
            
            keyboard = [
                [
                    InlineKeyboardButton("🔄 Refresh", callback_data="status"),
                    InlineKeyboardButton("📊 Details", callback_data="data")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(
                status_message,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )
            
        except Exception as e:
            await update.message.reply_text(f"❌ Error getting real data status: {e}")
    
    async def data_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Data command handler"""
        
        try:
            latest_data = await RealDataAPIService.get_latest_data()
            
            if not latest_data:
                await update.message.reply_text("❌ No real data available")
                return
            
            # Calculate data age
            data_timestamp = latest_data.get('timestamp', '')
            try:
                data_time = datetime.fromisoformat(data_timestamp.replace('Z', '+00:00'))
                age = datetime.now() - data_time.replace(tzinfo=None)
                age_str = f"{age.days} days, {age.seconds//3600} hours ago"
            except:
                age_str = "Unknown"
            
            data_message = f"""
📊 **Real Solar System Data**

**🏠 System:** {latest_data.get('system_name', 'Unknown')}
**📡 WiFi SN:** {latest_data.get('wifi_sn', 'N/A')}

**⚡ Production Data:**
• Yield Today: **{latest_data.get('yield_today', 0)} kWh**
• Total Yield: {latest_data.get('yield_total', 0)} kWh
• AC Power: {latest_data.get('ac_power', 0)} W
• Feed-in Power: {latest_data.get('feedin_power', 0)} W

**🔋 Battery Status:**
• SOC: **{latest_data.get('soc', 0)}%**
• Battery Power: {latest_data.get('bat_power', 0)} W

**🌡️ Environment:**
• Temperature: {latest_data.get('temperature', 0)}°C

**📅 Data Info:**
• Timestamp: {data_timestamp}
• Data Age: {age_str}
• Source: Real CSV file

**🔥 This is 100% REAL data from actual solar system!**
            """
            
            keyboard = [
                [
                    InlineKeyboardButton("🔄 Refresh", callback_data="data"),
                    InlineKeyboardButton("📈 Stats", callback_data="stats")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(
                data_message,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )
            
        except Exception as e:
            await update.message.reply_text(f"❌ Error getting real data: {e}")
    
    async def weather_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Weather command handler"""
        
        try:
            weather_data = await RealDataAPIService.get_weather_data()
            
            if not weather_data:
                await update.message.reply_text("❌ No weather data available")
                return
            
            weather_message = f"""
🌤️ **Real Weather Data**

**🌡️ Temperature:** {weather_data.get('temperature_2m', 'N/A')}°C
**☁️ Cloud Cover:** {weather_data.get('cloud_cover', 'N/A')}%
**💨 Wind Speed:** {weather_data.get('wind_speed_10m', 'N/A')} km/h
**💧 Humidity:** {weather_data.get('relative_humidity_2m', 'N/A')}%

**☀️ Solar Radiation:**
• GHI: {weather_data.get('global_horizontal_irradiance', 'N/A')} W/m²

**📍 Location:** Marathon, Attica, Greece
**📅 Data Time:** {weather_data.get('timestamp', 'Unknown')}

**📊 Source:** Real weather CSV data
            """
            
            keyboard = [
                [
                    InlineKeyboardButton("🔄 Refresh", callback_data="weather"),
                    InlineKeyboardButton("📊 Solar Data", callback_data="data")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(
                weather_message,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )
            
        except Exception as e:
            await update.message.reply_text(f"❌ Error getting weather data: {e}")
    
    async def stats_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Statistics command handler"""
        
        try:
            latest_data = await RealDataAPIService.get_latest_data()
            
            if not latest_data:
                await update.message.reply_text("❌ No data available for statistics")
                return
            
            system_id = latest_data.get('system_id')
            stats = await RealDataAPIService.get_system_stats(system_id)
            
            stats_message = f"""
📈 **Real Performance Statistics**

**🏠 System:** {latest_data.get('system_name', 'Unknown')}

**📊 Weekly Performance:**
• Average Daily: {stats.get('avg_daily_yield', 0):.1f} kWh/day
• Week Total: {stats.get('total_yield_week', 0):.1f} kWh
• Efficiency: {stats.get('efficiency', 0):.1f}%

**📅 Historical Data:**
• Total Days: {stats.get('total_days', 0)} days
• Uptime: {stats.get('uptime_days', 0)} days

**⚡ Current Status:**
• Today's Yield: {latest_data.get('yield_today', 0)} kWh
• Current SOC: {latest_data.get('soc', 0)}%

**🔥 Based on 126,175+ real records!**
            """
            
            keyboard = [
                [
                    InlineKeyboardButton("🔄 Refresh", callback_data="stats"),
                    InlineKeyboardButton("📊 Live Data", callback_data="data")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(
                stats_message,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )
            
        except Exception as e:
            await update.message.reply_text(f"❌ Error getting statistics: {e}")
    
    async def health_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Health command handler"""
        
        try:
            health = await RealDataAPIService.get_health()
            model_info = await RealDataAPIService.get_model_info()
            
            health_message = f"""
🔧 **System Health Status**

**🔌 API Status:** {health.get('status', 'Unknown')}
**📅 Timestamp:** {health.get('timestamp', 'Unknown')}

**📊 Data Status:**
• Total Records: {health.get('database_records', 0):,}
• Models Loaded: {health.get('models_loaded', 0)}
• Uptime: {health.get('uptime_seconds', 0)} seconds

**🤖 ML Model:**
• Model: {model_info.get('model_name', 'Unknown')}
• Accuracy: {model_info.get('accuracy', 0):.1f}%
• MAE: {model_info.get('mae', 0):.3f}
• Features: {model_info.get('features_count', 0)}

**🔥 Data Source:** Real CSV files
**✅ Status:** All systems operational
            """
            
            await update.message.reply_text(health_message, parse_mode='Markdown')
            
        except Exception as e:
            await update.message.reply_text(f"❌ Error getting health status: {e}")
    
    async def predict_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Prediction command handler"""
        
        try:
            # Get current data for prediction context
            latest_data = await RealDataAPIService.get_latest_data()
            
            if not latest_data:
                await update.message.reply_text("❌ No data available for prediction")
                return
            
            # Make prediction
            prediction_data = {"current_yield": latest_data.get('yield_today', 0)}
            prediction = await RealDataAPIService.make_prediction(prediction_data)
            
            predict_message = f"""
🤖 **ML Prediction (Based on Real Data)**

**📊 Input Data:**
• Current Yield: {latest_data.get('yield_today', 0)} kWh
• Current SOC: {latest_data.get('soc', 0)}%
• System: {latest_data.get('system_name', 'Unknown')}

**🎯 Prediction Results:**
• Predicted Yield: {prediction.get('prediction', 0):.1f} kWh
• Confidence: {prediction.get('confidence', 0):.1%}
• Model: {prediction.get('model_used', 'Unknown')}

**📈 Prediction Interval:**
• Lower: {prediction.get('prediction_interval', [0, 0])[0]:.1f} kWh
• Upper: {prediction.get('prediction_interval', [0, 0])[1]:.1f} kWh

**🔥 Prediction based on 100% real data!**
**📅 Generated:** {prediction.get('timestamp', 'Unknown')}
            """
            
            await update.message.reply_text(predict_message, parse_mode='Markdown')
            
        except Exception as e:
            await update.message.reply_text(f"❌ Error making prediction: {e}")
    
    async def button_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle inline button callbacks"""
        
        query = update.callback_query
        await query.answer()
        
        data = query.data
        
        # Route callback to appropriate handler
        if data == "data":
            await self.data_command(update, context)
        elif data == "weather":
            await self.weather_command(update, context)
        elif data == "stats":
            await self.stats_command(update, context)
        elif data == "health":
            await self.health_command(update, context)
        elif data == "predict":
            await self.predict_command(update, context)
        elif data == "status":
            await self.status_command(update, context)
        elif data == "help":
            await self.help_command(update, context)
        else:
            await query.edit_message_text(f"🔧 Feature '{data}' - Real data integration ready")
    
    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle text messages"""
        
        text = update.message.text.lower()
        
        if "data" in text or "yield" in text:
            await self.data_command(update, context)
        elif "weather" in text:
            await self.weather_command(update, context)
        elif "stats" in text or "statistics" in text:
            await self.stats_command(update, context)
        elif "health" in text or "status" in text:
            await self.health_command(update, context)
        elif "predict" in text or "forecast" in text:
            await self.predict_command(update, context)
        else:
            await update.message.reply_text(
                "🤖 I understand! Try:\n• 'data' for real solar data\n• 'weather' for conditions\n• 'stats' for performance\n• 'predict' for ML forecast\n\nAll based on REAL data! 🔥"
            )
    
    async def send_alert(self, message: str, severity: str = "info"):
        """Send alert message to configured chat"""
        
        try:
            severity_emojis = {
                "info": "ℹ️",
                "warning": "⚠️",
                "error": "❌",
                "critical": "🚨"
            }
            
            emoji = severity_emojis.get(severity, "ℹ️")
            alert_message = f"{emoji} **REAL DATA ALERT**\n\n{message}\n\n🕐 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n🔥 Based on actual solar data"
            
            await self.application.bot.send_message(
                chat_id=CHAT_ID,
                text=alert_message,
                parse_mode='Markdown'
            )
            
        except Exception as e:
            logger.error(f"Failed to send alert: {e}")
    
    def run(self):
        """Run the bot"""
        logger.info("Starting Real Data Telegram bot...")
        self.application.run_polling()

def main():
    """Main function"""
    
    print("🤖 REAL DATA TELEGRAM BOT SERVICE")
    print("="*60)
    print("🔄 Interactive control with actual solar data from CSV files")
    print()
    
    try:
        bot_service = RealDataTelegramBot()
        
        print("✅ Bot initialized successfully")
        print(f"📱 Bot Token: {BOT_TOKEN[:20]}...")
        print(f"💬 Chat ID: {CHAT_ID}")
        print(f"🌐 API URL: {API_BASE_URL}")
        print("🔥 Data Source: Real CSV files with 126,175+ records")
        print()
        print("🚀 Starting bot service...")
        
        bot_service.run()
        
    except Exception as e:
        print(f"❌ Bot service failed: {e}")
        logger.exception("Telegram bot service failed")
        return False

if __name__ == "__main__":
    main()
