[Unit]
Description=Enhanced Solar Alert System v2.0 with Hourly Intelligence
After=network.target postgresql.service
Wants=postgresql.service

[Service]
Type=simple
User=grlv
Group=grlv
WorkingDirectory=/home/<USER>/solar-prediction-project
Environment=PYTHONPATH=/home/<USER>/solar-prediction-project
ExecStart=/usr/bin/python3 /home/<USER>/solar-prediction-project/scripts/frontend_system/alert_system_enhanced.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

# Environment variables
Environment=DB_HOST=localhost
Environment=DB_PORT=5433
Environment=DB_NAME=solar_prediction
Environment=DB_USER=postgres
Environment=DB_PASSWORD=postgres

[Install]
WantedBy=multi-user.target
