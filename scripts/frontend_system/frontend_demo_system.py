#!/usr/bin/env python3
"""
Frontend Demo System
Standalone demo of the React frontend and Telegram integration
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, FileResponse
from pydantic import BaseModel
from typing import Dict, List, Any, Optional
import json
from datetime import datetime, timedelta
import logging
import uvicorn

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# FastAPI app
app = FastAPI(
    title="Solar Prediction Frontend Demo",
    description="Demo system for React frontend and Telegram integration",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# In-memory configuration store for demo
CONFIG_STORE = {
    "system.name": "Solar Prediction System",
    "system.version": "2.0.0",
    "system.timezone": "Europe/Athens",
    "location.default_latitude": 38.141348,
    "location.default_longitude": 24.007165,
    "location.country_code": "GR",
    "api.base_url": "http://localhost:8100",
    "api.version": "v1",
    "api.timeout_seconds": 30,
    "weather.provider": "open-meteo",
    "weather.update_interval_minutes": 60,
    "weather.forecast_days": 7,
    "ml.default_model": "enhanced_model_v3",
    "ml.confidence_threshold": 0.7,
    "ml.retraining_threshold_mae": 2.5,
    "ui.theme": "dark",
    "ui.refresh_interval_seconds": 30,
    "ui.chart_colors": ["#4facfe", "#00f2fe", "#667eea", "#764ba2"],
    "telegram.bot_token": "**********************************************",
    "telegram.chat_id": "**********",
    "telegram.alerts_enabled": True,
    "alerts.enabled": True,
    "alerts.severity_levels": ["info", "warning", "error", "critical"],
    "alerts.escalation_timeout_minutes": 30
}

# Mock data for demo
MOCK_DATA = {
    "health": {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "database_records": 268395,
        "uptime_seconds": 86400
    },
    "model_info": {
        "model_name": "enhanced_model_v3",
        "accuracy": 94.3,
        "mae": 0.566,
        "r2_score": 0.89,
        "last_training": "2025-06-07T10:30:00",
        "features_count": 101,
        "confidence": 0.85
    },
    "systems": [
        {
            "id": 1,
            "system_name": "system_1",
            "display_name": "Σπίτι Πάνω",
            "latitude": 38.141348,
            "longitude": 24.007165,
            "peak_power_kw": 10.5,
            "panel_type": "Monocrystalline",
            "status": "active"
        },
        {
            "id": 2,
            "system_name": "system_2",
            "display_name": "Σπίτι Κάτω",
            "latitude": 38.141348,
            "longitude": 24.007165,
            "peak_power_kw": 10.5,
            "panel_type": "Monocrystalline",
            "status": "active"
        }
    ],
    "systems_data": {
        "system_1": {
            "timestamp": datetime.now().isoformat(),
            "yield_today": 45.7,
            "yield_total": 12847.3,
            "ac_power": 3250,
            "soc": 78,
            "bat_power": -1200,
            "temperature": 24.5
        },
        "system_2": {
            "timestamp": datetime.now().isoformat(),
            "yield_today": 52.3,
            "yield_total": 14235.8,
            "ac_power": 3850,
            "soc": 82,
            "bat_power": -1450,
            "temperature": 25.1
        }
    },
    "weather_data": {
        "timestamp": datetime.now().isoformat(),
        "temperature_2m": 24.5,
        "cloud_cover": 25,
        "global_horizontal_irradiance": 650,
        "direct_normal_irradiance": 750,
        "wind_speed_10m": 12,
        "relative_humidity_2m": 65
    },
    "forecasts": {
        "system_1": {
            "tomorrow_prediction": 48.5,
            "total_prediction": 325.2,
            "daily_average": 46.5,
            "confidence": 0.87
        },
        "system_2": {
            "tomorrow_prediction": 55.1,
            "total_prediction": 371.8,
            "daily_average": 53.1,
            "confidence": 0.89
        }
    },
    "comparison": {
        "systems": [
            {
                "system_id": "system_1",
                "today_yield": 45.7,
                "current_power": 3250,
                "soc": 78
            },
            {
                "system_id": "system_2",
                "today_yield": 52.3,
                "current_power": 3850,
                "soc": 82
            }
        ],
        "weekly_stats": [
            {
                "system_id": "system_1",
                "avg_daily_yield": 42.3,
                "total_weekly_yield": 296.1
            },
            {
                "system_id": "system_2",
                "avg_daily_yield": 48.7,
                "total_weekly_yield": 340.9
            }
        ],
        "model_performance": [
            {
                "system_id": "system_1",
                "accuracy": 94.1,
                "mae": 0.578,
                "confidence": 0.85
            },
            {
                "system_id": "system_2",
                "accuracy": 94.5,
                "mae": 0.554,
                "confidence": 0.87
            }
        ]
    }
}

class ConfigResponse(BaseModel):
    """Configuration response model"""
    id: int
    key: str
    value: Any
    type: str
    scope: str = "global"
    category: str
    description: Optional[str] = None
    is_sensitive: bool = False
    is_readonly: bool = False
    version: int = 1
    created_at: datetime
    updated_at: datetime
    created_by: str = "demo"
    updated_by: str = "demo"

class ConfigUpdate(BaseModel):
    """Configuration update model"""
    value: Any
    reason: Optional[str] = None

# API Endpoints

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Solar Prediction Frontend Demo",
        "version": "1.0.0",
        "endpoints": {
            "frontend": "/frontend",
            "config": "/config",
            "health": "/health",
            "api": "/api/v1"
        }
    }

@app.get("/frontend", response_class=HTMLResponse)
async def get_frontend():
    """Serve React frontend"""
    try:
        with open('scripts/frontend_system/react_frontend_app.html', 'r') as f:
            html_content = f.read()
        
        # Update API URLs to point to this demo server
        html_content = html_content.replace(
            "this.baseUrl = 'http://localhost:8002'",
            "this.baseUrl = 'http://localhost:8003'"
        )
        html_content = html_content.replace(
            "this.baseUrl = await this.configService.getConfig('api.base_url') || 'http://localhost:8100'",
            "this.baseUrl = 'http://localhost:8003'"
        )
        
        return HTMLResponse(content=html_content)
    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="Frontend file not found")

# Configuration API endpoints
@app.get("/config", response_model=List[ConfigResponse])
async def list_configurations(category: Optional[str] = None):
    """List all configurations"""
    
    configs = []
    for key, value in CONFIG_STORE.items():
        # Determine category from key
        config_category = key.split('.')[0] if '.' in key else 'general'
        
        if category and config_category != category:
            continue
        
        # Determine type
        value_type = "string"
        if isinstance(value, bool):
            value_type = "boolean"
        elif isinstance(value, (int, float)):
            value_type = "number"
        elif isinstance(value, (list, dict)):
            value_type = "json"
        
        config = ConfigResponse(
            id=hash(key) % 10000,
            key=key,
            value=value,
            type=value_type,
            category=config_category,
            description=f"Configuration for {key}",
            is_sensitive="token" in key.lower() or "password" in key.lower(),
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        configs.append(config)
    
    return configs

@app.get("/config/{key}", response_model=ConfigResponse)
async def get_configuration(key: str):
    """Get specific configuration"""
    
    if key not in CONFIG_STORE:
        raise HTTPException(status_code=404, detail="Configuration not found")
    
    value = CONFIG_STORE[key]
    config_category = key.split('.')[0] if '.' in key else 'general'
    
    # Determine type
    value_type = "string"
    if isinstance(value, bool):
        value_type = "boolean"
    elif isinstance(value, (int, float)):
        value_type = "number"
    elif isinstance(value, (list, dict)):
        value_type = "json"
    
    return ConfigResponse(
        id=hash(key) % 10000,
        key=key,
        value=value,
        type=value_type,
        category=config_category,
        description=f"Configuration for {key}",
        is_sensitive="token" in key.lower() or "password" in key.lower(),
        created_at=datetime.now(),
        updated_at=datetime.now()
    )

@app.put("/config/{key}", response_model=ConfigResponse)
async def update_configuration(key: str, update: ConfigUpdate):
    """Update configuration"""
    
    if key not in CONFIG_STORE:
        raise HTTPException(status_code=404, detail="Configuration not found")
    
    # Update value
    CONFIG_STORE[key] = update.value
    
    # Return updated config
    return await get_configuration(key)

# Mock API endpoints
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return MOCK_DATA["health"]

@app.get("/api/v1/model/info")
async def get_model_info():
    """Get model information"""
    return MOCK_DATA["model_info"]

@app.get("/systems")
async def get_solar_systems():
    """Get list of solar systems"""
    return MOCK_DATA["systems"]

@app.get("/api/v1/data/solax/latest")
async def get_latest_data(system_id: Optional[str] = None):
    """Get latest solar data"""
    if system_id and system_id in MOCK_DATA["systems_data"]:
        data = MOCK_DATA["systems_data"][system_id].copy()
        data["timestamp"] = datetime.now().isoformat()
        return data
    elif system_id:
        raise HTTPException(status_code=404, detail="System not found")
    else:
        # Return combined data for all systems
        combined_data = {
            "timestamp": datetime.now().isoformat(),
            "yield_today": sum(s["yield_today"] for s in MOCK_DATA["systems_data"].values()),
            "yield_total": sum(s["yield_total"] for s in MOCK_DATA["systems_data"].values()),
            "ac_power": sum(s["ac_power"] for s in MOCK_DATA["systems_data"].values()),
            "soc": sum(s["soc"] for s in MOCK_DATA["systems_data"].values()) / len(MOCK_DATA["systems_data"]),
            "bat_power": sum(s["bat_power"] for s in MOCK_DATA["systems_data"].values()),
            "temperature": sum(s["temperature"] for s in MOCK_DATA["systems_data"].values()) / len(MOCK_DATA["systems_data"])
        }
        return combined_data

@app.get("/api/v1/stats/system/{system_id}")
async def get_system_stats(system_id: str):
    """Get system statistics"""
    if system_id not in MOCK_DATA["systems_data"]:
        raise HTTPException(status_code=404, detail="System not found")

    # Mock statistics
    return {
        "avg_daily_yield": 42.5 + (hash(system_id) % 10),
        "peak_power": 4200 + (hash(system_id) % 1000),
        "efficiency": 85.5 + (hash(system_id) % 10),
        "uptime_days": 365,
        "total_energy": 15000 + (hash(system_id) % 5000)
    }

@app.get("/api/v1/stats/comparison")
async def get_system_comparison():
    """Get system comparison statistics"""
    return MOCK_DATA["comparison"]

@app.get("/api/v1/forecast/production/{system_id}")
async def get_production_forecast(system_id: str, days: int = 7):
    """Get production forecast using ML pipeline"""
    if system_id not in MOCK_DATA["forecasts"]:
        raise HTTPException(status_code=404, detail="System not found")

    forecast = MOCK_DATA["forecasts"][system_id].copy()
    forecast["days"] = days
    forecast["total_prediction"] = forecast["daily_average"] * days
    forecast["timestamp"] = datetime.now().isoformat()

    return forecast

@app.get("/api/v1/predict/ensemble/{system_id}")
async def get_ensemble_prediction(system_id: str, horizon: str = "daily"):
    """Get ensemble prediction"""
    if system_id not in MOCK_DATA["forecasts"]:
        raise HTTPException(status_code=404, detail="System not found")

    forecast = MOCK_DATA["forecasts"][system_id]

    return {
        "prediction": forecast["tomorrow_prediction"],
        "confidence": forecast["confidence"],
        "model_name": "ensemble_v3",
        "features_count": 101,
        "lower_bound": forecast["tomorrow_prediction"] * 0.85,
        "upper_bound": forecast["tomorrow_prediction"] * 1.15,
        "horizon": horizon,
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/model/performance/{system_id}")
async def get_model_performance(system_id: str):
    """Get model performance for specific system"""
    if system_id not in MOCK_DATA["systems_data"]:
        raise HTTPException(status_code=404, detail="System not found")

    # Find performance data
    perf = next(
        (p for p in MOCK_DATA["comparison"]["model_performance"] if p["system_id"] == system_id),
        None
    )

    if not perf:
        raise HTTPException(status_code=404, detail="Performance data not found")

    return perf

@app.get("/api/v1/data/weather/latest")
async def get_weather_data():
    """Get latest weather data"""
    # Update timestamp to current time
    data = MOCK_DATA["weather_data"].copy()
    data["timestamp"] = datetime.now().isoformat()
    return data

@app.post("/api/v1/predict")
async def make_prediction(data: Dict[str, Any]):
    """Make prediction"""
    
    # Mock prediction response
    prediction = {
        "prediction": 52.3,
        "confidence": 0.87,
        "model_used": "enhanced_model_v3",
        "features_used": 101,
        "prediction_interval": [48.1, 56.5],
        "timestamp": datetime.now().isoformat()
    }
    
    return prediction

@app.get("/categories")
async def list_categories():
    """List configuration categories"""
    
    categories = {}
    for key in CONFIG_STORE.keys():
        category = key.split('.')[0] if '.' in key else 'general'
        if category not in categories:
            categories[category] = 0
        categories[category] += 1
    
    return [{"category": cat, "config_count": count} for cat, count in categories.items()]

def create_demo_summary():
    """Create demo system summary"""
    
    summary = {
        "frontend_demo_system": {
            "creation_date": datetime.now().isoformat(),
            "status": "ready",
            "description": "Complete frontend and Telegram integration demo"
        },
        "components": {
            "central_registry_api": {
                "description": "Configuration management with versioning",
                "endpoints": 8,
                "features": [
                    "In-memory configuration store",
                    "RESTful configuration API",
                    "Category-based organization",
                    "Type-safe value handling",
                    "Version tracking"
                ]
            },
            "react_frontend": {
                "description": "Modern React-based dashboard",
                "features": [
                    "Real-time data display",
                    "Interactive configuration management",
                    "Responsive design",
                    "Material Design UI",
                    "Chart visualizations",
                    "Map integration ready"
                ]
            },
            "telegram_bot": {
                "description": "Interactive Telegram bot for control and alerts",
                "features": [
                    "Command-based interaction",
                    "Inline keyboard menus",
                    "Real-time status updates",
                    "Configuration management",
                    "Alert system",
                    "Location management"
                ]
            },
            "mock_api": {
                "description": "Complete API simulation",
                "endpoints": [
                    "/health",
                    "/api/v1/model/info",
                    "/api/v1/data/solax/latest",
                    "/api/v1/data/weather/latest",
                    "/api/v1/predict"
                ]
            }
        },
        "demo_features": {
            "configuration_management": "Live config editing via UI and Telegram",
            "real_time_dashboard": "Live data display with auto-refresh",
            "telegram_integration": "Full bot with commands and alerts",
            "api_simulation": "Complete backend API simulation",
            "responsive_design": "Mobile-friendly interface"
        },
        "access_urls": {
            "frontend": "http://localhost:8003/frontend",
            "api_docs": "http://localhost:8003/docs",
            "config_api": "http://localhost:8003/config",
            "health_check": "http://localhost:8003/health"
        }
    }
    
    return summary

def print_demo_summary(summary):
    """Print demo system summary"""
    
    print("\n" + "="*80)
    print("🚀 FRONTEND & TELEGRAM DEMO SYSTEM")
    print("="*80)
    print(f"📅 Demo Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 Status: {summary['frontend_demo_system']['status'].upper()}")
    print()
    
    # Components
    print("🔧 DEMO COMPONENTS:")
    for component, details in summary['components'].items():
        print(f"   ✅ {component.replace('_', ' ').title()}")
        print(f"      📊 {details['description']}")
        if 'endpoints' in details:
            print(f"      🔗 Endpoints: {details['endpoints']}")
        if 'features' in details:
            print(f"      🔧 Features: {len(details['features'])}")
    print()
    
    # Demo features
    print("⚡ DEMO FEATURES:")
    for feature, description in summary['demo_features'].items():
        print(f"   ✅ {feature.replace('_', ' ').title()}: {description}")
    print()
    
    # Access URLs
    print("🌐 ACCESS URLS:")
    for name, url in summary['access_urls'].items():
        print(f"   🔗 {name.replace('_', ' ').title()}: {url}")
    print()
    
    print("🎯 DEMO STATUS: ✅ FULLY OPERATIONAL")
    print("🚀 Ready for comprehensive frontend and Telegram testing")
    
    print("="*80)

def main():
    """Main demo function"""
    
    print("🚀 FRONTEND & TELEGRAM DEMO SYSTEM")
    print("="*60)
    print("🔄 Complete demo of React frontend and Telegram integration")
    print()
    
    try:
        # Create demo summary
        summary = create_demo_summary()
        
        # Print summary
        print_demo_summary(summary)
        
        print(f"\n🎉 Demo system ready!")
        print("🌐 Starting demo server on http://localhost:8003")
        print("📱 Frontend available at: http://localhost:8003/frontend")
        print("📋 API docs at: http://localhost:8003/docs")
        print()
        
        # Start server
        uvicorn.run(app, host="0.0.0.0", port=8003)
        
        return True
        
    except Exception as e:
        print(f"❌ Demo system failed: {e}")
        logger.exception("Demo system failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
