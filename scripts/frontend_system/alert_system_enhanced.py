#!/usr/bin/env python3
"""
Enhanced Solar Alert System with Hourly Intelligence
===================================================

This is an improved version of the alert system that uses intelligent
hourly-based thresholds instead of fixed daily thresholds, providing
more accurate and context-aware solar production alerts.

Key Improvements:
1. Hourly-based thresholds using historical data
2. Weather-adjusted expectations
3. Intelligent alert logic that avoids false positives
4. Context-aware alert messages

Author: Solar Prediction System
Date: June 23, 2025
"""

import os
import sys
import time
import json
import logging
import threading
import requests
import smtplib
import psycopg2
import pytz
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from psycopg2.extras import RealDictCursor
from fastapi import FastAPI, HTTPException
from fastapi.responses import JSONResponse
import uvicorn

# Import our hourly threshold calculator
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from hourly_threshold_algorithm import HourlyThresholdCalculator

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Greek timezone
GREEK_TZ = pytz.timezone('Europe/Athens')

def get_greek_time():
    """Get current time in Greek timezone"""
    return datetime.now(GREEK_TZ)

# Database configuration
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'port': int(os.getenv('DB_PORT', '5433')),
    'database': os.getenv('DB_NAME', 'solar_prediction'),
    'user': os.getenv('DB_USER', 'postgres'),
    'password': os.getenv('DB_PASSWORD', 'postgres')
}

# Enhanced alert configuration
ENHANCED_ALERT_CONFIG = {
    'telegram': {
        'bot_token': '**********************************************',
        'chat_id': '1510889515'
    },
    'email': {
        'smtp_server': 'smtp.gmail.com',
        'smtp_port': 587,
        'username': '<EMAIL>',
        'password': 'app_password',
        'to_email': '<EMAIL>'
    },
    'thresholds': {
        'low_soc': 20,  # %
        'high_temperature': 60,  # °C
        'data_staleness': 300,  # seconds
        'system_offline': 600,  # seconds
        # Note: low_daily_yield removed - now using intelligent hourly thresholds
    },
    'hourly_intelligence': {
        'enabled': True,
        'historical_days': 14,
        'min_data_points': 5,
        'weather_adjustment': True,
        'deviation_threshold': 0.25  # 25% below expected triggers alert
    }
}

class EnhancedAlertSystem:
    """Enhanced alert monitoring system with hourly intelligence"""
    
    def __init__(self):
        self.db_config = DB_CONFIG
        self.alert_config = ENHANCED_ALERT_CONFIG
        self.alert_history = {}
        self.last_alerts = {}
        self.alert_cooldown = 1800  # 30 minutes between same alerts
        self.is_running = False
        
        # Initialize hourly threshold calculator
        self.threshold_calculator = HourlyThresholdCalculator(self.db_config)
        
        # Initialize alert tables
        self.init_alert_tables()
        
        logger.info("🚀 Enhanced Alert System initialized with hourly intelligence")
    
    def get_db_connection(self):
        """Get database connection"""
        try:
            return psycopg2.connect(**self.db_config)
        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            return None
    
    def init_alert_tables(self):
        """Initialize alert tracking tables"""
        conn = self.get_db_connection()
        if not conn:
            return False
        
        try:
            cur = conn.cursor()
            
            # Create enhanced alerts table
            cur.execute("""
                CREATE TABLE IF NOT EXISTS enhanced_system_alerts (
                    id SERIAL PRIMARY KEY,
                    alert_type VARCHAR(50) NOT NULL,
                    severity VARCHAR(20) NOT NULL,
                    system_id VARCHAR(20),
                    message TEXT NOT NULL,
                    details JSONB,
                    hourly_context JSONB,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    resolved_at TIMESTAMP,
                    is_resolved BOOLEAN DEFAULT FALSE,
                    notification_sent BOOLEAN DEFAULT FALSE,
                    alert_source VARCHAR(50) DEFAULT 'enhanced_alert_system'
                )
            """)
            
            conn.commit()
            logger.info("✅ Enhanced alert tables initialized")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing alert tables: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()
    
    def send_telegram_alert(self, message: str, severity: str = "info") -> bool:
        """Send alert via Telegram"""
        try:
            severity_emojis = {
                "critical": "🚨",
                "warning": "⚠️",
                "info": "ℹ️",
                "success": "✅"
            }
            
            emoji = severity_emojis.get(severity, "📢")
            greek_time = get_greek_time()
            formatted_message = f"{emoji} **Enhanced Solar Alert**\n\n{message}\n\n🕐 {greek_time.strftime('%Y-%m-%d %H:%M:%S EEST')}\n🧠 Powered by Hourly Intelligence"
            
            url = f"https://api.telegram.org/bot{self.alert_config['telegram']['bot_token']}/sendMessage"
            
            payload = {
                'chat_id': self.alert_config['telegram']['chat_id'],
                'text': formatted_message,
                'parse_mode': 'Markdown'
            }
            
            response = requests.post(url, json=payload, timeout=10)
            
            if response.status_code == 200:
                logger.info(f"✅ Enhanced Telegram alert sent: {severity}")
                return True
            else:
                logger.error(f"❌ Telegram alert failed: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Telegram alert error: {e}")
            return False
    
    def log_enhanced_alert(self, alert_type: str, severity: str, message: str, 
                          system_id: str = None, details: Dict = None, 
                          hourly_context: Dict = None) -> int:
        """Log enhanced alert to database with hourly context"""
        conn = self.get_db_connection()
        if not conn:
            return None
        
        try:
            cur = conn.cursor()
            
            cur.execute("""
                INSERT INTO enhanced_system_alerts 
                (alert_type, severity, system_id, message, details, hourly_context)
                VALUES (%s, %s, %s, %s, %s, %s)
                RETURNING id
            """, (
                alert_type, severity, system_id, message, 
                json.dumps(details) if details else None,
                json.dumps(hourly_context) if hourly_context else None
            ))
            
            alert_id = cur.fetchone()[0]
            conn.commit()
            
            return alert_id
            
        except Exception as e:
            logger.error(f"Error logging enhanced alert: {e}")
            conn.rollback()
            return None
        finally:
            conn.close()
    
    def should_send_alert(self, alert_key: str) -> bool:
        """Check if alert should be sent (cooldown logic)"""
        now = get_greek_time()

        if alert_key in self.last_alerts:
            time_since_last = (now - self.last_alerts[alert_key]).total_seconds()
            if time_since_last < self.alert_cooldown:
                return False

        self.last_alerts[alert_key] = now
        return True

    def check_enhanced_solar_production(self):
        """Enhanced solar production check using hourly intelligence"""
        try:
            current_time = get_greek_time()
            current_hour = current_time.hour

            # Only check during daylight hours (6 AM to 7 PM)
            if not (6 <= current_hour <= 19):
                return

            logger.debug(f"🔍 Enhanced production check at {current_hour:02d}:00")

            for system_id in ['system1', 'system2']:
                try:
                    # Get current production
                    current_yield = self.get_current_daily_yield(system_id)
                    if current_yield is None:
                        continue

                    # Calculate intelligent threshold and expected yield
                    threshold, expected_yield, metadata = self.threshold_calculator.calculate_expected_yield_for_hour(
                        current_hour, system_id, current_time
                    )

                    # Get current weather for context
                    weather = metadata.get('current_weather', {})

                    # Determine if alert should be sent
                    should_alert, reason = self.threshold_calculator.should_send_alert(
                        current_yield, expected_yield, threshold, current_hour, weather, system_id
                    )

                    if should_alert:
                        alert_key = f"enhanced_low_production_{system_id}_{current_hour}"

                        if self.should_send_alert(alert_key):
                            # Generate intelligent alert message
                            message = self.threshold_calculator.get_alert_message(
                                system_id, current_hour, current_yield, expected_yield,
                                threshold, reason, weather
                            )

                            # Determine severity
                            deviation = ((expected_yield - current_yield) / expected_yield) if expected_yield > 0 else 0
                            severity = "critical" if deviation > 0.5 else "warning"

                            # Prepare detailed context
                            hourly_context = {
                                'hour': current_hour,
                                'current_yield': current_yield,
                                'expected_yield': expected_yield,
                                'threshold': threshold,
                                'deviation_percentage': deviation * 100,
                                'weather_factor': metadata.get('weather_factor', 1.0),
                                'historical_data_points': metadata.get('historical_data_points', 0),
                                'reason': reason,
                                'weather_conditions': weather
                            }

                            # Log enhanced alert
                            alert_id = self.log_enhanced_alert(
                                alert_type="enhanced_low_production",
                                severity=severity,
                                message=message,
                                system_id=system_id,
                                details={'algorithm_version': '2.0', 'hourly_intelligence': True},
                                hourly_context=hourly_context
                            )

                            # Send notification
                            if self.send_telegram_alert(message, severity):
                                logger.info(f"🚨 Enhanced alert sent for {system_id}: {reason}")
                            else:
                                logger.error(f"❌ Failed to send enhanced alert for {system_id}")

                    else:
                        logger.debug(f"✅ {system_id} production OK: {current_yield:.1f} kWh (expected: {expected_yield:.1f}, threshold: {threshold:.1f})")

                except Exception as e:
                    logger.error(f"Error checking enhanced production for {system_id}: {e}")

        except Exception as e:
            logger.error(f"Error in enhanced solar production check: {e}")

    def get_current_daily_yield(self, system_id: str) -> Optional[float]:
        """Get current daily yield for a system"""
        conn = self.get_db_connection()
        if not conn:
            return None

        try:
            cur = conn.cursor(cursor_factory=RealDictCursor)

            table_name = 'solax_data' if system_id == 'system1' else 'solax_data2'

            cur.execute(f"""
                SELECT MAX(yield_today) as current_yield
                FROM {table_name}
                WHERE DATE(timestamp) = CURRENT_DATE
                AND yield_today IS NOT NULL
            """)

            result = cur.fetchone()

            if result and result['current_yield'] is not None:
                return float(result['current_yield'])
            else:
                logger.warning(f"No yield data found for {system_id} today")
                return None

        except Exception as e:
            logger.error(f"Error getting current daily yield for {system_id}: {e}")
            return None
        finally:
            conn.close()

    def check_battery_soc(self):
        """Check battery State of Charge (existing logic)"""
        try:
            current_time = get_greek_time()
            current_hour = current_time.hour

            # Skip low SOC alerts during charging hours (6 AM to 12 PM)
            if 6 <= current_hour <= 12:
                return

            conn = self.get_db_connection()
            if not conn:
                return

            cur = conn.cursor(cursor_factory=RealDictCursor)

            for system_id, table_name in [('system1', 'solax_data'), ('system2', 'solax_data2')]:
                try:
                    cur.execute(f"""
                        SELECT soc, timestamp
                        FROM {table_name}
                        WHERE timestamp >= NOW() - INTERVAL '10 minutes'
                        ORDER BY timestamp DESC
                        LIMIT 1
                    """)

                    result = cur.fetchone()

                    if result and result['soc'] is not None:
                        soc = float(result['soc'])

                        if soc < self.alert_config['thresholds']['low_soc']:
                            alert_key = f"low_soc_{system_id}"

                            if self.should_send_alert(alert_key):
                                message = f"🔋 {system_id.upper()}: Low battery SOC\n\n"
                                message += f"📊 Current SOC: {soc:.1f}%\n"
                                message += f"⚠️ Threshold: {self.alert_config['thresholds']['low_soc']}%\n\n"
                                message += f"💡 Consider reducing consumption or checking solar production"

                                self.log_enhanced_alert(
                                    alert_type="low_battery_soc",
                                    severity="warning",
                                    message=message,
                                    system_id=system_id,
                                    details={'soc': soc, 'threshold': self.alert_config['thresholds']['low_soc']}
                                )

                                self.send_telegram_alert(message, "warning")
                                logger.info(f"🔋 Low SOC alert sent for {system_id}: {soc:.1f}%")

                except Exception as e:
                    logger.error(f"Error checking SOC for {system_id}: {e}")

            conn.close()

        except Exception as e:
            logger.error(f"Error in battery SOC check: {e}")

    def check_system_health(self):
        """Check overall system health (existing logic with enhancements)"""
        try:
            conn = self.get_db_connection()
            if not conn:
                return

            cur = conn.cursor(cursor_factory=RealDictCursor)
            current_time = get_greek_time()

            for system_id, table_name in [('system1', 'solax_data'), ('system2', 'solax_data2')]:
                try:
                    # Check data freshness
                    cur.execute(f"""
                        SELECT timestamp, ac_power, yield_today
                        FROM {table_name}
                        ORDER BY timestamp DESC
                        LIMIT 1
                    """)

                    result = cur.fetchone()

                    if result:
                        last_update = result['timestamp']
                        if isinstance(last_update, str):
                            last_update = datetime.fromisoformat(last_update.replace('Z', '+00:00'))

                        # Convert to Greek time for comparison
                        if last_update.tzinfo is None:
                            last_update = pytz.UTC.localize(last_update)
                        last_update_greek = last_update.astimezone(GREEK_TZ)

                        time_diff = (current_time - last_update_greek).total_seconds()

                        if time_diff > self.alert_config['thresholds']['data_staleness']:
                            alert_key = f"stale_data_{system_id}"

                            if self.should_send_alert(alert_key):
                                message = f"📡 {system_id.upper()}: Data collection issue\n\n"
                                message += f"⏰ Last update: {last_update_greek.strftime('%H:%M:%S')}\n"
                                message += f"🕐 Time since: {int(time_diff/60)} minutes\n\n"
                                message += f"🔧 Check data collector service"

                                self.log_enhanced_alert(
                                    alert_type="stale_data",
                                    severity="warning",
                                    message=message,
                                    system_id=system_id,
                                    details={'time_diff_seconds': time_diff, 'last_update': last_update_greek.isoformat()}
                                )

                                self.send_telegram_alert(message, "warning")
                                logger.info(f"📡 Stale data alert sent for {system_id}")

                    else:
                        alert_key = f"no_data_{system_id}"

                        if self.should_send_alert(alert_key):
                            message = f"❌ {system_id.upper()}: No data available\n\n"
                            message += f"🚨 System appears to be offline\n"
                            message += f"🔧 Check system connectivity and data collector"

                            self.log_enhanced_alert(
                                alert_type="no_data",
                                severity="critical",
                                message=message,
                                system_id=system_id
                            )

                            self.send_telegram_alert(message, "critical")
                            logger.error(f"❌ No data alert sent for {system_id}")

                except Exception as e:
                    logger.error(f"Error checking system health for {system_id}: {e}")

            conn.close()

        except Exception as e:
            logger.error(f"Error in system health check: {e}")

    def run_monitoring_cycle(self):
        """Run one complete monitoring cycle"""
        try:
            logger.debug("🔄 Starting enhanced monitoring cycle")

            # Enhanced solar production check (main improvement)
            self.check_enhanced_solar_production()

            # Existing checks
            self.check_battery_soc()
            self.check_system_health()

            logger.debug("✅ Enhanced monitoring cycle completed")

        except Exception as e:
            logger.error(f"Error in monitoring cycle: {e}")

    def start_monitoring(self, interval: int = 300):
        """Start the enhanced monitoring loop"""
        self.is_running = True
        logger.info(f"🚀 Enhanced Alert System started (interval: {interval}s)")

        while self.is_running:
            try:
                self.run_monitoring_cycle()
                time.sleep(interval)

            except KeyboardInterrupt:
                logger.info("⏹️ Enhanced monitoring stopped by user")
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(60)  # Wait 1 minute before retrying

    def stop_monitoring(self):
        """Stop the monitoring loop"""
        self.is_running = False
        logger.info("⏹️ Enhanced Alert System stopped")


# FastAPI app for enhanced alert system management
app = FastAPI(title="Enhanced Solar Alert System", version="2.0.0")

# Global alert system instance
alert_system = None

@app.on_event("startup")
async def startup_event():
    """Initialize enhanced alert system on startup"""
    global alert_system
    alert_system = EnhancedAlertSystem()

    # Start monitoring in background thread
    monitoring_thread = threading.Thread(
        target=alert_system.start_monitoring,
        args=(300,),  # 5-minute intervals
        daemon=True
    )
    monitoring_thread.start()

    logger.info("🚀 Enhanced Alert System API started")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "enhanced_alert_system",
        "version": "2.0.0",
        "features": ["hourly_intelligence", "weather_adjustment", "smart_thresholds"],
        "timestamp": get_greek_time().isoformat()
    }

@app.get("/alerts/recent")
async def get_recent_alerts(limit: int = 10):
    """Get recent enhanced alerts"""
    if not alert_system:
        raise HTTPException(status_code=503, detail="Alert system not initialized")

    conn = alert_system.get_db_connection()
    if not conn:
        raise HTTPException(status_code=503, detail="Database connection failed")

    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)

        cur.execute("""
            SELECT id, alert_type, severity, system_id, message,
                   hourly_context, created_at, is_resolved
            FROM enhanced_system_alerts
            ORDER BY created_at DESC
            LIMIT %s
        """, (limit,))

        alerts = cur.fetchall()

        # Convert to JSON-serializable format
        result = []
        for alert in alerts:
            alert_dict = dict(alert)
            alert_dict['created_at'] = alert_dict['created_at'].isoformat()
            result.append(alert_dict)

        return {"alerts": result, "count": len(result)}

    except Exception as e:
        logger.error(f"Error getting recent alerts: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving alerts")
    finally:
        conn.close()

@app.post("/test/production-check")
async def test_production_check():
    """Test the enhanced production check manually"""
    if not alert_system:
        raise HTTPException(status_code=503, detail="Alert system not initialized")

    try:
        alert_system.check_enhanced_solar_production()
        return {
            "status": "success",
            "message": "Enhanced production check completed",
            "timestamp": get_greek_time().isoformat()
        }
    except Exception as e:
        logger.error(f"Error in test production check: {e}")
        raise HTTPException(status_code=500, detail=f"Error: {str(e)}")


if __name__ == "__main__":
    # Run the enhanced alert system
    try:
        logger.info("🚀 Starting Enhanced Solar Alert System v2.0")

        # Start FastAPI server
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8109,  # Enhanced alert system port
            log_level="info"
        )

    except KeyboardInterrupt:
        logger.info("⏹️ Enhanced Alert System stopped by user")
    except Exception as e:
        logger.error(f"Error starting Enhanced Alert System: {e}")
        sys.exit(1)
