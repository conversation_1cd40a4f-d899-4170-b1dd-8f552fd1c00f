#!/usr/bin/env python3
"""
Simple API Info Service for Frontend
Provides basic API information endpoint
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from datetime import datetime

app = FastAPI(title="Solar API Info", version="1.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/api")
async def api_info():
    """API information endpoint"""
    return {
        "message": "Solar Prediction API",
        "version": "1.0.0",
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "background_tasks": True,
        "endpoints": {
            "health": "/health",
            "solar_data": "/api/v1/data/solax/latest",
            "weather": "/api/v1/weather/current",
            "model_info": "/api/v1/model/info"
        }
    }

if __name__ == "__main__":
    print("🌐 Starting Simple API Info Service on port 8102...")
    uvicorn.run(app, host="0.0.0.0", port=8102)
