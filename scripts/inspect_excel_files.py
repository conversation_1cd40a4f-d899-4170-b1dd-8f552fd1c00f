#!/usr/bin/env python3
"""
Inspect Excel Files
Check the structure of the Excel files to understand the data format
"""

import os
import pandas as pd

def inspect_excel_files():
    """Inspect the structure of Excel files"""
    print("🔍 INSPECTING EXCEL FILES STRUCTURE")
    print("=" * 50)
    
    for system_id in [1, 2]:
        system_dir = f"data/raw/System{system_id}"
        
        if not os.path.exists(system_dir):
            print(f"❌ Directory not found: {system_dir}")
            continue
        
        print(f"\n📊 System {system_id} Files:")
        excel_files = [f for f in os.listdir(system_dir) if f.endswith('.xlsx')]
        
        for excel_file in excel_files:
            file_path = os.path.join(system_dir, excel_file)
            print(f"\n📄 File: {excel_file}")
            
            try:
                # Try to read Excel file
                df = pd.read_excel(file_path, nrows=5)  # Read only first 5 rows
                
                print(f"   Shape: {df.shape}")
                print(f"   Columns: {list(df.columns)}")
                print(f"   Sample data:")
                for i, row in df.iterrows():
                    print(f"     Row {i}: {dict(row)}")
                    if i >= 2:  # Show only first 3 rows
                        break
                        
            except Exception as e:
                print(f"   ❌ Error reading file: {e}")
                
                # Try different sheet names or parameters
                try:
                    xl_file = pd.ExcelFile(file_path)
                    print(f"   Sheet names: {xl_file.sheet_names}")
                    
                    # Try first sheet
                    if xl_file.sheet_names:
                        df = pd.read_excel(file_path, sheet_name=xl_file.sheet_names[0], nrows=5)
                        print(f"   First sheet columns: {list(df.columns)}")
                        
                except Exception as e2:
                    print(f"   ❌ Could not read any sheet: {e2}")

def main():
    """Main inspection"""
    print("🔍 EXCEL FILES INSPECTION")
    print("=" * 30)
    
    inspect_excel_files()
    
    print(f"\n✅ Inspection completed!")

if __name__ == "__main__":
    main()
