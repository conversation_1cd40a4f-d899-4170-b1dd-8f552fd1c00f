#!/usr/bin/env python3
"""
Fix Data Coverage Gap - Solar Prediction System
Migrate recent historical data to align SolaX and Weather data coverage
"""

import psycopg2
import json
from datetime import datetime, timedelta
import sys
import os

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'user': 'postgres',
    'password': 'postgres',
    'database': 'solar_prediction'
}

def check_current_status():
    """Check current data status"""
    print("🔍 CHECKING CURRENT DATA STATUS")
    print("=" * 50)
    
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # Check SolaX data
        cursor.execute("""
            SELECT COUNT(*), MIN(timestamp), MAX(timestamp)
            FROM solax_data;
        """)
        solax_count, solax_min, solax_max = cursor.fetchone()
        solax_days = (solax_max - solax_min).days if solax_min and solax_max else 0
        
        # Check Weather data
        cursor.execute("""
            SELECT COUNT(*), MIN(timestamp), MAX(timestamp)
            FROM weather_data;
        """)
        weather_count, weather_min, weather_max = cursor.fetchone()
        weather_days = (weather_max - weather_min).days if weather_min and weather_max else 0
        
        # Check Historical data
        cursor.execute("""
            SELECT COUNT(*), MIN(timestamp), MAX(timestamp)
            FROM normalized_training_data;
        """)
        hist_count, hist_min, hist_max = cursor.fetchone()
        hist_days = (hist_max - hist_min).days if hist_min and hist_max else 0
        
        print(f"📡 Current SolaX Data:")
        print(f"   Records: {solax_count:,}")
        print(f"   Coverage: {solax_days} days ({solax_min} → {solax_max})")
        print()
        
        print(f"🌤️  Weather Data:")
        print(f"   Records: {weather_count:,}")
        print(f"   Coverage: {weather_days} days ({weather_min} → {weather_max})")
        print()
        
        print(f"📚 Historical Data:")
        print(f"   Records: {hist_count:,}")
        print(f"   Coverage: {hist_days} days ({hist_min} → {hist_max})")
        print()
        
        # Calculate gap
        gap = abs(solax_days - weather_days)
        print(f"⚠️  Coverage Gap: {gap} days")
        
        conn.close()
        
        return {
            'solax_days': solax_days,
            'weather_days': weather_days,
            'hist_days': hist_days,
            'gap': gap,
            'solax_max': solax_max,
            'weather_min': weather_min,
            'hist_max': hist_max
        }
        
    except Exception as e:
        print(f"❌ Error checking status: {e}")
        return None

def migrate_historical_data(days_to_migrate=30, dry_run=True):
    """Migrate historical data to current SolaX table"""
    print(f"🔄 {'DRY RUN: ' if dry_run else ''}MIGRATING HISTORICAL DATA")
    print("=" * 50)
    print(f"📅 Migrating last {days_to_migrate} days from historical data")
    
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # Get current SolaX data range to avoid duplicates
        cursor.execute("SELECT MAX(timestamp) FROM solax_data;")
        current_max = cursor.fetchone()[0]
        
        # Calculate migration window
        if current_max:
            # Start from where current data ends, go back in historical data
            end_date = current_max - timedelta(hours=1)  # Small gap to avoid overlap
            start_date = end_date - timedelta(days=days_to_migrate)
        else:
            # No current data, use recent historical data
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days_to_migrate)
        
        print(f"📅 Migration window: {start_date} → {end_date}")
        
        # Get historical data to migrate
        cursor.execute("""
            SELECT 
                timestamp, ac_power, soc, yield_today, yield_total,
                powerdc1, powerdc2, bat_power, feedin_power,
                feedin_energy, consume_energy, temperature
            FROM normalized_training_data
            WHERE timestamp BETWEEN %s AND %s
            ORDER BY timestamp;
        """, (start_date, end_date))
        
        historical_records = cursor.fetchall()
        print(f"📋 Found {len(historical_records)} historical records to migrate")
        
        if not historical_records:
            print("ℹ️  No historical data found in the specified range")
            conn.close()
            return True
        
        if dry_run:
            print("🔍 DRY RUN - Would migrate:")
            print(f"   📊 Records: {len(historical_records)}")
            print(f"   📅 Date range: {historical_records[0][0]} → {historical_records[-1][0]}")
            print(f"   ⚡ Sample power values: {[r[1] for r in historical_records[:5]]}")
            print()
            print("🚀 To execute migration, run with dry_run=False")
        else:
            print("💾 Executing migration...")
            migrated_count = 0
            errors = 0
            
            for record in historical_records:
                try:
                    # Insert historical record into solax_data
                    insert_query = """
                        INSERT INTO solax_data (
                            timestamp, ac_power, soc, yield_today, yield_total,
                            powerdc1, powerdc2, bat_power, feedin_power,
                            feedin_energy, consume_energy, temperature,
                            inverter_sn, wifi_sn, inverter_type, inverter_status,
                            upload_time, bat_status, raw_data
                        ) VALUES (
                            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                            'HISTORICAL', 'HISTORICAL', 'MIGRATED', 1,
                            %s, 1, %s
                        )
                    """
                    
                    # Prepare values
                    timestamp = record[0]
                    values = record[1:] + (
                        timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                        json.dumps({
                            "migrated": True,
                            "source": "normalized_training_data",
                            "migration_date": datetime.now().isoformat()
                        })
                    )
                    
                    cursor.execute(insert_query, values)
                    migrated_count += 1
                    
                    if migrated_count % 1000 == 0:
                        print(f"  📊 Migrated {migrated_count:,} records...")
                        conn.commit()  # Commit in batches
                        
                except Exception as e:
                    errors += 1
                    if errors < 10:  # Show first 10 errors
                        print(f"⚠️  Error migrating record {record[0]}: {e}")
                    continue
            
            conn.commit()
            print(f"✅ Migration completed!")
            print(f"   📊 Successfully migrated: {migrated_count:,} records")
            print(f"   ❌ Errors: {errors}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        return False

def verify_migration():
    """Verify migration results"""
    print("✅ VERIFYING MIGRATION RESULTS")
    print("=" * 50)
    
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # Check updated SolaX data
        cursor.execute("""
            SELECT 
                COUNT(*) as total,
                COUNT(CASE WHEN inverter_sn = 'HISTORICAL' THEN 1 END) as migrated,
                MIN(timestamp) as earliest,
                MAX(timestamp) as latest
            FROM solax_data;
        """)
        
        total, migrated, earliest, latest = cursor.fetchone()
        days_coverage = (latest - earliest).days if earliest and latest else 0
        
        print(f"📊 Updated SolaX Data:")
        print(f"   Total records: {total:,}")
        print(f"   Migrated records: {migrated:,}")
        print(f"   Current records: {total - migrated:,}")
        print(f"   Date range: {earliest} → {latest}")
        print(f"   Coverage: {days_coverage} days")
        
        # Check data alignment with weather
        cursor.execute("""
            SELECT MIN(timestamp), MAX(timestamp)
            FROM weather_data;
        """)
        weather_min, weather_max = cursor.fetchone()
        weather_days = (weather_max - weather_min).days if weather_min and weather_max else 0
        
        print()
        print(f"🌤️  Weather Data Coverage: {weather_days} days")
        print(f"📡 SolaX Data Coverage: {days_coverage} days")
        
        gap = abs(days_coverage - weather_days)
        print(f"📏 Coverage Gap: {gap} days")
        
        if gap <= 7:
            print("✅ Data alignment is now GOOD (≤7 days gap)")
        else:
            print("⚠️  Data alignment still needs improvement")
        
        conn.close()
        return gap <= 7
        
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        return False

def main():
    """Main execution function"""
    print("🔧 SOLAR PREDICTION SYSTEM - DATA COVERAGE FIX")
    print("=" * 60)
    print(f"🕐 Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Step 1: Check current status
    status = check_current_status()
    if not status:
        print("❌ Failed to check current status")
        return
    
    print()
    
    # Step 2: Determine if migration is needed
    if status['gap'] <= 7:
        print("✅ Data coverage gap is acceptable (≤7 days)")
        print("🎯 No migration needed")
        return
    
    print(f"⚠️  Large coverage gap detected: {status['gap']} days")
    print("🔄 Migration recommended")
    print()
    
    # Step 3: Run dry run migration
    print("🧪 RUNNING DRY RUN MIGRATION")
    print("-" * 40)
    if not migrate_historical_data(days_to_migrate=30, dry_run=True):
        print("❌ Dry run failed")
        return
    
    print()
    
    # Step 4: Ask for confirmation
    print("🚀 READY TO EXECUTE MIGRATION")
    print("-" * 40)
    print("This will:")
    print("1. Migrate last 30 days of historical data")
    print("2. Add records to current solax_data table")
    print("3. Improve data coverage alignment")
    print()
    
    try:
        confirm = input("Execute migration? (yes/no): ").strip().lower()
        if confirm in ['yes', 'y']:
            print()
            print("🔄 EXECUTING MIGRATION...")
            print("-" * 30)
            
            if migrate_historical_data(days_to_migrate=30, dry_run=False):
                print()
                verify_migration()
                print()
                print("🎉 DATA COVERAGE FIX COMPLETED!")
            else:
                print("❌ Migration failed")
        else:
            print("⏹️  Migration cancelled by user")
            
    except KeyboardInterrupt:
        print("\n⏹️  Operation cancelled by user")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"\n❌ Script failed: {e}")
        sys.exit(1)
