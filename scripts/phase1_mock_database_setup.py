#!/usr/bin/env python3
"""
Phase 1: Mock Database Setup for Development
Creates mock data structures when PostgreSQL is not available
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import json

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MockDatabaseSetup:
    """Mock database setup for development when PostgreSQL is not available"""
    
    def __init__(self):
        self.data_dir = 'data/mock'
        self.setup_complete = False
        
        # Ensure data directory exists
        os.makedirs(self.data_dir, exist_ok=True)
        os.makedirs('reports/phase1', exist_ok=True)
    
    def generate_mock_solax_data(self, system_id='system_1', days=90):
        """Generate mock SolaX data for development"""
        
        logger.info(f"📊 Generating mock data for {system_id} ({days} days)...")
        
        # Create date range
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        # Generate hourly timestamps
        timestamps = pd.date_range(start=start_date, end=end_date, freq='5min')
        
        mock_data = []
        
        for ts in timestamps:
            hour = ts.hour
            day_of_year = ts.timetuple().tm_yday
            
            # Solar elevation simulation
            solar_elevation = max(0, 60 * np.sin(np.pi * (hour - 6) / 12)) if 6 <= hour <= 18 else 0
            
            # Base production simulation
            if solar_elevation > 0:
                # Daytime production
                base_power = 8000 * np.sin(np.radians(solar_elevation)) * np.random.uniform(0.8, 1.2)
                base_power = max(0, base_power + np.random.normal(0, 500))
            else:
                # Nighttime - battery discharge
                base_power = np.random.uniform(0, 200) if np.random.random() > 0.7 else 0
            
            # Seasonal adjustment
            seasonal_factor = 1 + 0.3 * np.sin(2 * np.pi * (day_of_year - 80) / 365)
            base_power *= seasonal_factor
            
            # System-specific adjustment
            if system_id == 'system_2':
                base_power *= 1.08  # System 2 slightly better performance
            
            # Calculate cumulative yield (reset daily)
            if ts.hour == 0 and ts.minute == 0:
                daily_yield_start = 0
            else:
                # Cumulative for the day
                hours_since_midnight = ts.hour + ts.minute / 60
                if hours_since_midnight > 6:
                    daily_yield_start = (hours_since_midnight - 6) * base_power / 1000 * 0.1
                else:
                    daily_yield_start = 0
            
            # Battery simulation
            battery_soc = 50 + 30 * np.sin(2 * np.pi * hour / 24) + np.random.normal(0, 5)
            battery_soc = np.clip(battery_soc, 10, 95)
            
            battery_power = np.random.uniform(-3000, 3000)
            
            # Grid and load simulation
            grid_power = np.random.uniform(-2000, 5000)
            load_power = np.random.uniform(500, 3000)
            
            mock_data.append({
                'timestamp': ts,
                'yield_today': daily_yield_start,
                'total_yield': daily_yield_start * 1000,  # Convert to Wh
                'ac_power': base_power,
                'battery_soc': battery_soc,
                'battery_power': battery_power,
                'grid_power': grid_power,
                'load_power': load_power,
                'system_id': system_id
            })
        
        df = pd.DataFrame(mock_data)
        
        # Save to CSV
        filename = f'{self.data_dir}/mock_{system_id}_data.csv'
        df.to_csv(filename, index=False)
        
        logger.info(f"✅ Generated {len(df):,} records for {system_id}")
        logger.info(f"📁 Saved to: {filename}")
        
        return df
    
    def generate_mock_weather_data(self, days=90):
        """Generate mock weather data"""
        
        logger.info(f"🌤️ Generating mock weather data ({days} days)...")
        
        # Create date range
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        # Generate hourly timestamps
        timestamps = pd.date_range(start=start_date, end=end_date, freq='H')
        
        weather_data = []
        
        for ts in timestamps:
            hour = ts.hour
            day_of_year = ts.timetuple().tm_yday
            
            # Temperature simulation (seasonal + daily cycle)
            base_temp = 20 + 10 * np.sin(2 * np.pi * (day_of_year - 80) / 365)  # Seasonal
            daily_temp_variation = 8 * np.sin(2 * np.pi * (hour - 6) / 24)      # Daily
            temperature = base_temp + daily_temp_variation + np.random.normal(0, 2)
            
            # Solar radiation simulation
            solar_elevation = max(0, 60 * np.sin(np.pi * (hour - 6) / 12)) if 6 <= hour <= 18 else 0
            
            if solar_elevation > 0:
                clear_sky_ghi = 1000 * np.sin(np.radians(solar_elevation))
                cloud_cover = max(0, min(100, 30 + np.random.normal(0, 20)))
                ghi = clear_sky_ghi * (1 - cloud_cover / 100 * 0.8) + np.random.normal(0, 50)
                ghi = max(0, ghi)
                
                # DNI and DHI
                dni = ghi * 0.8 * (1 - cloud_cover / 100)
                dhi = ghi * 0.2 + ghi * cloud_cover / 100 * 0.3
            else:
                ghi = dni = dhi = 0
                cloud_cover = np.random.uniform(0, 100)
            
            # Other weather parameters
            humidity = 60 + np.random.normal(0, 15)
            humidity = np.clip(humidity, 20, 95)
            
            wind_speed = 5 + np.random.normal(0, 3)
            wind_speed = np.clip(wind_speed, 0, 20)
            
            pressure = 1013 + np.random.normal(0, 10)
            
            weather_data.append({
                'timestamp': ts,
                'ghi': ghi,
                'dni': dni,
                'dhi': dhi,
                'temperature': temperature,
                'humidity': humidity,
                'wind_speed': wind_speed,
                'cloud_cover': cloud_cover,
                'pressure': pressure
            })
        
        df = pd.DataFrame(weather_data)
        
        # Save to CSV
        filename = f'{self.data_dir}/mock_weather_data.csv'
        df.to_csv(filename, index=False)
        
        logger.info(f"✅ Generated {len(df):,} weather records")
        logger.info(f"📁 Saved to: {filename}")
        
        return df
    
    def create_integrated_features_data(self):
        """Create integrated features dataset"""
        
        logger.info("🔗 Creating integrated features dataset...")
        
        try:
            # Load mock data
            system1_df = pd.read_csv(f'{self.data_dir}/mock_system_1_data.csv')
            system2_df = pd.read_csv(f'{self.data_dir}/mock_system_2_data.csv')
            weather_df = pd.read_csv(f'{self.data_dir}/mock_weather_data.csv')
            
            # Convert timestamps
            system1_df['timestamp'] = pd.to_datetime(system1_df['timestamp'])
            system2_df['timestamp'] = pd.to_datetime(system2_df['timestamp'])
            weather_df['timestamp'] = pd.to_datetime(weather_df['timestamp'])
            
            # Combine solar data
            solar_df = pd.concat([system1_df, system2_df], ignore_index=True)
            
            # Merge with weather data (nearest hour)
            solar_df['hour_timestamp'] = solar_df['timestamp'].dt.floor('H')
            weather_df['hour_timestamp'] = weather_df['timestamp']
            
            integrated_df = pd.merge_asof(
                solar_df.sort_values('timestamp'),
                weather_df.sort_values('hour_timestamp'),
                left_on='timestamp',
                right_on='hour_timestamp',
                direction='nearest',
                tolerance=pd.Timedelta('30min')
            )
            
            # Add temporal features
            integrated_df['hour'] = integrated_df['timestamp'].dt.hour
            integrated_df['day_of_year'] = integrated_df['timestamp'].dt.dayofyear
            integrated_df['month'] = integrated_df['timestamp'].dt.month
            integrated_df['weekday'] = integrated_df['timestamp'].dt.weekday
            
            # Save integrated data
            filename = f'{self.data_dir}/mock_integrated_features.csv'
            integrated_df.to_csv(filename, index=False)
            
            logger.info(f"✅ Created integrated dataset with {len(integrated_df):,} records")
            logger.info(f"📁 Saved to: {filename}")
            
            return integrated_df
            
        except Exception as e:
            logger.error(f"❌ Failed to create integrated features: {e}")
            return None
    
    def generate_analysis_report(self):
        """Generate mock analysis report"""
        
        report = {
            'analysis_date': datetime.now().isoformat(),
            'database_status': 'mock_mode',
            'tables_found': 3,
            'schema_issues': [],
            'recommendations': [
                "Mock data generated successfully",
                "Ready to proceed to Phase 2: Feature Engineering",
                "Connect to real PostgreSQL database when available"
            ],
            'status': 'ready_for_phase2',
            'mock_data_summary': {
                'system_1_records': 25920,  # 90 days * 24 hours * 12 (5-min intervals)
                'system_2_records': 25920,
                'weather_records': 2160,    # 90 days * 24 hours
                'integrated_records': 51840,
                'date_range': {
                    'start': (datetime.now() - timedelta(days=90)).isoformat(),
                    'end': datetime.now().isoformat()
                }
            }
        }
        
        # Save report
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_path = f'reports/phase1/mock_database_setup_{timestamp}.json'
        
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        logger.info(f"📋 Mock setup report saved to: {report_path}")
        
        return report
    
    def setup_mock_environment(self):
        """Setup complete mock environment"""
        
        logger.info("🚀 Setting up mock database environment...")
        
        try:
            # Generate mock data for both systems
            system1_df = self.generate_mock_solax_data('system_1', days=90)
            system2_df = self.generate_mock_solax_data('system_2', days=90)
            
            # Generate weather data
            weather_df = self.generate_mock_weather_data(days=90)
            
            # Create integrated features
            integrated_df = self.create_integrated_features_data()
            
            if integrated_df is not None:
                self.setup_complete = True
                
                # Generate report
                report = self.generate_analysis_report()
                
                return report
            else:
                return None
                
        except Exception as e:
            logger.error(f"❌ Mock setup failed: {e}")
            return None
    
    def print_summary(self, report):
        """Print setup summary"""
        
        print("\n" + "="*80)
        print("🔧 MOCK DATABASE SETUP SUMMARY")
        print("="*80)
        print(f"📅 Setup Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🗄️ Mode: Mock Development Environment")
        print()
        
        if report:
            mock_summary = report['mock_data_summary']
            
            print("📊 MOCK DATA GENERATED:")
            print(f"   📋 System 1 Records: {mock_summary['system_1_records']:,}")
            print(f"   📋 System 2 Records: {mock_summary['system_2_records']:,}")
            print(f"   🌤️ Weather Records: {mock_summary['weather_records']:,}")
            print(f"   🔗 Integrated Records: {mock_summary['integrated_records']:,}")
            print()
            
            print("📅 DATE RANGE:")
            print(f"   Start: {mock_summary['date_range']['start']}")
            print(f"   End: {mock_summary['date_range']['end']}")
            print()
            
            print("📁 FILES CREATED:")
            print(f"   📊 {self.data_dir}/mock_system_1_data.csv")
            print(f"   📊 {self.data_dir}/mock_system_2_data.csv")
            print(f"   🌤️ {self.data_dir}/mock_weather_data.csv")
            print(f"   🔗 {self.data_dir}/mock_integrated_features.csv")
            print()
            
            print("🎯 STATUS:")
            for rec in report['recommendations']:
                print(f"   ✅ {rec}")
            print()
            
            print("📋 NEXT STEPS:")
            print("   🚀 Proceed to Phase 2: Feature Engineering")
            print("   📊 Run: python scripts/phase2_feature_engineering.py")
            print("   🔗 Connect to real PostgreSQL when available")
        else:
            print("❌ MOCK SETUP FAILED")
        
        print("="*80)

def main():
    """Main mock setup function"""
    
    print("🔧 PHASE 1: MOCK DATABASE SETUP")
    print("="*60)
    print("ℹ️ Setting up mock environment for development")
    print("🔗 Connect to real PostgreSQL database when available")
    print()
    
    try:
        # Initialize mock setup
        mock_setup = MockDatabaseSetup()
        
        # Setup mock environment
        report = mock_setup.setup_mock_environment()
        
        if report:
            # Print summary
            mock_setup.print_summary(report)
            
            print(f"\n🎉 Mock database setup completed successfully!")
            print("🚀 Ready to proceed to Phase 2: Feature Engineering")
            
            return True
        else:
            print("❌ Mock setup failed")
            return False
            
    except Exception as e:
        print(f"❌ Mock setup failed: {e}")
        logger.exception("Mock setup failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
