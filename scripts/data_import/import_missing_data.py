#!/usr/bin/env python3
"""
Import missing data from Excel files in data/raw/new/
Handles System 1 and System 2 plant reports for June 2025
"""

import pandas as pd
import psycopg2
import psycopg2.extras
from datetime import datetime
import os
import sys

class MissingDataImporter:
    """Import missing solar data from Excel files"""
    
    def __init__(self, db_connection_string):
        self.db_connection_string = db_connection_string
        self.data_folder = "/home/<USER>/solar-prediction-project/data/raw/new"
        
    def read_excel_file(self, filepath, system_id):
        """Read and process Excel file for a system"""
        print(f"📖 Reading {filepath}")

        try:
            # Read Excel file - try different approaches
            df_raw = pd.read_excel(filepath)
            print(f"   Raw data shape: {df_raw.shape}")

            # The Excel seems to have a complex structure - try manual parsing
            print(f"   Trying manual parsing...")

            # Try reading with no header first
            df_no_header = pd.read_excel(filepath, header=None)

            # Look for the actual data rows
            for i in range(min(20, len(df_no_header))):
                row_values = df_no_header.iloc[i].astype(str).tolist()

                # Check if this looks like a data row (has numbers and time)
                if (len(row_values) >= 7 and
                    any('2025' in str(val) for val in row_values) and
                    any(str(val).replace('.', '').isdigit() for val in row_values)):
                    print(f"   Found data starting at row {i}")
                    print(f"   Sample row: {row_values}")

                    # Create proper column names
                    columns = ['No', 'Update_time', 'Daily_PV_Yield_kWh', 'Daily_inverter_output_kWh',
                              'Daily_exported_energy_kWh', 'Daily_consumed_kWh', 'Daily_imported_energy_kWh']

                    # Extract data from this row onwards
                    df = df_no_header.iloc[i:].copy()
                    df.columns = columns[:len(df.columns)]
                    df = df.reset_index(drop=True)

                    print(f"   Processed data shape: {df.shape}")
                    print(f"   Columns: {list(df.columns)}")
                    print(f"   First 3 rows:")
                    print(df.head(3))
                    return df

            # If no header found, try different skip rows
            print(f"   No clear header found, trying skiprows=1")
            df = pd.read_excel(filepath, skiprows=1)
            print(f"   Processed data shape: {df.shape}")
            print(f"   Columns: {list(df.columns)}")
            print(f"   First 3 rows:")
            print(df.head(3))

            return df

        except Exception as e:
            print(f"   ❌ Error reading Excel: {e}")
            return None
    
    def process_system_data(self, df, system_id):
        """Process and standardize system data"""
        print(f"🔄 Processing System {system_id} data")

        try:
            processed_records = []

            # Convert data types
            df['Update_time'] = pd.to_datetime(df['Update_time'])
            df['Daily_PV_Yield_kWh'] = pd.to_numeric(df['Daily_PV_Yield_kWh'], errors='coerce')
            df['Daily_consumed_kWh'] = pd.to_numeric(df['Daily_consumed_kWh'], errors='coerce')
            df['Daily_exported_energy_kWh'] = pd.to_numeric(df['Daily_exported_energy_kWh'], errors='coerce')
            df['Daily_imported_energy_kWh'] = pd.to_numeric(df['Daily_imported_energy_kWh'], errors='coerce')

            print(f"   Converting {len(df)} records...")

            for idx, row in df.iterrows():
                try:
                    # Map Excel columns to database fields
                    timestamp = row['Update_time']
                    yield_today = row['Daily_PV_Yield_kWh']
                    consume_energy = row['Daily_consumed_kWh']
                    feedin_energy = row['Daily_exported_energy_kWh']

                    # Calculate additional fields (basic estimates)
                    ac_power = 0  # Will be calculated from differences
                    soc = 50  # Default SOC
                    bat_power = 0  # Default (correct column name)

                    record = (
                        timestamp, yield_today, consume_energy, feedin_energy,
                        ac_power, soc, bat_power
                    )
                    processed_records.append(record)

                except Exception as e:
                    print(f"   ⚠️ Error processing row {idx}: {e}")
                    continue

            print(f"   ✅ Processed {len(processed_records)} records")
            return processed_records

        except Exception as e:
            print(f"   ❌ Error processing data: {e}")
            return []
    
    def insert_data_to_db(self, records, system_id):
        """Insert processed records to database"""
        if not records:
            print(f"   No records to insert for System {system_id}")
            return 0
            
        print(f"💾 Inserting {len(records)} records for System {system_id}")
        
        try:
            conn = psycopg2.connect(self.db_connection_string)
            cur = conn.cursor()
            
            table_name = 'solax_data' if system_id == 1 else 'solax_data2'
            
            # Insert query with correct column names
            insert_query = f"""
            INSERT INTO {table_name} (
                timestamp, yield_today, consume_energy, feedin_energy,
                ac_power, soc, bat_power
            ) VALUES %s
            ON CONFLICT (timestamp) DO UPDATE SET
                yield_today = EXCLUDED.yield_today,
                consume_energy = EXCLUDED.consume_energy,
                feedin_energy = EXCLUDED.feedin_energy,
                ac_power = EXCLUDED.ac_power,
                soc = EXCLUDED.soc,
                bat_power = EXCLUDED.bat_power
            """
            
            # Execute batch insert
            psycopg2.extras.execute_values(
                cur, insert_query, records, template=None, page_size=1000
            )
            
            conn.commit()
            inserted_count = cur.rowcount
            
            conn.close()
            print(f"   ✅ Inserted {inserted_count} records")
            return inserted_count
            
        except Exception as e:
            print(f"   ❌ Database error: {e}")
            return 0
    
    def import_all_files(self):
        """Import all Excel files in the data folder"""
        print("🚀 STARTING MISSING DATA IMPORT")
        print("=" * 60)
        
        files_to_process = [
            ("System1 Plant Reports 2025-06-01-2025-06-24.xlsx", 1),
            ("System 2 Plant Reports 2025-06-01-2025-06-24.xlsx", 2)
        ]
        
        total_imported = 0
        
        for filename, system_id in files_to_process:
            filepath = os.path.join(self.data_folder, filename)
            
            if not os.path.exists(filepath):
                print(f"⚠️ File not found: {filepath}")
                continue
                
            print(f"\n📁 Processing {filename}")
            print("-" * 40)
            
            # Read Excel file
            df = self.read_excel_file(filepath, system_id)
            if df is None:
                continue
                
            # Process data
            records = self.process_system_data(df, system_id)
            
            # Insert to database
            imported = self.insert_data_to_db(records, system_id)
            total_imported += imported
        
        print(f"\n🎯 IMPORT SUMMARY:")
        print(f"   Total records imported: {total_imported}")
        print(f"   Files processed: {len(files_to_process)}")
        
        return total_imported

def main():
    """Main function"""
    print("📊 MISSING DATA IMPORT TOOL")
    print("=" * 80)
    
    # Database connection
    db_connection = 'postgresql://postgres:postgres@localhost:5433/solar_prediction'
    
    # Create importer
    importer = MissingDataImporter(db_connection)
    
    # Import all files
    total_imported = importer.import_all_files()
    
    if total_imported > 0:
        print(f"\n✅ SUCCESS: Imported {total_imported} records")
        print(f"🔄 Next steps:")
        print(f"   1. Verify data integrity")
        print(f"   2. Recalculate billing fields for imported dates")
        print(f"   3. Update ROI calculations")
    else:
        print(f"\n⚠️ No data was imported")
        print(f"   Check Excel file structure and try again")

if __name__ == "__main__":
    main()
