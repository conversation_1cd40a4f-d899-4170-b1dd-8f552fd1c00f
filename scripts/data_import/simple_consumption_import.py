#!/usr/bin/env python3
"""
Simple Consumption Data Import
Imports consumption data from CSV/XLSX files to database
Date: June 2025
"""

import pandas as pd
import psycopg2
import os
import glob
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

def get_db_connection():
    """Get database connection"""
    return psycopg2.connect(**DB_CONFIG)

def read_file(file_path):
    """Read CSV or XLSX file and return DataFrame"""
    try:
        logger.info(f"Reading {file_path}")
        
        if file_path.endswith('.xlsx'):
            # Read XLSX file
            df = pd.read_excel(file_path)
        else:
            # Read CSV file - handle special format
            try:
                # Try normal CSV first
                df = pd.read_csv(file_path)
            except:
                # Handle special quoted format
                with open(file_path, 'r', encoding='utf-8-sig') as f:
                    lines = f.readlines()
                
                # Parse header
                header_line = lines[0].strip().strip('"')
                columns = [col.strip() for col in header_line.split(',')]
                
                # Parse data
                data_rows = []
                for line in lines[1:]:
                    line = line.strip().strip('"')
                    if line:
                        values = [val.strip() for val in line.split(',')]
                        if len(values) == len(columns):
                            data_rows.append(values)
                
                df = pd.DataFrame(data_rows, columns=columns)
        
        logger.info(f"Read {len(df)} rows from {file_path}")
        return df
        
    except Exception as e:
        logger.error(f"Error reading {file_path}: {e}")
        return None

def process_dataframe(df, system_id):
    """Process DataFrame and extract consumption data"""
    try:
        # Check required columns
        required_cols = ['Update time']
        if not all(col in df.columns for col in required_cols):
            logger.error(f"Missing required columns. Available: {list(df.columns)}")
            return None
        
        # Convert timestamp
        df['timestamp'] = pd.to_datetime(df['Update time'])
        
        # Find the right columns (they might have different names)
        yield_col = None
        feedin_col = None
        consumption_col = None
        
        for col in df.columns:
            if 'inverter output' in col.lower() or 'yield' in col.lower():
                yield_col = col
            elif 'export power' in col.lower() or 'feedin' in col.lower():
                feedin_col = col
            elif 'consumed' in col.lower() or 'consumption' in col.lower():
                consumption_col = col
        
        if not yield_col:
            logger.error("No yield column found")
            return None
        
        # Convert to numeric
        df[yield_col] = pd.to_numeric(df[yield_col], errors='coerce')
        if feedin_col:
            df[feedin_col] = pd.to_numeric(df[feedin_col], errors='coerce')
        if consumption_col:
            df[consumption_col] = pd.to_numeric(df[consumption_col], errors='coerce')
        
        # Sort by timestamp
        df = df.sort_values('timestamp')
        
        # Calculate daily values from cumulative
        df['yield_today'] = df[yield_col].diff().fillna(df[yield_col])
        
        if consumption_col:
            df['consume_energy'] = df[consumption_col].diff().fillna(df[consumption_col])
        else:
            df['consume_energy'] = 0
        
        # Handle negative values (resets at midnight)
        df.loc[df['yield_today'] < 0, 'yield_today'] = df[yield_col]
        if consumption_col:
            df.loc[df['consume_energy'] < 0, 'consume_energy'] = df[consumption_col]
        
        # Set feedin_power
        if feedin_col:
            df['feedin_power'] = df[feedin_col]
        else:
            df['feedin_power'] = 0
        
        # Calculate feedin_energy (approximate from power)
        df['feedin_energy'] = df['feedin_power'] * 5 / 60 / 1000  # 5-minute intervals
        
        # Add system identifier
        df['system_id'] = system_id
        
        # Filter meaningful data
        df = df[df[yield_col] > 0]
        
        # Select final columns
        result = df[['timestamp', 'yield_today', 'feedin_power', 'feedin_energy', 'consume_energy', 'system_id']].copy()
        
        logger.info(f"Processed {len(result)} valid records for {system_id}")
        return result
        
    except Exception as e:
        logger.error(f"Error processing DataFrame: {e}")
        return None

def import_to_database(df, table_name):
    """Import DataFrame to database table"""
    try:
        conn = get_db_connection()
        cur = conn.cursor()
        
        imported_count = 0
        updated_count = 0
        
        for _, row in df.iterrows():
            try:
                # Check if record exists
                cur.execute(f"""
                    SELECT COUNT(*) FROM {table_name} 
                    WHERE timestamp = %s
                """, (row['timestamp'],))
                
                exists = cur.fetchone()[0] > 0
                
                if not exists:
                    # Insert new record
                    if table_name == 'solax_data':
                        cur.execute("""
                            INSERT INTO solax_data (
                                timestamp, yield_today, feedin_power, 
                                feedin_energy, consume_energy
                            ) VALUES (%s, %s, %s, %s, %s)
                        """, (
                            row['timestamp'], row['yield_today'], 
                            row['feedin_power'], row['feedin_energy'], 
                            row['consume_energy']
                        ))
                    else:  # solax_data2
                        cur.execute("""
                            INSERT INTO solax_data2 (
                                timestamp, yield_today, feedin_power, 
                                feedin_energy, consume_energy
                            ) VALUES (%s, %s, %s, %s, %s)
                        """, (
                            row['timestamp'], row['yield_today'], 
                            row['feedin_power'], row['feedin_energy'], 
                            row['consume_energy']
                        ))
                    imported_count += 1
                else:
                    # Update existing record with consumption data
                    cur.execute(f"""
                        UPDATE {table_name} SET
                            feedin_power = COALESCE(feedin_power, %s),
                            feedin_energy = COALESCE(feedin_energy, %s),
                            consume_energy = COALESCE(consume_energy, %s)
                        WHERE timestamp = %s
                    """, (
                        row['feedin_power'], row['feedin_energy'], 
                        row['consume_energy'], row['timestamp']
                    ))
                    updated_count += 1
                    
            except Exception as e:
                logger.error(f"Error inserting/updating record: {e}")
                continue
        
        conn.commit()
        conn.close()
        
        logger.info(f"{table_name}: Imported {imported_count}, Updated {updated_count} records")
        return imported_count + updated_count
        
    except Exception as e:
        logger.error(f"Error importing to {table_name}: {e}")
        return 0

def import_system_data(system_path, system_id, table_name):
    """Import all files for a system"""
    try:
        # Get all files (CSV and XLSX)
        csv_files = glob.glob(f"{system_path}/*.csv")
        xlsx_files = glob.glob(f"{system_path}/*.xlsx")
        all_files = csv_files + xlsx_files
        
        total_imported = 0
        all_data = []
        
        for file_path in sorted(all_files):
            df = read_file(file_path)
            if df is not None:
                processed_df = process_dataframe(df, system_id)
                if processed_df is not None:
                    all_data.append(processed_df)
        
        if all_data:
            # Combine all data
            combined_df = pd.concat(all_data, ignore_index=True)
            
            # Remove duplicates
            combined_df = combined_df.drop_duplicates(subset=['timestamp'])
            combined_df = combined_df.sort_values('timestamp')
            
            # Import to database
            total_imported = import_to_database(combined_df, table_name)
        
        return total_imported
        
    except Exception as e:
        logger.error(f"Error importing {system_id} data: {e}")
        return 0

def verify_data():
    """Verify imported data"""
    try:
        conn = get_db_connection()
        cur = conn.cursor()
        
        # Check System1
        cur.execute("""
            SELECT 
                MIN(timestamp) as earliest,
                MAX(timestamp) as latest,
                COUNT(*) as total_records,
                COUNT(CASE WHEN feedin_power IS NOT NULL THEN 1 END) as feedin_records,
                COUNT(CASE WHEN consume_energy IS NOT NULL THEN 1 END) as consume_records
            FROM solax_data 
            WHERE timestamp >= '2024-03-01'
        """)
        system1_stats = cur.fetchone()
        
        # Check System2
        cur.execute("""
            SELECT 
                MIN(timestamp) as earliest,
                MAX(timestamp) as latest,
                COUNT(*) as total_records,
                COUNT(CASE WHEN feedin_power IS NOT NULL THEN 1 END) as feedin_records,
                COUNT(CASE WHEN consume_energy IS NOT NULL THEN 1 END) as consume_records
            FROM solax_data2 
            WHERE timestamp >= '2024-03-01'
        """)
        system2_stats = cur.fetchone()
        
        conn.close()
        
        return {
            'system1': system1_stats,
            'system2': system2_stats
        }
        
    except Exception as e:
        logger.error(f"Error verifying data: {e}")
        return None

if __name__ == "__main__":
    logger.info("🚀 Starting simple consumption data import...")
    
    # Import System1 data
    system1_imported = import_system_data(
        '/home/<USER>/solar-prediction-project/data/raw/System1',
        'system1',
        'solax_data'
    )
    
    # Import System2 data
    system2_imported = import_system_data(
        '/home/<USER>/solar-prediction-project/data/raw/System2',
        'system2',
        'solax_data2'
    )
    
    # Verify data
    stats = verify_data()
    
    if stats:
        print("\n📊 DATA IMPORT RESULTS:")
        print("=" * 50)
        
        print(f"\n🏠 SYSTEM1 (solax_data):")
        print(f"   📅 Period: {stats['system1'][0]} to {stats['system1'][1]}")
        print(f"   📊 Total records: {stats['system1'][2]:,}")
        print(f"   🔌 Feedin records: {stats['system1'][3]:,}")
        print(f"   💡 Consume records: {stats['system1'][4]:,}")
        
        print(f"\n🏠 SYSTEM2 (solax_data2):")
        print(f"   📅 Period: {stats['system2'][0]} to {stats['system2'][1]}")
        print(f"   📊 Total records: {stats['system2'][2]:,}")
        print(f"   🔌 Feedin records: {stats['system2'][3]:,}")
        print(f"   💡 Consume records: {stats['system2'][4]:,}")
        
        print(f"\n✅ Import completed: System1 +{system1_imported}, System2 +{system2_imported}")
        
        # Check if we have complete data
        if (stats['system1'][3] > 0 and stats['system1'][4] > 0 and 
            stats['system2'][3] > 0 and stats['system2'][4] > 0):
            print("\n🎉 SUCCESS: Both systems have complete consumption data from 3/2024 to present!")
        else:
            print("\n⚠️  WARNING: Some consumption data may be missing")
    
    logger.info("🎉 Simple consumption data import completed!")
