#!/usr/bin/env python3
"""
Import Raw Consumption Data to Database
Imports consumption data from CSV files to solax_data and solax_data2 tables
Date: June 2025
"""

import pandas as pd
import psycopg2
import os
import glob
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

def get_db_connection():
    """Get database connection"""
    return psycopg2.connect(**DB_CONFIG)

def process_csv_file(file_path, system_id):
    """Process a single CSV file and return DataFrame"""
    try:
        logger.info(f"Processing {file_path} for {system_id}")

        # Read CSV with proper encoding - handle the special format
        with open(file_path, 'r', encoding='utf-8-sig') as f:
            first_line = f.readline().strip()

        # Check if it's the problematic format (all in quotes)
        if first_line.startswith('"No.,Update time,'):
            # Parse the special format
            import csv
            data_rows = []

            with open(file_path, 'r', encoding='utf-8-sig') as f:
                reader = csv.reader(f)
                header_row = next(reader)

                # Extract column names from the quoted header
                if len(header_row) == 1:
                    # Split the single quoted string
                    columns = header_row[0].split(',')
                else:
                    columns = header_row

                # Process data rows
                for row in reader:
                    if len(row) == 1:
                        # Split the single quoted string
                        values = row[0].split(',')
                        data_rows.append(values)
                    else:
                        data_rows.append(row)

            # Create DataFrame
            df = pd.DataFrame(data_rows, columns=columns)

        else:
            # Normal CSV format
            df = pd.read_csv(file_path, encoding='utf-8-sig')

        # Clean column names
        df.columns = df.columns.str.strip()

        # Check if we have the expected structure
        if 'Update time' not in df.columns:
            logger.warning(f"No 'Update time' column in {file_path}")
            return None
        
        # Convert timestamp
        df['timestamp'] = pd.to_datetime(df['Update time'])

        # Convert numeric columns
        numeric_cols = ['Daily PV Yield(kWh)', 'Daily inverter output (kWh)',
                       'Daily exported energy(kWh)', 'Daily imported energy(kWh)',
                       'Export power(W)', 'Daily consumed(kWh)']

        for col in numeric_cols:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # Rename columns to match database schema
        # Note: "Daily inverter output (kWh)" is actually cumulative yield
        # "Daily consumed(kWh)" is actually cumulative consumption
        df = df.rename(columns={
            'Daily PV Yield(kWh)': 'daily_pv_yield',  # Usually 0
            'Daily inverter output (kWh)': 'cumulative_yield',  # This is the real yield
            'Daily exported energy(kWh)': 'daily_exported_energy',  # Usually 0
            'Daily imported energy(kWh)': 'daily_imported_energy',  # Usually 0
            'Export power(W)': 'feedin_power',  # Real feedin power
            'Daily consumed(kWh)': 'cumulative_consumption'  # Real consumption
        })

        # Calculate daily values from cumulative
        df = df.sort_values('timestamp')
        df['yield_today'] = df['cumulative_yield'].diff().fillna(df['cumulative_yield'])
        df['consume_energy'] = df['cumulative_consumption'].diff().fillna(df['cumulative_consumption'])

        # Handle negative values (resets at midnight)
        df.loc[df['yield_today'] < 0, 'yield_today'] = df['cumulative_yield']
        df.loc[df['consume_energy'] < 0, 'consume_energy'] = df['cumulative_consumption']

        # Calculate feedin_energy from feedin_power (approximate)
        df['feedin_energy'] = df['feedin_power'] * 5 / 60 / 1000  # 5-minute intervals, W to kWh

        # Add system identifier
        df['system_id'] = system_id

        # Filter out zero/null records and keep only meaningful data
        df = df[df['cumulative_yield'] > 0]
        
        logger.info(f"Processed {len(df)} valid records from {file_path}")
        return df
        
    except Exception as e:
        logger.error(f"Error processing {file_path}: {e}")
        return None

def import_system1_data():
    """Import System1 data to solax_data table"""
    try:
        conn = get_db_connection()
        cur = conn.cursor()
        
        # Get all System1 CSV files
        csv_files = glob.glob('/home/<USER>/solar-prediction-project/data/raw/System1/*.csv')
        
        total_imported = 0
        
        for csv_file in sorted(csv_files):
            df = process_csv_file(csv_file, 'system1')
            if df is None:
                continue
            
            # Import to database
            for _, row in df.iterrows():
                try:
                    # Check if record already exists
                    cur.execute("""
                        SELECT COUNT(*) FROM solax_data 
                        WHERE timestamp = %s
                    """, (row['timestamp'],))
                    
                    if cur.fetchone()[0] == 0:
                        # Insert new record
                        cur.execute("""
                            INSERT INTO solax_data (
                                timestamp, yield_today, feedin_power, 
                                feedin_energy, consume_energy
                            ) VALUES (%s, %s, %s, %s, %s)
                            ON CONFLICT (timestamp) DO UPDATE SET
                                feedin_power = EXCLUDED.feedin_power,
                                feedin_energy = EXCLUDED.feedin_energy,
                                consume_energy = EXCLUDED.consume_energy
                        """, (
                            row['timestamp'], row['yield_today'], 
                            row['feedin_power'], row['feedin_energy'], 
                            row['consume_energy']
                        ))
                        total_imported += 1
                    else:
                        # Update existing record with consumption data
                        cur.execute("""
                            UPDATE solax_data SET
                                feedin_power = COALESCE(feedin_power, %s),
                                feedin_energy = COALESCE(feedin_energy, %s),
                                consume_energy = COALESCE(consume_energy, %s)
                            WHERE timestamp = %s
                        """, (
                            row['feedin_power'], row['feedin_energy'], 
                            row['consume_energy'], row['timestamp']
                        ))
                        
                except Exception as e:
                    logger.error(f"Error inserting System1 record: {e}")
                    continue
        
        conn.commit()
        conn.close()
        
        logger.info(f"System1: Imported/updated {total_imported} records")
        return total_imported
        
    except Exception as e:
        logger.error(f"Error importing System1 data: {e}")
        return 0

def import_system2_data():
    """Import System2 data to solax_data2 table"""
    try:
        conn = get_db_connection()
        cur = conn.cursor()
        
        # Get all System2 CSV files
        csv_files = glob.glob('/home/<USER>/solar-prediction-project/data/raw/System2/*.csv')
        
        total_imported = 0
        
        for csv_file in sorted(csv_files):
            df = process_csv_file(csv_file, 'system2')
            if df is None:
                continue
            
            # Import to database
            for _, row in df.iterrows():
                try:
                    # Check if record already exists
                    cur.execute("""
                        SELECT COUNT(*) FROM solax_data2 
                        WHERE timestamp = %s
                    """, (row['timestamp'],))
                    
                    if cur.fetchone()[0] == 0:
                        # Insert new record
                        cur.execute("""
                            INSERT INTO solax_data2 (
                                timestamp, yield_today, feedin_power, 
                                feedin_energy, consume_energy, daily_exported_energy,
                                daily_imported_energy, daily_consumed
                            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                            ON CONFLICT (timestamp) DO UPDATE SET
                                feedin_power = EXCLUDED.feedin_power,
                                feedin_energy = EXCLUDED.feedin_energy,
                                consume_energy = EXCLUDED.consume_energy,
                                daily_exported_energy = EXCLUDED.daily_exported_energy,
                                daily_imported_energy = EXCLUDED.daily_imported_energy,
                                daily_consumed = EXCLUDED.daily_consumed
                        """, (
                            row['timestamp'], row['yield_today'], 
                            row['feedin_power'], row['feedin_energy'], 
                            row['consume_energy'], row['daily_exported_energy'],
                            row['daily_imported_energy'], row['daily_consumed']
                        ))
                        total_imported += 1
                    else:
                        # Update existing record with consumption data
                        cur.execute("""
                            UPDATE solax_data2 SET
                                feedin_power = COALESCE(feedin_power, %s),
                                feedin_energy = COALESCE(feedin_energy, %s),
                                consume_energy = COALESCE(consume_energy, %s),
                                daily_exported_energy = COALESCE(daily_exported_energy, %s),
                                daily_imported_energy = COALESCE(daily_imported_energy, %s),
                                daily_consumed = COALESCE(daily_consumed, %s)
                            WHERE timestamp = %s
                        """, (
                            row['feedin_power'], row['feedin_energy'], 
                            row['consume_energy'], row['daily_exported_energy'],
                            row['daily_imported_energy'], row['daily_consumed'],
                            row['timestamp']
                        ))
                        
                except Exception as e:
                    logger.error(f"Error inserting System2 record: {e}")
                    continue
        
        conn.commit()
        conn.close()
        
        logger.info(f"System2: Imported/updated {total_imported} records")
        return total_imported
        
    except Exception as e:
        logger.error(f"Error importing System2 data: {e}")
        return 0

def verify_data_completeness():
    """Verify data completeness from March 2024 to present"""
    try:
        conn = get_db_connection()
        cur = conn.cursor()
        
        # Check System1 data completeness
        cur.execute("""
            SELECT 
                MIN(timestamp) as earliest,
                MAX(timestamp) as latest,
                COUNT(*) as total_records,
                COUNT(CASE WHEN feedin_power IS NOT NULL THEN 1 END) as feedin_records,
                COUNT(CASE WHEN consume_energy IS NOT NULL THEN 1 END) as consume_records
            FROM solax_data 
            WHERE timestamp >= '2024-03-01'
        """)
        
        system1_stats = cur.fetchone()
        
        # Check System2 data completeness
        cur.execute("""
            SELECT 
                MIN(timestamp) as earliest,
                MAX(timestamp) as latest,
                COUNT(*) as total_records,
                COUNT(CASE WHEN feedin_power IS NOT NULL THEN 1 END) as feedin_records,
                COUNT(CASE WHEN consume_energy IS NOT NULL THEN 1 END) as consume_records
            FROM solax_data2 
            WHERE timestamp >= '2024-03-01'
        """)
        
        system2_stats = cur.fetchone()
        
        conn.close()
        
        return {
            'system1': {
                'earliest': system1_stats[0],
                'latest': system1_stats[1],
                'total_records': system1_stats[2],
                'feedin_records': system1_stats[3],
                'consume_records': system1_stats[4]
            },
            'system2': {
                'earliest': system2_stats[0],
                'latest': system2_stats[1],
                'total_records': system2_stats[2],
                'feedin_records': system2_stats[3],
                'consume_records': system2_stats[4]
            }
        }
        
    except Exception as e:
        logger.error(f"Error verifying data completeness: {e}")
        return None

if __name__ == "__main__":
    logger.info("🚀 Starting raw consumption data import...")
    
    # Import System1 data
    system1_imported = import_system1_data()
    
    # Import System2 data  
    system2_imported = import_system2_data()
    
    # Verify completeness
    stats = verify_data_completeness()
    
    if stats:
        print("\n📊 DATA COMPLETENESS VERIFICATION:")
        print("=" * 50)
        
        print(f"\n🏠 SYSTEM1 (solax_data):")
        print(f"   📅 Period: {stats['system1']['earliest']} to {stats['system1']['latest']}")
        print(f"   📊 Total records: {stats['system1']['total_records']:,}")
        print(f"   🔌 Feedin records: {stats['system1']['feedin_records']:,}")
        print(f"   💡 Consume records: {stats['system1']['consume_records']:,}")
        
        print(f"\n🏠 SYSTEM2 (solax_data2):")
        print(f"   📅 Period: {stats['system2']['earliest']} to {stats['system2']['latest']}")
        print(f"   📊 Total records: {stats['system2']['total_records']:,}")
        print(f"   🔌 Feedin records: {stats['system2']['feedin_records']:,}")
        print(f"   💡 Consume records: {stats['system2']['consume_records']:,}")
        
        print(f"\n✅ Import completed: System1 +{system1_imported}, System2 +{system2_imported}")
    
    logger.info("🎉 Raw consumption data import completed!")
