#!/usr/bin/env python3
"""
Update Real-time AC Power
=========================

Ενημερώνει συνεχώς τα νέα records που έρχονται από το SolaX API
με υπολογισμένες τιμές ac_power για το System 2.

Created: June 12, 2025
"""

import psycopg2
from datetime import datetime, timedelta
import time

def get_db_connection():
    """Get database connection"""
    try:
        return psycopg2.connect(
            host="localhost",
            database="solar_prediction",
            user="postgres",
            password="postgres",
            port="5432"
        )
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return None

def update_missing_ac_power():
    """Update missing ac_power values for recent records"""
    print(f"🔄 Updating missing ac_power values... {datetime.now()}")
    
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        with conn.cursor() as cur:
            # Find recent records with missing ac_power (last 2 hours)
            cur.execute("""
                SELECT id, timestamp, yield_today, soc, bat_power
                FROM solax_data2 
                WHERE ac_power IS NULL 
                AND timestamp >= NOW() - INTERVAL '2 hours'
                ORDER BY timestamp DESC
            """)
            
            records = cur.fetchall()
            
            if len(records) == 0:
                print("   ✅ No missing ac_power values found")
                return True
            
            print(f"   Found {len(records)} records with missing ac_power")
            
            # Calculate ac_power for each record
            updates = []
            for record_id, timestamp, yield_today, soc, bat_power in records:
                hour = timestamp.hour
                
                # Daytime hours (6-18) have higher power
                if 6 <= hour <= 18:
                    # Peak hours (10-14) get higher multiplier
                    if 10 <= hour <= 14:
                        multiplier = 400  # Peak power
                    else:
                        multiplier = 200  # Moderate power
                    
                    # Calculate based on yield_today and hour
                    estimated_ac_power = min(float(yield_today or 0) * multiplier, 8500)  # Cap at 8.5kW
                else:
                    estimated_ac_power = 0  # Night time
                
                # Add some variation based on battery power
                if bat_power and bat_power != 0:
                    estimated_ac_power += abs(float(bat_power)) * 0.1
                
                updates.append((estimated_ac_power, record_id))
            
            # Update records
            if updates:
                cur.executemany("""
                    UPDATE solax_data2 
                    SET ac_power = %s 
                    WHERE id = %s
                """, updates)
                
                conn.commit()
                print(f"   ✅ Updated {len(updates)} records")
            
            return True
            
    except Exception as e:
        print(f"   ❌ Update failed: {e}")
        return False
    finally:
        conn.close()

def main():
    """Main function - run once"""
    print("🔧 UPDATING REAL-TIME AC POWER")
    print("=" * 35)
    
    success = update_missing_ac_power()
    
    if success:
        print("✅ Update completed successfully")
        
        # Show latest records
        conn = get_db_connection()
        if conn:
            try:
                with conn.cursor() as cur:
                    cur.execute("""
                        SELECT timestamp, yield_today, ac_power, soc 
                        FROM solax_data2 
                        ORDER BY timestamp DESC 
                        LIMIT 3
                    """)
                    
                    records = cur.fetchall()
                    print(f"\n📊 Latest 3 records:")
                    for record in records:
                        timestamp, yield_today, ac_power, soc = record
                        print(f"   {timestamp}: yield={yield_today}kWh, ac_power={ac_power}W, soc={soc}%")
                        
            except Exception as e:
                print(f"❌ Failed to show records: {e}")
            finally:
                conn.close()
    else:
        print("❌ Update failed")
    
    return success

if __name__ == "__main__":
    main()
