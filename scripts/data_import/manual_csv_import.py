#!/usr/bin/env python3
"""
Manual CSV Import - No external dependencies except psycopg2
Imports System2 consumption data from CSV files
Date: June 2025
"""

import csv
import psycopg2
import os
import glob
from datetime import datetime

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 5433,  # Docker external port
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

def parse_csv_line(line):
    """Parse a CSV line that's wrapped in quotes"""
    # Remove BOM and quotes
    line = line.strip().strip('\ufeff').strip('"')
    
    # Split by comma
    parts = line.split(',')
    
    return parts

def process_csv_file(file_path):
    """Process a CSV file and return data rows"""
    print(f"Processing {file_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8-sig') as f:
            lines = f.readlines()
        
        if not lines:
            print(f"Empty file: {file_path}")
            return []
        
        # Parse header
        header_line = lines[0]
        header_parts = parse_csv_line(header_line)
        
        print(f"Header: {header_parts}")
        
        # Expected columns
        expected_cols = ['No.', 'Update time', 'Daily PV Yield(kWh)', 
                        'Daily inverter output (kWh)', 'Daily exported energy(kWh)',
                        'Daily imported energy(kWh)', 'Export power(W)', 'Daily consumed(kWh)']
        
        if len(header_parts) != len(expected_cols):
            print(f"Header mismatch. Expected {len(expected_cols)}, got {len(header_parts)}")
            return []
        
        # Process data lines
        data_rows = []
        prev_yield = 0
        prev_consumption = 0
        
        for i, line in enumerate(lines[1:], 1):
            if not line.strip():
                continue
                
            try:
                parts = parse_csv_line(line)
                
                if len(parts) != len(expected_cols):
                    continue
                
                # Extract values
                timestamp_str = parts[1]  # Update time
                daily_pv_yield = float(parts[2]) if parts[2] else 0  # Usually 0
                cumulative_yield = float(parts[3]) if parts[3] else 0  # Daily inverter output
                daily_exported = float(parts[4]) if parts[4] else 0  # Usually 0
                daily_imported = float(parts[5]) if parts[5] else 0  # Usually 0
                export_power = float(parts[6]) if parts[6] else 0  # Export power(W)
                cumulative_consumption = float(parts[7]) if parts[7] else 0  # Daily consumed
                
                # Skip zero records
                if cumulative_yield <= 0:
                    continue
                
                # Parse timestamp
                try:
                    timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
                except:
                    continue
                
                # Calculate daily values from cumulative
                yield_today = cumulative_yield - prev_yield if cumulative_yield >= prev_yield else cumulative_yield
                consume_energy = cumulative_consumption - prev_consumption if cumulative_consumption >= prev_consumption else cumulative_consumption
                
                # Update previous values
                prev_yield = cumulative_yield
                prev_consumption = cumulative_consumption
                
                # Calculate feedin_energy from power (approximate)
                feedin_energy = export_power * 5 / 60 / 1000  # 5-minute intervals, W to kWh
                
                data_row = {
                    'timestamp': timestamp,
                    'yield_today': yield_today,
                    'feedin_power': export_power,
                    'feedin_energy': feedin_energy,
                    'consume_energy': consume_energy
                }
                
                data_rows.append(data_row)
                
            except Exception as e:
                print(f"Error processing line {i}: {e}")
                continue
        
        print(f"Processed {len(data_rows)} valid records from {file_path}")
        return data_rows
        
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return []

def import_to_database(data_rows):
    """Import data to solax_data2 table"""
    if not data_rows:
        print("No data to import")
        return 0
    
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()
        
        imported_count = 0
        updated_count = 0
        
        for row in data_rows:
            try:
                # Check if record exists
                cur.execute("""
                    SELECT COUNT(*) FROM solax_data2 
                    WHERE timestamp = %s
                """, (row['timestamp'],))
                
                exists = cur.fetchone()[0] > 0
                
                if not exists:
                    # Insert new record
                    cur.execute("""
                        INSERT INTO solax_data2 (
                            timestamp, yield_today, feedin_power, 
                            feedin_energy, consume_energy
                        ) VALUES (%s, %s, %s, %s, %s)
                    """, (
                        row['timestamp'], row['yield_today'], 
                        row['feedin_power'], row['feedin_energy'], 
                        row['consume_energy']
                    ))
                    imported_count += 1
                else:
                    # Update existing record
                    cur.execute("""
                        UPDATE solax_data2 SET
                            feedin_power = COALESCE(feedin_power, %s),
                            feedin_energy = COALESCE(feedin_energy, %s),
                            consume_energy = COALESCE(consume_energy, %s)
                        WHERE timestamp = %s
                    """, (
                        row['feedin_power'], row['feedin_energy'], 
                        row['consume_energy'], row['timestamp']
                    ))
                    updated_count += 1
                    
            except Exception as e:
                print(f"Error inserting record: {e}")
                continue
        
        conn.commit()
        conn.close()
        
        print(f"Database import: {imported_count} new, {updated_count} updated")
        return imported_count + updated_count
        
    except Exception as e:
        print(f"Database error: {e}")
        return 0

def verify_import():
    """Verify the import results"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()
        
        # Check System2 data
        cur.execute("""
            SELECT 
                MIN(timestamp) as earliest,
                MAX(timestamp) as latest,
                COUNT(*) as total_records,
                COUNT(CASE WHEN feedin_power IS NOT NULL THEN 1 END) as feedin_records,
                COUNT(CASE WHEN consume_energy IS NOT NULL THEN 1 END) as consume_records
            FROM solax_data2 
            WHERE timestamp >= '2024-03-01'
        """)
        
        stats = cur.fetchone()
        conn.close()
        
        return stats
        
    except Exception as e:
        print(f"Verification error: {e}")
        return None

if __name__ == "__main__":
    print("🚀 Starting manual CSV import for System2...")
    
    # Get all System2 CSV files
    csv_files = glob.glob('/home/<USER>/solar-prediction-project/data/raw/System2/*.csv')
    
    if not csv_files:
        print("❌ No CSV files found")
        exit(1)
    
    print(f"Found {len(csv_files)} CSV files")
    
    all_data = []
    
    # Process each CSV file
    for csv_file in sorted(csv_files):
        data = process_csv_file(csv_file)
        all_data.extend(data)
    
    print(f"\nTotal records to import: {len(all_data)}")
    
    if all_data:
        # Remove duplicates by timestamp
        unique_data = {}
        for row in all_data:
            timestamp_key = row['timestamp'].isoformat()
            if timestamp_key not in unique_data:
                unique_data[timestamp_key] = row
        
        unique_list = list(unique_data.values())
        print(f"Unique records after deduplication: {len(unique_list)}")
        
        # Import to database
        imported = import_to_database(unique_list)
        
        # Verify results
        stats = verify_import()
        
        if stats:
            print(f"\n📊 IMPORT RESULTS:")
            print(f"   📅 Period: {stats[0]} to {stats[1]}")
            print(f"   📊 Total records: {stats[2]:,}")
            print(f"   🔌 Feedin records: {stats[3]:,}")
            print(f"   💡 Consume records: {stats[4]:,}")
            
            if stats[3] > 0 and stats[4] > 0:
                print(f"\n✅ SUCCESS: System2 now has consumption data from 3/2024 to present!")
            else:
                print(f"\n⚠️  WARNING: Some consumption data may be missing")
        
        print(f"\n🎉 Manual import completed: {imported} records processed")
    else:
        print("❌ No valid data found to import")
