#!/usr/bin/env python3
"""
Direct Database Import
Import CSV data directly to PostgreSQL using psycopg2
"""

import csv
import os
import psycopg2
from datetime import datetime

def get_db_connection():
    """Get database connection"""
    try:
        return psycopg2.connect(
            host="localhost",
            database="solar_prediction",
            user="postgres",
            password="postgres",
            port="5432"
        )
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return None

def import_csv_to_db():
    """Import CSV directly to database"""
    print("🚀 DIRECT DATABASE IMPORT")
    print("=" * 30)
    
    csv_file = "data/raw/System2/Plant Reports 2024-03-01-2024-06-28.csv"
    
    if not os.path.exists(csv_file):
        print(f"❌ CSV file not found: {csv_file}")
        return False
    
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        with conn.cursor() as cur:
            # Drop and recreate table to avoid constraints
            cur.execute("DROP TABLE IF EXISTS solax_data2")

            # Create fresh table
            cur.execute("""
                CREATE TABLE solax_data2 (
                    id SERIAL PRIMARY KEY,
                    timestamp TIMESTAMP NOT NULL,
                    inverter_sn VARCHAR(50),
                    wifi_sn VARCHAR(50),
                    yield_today DECIMAL(10,2),
                    soc DECIMAL(5,2),
                    bat_power DECIMAL(10,2),
                    temperature DECIMAL(5,2),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create index
            cur.execute("""
                CREATE INDEX IF NOT EXISTS idx_solax_data2_timestamp 
                ON solax_data2(timestamp)
            """)
            
            print(f"✅ Fresh table created")
            
            # Read and process CSV
            with open(csv_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            print(f"📊 Processing {len(lines)} lines from CSV...")
            
            # Skip header
            data_lines = lines[1:]
            
            valid_records = 0
            batch_size = 1000
            batch_data = []
            
            for line in data_lines:
                line = line.strip()
                if not line:
                    continue
                
                try:
                    # Parse CSV line
                    parts = line.split(',')
                    
                    if len(parts) >= 4:
                        # Extract timestamp and yield
                        timestamp_str = parts[1].strip('"')
                        yield_str = parts[3].strip('"')
                        
                        # Validate
                        dt = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
                        yield_val = float(yield_str)
                        
                        # Add to batch
                        batch_data.append((
                            dt,
                            'SYSTEM2_CSV_IMPORT',
                            'SYSTEM2_CSV_IMPORT',
                            yield_val,
                            50.0,  # soc
                            0.0,   # bat_power
                            20.0   # temperature
                        ))
                        
                        valid_records += 1
                        
                        # Insert batch when full
                        if len(batch_data) >= batch_size:
                            insert_batch(cur, batch_data)
                            batch_data = []
                            print(f"   Inserted {valid_records} records...")
                
                except (ValueError, IndexError):
                    continue
            
            # Insert remaining batch
            if batch_data:
                insert_batch(cur, batch_data)
            
            # Commit transaction
            conn.commit()
            
            print(f"✅ Import completed: {valid_records} records")
            
            # Verify import
            cur.execute("""
                SELECT 
                    COUNT(*) as total,
                    MIN(DATE(timestamp)) as earliest,
                    MAX(DATE(timestamp)) as latest,
                    COUNT(DISTINCT DATE(timestamp)) as unique_days
                FROM solax_data2 
                WHERE inverter_sn = 'SYSTEM2_CSV_IMPORT'
            """)
            
            result = cur.fetchone()
            print(f"\n📊 Verification:")
            print(f"   Total records: {result[0]}")
            print(f"   Date range: {result[1]} to {result[2]}")
            print(f"   Unique days: {result[3]}")
            
            # Check April 2024 specifically
            cur.execute("""
                SELECT 
                    COUNT(*) as april_records,
                    MIN(DATE(timestamp)) as first_date,
                    MAX(DATE(timestamp)) as last_date,
                    AVG(yield_today) as avg_yield
                FROM solax_data2 
                WHERE inverter_sn = 'SYSTEM2_CSV_IMPORT'
                    AND EXTRACT(year FROM timestamp) = 2024
                    AND EXTRACT(month FROM timestamp) = 4
            """)
            
            april_result = cur.fetchone()
            print(f"\n📅 April 2024 Data:")
            print(f"   Records: {april_result[0]}")
            print(f"   Date range: {april_result[1]} to {april_result[2]}")
            print(f"   Average yield: {april_result[3]:.1f} kWh" if april_result[3] else "No data")
            
            return april_result[0] > 0  # Return True if April data exists
            
    except Exception as e:
        print(f"❌ Import failed: {e}")
        conn.rollback()
        return False
    
    finally:
        conn.close()

def insert_batch(cursor, batch_data):
    """Insert batch of data"""
    insert_query = """
        INSERT INTO solax_data2 (
            timestamp, inverter_sn, wifi_sn, yield_today, 
            soc, bat_power, temperature
        ) VALUES (%s, %s, %s, %s, %s, %s, %s)
    """
    
    cursor.executemany(insert_query, batch_data)

def main():
    """Main function"""
    print("🚀 DIRECT DATABASE IMPORT - SYSTEM 2 CSV")
    print("=" * 50)
    
    success = import_csv_to_db()
    
    if success:
        print("\n🎉 IMPORT SUCCESSFUL!")
        print("✅ April 2024 data is now available")
        print("🔄 Running April comparison...")
        
        # Run the comparison
        os.system("python3 scripts/final_april_comparison.py")
    else:
        print("\n❌ IMPORT FAILED!")
    
    return success

if __name__ == "__main__":
    main()
