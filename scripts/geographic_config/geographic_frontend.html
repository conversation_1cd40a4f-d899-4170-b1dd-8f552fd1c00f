<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Solar System Geographic Configuration</title>
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .container-fluid {
            padding: 20px;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: none;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .card-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
            padding: 20px;
        }
        
        #map {
            height: 500px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #4facfe;
            box-shadow: 0 0 0 0.2rem rgba(79, 172, 254, 0.25);
        }
        
        .btn {
            border-radius: 10px;
            padding: 10px 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border: none;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            border: none;
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border: none;
        }
        
        .system-card {
            transition: transform 0.3s ease;
            cursor: pointer;
        }
        
        .system-card:hover {
            transform: translateY(-5px);
        }
        
        .coordinate-display {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
        }
        
        .status-badge {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: 600;
        }
        
        .status-active {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
        }
        
        .status-inactive {
            background: linear-gradient(135deg, #bdc3c7 0%, #95a5a6 100%);
            color: white;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .spinner-border {
            color: #4facfe;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .table th {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            font-weight: 600;
        }
        
        .modal-content {
            border-radius: 15px;
            border: none;
        }
        
        .modal-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            border: none;
        }
        
        .form-label {
            font-weight: 600;
            color: #495057;
        }
        
        .input-group-text {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            border-radius: 10px 0 0 10px;
        }
        
        .leaflet-popup-content {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .leaflet-popup-content h6 {
            color: #4facfe;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        @media (max-width: 768px) {
            .container-fluid {
                padding: 10px;
            }
            
            #map {
                height: 300px;
            }
            
            .card-header h4 {
                font-size: 1.2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header text-center">
                        <h2>🌍 Solar System Geographic Configuration</h2>
                        <p class="mb-0">Centralized management for solar prediction systems worldwide</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="row">
            <!-- Map Section -->
            <div class="col-lg-8 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h4>🗺️ Interactive System Map</h4>
                        <small>Click on the map to add a new system or drag existing markers to update locations</small>
                    </div>
                    <div class="card-body">
                        <div id="map"></div>
                        
                        <!-- Coordinate Display -->
                        <div class="coordinate-display mt-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>📍 Selected Location:</strong><br>
                                    <span id="selected-coords">Click on map to select location</span>
                                </div>
                                <div class="col-md-6">
                                    <strong>🌐 Location Details:</strong><br>
                                    <span id="location-details">No location selected</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Configuration -->
            <div class="col-lg-4 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h4>⚙️ System Configuration</h4>
                    </div>
                    <div class="card-body">
                        <form id="system-form">
                            <div class="mb-3">
                                <label for="system-name" class="form-label">System Name</label>
                                <input type="text" class="form-control" id="system-name" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="display-name" class="form-label">Display Name</label>
                                <input type="text" class="form-control" id="display-name" required>
                            </div>
                            
                            <div class="row">
                                <div class="col-6 mb-3">
                                    <label for="latitude" class="form-label">Latitude</label>
                                    <input type="number" class="form-control" id="latitude" step="0.000001" readonly>
                                </div>
                                <div class="col-6 mb-3">
                                    <label for="longitude" class="form-label">Longitude</label>
                                    <input type="number" class="form-control" id="longitude" step="0.000001" readonly>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="peak-power" class="form-label">Peak Power (kW)</label>
                                <div class="input-group">
                                    <span class="input-group-text">⚡</span>
                                    <input type="number" class="form-control" id="peak-power" step="0.1" required>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="panel-type" class="form-label">Panel Type</label>
                                <select class="form-select" id="panel-type">
                                    <option value="">Select panel type</option>
                                    <option value="Monocrystalline">Monocrystalline</option>
                                    <option value="Polycrystalline">Polycrystalline</option>
                                    <option value="Thin Film">Thin Film</option>
                                    <option value="Bifacial">Bifacial</option>
                                </select>
                            </div>
                            
                            <div class="row">
                                <div class="col-6 mb-3">
                                    <label for="tilt-angle" class="form-label">Tilt Angle (°)</label>
                                    <input type="number" class="form-control" id="tilt-angle" min="0" max="90" step="0.1">
                                </div>
                                <div class="col-6 mb-3">
                                    <label for="azimuth" class="form-label">Azimuth (°)</label>
                                    <input type="number" class="form-control" id="azimuth" min="0" max="359" step="0.1">
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="battery-capacity" class="form-label">Battery Capacity (kWh)</label>
                                <div class="input-group">
                                    <span class="input-group-text">🔋</span>
                                    <input type="number" class="form-control" id="battery-capacity" step="0.1">
                                </div>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Save System Configuration
                                </button>
                                <button type="button" class="btn btn-warning" id="update-btn" style="display: none;">
                                    <i class="fas fa-edit"></i> Update System
                                </button>
                            </div>
                        </form>
                        
                        <div class="loading" id="form-loading">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p>Processing request...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Systems List -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4>📋 Configured Systems</h4>
                        <button class="btn btn-success float-end" id="refresh-btn">
                            <i class="fas fa-sync"></i> Refresh
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="loading" id="table-loading">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p>Loading systems...</p>
                        </div>
                        
                        <div class="table-responsive" id="systems-table-container" style="display: none;">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>System Name</th>
                                        <th>Display Name</th>
                                        <th>Location</th>
                                        <th>Peak Power</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="systems-table-body">
                                </tbody>
                            </table>
                        </div>
                        
                        <div id="no-systems" style="display: none;" class="text-center py-4">
                            <h5>No systems configured</h5>
                            <p>Click on the map to add your first solar system</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert Container -->
    <div id="alert-container" style="position: fixed; top: 20px; right: 20px; z-index: 9999; width: 300px;"></div>

    <!-- Scripts -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
    
    <script>
        // Global variables
        let map;
        let markers = {};
        let selectedLocation = null;
        let editingSystem = null;
        
        // API base URL
        const API_BASE = 'http://localhost:8001';
        
        // Initialize map
        function initMap() {
            // Default location (Marathon, Greece)
            const defaultLat = 38.141348;
            const defaultLng = 24.007165;
            
            map = L.map('map').setView([defaultLat, defaultLng], 13);
            
            // Add tile layer
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(map);
            
            // Add click handler
            map.on('click', onMapClick);
            
            // Load existing systems
            loadSystems();
        }
        
        // Handle map clicks
        function onMapClick(e) {
            const lat = e.latlng.lat;
            const lng = e.latlng.lng;
            
            selectedLocation = { lat, lng };
            updateCoordinateDisplay(lat, lng);
            
            // Update form
            document.getElementById('latitude').value = lat.toFixed(6);
            document.getElementById('longitude').value = lng.toFixed(6);
            
            // Get location details
            getLocationDetails(lat, lng);
        }
        
        // Update coordinate display
        function updateCoordinateDisplay(lat, lng) {
            document.getElementById('selected-coords').textContent = 
                `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
        }
        
        // Get location details from API
        async function getLocationDetails(lat, lng) {
            try {
                const response = await fetch(`${API_BASE}/geocode?latitude=${lat}&longitude=${lng}`);
                const data = await response.json();
                
                let details = '';
                if (data.city) details += data.city + ', ';
                if (data.region) details += data.region + ', ';
                if (data.country_code) details += data.country_code;
                if (data.timezone) details += ` (${data.timezone})`;
                
                document.getElementById('location-details').textContent = 
                    details || 'Location details not available';
                    
            } catch (error) {
                console.error('Error getting location details:', error);
                document.getElementById('location-details').textContent = 
                    'Error getting location details';
            }
        }
        
        // Load systems from API
        async function loadSystems() {
            showLoading('table-loading');
            
            try {
                const response = await fetch(`${API_BASE}/systems`);
                const systems = await response.json();
                
                // Clear existing markers
                Object.values(markers).forEach(marker => map.removeLayer(marker));
                markers = {};
                
                // Add markers for each system
                systems.forEach(system => {
                    addSystemMarker(system);
                });
                
                // Update table
                updateSystemsTable(systems);
                
                hideLoading('table-loading');
                
                if (systems.length === 0) {
                    document.getElementById('no-systems').style.display = 'block';
                    document.getElementById('systems-table-container').style.display = 'none';
                } else {
                    document.getElementById('no-systems').style.display = 'none';
                    document.getElementById('systems-table-container').style.display = 'block';
                }
                
            } catch (error) {
                console.error('Error loading systems:', error);
                showAlert('Error loading systems', 'danger');
                hideLoading('table-loading');
            }
        }
        
        // Add system marker to map
        function addSystemMarker(system) {
            const marker = L.marker([system.latitude, system.longitude], {
                draggable: true
            }).addTo(map);
            
            // Popup content
            const popupContent = `
                <div>
                    <h6>${system.display_name}</h6>
                    <p><strong>System:</strong> ${system.system_name}</p>
                    <p><strong>Power:</strong> ${system.peak_power_kw} kW</p>
                    <p><strong>Location:</strong> ${system.latitude.toFixed(4)}, ${system.longitude.toFixed(4)}</p>
                    <button class="btn btn-sm btn-primary" onclick="editSystem('${system.system_name}')">
                        Edit System
                    </button>
                </div>
            `;
            
            marker.bindPopup(popupContent);
            
            // Handle marker drag
            marker.on('dragend', function(e) {
                const newPos = e.target.getLatLng();
                updateSystemLocation(system.system_name, newPos.lat, newPos.lng);
            });
            
            markers[system.system_name] = marker;
        }
        
        // Update systems table
        function updateSystemsTable(systems) {
            const tbody = document.getElementById('systems-table-body');
            tbody.innerHTML = '';
            
            systems.forEach(system => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${system.system_name}</td>
                    <td>${system.display_name}</td>
                    <td>${system.latitude.toFixed(4)}, ${system.longitude.toFixed(4)}</td>
                    <td>${system.peak_power_kw} kW</td>
                    <td>
                        <span class="status-badge status-${system.status}">
                            ${system.status.toUpperCase()}
                        </span>
                    </td>
                    <td>
                        <button class="btn btn-sm btn-primary" onclick="editSystem('${system.system_name}')">
                            Edit
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="deleteSystem('${system.system_name}')">
                            Delete
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }
        
        // Show loading spinner
        function showLoading(elementId) {
            document.getElementById(elementId).style.display = 'block';
        }
        
        // Hide loading spinner
        function hideLoading(elementId) {
            document.getElementById(elementId).style.display = 'none';
        }
        
        // Show alert
        function showAlert(message, type = 'info') {
            const alertContainer = document.getElementById('alert-container');
            const alertId = 'alert-' + Date.now();
            
            const alertHtml = `
                <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            alertContainer.insertAdjacentHTML('beforeend', alertHtml);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                const alertElement = document.getElementById(alertId);
                if (alertElement) {
                    alertElement.remove();
                }
            }, 5000);
        }
        
        // Handle form submission
        document.getElementById('system-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            if (!selectedLocation && !editingSystem) {
                showAlert('Please select a location on the map', 'warning');
                return;
            }
            
            showLoading('form-loading');
            
            const formData = {
                system_name: document.getElementById('system-name').value,
                display_name: document.getElementById('display-name').value,
                latitude: parseFloat(document.getElementById('latitude').value),
                longitude: parseFloat(document.getElementById('longitude').value),
                peak_power_kw: parseFloat(document.getElementById('peak-power').value),
                panel_type: document.getElementById('panel-type').value || null,
                tilt_angle: parseFloat(document.getElementById('tilt-angle').value) || null,
                azimuth: parseFloat(document.getElementById('azimuth').value) || null,
                battery_capacity_kwh: parseFloat(document.getElementById('battery-capacity').value) || null
            };
            
            try {
                let response;
                if (editingSystem) {
                    // Update existing system
                    response = await fetch(`${API_BASE}/systems/${editingSystem}`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(formData)
                    });
                } else {
                    // Create new system
                    response = await fetch(`${API_BASE}/systems`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(formData)
                    });
                }
                
                if (response.ok) {
                    showAlert(
                        editingSystem ? 'System updated successfully' : 'System created successfully', 
                        'success'
                    );
                    resetForm();
                    loadSystems();
                } else {
                    const error = await response.json();
                    showAlert(`Error: ${error.detail}`, 'danger');
                }
                
            } catch (error) {
                console.error('Error saving system:', error);
                showAlert('Error saving system', 'danger');
            }
            
            hideLoading('form-loading');
        });
        
        // Edit system
        async function editSystem(systemName) {
            try {
                const response = await fetch(`${API_BASE}/systems/${systemName}`);
                const system = await response.json();
                
                // Populate form
                document.getElementById('system-name').value = system.system_name;
                document.getElementById('display-name').value = system.display_name;
                document.getElementById('latitude').value = system.latitude;
                document.getElementById('longitude').value = system.longitude;
                document.getElementById('peak-power').value = system.peak_power_kw;
                document.getElementById('panel-type').value = system.panel_type || '';
                document.getElementById('tilt-angle').value = system.tilt_angle || '';
                document.getElementById('azimuth').value = system.azimuth || '';
                document.getElementById('battery-capacity').value = system.battery_capacity_kwh || '';
                
                // Update coordinate display
                updateCoordinateDisplay(system.latitude, system.longitude);
                
                // Set editing mode
                editingSystem = systemName;
                document.getElementById('system-name').readOnly = true;
                document.querySelector('button[type="submit"]').style.display = 'none';
                document.getElementById('update-btn').style.display = 'block';
                
                // Center map on system
                map.setView([system.latitude, system.longitude], 15);
                
            } catch (error) {
                console.error('Error loading system for edit:', error);
                showAlert('Error loading system', 'danger');
            }
        }
        
        // Delete system
        async function deleteSystem(systemName) {
            if (!confirm(`Are you sure you want to delete system "${systemName}"?`)) {
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/systems/${systemName}`, {
                    method: 'DELETE'
                });
                
                if (response.ok) {
                    showAlert('System deleted successfully', 'success');
                    loadSystems();
                } else {
                    const error = await response.json();
                    showAlert(`Error: ${error.detail}`, 'danger');
                }
                
            } catch (error) {
                console.error('Error deleting system:', error);
                showAlert('Error deleting system', 'danger');
            }
        }
        
        // Update system location
        async function updateSystemLocation(systemName, lat, lng) {
            try {
                const response = await fetch(`${API_BASE}/systems/${systemName}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        latitude: lat,
                        longitude: lng
                    })
                });
                
                if (response.ok) {
                    showAlert('System location updated', 'success');
                    loadSystems();
                } else {
                    showAlert('Error updating location', 'danger');
                    loadSystems(); // Reload to reset marker position
                }
                
            } catch (error) {
                console.error('Error updating location:', error);
                showAlert('Error updating location', 'danger');
                loadSystems();
            }
        }
        
        // Reset form
        function resetForm() {
            document.getElementById('system-form').reset();
            document.getElementById('system-name').readOnly = false;
            document.querySelector('button[type="submit"]').style.display = 'block';
            document.getElementById('update-btn').style.display = 'none';
            editingSystem = null;
            selectedLocation = null;
            
            document.getElementById('selected-coords').textContent = 'Click on map to select location';
            document.getElementById('location-details').textContent = 'No location selected';
        }
        
        // Update button handler
        document.getElementById('update-btn').addEventListener('click', function() {
            document.getElementById('system-form').dispatchEvent(new Event('submit'));
        });
        
        // Refresh button handler
        document.getElementById('refresh-btn').addEventListener('click', loadSystems);
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initMap();
        });
    </script>
</body>
</html>
