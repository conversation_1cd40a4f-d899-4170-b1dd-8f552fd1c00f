#!/usr/bin/env python3
"""
Geographic Configuration Manager
Centralized configuration management for solar prediction systems
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any
import psycopg2
from psycopg2.extras import RealDictCursor
import json
from datetime import datetime, date
import logging
from geopy.geocoders import Nominatim
import pytz
import requests
from dataclasses import dataclass

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# FastAPI app
app = FastAPI(
    title="Solar Prediction Geographic Config API",
    description="Centralized configuration management for solar prediction systems",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': ''
}

# Pydantic models
class SystemConfigBase(BaseModel):
    system_name: str = Field(..., min_length=1, max_length=50)
    display_name: str = Field(..., min_length=1, max_length=100)
    latitude: float = Field(..., ge=-90, le=90)
    longitude: float = Field(..., ge=-180, le=180)
    elevation_m: Optional[float] = Field(None, ge=-500, le=9000)
    timezone: str = Field(default="UTC")
    peak_power_kw: float = Field(..., gt=0)
    panel_count: Optional[int] = Field(None, gt=0)
    panel_type: Optional[str] = None
    panel_efficiency: Optional[float] = Field(None, ge=0, le=1)
    inverter_type: Optional[str] = None
    inverter_efficiency: Optional[float] = Field(None, ge=0, le=1)
    tilt_angle: Optional[float] = Field(None, ge=0, le=90)
    azimuth: Optional[float] = Field(None, ge=0, lt=360)
    tracking_type: str = Field(default="fixed")
    battery_capacity_kwh: Optional[float] = Field(None, ge=0)
    battery_type: Optional[str] = None
    battery_efficiency: Optional[float] = Field(None, ge=0, le=1)
    api_keys: Optional[Dict[str, Any]] = None
    api_endpoints: Optional[Dict[str, Any]] = None
    data_sources: Optional[Dict[str, Any]] = None
    status: str = Field(default="active")
    commissioning_date: Optional[date] = None
    last_maintenance: Optional[date] = None

class SystemConfigCreate(SystemConfigBase):
    pass

class SystemConfigUpdate(BaseModel):
    display_name: Optional[str] = Field(None, min_length=1, max_length=100)
    latitude: Optional[float] = Field(None, ge=-90, le=90)
    longitude: Optional[float] = Field(None, ge=-180, le=180)
    elevation_m: Optional[float] = Field(None, ge=-500, le=9000)
    timezone: Optional[str] = None
    peak_power_kw: Optional[float] = Field(None, gt=0)
    panel_count: Optional[int] = Field(None, gt=0)
    panel_type: Optional[str] = None
    panel_efficiency: Optional[float] = Field(None, ge=0, le=1)
    inverter_type: Optional[str] = None
    inverter_efficiency: Optional[float] = Field(None, ge=0, le=1)
    tilt_angle: Optional[float] = Field(None, ge=0, le=90)
    azimuth: Optional[float] = Field(None, ge=0, lt=360)
    tracking_type: Optional[str] = None
    battery_capacity_kwh: Optional[float] = Field(None, ge=0)
    battery_type: Optional[str] = None
    battery_efficiency: Optional[float] = Field(None, ge=0, le=1)
    api_keys: Optional[Dict[str, Any]] = None
    api_endpoints: Optional[Dict[str, Any]] = None
    data_sources: Optional[Dict[str, Any]] = None
    status: Optional[str] = None
    last_maintenance: Optional[date] = None

class SystemConfigResponse(SystemConfigBase):
    id: int
    country_code: Optional[str]
    region: Optional[str]
    city: Optional[str]
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str]
    updated_by: Optional[str]

class GeographicOverride(BaseModel):
    parameter_name: str = Field(..., min_length=1, max_length=50)
    parameter_value: Dict[str, Any]
    override_type: str = Field(default="seasonal")
    valid_from: Optional[date] = None
    valid_to: Optional[date] = None
    valid_months: Optional[List[int]] = Field(None, min_items=1, max_items=12)
    valid_hours: Optional[List[int]] = Field(None, min_items=1, max_items=24)
    weather_condition: Optional[str] = None
    priority: int = Field(default=1, ge=1, le=10)
    is_active: bool = Field(default=True)
    description: Optional[str] = None

    @validator('valid_months')
    def validate_months(cls, v):
        if v is not None:
            for month in v:
                if month < 1 or month > 12:
                    raise ValueError('Months must be between 1 and 12')
        return v

    @validator('valid_hours')
    def validate_hours(cls, v):
        if v is not None:
            for hour in v:
                if hour < 0 or hour > 23:
                    raise ValueError('Hours must be between 0 and 23')
        return v

class LocationInfo(BaseModel):
    latitude: float
    longitude: float
    country_code: Optional[str] = None
    timezone: Optional[str] = None
    elevation_m: Optional[float] = None
    region: Optional[str] = None
    city: Optional[str] = None

# Database connection
def get_db_connection():
    """Get database connection"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        return conn
    except Exception as e:
        logger.error(f"Database connection failed: {e}")
        raise HTTPException(status_code=500, detail="Database connection failed")

# Geocoding service
class GeocodingService:
    """Service for geocoding and reverse geocoding"""
    
    def __init__(self):
        self.geolocator = Nominatim(user_agent="solar_prediction_config")
    
    def get_location_details(self, latitude: float, longitude: float) -> LocationInfo:
        """Get location details from coordinates"""
        try:
            location = self.geolocator.reverse(f"{latitude}, {longitude}", language='en')
            
            if not location:
                return LocationInfo(latitude=latitude, longitude=longitude)
            
            address = location.raw.get('address', {})
            
            # Extract country code
            country_code = address.get('country_code', '').upper()
            
            # Extract region and city
            region = (address.get('state') or 
                     address.get('region') or 
                     address.get('province') or 
                     address.get('county'))
            
            city = (address.get('city') or 
                   address.get('town') or 
                   address.get('village') or 
                   address.get('municipality'))
            
            # Get timezone
            timezone = self.get_timezone(latitude, longitude)
            
            # Get elevation (simplified - in production use SRTM API)
            elevation = self.get_elevation(latitude, longitude)
            
            return LocationInfo(
                latitude=latitude,
                longitude=longitude,
                country_code=country_code,
                timezone=timezone,
                elevation_m=elevation,
                region=region,
                city=city
            )
            
        except Exception as e:
            logger.error(f"Geocoding failed: {e}")
            return LocationInfo(latitude=latitude, longitude=longitude)
    
    def get_timezone(self, latitude: float, longitude: float) -> str:
        """Get timezone for coordinates"""
        try:
            # Use a timezone API or library like timezonefinder
            # For now, return a default based on longitude
            if -180 <= longitude < -165:
                return "Pacific/Honolulu"
            elif -165 <= longitude < -135:
                return "America/Anchorage"
            elif -135 <= longitude < -120:
                return "America/Los_Angeles"
            elif -120 <= longitude < -105:
                return "America/Denver"
            elif -105 <= longitude < -90:
                return "America/Chicago"
            elif -90 <= longitude < -75:
                return "America/New_York"
            elif -15 <= longitude < 15:
                return "Europe/London"
            elif 15 <= longitude < 30:
                return "Europe/Berlin"
            elif 20 <= longitude < 30 and 35 <= latitude < 42:
                return "Europe/Athens"
            else:
                return "UTC"
        except:
            return "UTC"
    
    def get_elevation(self, latitude: float, longitude: float) -> Optional[float]:
        """Get elevation for coordinates (simplified)"""
        try:
            # In production, use NASA SRTM or similar API
            # For now, return a reasonable default
            return 50.0
        except:
            return None

geocoding_service = GeocodingService()

# API endpoints
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Solar Prediction Geographic Configuration API",
        "version": "1.0.0",
        "endpoints": {
            "systems": "/systems",
            "config": "/config/{system_name}",
            "overrides": "/systems/{system_id}/overrides",
            "geocode": "/geocode"
        }
    }

@app.get("/systems", response_model=List[SystemConfigResponse])
async def list_systems():
    """List all solar systems"""
    conn = get_db_connection()
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        cur.execute("""
            SELECT * FROM system_config 
            ORDER BY system_name
        """)
        systems = cur.fetchall()
        return [SystemConfigResponse(**dict(system)) for system in systems]
    finally:
        conn.close()

@app.get("/systems/{system_name}", response_model=SystemConfigResponse)
async def get_system(system_name: str):
    """Get system configuration by name"""
    conn = get_db_connection()
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        cur.execute("""
            SELECT * FROM system_config 
            WHERE system_name = %s
        """, (system_name,))
        system = cur.fetchone()
        
        if not system:
            raise HTTPException(status_code=404, detail="System not found")
        
        return SystemConfigResponse(**dict(system))
    finally:
        conn.close()

@app.post("/systems", response_model=SystemConfigResponse)
async def create_system(system: SystemConfigCreate, background_tasks: BackgroundTasks):
    """Create new system configuration"""
    conn = get_db_connection()
    try:
        # Get location details
        location_info = geocoding_service.get_location_details(
            system.latitude, system.longitude
        )
        
        cur = conn.cursor(cursor_factory=RealDictCursor)
        
        # Check if system name already exists
        cur.execute("SELECT id FROM system_config WHERE system_name = %s", (system.system_name,))
        if cur.fetchone():
            raise HTTPException(status_code=400, detail="System name already exists")
        
        # Insert new system
        insert_query = """
            INSERT INTO system_config (
                system_name, display_name, latitude, longitude, elevation_m,
                timezone, peak_power_kw, panel_count, panel_type, panel_efficiency,
                inverter_type, inverter_efficiency, tilt_angle, azimuth, tracking_type,
                battery_capacity_kwh, battery_type, battery_efficiency,
                api_keys, api_endpoints, data_sources, status,
                commissioning_date, last_maintenance,
                country_code, region, city, created_by
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                %s, %s, %s, %s, %s, %s, %s, %s
            ) RETURNING *
        """
        
        cur.execute(insert_query, (
            system.system_name, system.display_name, system.latitude, system.longitude,
            location_info.elevation_m or system.elevation_m,
            location_info.timezone or system.timezone,
            system.peak_power_kw, system.panel_count, system.panel_type, system.panel_efficiency,
            system.inverter_type, system.inverter_efficiency, system.tilt_angle, system.azimuth,
            system.tracking_type, system.battery_capacity_kwh, system.battery_type,
            system.battery_efficiency,
            json.dumps(system.api_keys) if system.api_keys else None,
            json.dumps(system.api_endpoints) if system.api_endpoints else None,
            json.dumps(system.data_sources) if system.data_sources else None,
            system.status, system.commissioning_date, system.last_maintenance,
            location_info.country_code, location_info.region, location_info.city,
            "api_user"
        ))
        
        new_system = cur.fetchone()
        conn.commit()
        
        # Schedule background tasks
        background_tasks.add_task(update_weather_station_mapping, new_system['id'])
        background_tasks.add_task(notify_config_change, "CREATE", system.system_name)
        
        return SystemConfigResponse(**dict(new_system))
        
    except psycopg2.IntegrityError as e:
        conn.rollback()
        raise HTTPException(status_code=400, detail=f"Database constraint violation: {e}")
    finally:
        conn.close()

@app.put("/systems/{system_name}", response_model=SystemConfigResponse)
async def update_system(
    system_name: str, 
    system_update: SystemConfigUpdate,
    background_tasks: BackgroundTasks
):
    """Update system configuration"""
    conn = get_db_connection()
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        
        # Check if system exists
        cur.execute("SELECT * FROM system_config WHERE system_name = %s", (system_name,))
        existing_system = cur.fetchone()
        
        if not existing_system:
            raise HTTPException(status_code=404, detail="System not found")
        
        # Build update query dynamically
        update_fields = []
        update_values = []
        
        for field, value in system_update.dict(exclude_unset=True).items():
            if field in ['api_keys', 'api_endpoints', 'data_sources'] and value is not None:
                update_fields.append(f"{field} = %s")
                update_values.append(json.dumps(value))
            else:
                update_fields.append(f"{field} = %s")
                update_values.append(value)
        
        if not update_fields:
            raise HTTPException(status_code=400, detail="No fields to update")
        
        # Add updated_by and updated_at
        update_fields.append("updated_by = %s")
        update_values.append("api_user")
        
        # If location changed, update geographic info
        if 'latitude' in system_update.dict(exclude_unset=True) or 'longitude' in system_update.dict(exclude_unset=True):
            lat = system_update.latitude or existing_system['latitude']
            lon = system_update.longitude or existing_system['longitude']
            
            location_info = geocoding_service.get_location_details(lat, lon)
            
            update_fields.extend([
                "country_code = %s",
                "region = %s", 
                "city = %s",
                "timezone = %s"
            ])
            update_values.extend([
                location_info.country_code,
                location_info.region,
                location_info.city,
                location_info.timezone
            ])
        
        update_values.append(system_name)
        
        update_query = f"""
            UPDATE system_config 
            SET {', '.join(update_fields)}
            WHERE system_name = %s
            RETURNING *
        """
        
        cur.execute(update_query, update_values)
        updated_system = cur.fetchone()
        conn.commit()
        
        # Schedule background tasks
        background_tasks.add_task(notify_config_change, "UPDATE", system_name)
        
        return SystemConfigResponse(**dict(updated_system))
        
    except psycopg2.IntegrityError as e:
        conn.rollback()
        raise HTTPException(status_code=400, detail=f"Database constraint violation: {e}")
    finally:
        conn.close()

@app.delete("/systems/{system_name}")
async def delete_system(system_name: str, background_tasks: BackgroundTasks):
    """Delete system configuration"""
    conn = get_db_connection()
    try:
        cur = conn.cursor()
        
        # Check if system exists
        cur.execute("SELECT id FROM system_config WHERE system_name = %s", (system_name,))
        if not cur.fetchone():
            raise HTTPException(status_code=404, detail="System not found")
        
        # Delete system (cascades to related tables)
        cur.execute("DELETE FROM system_config WHERE system_name = %s", (system_name,))
        conn.commit()
        
        # Schedule background tasks
        background_tasks.add_task(notify_config_change, "DELETE", system_name)
        
        return {"message": f"System {system_name} deleted successfully"}
        
    finally:
        conn.close()

@app.post("/geocode", response_model=LocationInfo)
async def geocode_location(latitude: float, longitude: float):
    """Get location information from coordinates"""
    return geocoding_service.get_location_details(latitude, longitude)

@app.get("/systems/{system_id}/overrides")
async def get_system_overrides(system_id: int):
    """Get geographic overrides for a system"""
    conn = get_db_connection()
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        cur.execute("""
            SELECT * FROM geographic_overrides 
            WHERE system_id = %s 
            ORDER BY priority DESC, created_at DESC
        """, (system_id,))
        overrides = cur.fetchall()
        return [dict(override) for override in overrides]
    finally:
        conn.close()

@app.post("/systems/{system_id}/overrides")
async def create_override(system_id: int, override: GeographicOverride):
    """Create geographic override for a system"""
    conn = get_db_connection()
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        
        # Check if system exists
        cur.execute("SELECT id FROM system_config WHERE id = %s", (system_id,))
        if not cur.fetchone():
            raise HTTPException(status_code=404, detail="System not found")
        
        # Insert override
        insert_query = """
            INSERT INTO geographic_overrides (
                system_id, parameter_name, parameter_value, override_type,
                valid_from, valid_to, valid_months, valid_hours,
                weather_condition, priority, is_active, description, created_by
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
            ) RETURNING *
        """
        
        cur.execute(insert_query, (
            system_id, override.parameter_name, json.dumps(override.parameter_value),
            override.override_type, override.valid_from, override.valid_to,
            override.valid_months, override.valid_hours, override.weather_condition,
            override.priority, override.is_active, override.description, "api_user"
        ))
        
        new_override = cur.fetchone()
        conn.commit()
        
        return dict(new_override)
        
    finally:
        conn.close()

# Background tasks
async def update_weather_station_mapping(system_id: int):
    """Update weather station mapping for a system"""
    logger.info(f"Updating weather station mapping for system {system_id}")
    # Implementation would find and map nearest weather stations

async def notify_config_change(change_type: str, system_name: str):
    """Notify other services of configuration changes"""
    logger.info(f"Configuration change: {change_type} for system {system_name}")
    # Implementation would send webhooks, update caches, etc.

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
