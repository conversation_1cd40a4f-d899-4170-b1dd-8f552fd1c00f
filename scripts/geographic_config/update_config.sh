#!/bin/bash
# Dynamic API Configuration Update Script

echo "🔄 Updating API configurations..."

# Get current configuration from API
CONFIG=$(curl -s http://localhost:8001/systems)

if [ $? -eq 0 ]; then
    echo "✅ Configuration retrieved successfully"
    
    # Update environment variables
    echo "📝 Updating environment variables..."
    
    # Extract coordinates for each system
    echo "$CONFIG" | jq -r '.[] | "export " + .system_name + "_LAT=" + (.latitude | tostring)' > /tmp/coords.env
    echo "$CONFIG" | jq -r '.[] | "export " + .system_name + "_LON=" + (.longitude | tostring)' >> /tmp/coords.env
    echo "$CONFIG" | jq -r '.[] | "export " + .system_name + "_TZ=" + .timezone' >> /tmp/coords.env
    
    # Source the new environment
    source /tmp/coords.env
    
    echo "✅ Environment variables updated"
    
    # Restart services that depend on configuration
    echo "🔄 Restarting dependent services..."
    
    # Add service restart commands here
    # systemctl restart solar_data_collection.service
    # systemctl restart solar_prediction.service
    
    echo "✅ Configuration update completed"
else
    echo "❌ Failed to retrieve configuration"
    exit 1
fi
