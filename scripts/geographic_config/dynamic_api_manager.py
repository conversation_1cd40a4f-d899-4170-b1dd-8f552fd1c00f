#!/usr/bin/env python3
"""
Dynamic API Configuration Manager
Manages API configurations and adapts to geographic locations
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import json
import requests
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import logging
import psycopg2
from psycopg2.extras import RealDictCursor
from abc import ABC, abstractmethod
import time

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': ''
}

@dataclass
class APIConfiguration:
    """API configuration for a specific provider"""
    provider: str
    base_url: str
    api_key: Optional[str] = None
    rate_limit_per_minute: int = 60
    rate_limit_per_day: int = 1000
    timeout_seconds: int = 30
    retry_attempts: int = 3
    retry_delay_seconds: int = 1
    geographic_parameters: Dict[str, Any] = None
    custom_headers: Dict[str, str] = None
    
    def __post_init__(self):
        if self.geographic_parameters is None:
            self.geographic_parameters = {}
        if self.custom_headers is None:
            self.custom_headers = {}

@dataclass
class SystemLocation:
    """Geographic location and system information"""
    system_name: str
    latitude: float
    longitude: float
    timezone: str
    country_code: str
    elevation_m: Optional[float] = None
    region: Optional[str] = None
    city: Optional[str] = None

class APIAdapter(ABC):
    """Abstract base class for API adapters"""
    
    def __init__(self, config: APIConfiguration):
        self.config = config
        self.last_request_time = 0
        self.request_count = 0
        self.daily_request_count = 0
        self.last_reset_date = datetime.now().date()
    
    @abstractmethod
    def build_request_params(self, location: SystemLocation, **kwargs) -> Dict[str, Any]:
        """Build request parameters for the specific API"""
        pass
    
    @abstractmethod
    def parse_response(self, response_data: Dict[str, Any]) -> Dict[str, Any]:
        """Parse API response into standardized format"""
        pass
    
    def check_rate_limit(self) -> bool:
        """Check if request is within rate limits"""
        now = time.time()
        current_date = datetime.now().date()
        
        # Reset daily counter if new day
        if current_date > self.last_reset_date:
            self.daily_request_count = 0
            self.last_reset_date = current_date
        
        # Check daily limit
        if self.daily_request_count >= self.config.rate_limit_per_day:
            logger.warning(f"Daily rate limit exceeded for {self.config.provider}")
            return False
        
        # Check per-minute limit
        time_since_last = now - self.last_request_time
        if time_since_last < (60 / self.config.rate_limit_per_minute):
            sleep_time = (60 / self.config.rate_limit_per_minute) - time_since_last
            logger.info(f"Rate limiting: sleeping {sleep_time:.2f} seconds")
            time.sleep(sleep_time)
        
        return True
    
    def make_request(self, location: SystemLocation, **kwargs) -> Optional[Dict[str, Any]]:
        """Make API request with rate limiting and error handling"""
        
        if not self.check_rate_limit():
            return None
        
        params = self.build_request_params(location, **kwargs)
        headers = {
            'User-Agent': 'Solar-Prediction-System/1.0',
            **self.config.custom_headers
        }
        
        if self.config.api_key:
            headers['Authorization'] = f'Bearer {self.config.api_key}'
        
        for attempt in range(self.config.retry_attempts):
            try:
                response = requests.get(
                    self.config.base_url,
                    params=params,
                    headers=headers,
                    timeout=self.config.timeout_seconds
                )
                
                response.raise_for_status()
                
                self.last_request_time = time.time()
                self.request_count += 1
                self.daily_request_count += 1
                
                return self.parse_response(response.json())
                
            except requests.exceptions.RequestException as e:
                logger.warning(f"API request failed (attempt {attempt + 1}): {e}")
                
                if attempt < self.config.retry_attempts - 1:
                    time.sleep(self.config.retry_delay_seconds * (2 ** attempt))
                else:
                    logger.error(f"All retry attempts failed for {self.config.provider}")
                    return None
        
        return None

class OpenMeteoAdapter(APIAdapter):
    """Adapter for Open-Meteo weather API"""
    
    def build_request_params(self, location: SystemLocation, **kwargs) -> Dict[str, Any]:
        """Build Open-Meteo API parameters"""
        
        params = {
            'latitude': location.latitude,
            'longitude': location.longitude,
            'timezone': location.timezone,
            'hourly': [
                'temperature_2m',
                'relative_humidity_2m',
                'cloud_cover',
                'global_horizontal_irradiance',
                'direct_normal_irradiance',
                'diffuse_radiation',
                'wind_speed_10m',
                'wind_direction_10m'
            ]
        }
        
        # Add forecast days if specified
        if 'forecast_days' in kwargs:
            params['forecast_days'] = kwargs['forecast_days']
        
        # Add past days if specified
        if 'past_days' in kwargs:
            params['past_days'] = kwargs['past_days']
        
        # Add date range if specified
        if 'start_date' in kwargs and 'end_date' in kwargs:
            params['start_date'] = kwargs['start_date']
            params['end_date'] = kwargs['end_date']
        
        return params
    
    def parse_response(self, response_data: Dict[str, Any]) -> Dict[str, Any]:
        """Parse Open-Meteo response"""
        
        hourly_data = response_data.get('hourly', {})
        
        return {
            'provider': 'open-meteo',
            'location': {
                'latitude': response_data.get('latitude'),
                'longitude': response_data.get('longitude'),
                'timezone': response_data.get('timezone')
            },
            'hourly_data': hourly_data,
            'units': response_data.get('hourly_units', {}),
            'retrieved_at': datetime.now().isoformat()
        }

class NASAPowerAdapter(APIAdapter):
    """Adapter for NASA POWER API"""
    
    def build_request_params(self, location: SystemLocation, **kwargs) -> Dict[str, Any]:
        """Build NASA POWER API parameters"""
        
        params = {
            'latitude': location.latitude,
            'longitude': location.longitude,
            'community': 'RE',
            'parameters': [
                'ALLSKY_SFC_SW_DWN',
                'T2M',
                'WS10M',
                'RH2M',
                'PS',
                'CLRSKY_SFC_SW_DWN',
                'ALLSKY_KT'
            ],
            'format': 'JSON',
            'temporal_average': 'HOURLY'
        }
        
        # Add date range
        if 'start_date' in kwargs and 'end_date' in kwargs:
            params['start'] = kwargs['start_date']
            params['end'] = kwargs['end_date']
        else:
            # Default to last 7 days
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=7)
            params['start'] = start_date.strftime('%Y%m%d')
            params['end'] = end_date.strftime('%Y%m%d')
        
        return params
    
    def parse_response(self, response_data: Dict[str, Any]) -> Dict[str, Any]:
        """Parse NASA POWER response"""
        
        properties = response_data.get('properties', {})
        parameter_data = properties.get('parameter', {})
        
        return {
            'provider': 'nasa-power',
            'location': {
                'latitude': properties.get('latitude'),
                'longitude': properties.get('longitude')
            },
            'parameter_data': parameter_data,
            'retrieved_at': datetime.now().isoformat()
        }

class SolaXAdapter(APIAdapter):
    """Adapter for SolaX Cloud API"""
    
    def build_request_params(self, location: SystemLocation, **kwargs) -> Dict[str, Any]:
        """Build SolaX API parameters"""
        
        params = {
            'tokenId': self.config.api_key,
            'sn': kwargs.get('serial_number', ''),
        }
        
        # Add date range if specified
        if 'date' in kwargs:
            params['date'] = kwargs['date']
        
        return params
    
    def parse_response(self, response_data: Dict[str, Any]) -> Dict[str, Any]:
        """Parse SolaX response"""
        
        return {
            'provider': 'solax',
            'success': response_data.get('success', False),
            'result': response_data.get('result', {}),
            'retrieved_at': datetime.now().isoformat()
        }

class DynamicAPIManager:
    """Manages dynamic API configurations for multiple systems"""
    
    def __init__(self):
        self.adapters: Dict[str, APIAdapter] = {}
        self.system_configs: Dict[str, SystemLocation] = {}
        self.load_configurations()
    
    def get_db_connection(self):
        """Get database connection"""
        return psycopg2.connect(**DB_CONFIG)
    
    def load_configurations(self):
        """Load system configurations from database"""
        
        try:
            conn = self.get_db_connection()
            cur = conn.cursor(cursor_factory=RealDictCursor)
            
            # Load system configurations
            cur.execute("""
                SELECT system_name, latitude, longitude, timezone, 
                       country_code, elevation_m, region, city,
                       api_keys, api_endpoints
                FROM system_config 
                WHERE status = 'active'
            """)
            
            systems = cur.fetchall()
            
            for system in systems:
                # Create system location
                location = SystemLocation(
                    system_name=system['system_name'],
                    latitude=system['latitude'],
                    longitude=system['longitude'],
                    timezone=system['timezone'] or 'UTC',
                    country_code=system['country_code'] or 'XX',
                    elevation_m=system['elevation_m'],
                    region=system['region'],
                    city=system['city']
                )
                
                self.system_configs[system['system_name']] = location
                
                # Load API configurations
                api_keys = system['api_keys'] or {}
                api_endpoints = system['api_endpoints'] or {}
                
                self.setup_adapters_for_system(system['system_name'], api_keys, api_endpoints)
            
            conn.close()
            logger.info(f"Loaded configurations for {len(systems)} systems")
            
        except Exception as e:
            logger.error(f"Failed to load configurations: {e}")
    
    def setup_adapters_for_system(self, system_name: str, api_keys: Dict, api_endpoints: Dict):
        """Setup API adapters for a specific system"""
        
        # Open-Meteo (free, no API key required)
        open_meteo_config = APIConfiguration(
            provider='open-meteo',
            base_url=api_endpoints.get('open_meteo', 'https://api.open-meteo.com/v1/forecast'),
            rate_limit_per_minute=60,
            rate_limit_per_day=10000
        )
        self.adapters[f"{system_name}_open_meteo"] = OpenMeteoAdapter(open_meteo_config)
        
        # NASA POWER (free, no API key required)
        nasa_config = APIConfiguration(
            provider='nasa-power',
            base_url=api_endpoints.get('nasa_power', 'https://power.larc.nasa.gov/api/temporal/hourly/point'),
            rate_limit_per_minute=30,
            rate_limit_per_day=1000
        )
        self.adapters[f"{system_name}_nasa_power"] = NASAPowerAdapter(nasa_config)
        
        # SolaX (requires API key)
        if 'solax_token' in api_keys:
            solax_config = APIConfiguration(
                provider='solax',
                base_url=api_endpoints.get('solax', 'https://www.solaxcloud.com/proxyApp/proxy/api/getRealtimeInfo.do'),
                api_key=api_keys['solax_token'],
                rate_limit_per_minute=10,
                rate_limit_per_day=1000
            )
            self.adapters[f"{system_name}_solax"] = SolaXAdapter(solax_config)
    
    def get_weather_data(self, system_name: str, provider: str = 'open-meteo', **kwargs) -> Optional[Dict[str, Any]]:
        """Get weather data for a system"""
        
        if system_name not in self.system_configs:
            logger.error(f"System {system_name} not found")
            return None
        
        adapter_key = f"{system_name}_{provider}"
        if adapter_key not in self.adapters:
            logger.error(f"Adapter {adapter_key} not found")
            return None
        
        location = self.system_configs[system_name]
        adapter = self.adapters[adapter_key]
        
        return adapter.make_request(location, **kwargs)
    
    def get_solar_data(self, system_name: str, **kwargs) -> Optional[Dict[str, Any]]:
        """Get solar system data"""
        
        adapter_key = f"{system_name}_solax"
        if adapter_key not in self.adapters:
            logger.error(f"SolaX adapter for {system_name} not found")
            return None
        
        location = self.system_configs[system_name]
        adapter = self.adapters[adapter_key]
        
        return adapter.make_request(location, **kwargs)
    
    def update_system_location(self, system_name: str, latitude: float, longitude: float):
        """Update system location and reconfigure APIs"""
        
        try:
            conn = self.get_db_connection()
            cur = conn.cursor(cursor_factory=RealDictCursor)
            
            # Get updated system info
            cur.execute("""
                SELECT system_name, latitude, longitude, timezone, 
                       country_code, elevation_m, region, city,
                       api_keys, api_endpoints
                FROM system_config 
                WHERE system_name = %s
            """, (system_name,))
            
            system = cur.fetchone()
            
            if system:
                # Update system location
                location = SystemLocation(
                    system_name=system['system_name'],
                    latitude=system['latitude'],
                    longitude=system['longitude'],
                    timezone=system['timezone'] or 'UTC',
                    country_code=system['country_code'] or 'XX',
                    elevation_m=system['elevation_m'],
                    region=system['region'],
                    city=system['city']
                )
                
                self.system_configs[system_name] = location
                
                # Reconfigure adapters
                api_keys = system['api_keys'] or {}
                api_endpoints = system['api_endpoints'] or {}
                self.setup_adapters_for_system(system_name, api_keys, api_endpoints)
                
                logger.info(f"Updated configuration for system {system_name}")
            
            conn.close()
            
        except Exception as e:
            logger.error(f"Failed to update system location: {e}")
    
    def get_api_usage_stats(self) -> Dict[str, Any]:
        """Get API usage statistics"""
        
        stats = {}
        
        for adapter_key, adapter in self.adapters.items():
            stats[adapter_key] = {
                'provider': adapter.config.provider,
                'request_count': adapter.request_count,
                'daily_request_count': adapter.daily_request_count,
                'rate_limit_per_minute': adapter.config.rate_limit_per_minute,
                'rate_limit_per_day': adapter.config.rate_limit_per_day,
                'last_request_time': adapter.last_request_time
            }
        
        return stats
    
    def test_all_apis(self) -> Dict[str, Any]:
        """Test all configured APIs"""
        
        results = {}
        
        for system_name in self.system_configs.keys():
            system_results = {}
            
            # Test weather APIs
            for provider in ['open-meteo', 'nasa-power']:
                try:
                    data = self.get_weather_data(system_name, provider, forecast_days=1)
                    system_results[provider] = {
                        'status': 'success' if data else 'failed',
                        'data_available': bool(data)
                    }
                except Exception as e:
                    system_results[provider] = {
                        'status': 'error',
                        'error': str(e)
                    }
            
            # Test SolaX API if available
            try:
                data = self.get_solar_data(system_name)
                system_results['solax'] = {
                    'status': 'success' if data else 'failed',
                    'data_available': bool(data)
                }
            except Exception as e:
                system_results['solax'] = {
                    'status': 'error',
                    'error': str(e)
                }
            
            results[system_name] = system_results
        
        return results

def create_configuration_script():
    """Create configuration update script"""
    
    script_content = """#!/bin/bash
# Dynamic API Configuration Update Script

echo "🔄 Updating API configurations..."

# Get current configuration from API
CONFIG=$(curl -s http://localhost:8001/systems)

if [ $? -eq 0 ]; then
    echo "✅ Configuration retrieved successfully"
    
    # Update environment variables
    echo "📝 Updating environment variables..."
    
    # Extract coordinates for each system
    echo "$CONFIG" | jq -r '.[] | "export " + .system_name + "_LAT=" + (.latitude | tostring)' > /tmp/coords.env
    echo "$CONFIG" | jq -r '.[] | "export " + .system_name + "_LON=" + (.longitude | tostring)' >> /tmp/coords.env
    echo "$CONFIG" | jq -r '.[] | "export " + .system_name + "_TZ=" + .timezone' >> /tmp/coords.env
    
    # Source the new environment
    source /tmp/coords.env
    
    echo "✅ Environment variables updated"
    
    # Restart services that depend on configuration
    echo "🔄 Restarting dependent services..."
    
    # Add service restart commands here
    # systemctl restart solar_data_collection.service
    # systemctl restart solar_prediction.service
    
    echo "✅ Configuration update completed"
else
    echo "❌ Failed to retrieve configuration"
    exit 1
fi
"""
    
    with open('scripts/geographic_config/update_config.sh', 'w') as f:
        f.write(script_content)
    
    os.chmod('scripts/geographic_config/update_config.sh', 0o755)

def main():
    """Main function for testing"""
    
    print("🌍 DYNAMIC API CONFIGURATION MANAGER")
    print("="*60)
    print("🔧 Testing API configurations and adapters")
    print()
    
    try:
        # Initialize manager
        manager = DynamicAPIManager()
        
        print(f"📊 Loaded {len(manager.system_configs)} systems")
        print(f"🔌 Configured {len(manager.adapters)} API adapters")
        print()
        
        # Test APIs
        print("🧪 Testing API connections...")
        test_results = manager.test_all_apis()
        
        for system_name, results in test_results.items():
            print(f"\n📍 System: {system_name}")
            for provider, result in results.items():
                status_emoji = "✅" if result['status'] == 'success' else "❌"
                print(f"   {status_emoji} {provider}: {result['status']}")
        
        # Show usage stats
        print("\n📊 API Usage Statistics:")
        stats = manager.get_api_usage_stats()
        
        for adapter_key, stat in stats.items():
            print(f"   📡 {adapter_key}: {stat['daily_request_count']}/{stat['rate_limit_per_day']} daily requests")
        
        # Create configuration script
        create_configuration_script()
        print("\n📝 Configuration update script created: scripts/geographic_config/update_config.sh")
        
        print("\n🎉 Dynamic API manager ready!")
        
        return True
        
    except Exception as e:
        print(f"❌ Dynamic API manager failed: {e}")
        logger.exception("Dynamic API manager failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
