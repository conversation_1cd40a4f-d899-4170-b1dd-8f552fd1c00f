#!/usr/bin/env python3
"""
ΤΕΛΙΚΗ ΑΚΡΙΒΗΣ ΑΝΑΛΥΣΗ ΔΕΔΟΜΕΝΩΝ - March 2024 to Present
"""

import psycopg2
from datetime import datetime

def final_data_analysis():
    """Τελική ακριβής ανάλυση όλων των δεδομένων"""
    print("🎯 ΤΕΛΙΚΗ ΑΚΡΙΒΗΣ ΑΝΑΛΥΣΗ ΔΕΔΟΜΕΝΩΝ")
    print("=" * 60)
    print("Στόχος: Επαλήθευση δεδομένων από March 2024 έως σήμερα")
    
    try:
        conn = psycopg2.connect(
            host="localhost",
            database="solar_prediction",
            user="postgres",
            password="postgres"
        )
        cursor = conn.cursor()
        
        # Ανάλυση κύριων πινάκων
        main_tables = [
            ('solax_data', 'System 1 (Σπίτι Πάνω)'),
            ('solax_data2', 'System 2 (Σπίτι Κάτω)'),
            ('weather_data', 'Weather Data'),
            ('normalized_training_data', 'Normalized Training Data'),
            ('integrated_data', 'Integrated Data'),
            ('integrated_data_enhanced', 'Enhanced Integrated Data')
        ]
        
        print(f"\n📊 ΑΝΑΛΥΣΗ ΚΥΡΙΩΝ ΠΙΝΑΚΩΝ:")
        
        total_solar_records = 0
        earliest_date = None
        latest_date = None
        
        for table_name, description in main_tables:
            print(f"\n🔍 {description} ({table_name}):")
            
            try:
                # Συνολικά records
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                total = cursor.fetchone()[0]
                
                if total > 0:
                    # Date range
                    cursor.execute(f"SELECT MIN(timestamp), MAX(timestamp) FROM {table_name}")
                    min_date, max_date = cursor.fetchone()
                    
                    print(f"   📈 Συνολικά: {total:,} records")
                    print(f"   📅 Εύρος: {min_date} έως {max_date}")
                    
                    # Ενημέρωση συνολικών στατιστικών
                    if 'solax' in table_name:
                        total_solar_records += total
                        
                        if earliest_date is None or min_date < earliest_date:
                            earliest_date = min_date
                        if latest_date is None or max_date > latest_date:
                            latest_date = max_date
                    
                    # Ελέγχω για δεδομένα από March 2024
                    cursor.execute(f"""
                        SELECT COUNT(*) FROM {table_name} 
                        WHERE timestamp >= '2024-03-01' AND timestamp < '2024-04-01'
                    """)
                    march_2024 = cursor.fetchone()[0]
                    
                    # Ελέγχω για δεδομένα από April 2024
                    cursor.execute(f"""
                        SELECT COUNT(*) FROM {table_name} 
                        WHERE timestamp >= '2024-04-01' AND timestamp < '2024-05-01'
                    """)
                    april_2024 = cursor.fetchone()[0]
                    
                    # Ελέγχω για δεδομένα από 2025
                    cursor.execute(f"""
                        SELECT COUNT(*) FROM {table_name} 
                        WHERE timestamp >= '2025-01-01'
                    """)
                    year_2025 = cursor.fetchone()[0]
                    
                    print(f"   🎯 March 2024: {march_2024:,} records")
                    print(f"   🎯 April 2024: {april_2024:,} records")
                    print(f"   🆕 Year 2025: {year_2025:,} records")
                    
                    # Ειδική ανάλυση για system_id αν υπάρχει
                    if 'solax' in table_name:
                        try:
                            cursor.execute(f"""
                                SELECT system_id, COUNT(*) 
                                FROM {table_name} 
                                WHERE system_id IS NOT NULL
                                GROUP BY system_id 
                                ORDER BY system_id
                            """)
                            system_data = cursor.fetchall()
                            
                            if system_data:
                                print(f"   🏠 System ID breakdown:")
                                for system_id, count in system_data:
                                    print(f"      System {system_id}: {count:,} records")
                        except:
                            print(f"   ⚠️  Δεν υπάρχει στήλη system_id")
                else:
                    print(f"   ❌ ΑΔΕΙΟΣ ΠΙΝΑΚΑΣ")
                    
            except Exception as e:
                print(f"   ❌ ΣΦΑΛΜΑ: {e}")
        
        # ΣΥΝΟΛΙΚΗ ΑΝΑΛΥΣΗ
        print(f"\n📋 ΣΥΝΟΛΙΚΗ ΑΝΑΛΥΣΗ:")
        print(f"   📊 Συνολικά Solar Records: {total_solar_records:,}")
        print(f"   📅 Εύρος Solar Data: {earliest_date} έως {latest_date}")
        
        # Ελέγχω αν έχουμε δεδομένα από March 2024
        cursor.execute("""
            SELECT 
                (SELECT COUNT(*) FROM solax_data WHERE timestamp >= '2024-03-01') as solax1_march,
                (SELECT COUNT(*) FROM solax_data2 WHERE timestamp >= '2024-03-01') as solax2_march
        """)
        solax1_march, solax2_march = cursor.fetchone()
        total_march = solax1_march + solax2_march
        
        print(f"\n🎯 ΔΕΔΟΜΕΝΑ ΑΠΟ MARCH 2024:")
        if total_march > 0:
            print(f"   ✅ ΕΧΟΥΜΕ δεδομένα από March 2024: {total_march:,} records")
            print(f"      System 1: {solax1_march:,} records")
            print(f"      System 2: {solax2_march:,} records")
            print(f"   🎯 ΕΠΙΒΕΒΑΙΩΣΗ: Τα δεδομένα από 3/2024 ΥΠΑΡΧΟΥΝ!")
        else:
            print(f"   ❌ ΔΕΝ ΕΧΟΥΜΕ δεδομένα από March 2024")
            print(f"   🔧 Χρειάζεται import από backup files")
        
        # Ελέγχω την κατάσταση για Enhanced Model v3
        print(f"\n🚀 ΕΤΟΙΜΟΤΗΤΑ ΓΙΑ ENHANCED MODEL V3:")
        
        if total_march > 0:
            print(f"   ✅ Historical Data: Διαθέσιμα από March 2024")
            print(f"   ✅ Dual Systems: Και τα δύο συστήματα έχουν δεδομένα")
            print(f"   ✅ Data Volume: {total_solar_records:,} records συνολικά")
            print(f"   🎯 ΕΤΟΙΜΟΙ για Enhanced Model v3 training!")
        else:
            print(f"   ❌ Χρειάζεται import historical data πρώτα")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Σφάλμα: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    final_data_analysis()
