#!/usr/bin/env python3
"""
Evaluate the enhanced solar prediction model.

This script:
1. Loads the trained models
2. Evaluates them on test data
3. Generates evaluation metrics and visualizations

Usage:
    python evaluate_enhanced_model.py [--model_dir=models/enhanced_v2] [--output_dir=evaluation]

Options:
    --model_dir     Directory with the trained models (default: models/enhanced_v2)
    --output_dir    Directory to save the evaluation results (default: evaluation)
"""

import os
import sys
import logging
import argparse
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
from typing import Dict, List, Tuple, Any, Optional
from sqlalchemy import func
from sqlalchemy.orm import Session
import joblib
import json
import xgboost as xgb
import lightgbm as lgb
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

# Add the project directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Database
from app.db.session import SessionLocal
from app.db.models.weather.normalized_training_data_enhanced import (
    NormalizedTrainingDataEnhanced,
)

# Import wrapper classes from train_enhanced_model_v2.py
from sklearn.base import BaseEstimator, RegressorMixin


# Create wrapper classes for XGBoost and LightGBM models
class XGBoostWrapper(BaseEstimator, RegressorMixin):
    def __init__(self, model):
        self.model = model

    def predict(self, X):
        dmatrix = xgb.DMatrix(X)
        return self.model.predict(dmatrix)

    def fit(self, X, y):
        # This is a dummy method, as the model is already trained
        return self


class LightGBMWrapper(BaseEstimator, RegressorMixin):
    def __init__(self, model):
        self.model = model

    def predict(self, X):
        return self.model.predict(X)

    def fit(self, X, y):
        # This is a dummy method, as the model is already trained
        return self


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()],
)
logger = logging.getLogger(__name__)


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Evaluate the enhanced solar prediction model"
    )
    parser.add_argument(
        "--model_dir",
        type=str,
        default="models/enhanced_v2",
        help="Directory with the trained models (default: models/enhanced_v2)",
    )
    parser.add_argument(
        "--output_dir",
        type=str,
        default="evaluation",
        help="Directory to save the evaluation results (default: evaluation)",
    )
    return parser.parse_args()


def load_models(model_dir: str) -> Dict[str, Any]:
    """
    Load the trained models.

    Args:
        model_dir: Directory with the trained models

    Returns:
        Dictionary with loaded models
    """
    logger.info(f"Loading models from {model_dir}...")

    # Load XGBoost model
    xgb_model_path = os.path.join(model_dir, "xgboost_model.json")
    xgb_model = xgb.Booster()
    xgb_model.load_model(xgb_model_path)
    logger.info(f"XGBoost model loaded from {xgb_model_path}")

    # Load LightGBM model
    lgb_model_path = os.path.join(model_dir, "lightgbm_model.txt")
    lgb_model = lgb.Booster(model_file=lgb_model_path)
    logger.info(f"LightGBM model loaded from {lgb_model_path}")

    # Load Random Forest model
    rf_model_path = os.path.join(model_dir, "random_forest_model.pkl")
    rf_model = joblib.load(rf_model_path)
    logger.info(f"Random Forest model loaded from {rf_model_path}")

    # Load Ensemble model
    ensemble_model_path = os.path.join(model_dir, "ensemble_model.pkl")
    ensemble_model = joblib.load(ensemble_model_path)
    logger.info(f"Ensemble model loaded from {ensemble_model_path}")

    # Load metadata
    metadata_path = os.path.join(model_dir, "metadata.json")
    with open(metadata_path, "r") as f:
        metadata = json.load(f)
    logger.info(f"Metadata loaded from {metadata_path}")

    # Load feature columns
    feature_columns_path = os.path.join(model_dir, "feature_columns.json")
    with open(feature_columns_path, "r") as f:
        feature_columns = json.load(f)
    logger.info(f"Feature columns loaded from {feature_columns_path}")

    return {
        "xgboost": xgb_model,
        "lightgbm": lgb_model,
        "random_forest": rf_model,
        "ensemble": ensemble_model,
        "metadata": metadata,
        "feature_columns": feature_columns,
    }


def load_test_data(db: Session) -> Tuple[pd.DataFrame, pd.Series]:
    """
    Load test data from the NormalizedTrainingDataEnhanced table.

    Args:
        db: Database session

    Returns:
        Tuple of (test features, test target)
    """
    logger.info("Loading test data...")

    # Load test data
    test_data = pd.read_sql(
        db.query(NormalizedTrainingDataEnhanced)
        .filter(NormalizedTrainingDataEnhanced.dataset == "test")
        .statement,
        db.bind,
    )

    logger.info(f"Loaded {len(test_data)} test samples")

    # Define the target variable
    target = test_data["ac_power"]

    # Remove rows with NaN in the target variable
    valid_indices = ~target.isna()
    test_data = test_data[valid_indices]
    target = test_data["ac_power"]

    logger.info(f"After removing NaN values: {len(test_data)} test samples")

    # Define the features
    # Get all columns that end with "_normalized"
    feature_columns = [
        col
        for col in test_data.columns
        if col.endswith("_normalized") and col != "ac_power_normalized"
    ]

    # Create the feature matrix
    features = test_data[feature_columns]

    # Fill NaN values with 0
    features = features.fillna(0)

    return features, target


def evaluate_models(
    models: Dict[str, Any],
    test_features: pd.DataFrame,
    test_target: pd.Series,
) -> Dict[str, Dict[str, float]]:
    """
    Evaluate the models on the test set.

    Args:
        models: Dictionary with trained models
        test_features: Test features
        test_target: Test target

    Returns:
        Dictionary with evaluation metrics
    """
    logger.info("Evaluating models...")

    results = {}

    # Evaluate XGBoost model
    xgb_pred = models["xgboost"].predict(xgb.DMatrix(test_features))
    xgb_rmse = np.sqrt(mean_squared_error(test_target, xgb_pred))
    xgb_mae = mean_absolute_error(test_target, xgb_pred)
    xgb_r2 = r2_score(test_target, xgb_pred)

    results["xgboost"] = {
        "rmse": xgb_rmse,
        "mae": xgb_mae,
        "r2": xgb_r2,
        "predictions": xgb_pred,
    }

    logger.info(
        f"XGBoost test RMSE: {xgb_rmse:.4f}, MAE: {xgb_mae:.4f}, R²: {xgb_r2:.4f}"
    )

    # Evaluate LightGBM model
    lgb_pred = models["lightgbm"].predict(test_features)
    lgb_rmse = np.sqrt(mean_squared_error(test_target, lgb_pred))
    lgb_mae = mean_absolute_error(test_target, lgb_pred)
    lgb_r2 = r2_score(test_target, lgb_pred)

    results["lightgbm"] = {
        "rmse": lgb_rmse,
        "mae": lgb_mae,
        "r2": lgb_r2,
        "predictions": lgb_pred,
    }

    logger.info(
        f"LightGBM test RMSE: {lgb_rmse:.4f}, MAE: {lgb_mae:.4f}, R²: {lgb_r2:.4f}"
    )

    # Evaluate Random Forest model
    rf_pred = models["random_forest"].predict(test_features)
    rf_rmse = np.sqrt(mean_squared_error(test_target, rf_pred))
    rf_mae = mean_absolute_error(test_target, rf_pred)
    rf_r2 = r2_score(test_target, rf_pred)

    results["random_forest"] = {
        "rmse": rf_rmse,
        "mae": rf_mae,
        "r2": rf_r2,
        "predictions": rf_pred,
    }

    logger.info(
        f"Random Forest test RMSE: {rf_rmse:.4f}, MAE: {rf_mae:.4f}, R²: {rf_r2:.4f}"
    )

    # Evaluate Ensemble model
    ensemble_pred = models["ensemble"].predict(test_features)
    ensemble_rmse = np.sqrt(mean_squared_error(test_target, ensemble_pred))
    ensemble_mae = mean_absolute_error(test_target, ensemble_pred)
    ensemble_r2 = r2_score(test_target, ensemble_pred)

    results["ensemble"] = {
        "rmse": ensemble_rmse,
        "mae": ensemble_mae,
        "r2": ensemble_r2,
        "predictions": ensemble_pred,
    }

    logger.info(
        f"Ensemble test RMSE: {ensemble_rmse:.4f}, MAE: {ensemble_mae:.4f}, R²: {ensemble_r2:.4f}"
    )

    # Find the best model
    best_model = min(results.items(), key=lambda x: x[1]["rmse"])
    logger.info(f"Best model: {best_model[0]} with RMSE: {best_model[1]['rmse']:.4f}")

    return results


def generate_visualizations(
    results: Dict[str, Dict[str, Any]],
    test_target: pd.Series,
    output_dir: str,
) -> None:
    """
    Generate visualizations for the evaluation results.

    Args:
        results: Dictionary with evaluation results
        test_target: Test target
        output_dir: Directory to save the visualizations
    """
    logger.info("Generating visualizations...")

    # Create the output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Set the style
    plt.style.use("ggplot")

    # Create a figure for the RMSE comparison
    plt.figure(figsize=(10, 6))
    models = list(results.keys())
    rmse_values = [results[model]["rmse"] for model in models]
    plt.bar(models, rmse_values)
    plt.xlabel("Model")
    plt.ylabel("RMSE")
    plt.title("RMSE Comparison")
    plt.savefig(os.path.join(output_dir, "rmse_comparison.png"))
    plt.close()
    logger.info(
        f"RMSE comparison saved to {os.path.join(output_dir, 'rmse_comparison.png')}"
    )

    # Create a figure for the MAE comparison
    plt.figure(figsize=(10, 6))
    mae_values = [results[model]["mae"] for model in models]
    plt.bar(models, mae_values)
    plt.xlabel("Model")
    plt.ylabel("MAE")
    plt.title("MAE Comparison")
    plt.savefig(os.path.join(output_dir, "mae_comparison.png"))
    plt.close()
    logger.info(
        f"MAE comparison saved to {os.path.join(output_dir, 'mae_comparison.png')}"
    )

    # Create a figure for the R² comparison
    plt.figure(figsize=(10, 6))
    r2_values = [results[model]["r2"] for model in models]
    plt.bar(models, r2_values)
    plt.xlabel("Model")
    plt.ylabel("R²")
    plt.title("R² Comparison")
    plt.savefig(os.path.join(output_dir, "r2_comparison.png"))
    plt.close()
    logger.info(
        f"R² comparison saved to {os.path.join(output_dir, 'r2_comparison.png')}"
    )

    # Create a figure for the actual vs. predicted values
    plt.figure(figsize=(12, 8))
    for model in models:
        plt.scatter(
            test_target,
            results[model]["predictions"],
            alpha=0.5,
            label=f"{model} (R²={results[model]['r2']:.4f})",
        )

    # Add the perfect prediction line
    min_val = min(
        test_target.min(),
        min([results[model]["predictions"].min() for model in models]),
    )
    max_val = max(
        test_target.max(),
        max([results[model]["predictions"].max() for model in models]),
    )
    plt.plot([min_val, max_val], [min_val, max_val], "k--", label="Perfect prediction")

    plt.xlabel("Actual")
    plt.ylabel("Predicted")
    plt.title("Actual vs. Predicted")
    plt.legend()
    plt.savefig(os.path.join(output_dir, "actual_vs_predicted.png"))
    plt.close()
    logger.info(
        f"Actual vs. predicted saved to {os.path.join(output_dir, 'actual_vs_predicted.png')}"
    )

    # Create a figure for the residuals
    plt.figure(figsize=(12, 8))
    for model in models:
        residuals = test_target - results[model]["predictions"]
        plt.scatter(
            results[model]["predictions"],
            residuals,
            alpha=0.5,
            label=f"{model} (RMSE={results[model]['rmse']:.4f})",
        )

    plt.axhline(y=0, color="k", linestyle="--")
    plt.xlabel("Predicted")
    plt.ylabel("Residuals")
    plt.title("Residuals vs. Predicted")
    plt.legend()
    plt.savefig(os.path.join(output_dir, "residuals.png"))
    plt.close()
    logger.info(f"Residuals saved to {os.path.join(output_dir, 'residuals.png')}")


def save_evaluation_results(
    results: Dict[str, Dict[str, Any]],
    output_dir: str,
) -> None:
    """
    Save the evaluation results.

    Args:
        results: Dictionary with evaluation results
        output_dir: Directory to save the results
    """
    logger.info("Saving evaluation results...")

    # Create the output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Save the evaluation metrics
    metrics = {
        model: {
            "rmse": results[model]["rmse"],
            "mae": results[model]["mae"],
            "r2": results[model]["r2"],
        }
        for model in results
    }

    metrics_path = os.path.join(output_dir, "metrics.json")
    with open(metrics_path, "w") as f:
        json.dump(metrics, f, indent=4)
    logger.info(f"Metrics saved to {metrics_path}")

    # Save the predictions
    predictions = {model: results[model]["predictions"].tolist() for model in results}

    predictions_path = os.path.join(output_dir, "predictions.json")
    with open(predictions_path, "w") as f:
        json.dump(predictions, f, indent=4)
    logger.info(f"Predictions saved to {predictions_path}")


def main():
    """Main function to evaluate the enhanced solar prediction model."""
    # Parse command line arguments
    args = parse_arguments()

    # Record start time
    start_time = datetime.now()
    logger.info(f"Starting model evaluation at {start_time}")

    # Connect to the database
    db = SessionLocal()

    try:
        # Load models
        models = load_models(args.model_dir)

        # Load test data
        test_features, test_target = load_test_data(db)

        # Evaluate models
        results = evaluate_models(models, test_features, test_target)

        # Generate visualizations
        generate_visualizations(results, test_target, args.output_dir)

        # Save evaluation results
        save_evaluation_results(results, args.output_dir)

        # Record end time
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()

        logger.info(f"Model evaluation completed at {end_time}")
        logger.info(f"Total duration: {duration:.2f} seconds")

    finally:
        db.close()


if __name__ == "__main__":
    main()
