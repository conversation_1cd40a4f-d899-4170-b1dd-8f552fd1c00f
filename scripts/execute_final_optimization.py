#!/usr/bin/env python3
"""
Execute Final Enhanced Model v3 Optimization

This script executes the complete final optimization pipeline:
1. Quality data preparation with system-aware features
2. Advanced algorithm comparison with quality weighting
3. Best model selection and production deployment
4. Performance validation and reporting

Final execution script for Enhanced Model v3 optimization.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
import numpy as np
import psycopg2
from dotenv import load_dotenv
import logging
from datetime import datetime
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestRegressor, VotingRegressor
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.preprocessing import RobustScaler
import joblib
from pathlib import Path
import json
import time

# Advanced ML libraries
try:
    import xgboost as xgb
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False
    print("⚠️ XGBoost not available")

try:
    import lightgbm as lgb
    LIGHTGBM_AVAILABLE = True
except ImportError:
    LIGHTGBM_AVAILABLE = False
    print("⚠️ LightGBM not available")

try:
    import catboost as cb
    CATBOOST_AVAILABLE = True
except ImportError:
    CATBOOST_AVAILABLE = False
    print("⚠️ CatBoost not available")

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FinalEnhancedModelV3Optimizer:
    """Final Enhanced Model v3 optimizer with quality data"""
    
    def __init__(self):
        self.output_dir = Path("models/enhanced_v3_final")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # System configurations
        self.system_configs = {
            1: {
                'name': 'Σπίτι Πάνω',
                'consumption_profile': 'low_steady',
                'daily_consumption_avg': 22.45,
                'grid_dependency': 0.0,
                'self_sufficiency': 1.0
            },
            2: {
                'name': 'Σπίτι Κάτω',
                'consumption_profile': 'high_variable',
                'daily_consumption_avg': 35.29,
                'grid_dependency': 0.395,
                'self_sufficiency': 0.605
            }
        }
        
        self.models = {}
        self.results = {}
        
    def load_and_prepare_quality_data(self):
        """Load and prepare highest quality data"""
        logger.info("🔍 Loading and preparing highest quality data...")
        
        try:
            load_dotenv()
            conn = psycopg2.connect(
                host=os.getenv('DB_HOST', 'localhost'),
                database=os.getenv('DB_NAME', 'solar_prediction'),
                user=os.getenv('DB_USER', 'postgres'),
                password=os.getenv('DB_PASSWORD', 'postgres')
            )
            
            # Enhanced query with strict quality filtering
            query = """
            WITH system1_data AS (
                SELECT
                    timestamp, ac_power, soc, bat_power, powerdc1, powerdc2,
                    feedin_power, consume_energy, feedin_energy,
                    1 as system_id
                FROM solax_data
                WHERE timestamp >= '2024-03-01'
                AND ac_power IS NOT NULL AND ac_power > 0 AND ac_power <= 12000
                AND soc IS NOT NULL AND soc BETWEEN 0 AND 100
                AND bat_power IS NOT NULL AND ABS(bat_power) <= 6000
                AND powerdc1 IS NOT NULL AND powerdc1 >= 0
                AND powerdc2 IS NOT NULL AND powerdc2 >= 0
            ),
            system2_data AS (
                SELECT
                    timestamp, ac_power, soc, bat_power, powerdc1, powerdc2,
                    feedin_power, consume_energy, feedin_energy,
                    2 as system_id
                FROM solax_data2
                WHERE timestamp >= '2024-03-01'
                AND ac_power IS NOT NULL AND ac_power > 0 AND ac_power <= 12000
                AND soc IS NOT NULL AND soc BETWEEN 0 AND 100
                AND bat_power IS NOT NULL AND ABS(bat_power) <= 6000
                AND powerdc1 IS NOT NULL AND powerdc1 >= 0
                AND powerdc2 IS NOT NULL AND powerdc2 >= 0
            ),
            combined_systems AS (
                SELECT * FROM system1_data
                UNION ALL
                SELECT * FROM system2_data
            ),
            weather_data AS (
                SELECT
                    timestamp,
                    COALESCE(ghi, 500) as ghi,
                    COALESCE(dni, 400) as dni,
                    COALESCE(dhi, 100) as dhi,
                    COALESCE(temperature, 20) as temperature,
                    COALESCE(cloud_cover, 30) as cloud_cover
                FROM cams_radiation_data
                WHERE timestamp >= '2024-03-01'
            )
            SELECT
                cs.*,
                COALESCE(w.ghi, 500) as ghi,
                COALESCE(w.dni, 400) as dni,
                COALESCE(w.dhi, 100) as dhi,
                COALESCE(w.temperature, 20) as temperature,
                COALESCE(w.cloud_cover, 30) as cloud_cover
            FROM combined_systems cs
            LEFT JOIN weather_data w ON DATE_TRUNC('hour', cs.timestamp) = DATE_TRUNC('hour', w.timestamp)
            ORDER BY cs.timestamp, cs.system_id
            """
            
            df = pd.read_sql(query, conn)
            conn.close()
            
            # Additional quality filtering
            initial_count = len(df)
            
            # Remove outliers using IQR method
            for col in ['ac_power', 'soc', 'bat_power']:
                Q1 = df[col].quantile(0.25)
                Q3 = df[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                df = df[(df[col] >= lower_bound) & (df[col] <= upper_bound)]
            
            # Remove records with unrealistic efficiency
            df['dc_total'] = df['powerdc1'] + df['powerdc2']
            df['efficiency'] = np.where(df['dc_total'] > 0, df['ac_power'] / df['dc_total'], 0)
            df = df[(df['efficiency'] >= 0.5) & (df['efficiency'] <= 1.1)]
            
            final_count = len(df)
            quality_retention = (final_count / initial_count) * 100
            
            logger.info(f"✅ Quality data loaded: {final_count} records ({quality_retention:.1f}% retention)")
            logger.info(f"   System 1: {len(df[df['system_id']==1])} records")
            logger.info(f"   System 2: {len(df[df['system_id']==2])} records")
            logger.info(f"   Date range: {df['timestamp'].min()} to {df['timestamp'].max()}")
            
            return df
            
        except Exception as e:
            logger.error(f"❌ Data loading failed: {e}")
            raise
    
    def engineer_premium_features(self, df):
        """Engineer premium quality features"""
        logger.info("🔧 Engineering premium quality features...")
        
        # Add system-specific configurations
        for system_id in [1, 2]:
            mask = df['system_id'] == system_id
            config = self.system_configs[system_id]
            
            df.loc[mask, 'consumption_profile'] = config['consumption_profile']
            df.loc[mask, 'daily_consumption_avg'] = config['daily_consumption_avg']
            df.loc[mask, 'grid_dependency'] = config['grid_dependency']
            df.loc[mask, 'self_sufficiency'] = config['self_sufficiency']
        
        # Temporal features with advanced cyclical encoding
        df['hour'] = df['timestamp'].dt.hour
        df['day_of_year'] = df['timestamp'].dt.dayofyear
        df['month'] = df['timestamp'].dt.month
        df['is_weekend'] = df['timestamp'].dt.dayofweek.isin([5, 6]).astype(int)
        df['season'] = (df['month'] % 12 + 3) // 3 % 4
        
        # Multiple cyclical encodings for better temporal representation
        df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
        df['day_sin'] = np.sin(2 * np.pi * df['day_of_year'] / 365)
        df['day_cos'] = np.cos(2 * np.pi * df['day_of_year'] / 365)
        df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
        df['season_sin'] = np.sin(2 * np.pi * df['season'] / 4)
        df['season_cos'] = np.cos(2 * np.pi * df['season'] / 4)
        
        # System-aware features
        df['consumption_profile_encoded'] = df['consumption_profile'].map({'low_steady': 1, 'high_variable': 2})
        
        # Advanced power features
        df['soc_normalized'] = df['soc'] / 100
        df['battery_utilization'] = np.abs(df['bat_power']) / 6000
        df['battery_direction'] = np.sign(df['bat_power'])  # Charging/discharging
        df['inverter_efficiency'] = df['efficiency']  # Already calculated
        
        # Weather normalization with robust scaling
        weather_features = ['ghi', 'dni', 'dhi', 'temperature', 'cloud_cover']
        for feature in weather_features:
            scaler = RobustScaler()
            normalized_values = scaler.fit_transform(df[feature].values.reshape(-1, 1)).flatten()
            # Scale to 0-1 range
            min_val, max_val = normalized_values.min(), normalized_values.max()
            if max_val > min_val:
                normalized_values = (normalized_values - min_val) / (max_val - min_val)
            df[f'{feature}_robust_norm'] = normalized_values
        
        # Advanced interaction features
        df['weather_production_factor'] = (
            df['ghi_robust_norm'] * 0.5 +
            df['dni_robust_norm'] * 0.3 +
            (1 - df['cloud_cover_robust_norm']) * 0.2
        )
        
        df['system_weather_interaction'] = df['system_id'] * df['weather_production_factor']
        df['consumption_weather_factor'] = df['daily_consumption_avg'] * df['weather_production_factor']
        df['system_efficiency_factor'] = df['system_id'] * df['inverter_efficiency']
        df['battery_weather_interaction'] = df['battery_utilization'] * df['weather_production_factor']
        
        # Peak and efficiency indicators
        df['is_peak_production'] = df['hour'].isin([11, 12, 13, 14]).astype(int)
        df['is_peak_consumption'] = df['hour'].isin([18, 19, 20, 21]).astype(int)
        df['is_high_efficiency'] = (df['inverter_efficiency'] > 0.9).astype(int)
        
        # Quality score (premium data gets high score)
        df['data_quality_score'] = 0.95  # High quality after filtering
        
        # Define feature columns for ML
        self.feature_columns = [
            'system_id', 'hour_sin', 'hour_cos', 'day_sin', 'day_cos', 
            'month_sin', 'month_cos', 'season_sin', 'season_cos', 'is_weekend',
            'consumption_profile_encoded', 'daily_consumption_avg', 'grid_dependency', 'self_sufficiency',
            'soc_normalized', 'battery_utilization', 'battery_direction', 'inverter_efficiency',
            'ghi_robust_norm', 'dni_robust_norm', 'dhi_robust_norm', 'temperature_robust_norm', 'cloud_cover_robust_norm',
            'weather_production_factor', 'system_weather_interaction', 'consumption_weather_factor',
            'system_efficiency_factor', 'battery_weather_interaction',
            'is_peak_production', 'is_peak_consumption', 'is_high_efficiency'
        ]
        
        logger.info(f"✅ Created {len(self.feature_columns)} premium features")
        return df
    
    def prepare_premium_training_data(self, df):
        """Prepare premium training data with quality weighting"""
        logger.info("📊 Preparing premium training data...")
        
        # Remove any remaining NaN values
        df_clean = df.dropna(subset=self.feature_columns + ['ac_power'])
        
        # Features and target
        X = df_clean[self.feature_columns].copy()
        y = df_clean['ac_power'].copy()
        
        # Quality-based sample weights
        sample_weights = df_clean['data_quality_score'].values
        
        # Stratified time-based split to ensure both systems in train/test
        split_date = df_clean['timestamp'].quantile(0.85)  # Use more data for training
        train_mask = df_clean['timestamp'] <= split_date
        test_mask = df_clean['timestamp'] > split_date
        
        X_train = X[train_mask]
        X_test = X[test_mask]
        y_train = y[train_mask]
        y_test = y[test_mask]
        weights_train = sample_weights[train_mask]
        weights_test = sample_weights[test_mask]
        
        # Ensure both systems are represented in test set
        test_system1_count = (X_test['system_id'] == 1).sum()
        test_system2_count = (X_test['system_id'] == 2).sum()
        
        logger.info(f"✅ Training set: {len(X_train)} samples")
        logger.info(f"✅ Test set: {len(X_test)} samples")
        logger.info(f"   Test System 1: {test_system1_count} samples")
        logger.info(f"   Test System 2: {test_system2_count} samples")
        logger.info(f"   Split date: {split_date}")
        
        return X_train, X_test, y_train, y_test, weights_train, weights_test
    
    def train_premium_algorithms(self, X_train, X_test, y_train, y_test, weights_train, weights_test):
        """Train premium algorithms with quality weighting"""
        logger.info("🚀 Training premium algorithms...")
        
        algorithms = []
        
        # 1. Enhanced Random Forest
        algorithms.append(('Enhanced Random Forest', RandomForestRegressor(
            n_estimators=300, max_depth=20, min_samples_split=5, min_samples_leaf=2,
            random_state=42, n_jobs=-1
        )))
        
        # 2. XGBoost with optimized parameters
        if XGBOOST_AVAILABLE:
            algorithms.append(('XGBoost Premium', xgb.XGBRegressor(
                n_estimators=300, max_depth=10, learning_rate=0.08,
                subsample=0.85, colsample_bytree=0.85, gamma=0.1,
                random_state=42, n_jobs=-1
            )))
        
        # 3. LightGBM with optimized parameters
        if LIGHTGBM_AVAILABLE:
            algorithms.append(('LightGBM Premium', lgb.LGBMRegressor(
                n_estimators=300, max_depth=10, learning_rate=0.08,
                subsample=0.85, colsample_bytree=0.85, reg_alpha=0.1, reg_lambda=0.1,
                random_state=42, verbose=-1
            )))
        
        # 4. CatBoost with optimized parameters
        if CATBOOST_AVAILABLE:
            algorithms.append(('CatBoost Premium', cb.CatBoostRegressor(
                iterations=300, depth=10, learning_rate=0.08,
                l2_leaf_reg=3, random_seed=42, verbose=False
            )))
        
        # Train and evaluate each algorithm
        for name, model in algorithms:
            logger.info(f"🔧 Training {name}...")
            
            start_time = time.time()
            
            # Train with sample weights
            try:
                if hasattr(model, 'fit') and 'sample_weight' in model.fit.__code__.co_varnames:
                    model.fit(X_train, y_train, sample_weight=weights_train)
                else:
                    model.fit(X_train, y_train)
            except Exception as e:
                logger.warning(f"Training {name} without sample weights: {e}")
                model.fit(X_train, y_train)
            
            training_time = time.time() - start_time
            
            # Predictions
            y_pred_train = model.predict(X_train)
            y_pred_test = model.predict(X_test)
            
            # Overall metrics
            train_metrics = {
                'rmse': np.sqrt(mean_squared_error(y_train, y_pred_train)),
                'mae': mean_absolute_error(y_train, y_pred_train),
                'r2': r2_score(y_train, y_pred_train)
            }
            
            test_metrics = {
                'rmse': np.sqrt(mean_squared_error(y_test, y_pred_test)),
                'mae': mean_absolute_error(y_test, y_pred_test),
                'r2': r2_score(y_test, y_pred_test)
            }
            
            # System-specific evaluation
            system_metrics = {}
            for system_id in [1, 2]:
                system_mask = X_test['system_id'] == system_id
                if system_mask.sum() > 0:
                    y_test_sys = y_test[system_mask]
                    y_pred_sys = y_pred_test[system_mask]
                    
                    system_metrics[f'system_{system_id}'] = {
                        'rmse': np.sqrt(mean_squared_error(y_test_sys, y_pred_sys)),
                        'mae': mean_absolute_error(y_test_sys, y_pred_sys),
                        'r2': r2_score(y_test_sys, y_pred_sys),
                        'samples': len(y_test_sys)
                    }
            
            # Calculate overfitting and store results
            overfitting = abs(train_metrics['r2'] - test_metrics['r2'])
            
            self.models[name] = model
            self.results[name] = {
                'train_metrics': train_metrics,
                'test_metrics': test_metrics,
                'system_metrics': system_metrics,
                'training_time': training_time,
                'overfitting': overfitting
            }
            
            logger.info(f"   {name}: R²={test_metrics['r2']:.3f}, RMSE={test_metrics['rmse']:.1f}W, Time={training_time:.1f}s")
        
        return self.models, self.results

    def create_premium_ensemble(self, X_train, y_train, weights_train):
        """Create premium ensemble from best models"""
        logger.info("🎯 Creating premium ensemble...")

        # Select models with R² > 0.75 for ensemble
        excellent_models = []
        for name, result in self.results.items():
            if result['test_metrics']['r2'] > 0.75:
                excellent_models.append((name, self.models[name]))

        if len(excellent_models) >= 2:
            # Create weighted voting regressor
            ensemble = VotingRegressor(excellent_models)

            try:
                if hasattr(ensemble, 'fit') and 'sample_weight' in ensemble.fit.__code__.co_varnames:
                    ensemble.fit(X_train, y_train, sample_weight=weights_train)
                else:
                    ensemble.fit(X_train, y_train)

                self.models['Premium Ensemble'] = ensemble
                logger.info(f"✅ Premium ensemble created with {len(excellent_models)} models:")
                for name, _ in excellent_models:
                    logger.info(f"   - {name}")
                return ensemble
            except Exception as e:
                logger.warning(f"Premium ensemble creation failed: {e}")
                return None
        else:
            logger.warning("Not enough excellent models for premium ensemble")
            return None

    def evaluate_premium_ensemble(self, ensemble, X_train, X_test, y_train, y_test):
        """Evaluate premium ensemble"""
        if ensemble is None:
            return

        logger.info("📊 Evaluating premium ensemble...")

        # Predictions
        y_pred_train = ensemble.predict(X_train)
        y_pred_test = ensemble.predict(X_test)

        # Overall metrics
        train_metrics = {
            'rmse': np.sqrt(mean_squared_error(y_train, y_pred_train)),
            'mae': mean_absolute_error(y_train, y_pred_train),
            'r2': r2_score(y_train, y_pred_train)
        }

        test_metrics = {
            'rmse': np.sqrt(mean_squared_error(y_test, y_pred_test)),
            'mae': mean_absolute_error(y_test, y_pred_test),
            'r2': r2_score(y_test, y_pred_test)
        }

        # System-specific evaluation
        system_metrics = {}
        for system_id in [1, 2]:
            system_mask = X_test['system_id'] == system_id
            if system_mask.sum() > 0:
                y_test_sys = y_test[system_mask]
                y_pred_sys = y_pred_test[system_mask]

                system_metrics[f'system_{system_id}'] = {
                    'rmse': np.sqrt(mean_squared_error(y_test_sys, y_pred_sys)),
                    'mae': mean_absolute_error(y_test_sys, y_pred_sys),
                    'r2': r2_score(y_test_sys, y_pred_sys),
                    'samples': len(y_test_sys)
                }

        overfitting = abs(train_metrics['r2'] - test_metrics['r2'])

        self.results['Premium Ensemble'] = {
            'train_metrics': train_metrics,
            'test_metrics': test_metrics,
            'system_metrics': system_metrics,
            'training_time': 0,
            'overfitting': overfitting
        }

        logger.info(f"✅ Premium Ensemble: R²={test_metrics['r2']:.3f}, RMSE={test_metrics['rmse']:.1f}W")

    def analyze_final_results(self):
        """Analyze final results and select ultimate best model"""
        logger.info("🏆 Analyzing final results...")

        # Create comprehensive comparison
        comparison_data = []
        for name, result in self.results.items():
            test_metrics = result['test_metrics']
            comparison_data.append({
                'Algorithm': name,
                'Test_R2': test_metrics['r2'],
                'Test_RMSE': test_metrics['rmse'],
                'Test_MAE': test_metrics['mae'],
                'Overfitting': result['overfitting'],
                'Training_Time': result['training_time'],
                'System1_R2': result['system_metrics'].get('system_1', {}).get('r2', 0),
                'System2_R2': result['system_metrics'].get('system_2', {}).get('r2', 0),
                'Avg_System_R2': (result['system_metrics'].get('system_1', {}).get('r2', 0) +
                                 result['system_metrics'].get('system_2', {}).get('r2', 0)) / 2
            })

        df_results = pd.DataFrame(comparison_data)
        df_results = df_results.sort_values('Test_R2', ascending=False)

        # Display comprehensive results
        logger.info("\n" + "="*140)
        logger.info("🏆 FINAL ENHANCED MODEL V3 OPTIMIZATION RESULTS")
        logger.info("="*140)

        for _, row in df_results.iterrows():
            logger.info(f"{row['Algorithm']:20} | R²: {row['Test_R2']:.3f} | "
                       f"RMSE: {row['Test_RMSE']:6.1f}W | "
                       f"MAE: {row['Test_MAE']:6.1f}W | "
                       f"Overfit: {row['Overfitting']:.3f} | "
                       f"S1_R²: {row['System1_R2']:.3f} | "
                       f"S2_R²: {row['System2_R2']:.3f} | "
                       f"Time: {row['Training_Time']:5.1f}s")

        # Select ultimate best model
        ultimate_best = df_results.iloc[0]['Algorithm']
        best_metrics = self.results[ultimate_best]['test_metrics']

        logger.info(f"\n🥇 ULTIMATE BEST MODEL: {ultimate_best}")
        logger.info(f"   Test R²: {best_metrics['r2']:.3f}")
        logger.info(f"   Test RMSE: {best_metrics['rmse']:.1f}W")
        logger.info(f"   Test MAE: {best_metrics['mae']:.1f}W")

        # Performance comparison with baseline
        baseline_r2 = 0.978  # Enhanced Model v2 baseline
        improvement = ((best_metrics['r2'] - baseline_r2) / baseline_r2) * 100

        if best_metrics['r2'] > baseline_r2:
            logger.info(f"🎉 IMPROVEMENT vs Enhanced Model v2: +{improvement:.2f}%")
        else:
            logger.info(f"⚠️ Performance vs Enhanced Model v2: {improvement:.2f}%")

        # Check if we achieved target performance
        target_r2 = 0.985
        target_rmse = 85

        target_achieved = (best_metrics['r2'] >= target_r2) and (best_metrics['rmse'] <= target_rmse)

        if target_achieved:
            logger.info("🎯 TARGET PERFORMANCE ACHIEVED!")
            logger.info(f"   Target R² ≥ {target_r2}: ✅ {best_metrics['r2']:.3f}")
            logger.info(f"   Target RMSE ≤ {target_rmse}W: ✅ {best_metrics['rmse']:.1f}W")
        else:
            logger.info("🎯 Target performance status:")
            logger.info(f"   Target R² ≥ {target_r2}: {'✅' if best_metrics['r2'] >= target_r2 else '❌'} {best_metrics['r2']:.3f}")
            logger.info(f"   Target RMSE ≤ {target_rmse}W: {'✅' if best_metrics['rmse'] <= target_rmse else '❌'} {best_metrics['rmse']:.1f}W")

        return df_results, ultimate_best, target_achieved

    def save_ultimate_model(self, ultimate_best):
        """Save the ultimate best model for production"""
        logger.info(f"💾 Saving ultimate model: {ultimate_best}")

        ultimate_model = self.models[ultimate_best]

        # Save model
        model_path = self.output_dir / f"enhanced_v3_ultimate_{ultimate_best.lower().replace(' ', '_')}.joblib"
        joblib.dump(ultimate_model, model_path)

        # Save features
        features_path = self.output_dir / "feature_columns_ultimate.json"
        with open(features_path, 'w') as f:
            json.dump(self.feature_columns, f, indent=2)

        # Save comprehensive results
        results_path = self.output_dir / "ultimate_optimization_results.json"
        serializable_results = {}
        for name, result in self.results.items():
            serializable_results[name] = {
                'test_metrics': result['test_metrics'],
                'system_metrics': result['system_metrics'],
                'training_time': result['training_time'],
                'overfitting': result['overfitting']
            }

        with open(results_path, 'w') as f:
            json.dump(serializable_results, f, indent=2)

        # Save system configurations
        config_path = self.output_dir / "system_configurations.json"
        with open(config_path, 'w') as f:
            json.dump(self.system_configs, f, indent=2)

        # Create deployment summary
        deployment_summary = {
            'model_name': ultimate_best,
            'model_path': str(model_path),
            'features_path': str(features_path),
            'feature_count': len(self.feature_columns),
            'performance': self.results[ultimate_best]['test_metrics'],
            'system_performance': self.results[ultimate_best]['system_metrics'],
            'deployment_date': datetime.now().isoformat(),
            'ready_for_production': True
        }

        summary_path = self.output_dir / "deployment_summary.json"
        with open(summary_path, 'w') as f:
            json.dump(deployment_summary, f, indent=2)

        logger.info(f"✅ Ultimate model saved: {model_path}")
        logger.info(f"✅ Features saved: {features_path}")
        logger.info(f"✅ Deployment summary: {summary_path}")

        return str(model_path), deployment_summary

def main():
    """Main optimization execution"""
    logger.info("🚀 FINAL ENHANCED MODEL V3 OPTIMIZATION EXECUTION")
    logger.info("="*80)

    # Check available libraries
    logger.info("📦 Available premium algorithms:")
    logger.info(f"   Enhanced Random Forest: ✅")
    logger.info(f"   XGBoost Premium: {'✅' if XGBOOST_AVAILABLE else '❌'}")
    logger.info(f"   LightGBM Premium: {'✅' if LIGHTGBM_AVAILABLE else '❌'}")
    logger.info(f"   CatBoost Premium: {'✅' if CATBOOST_AVAILABLE else '❌'}")

    try:
        # Initialize optimizer
        optimizer = FinalEnhancedModelV3Optimizer()

        # Load and prepare quality data
        df = optimizer.load_and_prepare_quality_data()

        if len(df) < 1000:
            logger.error(f"❌ Insufficient quality data: {len(df)} records")
            return False

        # Engineer premium features
        df = optimizer.engineer_premium_features(df)

        # Prepare premium training data
        X_train, X_test, y_train, y_test, weights_train, weights_test = optimizer.prepare_premium_training_data(df)

        # Train premium algorithms
        models, results = optimizer.train_premium_algorithms(
            X_train, X_test, y_train, y_test, weights_train, weights_test
        )

        # Create and evaluate premium ensemble
        ensemble = optimizer.create_premium_ensemble(X_train, y_train, weights_train)
        if ensemble:
            optimizer.evaluate_premium_ensemble(ensemble, X_train, X_test, y_train, y_test)

        # Analyze final results
        df_results, ultimate_best, target_achieved = optimizer.analyze_final_results()

        # Save ultimate model
        model_path, deployment_summary = optimizer.save_ultimate_model(ultimate_best)

        # Final summary
        logger.info("\n" + "="*80)
        logger.info("🎯 FINAL OPTIMIZATION COMPLETE!")
        logger.info(f"🏆 Ultimate Best Model: {ultimate_best}")
        logger.info(f"💾 Model Path: {model_path}")
        logger.info(f"🎯 Target Achievement: {'✅ YES' if target_achieved else '❌ NO'}")
        logger.info(f"🚀 Production Ready: {'✅ YES' if deployment_summary['ready_for_production'] else '❌ NO'}")
        logger.info("="*80)

        if target_achieved:
            logger.info("🎉 ENHANCED MODEL V3 OPTIMIZATION SUCCESSFUL!")
            logger.info("✅ Target performance achieved")
            logger.info("✅ System-aware predictions working")
            logger.info("✅ Premium quality data utilized")
            logger.info("🚀 Ready for immediate production deployment!")
        else:
            logger.info("⚠️ Enhanced Model v3 optimization completed with good results")
            logger.info("🔧 Consider further hyperparameter tuning for target achievement")

        return target_achieved

    except Exception as e:
        logger.error(f"❌ Final optimization failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
