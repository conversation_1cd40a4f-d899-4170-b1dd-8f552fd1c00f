#!/usr/bin/env python3
"""
Train an enhanced solar power prediction model.

This script trains a machine learning model to predict solar power production
based on historical data from the SolaX Cloud API and enhanced weather data.
"""

import sys
import os
import logging
import argparse
import pickle
from datetime import datetime, timedelta
from typing import List, Dict, Any, Tuple

import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt

# Add the project root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from app.db.session import SessionLocal
from app.db.models.solax.solax_data import SolaxData
from app.db.models.weather.weather_data import WeatherData
from app.services.solar_prediction_service import SolarPredictionService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("enhanced_solar_prediction_model.log"),
    ],
)
logger = logging.getLogger(__name__)


def load_data(days: int = 365) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Load historical data from the database.

    Args:
        days: Number of days of historical data to use

    Returns:
        Tuple of (solax_df, weather_df) containing the historical data
    """
    db = SessionLocal()
    try:
        # Calculate date range
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days)

        logger.info(f"Loading data from {start_time} to {end_time}")

        # Query SolaX data
        solax_query = (
            db.query(SolaxData)
            .filter(
                SolaxData.timestamp.between(start_time, end_time),
                SolaxData.ac_power.isnot(None),
                SolaxData.ac_power >= 0,
            )
            .order_by(SolaxData.timestamp)
        )

        solax_records = solax_query.all()

        # Query weather data
        weather_query = (
            db.query(WeatherData)
            .filter(WeatherData.timestamp.between(start_time, end_time))
            .order_by(WeatherData.timestamp)
        )

        weather_records = weather_query.all()

        logger.info(
            f"Loaded {len(solax_records)} SolaX records and {len(weather_records)} weather records"
        )

        # Convert SolaX data to DataFrame
        solax_data = []
        for record in solax_records:
            solax_data.append(
                {
                    "timestamp": record.timestamp,
                    "ac_power": record.ac_power,
                    "yield_today": record.yield_today,
                    "temperature": record.temperature,
                    "weather_condition": record.weather_condition,
                    "cloud_cover": record.cloud_cover,
                }
            )

        solax_df = pd.DataFrame(solax_data)

        # Convert weather data to DataFrame
        weather_data = []
        for record in weather_records:
            weather_data.append(
                {
                    "timestamp": record.timestamp,
                    "temperature_2m": record.temperature_2m,
                    "relative_humidity_2m": record.relative_humidity_2m,
                    "apparent_temperature": record.apparent_temperature,
                    "direct_radiation": record.direct_radiation,
                    "diffuse_radiation": record.diffuse_radiation,
                    "shortwave_radiation": record.shortwave_radiation,
                    "direct_normal_irradiance": record.direct_normal_irradiance,
                    "cloud_cover": record.cloud_cover,
                    "cloud_cover_low": record.cloud_cover_low,
                    "cloud_cover_mid": record.cloud_cover_mid,
                    "cloud_cover_high": record.cloud_cover_high,
                    "precipitation": record.precipitation,
                    "weather_code": record.weather_code,
                    "wind_speed_10m": record.wind_speed_10m,
                    "wind_direction_10m": record.wind_direction_10m,
                    "visibility": record.visibility,
                    "uv_index": record.uv_index,
                }
            )

        weather_df = pd.DataFrame(weather_data)

        return solax_df, weather_df

    finally:
        db.close()


def merge_data(solax_df: pd.DataFrame, weather_df: pd.DataFrame) -> pd.DataFrame:
    """
    Merge SolaX and weather data.

    Args:
        solax_df: DataFrame with SolaX data
        weather_df: DataFrame with weather data

    Returns:
        Merged DataFrame
    """
    # Handle timezone issues
    # Convert to datetime with UTC timezone
    solax_df["timestamp"] = pd.to_datetime(solax_df["timestamp"], utc=True)
    weather_df["timestamp"] = pd.to_datetime(weather_df["timestamp"], utc=True)

    # Round timestamps to the nearest hour for joining
    solax_df["hour_timestamp"] = solax_df["timestamp"].dt.floor("h")
    weather_df["hour_timestamp"] = weather_df["timestamp"].dt.floor("h")

    # Group SolaX data by hour and calculate average power
    solax_hourly = solax_df.groupby("hour_timestamp")["ac_power"].mean().reset_index()
    solax_hourly.rename(columns={"ac_power": "avg_ac_power"}, inplace=True)

    # Merge with weather data
    merged_df = pd.merge(
        solax_hourly,
        weather_df,
        left_on="hour_timestamp",
        right_on="hour_timestamp",
        how="inner",
    )

    logger.info(f"Merged data has {len(merged_df)} rows")

    return merged_df


def preprocess_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    Preprocess the data for training.

    Args:
        df: DataFrame containing the merged data

    Returns:
        Preprocessed DataFrame
    """
    # Extract time-based features
    df["hour"] = df["timestamp"].dt.hour
    df["day_of_week"] = df["timestamp"].dt.dayofweek
    df["month"] = df["timestamp"].dt.month
    df["day_of_year"] = df["timestamp"].dt.dayofyear
    df["is_weekend"] = df["timestamp"].dt.dayofweek >= 5

    # Calculate sun position
    solar_service = SolarPredictionService()
    df["sun_altitude"] = df.apply(
        lambda row: solar_service._calculate_sun_altitude(
            row["timestamp"], latitude=38.141367951893024
        ),
        axis=1,
    )

    # Handle missing values
    for col in df.columns:
        if df[col].dtype in [np.float64, np.int64] and df[col].isna().any():
            df[col] = df[col].fillna(0)

    return df


def create_features_and_target(
    df: pd.DataFrame,
    target_column: str = "avg_ac_power",
    include_enhanced_features: bool = True,
) -> Tuple[np.ndarray, np.ndarray, List[str]]:
    """
    Create features and target for training.

    Args:
        df: Preprocessed DataFrame
        target_column: Name of the target column
        include_enhanced_features: Whether to include enhanced weather features

    Returns:
        Tuple of (features, target, feature_names)
    """
    # Basic features (always included)
    basic_features = [
        "hour",
        "day_of_year",
        "sun_altitude",
        "temperature_2m",
        "direct_radiation",
        "diffuse_radiation",
        "shortwave_radiation",
        "cloud_cover",
    ]

    # Enhanced features (optional)
    enhanced_features = [
        "relative_humidity_2m",
        "apparent_temperature",
        "direct_normal_irradiance",
        "cloud_cover_low",
        "cloud_cover_mid",
        "cloud_cover_high",
        "precipitation",
        "weather_code",
        "wind_speed_10m",
        "wind_direction_10m",
        "visibility",
        "uv_index",
    ]

    # Select features based on configuration
    if include_enhanced_features:
        selected_features = basic_features + enhanced_features
    else:
        selected_features = basic_features

    # Filter out features that don't exist in the DataFrame
    selected_features = [f for f in selected_features if f in df.columns]

    # Create feature matrix and target vector
    X = df[selected_features].values
    y = df[target_column].values

    return X, y, selected_features


def train_model(
    X: np.ndarray,
    y: np.ndarray,
    feature_names: List[str],
    test_size: float = 0.2,
    random_state: int = 42,
) -> Tuple[Any, StandardScaler, Dict[str, float], np.ndarray, np.ndarray]:
    """
    Train a machine learning model.

    Args:
        X: Features
        y: Target
        feature_names: Names of the features
        test_size: Proportion of the data to use for testing
        random_state: Random state for reproducibility

    Returns:
        Tuple of (model, scaler, metrics, y_test, y_pred)
    """
    # Split the data into training and testing sets
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=test_size, random_state=random_state
    )

    # Scale the features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)

    # Train a Random Forest model
    model = RandomForestRegressor(
        n_estimators=100, random_state=random_state, n_jobs=-1
    )
    model.fit(X_train_scaled, y_train)

    # Make predictions
    y_pred = model.predict(X_test_scaled)

    # Calculate metrics
    metrics = {
        "mse": mean_squared_error(y_test, y_pred),
        "rmse": np.sqrt(mean_squared_error(y_test, y_pred)),
        "mae": mean_absolute_error(y_test, y_pred),
        "r2": r2_score(y_test, y_pred),
        "accuracy": 100 * (1 - np.mean(np.abs(y_test - y_pred) / (y_test + 1e-10))),
    }

    logger.info(f"Model metrics: {metrics}")

    # Log feature importances
    if hasattr(model, "feature_importances_"):
        importances = model.feature_importances_
        feature_importance = sorted(
            zip(feature_names, importances), key=lambda x: x[1], reverse=True
        )

        logger.info("Feature importances:")
        for feature, importance in feature_importance:
            logger.info(f"  {feature}: {importance:.4f}")

    return model, scaler, metrics, y_test, y_pred


def save_model(
    model: Any,
    scaler: Any,
    metrics: Dict[str, float],
    feature_names: List[str],
    output_dir: str,
    model_name: str = "enhanced_solar_model",
):
    """
    Save the trained model, scaler, and metrics.

    Args:
        model: Trained model
        scaler: Feature scaler
        metrics: Model metrics
        feature_names: Names of the features
        output_dir: Directory to save the model
        model_name: Name of the model
    """
    # Create the output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Save the model
    model_path = os.path.join(output_dir, f"{model_name}.pkl")
    with open(model_path, "wb") as f:
        pickle.dump(model, f)

    # Save the scaler
    scaler_path = os.path.join(output_dir, f"{model_name}_scaler.pkl")
    with open(scaler_path, "wb") as f:
        pickle.dump(scaler, f)

    # Save the feature names
    features_path = os.path.join(output_dir, f"{model_name}_features.pkl")
    with open(features_path, "wb") as f:
        pickle.dump(feature_names, f)

    # Save the metrics
    metrics_path = os.path.join(output_dir, f"{model_name}_metrics.json")
    with open(metrics_path, "w") as f:
        import json

        json.dump(metrics, f, indent=2)

    logger.info(f"Model saved to {model_path}")
    logger.info(f"Scaler saved to {scaler_path}")
    logger.info(f"Feature names saved to {features_path}")
    logger.info(f"Metrics saved to {metrics_path}")


def plot_feature_importance(
    model: Any,
    feature_names: List[str],
    output_dir: str,
    model_name: str = "enhanced_solar_model",
):
    """
    Plot feature importance.

    Args:
        model: Trained model
        feature_names: Names of the features
        output_dir: Directory to save the plot
        model_name: Name of the model
    """
    if not hasattr(model, "feature_importances_"):
        logger.warning("Model does not have feature_importances_ attribute")
        return

    # Create the output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Get feature importances
    importances = model.feature_importances_
    indices = np.argsort(importances)[::-1]

    # Plot feature importances
    plt.figure(figsize=(12, 8))
    plt.title("Feature Importances")
    plt.bar(range(len(importances)), importances[indices], align="center")
    plt.xticks(
        range(len(importances)), [feature_names[i] for i in indices], rotation=90
    )
    plt.tight_layout()

    # Save the plot
    plot_path = os.path.join(output_dir, f"{model_name}_feature_importance.png")
    plt.savefig(plot_path)
    plt.close()

    logger.info(f"Feature importance plot saved to {plot_path}")


def plot_predictions(
    y_true: np.ndarray,
    y_pred: np.ndarray,
    output_dir: str,
    model_name: str = "enhanced_solar_model",
):
    """
    Plot actual vs predicted values.

    Args:
        y_true: Actual values
        y_pred: Predicted values
        output_dir: Directory to save the plot
        model_name: Name of the model
    """
    # Create the output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Plot actual vs predicted values
    plt.figure(figsize=(12, 8))
    plt.scatter(y_true, y_pred, alpha=0.5)
    plt.plot([y_true.min(), y_true.max()], [y_true.min(), y_true.max()], "k--", lw=2)
    plt.xlabel("Actual Power (W)")
    plt.ylabel("Predicted Power (W)")
    plt.title("Actual vs Predicted Solar Power")
    plt.tight_layout()

    # Save the plot
    plot_path = os.path.join(output_dir, f"{model_name}_actual_vs_predicted.png")
    plt.savefig(plot_path)
    plt.close()

    logger.info(f"Actual vs predicted plot saved to {plot_path}")


def main():
    """Main function."""
    parser = argparse.ArgumentParser(
        description="Train an enhanced solar power prediction model"
    )
    parser.add_argument(
        "--output-dir",
        default="models/solar_prediction",
        help="Directory to save the model",
    )
    parser.add_argument(
        "--days", type=int, default=365, help="Number of days of historical data to use"
    )
    parser.add_argument(
        "--test-size",
        type=float,
        default=0.2,
        help="Proportion of the data to use for testing",
    )
    parser.add_argument(
        "--random-state", type=int, default=42, help="Random state for reproducibility"
    )
    parser.add_argument(
        "--basic-features-only",
        action="store_true",
        help="Use only basic features (no enhanced weather features)",
    )
    parser.add_argument(
        "--model-name", default="enhanced_solar_model", help="Name of the model"
    )

    args = parser.parse_args()

    # Load data
    solax_df, weather_df = load_data(days=args.days)

    # Merge data
    merged_df = merge_data(solax_df, weather_df)

    # Preprocess data
    processed_df = preprocess_data(merged_df)

    # Create features and target
    X, y, feature_names = create_features_and_target(
        processed_df, include_enhanced_features=not args.basic_features_only
    )

    # Train model
    model, scaler, metrics, y_test, y_pred = train_model(
        X, y, feature_names, test_size=args.test_size, random_state=args.random_state
    )

    # Save model
    save_model(model, scaler, metrics, feature_names, args.output_dir, args.model_name)

    # Plot feature importance
    plot_feature_importance(model, feature_names, args.output_dir, args.model_name)

    # Plot predictions
    plot_predictions(y_test, y_pred, args.output_dir, args.model_name)

    # Print summary
    logger.info("\nModel Training Summary:")
    logger.info(f"  Data points: {len(processed_df)}")
    logger.info(f"  Features: {len(feature_names)}")
    logger.info(f"  Feature list: {', '.join(feature_names)}")
    logger.info(f"  Mean Absolute Error: {metrics['mae']:.2f} W")
    logger.info(f"  Root Mean Squared Error: {metrics['rmse']:.2f} W")
    logger.info(f"  R² Score: {metrics['r2']:.4f}")
    logger.info(f"  Accuracy: {metrics['accuracy']:.2f}%")
    logger.info(
        f"  Model saved to: {os.path.join(args.output_dir, args.model_name + '.pkl')}"
    )


if __name__ == "__main__":
    main()
