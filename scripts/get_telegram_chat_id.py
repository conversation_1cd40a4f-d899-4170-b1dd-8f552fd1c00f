#!/usr/bin/env python3
"""
Get Telegram Chat ID
Script για να βρεις το Chat ID σου στο Telegram
"""

import requests
import json

def get_chat_id(bot_token):
    """Get chat ID από Telegram bot"""
    try:
        # Get updates από το bot
        url = f"https://api.telegram.org/bot{bot_token}/getUpdates"
        response = requests.get(url)
        
        if response.status_code == 200:
            data = response.json()
            
            if data['ok'] and data['result']:
                print("📱 Found chat messages:")
                print("=" * 50)
                
                for update in data['result']:
                    if 'message' in update:
                        message = update['message']
                        chat = message['chat']
                        
                        print(f"Chat ID: {chat['id']}")
                        print(f"Chat Type: {chat['type']}")
                        
                        if 'username' in chat:
                            print(f"Username: @{chat['username']}")
                        
                        if 'first_name' in chat:
                            print(f"Name: {chat['first_name']}")
                        
                        if 'text' in message:
                            print(f"Last Message: {message['text']}")
                        
                        print("-" * 30)
                        
                        return chat['id']
                
                print("❌ No messages found. Send a message to your bot first!")
                return None
            else:
                print("❌ No updates found. Send a message to your bot first!")
                return None
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def test_bot_token(bot_token):
    """Test if bot token is valid"""
    try:
        url = f"https://api.telegram.org/bot{bot_token}/getMe"
        response = requests.get(url)
        
        if response.status_code == 200:
            data = response.json()
            
            if data['ok']:
                bot_info = data['result']
                print("✅ Bot Token is valid!")
                print(f"Bot Name: {bot_info['first_name']}")
                print(f"Bot Username: @{bot_info['username']}")
                print(f"Bot ID: {bot_info['id']}")
                return True
            else:
                print(f"❌ Bot API Error: {data}")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing bot token: {e}")
        return False

def main():
    print("🤖 TELEGRAM BOT SETUP HELPER")
    print("=" * 40)
    
    # Bot token από το updated token
    bot_token = "8060881816:AAFBhGADTAAcUkbom_FEFSkOHoT5N6t5png"
    
    print(f"Testing bot token: {bot_token[:20]}...")
    
    # Test bot token
    if test_bot_token(bot_token):
        print("\n📱 Getting Chat ID...")
        print("=" * 40)
        
        chat_id = get_chat_id(bot_token)
        
        if chat_id:
            print(f"\n✅ Your Chat ID: {chat_id}")
            print("\n📝 Add this to your .env file:")
            print(f"TELEGRAM_CHAT_ID={chat_id}")
        else:
            print("\n❌ Could not get Chat ID")
            print("\n📝 To get your Chat ID:")
            print("1. Open Telegram")
            print("2. Search for @grlvSolarAI_bot")
            print("3. Send any message (e.g., 'Hello')")
            print("4. Run this script again")
    else:
        print("\n❌ Bot token is invalid or expired")
        print("\n📝 To fix this:")
        print("1. Go to @BotFather on Telegram")
        print("2. Send /mybots")
        print("3. Select your bot")
        print("4. Select 'API Token'")
        print("5. Copy the new token")

if __name__ == "__main__":
    main()
