#!/bin/bash
# GPU Dependencies Installation Script
# ===================================
#
# Install all required dependencies for GPU-accelerated ML pipeline
# Hardware: RTX 4070 Ti + 32GB RAM
# Created: June 6, 2025

set -e  # Exit on any error

echo "🚀 Installing GPU Dependencies for Advanced ML Pipeline"
echo "======================================================="

# Check if we're in a virtual environment
if [[ "$VIRTUAL_ENV" != "" ]]; then
    echo "✅ Virtual environment detected: $VIRTUAL_ENV"
else
    echo "⚠️ No virtual environment detected. Creating one..."
    python3 -m venv venv
    source venv/bin/activate
    echo "✅ Virtual environment created and activated"
fi

# Update pip
echo "📦 Updating pip..."
pip install --upgrade pip

# Core ML libraries
echo "🎯 Installing core ML libraries..."
pip install numpy pandas scikit-learn
pip install joblib psutil

# XGBoost with GPU support
echo "🚀 Installing XGBoost with GPU support..."
pip install xgboost

# LightGBM with GPU support
echo "💡 Installing LightGBM with GPU support..."
pip install lightgbm

# Optuna for hyperparameter optimization
echo "🎛️ Installing Optuna..."
pip install optuna optuna-dashboard

# Database connectivity
echo "🗄️ Installing database libraries..."
pip install psycopg2-binary

# Logging and monitoring
echo "📊 Installing monitoring libraries..."
pip install loguru

# Try to install GPU libraries (may fail if CUDA not properly configured)
echo "🔥 Attempting to install GPU libraries..."

# PyTorch with CUDA support
echo "   Installing PyTorch with CUDA..."
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121 || echo "⚠️ PyTorch CUDA installation failed"

# Try CuPy (may fail without proper CUDA setup)
echo "   Installing CuPy..."
pip install cupy-cuda12x || echo "⚠️ CuPy installation failed - CUDA 12.x required"

# Try RAPIDS (may fail without proper CUDA setup)
echo "   Installing RAPIDS cuML..."
pip install cuml-cu12 cudf-cu12 || echo "⚠️ RAPIDS installation failed - CUDA 12.x required"

# NVIDIA ML Python (for GPU monitoring)
echo "   Installing NVIDIA ML Python..."
pip install nvidia-ml-py3 || echo "⚠️ nvidia-ml-py3 installation failed"

# Additional useful libraries
echo "📈 Installing additional libraries..."
pip install matplotlib seaborn plotly
pip install tqdm

# Development tools
echo "🛠️ Installing development tools..."
pip install pytest black isort flake8

echo ""
echo "✅ Installation completed!"
echo ""

# Test installations
echo "🧪 Testing installations..."

echo "   Testing core libraries..."
python3 -c "import numpy, pandas, sklearn; print('✅ Core libraries OK')" || echo "❌ Core libraries failed"

echo "   Testing ML libraries..."
python3 -c "import xgboost, lightgbm, optuna; print('✅ ML libraries OK')" || echo "❌ ML libraries failed"

echo "   Testing GPU libraries..."
python3 -c "
try:
    import torch
    print(f'✅ PyTorch: {torch.__version__}')
    if torch.cuda.is_available():
        print(f'✅ CUDA available: {torch.cuda.get_device_name(0)}')
    else:
        print('⚠️ CUDA not available in PyTorch')
except ImportError:
    print('❌ PyTorch not installed')

try:
    import cupy as cp
    print(f'✅ CuPy: {cp.__version__}')
    print(f'✅ GPU: {cp.cuda.get_device_name()}')
except ImportError:
    print('❌ CuPy not installed')

try:
    import cuml, cudf
    print(f'✅ RAPIDS cuML: {cuml.__version__}')
    print(f'✅ RAPIDS cuDF: {cudf.__version__}')
except ImportError:
    print('❌ RAPIDS not installed')

try:
    import nvidia_ml_py3 as nvml
    nvml.nvmlInit()
    handle = nvml.nvmlDeviceGetHandleByIndex(0)
    name = nvml.nvmlDeviceGetName(handle)
    print(f'✅ NVIDIA ML: {name.decode()}')
except ImportError:
    print('❌ NVIDIA ML not installed')
except Exception as e:
    print(f'⚠️ NVIDIA ML error: {e}')
"

echo ""
echo "🎯 GPU Dependencies Installation Summary"
echo "======================================="

# Check NVIDIA driver
if command -v nvidia-smi &> /dev/null; then
    echo "✅ NVIDIA driver detected"
    nvidia-smi --query-gpu=name,driver_version,memory.total --format=csv,noheader
else
    echo "❌ NVIDIA driver not found"
fi

# Check CUDA
if command -v nvcc &> /dev/null; then
    echo "✅ CUDA toolkit detected"
    nvcc --version | grep "release"
else
    echo "⚠️ CUDA toolkit not found (may still work with conda/pip CUDA)"
fi

echo ""
echo "📋 Next Steps:"
echo "1. Activate virtual environment: source venv/bin/activate"
echo "2. Run GPU test: python3 test/scripts/test_gpu_infrastructure.py"
echo "3. If GPU libraries failed, check CUDA installation"
echo "4. For RAPIDS, you may need conda: conda install -c rapidsai cuml cudf"
echo ""

# Save installation log
echo "💾 Saving installation log..."
pip list > requirements_installed.txt
echo "✅ Installed packages saved to requirements_installed.txt"

echo "🎉 GPU Dependencies Installation Complete!"
