#!/usr/bin/env python3
"""
Setup Scheduled Runs
Creates crontab entries for automated data collection
"""

import os
import subprocess
import sys
from datetime import datetime

def setup_crontab():
    """Setup crontab entries for scheduled data collection"""
    
    print("🕐 Setting up Scheduled Data Collection")
    print("=" * 45)
    
    # Define project paths
    project_root = "/home/<USER>/solar-prediction-project"
    python_path = f"{project_root}/venv/bin/python"
    
    # Define scheduled jobs
    jobs = [
        {
            'name': 'Daily NASA POWER Collection',
            'schedule': '0 6 * * *',  # Daily at 6:00 AM
            'script': f'{project_root}/scripts/scheduled/daily_nasa_power_collection.py',
            'log': f'{project_root}/logs/nasa_power_daily.log'
        },
        {
            'name': 'Daily CAMS Collection',
            'schedule': '0 7 * * *',  # Daily at 7:00 AM
            'script': f'{project_root}/scripts/scheduled/daily_cams_collection.py',
            'log': f'{project_root}/logs/cams_daily.log'
        },
        {
            'name': 'Weekly Data Validation',
            'schedule': '0 8 * * 1',  # Weekly on Monday at 8:00 AM
            'script': f'{project_root}/scripts/validation/weekly_data_check.py',
            'log': f'{project_root}/logs/weekly_validation.log'
        }
    ]
    
    # Create logs directory
    logs_dir = f"{project_root}/logs"
    os.makedirs(logs_dir, exist_ok=True)
    print(f"📁 Created logs directory: {logs_dir}")
    
    # Get current crontab
    try:
        current_crontab = subprocess.check_output(['crontab', '-l'], stderr=subprocess.DEVNULL).decode('utf-8')
    except subprocess.CalledProcessError:
        current_crontab = ""
    
    print("\n📋 Current crontab entries:")
    if current_crontab.strip():
        for line in current_crontab.strip().split('\n'):
            if 'solar-prediction' in line:
                print(f"   {line}")
    else:
        print("   (no existing entries)")
    
    # Create new crontab entries
    new_entries = []
    new_entries.append("# Solar Prediction Project - Automated Data Collection")
    new_entries.append(f"# Setup on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    new_entries.append("")
    
    for job in jobs:
        comment = f"# {job['name']}"
        command = f"{job['schedule']} {python_path} {job['script']} >> {job['log']} 2>&1"
        
        new_entries.append(comment)
        new_entries.append(command)
        new_entries.append("")
    
    # Show proposed changes
    print("\n📝 Proposed crontab additions:")
    for entry in new_entries:
        if entry.strip():
            print(f"   {entry}")
    
    # Ask for confirmation
    proceed = input("\n🚀 Add these entries to crontab? (y/n): ").lower().strip()
    if proceed != 'y':
        print("❌ Crontab setup cancelled")
        return False
    
    # Add entries to crontab
    try:
        # Remove existing solar-prediction entries
        filtered_crontab = []
        for line in current_crontab.split('\n'):
            if 'solar-prediction' not in line.lower() and line.strip():
                filtered_crontab.append(line)
        
        # Add new entries
        updated_crontab = '\n'.join(filtered_crontab + new_entries)
        
        # Write to crontab
        process = subprocess.Popen(['crontab', '-'], stdin=subprocess.PIPE)
        process.communicate(input=updated_crontab.encode('utf-8'))
        
        if process.returncode == 0:
            print("✅ Crontab updated successfully")
            
            # Verify crontab
            verify_crontab = subprocess.check_output(['crontab', '-l']).decode('utf-8')
            solar_entries = [line for line in verify_crontab.split('\n') if 'solar-prediction' in line.lower()]
            print(f"📊 Added {len([e for e in solar_entries if e.strip() and not e.startswith('#')])} scheduled jobs")
            
            return True
        else:
            print("❌ Failed to update crontab")
            return False
            
    except Exception as e:
        print(f"❌ Error updating crontab: {e}")
        return False

def create_manual_test_script():
    """Create a script to manually test scheduled jobs"""
    
    print("\n🧪 Creating manual test script...")
    
    test_script_content = '''#!/bin/bash
# Manual test script for scheduled data collection

echo "🌟 Solar Prediction - Manual Data Collection Test"
echo "=" * 50

PROJECT_ROOT="/home/<USER>/solar-prediction-project"
PYTHON_PATH="$PROJECT_ROOT/venv/bin/python"

echo "📅 $(date)"
echo ""

# Test NASA POWER collection
echo "1️⃣ Testing NASA POWER daily collection..."
$PYTHON_PATH $PROJECT_ROOT/scripts/scheduled/daily_nasa_power_collection.py
echo ""

# Test CAMS collection (if available)
if [ -f "$PROJECT_ROOT/scripts/scheduled/daily_cams_collection.py" ]; then
    echo "2️⃣ Testing CAMS daily collection..."
    $PYTHON_PATH $PROJECT_ROOT/scripts/scheduled/daily_cams_collection.py
    echo ""
fi

# Test data validation
if [ -f "$PROJECT_ROOT/scripts/validation/weekly_data_check.py" ]; then
    echo "3️⃣ Testing data validation..."
    $PYTHON_PATH $PROJECT_ROOT/scripts/validation/weekly_data_check.py
    echo ""
fi

echo "✅ Manual test completed"
'''
    
    test_script_path = "/home/<USER>/solar-prediction-project/scripts/test_scheduled_collection.sh"
    
    try:
        with open(test_script_path, 'w') as f:
            f.write(test_script_content)
        
        # Make executable
        os.chmod(test_script_path, 0o755)
        
        print(f"✅ Created test script: {test_script_path}")
        print("   Run with: ./scripts/test_scheduled_collection.sh")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating test script: {e}")
        return False

def show_schedule_summary():
    """Show summary of scheduled jobs"""
    
    print("\n📅 Scheduled Jobs Summary:")
    print("=" * 30)
    print("🌅 06:00 Daily - NASA POWER data collection")
    print("🌍 07:00 Daily - CAMS data collection")
    print("📊 08:00 Monday - Weekly data validation")
    print("")
    print("📁 Logs location: /home/<USER>/solar-prediction-project/logs/")
    print("🧪 Manual test: ./scripts/test_scheduled_collection.sh")
    print("")
    print("🔧 Management commands:")
    print("   View crontab: crontab -l")
    print("   Edit crontab: crontab -e")
    print("   Remove all: crontab -r")

def main():
    """Main setup function"""
    
    print("🚀 Solar Prediction - Scheduled Runs Setup")
    print("=" * 50)
    
    # Setup crontab
    crontab_success = setup_crontab()
    
    # Create test script
    test_script_success = create_manual_test_script()
    
    # Show summary
    if crontab_success:
        show_schedule_summary()
    
    # Final status
    if crontab_success and test_script_success:
        print("\n🎯 Setup completed successfully!")
        print("   → Scheduled data collection is now active")
        print("   → Test manually with: ./scripts/test_scheduled_collection.sh")
        return True
    else:
        print("\n⚠️ Setup completed with issues")
        print("   → Some components may not be working")
        print("   → Check error messages above")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
