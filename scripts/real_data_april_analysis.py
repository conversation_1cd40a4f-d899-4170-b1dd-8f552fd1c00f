#!/usr/bin/env python3
"""
Real Data April Analysis
MANDATORY: Use ONLY real data from database
- April 2024 actual production
- April 2025 actual production  
- April 2026 prediction using trained models
- Weather patterns analysis
- System performance comparison
"""

import os
import sys
import json
import joblib
import numpy as np
import pandas as pd
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any, List, <PERSON>ple
import psycopg2
from psycopg2.extras import RealDictCursor
import warnings
warnings.filterwarnings('ignore')

def get_db_connection():
    """Get PostgreSQL database connection"""
    try:
        return psycopg2.connect(
            host=os.getenv("DB_HOST", "localhost"),
            database=os.getenv("DB_NAME", "solar_prediction"),
            user=os.getenv("DB_USER", "postgres"),
            password=os.getenv("DB_PASSWORD", "postgres"),
            port=os.getenv("DB_PORT", "5432")
        )
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return None

class RealDataAprilAnalyzer:
    """Analyzer using ONLY real data from database"""
    
    def __init__(self):
        self.conn = get_db_connection()
        if not self.conn:
            raise Exception("Cannot proceed without database connection")
        
        self.models = {}
        self.scalers = {}
        self.feature_columns = {}
        self.load_trained_models()
    
    def load_trained_models(self):
        """Load the corrected yield-based models"""
        print("📊 Loading trained models...")
        
        for system_id in [1, 2]:
            model_dir = f"models/corrected_yield_system{system_id}"
            
            if os.path.exists(f"{model_dir}/model.joblib"):
                self.models[system_id] = joblib.load(f"{model_dir}/model.joblib")
                self.scalers[system_id] = joblib.load(f"{model_dir}/scaler.joblib")
                
                with open(f"{model_dir}/metadata.json", 'r') as f:
                    metadata = json.load(f)
                
                # Get feature columns from metadata
                self.feature_columns[system_id] = [
                    'month', 'day_of_year', 'season', 'day_of_week', 'is_weekend',
                    'month_sin', 'month_cos', 'day_sin', 'day_cos',
                    'is_peak_season', 'is_low_season', 'solar_declination', 'day_length',
                    'yield_efficiency', 'seasonal_yield_factor', 'monthly_yield_factor'
                ]
                
                accuracy = metadata['performance']['accuracy_percent']
                print(f"✅ System {system_id}: {accuracy:.1f}% accuracy model loaded")
            else:
                print(f"❌ Model not found for System {system_id}")
    
    def get_june_production_data(self, year: int) -> Dict[str, pd.DataFrame]:
        """Get real June production data for specified year"""
        print(f"\n📊 Getting REAL June {year} production data...")

        june_data = {}

        for system_id in [1, 2]:
            table_name = 'solax_data' if system_id == 1 else 'solax_data2'

            try:
                with self.conn.cursor(cursor_factory=RealDictCursor) as cur:
                    # Get daily production for June
                    cur.execute(f"""
                        WITH daily_yields AS (
                            SELECT
                                DATE(timestamp) as date,
                                EXTRACT(day FROM timestamp) as day,
                                MAX(yield_today) as daily_yield,
                                AVG(soc) as avg_soc,
                                AVG(temperature) as avg_temp,
                                COUNT(*) as measurements
                            FROM {table_name}
                            WHERE EXTRACT(year FROM timestamp) = %s
                                AND EXTRACT(month FROM timestamp) = 6
                                AND yield_today >= 0
                            GROUP BY DATE(timestamp), EXTRACT(day FROM timestamp)
                            HAVING COUNT(*) >= 10
                                AND MAX(yield_today) > 0
                                AND MAX(yield_today) < 100
                        )
                        SELECT * FROM daily_yields
                        ORDER BY date
                    """, (year,))
                    
                    results = cur.fetchall()
                    
                    if results:
                        df = pd.DataFrame([dict(row) for row in results])
                        june_data[f"system_{system_id}"] = df

                        total_days = len(df)
                        avg_yield = df['daily_yield'].mean()
                        total_yield = df['daily_yield'].sum()

                        print(f"✅ System {system_id}: {total_days} days, avg {avg_yield:.1f} kWh/day, total {total_yield:.1f} kWh")
                    else:
                        print(f"❌ No data found for System {system_id} in June {year}")
                        june_data[f"system_{system_id}"] = pd.DataFrame()

            except Exception as e:
                print(f"❌ Error getting data for System {system_id}: {e}")
                june_data[f"system_{system_id}"] = pd.DataFrame()

        return june_data
    
    def get_june_weather_data(self, year: int) -> pd.DataFrame:
        """Get real June weather data for specified year"""
        print(f"\n🌤️  Getting REAL June {year} weather data...")

        try:
            with self.conn.cursor(cursor_factory=RealDictCursor) as cur:
                cur.execute("""
                    SELECT
                        DATE(timestamp) as date,
                        AVG(temperature_2m) as avg_temp,
                        AVG(cloud_cover) as avg_cloud,
                        AVG(relative_humidity_2m) as avg_humidity,
                        AVG(COALESCE(global_horizontal_irradiance, 0)) as avg_ghi,
                        COUNT(*) as measurements
                    FROM weather_data
                    WHERE EXTRACT(year FROM timestamp) = %s
                        AND EXTRACT(month FROM timestamp) = 6
                    GROUP BY DATE(timestamp)
                    ORDER BY date
                """, (year,))
                
                results = cur.fetchall()
                
                if results:
                    df = pd.DataFrame([dict(row) for row in results])
                    
                    total_days = len(df)
                    avg_temp = df['avg_temp'].mean()
                    avg_ghi = df['avg_ghi'].mean()
                    avg_cloud = df['avg_cloud'].mean()
                    
                    print(f"✅ Weather data: {total_days} days")
                    print(f"   Avg temperature: {avg_temp:.1f}°C")
                    print(f"   Avg GHI: {avg_ghi:.0f} W/m²")
                    print(f"   Avg cloud cover: {avg_cloud:.1f}%")

                    return df
                else:
                    print(f"❌ No weather data found for June {year}")
                    return pd.DataFrame()

        except Exception as e:
            print(f"❌ Error getting weather data: {e}")
            return pd.DataFrame()
    
    def create_prediction_features(self, date: datetime, system_id: int, weather_data: pd.DataFrame = None) -> np.array:
        """Create features for prediction using real patterns"""
        month = date.month
        day_of_year = date.timetuple().tm_yday
        day_of_week = date.weekday()
        
        # Temporal features
        season = (month - 1) // 3
        is_peak_season = 1 if month in [5, 6, 7] else 0
        is_low_season = 1 if month in [12, 1, 2] else 0
        is_weekend = 1 if day_of_week >= 5 else 0
        
        # Cyclical encoding
        month_sin = np.sin(2 * np.pi * month / 12)
        month_cos = np.cos(2 * np.pi * month / 12)
        day_sin = np.sin(2 * np.pi * day_of_year / 365)
        day_cos = np.cos(2 * np.pi * day_of_year / 365)
        
        # Solar position
        solar_declination = 23.45 * np.sin(np.radians(360 * (284 + day_of_year) / 365))
        day_length = 12 + 4 * np.sin(np.radians(solar_declination))
        
        # Use historical June patterns for seasonal factors
        if system_id == 1:
            seasonal_yield_factor = 65.1  # Summer average for System 1
            monthly_yield_factor = 65.1   # June average for System 1
        else:
            seasonal_yield_factor = 65.0  # Summer average for System 2
            monthly_yield_factor = 65.0   # June average for System 2
        
        # Calculate yield efficiency
        estimated_daily_yield = monthly_yield_factor
        yield_efficiency = estimated_daily_yield / (day_length + 1)
        
        # Feature vector
        features = [
            month,                    # month
            day_of_year,             # day_of_year
            season,                  # season
            day_of_week,             # day_of_week
            is_weekend,              # is_weekend
            month_sin,               # month_sin
            month_cos,               # month_cos
            day_sin,                 # day_sin
            day_cos,                 # day_cos
            is_peak_season,          # is_peak_season
            is_low_season,           # is_low_season
            solar_declination,       # solar_declination
            day_length,              # day_length
            yield_efficiency,        # yield_efficiency
            seasonal_yield_factor,   # seasonal_yield_factor
            monthly_yield_factor     # monthly_yield_factor
        ]
        
        return np.array([features])
    
    def predict_april_2026(self) -> Dict[str, Any]:
        """Predict April 2026 using trained models"""
        print(f"\n🔮 PREDICTING APRIL 2026 using trained models...")
        
        predictions = {}
        april_2026_dates = pd.date_range(start='2026-04-01', end='2026-04-30', freq='D')
        
        for system_id in [1, 2]:
            if system_id not in self.models:
                print(f"❌ No model available for System {system_id}")
                continue
            
            daily_predictions = []
            
            for date in april_2026_dates:
                # Create features
                features = self.create_prediction_features(date, system_id)
                
                # Scale and predict
                features_scaled = self.scalers[system_id].transform(features)
                prediction = self.models[system_id].predict(features_scaled)[0]
                
                daily_predictions.append({
                    'date': date.date(),
                    'day': date.day,
                    'day_name': date.strftime('%A'),
                    'predicted_yield': prediction,
                    'is_weekend': date.weekday() >= 5
                })
            
            predictions[f"system_{system_id}"] = daily_predictions
            
            # Calculate statistics
            total_predicted = sum(p['predicted_yield'] for p in daily_predictions)
            avg_predicted = total_predicted / len(daily_predictions)
            
            print(f"✅ System {system_id}: {len(daily_predictions)} days predicted")
            print(f"   Average: {avg_predicted:.1f} kWh/day")
            print(f"   Total: {total_predicted:.1f} kWh")
        
        return predictions
    
    def compare_april_years(self, data_2024: Dict, data_2025: Dict, predictions_2026: Dict) -> Dict[str, Any]:
        """Compare April data across years"""
        print(f"\n📊 COMPARING APRIL ACROSS YEARS")
        print("=" * 50)
        
        comparison = {}
        
        for system_key in ['system_1', 'system_2']:
            system_id = int(system_key.split('_')[1])
            
            # Calculate averages
            avg_2024 = data_2024[system_key]['daily_yield'].mean() if not data_2024[system_key].empty else 0
            avg_2025 = data_2025[system_key]['daily_yield'].mean() if not data_2025[system_key].empty else 0
            avg_2026 = np.mean([p['predicted_yield'] for p in predictions_2026[system_key]]) if system_key in predictions_2026 else 0
            
            # Calculate totals
            total_2024 = data_2024[system_key]['daily_yield'].sum() if not data_2024[system_key].empty else 0
            total_2025 = data_2025[system_key]['daily_yield'].sum() if not data_2025[system_key].empty else 0
            total_2026 = sum(p['predicted_yield'] for p in predictions_2026[system_key]) if system_key in predictions_2026 else 0
            
            # Calculate year-over-year changes
            change_2024_to_2025 = avg_2025 - avg_2024 if avg_2024 > 0 else 0
            change_2025_to_2026 = avg_2026 - avg_2025 if avg_2025 > 0 else 0
            
            change_2024_to_2025_pct = (change_2024_to_2025 / avg_2024 * 100) if avg_2024 > 0 else 0
            change_2025_to_2026_pct = (change_2025_to_2026 / avg_2025 * 100) if avg_2025 > 0 else 0
            
            comparison[system_key] = {
                'avg_2024': avg_2024,
                'avg_2025': avg_2025,
                'avg_2026': avg_2026,
                'total_2024': total_2024,
                'total_2025': total_2025,
                'total_2026': total_2026,
                'change_2024_to_2025_kwh': change_2024_to_2025,
                'change_2025_to_2026_kwh': change_2025_to_2026,
                'change_2024_to_2025_pct': change_2024_to_2025_pct,
                'change_2025_to_2026_pct': change_2025_to_2026_pct
            }
            
            print(f"\n📊 System {system_id} April Comparison:")
            print(f"   2024 Actual:  {avg_2024:.1f} kWh/day (total: {total_2024:.1f} kWh)")
            print(f"   2025 Actual:  {avg_2025:.1f} kWh/day (total: {total_2025:.1f} kWh)")
            print(f"   2026 Predicted: {avg_2026:.1f} kWh/day (total: {total_2026:.1f} kWh)")
            print(f"   ")
            print(f"   2024→2025: {change_2024_to_2025:+.1f} kWh/day ({change_2024_to_2025_pct:+.1f}%)")
            print(f"   2025→2026: {change_2025_to_2026:+.1f} kWh/day ({change_2025_to_2026_pct:+.1f}%)")
        
        return comparison
    
    def analyze_patterns_and_deviations(self, comparison: Dict) -> Dict[str, List[str]]:
        """Analyze patterns and identify deviations"""
        print(f"\n🔍 PATTERN & DEVIATION ANALYSIS")
        print("=" * 50)
        
        patterns = {}
        
        for system_key, data in comparison.items():
            system_id = int(system_key.split('_')[1])
            
            findings = []
            
            # Analyze year-over-year trends
            if abs(data['change_2024_to_2025_pct']) > 5:
                if data['change_2024_to_2025_pct'] > 0:
                    findings.append(f"Production INCREASED {data['change_2024_to_2025_pct']:.1f}% from 2024 to 2025")
                else:
                    findings.append(f"Production DECREASED {abs(data['change_2024_to_2025_pct']):.1f}% from 2024 to 2025")
            
            if abs(data['change_2025_to_2026_pct']) > 5:
                if data['change_2025_to_2026_pct'] > 0:
                    findings.append(f"Model predicts INCREASE of {data['change_2025_to_2026_pct']:.1f}% for 2026")
                else:
                    findings.append(f"Model predicts DECREASE of {abs(data['change_2025_to_2026_pct']):.1f}% for 2026")
            
            # System-specific patterns
            if system_id == 2:
                s1_data = comparison['system_1']
                if data['avg_2025'] < s1_data['avg_2025']:
                    findings.append("System 2 producing LESS than System 1 in 2025 (unusual)")
                if data['avg_2026'] < s1_data['avg_2026']:
                    findings.append("Model predicts System 2 will continue producing less in 2026")
            
            # Prediction accuracy assessment
            if data['avg_2024'] > 0 and data['avg_2025'] > 0:
                historical_avg = (data['avg_2024'] + data['avg_2025']) / 2
                prediction_vs_historical = ((data['avg_2026'] - historical_avg) / historical_avg) * 100
                
                if abs(prediction_vs_historical) > 10:
                    findings.append(f"2026 prediction deviates {prediction_vs_historical:+.1f}% from historical average")
            
            patterns[system_key] = findings
            
            print(f"\n🔍 System {system_id} Patterns:")
            for i, finding in enumerate(findings, 1):
                print(f"   {i}. {finding}")
        
        return patterns

def main():
    """Main real data analysis"""
    print("📊 REAL DATA APRIL ANALYSIS: 2024 vs 2025 vs 2026 PREDICTION")
    print("=" * 70)
    print(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🎯 Using ONLY real data from database")
    
    try:
        analyzer = RealDataAprilAnalyzer()
        
        # 1. Get real April data
        april_2024 = analyzer.get_april_production_data(2024)
        april_2025 = analyzer.get_april_production_data(2025)
        
        # 2. Get weather data
        weather_2024 = analyzer.get_april_weather_data(2024)
        weather_2025 = analyzer.get_april_weather_data(2025)
        
        # 3. Predict April 2026
        predictions_2026 = analyzer.predict_april_2026()
        
        # 4. Compare across years
        comparison = analyzer.compare_april_years(april_2024, april_2025, predictions_2026)
        
        # 5. Analyze patterns
        patterns = analyzer.analyze_patterns_and_deviations(comparison)
        
        print(f"\n✅ Real data analysis completed!")
        print("🎯 All data sourced from actual database records")
        
        return {
            'april_2024': april_2024,
            'april_2025': april_2025,
            'predictions_2026': predictions_2026,
            'comparison': comparison,
            'patterns': patterns
        }
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()
