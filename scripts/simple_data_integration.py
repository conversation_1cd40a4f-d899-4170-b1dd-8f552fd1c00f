#!/usr/bin/env python3
"""
Simple Data Integration for Enhanced Model v3 - System Aware
"""

import psycopg2
import json
from datetime import datetime

def create_integrated_data_enhanced():
    """Create integrated_data_enhanced table with system_id"""
    print("🔧 ENHANCED MODEL V3 - DATA INTEGRATION")
    print("=" * 50)
    
    try:
        # Connect to database
        conn = psycopg2.connect(
            host="localhost",
            database="solar_prediction",
            user="postgres",
            password="postgres"
        )
        cursor = conn.cursor()
        
        # Drop existing table if exists
        print("\n🗑️ Dropping existing integrated_data_enhanced table...")
        cursor.execute("DROP TABLE IF EXISTS integrated_data_enhanced")
        
        # Create new integrated table with system_id
        print("📊 Creating integrated_data_enhanced table...")
        create_table_sql = """
        CREATE TABLE integrated_data_enhanced (
            id SERIAL PRIMARY KEY,
            timestamp TIMESTAMP NOT NULL,
            system_id INTEGER NOT NULL,
            
            -- Solar data
            ac_power FLOAT,
            powerdc1 FLOAT,
            powerdc2 FLOAT,
            yield_today FLOAT,
            yield_total FLOAT,
            soc FLOAT,
            bat_power FLOAT,
            feedin_power FLOAT,
            feedin_energy FLOAT,
            consume_energy FLOAT,
            temperature FLOAT,
            
            -- Weather data
            direct_radiation FLOAT,
            diffuse_radiation FLOAT,
            shortwave_radiation FLOAT,
            direct_normal_irradiance FLOAT,
            temperature_2m FLOAT,
            relative_humidity_2m FLOAT,
            cloud_cover FLOAT,
            global_horizontal_irradiance FLOAT,
            solar_elevation_angle FLOAT,
            air_mass FLOAT,
            
            -- Metadata
            created_at TIMESTAMP DEFAULT NOW(),
            
            -- Indexes
            UNIQUE(timestamp, system_id)
        )
        """
        cursor.execute(create_table_sql)
        
        # Create indexes
        print("🔍 Creating indexes...")
        cursor.execute("CREATE INDEX idx_integrated_enhanced_timestamp ON integrated_data_enhanced(timestamp)")
        cursor.execute("CREATE INDEX idx_integrated_enhanced_system_id ON integrated_data_enhanced(system_id)")
        cursor.execute("CREATE INDEX idx_integrated_enhanced_timestamp_system ON integrated_data_enhanced(timestamp, system_id)")
        
        # Insert data from both systems
        print("\n📥 Integrating data from both solar systems...")
        
        # System 1 (solax_data)
        print("   Processing System 1 (solax_data)...")
        insert_sql = """
        INSERT INTO integrated_data_enhanced (
            timestamp, system_id, ac_power, powerdc1, powerdc2, yield_today, yield_total,
            soc, bat_power, feedin_power, feedin_energy, consume_energy, temperature,
            direct_radiation, diffuse_radiation, shortwave_radiation, direct_normal_irradiance,
            temperature_2m, relative_humidity_2m, cloud_cover, global_horizontal_irradiance,
            solar_elevation_angle, air_mass
        )
        SELECT 
            s.timestamp,
            COALESCE(s.system_id, 1) as system_id,
            s.ac_power,
            s.powerdc1,
            s.powerdc2,
            s.yield_today,
            s.yield_total,
            s.soc,
            s.bat_power,
            s.feedin_power,
            s.feedin_energy,
            s.consume_energy,
            s.temperature,
            w.direct_radiation,
            w.diffuse_radiation,
            w.shortwave_radiation,
            w.direct_normal_irradiance,
            w.temperature_2m,
            w.relative_humidity_2m,
            w.cloud_cover,
            w.global_horizontal_irradiance,
            w.solar_elevation_angle,
            w.air_mass
        FROM solax_data s
        LEFT JOIN weather_data w ON DATE_TRUNC('hour', s.timestamp) = DATE_TRUNC('hour', w.timestamp)
        WHERE s.timestamp >= '2025-04-16'
        ON CONFLICT (timestamp, system_id) DO NOTHING
        """
        cursor.execute(insert_sql)
        system1_count = cursor.rowcount
        
        # System 2 (solax_data2)
        print("   Processing System 2 (solax_data2)...")
        insert_sql = """
        INSERT INTO integrated_data_enhanced (
            timestamp, system_id, ac_power, powerdc1, powerdc2, yield_today, yield_total,
            soc, bat_power, feedin_power, feedin_energy, consume_energy, temperature,
            direct_radiation, diffuse_radiation, shortwave_radiation, direct_normal_irradiance,
            temperature_2m, relative_humidity_2m, cloud_cover, global_horizontal_irradiance,
            solar_elevation_angle, air_mass
        )
        SELECT 
            s.timestamp,
            COALESCE(s.system_id, 2) as system_id,
            s.ac_power,
            s.powerdc1,
            s.powerdc2,
            s.yield_today,
            s.yield_total,
            s.soc,
            s.bat_power,
            s.feedin_power,
            s.feedin_energy,
            s.consume_energy,
            s.temperature,
            w.direct_radiation,
            w.diffuse_radiation,
            w.shortwave_radiation,
            w.direct_normal_irradiance,
            w.temperature_2m,
            w.relative_humidity_2m,
            w.cloud_cover,
            w.global_horizontal_irradiance,
            w.solar_elevation_angle,
            w.air_mass
        FROM solax_data2 s
        LEFT JOIN weather_data w ON DATE_TRUNC('hour', s.timestamp) = DATE_TRUNC('hour', w.timestamp)
        WHERE s.timestamp >= '2025-04-16'
        ON CONFLICT (timestamp, system_id) DO NOTHING
        """
        cursor.execute(insert_sql)
        system2_count = cursor.rowcount
        
        # Get final statistics
        cursor.execute("SELECT COUNT(*), MIN(timestamp), MAX(timestamp) FROM integrated_data_enhanced")
        total_count, min_date, max_date = cursor.fetchone()
        
        cursor.execute("SELECT system_id, COUNT(*) FROM integrated_data_enhanced GROUP BY system_id ORDER BY system_id")
        system_stats = cursor.fetchall()
        
        conn.commit()
        cursor.close()
        conn.close()
        
        print(f"\n✅ DATA INTEGRATION COMPLETE!")
        print(f"   System 1 records: {system1_count}")
        print(f"   System 2 records: {system2_count}")
        print(f"   Total records: {total_count}")
        print(f"   Date range: {min_date} to {max_date}")
        print(f"\n📊 System breakdown:")
        for system_id, count in system_stats:
            print(f"   System {system_id}: {count} records")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = create_integrated_data_enhanced()
    if success:
        print("\n🎯 Ready for next step: Data Normalization")
    else:
        print("\n❌ Data integration failed")
