#!/bin/bash
# database_comparison.sh - Σύγκριση Local vs Docker PostgreSQL Databases

echo "🔍 SOLAR PREDICTION SYSTEM - DATABASE COMPARISON"
echo "================================================="
echo "Comparing Local PostgreSQL (5432) vs Docker PostgreSQL (5433)"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Database connection parameters
LOCAL_DB="postgresql://postgres:postgres@localhost:5432/solar_prediction"
DOCKER_DB="postgresql://postgres:postgres@localhost:5433/solar_prediction"

# Function to test database connectivity
test_connection() {
    local db_url=$1
    local db_name=$2
    
    if psql "$db_url" -c "SELECT 1;" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ $db_name connection successful${NC}"
        return 0
    else
        echo -e "${RED}❌ $db_name connection failed${NC}"
        return 1
    fi
}

# Function to get table list and counts
get_table_data() {
    local db_url=$1
    local output_file=$2
    
    psql "$db_url" -t -c "
        SELECT 
            t.table_name,
            COALESCE(s.n_live_tup, 0) as row_count,
            pg_size_pretty(pg_total_relation_size(c.oid)) as table_size
        FROM information_schema.tables t
        LEFT JOIN pg_stat_user_tables s ON s.relname = t.table_name
        LEFT JOIN pg_class c ON c.relname = t.table_name
        WHERE t.table_schema = 'public' 
        AND t.table_type = 'BASE TABLE'
        ORDER BY t.table_name;
    " > "$output_file"
}

# Test connections
echo "📡 Testing Database Connections:"
echo "--------------------------------"
test_connection "$LOCAL_DB" "Local PostgreSQL (5432)"
LOCAL_STATUS=$?

test_connection "$DOCKER_DB" "Docker PostgreSQL (5433)"
DOCKER_STATUS=$?

echo ""

if [ $LOCAL_STATUS -ne 0 ] && [ $DOCKER_STATUS -ne 0 ]; then
    echo -e "${RED}❌ Both databases are unreachable. Exiting.${NC}"
    exit 1
fi

# Create temporary files for comparison
LOCAL_TABLES="/tmp/local_tables.txt"
DOCKER_TABLES="/tmp/docker_tables.txt"

# Get table data from both databases
if [ $LOCAL_STATUS -eq 0 ]; then
    echo "📊 Analyzing Local Database..."
    get_table_data "$LOCAL_DB" "$LOCAL_TABLES"
else
    touch "$LOCAL_TABLES"
fi

if [ $DOCKER_STATUS -eq 0 ]; then
    echo "📊 Analyzing Docker Database..."
    get_table_data "$DOCKER_DB" "$DOCKER_TABLES"
else
    touch "$DOCKER_TABLES"
fi

echo ""
echo "📋 DATABASE COMPARISON RESULTS:"
echo "==============================="

# Create comparison report
echo ""
printf "%-25s %-15s %-10s %-15s %-10s %-10s\n" "TABLE NAME" "LOCAL ROWS" "LOCAL SIZE" "DOCKER ROWS" "DOCKER SIZE" "STATUS"
printf "%-25s %-15s %-10s %-15s %-10s %-10s\n" "-------------------------" "---------------" "----------" "---------------" "----------" "----------"

# Get all unique table names
ALL_TABLES=$(cat "$LOCAL_TABLES" "$DOCKER_TABLES" | awk '{print $1}' | sort -u | grep -v '^$')

for table in $ALL_TABLES; do
    # Get local data
    LOCAL_DATA=$(grep "^$table" "$LOCAL_TABLES" 2>/dev/null)
    if [ -n "$LOCAL_DATA" ]; then
        LOCAL_ROWS=$(echo "$LOCAL_DATA" | awk '{print $2}')
        LOCAL_SIZE=$(echo "$LOCAL_DATA" | awk '{print $3}')
    else
        LOCAL_ROWS="Missing"
        LOCAL_SIZE="N/A"
    fi
    
    # Get docker data
    DOCKER_DATA=$(grep "^$table" "$DOCKER_TABLES" 2>/dev/null)
    if [ -n "$DOCKER_DATA" ]; then
        DOCKER_ROWS=$(echo "$DOCKER_DATA" | awk '{print $2}')
        DOCKER_SIZE=$(echo "$DOCKER_DATA" | awk '{print $3}')
    else
        DOCKER_ROWS="Missing"
        DOCKER_SIZE="N/A"
    fi
    
    # Determine status
    if [ "$LOCAL_ROWS" = "Missing" ]; then
        STATUS="${BLUE}Docker Only${NC}"
    elif [ "$DOCKER_ROWS" = "Missing" ]; then
        STATUS="${RED}Local Only${NC}"
    elif [ "$LOCAL_ROWS" -eq "$DOCKER_ROWS" ]; then
        STATUS="${GREEN}Synced${NC}"
    else
        STATUS="${YELLOW}Different${NC}"
    fi
    
    printf "%-25s %-15s %-10s %-15s %-10s " "$table" "$LOCAL_ROWS" "$LOCAL_SIZE" "$DOCKER_ROWS" "$DOCKER_SIZE"
    echo -e "$STATUS"
done

echo ""
echo "📈 SUMMARY STATISTICS:"
echo "======================"

# Calculate summary statistics
TOTAL_LOCAL_TABLES=$(wc -l < "$LOCAL_TABLES" | tr -d ' ')
TOTAL_DOCKER_TABLES=$(wc -l < "$DOCKER_TABLES" | tr -d ' ')

echo "📊 Local Database Tables: $TOTAL_LOCAL_TABLES"
echo "📊 Docker Database Tables: $TOTAL_DOCKER_TABLES"

# Count critical data tables
CRITICAL_TABLES=("nasa_power_data" "solax_data" "solax_data2" "weather_data" "cams_radiation_data")

echo ""
echo "🔍 CRITICAL DATA TABLES ANALYSIS:"
echo "=================================="

for table in "${CRITICAL_TABLES[@]}"; do
    LOCAL_COUNT=$(grep "^$table" "$LOCAL_TABLES" 2>/dev/null | awk '{print $2}' || echo "0")
    DOCKER_COUNT=$(grep "^$table" "$DOCKER_TABLES" 2>/dev/null | awk '{print $2}' || echo "0")
    
    if [ "$LOCAL_COUNT" = "" ]; then LOCAL_COUNT="0"; fi
    if [ "$DOCKER_COUNT" = "" ]; then DOCKER_COUNT="0"; fi
    
    echo -n "📋 $table: Local=$LOCAL_COUNT, Docker=$DOCKER_COUNT"
    
    if [ "$LOCAL_COUNT" -gt "$DOCKER_COUNT" ]; then
        MISSING=$((LOCAL_COUNT - DOCKER_COUNT))
        echo -e " ${RED}(Missing $MISSING records in Docker)${NC}"
    elif [ "$DOCKER_COUNT" -gt "$LOCAL_COUNT" ]; then
        EXTRA=$((DOCKER_COUNT - LOCAL_COUNT))
        echo -e " ${BLUE}(Extra $EXTRA records in Docker)${NC}"
    else
        echo -e " ${GREEN}(Synced)${NC}"
    fi
done

echo ""
echo "💾 DATA MIGRATION RECOMMENDATIONS:"
echo "=================================="

# Check if migration is needed
MIGRATION_NEEDED=false
for table in "${CRITICAL_TABLES[@]}"; do
    LOCAL_COUNT=$(grep "^$table" "$LOCAL_TABLES" 2>/dev/null | awk '{print $2}' || echo "0")
    DOCKER_COUNT=$(grep "^$table" "$DOCKER_TABLES" 2>/dev/null | awk '{print $2}' || echo "0")
    
    if [ "$LOCAL_COUNT" = "" ]; then LOCAL_COUNT="0"; fi
    if [ "$DOCKER_COUNT" = "" ]; then DOCKER_COUNT="0"; fi
    
    if [ "$LOCAL_COUNT" -gt "$DOCKER_COUNT" ]; then
        MIGRATION_NEEDED=true
        break
    fi
done

if [ "$MIGRATION_NEEDED" = true ]; then
    echo -e "${YELLOW}⚠️ Migration Required:${NC}"
    echo "   1. Export data from Local PostgreSQL (port 5432)"
    echo "   2. Import data to Docker PostgreSQL (port 5433)"
    echo "   3. Update data collection scripts to use port 5433"
    echo "   4. Verify data integrity after migration"
    echo ""
    echo "🚀 Suggested Migration Command:"
    echo "   pg_dump -U postgres -d solar_prediction -p 5432 > migration_backup.sql"
    echo "   cat migration_backup.sql | docker exec -i solar-prediction-db psql -U postgres -d solar_prediction"
else
    echo -e "${GREEN}✅ No migration needed - databases are synced${NC}"
fi

# Cleanup
rm -f "$LOCAL_TABLES" "$DOCKER_TABLES"

echo ""
echo "✅ Database comparison completed!"

