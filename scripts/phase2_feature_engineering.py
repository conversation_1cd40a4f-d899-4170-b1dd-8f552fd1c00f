#!/usr/bin/env python3
"""
Phase 2: Advanced Feature Engineering
Creates 45+ advanced features from real solar and weather data
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import psycopg2
from psycopg2.extras import RealDictCursor
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import math

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AdvancedFeatureEngineer:
    """Advanced feature engineering for solar prediction ML pipeline"""
    
    def __init__(self):
        self.db_config = {
            'host': 'localhost',
            'database': 'solar_prediction',
            'user': 'postgres',
            'password': ''
        }
        
        # Solar system location (Marathon, Attica)
        self.latitude = 38.141348
        self.longitude = 24.007165
        
        self.features_created = []
        self.processing_stats = {}
    
    def connect_database(self):
        """Connect to database"""
        try:
            conn = psycopg2.connect(**self.db_config)
            logger.info("✅ Connected to database")
            return conn
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            return None
    
    def load_integrated_data(self, limit_days=None):
        """Load integrated solar and weather data"""
        
        logger.info("📊 Loading integrated solar and weather data...")
        
        conn = self.connect_database()
        if not conn:
            return None
        
        try:
            # Build query with optional date limit
            date_filter = ""
            if limit_days:
                date_filter = f"AND s1.timestamp >= NOW() - INTERVAL '{limit_days} days'"
            
            query = f"""
            SELECT 
                s1.timestamp,
                'system_1' as system_id,
                s1.yield_today,
                s1.yield_total as total_yield,
                s1.ac_power,
                s1.soc as battery_soc,
                s1.bat_power as battery_power,
                s1.temperature as system_temperature,
                w.temperature_2m as ambient_temperature,
                w.relative_humidity_2m as humidity,
                w.cloud_cover,
                w.global_horizontal_irradiance as ghi,
                w.direct_normal_irradiance as dni,
                w.diffuse_radiation as dhi,
                w.solar_elevation_angle,
                w.air_mass
            FROM solax_data s1
            LEFT JOIN weather_data w ON 
                DATE_TRUNC('hour', s1.timestamp) = DATE_TRUNC('hour', w.timestamp)
            WHERE s1.timestamp IS NOT NULL {date_filter}
            
            UNION ALL
            
            SELECT 
                s2.timestamp,
                'system_2' as system_id,
                s2.yield_today,
                s2.total_yield,
                s2.ac_power,
                s2.soc as battery_soc,
                s2.bat_power as battery_power,
                s2.temperature as system_temperature,
                w.temperature_2m as ambient_temperature,
                w.relative_humidity_2m as humidity,
                w.cloud_cover,
                w.global_horizontal_irradiance as ghi,
                w.direct_normal_irradiance as dni,
                w.diffuse_radiation as dhi,
                w.solar_elevation_angle,
                w.air_mass
            FROM solax_data2 s2
            LEFT JOIN weather_data w ON 
                DATE_TRUNC('hour', s2.timestamp) = DATE_TRUNC('hour', w.timestamp)
            WHERE s2.timestamp IS NOT NULL {date_filter}
            
            ORDER BY timestamp, system_id
            """
            
            df = pd.read_sql(query, conn)
            conn.close()
            
            logger.info(f"📊 Loaded {len(df):,} integrated records")
            
            # Convert timestamp to datetime
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            
            # Basic data cleaning
            df = df.dropna(subset=['timestamp', 'system_id'])
            
            logger.info(f"📊 After cleaning: {len(df):,} records")
            
            self.processing_stats['raw_records'] = len(df)
            
            return df
            
        except Exception as e:
            logger.error(f"❌ Failed to load integrated data: {e}")
            conn.close()
            return None
    
    def add_temporal_features(self, df):
        """Add temporal and cyclical features"""
        
        logger.info("⏰ Adding temporal features...")
        
        # Extract basic time components
        df['hour'] = df['timestamp'].dt.hour
        df['day_of_year'] = df['timestamp'].dt.dayofyear
        df['month'] = df['timestamp'].dt.month
        df['weekday'] = df['timestamp'].dt.weekday
        df['week_of_year'] = df['timestamp'].dt.isocalendar().week
        
        # Cyclical encoding for hour (24-hour cycle)
        df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
        
        # Cyclical encoding for day of year (365-day cycle)
        df['day_of_year_sin'] = np.sin(2 * np.pi * df['day_of_year'] / 365)
        df['day_of_year_cos'] = np.cos(2 * np.pi * df['day_of_year'] / 365)
        
        # Cyclical encoding for month (12-month cycle)
        df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
        
        # Season indicators
        df['is_summer'] = ((df['month'] >= 6) & (df['month'] <= 8)).astype(int)
        df['is_winter'] = ((df['month'] >= 12) | (df['month'] <= 2)).astype(int)
        df['is_spring'] = ((df['month'] >= 3) & (df['month'] <= 5)).astype(int)
        df['is_autumn'] = ((df['month'] >= 9) & (df['month'] <= 11)).astype(int)
        
        # Weekend indicator
        df['is_weekend'] = (df['weekday'] >= 5).astype(int)
        
        # Time of day categories
        df['is_morning'] = ((df['hour'] >= 6) & (df['hour'] < 12)).astype(int)
        df['is_afternoon'] = ((df['hour'] >= 12) & (df['hour'] < 18)).astype(int)
        df['is_evening'] = ((df['hour'] >= 18) & (df['hour'] < 22)).astype(int)
        df['is_night'] = ((df['hour'] >= 22) | (df['hour'] < 6)).astype(int)
        
        temporal_features = [
            'hour', 'day_of_year', 'month', 'weekday', 'week_of_year',
            'hour_sin', 'hour_cos', 'day_of_year_sin', 'day_of_year_cos',
            'month_sin', 'month_cos', 'is_summer', 'is_winter', 'is_spring',
            'is_autumn', 'is_weekend', 'is_morning', 'is_afternoon', 
            'is_evening', 'is_night'
        ]
        
        self.features_created.extend(temporal_features)
        logger.info(f"   ✅ Added {len(temporal_features)} temporal features")
        
        return df
    
    def add_solar_geometry_features(self, df):
        """Add solar geometry and astronomical features"""

        logger.info("☀️ Adding solar geometry features...")

        # Simplified solar calculations without external libraries
        def calculate_solar_position(timestamp, latitude, longitude):
            """Simplified solar position calculation"""

            # Day of year
            day_of_year = timestamp.timetuple().tm_yday

            # Solar declination (simplified)
            declination = 23.45 * np.sin(np.radians(360 * (284 + day_of_year) / 365))

            # Hour angle
            hour = timestamp.hour + timestamp.minute / 60
            hour_angle = 15 * (hour - 12)  # 15 degrees per hour from solar noon

            # Solar elevation
            lat_rad = np.radians(latitude)
            dec_rad = np.radians(declination)
            hour_rad = np.radians(hour_angle)

            elevation = np.degrees(np.arcsin(
                np.sin(lat_rad) * np.sin(dec_rad) +
                np.cos(lat_rad) * np.cos(dec_rad) * np.cos(hour_rad)
            ))

            # Solar azimuth (simplified)
            azimuth = np.degrees(np.arctan2(
                np.sin(hour_rad),
                np.cos(hour_rad) * np.sin(lat_rad) - np.tan(dec_rad) * np.cos(lat_rad)
            ))

            # Day length calculation
            day_length_hours = 2 * np.degrees(np.arccos(-np.tan(lat_rad) * np.tan(dec_rad))) / 15

            return {
                'solar_elevation': max(0, elevation),
                'solar_azimuth': azimuth,
                'day_length': day_length_hours,
                'declination': declination
            }

        # Calculate solar features for each row
        solar_features = []

        for idx, row in df.iterrows():
            timestamp = row['timestamp']

            solar_pos = calculate_solar_position(timestamp, self.latitude, self.longitude)

            # Additional calculations
            hour = timestamp.hour + timestamp.minute / 60

            # Approximate sunrise/sunset (simplified)
            day_length = solar_pos['day_length']
            sunrise_hour = 12 - day_length / 2
            sunset_hour = 12 + day_length / 2

            # Time to solar noon
            time_to_solar_noon = abs(hour - 12)

            # Daylight indicator
            is_daylight = 1 if sunrise_hour <= hour <= sunset_hour else 0

            solar_features.append({
                'solar_elevation': solar_pos['solar_elevation'],
                'solar_azimuth': solar_pos['solar_azimuth'],
                'day_length': day_length,
                'time_to_solar_noon': time_to_solar_noon,
                'is_daylight': is_daylight,
                'sunrise_hour': sunrise_hour,
                'sunset_hour': sunset_hour,
                'declination': solar_pos['declination']
            })

        # Add solar features to dataframe
        solar_df = pd.DataFrame(solar_features)
        for col in solar_df.columns:
            df[col] = solar_df[col]

        # Additional solar calculations
        df['solar_elevation_normalized'] = df['solar_elevation'] / 90  # Normalize to 0-1
        df['optimal_solar_angle'] = np.abs(df['solar_elevation'] - 45) / 45  # Distance from optimal 45°

        solar_feature_names = list(solar_df.columns) + ['solar_elevation_normalized', 'optimal_solar_angle']
        self.features_created.extend(solar_feature_names)
        logger.info(f"   ✅ Added {len(solar_feature_names)} solar geometry features")

        return df
    
    def add_weather_features(self, df):
        """Add advanced weather features"""
        
        logger.info("🌤️ Adding weather features...")
        
        # Normalize weather features
        df['ghi_normalized'] = df['ghi'] / 1000  # Normalize to typical max GHI
        df['dni_normalized'] = df['dni'] / 900   # Normalize to typical max DNI
        df['dhi_normalized'] = df['dhi'] / 500   # Normalize to typical max DHI
        
        # Temperature features
        df['ambient_temp_normalized'] = (df['ambient_temperature'] - 0) / 40  # 0-40°C range
        df['temp_optimal_deviation'] = np.abs(df['ambient_temperature'] - 25) / 25  # Distance from 25°C optimal
        
        # Cloud features
        df['cloud_cover_normalized'] = df['cloud_cover'] / 100
        df['clear_sky_factor'] = 1 - df['cloud_cover_normalized']
        
        # Clear sky index (actual GHI / theoretical clear sky GHI)
        df['clear_sky_index'] = np.where(
            df['solar_elevation'] > 0,
            df['ghi'] / (1000 * np.sin(np.radians(df['solar_elevation']))),
            0
        )
        df['clear_sky_index'] = np.clip(df['clear_sky_index'], 0, 1.2)
        
        # Humidity features
        df['humidity_normalized'] = df['humidity'] / 100
        df['humidity_comfort'] = 1 - np.abs(df['humidity'] - 50) / 50  # Distance from 50% optimal
        
        # Weather stability indicators
        df['weather_variability'] = df['cloud_cover'] * df['humidity_normalized']
        
        # Air mass correction (if available)
        df['air_mass_corrected_ghi'] = np.where(
            df['air_mass'].notna() & (df['air_mass'] > 0),
            df['ghi'] * df['air_mass'],
            df['ghi']
        )
        
        weather_features = [
            'ghi_normalized', 'dni_normalized', 'dhi_normalized',
            'ambient_temp_normalized', 'temp_optimal_deviation',
            'cloud_cover_normalized', 'clear_sky_factor', 'clear_sky_index',
            'humidity_normalized', 'humidity_comfort', 'weather_variability',
            'air_mass_corrected_ghi'
        ]
        
        self.features_created.extend(weather_features)
        logger.info(f"   ✅ Added {len(weather_features)} weather features")
        
        return df
    
    def add_system_features(self, df):
        """Add system-specific features"""
        
        logger.info("🔋 Adding system features...")
        
        # Battery features
        df['battery_soc_normalized'] = df['battery_soc'] / 100
        df['battery_power_normalized'] = df['battery_power'] / 12000  # Normalize to max battery power
        
        # Battery state indicators
        df['battery_charging'] = (df['battery_power'] > 100).astype(int)
        df['battery_discharging'] = (df['battery_power'] < -100).astype(int)
        df['battery_idle'] = ((df['battery_power'] >= -100) & (df['battery_power'] <= 100)).astype(int)
        
        # Battery capacity zones
        df['battery_low'] = (df['battery_soc'] < 20).astype(int)
        df['battery_medium'] = ((df['battery_soc'] >= 20) & (df['battery_soc'] < 80)).astype(int)
        df['battery_high'] = (df['battery_soc'] >= 80).astype(int)
        
        # System efficiency indicators
        df['ac_power_normalized'] = df['ac_power'] / 10500  # Normalize to system capacity
        
        # Performance ratio (actual power / theoretical power based on irradiance)
        df['performance_ratio'] = np.where(
            (df['ghi'] > 100) & (df['solar_elevation'] > 0),
            df['ac_power'] / (df['ghi'] * 10.5),  # 10.5kW system capacity
            0
        )
        df['performance_ratio'] = np.clip(df['performance_ratio'], 0, 1.5)
        
        # System temperature effects
        df['temp_derating_factor'] = np.where(
            df['system_temperature'].notna(),
            1 - (df['system_temperature'] - 25) * 0.004,  # 0.4% per degree above 25°C
            1
        )
        df['temp_derating_factor'] = np.clip(df['temp_derating_factor'], 0.7, 1.1)
        
        # System ID encoding
        df['is_system_1'] = (df['system_id'] == 'system_1').astype(int)
        df['is_system_2'] = (df['system_id'] == 'system_2').astype(int)
        
        system_features = [
            'battery_soc_normalized', 'battery_power_normalized',
            'battery_charging', 'battery_discharging', 'battery_idle',
            'battery_low', 'battery_medium', 'battery_high',
            'ac_power_normalized', 'performance_ratio', 'temp_derating_factor',
            'is_system_1', 'is_system_2'
        ]
        
        self.features_created.extend(system_features)
        logger.info(f"   ✅ Added {len(system_features)} system features")
        
        return df
    
    def add_lag_features(self, df):
        """Add lag and rolling window features"""
        
        logger.info("📈 Adding lag and rolling features...")
        
        # Sort by system and timestamp
        df = df.sort_values(['system_id', 'timestamp'])
        
        lag_features = []
        
        # Process each system separately
        for system_id in df['system_id'].unique():
            system_mask = df['system_id'] == system_id
            system_df = df[system_mask].copy()
            
            # Lag features (previous values)
            for col in ['yield_today', 'ac_power', 'battery_soc', 'ghi', 'ambient_temperature']:
                if col in system_df.columns:
                    system_df[f'{col}_lag1'] = system_df[col].shift(1)
                    system_df[f'{col}_lag2'] = system_df[col].shift(2)
                    system_df[f'{col}_lag6'] = system_df[col].shift(6)  # 30 minutes ago (5-min intervals)
                    system_df[f'{col}_lag12'] = system_df[col].shift(12)  # 1 hour ago
                    
                    lag_features.extend([f'{col}_lag1', f'{col}_lag2', f'{col}_lag6', f'{col}_lag12'])
            
            # Rolling window features
            for col in ['yield_today', 'ac_power', 'ghi', 'cloud_cover']:
                if col in system_df.columns:
                    # 30-minute rolling windows (6 periods of 5 minutes)
                    system_df[f'{col}_rolling_mean_6'] = system_df[col].rolling(6, min_periods=1).mean()
                    system_df[f'{col}_rolling_std_6'] = system_df[col].rolling(6, min_periods=1).std()
                    system_df[f'{col}_rolling_max_6'] = system_df[col].rolling(6, min_periods=1).max()
                    system_df[f'{col}_rolling_min_6'] = system_df[col].rolling(6, min_periods=1).min()
                    
                    # 1-hour rolling windows (12 periods)
                    system_df[f'{col}_rolling_mean_12'] = system_df[col].rolling(12, min_periods=1).mean()
                    
                    lag_features.extend([
                        f'{col}_rolling_mean_6', f'{col}_rolling_std_6',
                        f'{col}_rolling_max_6', f'{col}_rolling_min_6',
                        f'{col}_rolling_mean_12'
                    ])
            
            # Update main dataframe
            df.loc[system_mask, system_df.columns] = system_df
        
        # Remove duplicates from lag_features list
        lag_features = list(set(lag_features))
        
        self.features_created.extend(lag_features)
        logger.info(f"   ✅ Added {len(lag_features)} lag and rolling features")
        
        return df
    
    def add_derived_features(self, df):
        """Add derived and interaction features"""
        
        logger.info("🔬 Adding derived features...")
        
        # Energy efficiency features
        df['yield_per_hour'] = df['yield_today'] / (df['hour'] + 1)  # Avoid division by zero
        df['power_to_irradiance_ratio'] = np.where(
            df['ghi'] > 50,
            df['ac_power'] / df['ghi'],
            0
        )
        
        # Weather interaction features
        df['temp_humidity_index'] = df['ambient_temperature'] * (1 + df['humidity_normalized'])
        df['cloud_temp_interaction'] = df['cloud_cover_normalized'] * df['temp_optimal_deviation']
        
        # Solar-weather interactions
        df['solar_irradiance_efficiency'] = df['solar_elevation_normalized'] * df['clear_sky_factor']
        df['optimal_conditions_score'] = (
            df['solar_elevation_normalized'] * 
            df['clear_sky_factor'] * 
            (1 - df['temp_optimal_deviation']) *
            (1 - df['humidity_normalized'] * 0.5)
        )
        
        # Battery-solar interactions
        df['battery_solar_synergy'] = df['battery_soc_normalized'] * df['solar_elevation_normalized']
        df['energy_storage_potential'] = (1 - df['battery_soc_normalized']) * df['ac_power_normalized']
        
        # Time-based patterns
        df['weekend_solar_factor'] = df['is_weekend'] * df['solar_elevation_normalized']
        df['seasonal_efficiency'] = df['is_summer'] * 1.2 + df['is_winter'] * 0.8 + (df['is_spring'] + df['is_autumn']) * 1.0
        
        derived_features = [
            'yield_per_hour', 'power_to_irradiance_ratio', 'temp_humidity_index',
            'cloud_temp_interaction', 'solar_irradiance_efficiency', 'optimal_conditions_score',
            'battery_solar_synergy', 'energy_storage_potential', 'weekend_solar_factor',
            'seasonal_efficiency'
        ]
        
        self.features_created.extend(derived_features)
        logger.info(f"   ✅ Added {len(derived_features)} derived features")
        
        return df
    
    def run_feature_engineering(self, limit_days=30):
        """Run complete feature engineering pipeline"""
        
        logger.info("🚀 Starting advanced feature engineering...")
        
        # Load data
        df = self.load_integrated_data(limit_days=limit_days)
        if df is None:
            logger.error("❌ Failed to load data")
            return None
        
        # Add all feature categories
        df = self.add_temporal_features(df)
        df = self.add_solar_geometry_features(df)
        df = self.add_weather_features(df)
        df = self.add_system_features(df)
        df = self.add_lag_features(df)
        df = self.add_derived_features(df)
        
        # Final data cleaning
        df = df.replace([np.inf, -np.inf], np.nan)
        
        # Store processing statistics
        self.processing_stats.update({
            'final_records': len(df),
            'total_features': len(self.features_created),
            'feature_categories': {
                'temporal': 20,
                'solar_geometry': 9,
                'weather': 12,
                'system': 13,
                'lag_rolling': len([f for f in self.features_created if 'lag' in f or 'rolling' in f]),
                'derived': 10
            }
        })
        
        logger.info(f"🎉 Feature engineering completed!")
        logger.info(f"📊 Final dataset: {len(df):,} records with {len(self.features_created)} features")
        
        return df

def main():
    """Main feature engineering function"""
    
    print("🔧 PHASE 2: ADVANCED FEATURE ENGINEERING")
    print("="*60)
    print("📊 Processing real solar and weather data")
    print("🔬 Creating 45+ advanced features for ML pipeline")
    print()
    
    try:
        # Initialize feature engineer
        engineer = AdvancedFeatureEngineer()
        
        # Run feature engineering (limit to 30 days for testing)
        df = engineer.run_feature_engineering(limit_days=30)
        
        if df is not None:
            print(f"\n🎉 Feature engineering completed successfully!")
            print(f"📊 Created {len(engineer.features_created)} features")
            print(f"📈 Final dataset: {len(df):,} records")
            
            # Save enhanced dataset
            output_path = 'data/enhanced_features_dataset.csv'
            df.to_csv(output_path, index=False)
            print(f"💾 Enhanced dataset saved to: {output_path}")
            
            return True
        else:
            print("❌ Feature engineering failed")
            return False
            
    except Exception as e:
        print(f"❌ Feature engineering failed: {e}")
        logger.exception("Feature engineering failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
