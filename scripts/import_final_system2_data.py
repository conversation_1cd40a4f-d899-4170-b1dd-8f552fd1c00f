#!/usr/bin/env python3
"""
Import Final System 2 Data
Import the last CSV file: 2025-06-01 to 2025-06-04
"""

import os
import subprocess
from datetime import datetime

def import_final_csv():
    """Import the final CSV file"""
    print("🔄 IMPORTING FINAL SYSTEM 2 DATA")
    print("=" * 40)
    
    csv_file = "data/raw/System2/Plant Reports 2025-06-01-2025-06-04.csv"
    
    if not os.path.exists(csv_file):
        print(f"❌ File not found: {csv_file}")
        return False
    
    print(f"📊 Processing: {csv_file}")
    
    try:
        # Read file with proper encoding
        with open(csv_file, 'r', encoding='utf-8-sig') as f:
            lines = f.readlines()
        
        print(f"✅ Read {len(lines)} lines")
        
        # Parse header
        header_line = lines[0].strip().replace('"', '')
        print(f"📋 Header: {header_line}")
        
        # Create SQL statements
        sql_statements = []
        
        # Add setup
        sql_statements.append("""
-- Final System 2 Import (June 1-4, 2025)
-- Clear any existing data for this period first
DELETE FROM solax_data2 WHERE timestamp >= '2025-06-01' AND timestamp <= '2025-06-04';
""")
        
        valid_count = 0
        batch_size = 50
        current_batch = []
        
        # Process data lines
        for line_num, line in enumerate(lines[1:], 2):
            line = line.strip()
            if not line:
                continue
            
            try:
                # Remove quotes and split
                clean_line = line.replace('"', '')
                parts = clean_line.split(',')
                
                if len(parts) >= 4:
                    # Extract data
                    timestamp_str = parts[1].strip()
                    yield_str = parts[3].strip()  # Daily inverter output
                    
                    # Validate timestamp format
                    dt = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
                    yield_val = float(yield_str)
                    
                    # Only valid data
                    if yield_val >= 0 and yield_val < 100:
                        current_batch.append((dt, yield_val))
                        valid_count += 1
                        
                        # Process batch when full
                        if len(current_batch) >= batch_size:
                            sql_statements.append(create_batch_insert(current_batch))
                            current_batch = []
                
            except (ValueError, IndexError):
                continue
        
        # Process remaining batch
        if current_batch:
            sql_statements.append(create_batch_insert(current_batch))
        
        print(f"✅ Processing completed: {valid_count} valid records")
        
        if valid_count == 0:
            print("❌ No valid records found!")
            return False
        
        # Write SQL file
        sql_file = "system2_final_import.sql"
        with open(sql_file, 'w') as f:
            f.write('\n'.join(sql_statements))
        
        print(f"✅ SQL file created: {sql_file}")
        
        # Execute SQL
        print(f"\n💾 Executing SQL import...")
        cmd = ['psql', '-h', 'localhost', '-U', 'postgres', '-d', 'solar_prediction', '-f', sql_file]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ SQL execution successful!")
            
            # Clean up
            os.remove(sql_file)
            
            return True
        else:
            print(f"❌ SQL execution failed: {result.stderr}")
            return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def create_batch_insert(batch):
    """Create batch INSERT statement"""
    values = []
    for dt, yield_val in batch:
        values.append(f"('{dt}', 'SYSTEM2_FINAL', 'SYSTEM2_FINAL', {yield_val}, 50.0, 0.0, 20.0)")
    
    return f"""
INSERT INTO solax_data2 (timestamp, inverter_sn, wifi_sn, yield_today, soc, bat_power, temperature) VALUES
{', '.join(values)};
"""

def verify_complete_coverage():
    """Verify we have complete data coverage"""
    print(f"\n📊 VERIFYING COMPLETE DATA COVERAGE")
    print("=" * 40)
    
    try:
        # Check total records
        cmd = ['psql', '-h', 'localhost', '-U', 'postgres', '-d', 'solar_prediction', '-c', 
               "SELECT COUNT(*) as total_records FROM solax_data2;"]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ Total System 2 Records:")
            print(result.stdout)
        
        # Check date range
        cmd2 = ['psql', '-h', 'localhost', '-U', 'postgres', '-d', 'solar_prediction', '-c', 
                "SELECT MIN(timestamp) as earliest, MAX(timestamp) as latest FROM solax_data2;"]
        
        result2 = subprocess.run(cmd2, capture_output=True, text=True, timeout=30)
        
        if result2.returncode == 0:
            print("✅ System 2 Date Range:")
            print(result2.stdout)
        
        # Check monthly coverage
        cmd3 = ['psql', '-h', 'localhost', '-U', 'postgres', '-d', 'solar_prediction', '-c', 
                """SELECT 
                    EXTRACT(year FROM timestamp) as year,
                    EXTRACT(month FROM timestamp) as month,
                    COUNT(*) as records,
                    MIN(DATE(timestamp)) as first_date,
                    MAX(DATE(timestamp)) as last_date
                FROM solax_data2 
                GROUP BY EXTRACT(year FROM timestamp), EXTRACT(month FROM timestamp) 
                ORDER BY year, month;"""]
        
        result3 = subprocess.run(cmd3, capture_output=True, text=True, timeout=30)
        
        if result3.returncode == 0:
            print("✅ System 2 Monthly Coverage:")
            print(result3.stdout)
        
        return True
        
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        return False

def main():
    """Main function"""
    print("🚀 FINAL SYSTEM 2 DATA IMPORT")
    print("=" * 35)
    print(f"Started: {datetime.now()}")
    
    # Import final CSV
    if not import_final_csv():
        print("❌ Final import failed!")
        return False
    
    # Verify complete coverage
    if not verify_complete_coverage():
        print("❌ Verification failed!")
        return False
    
    print(f"\n🎉 FINAL IMPORT COMPLETED!")
    print("✅ System 2 data is now COMPLETE from March 2024 to June 4, 2025!")
    
    return True

if __name__ == "__main__":
    main()
