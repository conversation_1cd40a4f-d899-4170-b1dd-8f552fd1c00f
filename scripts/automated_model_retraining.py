#!/usr/bin/env python3
"""
Automated Model Retraining Pipeline
Περιοδική επανεκπαίδευση των multi-horizon μοντέλων με νέα δεδομένα
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project/src')

import psycopg2
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional
import schedule
import time

from models.multi_horizon_predictor import MultiHorizonPredictor
from core.config import settings
from core.database import get_database_connection

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/home/<USER>/solar-prediction-project/logs/model_retraining.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AutomatedModelRetrainer:
    """
    Automated pipeline για επανεκπαίδευση μοντέλων
    """
    
    def __init__(self):
        self.predictor = MultiHorizonPredictor()
        self.db_connection_string = "postgresql://postgres:postgres@localhost:5433/solar_prediction"
        self.backup_dir = Path("/home/<USER>/solar-prediction-project/data/model_backups")
        self.backup_dir.mkdir(exist_ok=True)
        
        # Retraining thresholds
        self.performance_thresholds = {
            'daily': {'min_r2': 0.85, 'max_mae': 8.0},
            'monthly': {'min_r2': 0.90, 'max_mae': 5.0},
            'yearly': {'min_r2': 0.75, 'max_mae': 3.0}
        }
        
        # Minimum data requirements
        self.min_data_requirements = {
            'daily': 365,    # 1 year of daily data
            'monthly': 24,   # 2 years of monthly data
            'yearly': 3      # 3 years of yearly data
        }
    
    def connect_database(self):
        """Σύνδεση στη βάση δεδομένων"""
        try:
            conn = psycopg2.connect(self.db_connection_string)
            logger.info("Database connection established")
            return conn
        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            return None
    
    def backup_current_models(self) -> bool:
        """Δημιουργεί backup των τρεχόντων μοντέλων"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = self.backup_dir / f"models_backup_{timestamp}"
            
            # Save current models
            success = self.predictor.save_models(str(backup_path))
            
            if success:
                logger.info(f"Models backed up to {backup_path}")
                return True
            else:
                logger.error("Failed to backup models")
                return False
                
        except Exception as e:
            logger.error(f"Model backup failed: {e}")
            return False
    
    def load_training_data(self, system_id: int, horizon: str) -> Optional[pd.DataFrame]:
        """Φορτώνει δεδομένα εκπαίδευσης για συγκεκριμένο horizon"""
        conn = self.connect_database()
        if not conn:
            return None
        
        try:
            table = 'solax_data' if system_id == 1 else 'solax_data2'
            
            if horizon == 'daily':
                query = f"""
                SELECT 
                    DATE(timestamp) as date,
                    EXTRACT(year FROM timestamp) as year,
                    EXTRACT(month FROM timestamp) as month,
                    EXTRACT(day FROM timestamp) as day,
                    EXTRACT(dow FROM timestamp) as day_of_week,
                    EXTRACT(doy FROM timestamp) as day_of_year,
                    EXTRACT(week FROM timestamp) as week_of_year,
                    EXTRACT(quarter FROM timestamp) as quarter,
                    MAX(yield_today) as target_yield
                FROM {table}
                WHERE timestamp >= CURRENT_DATE - INTERVAL '18 months'
                AND yield_today IS NOT NULL
                AND yield_today > 5
                GROUP BY DATE(timestamp), EXTRACT(year FROM timestamp), EXTRACT(month FROM timestamp),
                         EXTRACT(day FROM timestamp), EXTRACT(dow FROM timestamp), EXTRACT(doy FROM timestamp),
                         EXTRACT(week FROM timestamp), EXTRACT(quarter FROM timestamp)
                ORDER BY date
                """
                
            elif horizon == 'monthly':
                query = f"""
                SELECT 
                    DATE_TRUNC('month', timestamp) as month_start,
                    EXTRACT(year FROM timestamp) as year,
                    EXTRACT(month FROM timestamp) as month,
                    EXTRACT(quarter FROM timestamp) as quarter,
                    AVG(yield_today) as target_yield,
                    COUNT(*) as days_count
                FROM {table}
                WHERE timestamp >= CURRENT_DATE - INTERVAL '3 years'
                AND yield_today IS NOT NULL
                AND yield_today > 5
                GROUP BY DATE_TRUNC('month', timestamp), EXTRACT(year FROM timestamp), 
                         EXTRACT(month FROM timestamp), EXTRACT(quarter FROM timestamp)
                HAVING COUNT(*) >= 20  -- At least 20 days of data per month
                ORDER BY month_start
                """
                
            elif horizon == 'yearly':
                query = f"""
                SELECT 
                    EXTRACT(year FROM timestamp) as year,
                    AVG(yield_today) as target_yield,
                    COUNT(*) as days_count
                FROM {table}
                WHERE timestamp >= CURRENT_DATE - INTERVAL '5 years'
                AND yield_today IS NOT NULL
                AND yield_today > 5
                GROUP BY EXTRACT(year FROM timestamp)
                HAVING COUNT(*) >= 300  -- At least 300 days of data per year
                ORDER BY year
                """
            
            df = pd.read_sql(query, conn)
            
            if len(df) < self.min_data_requirements[horizon]:
                logger.warning(f"Insufficient data for {horizon} model: {len(df)} < {self.min_data_requirements[horizon]}")
                return None
            
            logger.info(f"Loaded {len(df)} records for {horizon} System {system_id}")
            return df
            
        except Exception as e:
            logger.error(f"Failed to load training data: {e}")
            return None
        finally:
            conn.close()
    
    def create_features_for_training(self, df: pd.DataFrame, horizon: str) -> pd.DataFrame:
        """Δημιουργεί features για εκπαίδευση"""
        try:
            if horizon == 'daily':
                # Daily features
                df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
                df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
                df['doy_sin'] = np.sin(2 * np.pi * df['day_of_year'] / 365)
                df['doy_cos'] = np.cos(2 * np.pi * df['day_of_year'] / 365)
                df['is_weekend'] = (df['day_of_week'] >= 5).astype(int)
                df['is_summer'] = df['month'].isin([6, 7, 8]).astype(int)
                df['seasonal_intensity'] = np.cos(2 * np.pi * (df['day_of_year'] - 172) / 365)
                df['solar_declination'] = 23.45 * np.sin(np.radians(360 * (284 + df['day_of_year']) / 365))
                
                # Calculate day length
                lat_rad = np.radians(38.14)
                decl_rad = np.radians(df['solar_declination'])
                hour_angle = np.arccos(-np.tan(lat_rad) * np.tan(decl_rad))
                df['day_length'] = 2 * hour_angle * 12 / np.pi
                
            elif horizon == 'monthly':
                # Monthly features
                df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
                df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
                df['is_summer'] = df['month'].isin([6, 7, 8]).astype(int)
                df['seasonal_intensity'] = np.cos(2 * np.pi * (df['month'] * 30.5 - 172) / 365)
                
            elif horizon == 'yearly':
                # Yearly features
                base_year = df['year'].min()
                df['year_normalized'] = (df['year'] - base_year) / 10.0
                df['is_leap_year'] = ((df['year'] % 4 == 0) & (df['year'] % 100 != 0)) | (df['year'] % 400 == 0)
                df['yield_trend'] = df['target_yield'].diff().fillna(0)
            
            # Fill any remaining NaN values
            df = df.fillna(method='bfill').fillna(method='ffill').fillna(0)
            
            return df
            
        except Exception as e:
            logger.error(f"Feature creation failed: {e}")
            return df
    
    def evaluate_model_performance(self, system_id: int, horizon: str) -> Dict[str, float]:
        """Αξιολογεί την απόδοση του τρέχοντος μοντέλου"""
        try:
            # Load recent data for evaluation
            df = self.load_training_data(system_id, horizon)
            if df is None or len(df) < 10:
                return {'r2': 0.0, 'mae': 999.0, 'samples': 0}
            
            # Use last 20% of data for evaluation
            eval_size = max(5, int(len(df) * 0.2))
            eval_df = df.tail(eval_size)
            
            # Create features
            eval_df = self.create_features_for_training(eval_df, horizon)
            
            # Make predictions and calculate metrics
            predictions = []
            actuals = []
            
            for _, row in eval_df.iterrows():
                if horizon == 'daily':
                    date = datetime.strptime(str(row['date']), '%Y-%m-%d')
                    result = self.predictor.predict_daily(date, system_id)
                elif horizon == 'monthly':
                    result = self.predictor.predict_monthly(int(row['year']), int(row['month']), system_id)
                elif horizon == 'yearly':
                    result = self.predictor.predict_yearly(int(row['year']), system_id)
                
                if 'error' not in result and result['prediction'] is not None:
                    predictions.append(result['prediction'])
                    actuals.append(row['target_yield'])
            
            if len(predictions) < 3:
                return {'r2': 0.0, 'mae': 999.0, 'samples': len(predictions)}
            
            # Calculate metrics
            from sklearn.metrics import r2_score, mean_absolute_error
            r2 = r2_score(actuals, predictions)
            mae = mean_absolute_error(actuals, predictions)
            
            return {'r2': r2, 'mae': mae, 'samples': len(predictions)}
            
        except Exception as e:
            logger.error(f"Model evaluation failed: {e}")
            return {'r2': 0.0, 'mae': 999.0, 'samples': 0}
    
    def should_retrain_model(self, system_id: int, horizon: str) -> bool:
        """Αποφασίζει αν χρειάζεται επανεκπαίδευση"""
        try:
            # Evaluate current performance
            performance = self.evaluate_model_performance(system_id, horizon)
            thresholds = self.performance_thresholds[horizon]
            
            logger.info(f"System {system_id} {horizon} performance: R²={performance['r2']:.4f}, MAE={performance['mae']:.2f}")
            
            # Check if performance is below thresholds
            needs_retraining = (
                performance['r2'] < thresholds['min_r2'] or
                performance['mae'] > thresholds['max_mae'] or
                performance['samples'] < 5
            )
            
            if needs_retraining:
                logger.info(f"Model retraining needed for System {system_id} {horizon}")
            else:
                logger.info(f"Model performance acceptable for System {system_id} {horizon}")
            
            return needs_retraining
            
        except Exception as e:
            logger.error(f"Failed to evaluate retraining need: {e}")
            return True  # Default to retraining if evaluation fails
    
    def retrain_model(self, system_id: int, horizon: str) -> bool:
        """Επανεκπαιδεύει ένα μοντέλο"""
        try:
            logger.info(f"Starting retraining for System {system_id} {horizon} model")
            
            # Load training data
            df = self.load_training_data(system_id, horizon)
            if df is None:
                logger.error(f"No training data available for System {system_id} {horizon}")
                return False
            
            # Create features
            df = self.create_features_for_training(df, horizon)
            
            # Train model
            success = self.predictor.train_model(horizon, system_id, df)
            
            if success:
                logger.info(f"Successfully retrained System {system_id} {horizon} model")
                return True
            else:
                logger.error(f"Failed to retrain System {system_id} {horizon} model")
                return False
                
        except Exception as e:
            logger.error(f"Model retraining failed: {e}")
            return False
    
    def run_automated_retraining(self):
        """Εκτελεί την αυτοματοποιημένη επανεκπαίδευση"""
        logger.info("Starting automated model retraining check")
        
        try:
            # Load current models
            self.predictor.load_models()
            
            # Backup current models
            if not self.backup_current_models():
                logger.error("Failed to backup models, aborting retraining")
                return
            
            retrained_models = []
            
            # Check each model
            for system_id in [1, 2]:
                for horizon in ['daily', 'monthly', 'yearly']:
                    try:
                        if self.should_retrain_model(system_id, horizon):
                            success = self.retrain_model(system_id, horizon)
                            if success:
                                retrained_models.append(f"System{system_id}_{horizon}")
                    except Exception as e:
                        logger.error(f"Error processing System {system_id} {horizon}: {e}")
                        continue
            
            # Save retrained models
            if retrained_models:
                self.predictor.save_models()
                logger.info(f"Retraining completed. Updated models: {retrained_models}")
            else:
                logger.info("No models needed retraining")
            
            # Log summary
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            summary = {
                'timestamp': timestamp,
                'retrained_models': retrained_models,
                'total_models_checked': 6,
                'models_retrained': len(retrained_models)
            }
            
            summary_file = Path("/home/<USER>/solar-prediction-project/logs/retraining_summary.json")
            with open(summary_file, 'w') as f:
                json.dump(summary, f, indent=2)
            
        except Exception as e:
            logger.error(f"Automated retraining failed: {e}")

def schedule_retraining():
    """Προγραμματίζει την περιοδική επανεκπαίδευση"""
    retrainer = AutomatedModelRetrainer()
    
    # Schedule weekly retraining (every Sunday at 2 AM)
    schedule.every().sunday.at("02:00").do(retrainer.run_automated_retraining)
    
    # Schedule monthly comprehensive retraining (1st of month at 3 AM)
    schedule.every().month.do(retrainer.run_automated_retraining)
    
    logger.info("Automated retraining scheduled")
    logger.info("Weekly: Every Sunday at 2:00 AM")
    logger.info("Monthly: 1st of each month at 3:00 AM")
    
    while True:
        schedule.run_pending()
        time.sleep(3600)  # Check every hour

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--run-now":
        # Run immediately for testing
        retrainer = AutomatedModelRetrainer()
        retrainer.run_automated_retraining()
    else:
        # Start scheduled retraining
        schedule_retraining()
