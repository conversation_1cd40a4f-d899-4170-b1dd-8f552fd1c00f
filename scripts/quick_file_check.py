#!/usr/bin/env python3
"""
Quick check των System files
"""

import os
from datetime import datetime

def quick_analysis():
    """Γρήγορη ανάλυση των files"""
    print("🔍 QUICK ANALYSIS - SYSTEM FILES")
    print("=" * 40)
    
    systems = [
        ("System1", "data/raw/System1"),
        ("System2", "data/raw/System2")
    ]
    
    for system_name, system_path in systems:
        print(f"\n🏠 {system_name}:")
        
        if not os.path.exists(system_path):
            print(f"   ❌ Φάκελος δεν υπάρχει")
            continue
        
        files = [f for f in os.listdir(system_path) if f.endswith('.xlsx')]
        files.sort()
        
        print(f"   📁 Files: {len(files)}")
        
        total_size = 0
        for file in files:
            file_path = os.path.join(system_path, file)
            size_mb = os.path.getsize(file_path) / 1024 / 1024
            total_size += size_mb
            
            # Εξαγωγή ημερομηνιών από το όνομα του file
            if "2024-03-01-2024-06-28" in file:
                period = "March-June 2024"
            elif "2024-06-28-2024-10-25" in file:
                period = "June-October 2024"
            elif "2024-10-25-2025-02-21" in file:
                period = "October 2024-February 2025"
            elif "2025-02-21-2025-06-01" in file:
                period = "February-June 2025"
            else:
                period = "Unknown period"
            
            print(f"      📄 {file}")
            print(f"         📅 {period}")
            print(f"         💾 {size_mb:.1f} MB")
        
        print(f"   📊 Total size: {total_size:.1f} MB")
    
    # Έλεγχος κάλυψης
    print(f"\n🎯 ΚΑΛΥΨΗ ΕΛΕΓΧΟΣ:")
    
    expected_periods = [
        "March-June 2024",
        "June-October 2024", 
        "October 2024-February 2025",
        "February-June 2025"
    ]
    
    print(f"   📅 Αναμενόμενες περίοδοι: {len(expected_periods)}")
    print(f"   📅 Στόχος: March 2024 to June 2025")
    
    # Ελέγχω αν υπάρχουν όλα τα files
    system1_files = len([f for f in os.listdir("data/raw/System1") if f.endswith('.xlsx')])
    system2_files = len([f for f in os.listdir("data/raw/System2") if f.endswith('.xlsx')])
    
    if system1_files == 4 and system2_files == 4:
        print(f"   ✅ ΠΛΗΡΗΣ ΚΑΛΥΨΗ!")
        print(f"      🏠 System1: {system1_files} files")
        print(f"      🏠 System2: {system2_files} files")
        print(f"      📅 Περίοδος: March 2024 - June 2025")
        print(f"      🎯 ΕΤΟΙΜΟΙ για import!")
    else:
        print(f"   ❌ ΕΛΛΕΙΠΗ ΚΑΛΥΨΗ")
        print(f"      🏠 System1: {system1_files} files (αναμένονται 4)")
        print(f"      🏠 System2: {system2_files} files (αναμένονται 4)")
    
    return system1_files == 4 and system2_files == 4

if __name__ == "__main__":
    success = quick_analysis()
    
    if success:
        print(f"\n🚀 ΕΠΟΜΕΝΟ ΒΗΜΑ:")
        print(f"   📥 Import historical data από System folders")
        print(f"   🔧 Mapping: System1 → solax_data, System2 → solax_data2")
        print(f"   📊 Αναμενόμενα records: ~100,000+ ανά σύστημα")
    else:
        print(f"\n❌ ΠΡΟΒΛΗΜΑ με τα files - χρειάζεται έλεγχος")
