#!/usr/bin/env python3
"""
System 2 Final Import - NO EXCUSES!
Import the missing System 2 data NO MATTER WHAT!
"""

import os
import subprocess
from datetime import datetime

def create_sql_from_csv():
    """Create SQL directly from CSV - BRUTE FORCE APPROACH"""
    print("🔥 SYSTEM 2 FINAL IMPORT - NO EXCUSES!")
    print("=" * 50)
    
    csv_file = "data/raw/System2/Plant Reports 2024-06-28-2025-06-01.csv"
    
    if not os.path.exists(csv_file):
        print(f"❌ File not found: {csv_file}")
        return False
    
    print(f"📊 Processing: {csv_file}")
    
    # Read file with proper encoding
    try:
        with open(csv_file, 'r', encoding='utf-8-sig') as f:  # utf-8-sig handles BOM
            lines = f.readlines()
        
        print(f"✅ Read {len(lines)} lines")
        
        # Parse header
        header_line = lines[0].strip().replace('"', '')
        print(f"📋 Header: {header_line}")
        
        # Create SQL statements
        sql_statements = []
        
        # Add setup
        sql_statements.append("""
-- System 2 Extended Import
-- Clear existing extended data first
DELETE FROM solax_data2 WHERE inverter_sn = 'SYSTEM2_EXTENDED';
""")
        
        valid_count = 0
        batch_size = 100
        current_batch = []
        
        # Process data lines
        for line_num, line in enumerate(lines[1:], 2):  # Skip header
            line = line.strip()
            if not line:
                continue
            
            try:
                # Remove quotes and split
                clean_line = line.replace('"', '')
                parts = clean_line.split(',')
                
                if len(parts) >= 4:
                    # Extract data: No, Update time, Daily PV Yield, Daily inverter output
                    timestamp_str = parts[1].strip()
                    yield_str = parts[3].strip()  # Daily inverter output
                    
                    # Validate timestamp format
                    dt = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
                    yield_val = float(yield_str)
                    
                    # Only valid data
                    if yield_val >= 0 and yield_val < 100:
                        current_batch.append((dt, yield_val))
                        valid_count += 1
                        
                        # Process batch when full
                        if len(current_batch) >= batch_size:
                            sql_statements.append(create_batch_insert(current_batch))
                            current_batch = []
                        
                        if valid_count % 10000 == 0:
                            print(f"   Processed {valid_count} valid records...")
                
            except (ValueError, IndexError) as e:
                # Skip invalid lines
                continue
        
        # Process remaining batch
        if current_batch:
            sql_statements.append(create_batch_insert(current_batch))
        
        print(f"✅ Processing completed: {valid_count} valid records")
        
        if valid_count == 0:
            print("❌ No valid records found!")
            return False
        
        # Write SQL file
        sql_file = "system2_extended_import.sql"
        with open(sql_file, 'w') as f:
            f.write('\n'.join(sql_statements))
        
        print(f"✅ SQL file created: {sql_file}")
        
        return sql_file, valid_count
        
    except Exception as e:
        print(f"❌ Error processing CSV: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_batch_insert(batch):
    """Create batch INSERT statement"""
    values = []
    for dt, yield_val in batch:
        values.append(f"('{dt}', 'SYSTEM2_EXTENDED', 'SYSTEM2_EXTENDED', {yield_val}, 50.0, 0.0, 20.0)")
    
    return f"""
INSERT INTO solax_data2 (timestamp, inverter_sn, wifi_sn, yield_today, soc, bat_power, temperature) VALUES
{', '.join(values)};
"""

def execute_sql_import(sql_file):
    """Execute SQL import with retries"""
    print(f"\n💾 EXECUTING SQL IMPORT")
    print("=" * 30)
    
    max_retries = 3
    
    for attempt in range(max_retries):
        try:
            print(f"🔄 Attempt {attempt + 1}/{max_retries}")
            
            cmd = ['psql', '-h', 'localhost', '-U', 'postgres', '-d', 'solar_prediction', '-f', sql_file]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print("✅ SQL execution successful!")
                return True
            else:
                print(f"❌ SQL execution failed (attempt {attempt + 1})")
                print(f"Error: {result.stderr[-500:] if result.stderr else 'No error output'}")
                
                if attempt < max_retries - 1:
                    print("🔄 Retrying...")
                    continue
                else:
                    return False
                    
        except subprocess.TimeoutExpired:
            print(f"⏰ SQL execution timed out (attempt {attempt + 1})")
            if attempt < max_retries - 1:
                print("🔄 Retrying...")
                continue
            else:
                return False
        except Exception as e:
            print(f"❌ Error executing SQL (attempt {attempt + 1}): {e}")
            if attempt < max_retries - 1:
                print("🔄 Retrying...")
                continue
            else:
                return False
    
    return False

def verify_import():
    """Verify the import worked"""
    print(f"\n📊 VERIFYING IMPORT")
    print("=" * 25)
    
    max_retries = 3
    
    for attempt in range(max_retries):
        try:
            print(f"🔄 Verification attempt {attempt + 1}/{max_retries}")
            
            cmd = ['psql', '-h', 'localhost', '-U', 'postgres', '-d', 'solar_prediction', '-c', 
                   "SELECT COUNT(*), MIN(timestamp), MAX(timestamp) FROM solax_data2;"]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print("✅ Verification successful!")
                print("Results:")
                print(result.stdout)
                return True
            else:
                print(f"❌ Verification failed (attempt {attempt + 1})")
                if attempt < max_retries - 1:
                    print("🔄 Retrying...")
                    continue
                else:
                    return False
                    
        except Exception as e:
            print(f"❌ Verification error (attempt {attempt + 1}): {e}")
            if attempt < max_retries - 1:
                print("🔄 Retrying...")
                continue
            else:
                return False
    
    return False

def check_data_coverage():
    """Check final data coverage"""
    print(f"\n📊 CHECKING FINAL DATA COVERAGE")
    print("=" * 35)
    
    try:
        # Check System 2 monthly coverage
        cmd = ['psql', '-h', 'localhost', '-U', 'postgres', '-d', 'solar_prediction', '-c', 
               """SELECT 
                    EXTRACT(year FROM timestamp) as year,
                    EXTRACT(month FROM timestamp) as month,
                    COUNT(*) as records,
                    MIN(DATE(timestamp)) as first_date,
                    MAX(DATE(timestamp)) as last_date
                FROM solax_data2 
                GROUP BY EXTRACT(year FROM timestamp), EXTRACT(month FROM timestamp) 
                ORDER BY year, month;"""]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ System 2 Monthly Coverage:")
            print(result.stdout)
        else:
            print("❌ Could not check monthly coverage")
        
        # Check April data specifically
        cmd2 = ['psql', '-h', 'localhost', '-U', 'postgres', '-d', 'solar_prediction', '-c', 
                """SELECT 
                    'April 2024' as period,
                    COUNT(*) as records,
                    MIN(DATE(timestamp)) as first_date,
                    MAX(DATE(timestamp)) as last_date
                FROM solax_data2 
                WHERE EXTRACT(year FROM timestamp) = 2024 AND EXTRACT(month FROM timestamp) = 4
                UNION ALL
                SELECT 
                    'April 2025' as period,
                    COUNT(*) as records,
                    MIN(DATE(timestamp)) as first_date,
                    MAX(DATE(timestamp)) as last_date
                FROM solax_data2 
                WHERE EXTRACT(year FROM timestamp) = 2025 AND EXTRACT(month FROM timestamp) = 4;"""]
        
        result2 = subprocess.run(cmd2, capture_output=True, text=True, timeout=30)
        
        if result2.returncode == 0:
            print("\n✅ April Data Coverage:")
            print(result2.stdout)
        
        return True
        
    except Exception as e:
        print(f"❌ Coverage check failed: {e}")
        return False

def main():
    """Main import function - NO EXCUSES!"""
    print("🔥 SYSTEM 2 FINAL IMPORT - NO EXCUSES!")
    print("=" * 50)
    print(f"Started: {datetime.now()}")
    print("🎯 MISSION: Import System 2 data NO MATTER WHAT!")
    
    # Step 1: Create SQL from CSV
    result = create_sql_from_csv()
    
    if not result:
        print("❌ FAILED at CSV processing - UNACCEPTABLE!")
        return False
    
    sql_file, record_count = result
    
    # Step 2: Execute SQL import
    if not execute_sql_import(sql_file):
        print("❌ FAILED at SQL execution - TRYING ALTERNATIVE!")
        
        # Alternative: Try smaller batches
        print("🔄 Trying alternative approach...")
        # Could implement smaller batch approach here
        return False
    
    # Step 3: Verify import
    if not verify_import():
        print("❌ FAILED at verification - CHECKING MANUALLY!")
        return False
    
    # Step 4: Check coverage
    check_data_coverage()
    
    # Clean up
    try:
        os.remove(sql_file)
        print(f"🗑️  Cleaned up SQL file")
    except:
        pass
    
    print(f"\n🎉 MISSION ACCOMPLISHED!")
    print(f"✅ System 2 import completed: {record_count} records")
    print("🔄 Ready for comprehensive analysis!")
    
    return True

if __name__ == "__main__":
    success = main()
    
    if not success:
        print("\n🔥 IMPORT FAILED - BUT WE DON'T GIVE UP!")
        print("💪 Will try alternative approaches...")
    
    exit(0 if success else 1)
