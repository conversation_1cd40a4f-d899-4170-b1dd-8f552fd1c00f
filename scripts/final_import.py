#!/usr/bin/env python3
"""
Final Import - Τελική έκδοση με σωστά headers και μικρά batches
"""

import psycopg2
import json
import os
from datetime import datetime
import time
from openpyxl import load_workbook

def final_import_file(file_path, system_id):
    """Τελικό import με σωστή δομή"""
    print(f"\n📥 IMPORTING: {os.path.basename(file_path)}")
    print(f"   🏠 System ID: {system_id}")
    
    try:
        # Load workbook
        wb = load_workbook(file_path, read_only=True, data_only=True)
        ws = wb.active
        
        # Headers από row 2 (skiprows=1)
        headers = [str(cell.value) if cell.value else f"col_{i}" for i, cell in enumerate(ws[2])]
        print(f"   📋 Headers: {headers}")
        
        # Βρίσκω timestamp column - "Update time"
        time_col_idx = None
        for i, header in enumerate(headers):
            if 'Update time' in header:
                time_col_idx = i
                break
        
        if time_col_idx is None:
            print(f"   ❌ 'Update time' column not found")
            wb.close()
            return 0
        
        print(f"   📅 Time column found at index {time_col_idx}")
        
        # Database connection
        conn = psycopg2.connect(
            host="localhost",
            database="solar_prediction",
            user="postgres",
            password="postgres"
        )
        cursor = conn.cursor()
        
        target_table = "solax_data" if system_id == 1 else "solax_data2"
        print(f"   💾 Target: {target_table}")
        
        # Import με μικρά batches
        imported_count = 0
        error_count = 0
        batch_size = 50  # Πολύ μικρά batches
        
        print(f"   📦 Processing with batch size {batch_size}...")
        
        # Process rows (start from row 3, skip headers)
        for row_num in range(3, ws.max_row + 1):
            try:
                row = ws[row_num]
                
                # Get timestamp
                timestamp_cell = row[time_col_idx]
                if not timestamp_cell.value:
                    error_count += 1
                    continue
                
                timestamp = timestamp_cell.value
                
                # Get other data
                row_data = {}
                for i, cell in enumerate(row):
                    if i < len(headers) and cell.value is not None:
                        row_data[headers[i]] = str(cell.value)
                
                # Extract key values
                daily_yield = row_data.get('Daily PV Yield(kWh)', 0)
                export_power = row_data.get('Export power(W)', 0)
                exported_energy = row_data.get('Daily exported energy(kWh)', 0)
                imported_energy = row_data.get('Daily imported energy(kWh)', 0)
                
                # Insert
                insert_sql = f"""
                    INSERT INTO {target_table} (
                        timestamp, ac_power, yield_today, feedin_power, 
                        feedin_energy, consume_energy, system_id, 
                        inverter_sn, wifi_sn, raw_data
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                
                raw_data = json.dumps({
                    'source': 'final_import',
                    'file': os.path.basename(file_path),
                    'system_id': system_id,
                    'imported_at': datetime.now().isoformat(),
                    'original_data': row_data
                })
                
                cursor.execute(insert_sql, (
                    timestamp,
                    float(export_power) if export_power else 0.0,
                    float(daily_yield) if daily_yield else 0.0,
                    float(export_power) if export_power else 0.0,
                    float(exported_energy) if exported_energy else 0.0,
                    float(imported_energy) if imported_energy else 0.0,
                    system_id,
                    f"HIST_SYS{system_id}",
                    f"HIST_WIFI{system_id}",
                    raw_data
                ))
                
                imported_count += 1
                
                # Batch commit
                if imported_count % batch_size == 0:
                    conn.commit()
                    print(f"      ✅ {imported_count:,} records imported...")
                    time.sleep(0.1)
                
            except Exception as e:
                error_count += 1
                if error_count <= 5:
                    print(f"      ⚠️  Row {row_num} error: {e}")
                continue
        
        # Final commit
        conn.commit()
        cursor.close()
        conn.close()
        wb.close()
        
        print(f"   📊 File completed:")
        print(f"      ✅ Imported: {imported_count:,} records")
        print(f"      ❌ Errors: {error_count:,} records")
        
        return imported_count
        
    except Exception as e:
        print(f"   ❌ File failed: {e}")
        return 0

def import_all_systems():
    """Import όλων των συστημάτων"""
    print("📥 FINAL HISTORICAL DATA IMPORT")
    print("=" * 50)
    
    systems = [
        ("data/raw/System1", 1, "System1"),
        ("data/raw/System2", 2, "System2")
    ]
    
    total_imported = 0
    start_time = time.time()
    
    for system_path, system_id, system_name in systems:
        print(f"\n🏠 IMPORTING {system_name}")
        
        if not os.path.exists(system_path):
            print(f"   ❌ Path not found: {system_path}")
            continue
        
        # Get Excel files
        excel_files = [f for f in os.listdir(system_path) if f.endswith('.xlsx')]
        excel_files.sort()
        
        print(f"   📁 Files: {len(excel_files)}")
        
        system_imported = 0
        
        for file_name in excel_files:
            file_path = os.path.join(system_path, file_name)
            imported = final_import_file(file_path, system_id)
            system_imported += imported
            
            # Small delay between files
            time.sleep(1)
        
        print(f"   📊 {system_name} total: {system_imported:,} records")
        total_imported += system_imported
    
    elapsed = time.time() - start_time
    
    print(f"\n📊 FINAL SUMMARY:")
    print(f"   📈 Total imported: {total_imported:,} records")
    print(f"   ⏱️  Time: {elapsed:.1f} seconds")
    
    if total_imported > 0:
        print(f"\n✅ HISTORICAL IMPORT COMPLETED!")
        print(f"   🎯 Ready for Enhanced Model v3!")
    else:
        print(f"\n❌ IMPORT FAILED!")
    
    return total_imported

if __name__ == "__main__":
    # Test με ένα file πρώτα
    test_file = "data/raw/System1/Plant Reports 2024-03-01-2024-06-28.xlsx"
    
    if os.path.exists(test_file):
        print("🧪 TESTING WITH ONE FILE FIRST...")
        imported = final_import_file(test_file, 1)
        
        if imported > 0:
            print(f"\n✅ TEST SUCCESSFUL! Imported {imported:,} records")
            print(f"🚀 Proceeding with full import...")
            import_all_systems()
        else:
            print(f"\n❌ TEST FAILED!")
    else:
        print(f"❌ Test file not found: {test_file}")
