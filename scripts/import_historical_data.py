#!/usr/bin/env python3
"""
Import Historical Data από System1 και System2 φακέλους
"""

import pandas as pd
import psycopg2
import json
import os
from datetime import datetime
from pathlib import Path

def import_system_files(system_path, system_id, system_name, dry_run=True):
    """Import όλων των files ενός συστήματος"""
    print(f"\n📥 IMPORTING {system_name} (system_id={system_id})")
    print(f"   📁 Path: {system_path}")
    print(f"   🔧 Dry run: {dry_run}")

    if not os.path.exists(system_path):
        print(f"   ❌ Φάκελος δεν υπάρχει")
        return 0

    # Βρίσκω όλα τα Excel files
    excel_files = [f for f in os.listdir(system_path) if f.endswith('.xlsx')]
    excel_files.sort()  # Χρονολογική σειρά

    print(f"   📄 Files to import: {len(excel_files)}")

    total_imported = 0

    for file_name in excel_files:
        file_path = os.path.join(system_path, file_name)
        print(f"\n   📄 Processing: {file_name}")

        try:
            # Διάβασμα Excel file
            df = pd.read_excel(file_path, skiprows=1)
            print(f"      📊 Loaded: {len(df)} rows")
            print(f"      📋 Columns: {list(df.columns)[:5]}...")

            if len(df) == 0:
                print(f"      ⚠️  Empty file - skipping")
                continue

            # Ελέγχω για timestamp column
            time_columns = [col for col in df.columns if 'time' in col.lower() or 'date' in col.lower()]

            if not time_columns:
                print(f"      ❌ No timestamp column found")
                continue

            time_col = time_columns[0]
            print(f"      📅 Time column: {time_col}")

            # Date range
            min_date = df[time_col].min()
            max_date = df[time_col].max()
            print(f"      📅 Date range: {min_date} to {max_date}")

            if not dry_run:
                # Πραγματικό import
                imported_count = import_dataframe_to_db(df, system_id, file_name, time_col)
                total_imported += imported_count
                print(f"      ✅ Imported: {imported_count} records")
            else:
                print(f"      🔧 Would import: {len(df)} records")
                total_imported += len(df)

        except Exception as e:
            print(f"      ❌ Error processing {file_name}: {e}")
            continue

    print(f"\n   📊 {system_name} Summary:")
    print(f"      📈 Total records: {total_imported:,}")
    print(f"      📁 Files processed: {len(excel_files)}")

    return total_imported

def import_dataframe_to_db(df, system_id, source_file, time_col):
    """Import DataFrame στη βάση δεδομένων"""

    try:
        conn = psycopg2.connect(
            host="localhost",
            database="solar_prediction",
            user="postgres",
            password="postgres"
        )
        cursor = conn.cursor()

        # Καθορισμός target table
        target_table = "solax_data" if system_id == 1 else "solax_data2"

        print(f"         💾 Importing to {target_table}...")

        imported_count = 0
        skipped_count = 0

        for _, row in df.iterrows():
            try:
                # Timestamp
                timestamp = row[time_col]
                if pd.isna(timestamp):
                    skipped_count += 1
                    continue

                # Mapping columns (προσαρμογή βάσει πραγματικής δομής)
                # Θα χρειαστεί να προσαρμόσω αυτό βάσει των πραγματικών columns

                # Βασικά fields με default values
                record_data = {
                    'timestamp': timestamp,
                    'ac_power': row.get('AC Power(W)', row.get('Export power(W)', 0)),
                    'powerdc1': row.get('PV1 Power(W)', 0),
                    'powerdc2': row.get('PV2 Power(W)', 0),
                    'yield_today': row.get('Daily PV Yield(kWh)', 0),
                    'yield_total': row.get('Total Yield(kWh)', 0),
                    'soc': row.get('SOC(%)', 0),
                    'bat_power': row.get('Battery Power(W)', 0),
                    'feedin_power': row.get('Feed-in Power(W)', row.get('Export power(W)', 0)),
                    'feedin_energy': row.get('Daily exported energy(kWh)', 0),
                    'consume_energy': row.get('Daily imported energy(kWh)', 0),
                    'temperature': row.get('Temperature(℃)', 20),  # Default 20°C
                    'system_id': system_id,
                    'inverter_sn': f"HIST_IMPORT_SYS{system_id}",
                    'wifi_sn': f"HIST_WIFI_SYS{system_id}",
                    'raw_data': json.dumps({
                        'source': 'historical_import',
                        'file': source_file,
                        'imported_at': datetime.now().isoformat(),
                        'original_columns': list(row.index)
                    })
                }

                # Insert στη βάση (χωρίς ON CONFLICT για τώρα)
                insert_query = f"""
                    INSERT INTO {target_table} (
                        timestamp, ac_power, powerdc1, powerdc2, yield_today, yield_total,
                        soc, bat_power, feedin_power, feedin_energy, consume_energy,
                        temperature, system_id, inverter_sn, wifi_sn, raw_data
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                    )
                """

                cursor.execute(insert_query, (
                    record_data['timestamp'],
                    record_data['ac_power'],
                    record_data['powerdc1'],
                    record_data['powerdc2'],
                    record_data['yield_today'],
                    record_data['yield_total'],
                    record_data['soc'],
                    record_data['bat_power'],
                    record_data['feedin_power'],
                    record_data['feedin_energy'],
                    record_data['consume_energy'],
                    record_data['temperature'],
                    record_data['system_id'],
                    record_data['inverter_sn'],
                    record_data['wifi_sn'],
                    record_data['raw_data']
                ))

                imported_count += 1

                # Progress indicator
                if imported_count % 1000 == 0:
                    print(f"         📊 Imported {imported_count:,} records...")

            except Exception as e:
                skipped_count += 1
                if skipped_count <= 5:  # Show first 5 errors only
                    print(f"         ⚠️  Error importing row: {e}")
                continue

        conn.commit()
        cursor.close()
        conn.close()

        if skipped_count > 0:
            print(f"         ⚠️  Skipped {skipped_count} records due to errors")

        return imported_count

    except Exception as e:
        print(f"         ❌ Database error: {e}")
        return 0

def main():
    """Κύρια συνάρτηση"""
    print("📥 HISTORICAL DATA IMPORT")
    print("=" * 50)
    print("Στόχος: Import από System1 και System2 φακέλους")

    # Φάση 1: Dry run (ήδη completed)
    print(f"\n🔧 ΦΑΣΗ 1: DRY RUN COMPLETED ✅")
    print(f"   📊 Expected: 250,814 records total")

    # Φάση 2: Πραγματικό import
    print(f"\n📥 ΦΑΣΗ 2: ΠΡΑΓΜΑΤΙΚΟ IMPORT")
    print(f"   ⚠️  Importing historical data από March 2024...")

    system1_imported = import_system_files("data/raw/System1", 1, "System1", dry_run=False)
    system2_imported = import_system_files("data/raw/System2", 2, "System2", dry_run=False)

    total_imported = system1_imported + system2_imported

    print(f"\n📊 FINAL IMPORT SUMMARY:")
    print(f"   🏠 System1: {system1_imported:,} records imported")
    print(f"   🏠 System2: {system2_imported:,} records imported")
    print(f"   📊 Total: {total_imported:,} records imported")

    if total_imported > 0:
        print(f"\n✅ IMPORT ΕΠΙΤΥΧΗΣ!")
        print(f"   📅 Historical data: March 2024 - June 2025")
        print(f"   🎯 Ready for Enhanced Model v3 training!")
    else:
        print(f"\n❌ IMPORT ΑΠΟΤΥΧΙΑ!")
        print(f"   🔧 Ελέγξτε database connection και permissions")

    return True

if __name__ == "__main__":
    main()
