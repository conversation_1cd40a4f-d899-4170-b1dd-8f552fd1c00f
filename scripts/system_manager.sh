#!/bin/bash
# Interactive Solar Prediction System Manager
# Professional management interface with real-time monitoring

# Colors for better UI
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Configuration
PROJECT_DIR="/home/<USER>/solar-prediction-project"
SCRIPT_PATH="scripts/production_scripts_api.py"
LOG_FILE="production.log"
PID_FILE="solar_system.pid"

cd "$PROJECT_DIR"

# Ensure virtual environment is activated
if [ -d "venv" ] && [ -z "$VIRTUAL_ENV" ]; then
    echo "🔧 Activating virtual environment..."
    source venv/bin/activate
fi

# Load environment variables if .env exists
if [ -f ".env" ]; then
    export $(grep -v '^#' .env | xargs) 2>/dev/null || true
fi

# Function to kill processes on specific port
kill_port_processes() {
    local port=$1
    local service_name=$2

    # Find processes using the port
    local pids=$(lsof -ti:$port 2>/dev/null)

    if [ -n "$pids" ]; then
        echo -e "${YELLOW}⚠️ Found processes using port $port: $pids${NC}"
        echo -e "${BLUE}🛑 Stopping $service_name processes...${NC}"

        # Try graceful shutdown first
        for pid in $pids; do
            if ps -p $pid > /dev/null 2>&1; then
                kill $pid 2>/dev/null
            fi
        done

        # Wait a moment
        sleep 2

        # Force kill if still running
        local remaining_pids=$(lsof -ti:$port 2>/dev/null)
        if [ -n "$remaining_pids" ]; then
            for pid in $remaining_pids; do
                if ps -p $pid > /dev/null 2>&1; then
                    kill -9 $pid 2>/dev/null
                fi
            done
        fi

        echo -e "${GREEN}✅ Port $port is now free${NC}"
    fi
}

# Function to cleanup all solar system processes
cleanup_solar_processes() {
    echo -e "${BLUE}🧹 Cleaning up Solar System processes...${NC}"

    # Kill specific Python processes
    pkill -f "production_scripts_api.py" 2>/dev/null
    pkill -f "yield_based_production_app.py" 2>/dev/null
    pkill -f "production_app.py" 2>/dev/null
    pkill -f "unified_data_collection_system.py" 2>/dev/null

    # Kill processes on common ports
    kill_port_processes 8100 "Solar API Server"
    kill_port_processes 8080 "Alternative Web Server"
    kill_port_processes 3000 "Frontend Server"

    # Clean up any uvicorn processes
    pkill -f "uvicorn.*solar" 2>/dev/null

    # Remove stale PID files
    rm -f "$PID_FILE"

    # Clean up PostgreSQL stale locks and zombie processes
    echo -e "${BLUE}🗄️ Cleaning up PostgreSQL stale locks...${NC}"
    if [ -f "/var/run/postgresql/.s.PGSQL.5432.lock" ]; then
        echo -e "${YELLOW}   Found stale PostgreSQL lock file${NC}"
        # Try without sudo first, then with sudo if needed
        if rm -f /var/run/postgresql/.s.PGSQL.5432.lock 2>/dev/null; then
            echo -e "${GREEN}   Removed stale lock file${NC}"
        elif command -v sudo >/dev/null 2>&1 && sudo -n rm -f /var/run/postgresql/.s.PGSQL.5432.lock 2>/dev/null; then
            echo -e "${GREEN}   Removed stale lock file (with sudo)${NC}"
        else
            echo -e "${RED}   ⚠️ Could not remove lock file - insufficient permissions${NC}"
        fi
    fi

    # Check for zombie PostgreSQL processes
    ZOMBIE_POSTGRES=$(ps aux | grep '[p]ostgres.*defunct' | awk '{print $2}' | head -5)
    if [ -n "$ZOMBIE_POSTGRES" ]; then
        echo -e "${YELLOW}   Found zombie PostgreSQL processes: $ZOMBIE_POSTGRES${NC}"
        for pid in $ZOMBIE_POSTGRES; do
            # Try without sudo first, then with sudo if needed
            if kill -9 $pid 2>/dev/null; then
                echo -e "${GREEN}   Killed zombie process $pid${NC}"
            elif command -v sudo >/dev/null 2>&1 && sudo -n kill -9 $pid 2>/dev/null; then
                echo -e "${GREEN}   Killed zombie process $pid (with sudo)${NC}"
            else
                echo -e "${RED}   ⚠️ Could not kill process $pid - insufficient permissions${NC}"
            fi
        done
    fi

    # Ensure PostgreSQL is running
    echo -e "${BLUE}🔧 Ensuring PostgreSQL is running...${NC}"
    if ! systemctl is-active --quiet postgresql@16-main 2>/dev/null; then
        echo -e "${YELLOW}   PostgreSQL not running, attempting to start...${NC}"
        # Try without sudo first, then with sudo if needed
        if systemctl --user start postgresql@16-main 2>/dev/null; then
            echo -e "${GREEN}   ✅ PostgreSQL started successfully (user service)${NC}"
        elif command -v sudo >/dev/null 2>&1 && sudo -n systemctl start postgresql@16-main 2>/dev/null; then
            echo -e "${GREEN}   ✅ PostgreSQL started successfully (system service)${NC}"
        else
            echo -e "${RED}   ⚠️ PostgreSQL start failed - may need manual intervention${NC}"
            echo -e "${YELLOW}   💡 Try: sudo systemctl start postgresql@16-main${NC}"
        fi
    else
        echo -e "${GREEN}   ✅ PostgreSQL is already running${NC}"
    fi

    echo -e "${GREEN}✅ Cleanup completed${NC}"
}

# Function to get server PID
get_server_pid() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            echo "$pid"
        else
            rm -f "$PID_FILE"
            echo ""
        fi
    else
        # Try to find by process name
        pgrep -f "production_scripts_api.py\|yield_based_production_app.py" | head -1
    fi
}

# Function to check service status
check_service_status() {
    local service=$1
    local url=$2
    local timeout=${3:-5}
    
    local response=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout "$timeout" "$url" 2>/dev/null)
    if [ "$response" = "200" ]; then
        echo -e "${GREEN}●${NC} Online"
    else
        echo -e "${RED}●${NC} Offline"
    fi
}

# Function to get system metrics
get_system_metrics() {
    local pid=$(get_server_pid)
    
    # Memory usage
    local total_mem=$(free -h | awk '/^Mem:/ {print $2}')
    local used_mem=$(free -h | awk '/^Mem:/ {print $3}')
    
    # Disk usage
    local disk_usage=$(df -h . | awk 'NR==2 {print $5}')
    
    # Server memory (if running)
    local server_mem="N/A"
    if [ -n "$pid" ]; then
        server_mem=$(ps -p "$pid" -o rss= 2>/dev/null | awk '{printf "%.1fMB", $1/1024}')
    fi
    
    # Database stats
    local db_status="Offline"
    local total_predictions="N/A"
    local recent_predictions="N/A"
    
    if psql -h localhost -U postgres -d solar_prediction -c "SELECT 1" >/dev/null 2>&1; then
        db_status="Online"
        total_predictions=$(psql -h localhost -U postgres -d solar_prediction -t -c "SELECT COUNT(*) FROM predictions;" 2>/dev/null | tr -d ' ')
        recent_predictions=$(psql -h localhost -U postgres -d solar_prediction -t -c "SELECT COUNT(*) FROM predictions WHERE timestamp > NOW() - INTERVAL '1 hour';" 2>/dev/null | tr -d ' ')
    fi
    
    echo "$used_mem/$total_mem|$disk_usage|$server_mem|$db_status|$total_predictions|$recent_predictions"
}

# Function to display header
display_header() {
    clear
    echo -e "${BLUE}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BLUE}║${WHITE}              🌞 Solar Prediction System Manager              ${BLUE}║${NC}"
    echo -e "${BLUE}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

# Function to display system status
display_status() {
    local pid=$(get_server_pid)
    local metrics=$(get_system_metrics)
    IFS='|' read -r mem_usage disk_usage server_mem db_status total_pred recent_pred <<< "$metrics"
    
    echo -e "${CYAN}📊 System Status${NC}"
    echo -e "${CYAN}─────────────────${NC}"
    
    # Server Status
    if [ -n "$pid" ]; then
        echo -e "🚀 Server:        ${GREEN}●${NC} Running (PID: $pid)"
    else
        echo -e "🚀 Server:        ${RED}●${NC} Stopped"
    fi
    
    # Service Status
    echo -e "🌐 Web Interface: $(check_service_status "web" "http://localhost:8100/")"
    echo -e "🔧 Admin Panel:   $(check_service_status "admin" "http://localhost:8100/admin")"
    echo -e "📡 API Health:    $(check_service_status "api" "http://localhost:8100/health")"
    echo -e "🗄️  Database:      ${db_status}"
    
    echo ""
    echo -e "${PURPLE}💾 System Resources${NC}"
    echo -e "${PURPLE}───────────────────${NC}"
    echo -e "Memory Usage:     $mem_usage"
    echo -e "Disk Usage:       $disk_usage"
    echo -e "Server Memory:    $server_mem"
    
    echo ""
    echo -e "${YELLOW}📈 Database Stats${NC}"
    echo -e "${YELLOW}─────────────────${NC}"
    echo -e "Total Predictions:  $total_predictions"
    echo -e "Recent (1h):        $recent_pred"
    
    echo ""
}

# Function to start server
start_server() {
    echo -e "${BLUE}🚀 Starting Solar Prediction System...${NC}"

    # Clean up any existing processes first
    cleanup_solar_processes
    sleep 1

    # Check if port is free
    if lsof -ti:8100 >/dev/null 2>&1; then
        echo -e "${RED}❌ Port 8100 is still in use. Please wait and try again.${NC}"
        return 1
    fi

    # Start server in background
    nohup python3 "$SCRIPT_PATH" > "$LOG_FILE" 2>&1 &
    local new_pid=$!
    echo "$new_pid" > "$PID_FILE"

    echo -e "${GREEN}✅ Server started (PID: $new_pid)${NC}"
    echo -e "${CYAN}📝 Logs: tail -f $LOG_FILE${NC}"

    # Wait a moment for startup
    sleep 3

    # Verify startup
    if ps -p "$new_pid" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Server is running successfully${NC}"
    else
        echo -e "${RED}❌ Server failed to start. Check logs: $LOG_FILE${NC}"
        rm -f "$PID_FILE"
        return 1
    fi
}

# Function to stop server
stop_server() {
    echo -e "${BLUE}🛑 Stopping Solar Prediction System...${NC}"

    # Use comprehensive cleanup
    cleanup_solar_processes

    echo -e "${GREEN}✅ All Solar System processes stopped${NC}"
}

# Function to restart server
restart_server() {
    echo -e "${BLUE}🔄 Restarting Solar Prediction System...${NC}"

    # Comprehensive cleanup first
    cleanup_solar_processes
    sleep 3

    # Start fresh
    start_server
}

# Function to show logs
show_logs() {
    if [ -f "$LOG_FILE" ]; then
        echo -e "${CYAN}📝 Recent Logs (last 20 lines):${NC}"
        echo -e "${CYAN}─────────────────────────────────${NC}"
        tail -20 "$LOG_FILE"
    else
        echo -e "${YELLOW}⚠️  No log file found${NC}"
    fi
}

# Function to display menu
display_menu() {
    echo -e "${WHITE}🎛️  Management Options${NC}"
    echo -e "${WHITE}─────────────────────${NC}"
    echo -e "${GREEN}[1]${NC} Start System"
    echo -e "${RED}[2]${NC} Stop System"
    echo -e "${YELLOW}[3]${NC} Restart System"
    echo -e "${CYAN}[4]${NC} View Logs"
    echo -e "${PURPLE}[5]${NC} Refresh Status"
    echo -e "${BLUE}[6]${NC} Open Web Interface"
    echo -e "${BLUE}[7]${NC} Open Admin Panel"
    echo -e "${RED}[8]${NC} Force Cleanup (Kill Stuck Processes)"
    echo -e "${WHITE}[0]${NC} Exit Manager"
    echo ""
    echo -e "${WHITE}Choose option [0-8]:${NC} "
}

# Function to open browser
open_browser() {
    local url=$1
    local name=$2
    
    echo -e "${BLUE}🌐 Opening $name...${NC}"
    
    # Try different browsers
    if command -v xdg-open > /dev/null; then
        xdg-open "$url" 2>/dev/null &
    elif command -v firefox > /dev/null; then
        firefox "$url" 2>/dev/null &
    elif command -v google-chrome > /dev/null; then
        google-chrome "$url" 2>/dev/null &
    else
        echo -e "${YELLOW}⚠️  No browser found. Please open manually: $url${NC}"
    fi
}

# Main loop
main() {
    while true; do
        display_header
        display_status
        display_menu
        
        read -r choice
        echo ""
        
        case $choice in
            1)
                start_server
                echo ""
                echo -e "${CYAN}Press Enter to continue...${NC}"
                read -r
                ;;
            2)
                stop_server
                echo ""
                echo -e "${CYAN}Press Enter to continue...${NC}"
                read -r
                ;;
            3)
                restart_server
                echo ""
                echo -e "${CYAN}Press Enter to continue...${NC}"
                read -r
                ;;
            4)
                show_logs
                echo ""
                echo -e "${CYAN}Press Enter to continue...${NC}"
                read -r
                ;;
            5)
                # Just refresh (continue loop)
                ;;
            6)
                open_browser "http://localhost:8100/" "Web Interface"
                echo ""
                echo -e "${CYAN}Press Enter to continue...${NC}"
                read -r
                ;;
            7)
                open_browser "http://localhost:8100/admin" "Admin Panel"
                echo ""
                echo -e "${CYAN}Press Enter to continue...${NC}"
                read -r
                ;;
            8)
                echo -e "${RED}🧹 Force Cleanup - Killing all stuck processes...${NC}"
                cleanup_solar_processes
                echo ""
                echo -e "${GREEN}✅ Force cleanup completed${NC}"
                echo -e "${CYAN}Press Enter to continue...${NC}"
                read -r
                ;;
            0)
                echo -e "${GREEN}👋 Goodbye!${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}❌ Invalid option. Please choose 0-8.${NC}"
                echo ""
                echo -e "${CYAN}Press Enter to continue...${NC}"
                read -r
                ;;
        esac
    done
}

# Check if we're in the right directory
if [ ! -f "$SCRIPT_PATH" ]; then
    echo -e "${RED}❌ Error: $SCRIPT_PATH not found${NC}"
    echo -e "${YELLOW}Please run this script from the project root directory${NC}"
    exit 1
fi

# Start the manager
main
