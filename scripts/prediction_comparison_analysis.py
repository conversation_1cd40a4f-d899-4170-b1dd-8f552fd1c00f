#!/usr/bin/env python3
"""
Comprehensive Prediction & Comparison Analysis
ANSWERS USER'S QUESTIONS:
1. Predictions for next days and hours
2. Comparison with real data (this year vs last year)
3. Deviation analysis and root causes
4. Seasonal pattern validation
"""

import os
import sys
import json
import joblib
import numpy as np
import pandas as pd
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, List, Tuple
import psycopg2
from psycopg2.extras import RealDictCursor
import warnings
warnings.filterwarnings('ignore')

def get_db_connection():
    """Get PostgreSQL database connection"""
    try:
        return psycopg2.connect(
            host=os.getenv("DB_HOST", "localhost"),
            database=os.getenv("DB_NAME", "solar_prediction"),
            user=os.getenv("DB_USER", "postgres"),
            password=os.getenv("DB_PASSWORD", "postgres"),
            port=os.getenv("DB_PORT", "5432")
        )
    except Exception as e:
        print(f"⚠️  Database connection failed: {e}")
        return None

class PredictionComparisonAnalyzer:
    """Comprehensive analyzer for predictions vs real data"""
    
    def __init__(self):
        self.conn = get_db_connection()
        self.models = {}
        self.scalers = {}
        self.load_corrected_models()
    
    def load_corrected_models(self):
        """Load the corrected yield-based models"""
        print("📊 Loading corrected yield-based models...")
        
        for system_id in [1, 2]:
            model_dir = f"models/corrected_yield_system{system_id}"
            
            if os.path.exists(f"{model_dir}/model.joblib"):
                self.models[system_id] = joblib.load(f"{model_dir}/model.joblib")
                self.scalers[system_id] = joblib.load(f"{model_dir}/scaler.joblib")
                
                with open(f"{model_dir}/metadata.json", 'r') as f:
                    metadata = json.load(f)
                
                accuracy = metadata['performance']['accuracy_percent']
                print(f"✅ System {system_id}: {accuracy:.1f}% accuracy model loaded")
            else:
                print(f"❌ Model not found for System {system_id}")
    
    def create_prediction_features(self, date: datetime, system_id: int) -> np.array:
        """Create features for prediction"""
        month = date.month
        day_of_year = date.timetuple().tm_yday
        day_of_week = date.weekday()
        
        # Seasonal patterns for Greece
        season = (month - 1) // 3
        is_peak_season = 1 if month in [5, 6, 7] else 0
        is_low_season = 1 if month in [12, 1, 2] else 0
        is_weekend = 1 if day_of_week >= 5 else 0
        
        # Cyclical encoding
        month_sin = np.sin(2 * np.pi * month / 12)
        month_cos = np.cos(2 * np.pi * month / 12)
        day_sin = np.sin(2 * np.pi * day_of_year / 365)
        day_cos = np.cos(2 * np.pi * day_of_year / 365)
        
        # Solar position
        solar_declination = 23.45 * np.sin(np.radians(360 * (284 + day_of_year) / 365))
        day_length = 12 + 4 * np.sin(np.radians(solar_declination))
        
        # System-specific seasonal factors (based on historical patterns)
        if system_id == 1:
            if month in [12, 1, 2]:  # Winter
                seasonal_yield_factor = 29.3
                monthly_yield_factor = 29.3
            elif month in [3, 4, 5]:  # Spring
                seasonal_yield_factor = 59.3
                monthly_yield_factor = 59.3
            elif month in [6, 7, 8]:  # Summer
                seasonal_yield_factor = 65.1
                monthly_yield_factor = 65.1
            else:  # Autumn
                seasonal_yield_factor = 44.0
                monthly_yield_factor = 44.0
        else:  # System 2
            if month in [12, 1, 2]:  # Winter
                seasonal_yield_factor = 29.3
                monthly_yield_factor = 29.3
            elif month in [3, 4, 5]:  # Spring
                seasonal_yield_factor = 53.5  # System 2 lower in spring
                monthly_yield_factor = 53.5
            elif month in [6, 7, 8]:  # Summer
                seasonal_yield_factor = 65.0
                monthly_yield_factor = 65.0
            else:  # Autumn
                seasonal_yield_factor = 44.0
                monthly_yield_factor = 44.0
        
        # Estimate yield efficiency (this would normally come from the model)
        estimated_daily_yield = seasonal_yield_factor
        yield_efficiency = estimated_daily_yield / (day_length + 1)
        
        # Feature vector (must match training features)
        features = [
            month,                    # month
            day_of_year,             # day_of_year
            season,                  # season
            day_of_week,             # day_of_week
            is_weekend,              # is_weekend
            month_sin,               # month_sin
            month_cos,               # month_cos
            day_sin,                 # day_sin
            day_cos,                 # day_cos
            is_peak_season,          # is_peak_season
            is_low_season,           # is_low_season
            solar_declination,       # solar_declination
            day_length,              # day_length
            yield_efficiency,        # yield_efficiency
            seasonal_yield_factor,   # seasonal_yield_factor
            monthly_yield_factor     # monthly_yield_factor
        ]
        
        return np.array([features])
    
    def predict_next_days(self, days_ahead: int = 7) -> Dict[str, Any]:
        """Predict yield for next N days"""
        print(f"\n🔮 PREDICTING NEXT {days_ahead} DAYS")
        print("=" * 50)
        
        predictions = {}
        today = datetime.now()
        
        for system_id in [1, 2]:
            if system_id not in self.models:
                print(f"❌ No model available for System {system_id}")
                continue
            
            system_predictions = []
            
            for i in range(days_ahead):
                pred_date = today + timedelta(days=i)
                
                # Create features
                features = self.create_prediction_features(pred_date, system_id)
                
                # Scale and predict
                features_scaled = self.scalers[system_id].transform(features)
                prediction = self.models[system_id].predict(features_scaled)[0]
                
                system_predictions.append({
                    'date': pred_date.strftime('%Y-%m-%d'),
                    'day_name': pred_date.strftime('%A'),
                    'predicted_yield': prediction,
                    'month': pred_date.month,
                    'is_weekend': pred_date.weekday() >= 5
                })
            
            predictions[f"system_{system_id}"] = system_predictions
            
            # Print predictions
            print(f"\n📊 System {system_id} Predictions:")
            print("Date       | Day      | Predicted Yield")
            print("-" * 40)
            
            total_predicted = 0
            for pred in system_predictions:
                total_predicted += pred['predicted_yield']
                weekend_flag = " (Weekend)" if pred['is_weekend'] else ""
                print(f"{pred['date']} | {pred['day_name'][:8]:8s} | {pred['predicted_yield']:6.1f} kWh{weekend_flag}")
            
            avg_predicted = total_predicted / len(system_predictions)
            print(f"Average: {avg_predicted:.1f} kWh/day")
        
        return predictions
    
    def get_real_data_comparison(self, days_back: int = 30) -> Dict[str, Any]:
        """Get real data for comparison (this year vs last year)"""
        print(f"\n📊 REAL DATA COMPARISON (Last {days_back} days)")
        print("=" * 50)
        
        if not self.conn:
            print("❌ No database connection - using mock data")
            return self.create_mock_comparison_data(days_back)
        
        comparison_data = {}
        current_year = datetime.now().year
        last_year = current_year - 1
        
        for system_id in [1, 2]:
            table_name = 'solax_data' if system_id == 1 else 'solax_data2'
            
            try:
                with self.conn.cursor(cursor_factory=RealDictCursor) as cur:
                    # Get this year's data
                    cur.execute(f"""
                        WITH daily_yields AS (
                            SELECT 
                                DATE(timestamp) as date,
                                MAX(yield_today) as daily_yield
                            FROM {table_name}
                            WHERE DATE(timestamp) >= CURRENT_DATE - INTERVAL '{days_back} days'
                                AND EXTRACT(year FROM timestamp) = {current_year}
                            GROUP BY DATE(timestamp)
                            HAVING MAX(yield_today) > 0 AND MAX(yield_today) < 100
                        )
                        SELECT * FROM daily_yields ORDER BY date DESC
                    """)
                    this_year_data = cur.fetchall()
                    
                    # Get last year's data (same dates)
                    cur.execute(f"""
                        WITH daily_yields AS (
                            SELECT 
                                DATE(timestamp) as date,
                                MAX(yield_today) as daily_yield
                            FROM {table_name}
                            WHERE DATE(timestamp) >= DATE '{current_year}-01-01' - INTERVAL '1 year' + (CURRENT_DATE - DATE '{current_year}-01-01')
                                AND DATE(timestamp) <= CURRENT_DATE - INTERVAL '1 year'
                                AND EXTRACT(year FROM timestamp) = {last_year}
                            GROUP BY DATE(timestamp)
                            HAVING MAX(yield_today) > 0 AND MAX(yield_today) < 100
                        )
                        SELECT * FROM daily_yields ORDER BY date DESC LIMIT {days_back}
                    """)
                    last_year_data = cur.fetchall()
                
                comparison_data[f"system_{system_id}"] = {
                    'this_year': [dict(row) for row in this_year_data],
                    'last_year': [dict(row) for row in last_year_data]
                }
                
                print(f"✅ System {system_id}: {len(this_year_data)} days this year, {len(last_year_data)} days last year")
                
            except Exception as e:
                print(f"❌ Error getting data for System {system_id}: {e}")
                comparison_data[f"system_{system_id}"] = {'this_year': [], 'last_year': []}
        
        return comparison_data
    
    def create_mock_comparison_data(self, days_back: int) -> Dict[str, Any]:
        """Create realistic mock data for comparison"""
        print("🔧 Creating mock comparison data...")
        
        comparison_data = {}
        today = datetime.now()
        
        for system_id in [1, 2]:
            this_year_data = []
            last_year_data = []
            
            for i in range(days_back):
                date_this_year = today - timedelta(days=i)
                date_last_year = date_this_year.replace(year=date_this_year.year - 1)
                
                # Seasonal base yield
                month = date_this_year.month
                if month in [12, 1, 2]:  # Winter
                    base_yield = 29
                elif month in [3, 4, 5]:  # Spring
                    base_yield = 59 if system_id == 1 else 54  # System 2 lower in spring
                elif month in [6, 7, 8]:  # Summer
                    base_yield = 65
                else:  # Autumn
                    base_yield = 44
                
                # System 2 generally produces more
                if system_id == 2:
                    base_yield *= 1.1
                
                # Add realistic variability
                this_year_yield = max(0, base_yield + np.random.normal(0, 5))
                last_year_yield = max(0, base_yield + np.random.normal(0, 5))
                
                this_year_data.append({
                    'date': date_this_year.date(),
                    'daily_yield': this_year_yield
                })
                
                last_year_data.append({
                    'date': date_last_year.date(),
                    'daily_yield': last_year_yield
                })
            
            comparison_data[f"system_{system_id}"] = {
                'this_year': this_year_data,
                'last_year': last_year_data
            }
        
        return comparison_data
    
    def analyze_prediction_accuracy(self, predictions: Dict, real_data: Dict) -> Dict[str, Any]:
        """Analyze prediction accuracy vs real data"""
        print(f"\n🔍 PREDICTION ACCURACY ANALYSIS")
        print("=" * 50)
        
        analysis_results = {}
        
        for system_key in ['system_1', 'system_2']:
            if system_key not in predictions or system_key not in real_data:
                continue
            
            system_id = int(system_key.split('_')[1])
            pred_data = predictions[system_key]
            real_this_year = real_data[system_key]['this_year']
            real_last_year = real_data[system_key]['last_year']
            
            # Calculate averages
            if pred_data:
                avg_predicted = np.mean([p['predicted_yield'] for p in pred_data])
            else:
                avg_predicted = 0
            
            if real_this_year:
                avg_this_year = np.mean([r['daily_yield'] for r in real_this_year])
            else:
                avg_this_year = 0
            
            if real_last_year:
                avg_last_year = np.mean([r['daily_yield'] for r in real_last_year])
            else:
                avg_last_year = 0
            
            # Calculate deviations
            pred_vs_this_year = avg_predicted - avg_this_year if avg_this_year > 0 else 0
            pred_vs_last_year = avg_predicted - avg_last_year if avg_last_year > 0 else 0
            this_year_vs_last_year = avg_this_year - avg_last_year if avg_last_year > 0 else 0
            
            # Calculate percentage deviations
            pred_vs_this_year_pct = (pred_vs_this_year / avg_this_year * 100) if avg_this_year > 0 else 0
            pred_vs_last_year_pct = (pred_vs_last_year / avg_last_year * 100) if avg_last_year > 0 else 0
            this_year_vs_last_year_pct = (this_year_vs_last_year / avg_last_year * 100) if avg_last_year > 0 else 0
            
            analysis_results[system_key] = {
                'avg_predicted': avg_predicted,
                'avg_this_year': avg_this_year,
                'avg_last_year': avg_last_year,
                'pred_vs_this_year_kwh': pred_vs_this_year,
                'pred_vs_last_year_kwh': pred_vs_last_year,
                'this_year_vs_last_year_kwh': this_year_vs_last_year,
                'pred_vs_this_year_pct': pred_vs_this_year_pct,
                'pred_vs_last_year_pct': pred_vs_last_year_pct,
                'this_year_vs_last_year_pct': this_year_vs_last_year_pct
            }
            
            print(f"\n📊 System {system_id} Analysis:")
            print(f"   Predicted (next days): {avg_predicted:.1f} kWh/day")
            print(f"   This year actual:      {avg_this_year:.1f} kWh/day")
            print(f"   Last year actual:      {avg_last_year:.1f} kWh/day")
            print(f"   ")
            print(f"   Prediction vs This Year: {pred_vs_this_year:+.1f} kWh ({pred_vs_this_year_pct:+.1f}%)")
            print(f"   Prediction vs Last Year: {pred_vs_last_year:+.1f} kWh ({pred_vs_last_year_pct:+.1f}%)")
            print(f"   This Year vs Last Year:  {this_year_vs_last_year:+.1f} kWh ({this_year_vs_last_year_pct:+.1f}%)")
        
        return analysis_results
    
    def identify_deviation_causes(self, analysis_results: Dict) -> Dict[str, List[str]]:
        """Identify potential causes of deviations"""
        print(f"\n🔍 DEVIATION CAUSE ANALYSIS")
        print("=" * 50)
        
        causes = {}
        current_month = datetime.now().month
        current_season = ["Winter", "Spring", "Summer", "Autumn"][(current_month - 1) // 3]
        
        for system_key, results in analysis_results.items():
            system_id = int(system_key.split('_')[1])
            system_causes = []
            
            # Analyze prediction vs this year
            pred_vs_this_pct = results['pred_vs_this_year_pct']
            if abs(pred_vs_this_pct) > 10:  # >10% deviation
                if pred_vs_this_pct > 0:
                    system_causes.append(f"Model over-predicts by {pred_vs_this_pct:.1f}% vs this year")
                else:
                    system_causes.append(f"Model under-predicts by {abs(pred_vs_this_pct):.1f}% vs this year")
            
            # Analyze year-over-year changes
            year_change_pct = results['this_year_vs_last_year_pct']
            if abs(year_change_pct) > 5:  # >5% year-over-year change
                if year_change_pct > 0:
                    system_causes.append(f"Production increased {year_change_pct:.1f}% vs last year")
                else:
                    system_causes.append(f"Production decreased {abs(year_change_pct):.1f}% vs last year")
            
            # System-specific issues
            if system_id == 2 and current_month in [3, 4, 5]:  # Spring issue for System 2
                system_causes.append("System 2 has known spring performance issues")
            
            # Seasonal factors
            if current_season == "Spring" and abs(pred_vs_this_pct) > 15:
                system_causes.append("Spring weather variability affects predictions")
            elif current_season == "Winter" and abs(pred_vs_this_pct) > 20:
                system_causes.append("Winter low-light conditions increase uncertainty")
            
            # Model training factors
            if abs(pred_vs_this_pct) > 5:
                system_causes.append("Model trained on limited historical data")
                system_causes.append("Weather patterns may differ from training period")
            
            causes[system_key] = system_causes
            
            print(f"\n🔍 System {system_id} Potential Causes:")
            for i, cause in enumerate(system_causes, 1):
                print(f"   {i}. {cause}")
        
        return causes

def main():
    """Main prediction and comparison analysis"""
    print("🔮 COMPREHENSIVE PREDICTION & COMPARISON ANALYSIS")
    print("=" * 70)
    print(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    analyzer = PredictionComparisonAnalyzer()
    
    # 1. Predict next 7 days
    predictions = analyzer.predict_next_days(days_ahead=7)
    
    # 2. Get real data for comparison
    real_data = analyzer.get_real_data_comparison(days_back=30)
    
    # 3. Analyze prediction accuracy
    analysis_results = analyzer.analyze_prediction_accuracy(predictions, real_data)
    
    # 4. Identify deviation causes
    deviation_causes = analyzer.identify_deviation_causes(analysis_results)
    
    # 5. Summary
    print(f"\n📋 SUMMARY")
    print("=" * 50)
    
    for system_key in ['system_1', 'system_2']:
        if system_key in analysis_results:
            system_id = int(system_key.split('_')[1])
            results = analysis_results[system_key]
            
            print(f"\n🏆 System {system_id} Summary:")
            print(f"   Next 7 days prediction: {results['avg_predicted']:.1f} kWh/day")
            print(f"   Deviation vs this year: {results['pred_vs_this_year_pct']:+.1f}%")
            print(f"   Deviation vs last year: {results['pred_vs_last_year_pct']:+.1f}%")
            
            if abs(results['pred_vs_this_year_pct']) <= 5:
                print(f"   ✅ Excellent prediction accuracy (<5% deviation)")
            elif abs(results['pred_vs_this_year_pct']) <= 10:
                print(f"   ✅ Good prediction accuracy (<10% deviation)")
            else:
                print(f"   ⚠️  Significant deviation (>10%) - needs investigation")
    
    print(f"\n✅ Analysis completed!")
    return {
        'predictions': predictions,
        'real_data': real_data,
        'analysis_results': analysis_results,
        'deviation_causes': deviation_causes
    }

if __name__ == "__main__":
    main()
