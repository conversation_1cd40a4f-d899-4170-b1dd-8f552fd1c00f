#!/usr/bin/env python3
"""
Simple CSV to SQL
Convert CSV data to SQL INSERT statements
"""

import csv
import os
from datetime import datetime

def convert_csv_to_sql():
    """Convert CSV to SQL INSERT statements"""
    print("🔄 CONVERTING CSV TO SQL")
    print("=" * 30)
    
    csv_file = "data/raw/System2/Plant Reports 2024-03-01-2024-06-28.csv"
    sql_file = "system2_import.sql"
    
    if not os.path.exists(csv_file):
        print(f"❌ CSV file not found: {csv_file}")
        return False
    
    try:
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            
            # Read header
            header = next(reader)
            print(f"Header: {header}")
            
            # Clean header (remove BOM and quotes)
            clean_header = []
            for col in header:
                clean_col = col.replace('\ufeff', '').replace('"', '').strip()
                clean_header.append(clean_col)

            print(f"Clean header: {clean_header}")

            # Find column indices
            time_col_idx = None
            yield_col_idx = None

            for i, col in enumerate(clean_header):
                if 'Update time' in col:
                    time_col_idx = i
                elif 'Daily inverter output' in col and 'kWh' in col:
                    yield_col_idx = i
            
            if time_col_idx is None or yield_col_idx is None:
                print("❌ Required columns not found")
                return False
            
            # Generate SQL
            sql_statements = []
            
            # Create table statement
            sql_statements.append("""
-- Create table for System 2 data
CREATE TABLE IF NOT EXISTS solax_data2 (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMP NOT NULL,
    inverter_sn VARCHAR(50),
    wifi_sn VARCHAR(50),
    yield_today DECIMAL(10,2),
    soc DECIMAL(5,2),
    bat_power DECIMAL(10,2),
    temperature DECIMAL(5,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create index
CREATE INDEX IF NOT EXISTS idx_solax_data2_timestamp ON solax_data2(timestamp);

-- Clear existing CSV import data
DELETE FROM solax_data2 WHERE inverter_sn = 'SYSTEM2_CSV_IMPORT';

-- Insert data
""")
            
            # Process data
            valid_records = 0
            batch_size = 100
            current_batch = []
            
            for row in reader:
                if len(row) > max(time_col_idx, yield_col_idx):
                    try:
                        # Parse timestamp
                        time_str = row[time_col_idx].strip('"')
                        dt = datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
                        
                        # Parse yield
                        yield_str = row[yield_col_idx].strip('"')
                        yield_val = float(yield_str)
                        
                        # Add to batch
                        current_batch.append((dt, yield_val))
                        valid_records += 1
                        
                        # Process batch
                        if len(current_batch) >= batch_size:
                            sql_statements.append(create_insert_statement(current_batch))
                            current_batch = []
                        
                        if valid_records % 1000 == 0:
                            print(f"   Processed {valid_records} records...")
                            
                    except (ValueError, IndexError):
                        continue
            
            # Process remaining batch
            if current_batch:
                sql_statements.append(create_insert_statement(current_batch))
            
            # Write SQL file
            with open(sql_file, 'w') as sql_f:
                sql_f.write('\n'.join(sql_statements))
            
            print(f"✅ SQL file created: {sql_file}")
            print(f"   Valid records: {valid_records}")
            
            return True
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def create_insert_statement(batch):
    """Create INSERT statement for batch"""
    values = []
    for dt, yield_val in batch:
        values.append(f"('{dt}', 'SYSTEM2_CSV_IMPORT', 'SYSTEM2_CSV_IMPORT', {yield_val}, 50.0, 0.0, 20.0)")
    
    return f"""
INSERT INTO solax_data2 (timestamp, inverter_sn, wifi_sn, yield_today, soc, bat_power, temperature) VALUES
{', '.join(values)};
"""

def execute_sql_file():
    """Execute the SQL file"""
    print("\n💾 EXECUTING SQL FILE")
    print("=" * 25)
    
    sql_file = "system2_import.sql"
    
    if not os.path.exists(sql_file):
        print(f"❌ SQL file not found: {sql_file}")
        return False
    
    # Execute using psql command
    cmd = f'psql -h localhost -U postgres -d solar_prediction -f {sql_file}'
    print(f"Executing: {cmd}")
    
    result = os.system(cmd)
    
    if result == 0:
        print("✅ SQL execution completed successfully!")
        return True
    else:
        print(f"❌ SQL execution failed with code: {result}")
        return False

def verify_import():
    """Verify the import"""
    print("\n📊 VERIFYING IMPORT")
    print("=" * 20)
    
    cmd = """psql -h localhost -U postgres -d solar_prediction -c "
SELECT 
    COUNT(*) as total_records,
    MIN(timestamp) as earliest,
    MAX(timestamp) as latest,
    MIN(yield_today) as min_yield,
    MAX(yield_today) as max_yield
FROM solax_data2 
WHERE inverter_sn = 'SYSTEM2_CSV_IMPORT';
" """
    
    print("Verification query:")
    result = os.system(cmd)
    
    return result == 0

def main():
    """Main function"""
    print("🚀 SIMPLE CSV TO SQL CONVERTER")
    print("=" * 40)
    
    # Step 1: Convert CSV to SQL
    if not convert_csv_to_sql():
        return False
    
    # Step 2: Execute SQL
    if not execute_sql_file():
        return False
    
    # Step 3: Verify import
    if not verify_import():
        return False
    
    print("\n🎉 IMPORT COMPLETED SUCCESSFULLY!")
    print("✅ All System 2 data imported to database")
    print("🔄 Ready for April 2024 vs 2025 vs 2026 comparison")
    
    return True

if __name__ == "__main__":
    main()
