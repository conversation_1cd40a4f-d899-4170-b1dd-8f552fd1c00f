#!/usr/bin/env python3
"""
Enhanced Seasonal Yield Trainer
MANDATORY: Advanced seasonal pattern recognition for yield predictions
- Detailed seasonal analysis (Spring, Summer, Autumn, Winter)
- Solar angle calculations for Greece (38.14°N, 24.01°E)
- Day length variations throughout the year
- Seasonal weather pattern integration
- System-specific seasonal behaviors
"""

import os
import sys
import json
import joblib
import numpy as np
import pandas as pd
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any, List, Tuple
import psycopg2
from psycopg2.extras import RealDictCursor
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')

# Solar position calculations for Marathon, Greece (38.14°N, 24.01°E)
LATITUDE = 38.141348260997596
LONGITUDE = 24.0071653937747

def calculate_solar_position(day_of_year: int, hour: int) -> Dict[str, float]:
    """Calculate solar position for Marathon, Greece"""
    
    # Solar declination angle
    declination = 23.45 * np.sin(np.radians(360 * (284 + day_of_year) / 365))
    
    # Hour angle (solar noon = 0)
    hour_angle = 15 * (hour - 12)
    
    # Solar elevation angle
    elevation = np.arcsin(
        np.sin(np.radians(declination)) * np.sin(np.radians(LATITUDE)) +
        np.cos(np.radians(declination)) * np.cos(np.radians(LATITUDE)) * np.cos(np.radians(hour_angle))
    )
    
    # Solar azimuth angle
    azimuth = np.arctan2(
        np.sin(np.radians(hour_angle)),
        np.cos(np.radians(hour_angle)) * np.sin(np.radians(LATITUDE)) - 
        np.tan(np.radians(declination)) * np.cos(np.radians(LATITUDE))
    )
    
    # Convert to degrees and ensure positive elevation
    elevation_deg = max(0, np.degrees(elevation))
    azimuth_deg = np.degrees(azimuth)
    
    # Day length calculation
    day_length = 2 * np.arccos(-np.tan(np.radians(LATITUDE)) * np.tan(np.radians(declination))) / 15
    day_length = max(8, min(16, day_length))  # Reasonable bounds for Greece
    
    return {
        'solar_elevation': elevation_deg,
        'solar_azimuth': azimuth_deg,
        'solar_declination': declination,
        'day_length_hours': day_length,
        'is_daylight': 1 if elevation_deg > 0 else 0
    }

def get_seasonal_characteristics(month: int, day_of_year: int) -> Dict[str, float]:
    """Get detailed seasonal characteristics for Greece"""
    
    # Define seasons for Greece (Mediterranean climate)
    if month in [12, 1, 2]:  # Winter
        season = 0
        season_name = "Winter"
        expected_ghi = 200  # Lower solar irradiance
        expected_temp = 12  # Average winter temperature
        expected_cloud = 60  # More cloudy days
    elif month in [3, 4, 5]:  # Spring
        season = 1
        season_name = "Spring"
        expected_ghi = 500  # Increasing solar irradiance
        expected_temp = 18  # Mild temperatures
        expected_cloud = 40  # Moderate cloudiness
    elif month in [6, 7, 8]:  # Summer
        season = 2
        season_name = "Summer"
        expected_ghi = 800  # Peak solar irradiance
        expected_temp = 28  # Hot temperatures
        expected_cloud = 20  # Clear skies
    else:  # Autumn (9, 10, 11)
        season = 3
        season_name = "Autumn"
        expected_ghi = 400  # Decreasing solar irradiance
        expected_temp = 20  # Cooling temperatures
        expected_cloud = 50  # Increasing cloudiness
    
    # Seasonal progression within the season (0-1)
    season_start_day = [335, 60, 152, 244][season]  # Approximate season start days
    season_progress = ((day_of_year - season_start_day) % 365) / 90  # 90 days per season
    season_progress = max(0, min(1, season_progress))
    
    # Peak production months (May-July for Greece)
    peak_production_factor = 1.0
    if month in [5, 6, 7]:  # Peak months
        peak_production_factor = 1.2
    elif month in [4, 8]:  # Good months
        peak_production_factor = 1.1
    elif month in [3, 9]:  # Moderate months
        peak_production_factor = 0.9
    elif month in [10, 11, 2]:  # Lower months
        peak_production_factor = 0.7
    else:  # Winter months (12, 1)
        peak_production_factor = 0.5
    
    return {
        'season': season,
        'season_progress': season_progress,
        'expected_ghi': expected_ghi,
        'expected_temp': expected_temp,
        'expected_cloud': expected_cloud,
        'peak_production_factor': peak_production_factor,
        'is_peak_season': 1 if month in [5, 6, 7] else 0,
        'is_low_season': 1 if month in [12, 1, 2] else 0
    }

def create_enhanced_seasonal_features(df: pd.DataFrame) -> pd.DataFrame:
    """Create comprehensive seasonal features"""
    print("🌍 Creating enhanced seasonal features...")

    # Basic temporal features (use existing columns if available)
    if 'hour' not in df.columns:
        df['hour'] = 12  # Default to noon for daily predictions
    if 'day_of_week' not in df.columns:
        df['day_of_week'] = pd.to_datetime(df['date']).dt.dayofweek
    
    # Cyclical encoding for smooth transitions
    df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
    df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
    df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
    df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
    df['day_sin'] = np.sin(2 * np.pi * df['day_of_year'] / 365)
    df['day_cos'] = np.cos(2 * np.pi * df['day_of_year'] / 365)
    
    # Enhanced seasonal features
    seasonal_features = []
    solar_features = []
    
    for idx, row in df.iterrows():
        # Get seasonal characteristics
        seasonal = get_seasonal_characteristics(row['month'], row['day_of_year'])
        seasonal_features.append(seasonal)
        
        # Get solar position
        solar = calculate_solar_position(row['day_of_year'], row['hour'])
        solar_features.append(solar)
    
    # Add seasonal features
    seasonal_df = pd.DataFrame(seasonal_features)
    for col in seasonal_df.columns:
        df[f'seasonal_{col}'] = seasonal_df[col].values
    
    # Add solar position features
    solar_df = pd.DataFrame(solar_features)
    for col in solar_df.columns:
        df[f'solar_{col}'] = solar_df[col].values
    
    # Interaction features
    df['ghi_season_interaction'] = df.get('ghi', 500) * df['seasonal_peak_production_factor']
    df['temp_season_efficiency'] = 1 - abs(df.get('temperature_2m', 25) - df['seasonal_expected_temp']) * 0.02
    df['cloud_season_factor'] = (100 - df.get('cloud_cover', 30)) / 100 * df['seasonal_peak_production_factor']
    
    # Time-based patterns
    df['is_weekend'] = (df['day_of_week'] >= 5).astype(int)
    df['is_morning'] = ((df['hour'] >= 6) & (df['hour'] <= 10)).astype(int)
    df['is_midday'] = ((df['hour'] >= 11) & (df['hour'] <= 15)).astype(int)
    df['is_evening'] = ((df['hour'] >= 16) & (df['hour'] <= 19)).astype(int)
    
    print(f"✅ Enhanced seasonal features created: {len([col for col in df.columns if 'seasonal_' in col or 'solar_' in col])} new features")
    
    return df

class EnhancedSeasonalYieldTrainer:
    """Enhanced trainer with detailed seasonal analysis"""
    
    def __init__(self):
        self.conn = psycopg2.connect(
            host=os.getenv("DB_HOST", "localhost"),
            database=os.getenv("DB_NAME", "solar_prediction"),
            user=os.getenv("DB_USER", "postgres"),
            password=os.getenv("DB_PASSWORD", "postgres"),
            port=os.getenv("DB_PORT", "5432")
        )
    
    def get_seasonal_training_data(self, system_id: int) -> pd.DataFrame:
        """Get comprehensive seasonal training data"""
        table_name = 'solax_data' if system_id == 1 else 'solax_data2'
        
        print(f"📊 Loading seasonal data for System {system_id}...")
        
        # Get all available data with seasonal context
        query = f"""
        WITH seasonal_data AS (
            SELECT 
                timestamp,
                yield_today,
                soc,
                bat_power,
                temperature,
                EXTRACT(month FROM timestamp) as month,
                EXTRACT(doy FROM timestamp) as day_of_year,
                EXTRACT(hour FROM timestamp) as hour,
                DATE(timestamp) as date
            FROM {table_name}
            WHERE timestamp >= '2024-03-01'
                AND yield_today >= 0
            ORDER BY timestamp
        ),
        daily_yields AS (
            SELECT 
                date,
                month,
                day_of_year,
                MAX(yield_today) as daily_total_yield,
                AVG(soc) as avg_soc,
                AVG(temperature) as avg_temp,
                COUNT(*) as measurements
            FROM seasonal_data
            GROUP BY date, month, day_of_year
            HAVING COUNT(*) >= 10 AND MAX(yield_today) > 0 AND MAX(yield_today) < 100
        )
        SELECT * FROM daily_yields
        ORDER BY date
        """
        
        df = pd.read_sql(query, self.conn)
        print(f"✅ Loaded {len(df):,} daily records for System {system_id}")
        
        return df
    
    def analyze_seasonal_patterns(self, df: pd.DataFrame, system_id: int) -> Dict[str, Any]:
        """Analyze seasonal patterns in the data"""
        print(f"🔍 Analyzing seasonal patterns for System {system_id}...")
        
        # Group by season
        seasonal_stats = df.groupby('month').agg({
            'daily_total_yield': ['mean', 'std', 'min', 'max', 'count'],
            'avg_soc': 'mean',
            'avg_temp': 'mean'
        }).round(2)
        
        print("📊 Monthly Yield Statistics:")
        print("Month | Avg Yield | Std | Min | Max | Count")
        print("-" * 45)
        
        monthly_analysis = {}
        for month in range(1, 13):
            if month in seasonal_stats.index:
                stats = seasonal_stats.loc[month]
                avg_yield = stats[('daily_total_yield', 'mean')]
                std_yield = stats[('daily_total_yield', 'std')]
                min_yield = stats[('daily_total_yield', 'min')]
                max_yield = stats[('daily_total_yield', 'max')]
                count = stats[('daily_total_yield', 'count')]
                
                monthly_analysis[month] = {
                    'avg_yield': avg_yield,
                    'std_yield': std_yield,
                    'min_yield': min_yield,
                    'max_yield': max_yield,
                    'count': count
                }
                
                print(f"{month:5d} | {avg_yield:9.1f} | {std_yield:3.1f} | {min_yield:3.1f} | {max_yield:3.1f} | {count:5.0f}")
        
        # Seasonal grouping
        seasons = {
            'Winter': [12, 1, 2],
            'Spring': [3, 4, 5], 
            'Summer': [6, 7, 8],
            'Autumn': [9, 10, 11]
        }
        
        seasonal_analysis = {}
        print(f"\n📊 Seasonal Analysis:")
        for season_name, months in seasons.items():
            season_data = df[df['month'].isin(months)]
            if len(season_data) > 0:
                avg_yield = season_data['daily_total_yield'].mean()
                std_yield = season_data['daily_total_yield'].std()
                count = len(season_data)
                
                seasonal_analysis[season_name] = {
                    'avg_yield': avg_yield,
                    'std_yield': std_yield,
                    'count': count,
                    'months': months
                }
                
                print(f"{season_name:8s}: {avg_yield:6.1f} ± {std_yield:4.1f} kWh ({count:3d} days)")
        
        return {
            'monthly_analysis': monthly_analysis,
            'seasonal_analysis': seasonal_analysis,
            'total_days': len(df),
            'yield_range': {
                'min': df['daily_total_yield'].min(),
                'max': df['daily_total_yield'].max(),
                'mean': df['daily_total_yield'].mean(),
                'std': df['daily_total_yield'].std()
            }
        }
    
    def train_seasonal_model(self, system_id: int) -> Dict[str, Any]:
        """Train enhanced seasonal model"""
        print(f"\n🎯 Training Enhanced Seasonal Model for System {system_id}")
        print("=" * 60)
        
        # Get seasonal training data
        df = self.get_seasonal_training_data(system_id)
        
        if len(df) < 100:
            raise ValueError(f"Insufficient data for System {system_id}: {len(df)} records")
        
        # Analyze seasonal patterns
        seasonal_analysis = self.analyze_seasonal_patterns(df, system_id)
        
        # Create enhanced seasonal features
        df_enhanced = create_enhanced_seasonal_features(df)
        
        # Define feature columns
        feature_cols = [col for col in df_enhanced.columns if any(prefix in col for prefix in [
            'hour_', 'month_', 'day_', 'seasonal_', 'solar_', 'ghi_', 'temp_', 'cloud_',
            'is_weekend', 'is_morning', 'is_midday', 'is_evening', 'avg_soc', 'avg_temp'
        ])]
        
        # Prepare training data
        X = df_enhanced[feature_cols].fillna(0)
        y = df_enhanced['daily_total_yield']
        
        print(f"📊 Training data: {len(X):,} samples, {len(feature_cols)} features")
        print(f"📊 Yield range: {y.min():.2f} - {y.max():.2f} kWh")
        
        # Train model
        model = RandomForestRegressor(n_estimators=300, random_state=42, n_jobs=-1)
        scaler = StandardScaler()
        
        X_scaled = scaler.fit_transform(X)
        model.fit(X_scaled, y)
        
        # Evaluate
        y_pred = model.predict(X_scaled)
        r2 = r2_score(y, y_pred)
        mae = mean_absolute_error(y, y_pred)
        rmse = np.sqrt(mean_squared_error(y, y_pred))
        
        # Feature importance
        feature_importance = dict(zip(feature_cols, model.feature_importances_))
        top_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)[:15]
        
        print(f"\n✅ Enhanced Seasonal Model Results:")
        print(f"   Accuracy: {r2*100:.1f}% (R² = {r2:.3f})")
        print(f"   MAE: {mae:.2f} kWh")
        print(f"   RMSE: {rmse:.2f} kWh")
        
        print(f"\n🔍 Top 15 Features:")
        for i, (feature, importance) in enumerate(top_features, 1):
            print(f"   {i:2d}. {feature:30s}: {importance:.4f} ({importance*100:.1f}%)")
        
        return {
            'model': model,
            'scaler': scaler,
            'feature_columns': feature_cols,
            'feature_importance': feature_importance,
            'seasonal_analysis': seasonal_analysis,
            'performance': {
                'r2_score': r2,
                'accuracy_percent': r2 * 100,
                'mae': mae,
                'rmse': rmse
            },
            'training_samples': len(X),
            'system_id': system_id
        }

def main():
    """Main enhanced seasonal training"""
    print("🌍 Enhanced Seasonal Yield-Based Model Training")
    print("Advanced seasonal pattern recognition for Greece")
    print("=" * 70)
    
    trainer = EnhancedSeasonalYieldTrainer()
    
    for system_id in [1, 2]:
        try:
            result = trainer.train_seasonal_model(system_id)
            
            # Save model
            model_dir = f"models/enhanced_seasonal_system{system_id}"
            os.makedirs(model_dir, exist_ok=True)
            
            joblib.dump(result['model'], f"{model_dir}/model.joblib")
            joblib.dump(result['scaler'], f"{model_dir}/scaler.joblib")
            
            # Save metadata (convert numpy types to Python types)
            metadata = {
                'model_type': 'enhanced_seasonal_yield',
                'system_id': int(system_id),
                'created_at': datetime.now().isoformat(),
                'performance': {k: float(v) for k, v in result['performance'].items()},
                'seasonal_analysis': result['seasonal_analysis'],
                'feature_importance': {k: float(v) for k, v in sorted(result['feature_importance'].items(), key=lambda x: x[1], reverse=True)[:20]},
                'training_samples': int(result['training_samples']),
                'target_accuracy_met': bool(result['performance']['accuracy_percent'] >= 95.0)
            }
            
            with open(f"{model_dir}/metadata.json", 'w') as f:
                json.dump(metadata, f, indent=2)
            
            print(f"\n✅ Enhanced Seasonal Model saved for System {system_id}")
            
        except Exception as e:
            print(f"❌ Training failed for System {system_id}: {e}")
    
    print("\n🌍 Enhanced seasonal training completed!")

if __name__ == "__main__":
    main()
