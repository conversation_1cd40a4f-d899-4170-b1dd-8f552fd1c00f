#!/usr/bin/env python3
"""
AUTOMATED RETRAINING SYSTEM
Automated retraining schedule for seasonal models
Created: June 4, 2025
"""

import os
import sys
import json
import shutil
import psycopg2
import subprocess
from datetime import datetime, timedelta
from pathlib import Path
import logging
from typing import Dict, List, Any

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/automated_retraining.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AutomatedRetraining:
    """Automated retraining system for seasonal models"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent.parent
        self.models_dir = self.project_root / "models"
        self.seasonal_models_dir = self.models_dir / "seasonal_models"
        self.backup_dir = self.models_dir / "backups"
        
        self.db_configs = [
            "postgresql://grlv:Gr1234@localhost:5433/solar_prediction",
            "postgresql://postgres:postgres@localhost:5433/solar_prediction"
        ]
        
        # Retraining criteria
        self.retraining_criteria = {
            'min_data_points': 1000,  # Minimum new data points
            'performance_threshold': 0.85,  # Minimum R² score
            'days_since_last_training': 30,  # Days since last training
            'accuracy_drop_threshold': 0.05  # Max accuracy drop before retraining
        }
        
        # Create directories
        self.backup_dir.mkdir(exist_ok=True)
        Path("logs").mkdir(exist_ok=True)
        
        logger.info("🔄 Automated Retraining System initialized")
    
    def connect_database(self):
        """Connect to PostgreSQL database"""
        for config in self.db_configs:
            try:
                conn = psycopg2.connect(config)
                return conn
            except Exception as e:
                continue
        return None
    
    def check_data_availability(self) -> Dict[str, Any]:
        """Check if enough new data is available for retraining"""
        conn = self.connect_database()
        if not conn:
            logger.error("❌ Cannot connect to database")
            return {}
        
        try:
            cursor = conn.cursor()
            
            # Check data from last 30 days
            check_date = datetime.now() - timedelta(days=30)
            
            # Check solax_data
            cursor.execute("""
                SELECT COUNT(*) FROM solax_data 
                WHERE timestamp > %s
            """, (check_date,))
            
            solax_count = cursor.fetchone()[0]
            
            # Check weather_data
            cursor.execute("""
                SELECT COUNT(*) FROM weather_data 
                WHERE timestamp > %s AND is_forecast = false
            """, (check_date,))
            
            weather_count = cursor.fetchone()[0]
            
            # Check performance metrics
            cursor.execute("""
                SELECT 
                    AVG(confidence_score) as avg_confidence,
                    COUNT(*) as metrics_count
                FROM performance_metrics 
                WHERE timestamp > %s AND status = 'success'
            """, (check_date,))
            
            perf_result = cursor.fetchone()
            avg_confidence = perf_result[0] if perf_result[0] else 0
            metrics_count = perf_result[1]
            
            cursor.close()
            conn.close()
            
            data_status = {
                'check_date': check_date.isoformat(),
                'solax_data_points': solax_count,
                'weather_data_points': weather_count,
                'performance_metrics': metrics_count,
                'avg_confidence': avg_confidence,
                'sufficient_data': solax_count >= self.retraining_criteria['min_data_points'],
                'performance_acceptable': avg_confidence >= self.retraining_criteria['performance_threshold']
            }
            
            logger.info(f"📊 Data availability check:")
            logger.info(f"   SolaX data points: {solax_count:,}")
            logger.info(f"   Weather data points: {weather_count:,}")
            logger.info(f"   Performance metrics: {metrics_count:,}")
            logger.info(f"   Average confidence: {avg_confidence:.3f}")
            
            return data_status
            
        except Exception as e:
            logger.error(f"❌ Failed to check data availability: {e}")
            return {}
    
    def check_model_performance(self) -> Dict[str, Any]:
        """Check current model performance"""
        conn = self.connect_database()
        if not conn:
            return {}
        
        try:
            cursor = conn.cursor()
            
            # Get performance by season from last 7 days
            check_date = datetime.now() - timedelta(days=7)
            
            cursor.execute("""
                SELECT 
                    season,
                    AVG(confidence_score) as avg_confidence,
                    COUNT(*) as prediction_count,
                    AVG(response_time_ms) as avg_response_time
                FROM performance_metrics 
                WHERE timestamp > %s AND status = 'success'
                GROUP BY season
                ORDER BY season
            """, (check_date,))
            
            results = cursor.fetchall()
            
            performance_data = {
                'check_period': '7 days',
                'seasonal_performance': {},
                'overall_performance': {
                    'avg_confidence': 0,
                    'total_predictions': 0,
                    'avg_response_time': 0
                }
            }
            
            total_confidence = 0
            total_predictions = 0
            total_response_time = 0
            
            for season, avg_conf, count, avg_resp in results:
                performance_data['seasonal_performance'][season] = {
                    'avg_confidence': avg_conf,
                    'prediction_count': count,
                    'avg_response_time': avg_resp,
                    'needs_retraining': avg_conf < self.retraining_criteria['performance_threshold']
                }
                
                total_confidence += avg_conf * count
                total_predictions += count
                total_response_time += avg_resp * count
            
            if total_predictions > 0:
                performance_data['overall_performance'] = {
                    'avg_confidence': total_confidence / total_predictions,
                    'total_predictions': total_predictions,
                    'avg_response_time': total_response_time / total_predictions
                }
            
            cursor.close()
            conn.close()
            
            return performance_data
            
        except Exception as e:
            logger.error(f"❌ Failed to check model performance: {e}")
            return {}
    
    def backup_current_models(self) -> bool:
        """Backup current seasonal models"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_path = self.backup_dir / f"seasonal_models_backup_{timestamp}"
            
            if self.seasonal_models_dir.exists():
                shutil.copytree(self.seasonal_models_dir, backup_path)
                logger.info(f"✅ Models backed up to {backup_path}")
                return True
            else:
                logger.warning("⚠️  No seasonal models directory found to backup")
                return False
                
        except Exception as e:
            logger.error(f"❌ Failed to backup models: {e}")
            return False
    
    def run_retraining_script(self) -> Dict[str, Any]:
        """Run the seasonal models training script"""
        try:
            logger.info("🚀 Starting automated retraining...")
            
            # Run the seasonal models demo script
            training_script = self.project_root / "scripts" / "training" / "seasonal_models_demo.py"
            
            if not training_script.exists():
                raise FileNotFoundError(f"Training script not found: {training_script}")
            
            # Execute training script
            result = subprocess.run([
                sys.executable, str(training_script)
            ], capture_output=True, text=True, timeout=3600)  # 1 hour timeout
            
            if result.returncode == 0:
                logger.info("✅ Retraining completed successfully")
                return {
                    'status': 'success',
                    'stdout': result.stdout,
                    'stderr': result.stderr,
                    'execution_time': 'completed'
                }
            else:
                logger.error(f"❌ Retraining failed with return code {result.returncode}")
                return {
                    'status': 'failed',
                    'stdout': result.stdout,
                    'stderr': result.stderr,
                    'return_code': result.returncode
                }
                
        except subprocess.TimeoutExpired:
            logger.error("❌ Retraining timed out after 1 hour")
            return {'status': 'timeout', 'message': 'Training timed out'}
        except Exception as e:
            logger.error(f"❌ Retraining error: {e}")
            return {'status': 'error', 'message': str(e)}
    
    def validate_new_models(self) -> Dict[str, Any]:
        """Validate newly trained models"""
        try:
            # Check if all 8 seasonal models exist
            expected_models = []
            for system_id in [1, 2]:
                for season in ['spring', 'summer', 'autumn', 'winter']:
                    model_dir = self.seasonal_models_dir / f"{season}_system{system_id}"
                    expected_models.append({
                        'path': model_dir,
                        'season': season,
                        'system_id': system_id,
                        'exists': model_dir.exists(),
                        'has_model': (model_dir / "model.joblib").exists(),
                        'has_scaler': (model_dir / "scaler.joblib").exists(),
                        'has_metadata': (model_dir / "metadata.json").exists()
                    })
            
            validation_results = {
                'total_models': len(expected_models),
                'valid_models': 0,
                'invalid_models': 0,
                'model_details': expected_models
            }
            
            for model in expected_models:
                if model['exists'] and model['has_model'] and model['has_scaler'] and model['has_metadata']:
                    validation_results['valid_models'] += 1
                    
                    # Load and check metadata
                    try:
                        with open(model['path'] / "metadata.json", 'r') as f:
                            metadata = json.load(f)
                        
                        model['performance'] = metadata.get('performance', {})
                        model['training_date'] = metadata.get('training_date', 'unknown')
                        
                    except Exception as e:
                        logger.warning(f"⚠️  Could not read metadata for {model['path']}: {e}")
                else:
                    validation_results['invalid_models'] += 1
            
            validation_results['all_models_valid'] = validation_results['valid_models'] == validation_results['total_models']
            
            logger.info(f"📊 Model validation: {validation_results['valid_models']}/{validation_results['total_models']} valid")
            
            return validation_results
            
        except Exception as e:
            logger.error(f"❌ Model validation failed: {e}")
            return {'status': 'error', 'message': str(e)}
    
    def should_retrain(self) -> Dict[str, Any]:
        """Determine if retraining should be performed"""
        logger.info("🔍 Checking if retraining is needed...")
        
        # Check data availability
        data_status = self.check_data_availability()
        if not data_status:
            return {'should_retrain': False, 'reason': 'Cannot check data availability'}
        
        # Check model performance
        performance_status = self.check_model_performance()
        if not performance_status:
            return {'should_retrain': False, 'reason': 'Cannot check model performance'}
        
        # Decision logic
        reasons = []
        should_retrain = False
        
        # Check if we have sufficient new data
        if not data_status['sufficient_data']:
            reasons.append(f"Insufficient data: {data_status['solax_data_points']} < {self.retraining_criteria['min_data_points']}")
        
        # Check if performance is degrading
        overall_perf = performance_status.get('overall_performance', {})
        if overall_perf.get('avg_confidence', 1.0) < self.retraining_criteria['performance_threshold']:
            reasons.append(f"Performance below threshold: {overall_perf.get('avg_confidence', 0):.3f} < {self.retraining_criteria['performance_threshold']}")
            should_retrain = True
        
        # Check individual seasonal performance
        seasonal_perf = performance_status.get('seasonal_performance', {})
        for season, perf in seasonal_perf.items():
            if perf.get('needs_retraining', False):
                reasons.append(f"{season.capitalize()} model needs retraining: {perf.get('avg_confidence', 0):.3f} < {self.retraining_criteria['performance_threshold']}")
                should_retrain = True
        
        # If we have sufficient data and no performance issues, check time since last training
        if data_status['sufficient_data'] and not should_retrain:
            # For now, assume we should retrain if we have enough data
            should_retrain = True
            reasons.append("Sufficient new data available for improvement")
        
        decision = {
            'should_retrain': should_retrain,
            'reasons': reasons,
            'data_status': data_status,
            'performance_status': performance_status
        }
        
        logger.info(f"🎯 Retraining decision: {'YES' if should_retrain else 'NO'}")
        for reason in reasons:
            logger.info(f"   - {reason}")
        
        return decision
    
    def run_automated_retraining(self) -> Dict[str, Any]:
        """Run complete automated retraining process"""
        logger.info("🚀 Starting automated retraining process")
        
        process_log = {
            'start_time': datetime.now().isoformat(),
            'steps': [],
            'final_status': 'unknown'
        }
        
        try:
            # Step 1: Check if retraining is needed
            decision = self.should_retrain()
            process_log['steps'].append({
                'step': 'retraining_decision',
                'status': 'completed',
                'result': decision
            })
            
            if not decision['should_retrain']:
                process_log['final_status'] = 'skipped'
                logger.info("✅ Retraining not needed at this time")
                return process_log
            
            # Step 2: Backup current models
            backup_success = self.backup_current_models()
            process_log['steps'].append({
                'step': 'backup_models',
                'status': 'completed' if backup_success else 'failed',
                'result': backup_success
            })
            
            if not backup_success:
                logger.warning("⚠️  Backup failed, but continuing with retraining")
            
            # Step 3: Run retraining
            training_result = self.run_retraining_script()
            process_log['steps'].append({
                'step': 'model_training',
                'status': training_result['status'],
                'result': training_result
            })
            
            if training_result['status'] != 'success':
                process_log['final_status'] = 'failed'
                logger.error("❌ Retraining failed")
                return process_log
            
            # Step 4: Validate new models
            validation_result = self.validate_new_models()
            process_log['steps'].append({
                'step': 'model_validation',
                'status': 'completed',
                'result': validation_result
            })
            
            if not validation_result.get('all_models_valid', False):
                process_log['final_status'] = 'validation_failed'
                logger.error("❌ Model validation failed")
                return process_log
            
            process_log['final_status'] = 'success'
            process_log['end_time'] = datetime.now().isoformat()
            
            logger.info("✅ Automated retraining completed successfully")
            return process_log
            
        except Exception as e:
            process_log['final_status'] = 'error'
            process_log['error'] = str(e)
            logger.error(f"❌ Automated retraining error: {e}")
            return process_log


def main():
    """Main retraining function"""
    retrainer = AutomatedRetraining()
    
    if len(sys.argv) > 1 and sys.argv[1] == "check":
        # Just check if retraining is needed
        decision = retrainer.should_retrain()
        print(json.dumps(decision, indent=2, default=str))
    elif len(sys.argv) > 1 and sys.argv[1] == "force":
        # Force retraining
        logger.info("🔄 Forcing retraining...")
        result = retrainer.run_retraining_script()
        print(json.dumps(result, indent=2, default=str))
    else:
        # Run full automated process
        result = retrainer.run_automated_retraining()
        print(json.dumps(result, indent=2, default=str))


if __name__ == "__main__":
    main()
