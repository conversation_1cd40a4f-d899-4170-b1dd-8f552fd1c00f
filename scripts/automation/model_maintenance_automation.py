#!/usr/bin/env python3
"""
Model Maintenance Automation
Automated model monitoring, drift detection, and retraining
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import json
import joblib
import schedule
import time
import threading
from dataclasses import dataclass
from typing import Dict, List, Optional
from sklearn.metrics import mean_absolute_error, r2_score
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/automation/model_maintenance.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class ModelMaintenanceConfig:
    """Configuration for model maintenance automation"""
    drift_check_frequency_hours: int = 12
    performance_check_frequency_hours: int = 6
    retraining_check_frequency_hours: int = 24
    calibration_update_frequency_hours: int = 6
    
    # Thresholds
    mae_threshold: float = 2.5  # Retrain if MAE > 2.5 kWh
    confidence_threshold: float = 0.6  # Retrain if avg confidence < 60%
    drift_threshold: float = 0.3  # Retrain if drift score > 0.3
    r2_threshold: float = 0.85  # Retrain if R² < 0.85
    
    # Model paths
    models_dir: str = 'models/phase3'
    backup_dir: str = 'models/backup'

class ModelMaintenanceAutomation:
    """Automated model maintenance and monitoring"""
    
    def __init__(self, config: ModelMaintenanceConfig):
        self.config = config
        self.automation_active = False
        self.maintenance_history = []
        
        # Current models
        self.current_models = {}
        self.current_scaler = None
        self.model_metadata = {}
        
        # Performance tracking
        self.performance_history = []
        self.drift_history = []
        
        # Ensure directories exist
        os.makedirs('logs/automation', exist_ok=True)
        os.makedirs(self.config.backup_dir, exist_ok=True)
        
        # Load current models
        self.load_current_models()
    
    def load_current_models(self):
        """Load current production models"""
        
        logger.info("📦 Loading current production models...")
        
        try:
            if not os.path.exists(self.config.models_dir):
                logger.warning(f"⚠️ Models directory not found: {self.config.models_dir}")
                return False
            
            # Find latest model files
            model_files = [f for f in os.listdir(self.config.models_dir) if f.endswith('.joblib')]
            
            if not model_files:
                logger.warning("⚠️ No model files found")
                return False
            
            # Get latest timestamp
            timestamps = set()
            for f in model_files:
                if '_20' in f:
                    timestamp = f.split('_')[-1].replace('.joblib', '')
                    timestamps.add(timestamp)
            
            if not timestamps:
                logger.warning("⚠️ No valid timestamps found in model files")
                return False
            
            latest_timestamp = max(timestamps)
            logger.info(f"📅 Loading models with timestamp: {latest_timestamp}")
            
            # Load models
            model_types = ['random_forest', 'gradient_boosting']
            
            for model_type in model_types:
                model_file = f'{model_type}_model_{latest_timestamp}.joblib'
                model_path = os.path.join(self.config.models_dir, model_file)
                
                if os.path.exists(model_path):
                    self.current_models[model_type] = joblib.load(model_path)
                    logger.info(f"   ✅ Loaded {model_type}")
                else:
                    logger.warning(f"   ⚠️ Model file not found: {model_file}")
            
            # Load scaler
            scaler_file = f'feature_scaler_{latest_timestamp}.joblib'
            scaler_path = os.path.join(self.config.models_dir, scaler_file)
            
            if os.path.exists(scaler_path):
                self.current_scaler = joblib.load(scaler_path)
                logger.info(f"   ✅ Loaded feature scaler")
            
            # Load metadata
            results_file = f'validation_results_{latest_timestamp}.json'
            results_path = os.path.join(self.config.models_dir, results_file)
            
            if os.path.exists(results_path):
                with open(results_path, 'r') as f:
                    self.model_metadata = json.load(f)
                logger.info(f"   ✅ Loaded model metadata")
            
            logger.info(f"🎉 Models loaded successfully: {len(self.current_models)} models")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to load models: {e}")
            return False
    
    def check_model_performance(self):
        """Check current model performance against recent data"""
        
        logger.info("📊 Checking model performance...")
        
        task_start = datetime.now()
        
        try:
            # Get recent data with features
            recent_data = self.get_recent_data_with_features()
            
            if recent_data is None or len(recent_data) < 10:
                logger.warning("⚠️ Insufficient recent data for performance check")
                return {'status': 'insufficient_data'}
            
            # Prepare features
            feature_columns = [col for col in recent_data.columns 
                             if col not in ['timestamp', 'system_id', 'yield_today', 'total_yield']]
            
            X = recent_data[feature_columns].fillna(0)
            y_true = recent_data['yield_today'].fillna(0)
            
            if self.current_scaler:
                X_scaled = self.current_scaler.transform(X)
            else:
                X_scaled = X.values
            
            # Make predictions with each model
            performance_results = {}
            
            for model_name, model in self.current_models.items():
                try:
                    y_pred = model.predict(X_scaled)
                    
                    # Calculate metrics
                    mae = mean_absolute_error(y_true, y_pred)
                    r2 = r2_score(y_true, y_pred)
                    
                    # Calculate confidence (inverse of prediction variance)
                    pred_std = np.std(y_pred)
                    confidence = max(0.1, 1 - (pred_std / (np.mean(y_pred) + 0.001)))
                    
                    performance_results[model_name] = {
                        'mae': mae,
                        'r2': r2,
                        'confidence': confidence,
                        'predictions_count': len(y_pred),
                        'meets_mae_threshold': mae < self.config.mae_threshold,
                        'meets_r2_threshold': r2 > self.config.r2_threshold,
                        'meets_confidence_threshold': confidence > self.config.confidence_threshold
                    }
                    
                    logger.info(f"   📊 {model_name}: MAE {mae:.3f}, R² {r2:.3f}, Confidence {confidence:.3f}")
                    
                except Exception as e:
                    logger.error(f"❌ Performance check failed for {model_name}: {e}")
                    performance_results[model_name] = {'error': str(e)}
            
            # Calculate ensemble performance
            if len(performance_results) >= 2:
                ensemble_mae = np.mean([r['mae'] for r in performance_results.values() if 'mae' in r])
                ensemble_r2 = np.mean([r['r2'] for r in performance_results.values() if 'r2' in r])
                ensemble_confidence = np.mean([r['confidence'] for r in performance_results.values() if 'confidence' in r])
                
                performance_results['ensemble'] = {
                    'mae': ensemble_mae,
                    'r2': ensemble_r2,
                    'confidence': ensemble_confidence,
                    'meets_mae_threshold': ensemble_mae < self.config.mae_threshold,
                    'meets_r2_threshold': ensemble_r2 > self.config.r2_threshold,
                    'meets_confidence_threshold': ensemble_confidence > self.config.confidence_threshold
                }
            
            task_duration = (datetime.now() - task_start).total_seconds()
            
            result = {
                'task': 'check_model_performance',
                'timestamp': task_start.isoformat(),
                'duration_seconds': task_duration,
                'status': 'success',
                'performance_results': performance_results,
                'data_points': len(recent_data),
                'retraining_recommended': self.should_retrain_based_on_performance(performance_results)
            }
            
            self.performance_history.append(result)
            self.maintenance_history.append(result)
            
            # Check if retraining is needed
            if result['retraining_recommended']:
                logger.warning("🚨 Model performance degradation detected - retraining recommended")
            else:
                logger.info("✅ Model performance within acceptable thresholds")
            
            return result
            
        except Exception as e:
            task_duration = (datetime.now() - task_start).total_seconds()
            
            result = {
                'task': 'check_model_performance',
                'timestamp': task_start.isoformat(),
                'duration_seconds': task_duration,
                'status': 'failed',
                'error': str(e)
            }
            
            self.maintenance_history.append(result)
            logger.error(f"❌ Model performance check failed: {e}")
            
            return result
    
    def detect_model_drift(self):
        """Detect model drift by comparing prediction distributions"""
        
        logger.info("📈 Detecting model drift...")
        
        task_start = datetime.now()
        
        try:
            # Get recent predictions and historical baseline
            recent_data = self.get_recent_data_with_features(days=1)  # Last 24 hours
            historical_data = self.get_recent_data_with_features(days=30, offset_days=1)  # Previous 30 days
            
            if recent_data is None or historical_data is None:
                logger.warning("⚠️ Insufficient data for drift detection")
                return {'status': 'insufficient_data'}
            
            if len(recent_data) < 10 or len(historical_data) < 100:
                logger.warning("⚠️ Insufficient data points for reliable drift detection")
                return {'status': 'insufficient_data'}
            
            # Prepare features
            feature_columns = [col for col in recent_data.columns 
                             if col not in ['timestamp', 'system_id', 'yield_today', 'total_yield']]
            
            # Calculate feature drift
            drift_results = {}
            
            for feature in feature_columns[:20]:  # Check top 20 features
                if feature in recent_data.columns and feature in historical_data.columns:
                    recent_values = recent_data[feature].dropna()
                    historical_values = historical_data[feature].dropna()
                    
                    if len(recent_values) > 5 and len(historical_values) > 20:
                        # Calculate statistical distance (simplified KL divergence)
                        recent_mean = recent_values.mean()
                        historical_mean = historical_values.mean()
                        historical_std = historical_values.std()
                        
                        if historical_std > 0:
                            drift_score = abs(recent_mean - historical_mean) / historical_std
                        else:
                            drift_score = 0
                        
                        drift_results[feature] = {
                            'recent_mean': recent_mean,
                            'historical_mean': historical_mean,
                            'drift_score': drift_score,
                            'drift_detected': drift_score > self.config.drift_threshold
                        }
            
            # Calculate overall drift score
            if drift_results:
                overall_drift_score = np.mean([r['drift_score'] for r in drift_results.values()])
                drift_detected = overall_drift_score > self.config.drift_threshold
            else:
                overall_drift_score = 0
                drift_detected = False
            
            task_duration = (datetime.now() - task_start).total_seconds()
            
            result = {
                'task': 'detect_model_drift',
                'timestamp': task_start.isoformat(),
                'duration_seconds': task_duration,
                'status': 'success',
                'drift_results': drift_results,
                'overall_drift_score': overall_drift_score,
                'drift_detected': drift_detected,
                'features_checked': len(drift_results),
                'retraining_recommended': drift_detected
            }
            
            self.drift_history.append(result)
            self.maintenance_history.append(result)
            
            if drift_detected:
                logger.warning(f"🚨 Model drift detected: {overall_drift_score:.3f} > {self.config.drift_threshold}")
            else:
                logger.info(f"✅ No significant model drift: {overall_drift_score:.3f}")
            
            return result
            
        except Exception as e:
            task_duration = (datetime.now() - task_start).total_seconds()
            
            result = {
                'task': 'detect_model_drift',
                'timestamp': task_start.isoformat(),
                'duration_seconds': task_duration,
                'status': 'failed',
                'error': str(e)
            }
            
            self.maintenance_history.append(result)
            logger.error(f"❌ Model drift detection failed: {e}")
            
            return result
    
    def should_retrain_based_on_performance(self, performance_results):
        """Determine if retraining is needed based on performance"""
        
        # Check ensemble performance first
        if 'ensemble' in performance_results:
            ensemble = performance_results['ensemble']
            if (not ensemble.get('meets_mae_threshold', True) or 
                not ensemble.get('meets_r2_threshold', True) or 
                not ensemble.get('meets_confidence_threshold', True)):
                return True
        
        # Check individual models
        failing_models = 0
        total_models = 0
        
        for model_name, results in performance_results.items():
            if model_name != 'ensemble' and 'mae' in results:
                total_models += 1
                if (not results.get('meets_mae_threshold', True) or 
                    not results.get('meets_r2_threshold', True) or 
                    not results.get('meets_confidence_threshold', True)):
                    failing_models += 1
        
        # Retrain if more than 50% of models are failing
        return failing_models > (total_models / 2) if total_models > 0 else False
    
    def execute_automated_retraining(self):
        """Execute automated model retraining"""
        
        logger.info("🔄 Starting automated model retraining...")
        
        task_start = datetime.now()
        
        try:
            # Backup current models
            backup_timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            self.backup_current_models(backup_timestamp)
            
            # Get training data
            training_data = self.get_recent_data_with_features(days=30)
            
            if training_data is None or len(training_data) < 1000:
                raise Exception("Insufficient training data")
            
            # Prepare training data
            feature_columns = [col for col in training_data.columns 
                             if col not in ['timestamp', 'system_id', 'yield_today', 'total_yield']]
            
            X = training_data[feature_columns].fillna(0)
            y = training_data['yield_today'].fillna(0)
            
            # Split data
            split_idx = int(len(X) * 0.8)
            X_train, X_val = X[:split_idx], X[split_idx:]
            y_train, y_val = y[:split_idx], y[split_idx:]
            
            # Scale features
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_val_scaled = scaler.transform(X_val)
            
            # Train new models
            new_models = {}
            validation_results = {}
            
            # Random Forest
            rf_model = RandomForestRegressor(
                n_estimators=200,
                max_depth=15,
                min_samples_split=5,
                random_state=42,
                n_jobs=-1
            )
            rf_model.fit(X_train_scaled, y_train)
            
            rf_pred = rf_model.predict(X_val_scaled)
            rf_mae = mean_absolute_error(y_val, rf_pred)
            rf_r2 = r2_score(y_val, rf_pred)
            
            new_models['random_forest'] = rf_model
            validation_results['random_forest'] = {
                'val_mae': rf_mae,
                'val_r2': rf_r2,
                'target_met': rf_mae < self.config.mae_threshold
            }
            
            # Gradient Boosting
            gb_model = GradientBoostingRegressor(
                n_estimators=200,
                max_depth=8,
                learning_rate=0.1,
                random_state=42
            )
            gb_model.fit(X_train_scaled, y_train)
            
            gb_pred = gb_model.predict(X_val_scaled)
            gb_mae = mean_absolute_error(y_val, gb_pred)
            gb_r2 = r2_score(y_val, gb_pred)
            
            new_models['gradient_boosting'] = gb_model
            validation_results['gradient_boosting'] = {
                'val_mae': gb_mae,
                'val_r2': gb_r2,
                'target_met': gb_mae < self.config.mae_threshold
            }
            
            # Check if new models are better
            new_models_acceptable = all(r['target_met'] for r in validation_results.values())
            
            if new_models_acceptable:
                # Save new models
                self.save_retrained_models(new_models, scaler, validation_results)
                
                # Update current models
                self.current_models = new_models
                self.current_scaler = scaler
                self.model_metadata = validation_results
                
                task_duration = (datetime.now() - task_start).total_seconds()
                
                result = {
                    'task': 'execute_automated_retraining',
                    'timestamp': task_start.isoformat(),
                    'duration_seconds': task_duration,
                    'status': 'success',
                    'validation_results': validation_results,
                    'models_deployed': True,
                    'backup_timestamp': backup_timestamp,
                    'training_samples': len(X_train)
                }
                
                logger.info(f"✅ Automated retraining completed successfully")
                logger.info(f"   📊 Random Forest: MAE {rf_mae:.3f}, R² {rf_r2:.3f}")
                logger.info(f"   📊 Gradient Boosting: MAE {gb_mae:.3f}, R² {gb_r2:.3f}")
                
            else:
                # New models don't meet criteria
                result = {
                    'task': 'execute_automated_retraining',
                    'timestamp': task_start.isoformat(),
                    'duration_seconds': task_duration,
                    'status': 'models_rejected',
                    'validation_results': validation_results,
                    'models_deployed': False,
                    'reason': 'New models do not meet performance criteria'
                }
                
                logger.warning("⚠️ New models rejected - performance below threshold")
            
            self.maintenance_history.append(result)
            return result
            
        except Exception as e:
            task_duration = (datetime.now() - task_start).total_seconds()
            
            result = {
                'task': 'execute_automated_retraining',
                'timestamp': task_start.isoformat(),
                'duration_seconds': task_duration,
                'status': 'failed',
                'error': str(e)
            }
            
            self.maintenance_history.append(result)
            logger.error(f"❌ Automated retraining failed: {e}")
            
            return result
    
    def get_recent_data_with_features(self, days=7, offset_days=0):
        """Get recent data with computed features"""
        
        try:
            # This would connect to the enhanced_features_realtime table
            # For now, return None to indicate data unavailability
            logger.info(f"📊 Attempting to get {days} days of feature data (offset: {offset_days})")
            return None
            
        except Exception as e:
            logger.error(f"❌ Failed to get recent data: {e}")
            return None
    
    def backup_current_models(self, backup_timestamp):
        """Backup current models before retraining"""
        
        logger.info(f"💾 Backing up current models with timestamp: {backup_timestamp}")
        
        try:
            for model_name, model in self.current_models.items():
                backup_path = os.path.join(self.config.backup_dir, f'{model_name}_backup_{backup_timestamp}.joblib')
                joblib.dump(model, backup_path)
            
            if self.current_scaler:
                scaler_backup_path = os.path.join(self.config.backup_dir, f'scaler_backup_{backup_timestamp}.joblib')
                joblib.dump(self.current_scaler, scaler_backup_path)
            
            logger.info("✅ Models backed up successfully")
            
        except Exception as e:
            logger.error(f"❌ Model backup failed: {e}")
    
    def save_retrained_models(self, models, scaler, validation_results):
        """Save newly retrained models"""
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        try:
            # Save models
            for model_name, model in models.items():
                model_path = os.path.join(self.config.models_dir, f'{model_name}_model_{timestamp}.joblib')
                joblib.dump(model, model_path)
            
            # Save scaler
            scaler_path = os.path.join(self.config.models_dir, f'feature_scaler_{timestamp}.joblib')
            joblib.dump(scaler, scaler_path)
            
            # Save validation results
            results_path = os.path.join(self.config.models_dir, f'validation_results_{timestamp}.json')
            with open(results_path, 'w') as f:
                json.dump(validation_results, f, indent=2, default=str)
            
            logger.info(f"✅ Retrained models saved with timestamp: {timestamp}")
            
        except Exception as e:
            logger.error(f"❌ Failed to save retrained models: {e}")
    
    def schedule_maintenance_tasks(self):
        """Schedule all maintenance tasks"""
        
        logger.info("📅 Scheduling model maintenance tasks...")
        
        # Schedule performance checks
        schedule.every(self.config.performance_check_frequency_hours).hours.do(
            self.check_model_performance
        )
        
        # Schedule drift detection
        schedule.every(self.config.drift_check_frequency_hours).hours.do(
            self.detect_model_drift
        )
        
        # Schedule retraining checks
        schedule.every(self.config.retraining_check_frequency_hours).hours.do(
            self.check_retraining_triggers
        )
        
        logger.info("✅ Model maintenance tasks scheduled:")
        logger.info(f"   📊 Performance checks: every {self.config.performance_check_frequency_hours} hours")
        logger.info(f"   📈 Drift detection: every {self.config.drift_check_frequency_hours} hours")
        logger.info(f"   🔄 Retraining checks: every {self.config.retraining_check_frequency_hours} hours")
    
    def check_retraining_triggers(self):
        """Check if retraining should be triggered"""
        
        logger.info("🎯 Checking retraining triggers...")
        
        try:
            triggers_met = []
            
            # Check recent performance
            recent_performance = [h for h in self.performance_history 
                                if datetime.fromisoformat(h['timestamp']) > datetime.now() - timedelta(hours=24)]
            
            if recent_performance:
                latest_performance = recent_performance[-1]
                if latest_performance.get('retraining_recommended', False):
                    triggers_met.append('performance_degradation')
            
            # Check recent drift
            recent_drift = [h for h in self.drift_history 
                          if datetime.fromisoformat(h['timestamp']) > datetime.now() - timedelta(hours=24)]
            
            if recent_drift:
                latest_drift = recent_drift[-1]
                if latest_drift.get('drift_detected', False):
                    triggers_met.append('data_drift')
            
            # Check time-based trigger (weekly retraining)
            last_retraining = None
            for h in reversed(self.maintenance_history):
                if h.get('task') == 'execute_automated_retraining' and h.get('status') == 'success':
                    last_retraining = datetime.fromisoformat(h['timestamp'])
                    break
            
            if last_retraining is None or (datetime.now() - last_retraining).days >= 7:
                triggers_met.append('scheduled_retraining')
            
            # Execute retraining if triggers met
            if triggers_met:
                logger.info(f"🚨 Retraining triggers met: {', '.join(triggers_met)}")
                self.execute_automated_retraining()
            else:
                logger.info("✅ No retraining triggers met")
            
            return triggers_met
            
        except Exception as e:
            logger.error(f"❌ Retraining trigger check failed: {e}")
            return []
    
    def run_maintenance_automation(self):
        """Run model maintenance automation"""
        
        logger.info("🚀 Starting model maintenance automation...")
        
        self.automation_active = True
        
        # Schedule tasks
        self.schedule_maintenance_tasks()
        
        # Run initial checks
        logger.info("🔄 Running initial maintenance checks...")
        self.check_model_performance()
        self.detect_model_drift()
        
        # Main automation loop
        try:
            while self.automation_active:
                schedule.run_pending()
                time.sleep(300)  # Check every 5 minutes
                
        except KeyboardInterrupt:
            logger.info("⏹️ Model maintenance stopped by user")
            self.automation_active = False
        except Exception as e:
            logger.error(f"❌ Model maintenance loop failed: {e}")
            self.automation_active = False
    
    def stop_automation(self):
        """Stop maintenance automation"""
        self.automation_active = False
        logger.info("⏹️ Model maintenance stopping...")

def main():
    """Main model maintenance function"""
    
    print("🤖 MODEL MAINTENANCE AUTOMATION")
    print("="*60)
    print("📊 Automated model monitoring and retraining")
    print("🔄 Drift detection and performance tracking")
    print()
    
    try:
        # Initialize automation
        config = ModelMaintenanceConfig()
        automation = ModelMaintenanceAutomation(config)
        
        # Run automation
        automation.run_maintenance_automation()
        
        return True
        
    except Exception as e:
        print(f"❌ Model maintenance automation failed: {e}")
        logger.exception("Model maintenance automation failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
