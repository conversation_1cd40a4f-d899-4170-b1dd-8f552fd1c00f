#!/bin/bash
# REMOVE PREDICTION CRON JOBS
# Remove automated prediction caching cron jobs
# Created: June 4, 2025

echo "🗑️  REMOVING PREDICTION CRON JOBS"
echo "================================="

# Temporary cron file
TEMP_CRON="/tmp/prediction_cron_remove"

# Get existing cron jobs (excluding our prediction jobs)
crontab -l 2>/dev/null | grep -v "# Prediction Cache" | grep -v "predictions_cache_manager.py" > "$TEMP_CRON"

# Install the cleaned cron jobs
crontab "$TEMP_CRON"

if [ $? -eq 0 ]; then
    echo "✅ Prediction cron jobs removed successfully"
else
    echo "❌ Failed to remove cron jobs"
    exit 1
fi

# Clean up
rm "$TEMP_CRON"

echo ""
echo "📋 REMAINING CRON JOBS:"
echo "======================"
crontab -l 2>/dev/null || echo "No cron jobs remaining"

echo ""
echo "✅ PREDICTION CRON JOBS REMOVAL COMPLETED!"
