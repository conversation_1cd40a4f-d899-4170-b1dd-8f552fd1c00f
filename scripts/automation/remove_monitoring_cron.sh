#!/bin/bash
# REMOVE MONITORING CRON JOBS
# Remove automated monitoring and retraining cron jobs
# Created: June 4, 2025

echo "🗑️  REMOVING MONITORING CRON JOBS"
echo "================================="

# Temporary cron file
TEMP_CRON="/tmp/solar_monitoring_cron_remove"

# Get existing cron jobs (excluding our monitoring jobs)
crontab -l 2>/dev/null | grep -v "# Solar Monitoring" | grep -v "performance_monitor.py" | grep -v "performance_alerts.py" | grep -v "automated_retraining.py" > "$TEMP_CRON"

# Install the cleaned cron jobs
crontab "$TEMP_CRON"

if [ $? -eq 0 ]; then
    echo "✅ Solar monitoring cron jobs removed successfully"
else
    echo "❌ Failed to remove cron jobs"
    exit 1
fi

# Clean up
rm "$TEMP_CRON"

echo ""
echo "📋 REMAINING CRON JOBS:"
echo "======================"
crontab -l 2>/dev/null || echo "No cron jobs remaining"

echo ""
echo "✅ MONITORING CRON JOBS REMOVAL COMPLETED!"
