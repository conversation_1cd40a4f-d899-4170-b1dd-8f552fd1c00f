#!/bin/bash
# SETUP MONITORING CRON JOBS
# Setup automated monitoring and retraining cron jobs
# Created: June 4, 2025

echo "🔄 SETTING UP MONITORING CRON JOBS"
echo "=================================="

# Get project directory
PROJECT_DIR="/home/<USER>/solar-prediction-project"
PYTHON_PATH="$PROJECT_DIR/venv/bin/python3"

# Check if project directory exists
if [ ! -d "$PROJECT_DIR" ]; then
    echo "❌ Project directory not found: $PROJECT_DIR"
    exit 1
fi

# Check if virtual environment exists
if [ ! -f "$PYTHON_PATH" ]; then
    echo "❌ Python virtual environment not found: $PYTHON_PATH"
    echo "   Please create virtual environment first"
    exit 1
fi

echo "✅ Project directory: $PROJECT_DIR"
echo "✅ Python path: $PYTHON_PATH"

# Create logs directory
mkdir -p "$PROJECT_DIR/logs"

# Create cron jobs
echo ""
echo "📋 Creating cron job entries..."

# Temporary cron file
TEMP_CRON="/tmp/solar_monitoring_cron"

# Get existing cron jobs (excluding our monitoring jobs)
crontab -l 2>/dev/null | grep -v "# Solar Monitoring" | grep -v "performance_monitor.py" | grep -v "performance_alerts.py" | grep -v "automated_retraining.py" > "$TEMP_CRON"

# Add our monitoring cron jobs
cat >> "$TEMP_CRON" << EOF

# Solar Monitoring - Performance Monitor (every 15 minutes)
*/15 * * * * cd $PROJECT_DIR && $PYTHON_PATH scripts/monitoring/performance_monitor.py once >> logs/cron_performance.log 2>&1

# Solar Monitoring - Performance Alerts (every hour)
0 * * * * cd $PROJECT_DIR && $PYTHON_PATH scripts/monitoring/performance_alerts.py once >> logs/cron_alerts.log 2>&1

# Solar Monitoring - Automated Retraining Check (daily at 2 AM)
0 2 * * * cd $PROJECT_DIR && $PYTHON_PATH scripts/automation/automated_retraining.py check >> logs/cron_retraining.log 2>&1

# Solar Monitoring - Weekly Retraining (Sundays at 3 AM)
0 3 * * 0 cd $PROJECT_DIR && $PYTHON_PATH scripts/automation/automated_retraining.py >> logs/cron_weekly_retraining.log 2>&1

EOF

# Install the new cron jobs
crontab "$TEMP_CRON"

if [ $? -eq 0 ]; then
    echo "✅ Cron jobs installed successfully"
else
    echo "❌ Failed to install cron jobs"
    exit 1
fi

# Clean up
rm "$TEMP_CRON"

echo ""
echo "📊 INSTALLED CRON JOBS:"
echo "======================"
echo "🔍 Performance Monitor: Every 15 minutes"
echo "🚨 Performance Alerts:  Every hour"
echo "🔄 Retraining Check:    Daily at 2 AM"
echo "🔄 Weekly Retraining:   Sundays at 3 AM"

echo ""
echo "📋 LOG FILES:"
echo "============="
echo "Performance Monitor: $PROJECT_DIR/logs/cron_performance.log"
echo "Performance Alerts:  $PROJECT_DIR/logs/cron_alerts.log"
echo "Retraining Check:    $PROJECT_DIR/logs/cron_retraining.log"
echo "Weekly Retraining:   $PROJECT_DIR/logs/cron_weekly_retraining.log"

echo ""
echo "🎯 MANAGEMENT COMMANDS:"
echo "======================"
echo "View current cron jobs:     crontab -l"
echo "Remove monitoring jobs:     $PROJECT_DIR/scripts/automation/remove_monitoring_cron.sh"
echo "Test performance monitor:   cd $PROJECT_DIR && $PYTHON_PATH scripts/monitoring/performance_monitor.py once"
echo "Test alerts:               cd $PROJECT_DIR && $PYTHON_PATH scripts/monitoring/performance_alerts.py test"
echo "Test retraining check:     cd $PROJECT_DIR && $PYTHON_PATH scripts/automation/automated_retraining.py check"

echo ""
echo "✅ MONITORING CRON JOBS SETUP COMPLETED!"
echo "The system will now automatically monitor performance and handle retraining."
