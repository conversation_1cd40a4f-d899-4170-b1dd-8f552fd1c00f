#!/usr/bin/env python3
"""
Data Pipeline Automation
Automated data normalization, validation, and maintenance
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import json
import psycopg2
from psycopg2.extras import RealDictCursor
import schedule
import time
import threading
from dataclasses import dataclass
from typing import Dict, List, Optional
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/automation/data_pipeline.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class AutomationConfig:
    """Configuration for automation tasks"""
    db_config: Dict = None
    update_frequency_minutes: int = 15
    validation_frequency_hours: int = 6
    backup_frequency_hours: int = 24
    outlier_detection_frequency_hours: int = 24
    drift_detection_frequency_hours: int = 12
    
    def __post_init__(self):
        if self.db_config is None:
            self.db_config = {
                'host': 'localhost',
                'database': 'solar_prediction',
                'user': 'postgres',
                'password': ''
            }

class DataPipelineAutomation:
    """Automated data pipeline management"""
    
    def __init__(self, config: AutomationConfig):
        self.config = config
        self.automation_active = False
        self.task_history = []
        self.alert_thresholds = {
            'missing_data_percent': 5.0,
            'outlier_percent': 10.0,
            'drift_score': 0.25,
            'validation_errors': 5
        }
        
        # Ensure log directory exists
        os.makedirs('logs/automation', exist_ok=True)
    
    def connect_database(self):
        """Connect to database"""
        try:
            conn = psycopg2.connect(**self.config.db_config)
            return conn
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            return None
    
    def update_normalized_tables(self):
        """Update normalized data tables"""
        
        logger.info("🔄 Starting normalized tables update...")
        
        task_start = datetime.now()
        
        try:
            conn = self.connect_database()
            if not conn:
                raise Exception("Database connection failed")
            
            cur = conn.cursor(cursor_factory=RealDictCursor)
            
            # Update total_yield for new records in solax_data2
            update_query = """
            UPDATE solax_data2 
            SET total_yield = CASE 
                WHEN yield_today < LAG(yield_today) OVER (ORDER BY timestamp) 
                THEN LAG(total_yield) OVER (ORDER BY timestamp) + yield_today
                ELSE COALESCE(LAG(total_yield) OVER (ORDER BY timestamp), 0) + 
                     (yield_today - COALESCE(LAG(yield_today) OVER (ORDER BY timestamp), 0))
            END
            WHERE total_yield IS NULL 
               OR timestamp >= NOW() - INTERVAL '1 hour'
            """
            
            cur.execute(update_query)
            updated_rows = cur.rowcount
            
            # Update ac_power estimates for new records
            power_update_query = """
            UPDATE solax_data2 
            SET ac_power = CASE 
                WHEN yield_today > LAG(yield_today) OVER (ORDER BY timestamp) 
                THEN ((yield_today - LAG(yield_today) OVER (ORDER BY timestamp)) * 1000 * 12)
                ELSE 0
            END
            WHERE ac_power IS NULL 
               OR timestamp >= NOW() - INTERVAL '1 hour'
            """
            
            cur.execute(power_update_query)
            power_updated_rows = cur.rowcount
            
            # Normalize weather data
            weather_normalize_query = """
            UPDATE weather_data 
            SET 
                ghi_normalized = global_horizontal_irradiance / 1000.0,
                temp_normalized = (temperature_2m - 0) / 40.0,
                cloud_normalized = cloud_cover / 100.0
            WHERE timestamp >= NOW() - INTERVAL '1 hour'
            """
            
            cur.execute(weather_normalize_query)
            weather_updated_rows = cur.rowcount
            
            conn.commit()
            conn.close()
            
            task_duration = (datetime.now() - task_start).total_seconds()
            
            result = {
                'task': 'update_normalized_tables',
                'timestamp': task_start.isoformat(),
                'duration_seconds': task_duration,
                'status': 'success',
                'metrics': {
                    'total_yield_updated': updated_rows,
                    'ac_power_updated': power_updated_rows,
                    'weather_normalized': weather_updated_rows
                }
            }
            
            self.task_history.append(result)
            
            logger.info(f"✅ Normalized tables updated: {updated_rows} total_yield, {power_updated_rows} ac_power, {weather_updated_rows} weather")
            
            return result
            
        except Exception as e:
            task_duration = (datetime.now() - task_start).total_seconds()
            
            result = {
                'task': 'update_normalized_tables',
                'timestamp': task_start.isoformat(),
                'duration_seconds': task_duration,
                'status': 'failed',
                'error': str(e)
            }
            
            self.task_history.append(result)
            logger.error(f"❌ Normalized tables update failed: {e}")
            
            return result
    
    def validate_data_quality(self):
        """Validate data quality and detect issues"""
        
        logger.info("🔍 Starting data quality validation...")
        
        task_start = datetime.now()
        
        try:
            conn = self.connect_database()
            if not conn:
                raise Exception("Database connection failed")
            
            cur = conn.cursor(cursor_factory=RealDictCursor)
            
            validation_results = {}
            
            # Check for missing data
            for table in ['solax_data', 'solax_data2', 'weather_data']:
                cur.execute(f"""
                    SELECT 
                        COUNT(*) as total_records,
                        COUNT(timestamp) as timestamp_count,
                        COUNT(*) - COUNT(timestamp) as missing_timestamps
                    FROM {table}
                    WHERE timestamp >= NOW() - INTERVAL '24 hours'
                """)
                
                result = cur.fetchone()
                missing_percent = (result['missing_timestamps'] / result['total_records']) * 100 if result['total_records'] > 0 else 0
                
                validation_results[table] = {
                    'total_records': result['total_records'],
                    'missing_timestamps': result['missing_timestamps'],
                    'missing_percent': missing_percent,
                    'quality_score': 100 - missing_percent
                }
            
            # Check for outliers in key columns
            outlier_checks = [
                ('solax_data', 'yield_today', 0, 100),
                ('solax_data', 'ac_power', 0, 15000),
                ('solax_data2', 'yield_today', 0, 100),
                ('weather_data', 'temperature_2m', -20, 50),
                ('weather_data', 'global_horizontal_irradiance', 0, 1200)
            ]
            
            outlier_results = {}
            
            for table, column, min_val, max_val in outlier_checks:
                cur.execute(f"""
                    SELECT 
                        COUNT(*) as total_records,
                        COUNT(CASE WHEN {column} < {min_val} OR {column} > {max_val} THEN 1 END) as outliers
                    FROM {table}
                    WHERE timestamp >= NOW() - INTERVAL '24 hours'
                      AND {column} IS NOT NULL
                """)
                
                result = cur.fetchone()
                outlier_percent = (result['outliers'] / result['total_records']) * 100 if result['total_records'] > 0 else 0
                
                outlier_results[f"{table}.{column}"] = {
                    'total_records': result['total_records'],
                    'outliers': result['outliers'],
                    'outlier_percent': outlier_percent
                }
            
            conn.close()
            
            # Generate alerts if thresholds exceeded
            alerts = []
            
            for table, metrics in validation_results.items():
                if metrics['missing_percent'] > self.alert_thresholds['missing_data_percent']:
                    alerts.append({
                        'type': 'data_quality',
                        'severity': 'warning',
                        'message': f"High missing data in {table}: {metrics['missing_percent']:.1f}%",
                        'table': table,
                        'metric': 'missing_data',
                        'value': metrics['missing_percent']
                    })
            
            for column, metrics in outlier_results.items():
                if metrics['outlier_percent'] > self.alert_thresholds['outlier_percent']:
                    alerts.append({
                        'type': 'data_quality',
                        'severity': 'warning',
                        'message': f"High outlier rate in {column}: {metrics['outlier_percent']:.1f}%",
                        'column': column,
                        'metric': 'outliers',
                        'value': metrics['outlier_percent']
                    })
            
            task_duration = (datetime.now() - task_start).total_seconds()
            
            result = {
                'task': 'validate_data_quality',
                'timestamp': task_start.isoformat(),
                'duration_seconds': task_duration,
                'status': 'success',
                'validation_results': validation_results,
                'outlier_results': outlier_results,
                'alerts': alerts,
                'overall_quality_score': np.mean([v['quality_score'] for v in validation_results.values()])
            }
            
            self.task_history.append(result)
            
            if alerts:
                logger.warning(f"⚠️ Data quality issues detected: {len(alerts)} alerts")
                for alert in alerts:
                    logger.warning(f"   {alert['message']}")
            else:
                logger.info(f"✅ Data quality validation passed: {result['overall_quality_score']:.1f}% quality score")
            
            return result
            
        except Exception as e:
            task_duration = (datetime.now() - task_start).total_seconds()
            
            result = {
                'task': 'validate_data_quality',
                'timestamp': task_start.isoformat(),
                'duration_seconds': task_duration,
                'status': 'failed',
                'error': str(e)
            }
            
            self.task_history.append(result)
            logger.error(f"❌ Data quality validation failed: {e}")
            
            return result
    
    def detect_data_drift(self):
        """Detect data drift in recent data"""
        
        logger.info("📊 Starting data drift detection...")
        
        task_start = datetime.now()
        
        try:
            conn = self.connect_database()
            if not conn:
                raise Exception("Database connection failed")
            
            cur = conn.cursor(cursor_factory=RealDictCursor)
            
            # Compare recent data (last 24h) with historical baseline (last 30 days)
            drift_results = {}
            
            drift_checks = [
                ('solax_data', 'yield_today'),
                ('solax_data', 'ac_power'),
                ('weather_data', 'temperature_2m'),
                ('weather_data', 'global_horizontal_irradiance')
            ]
            
            for table, column in drift_checks:
                # Get recent statistics
                cur.execute(f"""
                    SELECT 
                        AVG({column}) as recent_mean,
                        STDDEV({column}) as recent_std
                    FROM {table}
                    WHERE timestamp >= NOW() - INTERVAL '24 hours'
                      AND {column} IS NOT NULL
                """)
                
                recent_stats = cur.fetchone()
                
                # Get historical baseline
                cur.execute(f"""
                    SELECT 
                        AVG({column}) as baseline_mean,
                        STDDEV({column}) as baseline_std
                    FROM {table}
                    WHERE timestamp >= NOW() - INTERVAL '30 days'
                      AND timestamp < NOW() - INTERVAL '24 hours'
                      AND {column} IS NOT NULL
                """)
                
                baseline_stats = cur.fetchone()
                
                if recent_stats and baseline_stats and all(v is not None for v in [recent_stats['recent_mean'], baseline_stats['baseline_mean']]):
                    # Calculate drift score (normalized difference)
                    mean_diff = abs(recent_stats['recent_mean'] - baseline_stats['baseline_mean'])
                    baseline_std = baseline_stats['baseline_std'] or 1.0
                    drift_score = mean_diff / baseline_std
                    
                    drift_results[f"{table}.{column}"] = {
                        'recent_mean': recent_stats['recent_mean'],
                        'baseline_mean': baseline_stats['baseline_mean'],
                        'drift_score': drift_score,
                        'drift_detected': drift_score > self.alert_thresholds['drift_score']
                    }
            
            conn.close()
            
            # Generate drift alerts
            drift_alerts = []
            
            for column, metrics in drift_results.items():
                if metrics['drift_detected']:
                    drift_alerts.append({
                        'type': 'data_drift',
                        'severity': 'critical',
                        'message': f"Data drift detected in {column}: score {metrics['drift_score']:.3f}",
                        'column': column,
                        'drift_score': metrics['drift_score'],
                        'threshold': self.alert_thresholds['drift_score']
                    })
            
            task_duration = (datetime.now() - task_start).total_seconds()
            
            result = {
                'task': 'detect_data_drift',
                'timestamp': task_start.isoformat(),
                'duration_seconds': task_duration,
                'status': 'success',
                'drift_results': drift_results,
                'drift_alerts': drift_alerts,
                'overall_drift_score': np.mean([v['drift_score'] for v in drift_results.values()]) if drift_results else 0
            }
            
            self.task_history.append(result)
            
            if drift_alerts:
                logger.warning(f"🚨 Data drift detected: {len(drift_alerts)} alerts")
                for alert in drift_alerts:
                    logger.warning(f"   {alert['message']}")
            else:
                logger.info(f"✅ No significant data drift detected: {result['overall_drift_score']:.3f} average score")
            
            return result
            
        except Exception as e:
            task_duration = (datetime.now() - task_start).total_seconds()
            
            result = {
                'task': 'detect_data_drift',
                'timestamp': task_start.isoformat(),
                'duration_seconds': task_duration,
                'status': 'failed',
                'error': str(e)
            }
            
            self.task_history.append(result)
            logger.error(f"❌ Data drift detection failed: {e}")
            
            return result
    
    def create_database_backup(self):
        """Create automated database backup"""
        
        logger.info("💾 Starting database backup...")
        
        task_start = datetime.now()
        
        try:
            # Create backup directory
            backup_dir = 'backups/automated'
            os.makedirs(backup_dir, exist_ok=True)
            
            # Generate backup filename
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_file = f"{backup_dir}/solar_prediction_auto_{timestamp}.sql"
            
            # Create backup using pg_dump
            import subprocess
            
            cmd = [
                'pg_dump',
                '-h', self.config.db_config['host'],
                '-U', self.config.db_config['user'],
                '-d', self.config.db_config['database'],
                '-f', backup_file,
                '--verbose'
            ]
            
            # Set password environment variable
            env = os.environ.copy()
            env['PGPASSWORD'] = self.config.db_config['password']
            
            result_process = subprocess.run(cmd, env=env, capture_output=True, text=True)
            
            if result_process.returncode == 0:
                # Get backup file size
                backup_size = os.path.getsize(backup_file)
                
                task_duration = (datetime.now() - task_start).total_seconds()
                
                result = {
                    'task': 'create_database_backup',
                    'timestamp': task_start.isoformat(),
                    'duration_seconds': task_duration,
                    'status': 'success',
                    'backup_file': backup_file,
                    'backup_size_mb': backup_size / (1024 * 1024),
                    'backup_timestamp': timestamp
                }
                
                self.task_history.append(result)
                
                logger.info(f"✅ Database backup created: {backup_file} ({backup_size / (1024 * 1024):.1f} MB)")
                
                return result
            else:
                raise Exception(f"pg_dump failed: {result_process.stderr}")
                
        except Exception as e:
            task_duration = (datetime.now() - task_start).total_seconds()
            
            result = {
                'task': 'create_database_backup',
                'timestamp': task_start.isoformat(),
                'duration_seconds': task_duration,
                'status': 'failed',
                'error': str(e)
            }
            
            self.task_history.append(result)
            logger.error(f"❌ Database backup failed: {e}")
            
            return result
    
    def schedule_automation_tasks(self):
        """Schedule all automation tasks"""
        
        logger.info("📅 Scheduling automation tasks...")
        
        # Schedule data updates every 15 minutes
        schedule.every(self.config.update_frequency_minutes).minutes.do(self.update_normalized_tables)
        
        # Schedule validation every 6 hours
        schedule.every(self.config.validation_frequency_hours).hours.do(self.validate_data_quality)
        
        # Schedule drift detection every 12 hours
        schedule.every(self.config.drift_detection_frequency_hours).hours.do(self.detect_data_drift)
        
        # Schedule backup every 24 hours
        schedule.every(self.config.backup_frequency_hours).hours.do(self.create_database_backup)
        
        logger.info("✅ Automation tasks scheduled:")
        logger.info(f"   🔄 Data updates: every {self.config.update_frequency_minutes} minutes")
        logger.info(f"   🔍 Data validation: every {self.config.validation_frequency_hours} hours")
        logger.info(f"   📊 Drift detection: every {self.config.drift_detection_frequency_hours} hours")
        logger.info(f"   💾 Database backup: every {self.config.backup_frequency_hours} hours")
    
    def run_automation_loop(self):
        """Run the automation loop"""
        
        logger.info("🚀 Starting data pipeline automation...")
        
        self.automation_active = True
        
        # Schedule tasks
        self.schedule_automation_tasks()
        
        # Run initial tasks
        logger.info("🔄 Running initial automation tasks...")
        self.update_normalized_tables()
        self.validate_data_quality()
        
        # Main automation loop
        try:
            while self.automation_active:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
                
        except KeyboardInterrupt:
            logger.info("⏹️ Automation stopped by user")
            self.automation_active = False
        except Exception as e:
            logger.error(f"❌ Automation loop failed: {e}")
            self.automation_active = False
    
    def stop_automation(self):
        """Stop automation loop"""
        self.automation_active = False
        logger.info("⏹️ Automation stopping...")
    
    def get_automation_status(self):
        """Get current automation status"""
        
        recent_tasks = [task for task in self.task_history if 
                       datetime.fromisoformat(task['timestamp']) > datetime.now() - timedelta(hours=24)]
        
        status = {
            'automation_active': self.automation_active,
            'total_tasks_24h': len(recent_tasks),
            'successful_tasks_24h': len([t for t in recent_tasks if t['status'] == 'success']),
            'failed_tasks_24h': len([t for t in recent_tasks if t['status'] == 'failed']),
            'last_update': recent_tasks[-1]['timestamp'] if recent_tasks else None,
            'scheduled_tasks': len(schedule.jobs),
            'next_run_times': {job.job_func.__name__: str(job.next_run) for job in schedule.jobs}
        }
        
        return status

def main():
    """Main automation function"""
    
    print("🤖 DATA PIPELINE AUTOMATION")
    print("="*60)
    print("🔄 Automated data normalization and validation")
    print("📊 Real-time quality monitoring and drift detection")
    print()
    
    try:
        # Initialize automation
        config = AutomationConfig()
        automation = DataPipelineAutomation(config)
        
        # Run automation loop
        automation.run_automation_loop()
        
        return True
        
    except Exception as e:
        print(f"❌ Automation failed: {e}")
        logger.exception("Data pipeline automation failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
