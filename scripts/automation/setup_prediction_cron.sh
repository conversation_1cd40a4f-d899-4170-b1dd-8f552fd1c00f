#!/bin/bash
# SETUP PREDICTION CRON JOBS
# Setup automated prediction caching cron jobs
# Created: June 4, 2025

echo "🔄 SETTING UP PREDICTION CRON JOBS"
echo "=================================="

# Get project directory
PROJECT_DIR="/home/<USER>/solar-prediction-project"
PYTHON_PATH="$PROJECT_DIR/venv/bin/python3"

# Check if project directory exists
if [ ! -d "$PROJECT_DIR" ]; then
    echo "❌ Project directory not found: $PROJECT_DIR"
    exit 1
fi

# Check if virtual environment exists
if [ ! -f "$PYTHON_PATH" ]; then
    echo "❌ Python virtual environment not found: $PYTHON_PATH"
    echo "   Please create virtual environment first"
    exit 1
fi

echo "✅ Project directory: $PROJECT_DIR"
echo "✅ Python path: $PYTHON_PATH"

# Create logs directory
mkdir -p "$PROJECT_DIR/logs"

# Create cron jobs
echo ""
echo "📋 Creating cron job entries..."

# Temporary cron file
TEMP_CRON="/tmp/prediction_cron"

# Get existing cron jobs (excluding our prediction jobs)
crontab -l 2>/dev/null | grep -v "# Prediction Cache" | grep -v "predictions_cache_manager.py" > "$TEMP_CRON"

# Add our prediction cron jobs
cat >> "$TEMP_CRON" << EOF

# Prediction Cache - Hourly Cache Update (every 15 minutes)
*/15 * * * * cd $PROJECT_DIR && $PYTHON_PATH -c "
import sys
sys.path.append('.')
from scripts.production.predictions_cache_manager import PredictionsCacheManager
cache_manager = PredictionsCacheManager()
cache_manager.update_hourly_cache()
" >> logs/cron_hourly_cache.log 2>&1

# Prediction Cache - Daily Cache Update (every hour)
0 * * * * cd $PROJECT_DIR && $PYTHON_PATH -c "
import sys
sys.path.append('.')
from scripts.production.predictions_cache_manager import PredictionsCacheManager
cache_manager = PredictionsCacheManager()
cache_manager.update_daily_cache()
" >> logs/cron_daily_cache.log 2>&1

# Prediction Cache - Cleanup Old Predictions (daily at 3 AM)
0 3 * * * cd $PROJECT_DIR && $PYTHON_PATH -c "
import sys
sys.path.append('.')
from scripts.production.predictions_cache_manager import PredictionsCacheManager
cache_manager = PredictionsCacheManager()
cache_manager.cleanup_old_cache(days_to_keep=7)
" >> logs/cron_cleanup.log 2>&1

EOF

# Install the new cron jobs
crontab "$TEMP_CRON"

if [ $? -eq 0 ]; then
    echo "✅ Cron jobs installed successfully"
else
    echo "❌ Failed to install cron jobs"
    exit 1
fi

# Clean up
rm "$TEMP_CRON"

echo ""
echo "📊 INSTALLED CRON JOBS:"
echo "======================"
echo "🔄 Hourly Cache Update:  Every 15 minutes"
echo "📅 Daily Cache Update:   Every hour"
echo "🧹 Cache Cleanup:        Daily at 3 AM"

echo ""
echo "📋 LOG FILES:"
echo "============="
echo "Hourly Cache:  $PROJECT_DIR/logs/cron_hourly_cache.log"
echo "Daily Cache:   $PROJECT_DIR/logs/cron_daily_cache.log"
echo "Cache Cleanup: $PROJECT_DIR/logs/cron_cleanup.log"

echo ""
echo "🎯 MANAGEMENT COMMANDS:"
echo "======================"
echo "View current cron jobs:     crontab -l"
echo "Remove prediction jobs:     $PROJECT_DIR/scripts/automation/remove_prediction_cron.sh"
echo "Test hourly cache:          cd $PROJECT_DIR && $PYTHON_PATH -c 'from scripts.production.predictions_cache_manager import PredictionsCacheManager; PredictionsCacheManager().update_hourly_cache()'"
echo "Test daily cache:           cd $PROJECT_DIR && $PYTHON_PATH -c 'from scripts.production.predictions_cache_manager import PredictionsCacheManager; PredictionsCacheManager().update_daily_cache()'"
echo "View cache stats:           cd $PROJECT_DIR && $PYTHON_PATH -c 'from scripts.production.predictions_cache_manager import PredictionsCacheManager; import json; print(json.dumps(PredictionsCacheManager().get_cache_statistics(), indent=2))'"

echo ""
echo "✅ PREDICTION CRON JOBS SETUP COMPLETED!"
echo "The system will now automatically cache predictions every 15 minutes."
