#!/usr/bin/env python3
"""
Feature Pipeline Automation
Automated feature engineering and real-time feature computation
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import json
import psycopg2
from psycopg2.extras import RealDictCursor
import schedule
import time
import threading
from dataclasses import dataclass
from typing import Dict, List, Optional
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/automation/feature_pipeline.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class FeaturePipelineConfig:
    """Configuration for feature pipeline automation"""
    db_config: Dict = None
    feature_update_frequency_minutes: int = 15
    batch_size: int = 1000
    lookback_hours: int = 24
    feature_validation_frequency_hours: int = 6
    
    def __post_init__(self):
        if self.db_config is None:
            self.db_config = {
                'host': 'localhost',
                'database': 'solar_prediction',
                'user': 'postgres',
                'password': ''
            }

class FeaturePipelineAutomation:
    """Automated feature engineering pipeline"""
    
    def __init__(self, config: FeaturePipelineConfig):
        self.config = config
        self.automation_active = False
        self.feature_history = []
        
        # Feature definitions
        self.feature_definitions = {
            'temporal': [
                'hour', 'day_of_year', 'month', 'weekday',
                'hour_sin', 'hour_cos', 'day_of_year_sin', 'day_of_year_cos',
                'is_summer', 'is_winter', 'is_daytime', 'is_peak_sun'
            ],
            'solar': [
                'solar_elevation_approx', 'solar_elevation_normalized',
                'time_from_solar_noon', 'day_length_approx'
            ],
            'weather': [
                'ghi_normalized', 'cloud_cover_normalized', 'temp_normalized',
                'clear_sky_factor', 'temp_optimality', 'weather_quality'
            ],
            'system': [
                'battery_soc_normalized', 'ac_power_normalized',
                'battery_charging', 'battery_discharging', 'system_efficiency',
                'is_system_1', 'is_system_2'
            ],
            'interactions': [
                'solar_weather_score', 'optimal_conditions',
                'battery_solar_potential', 'peak_sun_weather'
            ]
        }
        
        # Ensure log directory exists
        os.makedirs('logs/automation', exist_ok=True)
    
    def connect_database(self):
        """Connect to database"""
        try:
            conn = psycopg2.connect(**self.config.db_config)
            return conn
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            return None
    
    def create_enhanced_features_table(self):
        """Create or update enhanced features table"""
        
        logger.info("🔧 Creating/updating enhanced features table...")
        
        try:
            conn = self.connect_database()
            if not conn:
                raise Exception("Database connection failed")
            
            cur = conn.cursor()
            
            # Create enhanced features table with all computed features
            create_table_query = """
            CREATE TABLE IF NOT EXISTS enhanced_features_realtime (
                id SERIAL PRIMARY KEY,
                timestamp TIMESTAMP NOT NULL,
                system_id VARCHAR(20) NOT NULL,
                
                -- Original data
                yield_today DOUBLE PRECISION,
                total_yield DOUBLE PRECISION,
                ac_power DOUBLE PRECISION,
                battery_soc DOUBLE PRECISION,
                battery_power DOUBLE PRECISION,
                ambient_temperature DOUBLE PRECISION,
                cloud_cover DOUBLE PRECISION,
                ghi DOUBLE PRECISION,
                
                -- Temporal features
                hour INTEGER,
                day_of_year INTEGER,
                month INTEGER,
                weekday INTEGER,
                hour_sin DOUBLE PRECISION,
                hour_cos DOUBLE PRECISION,
                day_of_year_sin DOUBLE PRECISION,
                day_of_year_cos DOUBLE PRECISION,
                is_summer INTEGER,
                is_winter INTEGER,
                is_spring INTEGER,
                is_autumn INTEGER,
                is_daytime INTEGER,
                is_peak_sun INTEGER,
                is_weekend INTEGER,
                
                -- Solar features
                solar_elevation_approx DOUBLE PRECISION,
                solar_elevation_normalized DOUBLE PRECISION,
                time_from_solar_noon DOUBLE PRECISION,
                day_length_approx DOUBLE PRECISION,
                
                -- Weather features
                ghi_normalized DOUBLE PRECISION,
                cloud_cover_normalized DOUBLE PRECISION,
                temp_normalized DOUBLE PRECISION,
                clear_sky_factor DOUBLE PRECISION,
                temp_optimality DOUBLE PRECISION,
                weather_quality DOUBLE PRECISION,
                
                -- System features
                battery_soc_normalized DOUBLE PRECISION,
                ac_power_normalized DOUBLE PRECISION,
                battery_power_normalized DOUBLE PRECISION,
                battery_charging INTEGER,
                battery_discharging INTEGER,
                system_efficiency DOUBLE PRECISION,
                is_system_1 INTEGER,
                is_system_2 INTEGER,
                
                -- Lag features
                yield_lag_1h DOUBLE PRECISION,
                ac_power_lag_1h DOUBLE PRECISION,
                battery_soc_lag_1h DOUBLE PRECISION,
                ghi_lag_1h DOUBLE PRECISION,
                
                -- Rolling features
                yield_rolling_30min DOUBLE PRECISION,
                ac_power_rolling_30min DOUBLE PRECISION,
                ghi_rolling_30min DOUBLE PRECISION,
                
                -- Interaction features
                solar_weather_score DOUBLE PRECISION,
                optimal_conditions DOUBLE PRECISION,
                battery_solar_potential DOUBLE PRECISION,
                peak_sun_weather DOUBLE PRECISION,
                summer_efficiency DOUBLE PRECISION,
                
                -- Metadata
                feature_version VARCHAR(10) DEFAULT 'v3.0',
                created_at TIMESTAMP DEFAULT NOW(),
                
                UNIQUE(timestamp, system_id)
            );
            
            CREATE INDEX IF NOT EXISTS idx_enhanced_features_timestamp 
            ON enhanced_features_realtime(timestamp);
            
            CREATE INDEX IF NOT EXISTS idx_enhanced_features_system 
            ON enhanced_features_realtime(system_id);
            
            CREATE INDEX IF NOT EXISTS idx_enhanced_features_timestamp_system 
            ON enhanced_features_realtime(timestamp, system_id);
            """
            
            cur.execute(create_table_query)
            conn.commit()
            conn.close()
            
            logger.info("✅ Enhanced features table ready")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to create enhanced features table: {e}")
            return False
    
    def compute_realtime_features(self, lookback_minutes=60):
        """Compute features for recent data"""
        
        logger.info(f"🔬 Computing real-time features for last {lookback_minutes} minutes...")
        
        task_start = datetime.now()
        
        try:
            conn = self.connect_database()
            if not conn:
                raise Exception("Database connection failed")
            
            cur = conn.cursor(cursor_factory=RealDictCursor)
            
            # Get recent data that needs feature computation
            data_query = f"""
            WITH recent_data AS (
                SELECT 
                    s.timestamp,
                    s.system_id,
                    s.yield_today,
                    s.total_yield,
                    s.ac_power,
                    s.battery_soc,
                    s.battery_power,
                    w.temperature_2m as ambient_temperature,
                    w.cloud_cover,
                    w.global_horizontal_irradiance as ghi
                FROM (
                    SELECT timestamp, 'system_1' as system_id, yield_today, 
                           yield_total as total_yield, ac_power, soc as battery_soc, 
                           bat_power as battery_power
                    FROM solax_data 
                    WHERE timestamp >= NOW() - INTERVAL '{lookback_minutes} minutes'
                    
                    UNION ALL
                    
                    SELECT timestamp, 'system_2' as system_id, yield_today, 
                           total_yield, ac_power, soc as battery_soc, 
                           bat_power as battery_power
                    FROM solax_data2 
                    WHERE timestamp >= NOW() - INTERVAL '{lookback_minutes} minutes'
                ) s
                LEFT JOIN weather_data w ON 
                    DATE_TRUNC('hour', s.timestamp) = DATE_TRUNC('hour', w.timestamp)
                WHERE NOT EXISTS (
                    SELECT 1 FROM enhanced_features_realtime ef 
                    WHERE ef.timestamp = s.timestamp AND ef.system_id = s.system_id
                )
            )
            SELECT * FROM recent_data ORDER BY timestamp, system_id
            """
            
            cur.execute(data_query)
            raw_data = cur.fetchall()
            
            if not raw_data:
                logger.info("ℹ️ No new data to process")
                return {'status': 'no_new_data', 'records_processed': 0}
            
            logger.info(f"📊 Processing {len(raw_data)} records...")
            
            # Convert to DataFrame for easier processing
            df = pd.DataFrame(raw_data)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            
            # Compute features
            enhanced_df = self.compute_all_features(df)
            
            # Insert enhanced features back to database
            records_inserted = self.insert_enhanced_features(enhanced_df)
            
            conn.close()
            
            task_duration = (datetime.now() - task_start).total_seconds()
            
            result = {
                'task': 'compute_realtime_features',
                'timestamp': task_start.isoformat(),
                'duration_seconds': task_duration,
                'status': 'success',
                'records_processed': len(raw_data),
                'records_inserted': records_inserted,
                'feature_count': len(enhanced_df.columns) - 3  # Exclude timestamp, system_id, id
            }
            
            self.feature_history.append(result)
            
            logger.info(f"✅ Features computed: {records_inserted} records with {result['feature_count']} features")
            
            return result
            
        except Exception as e:
            task_duration = (datetime.now() - task_start).total_seconds()
            
            result = {
                'task': 'compute_realtime_features',
                'timestamp': task_start.isoformat(),
                'duration_seconds': task_duration,
                'status': 'failed',
                'error': str(e)
            }
            
            self.feature_history.append(result)
            logger.error(f"❌ Feature computation failed: {e}")
            
            return result
    
    def compute_all_features(self, df):
        """Compute all feature categories"""
        
        # Make a copy to avoid modifying original
        enhanced_df = df.copy()
        
        # Temporal features
        enhanced_df = self.add_temporal_features(enhanced_df)
        
        # Solar features
        enhanced_df = self.add_solar_features(enhanced_df)
        
        # Weather features
        enhanced_df = self.add_weather_features(enhanced_df)
        
        # System features
        enhanced_df = self.add_system_features(enhanced_df)
        
        # Lag and rolling features
        enhanced_df = self.add_lag_rolling_features(enhanced_df)
        
        # Interaction features
        enhanced_df = self.add_interaction_features(enhanced_df)
        
        return enhanced_df
    
    def add_temporal_features(self, df):
        """Add temporal features"""
        
        df['hour'] = df['timestamp'].dt.hour
        df['day_of_year'] = df['timestamp'].dt.dayofyear
        df['month'] = df['timestamp'].dt.month
        df['weekday'] = df['timestamp'].dt.weekday
        
        # Cyclical encoding
        df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
        df['day_of_year_sin'] = np.sin(2 * np.pi * df['day_of_year'] / 365)
        df['day_of_year_cos'] = np.cos(2 * np.pi * df['day_of_year'] / 365)
        
        # Season indicators
        df['is_summer'] = ((df['month'] >= 6) & (df['month'] <= 8)).astype(int)
        df['is_winter'] = ((df['month'] >= 12) | (df['month'] <= 2)).astype(int)
        df['is_spring'] = ((df['month'] >= 3) & (df['month'] <= 5)).astype(int)
        df['is_autumn'] = ((df['month'] >= 9) & (df['month'] <= 11)).astype(int)
        
        # Time of day
        df['is_daytime'] = ((df['hour'] >= 6) & (df['hour'] <= 18)).astype(int)
        df['is_peak_sun'] = ((df['hour'] >= 10) & (df['hour'] <= 14)).astype(int)
        df['is_weekend'] = (df['weekday'] >= 5).astype(int)
        
        return df
    
    def add_solar_features(self, df):
        """Add solar geometry features"""
        
        # Simple solar elevation approximation
        df['solar_elevation_approx'] = np.where(
            df['is_daytime'] == 1,
            60 * np.sin(np.pi * (df['hour'] - 6) / 12),
            0
        )
        df['solar_elevation_approx'] = np.clip(df['solar_elevation_approx'], 0, 90)
        
        # Normalize solar elevation
        df['solar_elevation_normalized'] = df['solar_elevation_approx'] / 90
        
        # Distance from solar noon
        df['time_from_solar_noon'] = np.abs(df['hour'] - 12)
        
        # Day length approximation
        df['day_length_approx'] = 12 + 2 * np.sin(2 * np.pi * (df['day_of_year'] - 80) / 365)
        
        return df
    
    def add_weather_features(self, df):
        """Add weather features"""
        
        # Normalize weather features
        df['ghi_normalized'] = np.where(df['ghi'].notna(), df['ghi'] / 1000, 0)
        df['cloud_cover_normalized'] = np.where(df['cloud_cover'].notna(), df['cloud_cover'] / 100, 0)
        df['temp_normalized'] = np.where(df['ambient_temperature'].notna(), 
                                        (df['ambient_temperature'] - 0) / 40, 0.5)
        
        # Clear sky factor
        df['clear_sky_factor'] = 1 - df['cloud_cover_normalized']
        
        # Temperature optimality
        df['temp_optimality'] = np.where(df['ambient_temperature'].notna(),
                                        1 - np.abs(df['ambient_temperature'] - 25) / 25, 0.5)
        df['temp_optimality'] = np.clip(df['temp_optimality'], 0, 1)
        
        # Weather quality score
        df['weather_quality'] = (df['clear_sky_factor'] + df['temp_optimality']) / 2
        
        return df
    
    def add_system_features(self, df):
        """Add system features"""
        
        # Normalize system features
        df['battery_soc_normalized'] = df['battery_soc'] / 100
        df['ac_power_normalized'] = df['ac_power'] / 10500
        df['battery_power_normalized'] = np.where(df['battery_power'].notna(), 
                                                 df['battery_power'] / 12000, 0)
        
        # Battery state
        df['battery_charging'] = (df['battery_power'] > 100).astype(int)
        df['battery_discharging'] = (df['battery_power'] < -100).astype(int)
        
        # System efficiency
        df['system_efficiency'] = np.where(
            (df['ghi'] > 100) & (df['ac_power'] > 0),
            df['ac_power'] / (df['ghi'] * 10.5),
            0
        )
        df['system_efficiency'] = np.clip(df['system_efficiency'], 0, 1.5)
        
        # System ID encoding
        df['is_system_1'] = (df['system_id'] == 'system_1').astype(int)
        df['is_system_2'] = (df['system_id'] == 'system_2').astype(int)
        
        return df
    
    def add_lag_rolling_features(self, df):
        """Add lag and rolling features"""
        
        # Sort by system and timestamp
        df = df.sort_values(['system_id', 'timestamp'])
        
        # Add lag features for each system
        for system_id in df['system_id'].unique():
            mask = df['system_id'] == system_id
            
            # 1-hour lag (12 periods for 5-min data)
            df.loc[mask, 'yield_lag_1h'] = df.loc[mask, 'yield_today'].shift(12)
            df.loc[mask, 'ac_power_lag_1h'] = df.loc[mask, 'ac_power'].shift(12)
            df.loc[mask, 'battery_soc_lag_1h'] = df.loc[mask, 'battery_soc'].shift(12)
            df.loc[mask, 'ghi_lag_1h'] = df.loc[mask, 'ghi'].shift(12)
            
            # 30-minute rolling averages (6 periods)
            df.loc[mask, 'yield_rolling_30min'] = df.loc[mask, 'yield_today'].rolling(6, min_periods=1).mean()
            df.loc[mask, 'ac_power_rolling_30min'] = df.loc[mask, 'ac_power'].rolling(6, min_periods=1).mean()
            df.loc[mask, 'ghi_rolling_30min'] = df.loc[mask, 'ghi'].rolling(6, min_periods=1).mean()
        
        return df
    
    def add_interaction_features(self, df):
        """Add interaction features"""
        
        # Solar-weather interactions
        df['solar_weather_score'] = df['solar_elevation_normalized'] * df['weather_quality']
        df['optimal_conditions'] = (df['solar_elevation_normalized'] * 
                                   df['clear_sky_factor'] * 
                                   df['temp_optimality'])
        
        # Battery-solar interactions
        df['battery_solar_potential'] = ((1 - df['battery_soc_normalized']) * 
                                        df['solar_elevation_normalized'])
        
        # Time-based interactions
        df['peak_sun_weather'] = df['is_peak_sun'] * df['weather_quality']
        df['summer_efficiency'] = df['is_summer'] * df['system_efficiency']
        
        return df
    
    def insert_enhanced_features(self, df):
        """Insert enhanced features into database"""
        
        try:
            conn = self.connect_database()
            if not conn:
                raise Exception("Database connection failed")
            
            cur = conn.cursor()
            
            # Prepare insert query
            columns = [
                'timestamp', 'system_id', 'yield_today', 'total_yield', 'ac_power',
                'battery_soc', 'battery_power', 'ambient_temperature', 'cloud_cover', 'ghi',
                'hour', 'day_of_year', 'month', 'weekday', 'hour_sin', 'hour_cos',
                'day_of_year_sin', 'day_of_year_cos', 'is_summer', 'is_winter',
                'is_spring', 'is_autumn', 'is_daytime', 'is_peak_sun', 'is_weekend',
                'solar_elevation_approx', 'solar_elevation_normalized', 'time_from_solar_noon',
                'day_length_approx', 'ghi_normalized', 'cloud_cover_normalized',
                'temp_normalized', 'clear_sky_factor', 'temp_optimality', 'weather_quality',
                'battery_soc_normalized', 'ac_power_normalized', 'battery_power_normalized',
                'battery_charging', 'battery_discharging', 'system_efficiency',
                'is_system_1', 'is_system_2', 'yield_lag_1h', 'ac_power_lag_1h',
                'battery_soc_lag_1h', 'ghi_lag_1h', 'yield_rolling_30min',
                'ac_power_rolling_30min', 'ghi_rolling_30min', 'solar_weather_score',
                'optimal_conditions', 'battery_solar_potential', 'peak_sun_weather',
                'summer_efficiency'
            ]
            
            # Filter DataFrame to only include available columns
            available_columns = [col for col in columns if col in df.columns]
            insert_df = df[available_columns].copy()
            
            # Replace NaN with None for database insertion
            insert_df = insert_df.where(pd.notna(insert_df), None)
            
            # Create insert query
            placeholders = ', '.join(['%s'] * len(available_columns))
            insert_query = f"""
            INSERT INTO enhanced_features_realtime ({', '.join(available_columns)})
            VALUES ({placeholders})
            ON CONFLICT (timestamp, system_id) DO UPDATE SET
            {', '.join([f'{col} = EXCLUDED.{col}' for col in available_columns if col not in ['timestamp', 'system_id']])}
            """
            
            # Insert data
            records_inserted = 0
            for _, row in insert_df.iterrows():
                try:
                    cur.execute(insert_query, tuple(row[available_columns]))
                    records_inserted += 1
                except Exception as e:
                    logger.warning(f"⚠️ Failed to insert record for {row['timestamp']}: {e}")
            
            conn.commit()
            conn.close()
            
            return records_inserted
            
        except Exception as e:
            logger.error(f"❌ Failed to insert enhanced features: {e}")
            return 0
    
    def validate_feature_quality(self):
        """Validate feature quality and completeness"""
        
        logger.info("🔍 Validating feature quality...")
        
        try:
            conn = self.connect_database()
            if not conn:
                raise Exception("Database connection failed")
            
            cur = conn.cursor(cursor_factory=RealDictCursor)
            
            # Check feature completeness for recent data
            validation_query = """
            SELECT 
                COUNT(*) as total_records,
                COUNT(solar_elevation_normalized) as solar_features,
                COUNT(weather_quality) as weather_features,
                COUNT(battery_soc_normalized) as system_features,
                COUNT(yield_lag_1h) as lag_features,
                COUNT(solar_weather_score) as interaction_features,
                AVG(CASE WHEN solar_elevation_normalized IS NOT NULL THEN 1 ELSE 0 END) * 100 as completeness_percent
            FROM enhanced_features_realtime
            WHERE timestamp >= NOW() - INTERVAL '24 hours'
            """
            
            cur.execute(validation_query)
            validation_result = cur.fetchone()
            
            conn.close()
            
            # Calculate feature quality score
            total_records = validation_result['total_records']
            if total_records > 0:
                feature_scores = {
                    'solar_features': (validation_result['solar_features'] / total_records) * 100,
                    'weather_features': (validation_result['weather_features'] / total_records) * 100,
                    'system_features': (validation_result['system_features'] / total_records) * 100,
                    'lag_features': (validation_result['lag_features'] / total_records) * 100,
                    'interaction_features': (validation_result['interaction_features'] / total_records) * 100
                }
                
                overall_quality = np.mean(list(feature_scores.values()))
            else:
                feature_scores = {}
                overall_quality = 0
            
            result = {
                'task': 'validate_feature_quality',
                'timestamp': datetime.now().isoformat(),
                'status': 'success',
                'total_records': total_records,
                'feature_scores': feature_scores,
                'overall_quality': overall_quality,
                'completeness_percent': validation_result['completeness_percent']
            }
            
            logger.info(f"✅ Feature quality validation: {overall_quality:.1f}% overall quality")
            
            return result
            
        except Exception as e:
            result = {
                'task': 'validate_feature_quality',
                'timestamp': datetime.now().isoformat(),
                'status': 'failed',
                'error': str(e)
            }
            
            logger.error(f"❌ Feature quality validation failed: {e}")
            return result
    
    def schedule_feature_tasks(self):
        """Schedule feature pipeline tasks"""
        
        logger.info("📅 Scheduling feature pipeline tasks...")
        
        # Schedule feature computation every 15 minutes
        schedule.every(self.config.feature_update_frequency_minutes).minutes.do(
            self.compute_realtime_features
        )
        
        # Schedule feature validation every 6 hours
        schedule.every(self.config.feature_validation_frequency_hours).hours.do(
            self.validate_feature_quality
        )
        
        logger.info("✅ Feature pipeline tasks scheduled:")
        logger.info(f"   🔬 Feature computation: every {self.config.feature_update_frequency_minutes} minutes")
        logger.info(f"   🔍 Feature validation: every {self.config.feature_validation_frequency_hours} hours")
    
    def run_feature_automation(self):
        """Run feature pipeline automation"""
        
        logger.info("🚀 Starting feature pipeline automation...")
        
        self.automation_active = True
        
        # Create enhanced features table
        self.create_enhanced_features_table()
        
        # Schedule tasks
        self.schedule_feature_tasks()
        
        # Run initial feature computation
        logger.info("🔄 Running initial feature computation...")
        self.compute_realtime_features(lookback_minutes=120)  # Process last 2 hours
        
        # Main automation loop
        try:
            while self.automation_active:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
                
        except KeyboardInterrupt:
            logger.info("⏹️ Feature automation stopped by user")
            self.automation_active = False
        except Exception as e:
            logger.error(f"❌ Feature automation loop failed: {e}")
            self.automation_active = False
    
    def stop_automation(self):
        """Stop feature automation"""
        self.automation_active = False
        logger.info("⏹️ Feature automation stopping...")

def main():
    """Main feature automation function"""
    
    print("🔬 FEATURE PIPELINE AUTOMATION")
    print("="*60)
    print("🔄 Automated feature engineering and computation")
    print("📊 Real-time feature quality monitoring")
    print()
    
    try:
        # Initialize automation
        config = FeaturePipelineConfig()
        automation = FeaturePipelineAutomation(config)
        
        # Run automation
        automation.run_feature_automation()
        
        return True
        
    except Exception as e:
        print(f"❌ Feature automation failed: {e}")
        logger.exception("Feature pipeline automation failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
