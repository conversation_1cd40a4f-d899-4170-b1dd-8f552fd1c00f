#!/usr/bin/env python3
"""
Weather Data Import
Import weather data from Open-Meteo API for the full period
"""

import os
import requests
import json
import psycopg2
from datetime import datetime, timedelta
import time

def get_db_connection():
    """Get database connection"""
    try:
        return psycopg2.connect(
            host="localhost",
            database="solar_prediction",
            user="postgres",
            password="postgres",
            port="5432"
        )
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return None

def fetch_weather_data(start_date, end_date):
    """Fetch weather data from Open-Meteo API"""
    print(f"🌤️  Fetching weather data: {start_date} to {end_date}")
    
    # Marathon, Attica coordinates
    lat = 38.141367951893024
    lon = 24.00715534164505
    
    url = "https://archive-api.open-meteo.com/v1/archive"
    
    params = {
        'latitude': lat,
        'longitude': lon,
        'start_date': start_date,
        'end_date': end_date,
        'hourly': [
            'temperature_2m',
            'direct_radiation',
            'diffuse_radiation',
            'shortwave_radiation',
            'relative_humidity_2m',
            'cloud_cover'
        ],
        'timezone': 'Europe/Athens'
    }
    
    try:
        response = requests.get(url, params=params, timeout=30)
        response.raise_for_status()
        
        data = response.json()
        
        if 'hourly' not in data:
            print(f"   ❌ No hourly data in response")
            return []
        
        hourly = data['hourly']
        times = hourly['time']
        
        weather_records = []
        
        for i, time_str in enumerate(times):
            try:
                timestamp = datetime.fromisoformat(time_str.replace('T', ' '))
                
                record = {
                    'timestamp': timestamp,
                    'temperature_2m': hourly.get('temperature_2m', [None] * len(times))[i],
                    'direct_radiation': hourly.get('direct_radiation', [None] * len(times))[i],
                    'diffuse_radiation': hourly.get('diffuse_radiation', [None] * len(times))[i],
                    'shortwave_radiation': hourly.get('shortwave_radiation', [None] * len(times))[i],
                    'relative_humidity_2m': hourly.get('relative_humidity_2m', [None] * len(times))[i],
                    'cloud_cover': hourly.get('cloud_cover', [None] * len(times))[i]
                }
                
                weather_records.append(record)
                
            except Exception as e:
                continue
        
        print(f"   ✅ Fetched {len(weather_records)} weather records")
        return weather_records
        
    except Exception as e:
        print(f"   ❌ Error fetching weather data: {e}")
        return []

def import_weather_data():
    """Import weather data for the full period"""
    print("🌤️  COMPREHENSIVE WEATHER DATA IMPORT")
    print("=" * 40)
    
    # Define date ranges to fetch
    date_ranges = [
        ('2024-03-01', '2024-06-30'),  # Q1-Q2 2024
        ('2024-07-01', '2024-12-31'),  # Q3-Q4 2024
        ('2025-01-01', '2025-06-04')   # 2025 to current
    ]
    
    all_weather_data = []
    
    # Fetch data for each range
    for start_date, end_date in date_ranges:
        print(f"\n📅 Fetching period: {start_date} to {end_date}")
        
        weather_data = fetch_weather_data(start_date, end_date)
        if weather_data:
            all_weather_data.extend(weather_data)
        
        # Be nice to the API
        time.sleep(1)
    
    if not all_weather_data:
        print("❌ No weather data fetched")
        return False
    
    print(f"\n📊 Total weather records: {len(all_weather_data)}")
    print(f"   Date range: {all_weather_data[0]['timestamp']} to {all_weather_data[-1]['timestamp']}")
    
    # Import to database
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        with conn.cursor() as cur:
            # Clear existing weather data
            print(f"\n💾 Importing to database...")
            cur.execute("DELETE FROM weather_data")
            
            # Insert new data in batches
            batch_size = 1000
            total_inserted = 0
            
            for i in range(0, len(all_weather_data), batch_size):
                batch = all_weather_data[i:i + batch_size]
                
                # Prepare batch data
                batch_data = []
                for record in batch:
                    # Calculate global_horizontal_irradiance from direct + diffuse
                    ghi = None
                    if record['direct_radiation'] is not None and record['diffuse_radiation'] is not None:
                        ghi = record['direct_radiation'] + record['diffuse_radiation']
                    elif record['shortwave_radiation'] is not None:
                        ghi = record['shortwave_radiation']
                    
                    batch_data.append((
                        record['timestamp'],
                        record['temperature_2m'],
                        record['relative_humidity_2m'],
                        record['cloud_cover'],
                        ghi,
                        record['direct_radiation'],
                        record['diffuse_radiation'],
                        record['shortwave_radiation']
                    ))
                
                # Insert batch
                insert_query = """
                    INSERT INTO weather_data (
                        timestamp, temperature_2m, relative_humidity_2m, cloud_cover,
                        global_horizontal_irradiance, direct_normal_irradiance,
                        diffuse_radiation, shortwave_radiation
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """
                
                cur.executemany(insert_query, batch_data)
                total_inserted += len(batch_data)
                
                if total_inserted % 5000 == 0:
                    print(f"   Inserted {total_inserted} records...")
            
            conn.commit()
            
            print(f"✅ Weather import completed: {total_inserted} records")
            
            # Verify import
            cur.execute("""
                SELECT 
                    COUNT(*) as total,
                    MIN(timestamp) as earliest,
                    MAX(timestamp) as latest,
                    ROUND(AVG(temperature_2m), 2) as avg_temp,
                    ROUND(AVG(global_horizontal_irradiance), 2) as avg_ghi
                FROM weather_data
                WHERE timestamp IS NOT NULL
            """)
            
            result = cur.fetchone()
            print(f"\n📊 Weather Import Verification:")
            print(f"   Total records: {result[0]:,}")
            print(f"   Date range: {result[1]} to {result[2]}")
            print(f"   Average temperature: {result[3]}°C")
            print(f"   Average GHI: {result[4]} W/m²")
            
            return True
            
    except Exception as e:
        print(f"❌ Database import failed: {e}")
        conn.rollback()
        return False
    
    finally:
        conn.close()

def main():
    """Main import function"""
    print("🌤️  COMPREHENSIVE WEATHER DATA IMPORT")
    print("=" * 45)
    print(f"Started: {datetime.now()}")
    
    success = import_weather_data()
    
    if success:
        print(f"\n🎉 WEATHER IMPORT COMPLETED!")
        print("✅ Weather data imported for full period")
    else:
        print(f"\n❌ WEATHER IMPORT FAILED!")
    
    return success

if __name__ == "__main__":
    main()
