#!/usr/bin/env python3
"""
DISCORD BOT LAUNCHER
Production launcher για Discord bot με error handling και auto-restart
"""

import os
import sys
import json
import time
import logging
import asyncio
import subprocess
from datetime import datetime
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

class DiscordBotLauncher:
    """Discord bot launcher με monitoring και auto-restart"""
    
    def __init__(self):
        self.logger = self._setup_logger()
        self.config_path = project_root / "monitoring/config/discord_config.json"
        self.bot_script = project_root / "monitoring/discord_bot/solar_bot.py"
        self.max_restarts = 5
        self.restart_delay = 30  # seconds
        self.restart_count = 0
        
    def _setup_logger(self) -> logging.Logger:
        """Setup logger"""
        logger = logging.getLogger("discord.launcher")
        logger.setLevel(logging.INFO)
        
        # Create logs directory
        log_dir = project_root / "logs/discord"
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # File handler
        file_handler = logging.FileHandler(log_dir / "discord_bot_launcher.log")
        file_handler.setLevel(logging.INFO)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # Formatter
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    def check_configuration(self) -> bool:
        """Check Discord configuration"""
        try:
            if not self.config_path.exists():
                self.logger.error(f"❌ Discord config not found: {self.config_path}")
                self.logger.info("   Create monitoring/config/discord_config.json με your Discord tokens")
                return False
            
            with open(self.config_path, 'r') as f:
                config = json.load(f)
            
            # Check required fields
            required_fields = ['bot_token', 'webhook_url', 'guild_id']
            missing_fields = []
            
            for field in required_fields:
                value = config.get(field, '')
                if not value or 'PLACEHOLDER' in value or 'YOUR_' in value or 'EXAMPLE' in value:
                    missing_fields.append(field)
            
            if missing_fields:
                self.logger.error(f"❌ Missing Discord configuration: {missing_fields}")
                self.logger.info("   Update discord_config.json με your actual Discord tokens")
                return False
            
            self.logger.info("✅ Discord configuration validated")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Configuration check failed: {e}")
            return False
    
    def check_dependencies(self) -> bool:
        """Check required dependencies"""
        try:
            import discord
            self.logger.info("✅ discord.py available")
            return True
        except ImportError:
            self.logger.error("❌ discord.py not installed")
            self.logger.info("   Install με: pip install discord.py")
            return False
    
    def check_bot_script(self) -> bool:
        """Check if bot script exists"""
        if not self.bot_script.exists():
            self.logger.error(f"❌ Bot script not found: {self.bot_script}")
            return False
        
        self.logger.info("✅ Bot script found")
        return True
    
    def start_bot(self) -> bool:
        """Start Discord bot"""
        try:
            self.logger.info("🤖 Starting Discord bot...")
            
            # Change to project directory
            os.chdir(project_root)
            
            # Start bot process
            process = subprocess.Popen(
                [sys.executable, str(self.bot_script)],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            self.logger.info(f"🚀 Discord bot started με PID: {process.pid}")
            
            # Monitor bot process
            while True:
                # Check if process is still running
                if process.poll() is not None:
                    # Process has terminated
                    stdout, stderr = process.communicate()
                    
                    self.logger.error(f"❌ Discord bot terminated με exit code: {process.returncode}")
                    
                    if stdout:
                        self.logger.info(f"STDOUT: {stdout}")
                    if stderr:
                        self.logger.error(f"STDERR: {stderr}")
                    
                    return False
                
                # Sleep before next check
                time.sleep(5)
                
        except KeyboardInterrupt:
            self.logger.info("🛑 Bot stopped by user")
            if 'process' in locals():
                process.terminate()
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to start bot: {e}")
            return False
    
    def run_with_auto_restart(self):
        """Run bot με auto-restart capability"""
        self.logger.info("🤖 DISCORD BOT LAUNCHER")
        self.logger.info("=" * 50)
        
        # Pre-flight checks
        if not self.check_dependencies():
            return False
        
        if not self.check_bot_script():
            return False
        
        if not self.check_configuration():
            return False
        
        # Start bot με auto-restart
        while self.restart_count < self.max_restarts:
            try:
                self.logger.info(f"🚀 Starting bot (attempt {self.restart_count + 1}/{self.max_restarts})")
                
                success = self.start_bot()
                
                if success:
                    # Bot stopped gracefully
                    self.logger.info("✅ Bot stopped gracefully")
                    break
                else:
                    # Bot crashed, attempt restart
                    self.restart_count += 1
                    
                    if self.restart_count < self.max_restarts:
                        self.logger.warning(f"⚠️ Bot crashed, restarting σε {self.restart_delay} seconds...")
                        time.sleep(self.restart_delay)
                    else:
                        self.logger.error(f"❌ Max restarts ({self.max_restarts}) reached, giving up")
                        return False
                        
            except KeyboardInterrupt:
                self.logger.info("🛑 Launcher stopped by user")
                break
            except Exception as e:
                self.logger.error(f"❌ Launcher error: {e}")
                self.restart_count += 1
                
                if self.restart_count < self.max_restarts:
                    time.sleep(self.restart_delay)
                else:
                    return False
        
        return True
    
    def run_once(self):
        """Run bot once without auto-restart"""
        self.logger.info("🤖 DISCORD BOT - SINGLE RUN")
        self.logger.info("=" * 40)
        
        # Pre-flight checks
        if not self.check_dependencies():
            return False
        
        if not self.check_bot_script():
            return False
        
        if not self.check_configuration():
            return False
        
        # Start bot
        return self.start_bot()

def main():
    """Main launcher function"""
    launcher = DiscordBotLauncher()
    
    # Check command line arguments
    if len(sys.argv) > 1 and sys.argv[1] == "--once":
        success = launcher.run_once()
    else:
        success = launcher.run_with_auto_restart()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
