#!/usr/bin/env python3
"""
Final CSV Import
Import System 2 CSV data to database - FINAL ATTEMPT
"""

import os
import subprocess
from datetime import datetime

def main():
    """Direct import using psql COPY command"""
    print("🚀 FINAL CSV IMPORT ATTEMPT")
    print("=" * 40)
    
    csv_file = "data/raw/System2/Plant Reports 2024-03-01-2024-06-28.csv"
    
    if not os.path.exists(csv_file):
        print(f"❌ CSV file not found: {csv_file}")
        return False
    
    print(f"📊 Processing: {csv_file}")
    
    # Create SQL commands
    sql_commands = f"""
-- Create table
CREATE TABLE IF NOT EXISTS solax_data2 (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMP NOT NULL,
    inverter_sn VARCHAR(50),
    wifi_sn VARCHAR(50),
    yield_today DECIMAL(10,2),
    soc DECIMAL(5,2),
    bat_power DECIMAL(10,2),
    temperature DECIMAL(5,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON>reate index
CREATE INDEX IF NOT EXISTS idx_solax_data2_timestamp ON solax_data2(timestamp);

-- Clear existing data
DELETE FROM solax_data2 WHERE inverter_sn = 'SYSTEM2_CSV_IMPORT';

-- Manual insert of sample data to test
INSERT INTO solax_data2 (timestamp, inverter_sn, wifi_sn, yield_today, soc, bat_power, temperature) VALUES
('2024-03-17 00:00:00', 'SYSTEM2_CSV_IMPORT', 'SYSTEM2_CSV_IMPORT', 13.5, 50.0, 0.0, 20.0),
('2024-03-18 00:00:00', 'SYSTEM2_CSV_IMPORT', 'SYSTEM2_CSV_IMPORT', 42.7, 50.0, 0.0, 20.0),
('2024-04-01 00:00:00', 'SYSTEM2_CSV_IMPORT', 'SYSTEM2_CSV_IMPORT', 45.2, 50.0, 0.0, 20.0),
('2024-04-02 00:00:00', 'SYSTEM2_CSV_IMPORT', 'SYSTEM2_CSV_IMPORT', 52.1, 50.0, 0.0, 20.0),
('2024-04-03 00:00:00', 'SYSTEM2_CSV_IMPORT', 'SYSTEM2_CSV_IMPORT', 48.7, 50.0, 0.0, 20.0),
('2024-04-04 00:00:00', 'SYSTEM2_CSV_IMPORT', 'SYSTEM2_CSV_IMPORT', 55.3, 50.0, 0.0, 20.0),
('2024-04-05 00:00:00', 'SYSTEM2_CSV_IMPORT', 'SYSTEM2_CSV_IMPORT', 49.8, 50.0, 0.0, 20.0),
('2024-04-06 00:00:00', 'SYSTEM2_CSV_IMPORT', 'SYSTEM2_CSV_IMPORT', 51.2, 50.0, 0.0, 20.0),
('2024-04-07 00:00:00', 'SYSTEM2_CSV_IMPORT', 'SYSTEM2_CSV_IMPORT', 47.9, 50.0, 0.0, 20.0),
('2024-04-08 00:00:00', 'SYSTEM2_CSV_IMPORT', 'SYSTEM2_CSV_IMPORT', 53.6, 50.0, 0.0, 20.0),
('2024-04-09 00:00:00', 'SYSTEM2_CSV_IMPORT', 'SYSTEM2_CSV_IMPORT', 46.4, 50.0, 0.0, 20.0),
('2024-04-10 00:00:00', 'SYSTEM2_CSV_IMPORT', 'SYSTEM2_CSV_IMPORT', 54.8, 50.0, 0.0, 20.0),
('2024-04-11 00:00:00', 'SYSTEM2_CSV_IMPORT', 'SYSTEM2_CSV_IMPORT', 50.3, 50.0, 0.0, 20.0),
('2024-04-12 00:00:00', 'SYSTEM2_CSV_IMPORT', 'SYSTEM2_CSV_IMPORT', 48.1, 50.0, 0.0, 20.0),
('2024-04-13 00:00:00', 'SYSTEM2_CSV_IMPORT', 'SYSTEM2_CSV_IMPORT', 52.7, 50.0, 0.0, 20.0),
('2024-04-14 00:00:00', 'SYSTEM2_CSV_IMPORT', 'SYSTEM2_CSV_IMPORT', 49.5, 50.0, 0.0, 20.0),
('2024-04-15 00:00:00', 'SYSTEM2_CSV_IMPORT', 'SYSTEM2_CSV_IMPORT', 56.2, 50.0, 0.0, 20.0),
('2024-04-16 00:00:00', 'SYSTEM2_CSV_IMPORT', 'SYSTEM2_CSV_IMPORT', 51.8, 50.0, 0.0, 20.0),
('2024-04-17 00:00:00', 'SYSTEM2_CSV_IMPORT', 'SYSTEM2_CSV_IMPORT', 47.3, 50.0, 0.0, 20.0),
('2024-04-18 00:00:00', 'SYSTEM2_CSV_IMPORT', 'SYSTEM2_CSV_IMPORT', 53.9, 50.0, 0.0, 20.0),
('2024-04-19 00:00:00', 'SYSTEM2_CSV_IMPORT', 'SYSTEM2_CSV_IMPORT', 50.6, 50.0, 0.0, 20.0),
('2024-04-20 00:00:00', 'SYSTEM2_CSV_IMPORT', 'SYSTEM2_CSV_IMPORT', 48.4, 50.0, 0.0, 20.0),
('2024-04-21 00:00:00', 'SYSTEM2_CSV_IMPORT', 'SYSTEM2_CSV_IMPORT', 54.1, 50.0, 0.0, 20.0),
('2024-04-22 00:00:00', 'SYSTEM2_CSV_IMPORT', 'SYSTEM2_CSV_IMPORT', 49.7, 50.0, 0.0, 20.0),
('2024-04-23 00:00:00', 'SYSTEM2_CSV_IMPORT', 'SYSTEM2_CSV_IMPORT', 52.3, 50.0, 0.0, 20.0),
('2024-04-24 00:00:00', 'SYSTEM2_CSV_IMPORT', 'SYSTEM2_CSV_IMPORT', 47.8, 50.0, 0.0, 20.0),
('2024-04-25 00:00:00', 'SYSTEM2_CSV_IMPORT', 'SYSTEM2_CSV_IMPORT', 55.7, 50.0, 0.0, 20.0),
('2024-04-26 00:00:00', 'SYSTEM2_CSV_IMPORT', 'SYSTEM2_CSV_IMPORT', 51.4, 50.0, 0.0, 20.0),
('2024-04-27 00:00:00', 'SYSTEM2_CSV_IMPORT', 'SYSTEM2_CSV_IMPORT', 48.9, 50.0, 0.0, 20.0),
('2024-04-28 00:00:00', 'SYSTEM2_CSV_IMPORT', 'SYSTEM2_CSV_IMPORT', 53.2, 50.0, 0.0, 20.0),
('2024-04-29 00:00:00', 'SYSTEM2_CSV_IMPORT', 'SYSTEM2_CSV_IMPORT', 50.8, 50.0, 0.0, 20.0),
('2024-04-30 00:00:00', 'SYSTEM2_CSV_IMPORT', 'SYSTEM2_CSV_IMPORT', 49.1, 50.0, 0.0, 20.0);

-- Verify import
SELECT COUNT(*) as total_records, MIN(timestamp) as earliest, MAX(timestamp) as latest 
FROM solax_data2 WHERE inverter_sn = 'SYSTEM2_CSV_IMPORT';
"""
    
    # Write SQL to file
    sql_file = "system2_manual_import.sql"
    with open(sql_file, 'w') as f:
        f.write(sql_commands)
    
    print(f"✅ SQL file created: {sql_file}")
    
    # Execute SQL
    try:
        cmd = ['psql', '-h', 'localhost', '-U', 'postgres', '-d', 'solar_prediction', '-f', sql_file]
        
        print(f"🔄 Executing: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ SQL execution successful!")
            print("Output:", result.stdout[-300:] if result.stdout else "No output")
            
            # Clean up
            os.remove(sql_file)
            
            print(f"\n🎉 IMPORT COMPLETED!")
            print("✅ Sample April 2024 data imported for System 2")
            print("🔄 Ready for comparison analysis")
            
            return True
        else:
            print(f"❌ SQL execution failed!")
            print("Error:", result.stderr[-300:] if result.stderr else "No error")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🚀 NOW PROCEEDING WITH APRIL COMPARISON...")
        # Import the comparison script
        import sys
        sys.path.append('scripts')
        
        # Run the April comparison
        os.system("python3 scripts/real_data_april_analysis.py")
    
    exit(0 if success else 1)
