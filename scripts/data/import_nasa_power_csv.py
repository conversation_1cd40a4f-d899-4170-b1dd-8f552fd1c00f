#!/usr/bin/env python3
"""
Import NASA POWER CSV Data
Imports the existing NASA POWER CSV file into the database
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import pandas as pd
import numpy as np
from datetime import datetime
import logging
import psycopg2
from psycopg2.extras import execute_batch

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class NASAPowerCSVImporter:
    """Imports NASA POWER CSV data into database"""
    
    def __init__(self):
        self.csv_file = "/home/<USER>/solar-prediction-project/data/raw/NASA/POWER_Point_Hourly_20240301_20250607_038d14N_024d00E_LST (1).csv"
        self.latitude = 38.1419
        self.longitude = 24.0046
        
        # Physics constants for derived features
        self.NOCT = 45.0  # Nominal Operating Cell Temperature
        self.TEMP_COEFF = -0.004  # Temperature coefficient (-0.4%/°C)
    
    def read_nasa_power_csv(self) -> pd.DataFrame:
        """Read and parse the NASA POWER CSV file"""
        
        logger.info(f"Reading NASA POWER CSV: {self.csv_file}")
        
        try:
            # Read CSV, skipping header lines
            df = pd.read_csv(self.csv_file, skiprows=26)  # Skip header until data starts
            
            logger.info(f"Raw CSV loaded: {len(df)} rows, {len(df.columns)} columns")
            
            # Create timestamp from YEAR, MO, DY, HR columns
            df['timestamp'] = pd.to_datetime(
                df[['YEAR', 'MO', 'DY', 'HR']].rename(columns={
                    'YEAR': 'year', 'MO': 'month', 'DY': 'day', 'HR': 'hour'
                })
            )
            
            # Map NASA POWER columns to our schema
            df_mapped = pd.DataFrame({
                'timestamp': df['timestamp'],
                'latitude': self.latitude,
                'longitude': self.longitude,
                
                # Primary NASA POWER parameters
                'ghi': df['ALLSKY_SFC_SW_DWN'].replace(-999.0, np.nan),  # W/m²
                'temperature': df['T2M'].replace(-999.0, np.nan),  # °C
                'wind_speed': df['WS10M'].replace(-999.0, np.nan),  # m/s
                'relative_humidity': df['RH2M'].replace(-999.0, np.nan),  # %
                'surface_pressure': df['PS'].replace(-999.0, np.nan),  # kPa
                'clear_sky_ghi': df['CLRSKY_SFC_SW_DWN'].replace(-999.0, np.nan),  # W/m²
                'clearness_index': df['ALLSKY_KT'].replace(-999.0, np.nan),  # dimensionless
                
                # Additional parameters
                'solar_zenith_angle': df['SZA'].replace(-999.0, np.nan),  # degrees
                'specific_humidity': df['QV2M'].replace(-999.0, np.nan),  # g/kg
                'precipitation': df['PRECTOTCORR'].replace(-999.0, np.nan),  # mm/hour
                'surface_albedo': df['ALLSKY_SRF_ALB'].replace(-999.0, np.nan),  # dimensionless
                'diffuse_radiation': df['ALLSKY_SFC_SW_DIFF'].replace(-999.0, np.nan),  # W/m²
                'direct_normal_irradiance': df['ALLSKY_SFC_SW_DNI'].replace(-999.0, np.nan),  # W/m²
            })
            
            logger.info(f"Mapped data: {len(df_mapped)} records")
            
            return df_mapped
            
        except Exception as e:
            logger.error(f"Error reading CSV: {e}")
            return pd.DataFrame()
    
    def calculate_derived_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate physics-based derived features"""
        
        logger.info("Calculating derived features...")
        
        # Module temperature (NOCT model)
        df['module_temperature'] = np.where(
            (df['ghi'] > 0) & (df['temperature'].notna()),
            df['temperature'] + (df['ghi'] / 800.0) * (self.NOCT - 20.0),
            df['temperature']
        )
        
        # Temperature efficiency factor
        df['temperature_efficiency_factor'] = np.where(
            df['module_temperature'].notna(),
            np.maximum(0.5, 1 + self.TEMP_COEFF * (df['module_temperature'] - 25.0)),
            1.0
        )
        
        # Wind cooling factor
        df['wind_cooling_factor'] = np.where(
            df['wind_speed'].notna(),
            1.0 + (df['wind_speed'] * 0.02),  # 2% per m/s
            1.0
        )
        df['wind_cooling_factor'] = np.minimum(1.3, df['wind_cooling_factor'])  # Cap at 30%
        
        # Air mass (simplified - using solar zenith angle)
        df['air_mass'] = np.where(
            (df['solar_zenith_angle'].notna()) & (df['solar_zenith_angle'] < 90),
            1 / np.cos(np.radians(df['solar_zenith_angle'])),
            np.nan
        )
        df['air_mass'] = np.minimum(10.0, df['air_mass'])  # Cap at 10
        
        # Data source and metadata
        df['source'] = 'nasa_power_csv'
        df['ingestion_run_id'] = f"csv_import_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        logger.info("Derived features calculated")
        
        return df
    
    def validate_data_quality(self, df: pd.DataFrame) -> dict:
        """Validate data quality before import"""
        
        logger.info("Validating data quality...")
        
        quality_report = {
            'total_records': len(df),
            'date_range': {
                'start': df['timestamp'].min(),
                'end': df['timestamp'].max()
            },
            'missing_data': {},
            'value_ranges': {},
            'quality_issues': []
        }
        
        # Check missing data
        key_columns = ['ghi', 'temperature', 'clearness_index', 'wind_speed']
        for col in key_columns:
            missing_pct = (df[col].isna().sum() / len(df)) * 100
            quality_report['missing_data'][col] = missing_pct
            
            if missing_pct > 50:
                quality_report['quality_issues'].append(f"{col}: {missing_pct:.1f}% missing")
        
        # Check value ranges
        for col in key_columns:
            if col in df.columns and not df[col].isna().all():
                values = df[col].dropna()
                quality_report['value_ranges'][col] = {
                    'min': float(values.min()),
                    'max': float(values.max()),
                    'mean': float(values.mean())
                }
        
        # Validate ranges
        if 'ghi' in quality_report['value_ranges']:
            ghi_max = quality_report['value_ranges']['ghi']['max']
            if ghi_max > 1500:
                quality_report['quality_issues'].append(f"GHI max too high: {ghi_max}")
        
        if 'temperature' in quality_report['value_ranges']:
            temp_range = quality_report['value_ranges']['temperature']
            if temp_range['min'] < -20 or temp_range['max'] > 60:
                quality_report['quality_issues'].append(f"Temperature range unusual: {temp_range['min']} to {temp_range['max']}")
        
        logger.info(f"Data quality: {len(quality_report['quality_issues'])} issues found")
        
        return quality_report
    
    def import_to_database(self, df: pd.DataFrame, batch_size: int = 1000) -> bool:
        """Import data to database in batches"""
        
        logger.info(f"Importing {len(df)} records to database...")
        
        try:
            conn = psycopg2.connect(
                host='localhost', port=5433,
                database='solar_prediction',
                user='postgres',
                password='postgres'
            )
            
            # Prepare insert query
            insert_query = """
            INSERT INTO nasa_power_data (
                timestamp, latitude, longitude, ghi, temperature, wind_speed,
                relative_humidity, surface_pressure, clear_sky_ghi, clearness_index,
                module_temperature, temperature_efficiency_factor, wind_cooling_factor,
                air_mass, source, ingestion_run_id, raw_data
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
            )
            """
            
            # Prepare data for batch insert
            records = []
            for _, row in df.iterrows():
                # Create raw_data JSON
                raw_data = {
                    'solar_zenith_angle': row.get('solar_zenith_angle'),
                    'specific_humidity': row.get('specific_humidity'),
                    'precipitation': row.get('precipitation'),
                    'surface_albedo': row.get('surface_albedo'),
                    'diffuse_radiation': row.get('diffuse_radiation'),
                    'direct_normal_irradiance': row.get('direct_normal_irradiance')
                }
                
                record = (
                    row['timestamp'],
                    row['latitude'],
                    row['longitude'],
                    row['ghi'] if pd.notna(row['ghi']) else None,
                    row['temperature'] if pd.notna(row['temperature']) else None,
                    row['wind_speed'] if pd.notna(row['wind_speed']) else None,
                    row['relative_humidity'] if pd.notna(row['relative_humidity']) else None,
                    row['surface_pressure'] if pd.notna(row['surface_pressure']) else None,
                    row['clear_sky_ghi'] if pd.notna(row['clear_sky_ghi']) else None,
                    row['clearness_index'] if pd.notna(row['clearness_index']) else None,
                    row['module_temperature'] if pd.notna(row['module_temperature']) else None,
                    row['temperature_efficiency_factor'] if pd.notna(row['temperature_efficiency_factor']) else None,
                    row['wind_cooling_factor'] if pd.notna(row['wind_cooling_factor']) else None,
                    row['air_mass'] if pd.notna(row['air_mass']) else None,
                    row['source'],
                    row['ingestion_run_id'],
                    raw_data
                )
                records.append(record)
            
            # Insert in batches
            with conn.cursor() as cur:
                total_inserted = 0
                
                for i in range(0, len(records), batch_size):
                    batch = records[i:i + batch_size]
                    
                    try:
                        execute_batch(cur, insert_query, batch, page_size=batch_size)
                        total_inserted += len(batch)
                        
                        if total_inserted % (batch_size * 10) == 0:
                            logger.info(f"Inserted {total_inserted}/{len(records)} records...")
                            
                    except Exception as e:
                        logger.error(f"Error inserting batch {i//batch_size + 1}: {e}")
                        # Continue with next batch
                        continue
                
                conn.commit()
                
            conn.close()
            
            logger.info(f"✅ Successfully imported {total_inserted} records")
            return True
            
        except Exception as e:
            logger.error(f"Database import error: {e}")
            return False
    
    def check_existing_data(self) -> int:
        """Check how many NASA POWER records already exist"""
        
        try:
            conn = psycopg2.connect(
                host='localhost', port=5433,
                database='solar_prediction',
                user='postgres',
                password='postgres'
            )
            
            with conn.cursor() as cur:
                cur.execute("SELECT COUNT(*) FROM nasa_power_data;")
                count = cur.fetchone()[0]
            
            conn.close()
            return count
            
        except Exception as e:
            logger.error(f"Error checking existing data: {e}")
            return 0


def main():
    """Main execution function"""
    
    print("🚀 NASA POWER CSV Import")
    print("=" * 30)
    
    # Initialize importer
    importer = NASAPowerCSVImporter()
    
    # Check existing data
    print("📊 Checking existing data...")
    existing_count = importer.check_existing_data()
    print(f"   Existing NASA POWER records: {existing_count:,}")
    
    if existing_count > 0:
        proceed = input(f"\n⚠️ Found {existing_count} existing records. Continue anyway? (y/n): ").lower().strip()
        if proceed != 'y':
            print("❌ Import cancelled by user")
            return False
    
    # Read CSV data
    print("\n📁 Reading NASA POWER CSV...")
    df = importer.read_nasa_power_csv()
    
    if df.empty:
        print("❌ No data loaded from CSV")
        return False
    
    print(f"✅ Loaded {len(df):,} records from CSV")
    print(f"   Date range: {df['timestamp'].min()} to {df['timestamp'].max()}")
    
    # Calculate derived features
    print("\n🔧 Calculating derived features...")
    df = importer.calculate_derived_features(df)
    
    # Validate data quality
    print("\n🔍 Validating data quality...")
    quality_report = importer.validate_data_quality(df)
    
    print(f"   Total records: {quality_report['total_records']:,}")
    print(f"   Date range: {quality_report['date_range']['start']} to {quality_report['date_range']['end']}")
    print(f"   Quality issues: {len(quality_report['quality_issues'])}")
    
    for issue in quality_report['quality_issues']:
        print(f"     ⚠️ {issue}")
    
    # Show missing data summary
    print("\n📈 Missing Data Summary:")
    for col, pct in quality_report['missing_data'].items():
        print(f"   {col}: {pct:.1f}% missing")
    
    # Ask for confirmation
    if quality_report['quality_issues']:
        proceed = input(f"\n⚠️ Found {len(quality_report['quality_issues'])} quality issues. Continue? (y/n): ").lower().strip()
        if proceed != 'y':
            print("❌ Import cancelled due to quality issues")
            return False
    
    # Import to database
    print("\n💾 Importing to database...")
    success = importer.import_to_database(df)
    
    if success:
        print("\n✅ NASA POWER CSV import completed successfully!")
        
        # Final verification
        final_count = importer.check_existing_data()
        print(f"📊 Total NASA POWER records in database: {final_count:,}")
        
        print("\n🎯 Next steps:")
        print("   1. Verify data quality in database")
        print("   2. Test enhanced feature engineering")
        print("   3. Train models with NASA POWER features")
        
        return True
    else:
        print("\n❌ Import failed")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
