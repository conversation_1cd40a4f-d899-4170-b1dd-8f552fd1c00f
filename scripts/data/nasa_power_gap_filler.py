#!/usr/bin/env python3
"""
NASA POWER Gap Filler
Fills missing NASA POWER data gaps systematically
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import psycopg2
import time
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class NASAPowerGapFiller:
    """Fill gaps in NASA POWER data"""
    
    def __init__(self):
        self.base_url = "https://power.larc.nasa.gov/api/temporal/hourly/point"
        self.latitude = 38.141348260997596
        self.longitude = 24.0071653937747
        
        # Parameters to collect
        self.parameters = [
            'ALLSKY_SFC_SW_DWN',    # GHI (W/m²)
            'T2M',                   # Temperature (°C)
            'WS10M',                 # Wind Speed (m/s)
            'RH2M',                  # Relative Humidity (%)
            'PS',                    # Surface Pressure (kPa)
            'CLRSKY_SFC_SW_DWN',    # Clear Sky GHI (W/m²)
            'ALLSKY_KT'             # Clearness Index
        ]
        
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Solar-Prediction-Project/1.0 (Gap Filling)'
        })
    
    def find_data_gaps(self) -> list:
        """Find gaps in NASA POWER data"""
        
        logger.info("🔍 Finding NASA POWER data gaps...")
        
        try:
            conn = psycopg2.connect(
                host='localhost', port=5433,
                database='solar_prediction',
                user='postgres',
                password='postgres'
            )
            
            # Get date range of existing data
            with conn.cursor() as cur:
                cur.execute("""
                    SELECT 
                        DATE(MIN(timestamp)) as earliest,
                        DATE(MAX(timestamp)) as latest,
                        COUNT(*) as total_records
                    FROM nasa_power_data
                """)
                result = cur.fetchone()
                
                if result and result[0]:
                    earliest_date = result[0]
                    latest_date = result[1]
                    total_records = result[2]
                    
                    logger.info(f"📊 Existing data: {earliest_date} to {latest_date} ({total_records:,} records)")
                else:
                    logger.warning("No existing NASA POWER data found")
                    return []
            
            # Define target range (March 2024 to present)
            target_start = datetime(2024, 3, 1).date()
            target_end = (datetime.now() - timedelta(days=2)).date()  # NASA has 2-day delay
            
            logger.info(f"🎯 Target range: {target_start} to {target_end}")
            
            # Find missing date ranges
            gaps = []
            
            # Gap before existing data
            if earliest_date > target_start:
                gap_days = (earliest_date - target_start).days
                logger.info(f"📅 Gap before existing data: {gap_days} days")
                gaps.append({
                    'start': target_start,
                    'end': earliest_date - timedelta(days=1),
                    'days': gap_days,
                    'type': 'before'
                })
            
            # Gap after existing data
            if latest_date < target_end:
                gap_days = (target_end - latest_date).days
                logger.info(f"📅 Gap after existing data: {gap_days} days")
                gaps.append({
                    'start': latest_date + timedelta(days=1),
                    'end': target_end,
                    'days': gap_days,
                    'type': 'after'
                })
            
            conn.close()
            
            total_gap_days = sum(gap['days'] for gap in gaps)
            logger.info(f"📈 Total gap days: {total_gap_days}")
            
            return gaps
            
        except Exception as e:
            logger.error(f"Error finding gaps: {e}")
            return []
    
    def fill_date_range(self, start_date: datetime.date, end_date: datetime.date) -> dict:
        """Fill data for a specific date range"""
        
        logger.info(f"📥 Filling NASA POWER data: {start_date} to {end_date}")
        
        current_date = start_date
        successful_days = 0
        failed_days = 0
        total_records = 0
        
        while current_date <= end_date:
            try:
                logger.info(f"📅 Processing {current_date}")
                
                # Request data for this day
                result = self._request_day_data(current_date)
                
                if result['status'] == 'success':
                    # Save to database
                    records_saved = self._save_day_data(current_date, result['data'])
                    successful_days += 1
                    total_records += records_saved
                    logger.info(f"✅ {current_date}: {records_saved} records saved")
                else:
                    failed_days += 1
                    logger.error(f"❌ {current_date}: {result.get('error', 'Unknown error')}")
                
                # Conservative delay (1 request per 5 seconds)
                time.sleep(5)
                
                current_date += timedelta(days=1)
                
            except Exception as e:
                logger.error(f"❌ Error processing {current_date}: {e}")
                failed_days += 1
                current_date += timedelta(days=1)
                continue
        
        return {
            'successful_days': successful_days,
            'failed_days': failed_days,
            'total_records': total_records,
            'success_rate': (successful_days / (successful_days + failed_days)) * 100 if (successful_days + failed_days) > 0 else 0
        }
    
    def _request_day_data(self, date: datetime.date) -> dict:
        """Request NASA POWER data for a single day"""
        
        date_str = date.strftime('%Y%m%d')
        
        params = {
            'parameters': ','.join(self.parameters),
            'community': 'RE',
            'longitude': self.longitude,
            'latitude': self.latitude,
            'start': date_str,
            'end': date_str,
            'format': 'JSON'
        }
        
        try:
            response = self.session.get(
                self.base_url,
                params=params,
                timeout=60
            )
            
            if response.status_code == 200:
                data = response.json()
                return {'status': 'success', 'data': data}
            else:
                return {'status': 'failed', 'error': f"HTTP {response.status_code}"}
                
        except Exception as e:
            return {'status': 'error', 'error': str(e)}
    
    def _save_day_data(self, date: datetime.date, api_data: dict) -> int:
        """Save NASA POWER data for a day to database"""
        
        try:
            conn = psycopg2.connect(
                host='localhost', port=5433,
                database='solar_prediction',
                user='postgres',
                password='postgres'
            )
            
            if 'properties' not in api_data or 'parameter' not in api_data['properties']:
                logger.warning(f"No parameter data for {date}")
                return 0
            
            parameters_data = api_data['properties']['parameter']
            records_saved = 0
            
            # Process hourly data
            for hour in range(24):
                hour_str = f"{hour:02d}"
                
                # Create timestamp
                timestamp = datetime.combine(date, datetime.min.time()) + timedelta(hours=hour)
                
                # Extract values for this hour
                record = {
                    'timestamp': timestamp,
                    'latitude': self.latitude,
                    'longitude': self.longitude,
                    'source': 'nasa_power_gap_fill',
                    'ingestion_run_id': f"gap_fill_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                }
                
                # Map parameters
                param_mapping = {
                    'ghi': 'ALLSKY_SFC_SW_DWN',
                    'temperature': 'T2M',
                    'wind_speed': 'WS10M',
                    'relative_humidity': 'RH2M',
                    'surface_pressure': 'PS',
                    'clear_sky_ghi': 'CLRSKY_SFC_SW_DWN',
                    'clearness_index': 'ALLSKY_KT'
                }
                
                for db_field, api_param in param_mapping.items():
                    if api_param in parameters_data and hour_str in parameters_data[api_param]:
                        value = parameters_data[api_param][hour_str]
                        record[db_field] = value if value != -999.0 else None
                    else:
                        record[db_field] = None
                
                # Insert record
                with conn.cursor() as cur:
                    cur.execute("""
                        INSERT INTO nasa_power_data (
                            timestamp, latitude, longitude, ghi, temperature, 
                            wind_speed, relative_humidity, surface_pressure, 
                            clear_sky_ghi, clearness_index, source, ingestion_run_id
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        record['timestamp'],
                        record['latitude'],
                        record['longitude'],
                        record['ghi'],
                        record['temperature'],
                        record['wind_speed'],
                        record['relative_humidity'],
                        record['surface_pressure'],
                        record['clear_sky_ghi'],
                        record['clearness_index'],
                        record['source'],
                        record['ingestion_run_id']
                    ))
                
                records_saved += 1
            
            conn.commit()
            conn.close()
            
            return records_saved
            
        except Exception as e:
            logger.error(f"Error saving data for {date}: {e}")
            return 0


def main():
    """Main gap filling function"""
    
    print("🔧 NASA POWER Gap Filler")
    print("=" * 30)
    
    # Initialize gap filler
    filler = NASAPowerGapFiller()
    
    # Find gaps
    print("1️⃣ Finding data gaps...")
    gaps = filler.find_data_gaps()
    
    if not gaps:
        print("✅ No gaps found - data appears complete!")
        return True
    
    print(f"📊 Found {len(gaps)} gap(s):")
    for i, gap in enumerate(gaps):
        print(f"   Gap {i+1}: {gap['start']} to {gap['end']} ({gap['days']} days)")
    
    # Ask for confirmation
    total_days = sum(gap['days'] for gap in gaps)
    estimated_time = total_days * 5 / 60  # 5 seconds per day
    
    print(f"\n⏱️ Estimated time: {estimated_time:.1f} minutes")
    print(f"📡 API requests: {total_days}")
    
    proceed = input("\n🚀 Proceed with gap filling? (y/n): ").lower().strip()
    if proceed != 'y':
        print("❌ Gap filling cancelled")
        return False
    
    # Fill gaps
    print("\n2️⃣ Filling gaps...")
    total_successful = 0
    total_failed = 0
    total_records = 0
    
    for i, gap in enumerate(gaps):
        print(f"\n📥 Processing gap {i+1}/{len(gaps)}: {gap['start']} to {gap['end']}")
        
        result = filler.fill_date_range(gap['start'], gap['end'])
        
        print(f"   ✅ Successful days: {result['successful_days']}")
        print(f"   ❌ Failed days: {result['failed_days']}")
        print(f"   📊 Records saved: {result['total_records']}")
        print(f"   📈 Success rate: {result['success_rate']:.1f}%")
        
        total_successful += result['successful_days']
        total_failed += result['failed_days']
        total_records += result['total_records']
    
    # Summary
    print("\n🎯 Gap Filling Summary:")
    print("=" * 25)
    print(f"✅ Successful days: {total_successful}")
    print(f"❌ Failed days: {total_failed}")
    print(f"📊 Total records saved: {total_records:,}")
    
    if total_successful + total_failed > 0:
        overall_success_rate = (total_successful / (total_successful + total_failed)) * 100
        print(f"📈 Overall success rate: {overall_success_rate:.1f}%")
    
    if overall_success_rate >= 90:
        print("\n🏆 Gap filling completed successfully!")
        return True
    else:
        print("\n⚠️ Gap filling completed with issues")
        print("   → Some data may still be missing")
        print("   → Consider retrying failed dates")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
