#!/usr/bin/env python3
"""
Enhanced Data Quality System for Solar Prediction

This system implements comprehensive data quality improvement strategies:
1. System-aware data separation and normalization
2. Advanced data cleaning and validation
3. Intelligent missing data handling
4. Dynamic normalization ranges
5. Quality metrics and monitoring

Based on extensive documentation analysis and best practices.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import pandas as pd
import numpy as np
import psycopg2
from dotenv import load_dotenv
import logging
from datetime import datetime, timedelta
from sklearn.preprocessing import MinMaxScaler, RobustScaler
from sklearn.impute import KNNImputer
from sklearn.ensemble import IsolationForest
from typing import Dict, List, Tuple, Any, Optional
import json
from pathlib import Path

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedDataQualitySystem:
    """Comprehensive data quality improvement system"""
    
    def __init__(self, output_dir="data/quality_enhanced"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # System-specific configurations
        self.system_configs = {
            1: {
                'name': 'Σπίτι Πάνω',
                'capacity_kw': 10.5,
                'battery_kwh': 12.0,
                'consumption_profile': 'low_steady',
                'grid_dependency': 0.0,
                'self_sufficiency': 1.0
            },
            2: {
                'name': 'Σπίτι Κάτω', 
                'capacity_kw': 10.5,
                'battery_kwh': 12.0,
                'consumption_profile': 'high_variable',
                'grid_dependency': 0.395,
                'self_sufficiency': 0.605
            }
        }
        
        # Quality thresholds
        self.quality_thresholds = {
            'ac_power_max': 12000,  # Max AC power (W)
            'soc_range': (0, 100),  # SOC percentage range
            'temperature_range': (-10, 50),  # Temperature range (°C)
            'ghi_max': 1400,  # Max GHI (W/m²)
            'outlier_contamination': 0.05  # 5% outlier threshold
        }
        
        # Normalization strategies
        self.normalization_strategies = {
            'temporal': 'cyclical',  # Use sin/cos for temporal features
            'weather': 'robust',     # Use robust scaler for weather data
            'power': 'minmax',       # Use min-max for power data
            'system': 'system_aware' # System-specific normalization
        }
        
    def create_system_aware_tables(self):
        """Create system-aware database tables"""
        logger.info("Creating system-aware database tables...")
        
        load_dotenv()
        conn = psycopg2.connect(
            host=os.getenv('DATABASE_HOST', 'localhost'),
            database=os.getenv('DATABASE_NAME', 'solar_prediction'),
            user=os.getenv('DATABASE_USER', 'postgres'),
            password=os.getenv('DATABASE_PASSWORD', 'postgres')
        )
        
        cursor = conn.cursor()
        
        # Create unified table with system_id
        create_unified_table = """
        CREATE TABLE IF NOT EXISTS solax_unified_data (
            id SERIAL PRIMARY KEY,
            timestamp TIMESTAMP NOT NULL,
            system_id INTEGER NOT NULL,
            system_name VARCHAR(50) NOT NULL,
            ac_power REAL,
            yield_today REAL,
            soc REAL,
            bat_power REAL,
            powerdc1 REAL,
            powerdc2 REAL,
            feedin_power REAL,
            consume_energy REAL,
            feedin_energy REAL,
            temperature REAL,
            cloud_cover REAL,
            data_quality_score REAL DEFAULT 1.0,
            is_outlier BOOLEAN DEFAULT FALSE,
            imputed_fields TEXT[],
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(timestamp, system_id)
        );
        
        CREATE INDEX IF NOT EXISTS idx_unified_timestamp_system ON solax_unified_data(timestamp, system_id);
        CREATE INDEX IF NOT EXISTS idx_unified_system_id ON solax_unified_data(system_id);
        """
        
        # Create enhanced normalized table
        create_normalized_table = """
        CREATE TABLE IF NOT EXISTS normalized_training_data_enhanced_v3 (
            id SERIAL PRIMARY KEY,
            timestamp TIMESTAMP NOT NULL,
            system_id INTEGER NOT NULL,
            ac_power REAL NOT NULL,
            
            -- Temporal features (cyclical encoding)
            hour_sin REAL,
            hour_cos REAL,
            day_sin REAL,
            day_cos REAL,
            month_sin REAL,
            month_cos REAL,
            season_normalized REAL,
            is_weekend_normalized REAL,
            
            -- System-aware features
            system_capacity_normalized REAL,
            consumption_profile_encoded INTEGER,
            grid_dependency_normalized REAL,
            self_sufficiency_normalized REAL,
            
            -- Power features (system-specific normalization)
            ac_power_normalized REAL,
            soc_normalized REAL,
            bat_power_normalized REAL,
            powerdc1_normalized REAL,
            powerdc2_normalized REAL,
            
            -- Weather features (robust normalization)
            ghi_robust_normalized REAL,
            dni_robust_normalized REAL,
            dhi_robust_normalized REAL,
            temperature_robust_normalized REAL,
            cloud_cover_robust_normalized REAL,
            
            -- Advanced features
            power_efficiency_ratio REAL,
            battery_utilization_rate REAL,
            weather_production_correlation REAL,
            system_performance_index REAL,
            
            -- Quality metrics
            data_quality_score REAL,
            feature_completeness REAL,
            temporal_consistency REAL,
            
            -- Metadata
            normalization_version VARCHAR(10) DEFAULT 'v3.0',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            
            UNIQUE(timestamp, system_id)
        );
        
        CREATE INDEX IF NOT EXISTS idx_normalized_v3_timestamp_system ON normalized_training_data_enhanced_v3(timestamp, system_id);
        """
        
        cursor.execute(create_unified_table)
        cursor.execute(create_normalized_table)
        conn.commit()
        
        logger.info("✅ System-aware tables created successfully")
        
        cursor.close()
        conn.close()
        
    def load_and_unify_data(self):
        """Load data from both systems and create unified dataset"""
        logger.info("Loading and unifying data from both systems...")
        
        load_dotenv()
        conn = psycopg2.connect(
            host=os.getenv('DATABASE_HOST', 'localhost'),
            database=os.getenv('DATABASE_NAME', 'solar_prediction'),
            user=os.getenv('DATABASE_USER', 'postgres'),
            password=os.getenv('DATABASE_PASSWORD', 'postgres')
        )
        
        # Load data from both systems
        query_system1 = """
        SELECT timestamp, ac_power, yield_today, soc, bat_power, powerdc1, powerdc2,
               feedin_power, consume_energy, feedin_energy, temperature, cloud_cover
        FROM solax_data
        WHERE timestamp >= '2024-03-01'
        AND ac_power IS NOT NULL
        ORDER BY timestamp
        """
        
        query_system2 = """
        SELECT timestamp, ac_power, yield_today, soc, bat_power, powerdc1, powerdc2,
               feedin_power, consume_energy, feedin_energy, temperature, cloud_cover
        FROM solax_data2
        WHERE timestamp >= '2024-03-01'
        AND ac_power IS NOT NULL
        ORDER BY timestamp
        """
        
        df_system1 = pd.read_sql(query_system1, conn)
        df_system2 = pd.read_sql(query_system2, conn)
        
        # Add system identification
        df_system1['system_id'] = 1
        df_system1['system_name'] = self.system_configs[1]['name']
        
        df_system2['system_id'] = 2
        df_system2['system_name'] = self.system_configs[2]['name']
        
        # Combine datasets
        df_unified = pd.concat([df_system1, df_system2], ignore_index=True)
        df_unified = df_unified.sort_values(['timestamp', 'system_id'])
        
        logger.info(f"Unified dataset created: {len(df_unified)} records")
        logger.info(f"System 1: {len(df_system1)} records")
        logger.info(f"System 2: {len(df_system2)} records")
        
        conn.close()
        return df_unified
    
    def detect_and_handle_outliers(self, df):
        """Detect and handle outliers using Isolation Forest"""
        logger.info("Detecting and handling outliers...")
        
        df_clean = df.copy()
        
        # Features for outlier detection
        outlier_features = ['ac_power', 'soc', 'bat_power', 'powerdc1', 'powerdc2']
        
        for system_id in [1, 2]:
            system_mask = df_clean['system_id'] == system_id
            system_data = df_clean[system_mask]
            
            if len(system_data) < 100:  # Skip if insufficient data
                continue
                
            # Prepare features for outlier detection
            feature_data = system_data[outlier_features].fillna(0)
            
            # Apply Isolation Forest
            iso_forest = IsolationForest(
                contamination=self.quality_thresholds['outlier_contamination'],
                random_state=42
            )
            
            outlier_labels = iso_forest.fit_predict(feature_data)
            is_outlier = outlier_labels == -1
            
            # Mark outliers
            df_clean.loc[system_mask, 'is_outlier'] = is_outlier
            
            outlier_count = is_outlier.sum()
            logger.info(f"System {system_id}: Detected {outlier_count} outliers ({outlier_count/len(system_data)*100:.1f}%)")
        
        return df_clean
    
    def intelligent_imputation(self, df):
        """Intelligent missing data imputation"""
        logger.info("Performing intelligent data imputation...")
        
        df_imputed = df.copy()
        
        # Features that can be imputed
        imputable_features = ['ac_power', 'soc', 'bat_power', 'powerdc1', 'powerdc2', 
                             'feedin_power', 'consume_energy', 'feedin_energy']
        
        for system_id in [1, 2]:
            system_mask = df_imputed['system_id'] == system_id
            system_data = df_imputed[system_mask]
            
            if len(system_data) < 50:  # Skip if insufficient data
                continue
            
            # Prepare data for imputation
            imputation_data = system_data[imputable_features].copy()
            
            # Track which fields were imputed
            missing_mask = imputation_data.isnull()
            
            # Use KNN imputation
            imputer = KNNImputer(n_neighbors=5)
            imputed_values = imputer.fit_transform(imputation_data)
            
            # Update the dataframe
            df_imputed.loc[system_mask, imputable_features] = imputed_values
            
            # Track imputed fields
            for idx, (_, row) in enumerate(system_data.iterrows()):
                imputed_fields = []
                for col in imputable_features:
                    if missing_mask.iloc[idx][col]:
                        imputed_fields.append(col)
                
                if imputed_fields:
                    df_imputed.loc[row.name, 'imputed_fields'] = imputed_fields
            
            imputed_count = missing_mask.sum().sum()
            logger.info(f"System {system_id}: Imputed {imputed_count} missing values")
        
        return df_imputed
    
    def calculate_data_quality_scores(self, df):
        """Calculate comprehensive data quality scores"""
        logger.info("Calculating data quality scores...")
        
        df_scored = df.copy()
        
        for system_id in [1, 2]:
            system_mask = df_scored['system_id'] == system_id
            system_data = df_scored[system_mask]
            
            quality_scores = []
            
            for _, row in system_data.iterrows():
                score = 1.0
                
                # Penalize outliers
                if row.get('is_outlier', False):
                    score *= 0.7
                
                # Penalize imputed data
                imputed_fields = row.get('imputed_fields', [])
                if imputed_fields:
                    score *= (1.0 - len(imputed_fields) * 0.05)  # 5% penalty per imputed field
                
                # Check data consistency
                if pd.notna(row['ac_power']) and pd.notna(row['powerdc1']) and pd.notna(row['powerdc2']):
                    dc_total = row['powerdc1'] + row['powerdc2']
                    if dc_total > 0:
                        efficiency = row['ac_power'] / dc_total
                        if efficiency < 0.5 or efficiency > 1.1:  # Unrealistic efficiency
                            score *= 0.8
                
                # Check reasonable ranges
                if pd.notna(row['soc']):
                    if row['soc'] < 0 or row['soc'] > 100:
                        score *= 0.6
                
                quality_scores.append(max(0.0, min(1.0, score)))
            
            df_scored.loc[system_mask, 'data_quality_score'] = quality_scores
            
            avg_quality = np.mean(quality_scores)
            logger.info(f"System {system_id}: Average data quality score: {avg_quality:.3f}")
        
        return df_scored

    def system_aware_normalization(self, df):
        """Advanced system-aware normalization"""
        logger.info("Performing system-aware normalization...")

        df_normalized = df.copy()

        # Temporal features with cyclical encoding
        df_normalized['hour_sin'] = np.sin(2 * np.pi * df['timestamp'].dt.hour / 24)
        df_normalized['hour_cos'] = np.cos(2 * np.pi * df['timestamp'].dt.hour / 24)
        df_normalized['day_sin'] = np.sin(2 * np.pi * df['timestamp'].dt.dayofyear / 365)
        df_normalized['day_cos'] = np.cos(2 * np.pi * df['timestamp'].dt.dayofyear / 365)
        df_normalized['month_sin'] = np.sin(2 * np.pi * df['timestamp'].dt.month / 12)
        df_normalized['month_cos'] = np.cos(2 * np.pi * df['timestamp'].dt.month / 12)

        # Season and weekend
        df_normalized['season_normalized'] = ((df['timestamp'].dt.month % 12 + 3) // 3) / 4.0
        df_normalized['is_weekend_normalized'] = df['timestamp'].dt.dayofweek.isin([5, 6]).astype(float)

        # System-specific features
        for system_id in [1, 2]:
            system_mask = df_normalized['system_id'] == system_id
            config = self.system_configs[system_id]

            df_normalized.loc[system_mask, 'system_capacity_normalized'] = config['capacity_kw'] / 15.0  # Normalize to max 15kW
            df_normalized.loc[system_mask, 'consumption_profile_encoded'] = 1 if config['consumption_profile'] == 'low_steady' else 2
            df_normalized.loc[system_mask, 'grid_dependency_normalized'] = config['grid_dependency']
            df_normalized.loc[system_mask, 'self_sufficiency_normalized'] = config['self_sufficiency']

        # System-specific power normalization
        power_features = ['ac_power', 'soc', 'bat_power', 'powerdc1', 'powerdc2']

        for system_id in [1, 2]:
            system_mask = df_normalized['system_id'] == system_id
            system_data = df_normalized[system_mask]

            if len(system_data) == 0:
                continue

            # Custom normalization ranges per system
            normalization_ranges = {
                'ac_power': (0, self.system_configs[system_id]['capacity_kw'] * 1000),  # kW to W
                'soc': (0, 100),
                'bat_power': (-6000, 6000),  # Battery can charge/discharge
                'powerdc1': (0, self.system_configs[system_id]['capacity_kw'] * 500),  # Half capacity per string
                'powerdc2': (0, self.system_configs[system_id]['capacity_kw'] * 500)
            }

            for feature in power_features:
                if feature in system_data.columns:
                    min_val, max_val = normalization_ranges[feature]
                    normalized_values = (system_data[feature] - min_val) / (max_val - min_val)
                    normalized_values = np.clip(normalized_values, 0, 1)
                    df_normalized.loc[system_mask, f'{feature}_normalized'] = normalized_values

        # Weather features with robust normalization
        weather_features = ['ghi', 'dni', 'dhi', 'temperature', 'cloud_cover']

        # Load weather data if available
        try:
            load_dotenv()
            conn = psycopg2.connect(
                host=os.getenv('DATABASE_HOST', 'localhost'),
                database=os.getenv('DATABASE_NAME', 'solar_prediction'),
                user=os.getenv('DATABASE_USER', 'postgres'),
                password=os.getenv('DATABASE_PASSWORD', 'postgres')
            )

            weather_query = """
            SELECT timestamp, ghi, dni, dhi, temperature, cloud_cover
            FROM cams_radiation_data
            WHERE timestamp >= '2024-03-01'
            ORDER BY timestamp
            """

            df_weather = pd.read_sql(weather_query, conn)
            conn.close()

            # Merge weather data
            df_normalized = pd.merge(
                df_normalized,
                df_weather,
                on='timestamp',
                how='left',
                suffixes=('', '_weather')
            )

            # Use robust scaler for weather features
            for feature in weather_features:
                if f'{feature}_weather' in df_normalized.columns:
                    weather_data = df_normalized[f'{feature}_weather'].dropna()
                    if len(weather_data) > 10:
                        scaler = RobustScaler()
                        normalized_values = scaler.fit_transform(weather_data.values.reshape(-1, 1)).flatten()

                        # Map back to original dataframe
                        df_normalized[f'{feature}_robust_normalized'] = np.nan
                        df_normalized.loc[weather_data.index, f'{feature}_robust_normalized'] = normalized_values

        except Exception as e:
            logger.warning(f"Could not load weather data: {e}")
            # Use default values for weather features
            for feature in weather_features:
                df_normalized[f'{feature}_robust_normalized'] = 0.5  # Default middle value

        return df_normalized

    def calculate_advanced_features(self, df):
        """Calculate advanced derived features"""
        logger.info("Calculating advanced features...")

        df_advanced = df.copy()

        # Power efficiency ratio
        df_advanced['power_efficiency_ratio'] = 0.0
        mask = (df_advanced['powerdc1_normalized'] + df_advanced['powerdc2_normalized']) > 0
        df_advanced.loc[mask, 'power_efficiency_ratio'] = (
            df_advanced.loc[mask, 'ac_power_normalized'] /
            (df_advanced.loc[mask, 'powerdc1_normalized'] + df_advanced.loc[mask, 'powerdc2_normalized'])
        )

        # Battery utilization rate
        df_advanced['battery_utilization_rate'] = np.abs(df_advanced['bat_power_normalized'])

        # Weather-production correlation (simplified)
        if 'ghi_robust_normalized' in df_advanced.columns:
            df_advanced['weather_production_correlation'] = (
                df_advanced['ghi_robust_normalized'] * df_advanced['ac_power_normalized']
            )
        else:
            df_advanced['weather_production_correlation'] = df_advanced['ac_power_normalized']

        # System performance index
        df_advanced['system_performance_index'] = (
            df_advanced['ac_power_normalized'] * 0.4 +
            df_advanced['power_efficiency_ratio'] * 0.3 +
            df_advanced['soc_normalized'] * 0.2 +
            df_advanced['data_quality_score'] * 0.1
        )

        # Feature completeness
        feature_columns = [col for col in df_advanced.columns if col.endswith('_normalized')]
        df_advanced['feature_completeness'] = (
            df_advanced[feature_columns].notna().sum(axis=1) / len(feature_columns)
        )

        # Temporal consistency (simplified)
        df_advanced['temporal_consistency'] = 1.0  # Default high consistency

        return df_advanced

    def save_to_database(self, df):
        """Save processed data to database"""
        logger.info("Saving processed data to database...")

        load_dotenv()
        conn = psycopg2.connect(
            host=os.getenv('DATABASE_HOST', 'localhost'),
            database=os.getenv('DATABASE_NAME', 'solar_prediction'),
            user=os.getenv('DATABASE_USER', 'postgres'),
            password=os.getenv('DATABASE_PASSWORD', 'postgres')
        )

        cursor = conn.cursor()

        # Clear existing data
        cursor.execute("DELETE FROM solax_unified_data")
        cursor.execute("DELETE FROM normalized_training_data_enhanced_v3")

        # Save unified data
        unified_columns = [
            'timestamp', 'system_id', 'system_name', 'ac_power', 'yield_today',
            'soc', 'bat_power', 'powerdc1', 'powerdc2', 'feedin_power',
            'consume_energy', 'feedin_energy', 'temperature', 'cloud_cover',
            'data_quality_score', 'is_outlier'
        ]

        for _, row in df.iterrows():
            values = [row.get(col) for col in unified_columns]
            placeholders = ', '.join(['%s'] * len(values))

            insert_query = f"""
            INSERT INTO solax_unified_data ({', '.join(unified_columns)})
            VALUES ({placeholders})
            ON CONFLICT (timestamp, system_id) DO UPDATE SET
            data_quality_score = EXCLUDED.data_quality_score,
            is_outlier = EXCLUDED.is_outlier
            """

            cursor.execute(insert_query, values)

        # Save normalized data
        normalized_columns = [
            'timestamp', 'system_id', 'ac_power',
            'hour_sin', 'hour_cos', 'day_sin', 'day_cos', 'month_sin', 'month_cos',
            'season_normalized', 'is_weekend_normalized',
            'system_capacity_normalized', 'consumption_profile_encoded',
            'grid_dependency_normalized', 'self_sufficiency_normalized',
            'ac_power_normalized', 'soc_normalized', 'bat_power_normalized',
            'powerdc1_normalized', 'powerdc2_normalized',
            'ghi_robust_normalized', 'dni_robust_normalized', 'dhi_robust_normalized',
            'temperature_robust_normalized', 'cloud_cover_robust_normalized',
            'power_efficiency_ratio', 'battery_utilization_rate',
            'weather_production_correlation', 'system_performance_index',
            'data_quality_score', 'feature_completeness', 'temporal_consistency'
        ]

        for _, row in df.iterrows():
            values = [row.get(col, 0.0) for col in normalized_columns]
            placeholders = ', '.join(['%s'] * len(values))

            insert_query = f"""
            INSERT INTO normalized_training_data_enhanced_v3 ({', '.join(normalized_columns)})
            VALUES ({placeholders})
            ON CONFLICT (timestamp, system_id) DO UPDATE SET
            data_quality_score = EXCLUDED.data_quality_score
            """

            cursor.execute(insert_query, values)

        conn.commit()
        cursor.close()
        conn.close()

        logger.info(f"✅ Saved {len(df)} records to database")

    def generate_quality_report(self, df):
        """Generate comprehensive data quality report"""
        logger.info("Generating data quality report...")

        report = {
            'timestamp': datetime.now().isoformat(),
            'total_records': len(df),
            'systems': {}
        }

        for system_id in [1, 2]:
            system_data = df[df['system_id'] == system_id]
            system_name = self.system_configs[system_id]['name']

            if len(system_data) == 0:
                continue

            system_report = {
                'name': system_name,
                'record_count': len(system_data),
                'date_range': {
                    'start': system_data['timestamp'].min().isoformat(),
                    'end': system_data['timestamp'].max().isoformat()
                },
                'data_quality': {
                    'average_score': float(system_data['data_quality_score'].mean()),
                    'outlier_percentage': float((system_data['is_outlier'] == True).sum() / len(system_data) * 100),
                    'missing_data_percentage': float(system_data.isnull().sum().sum() / (len(system_data) * len(system_data.columns)) * 100)
                },
                'feature_statistics': {
                    'ac_power_avg': float(system_data['ac_power'].mean()) if 'ac_power' in system_data else 0,
                    'soc_avg': float(system_data['soc'].mean()) if 'soc' in system_data else 0,
                    'feature_completeness_avg': float(system_data['feature_completeness'].mean()) if 'feature_completeness' in system_data else 0
                }
            }

            report['systems'][system_id] = system_report

        # Save report
        report_path = self.output_dir / f"data_quality_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2)

        logger.info(f"✅ Quality report saved: {report_path}")
        return report

def main():
    """Main execution function"""
    logger.info("🚀 ENHANCED DATA QUALITY SYSTEM")
    logger.info("="*80)

    try:
        # Initialize system
        quality_system = EnhancedDataQualitySystem()

        # Create system-aware tables
        quality_system.create_system_aware_tables()

        # Load and unify data
        df_unified = quality_system.load_and_unify_data()

        # Data quality pipeline
        df_clean = quality_system.detect_and_handle_outliers(df_unified)
        df_imputed = quality_system.intelligent_imputation(df_clean)
        df_scored = quality_system.calculate_data_quality_scores(df_imputed)

        # Advanced normalization
        df_normalized = quality_system.system_aware_normalization(df_scored)
        df_final = quality_system.calculate_advanced_features(df_normalized)

        # Save to database
        quality_system.save_to_database(df_final)

        # Generate quality report
        report = quality_system.generate_quality_report(df_final)

        # Summary
        logger.info("\n" + "="*80)
        logger.info("🎯 ENHANCED DATA QUALITY SYSTEM COMPLETE!")
        logger.info(f"✅ Processed {len(df_final)} total records")
        logger.info(f"✅ System 1: {len(df_final[df_final['system_id']==1])} records")
        logger.info(f"✅ System 2: {len(df_final[df_final['system_id']==2])} records")
        logger.info(f"✅ Average quality score: {df_final['data_quality_score'].mean():.3f}")
        logger.info(f"✅ Feature completeness: {df_final['feature_completeness'].mean():.3f}")
        logger.info("🚀 Enhanced Model v3 ready for training with high-quality data!")

        return True

    except Exception as e:
        logger.error(f"❌ Enhanced data quality system failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
