#!/usr/bin/env python3
"""
NASA POWER Data Collector - Fixed Version with Correct Date Logic
Prevents fetching future dates and properly handles data gaps
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import psycopg2
from tenacity import retry, wait_exponential, stop_after_attempt
import time
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class NASAPowerCollectorFixed:
    """NASA POWER data collector with correct date logic and realistic expectations"""
    
    def __init__(self):
        self.base_url = "https://power.larc.nasa.gov/api/temporal/hourly/point"
        self.latitude = 38.141348260997596
        self.longitude = 24.0071653937747
        
        # NASA POWER parameters mapping to database columns
        self.parameters = {
            'ALLSKY_SFC_SW_DWN': 'ghi',                    # Global Horizontal Irradiance
            'T2M': 'temperature',                          # Temperature at 2 Meters
            'WS10M': 'wind_speed',                         # Wind Speed at 10 Meters
            'RH2M': 'relative_humidity',                   # Relative Humidity at 2 Meters
            'PS': 'surface_pressure',                      # Surface Pressure
            'CLRSKY_SFC_SW_DWN': 'clear_sky_ghi',         # Clear Sky GHI
            'ALLSKY_KT': 'clearness_index'                 # Clearness Index
        }
        
        # Updated database configuration
        self.db_config = {
            'host': os.getenv('DATABASE_HOST', 'localhost'),
            'port': int(os.getenv('DATABASE_PORT', '5432')),
            'database': os.getenv('DATABASE_NAME', 'solar_prediction'),
            'user': os.getenv('DATABASE_USER', 'postgres'),
            'password': os.getenv('DATABASE_PASSWORD', 'postgres')
        }
        
        # Session for connection pooling
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Solar-Prediction-Project/1.0 (Fixed Version)'
        })
    
    def get_last_record_date(self, table_name: str) -> datetime.date:
        """Get the date of the last record with actual data - FIXED to check actual last date"""
        
        try:
            conn = psycopg2.connect(**self.db_config)
            
            with conn.cursor() as cur:
                # Get the ACTUAL last record date, not just where ghi is not null
                cur.execute(f"""
                    SELECT MAX(DATE(timestamp)) 
                    FROM {table_name} 
                    WHERE source = 'nasa_power_api'
                """)
                
                result = cur.fetchone()[0]
                
            conn.close()
            
            if result:
                logger.info(f"✅ Last NASA POWER record found: {result}")
                return result + timedelta(days=1)
            else:
                # Start from a reasonable historical date
                default_date = datetime(2024, 1, 1).date()
                logger.info(f"📅 No NASA POWER data found, starting from: {default_date}")
                return default_date
                
        except Exception as e:
            logger.error(f"❌ Error getting last record date: {e}")
            return datetime(2024, 1, 1).date()
    
    def find_data_gaps(self, table_name: str, start_date: datetime.date, end_date: datetime.date) -> list:
        """Find gaps in NASA POWER data - FIXED to not look for future data"""
        
        # Ensure we don't look for future data
        today = datetime.now().date()
        if end_date > today:
            end_date = today
            logger.info(f"📅 Adjusted end date to today: {end_date}")
        
        if start_date > end_date:
            logger.info("📅 Start date is after end date, no gaps to find")
            return []
        
        try:
            conn = psycopg2.connect(**self.db_config)
            
            with conn.cursor() as cur:
                cur.execute(f"""
                    SELECT DATE(timestamp) as record_date
                    FROM {table_name}
                    WHERE source = 'nasa_power_api' 
                    AND DATE(timestamp) BETWEEN %s AND %s
                    ORDER BY record_date
                """, (start_date, end_date))
                
                existing_dates = {row[0] for row in cur.fetchall()}
                
            conn.close()
            
            # Generate all dates in range
            all_dates = set()
            current = start_date
            while current <= end_date:
                all_dates.add(current)
                current += timedelta(days=1)
            
            # Find missing dates
            missing_dates = sorted(all_dates - existing_dates)
            
            if missing_dates:
                logger.info(f"📊 Found {len(missing_dates)} missing NASA POWER dates")
                # Limit gaps to reasonable number
                if len(missing_dates) > 30:  # More than 1 month
                    logger.info(f"⚠️ Too many gaps ({len(missing_dates)}), limiting to most recent 30 days")
                    missing_dates = missing_dates[-30:]  # Take the most recent 30 missing days
                return missing_dates
            else:
                logger.info("✅ No NASA POWER data gaps found")
                return []
                
        except Exception as e:
            logger.error(f"❌ Error finding NASA POWER data gaps: {e}")
            return []
    
    @retry(wait=wait_exponential(multiplier=1, min=4, max=120), stop=stop_after_attempt(3))
    def fetch_nasa_power_chunk(self, start_date: datetime.date, end_date: datetime.date) -> dict:
        """Fetch NASA POWER data with retry logic"""
        
        logger.info(f"🛰️ Fetching NASA POWER data: {start_date} to {end_date}")
        
        # Format dates for NASA POWER API
        start_str = start_date.strftime('%Y%m%d')
        end_str = end_date.strftime('%Y%m%d')
        
        # Build parameters string
        parameters_str = ','.join(self.parameters.keys())
        
        params = {
            'parameters': parameters_str,
            'community': 'RE',
            'longitude': self.longitude,
            'latitude': self.latitude,
            'start': start_str,
            'end': end_str,
            'format': 'JSON',
            'time-standard': 'LST'
        }
        
        logger.info(f"📡 NASA POWER request: {len(self.parameters)} parameters")
        logger.info(f"📍 Location: {self.latitude}, {self.longitude}")
        logger.info(f"📅 Date range: {start_str} to {end_str}")
        
        try:
            response = self.session.get(
                self.base_url,
                params=params,
                timeout=60
            )
            
            if response.status_code == 200:
                data = response.json()
                logger.info(f"✅ NASA POWER data retrieved successfully")
                return {'status': 'success', 'data': data}
            else:
                logger.error(f"❌ NASA POWER API returned status {response.status_code}")
                return {'status': 'failed', 'error': f"HTTP {response.status_code}"}
                
        except Exception as e:
            logger.error(f"❌ NASA POWER retrieval failed: {e}")
            raise
    
    def process_nasa_power_chunk(self, api_response: dict, start_date: datetime.date, end_date: datetime.date) -> pd.DataFrame:
        """Process NASA POWER data with CORRECT structure handling"""
        
        logger.info("🔧 Processing NASA POWER data...")
        
        try:
            if api_response['status'] != 'success':
                logger.error(f"API response indicates failure: {api_response.get('error', 'Unknown error')}")
                return pd.DataFrame()
            
            api_data = api_response['data']
            
            if 'properties' not in api_data or 'parameter' not in api_data['properties']:
                logger.warning("No parameter data in API response")
                return pd.DataFrame()
            
            parameters_data = api_data['properties']['parameter']
            records = []
            
            # Process each day in the date range
            current_date = start_date
            while current_date <= end_date:
                # Process each hour of the day
                for hour in range(24):
                    # NASA POWER uses YYYYMMDDHH format as keys
                    datetime_key = current_date.strftime('%Y%m%d') + f"{hour:02d}"
                    
                    # Create timestamp
                    timestamp = datetime.combine(current_date, datetime.min.time()) + timedelta(hours=hour)
                    
                    # Build record
                    record = {
                        'timestamp': timestamp,
                        'latitude': self.latitude,
                        'longitude': self.longitude,
                        'source': 'nasa_power_api',
                        'ingestion_run_id': f"nasa_power_fixed_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    }
                    
                    # Extract parameter values using CORRECT structure
                    for nasa_param, db_column in self.parameters.items():
                        value = None
                        
                        if nasa_param in parameters_data:
                            param_data = parameters_data[nasa_param]
                            
                            # NASA POWER structure: {PARAMETER: {YYYYMMDDHH: value}}
                            if datetime_key in param_data:
                                raw_value = param_data[datetime_key]
                                
                                # Handle NASA POWER missing value indicator
                                if raw_value != -999.0 and not pd.isna(raw_value):
                                    value = float(raw_value)
                                    
                        record[db_column] = value
                    
                    # Calculate derived parameters only if base values exist
                    if record.get('temperature') is not None and record.get('ghi') is not None:
                        ambient_temp = record['temperature']
                        ghi = record['ghi']
                        wind_speed = record.get('wind_speed', 2) or 2
                        
                        # Module temperature estimation
                        if ghi > 0:
                            noct = 45
                            module_temp = ambient_temp + (ghi / 800) * (noct - 20) * (1 - wind_speed * 0.05)
                            record['module_temperature'] = module_temp
                            
                            # Temperature efficiency factor
                            temp_coeff = -0.004
                            record['temperature_efficiency_factor'] = 1 + temp_coeff * (module_temp - 25)
                    
                    if record.get('wind_speed') is not None:
                        wind_speed = record['wind_speed']
                        record['wind_cooling_factor'] = 1 + (wind_speed * 0.02)
                    
                    if record.get('ghi') is not None and record.get('clear_sky_ghi') is not None:
                        hour_angle = abs(hour - 12) * 15
                        zenith_angle = min(85, hour_angle)
                        air_mass = 1 / max(0.1, np.cos(np.radians(zenith_angle)))
                        record['air_mass'] = air_mass
                    
                    records.append(record)
                
                current_date += timedelta(days=1)
            
            df = pd.DataFrame(records)
            
            # Enhanced logging
            if not df.empty:
                non_null_ghi = df['ghi'].dropna()
                non_null_temp = df['temperature'].dropna()
                
                logger.info(f"✅ NASA POWER processing completed: {len(df)} total records")
                logger.info(f"   Non-null GHI records: {len(non_null_ghi)}")
                logger.info(f"   Non-null temperature records: {len(non_null_temp)}")
                
                if len(non_null_ghi) > 0:
                    logger.info(f"   GHI range: {non_null_ghi.min():.1f} - {non_null_ghi.max():.1f} W/m²")
                else:
                    logger.warning("   No valid GHI values found!")
                    
                if len(non_null_temp) > 0:
                    logger.info(f"   Temperature range: {non_null_temp.min():.1f} - {non_null_temp.max():.1f} °C")
                else:
                    logger.warning("   No valid temperature values found!")
            
            return df
            
        except Exception as e:
            logger.error(f"❌ NASA POWER processing failed: {e}")
            raise
    
    def save_to_database(self, df: pd.DataFrame, table_name: str) -> int:
        """Save NASA POWER data to database"""
        
        if df.empty:
            logger.info("📭 No NASA POWER data to save")
            return 0
        
        logger.info(f"💾 Saving {len(df)} NASA POWER records to {table_name}...")
        
        try:
            conn = psycopg2.connect(**self.db_config)
            records_saved = 0
            records_skipped = 0
            
            for _, row in df.iterrows():
                try:
                    # Check if record exists
                    with conn.cursor() as cur:
                        cur.execute(f"""
                            SELECT COUNT(*) FROM {table_name} 
                            WHERE timestamp = %s AND source = 'nasa_power_api'
                        """, (row['timestamp'],))
                        
                        if cur.fetchone()[0] > 0:
                            records_skipped += 1
                            continue
                    
                    # Insert with all available columns
                    with conn.cursor() as cur:
                        cur.execute(f"""
                            INSERT INTO {table_name} (
                                timestamp, latitude, longitude, ghi, temperature, 
                                wind_speed, relative_humidity, surface_pressure, 
                                clear_sky_ghi, clearness_index, module_temperature,
                                temperature_efficiency_factor, wind_cooling_factor, 
                                air_mass, source, ingestion_run_id
                            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """, (
                            row['timestamp'],
                            row['latitude'],
                            row['longitude'],
                            row.get('ghi'),
                            row.get('temperature'),
                            row.get('wind_speed'),
                            row.get('relative_humidity'),
                            row.get('surface_pressure'),
                            row.get('clear_sky_ghi'),
                            row.get('clearness_index'),
                            row.get('module_temperature'),
                            row.get('temperature_efficiency_factor'),
                            row.get('wind_cooling_factor'),
                            row.get('air_mass'),
                            row['source'],
                            row['ingestion_run_id']
                        ))
                    
                    records_saved += 1
                    
                except Exception as e:
                    logger.warning(f"Error saving record {row.get('timestamp', 'unknown')}: {e}")
                    continue
            
            conn.commit()
            conn.close()
            
            logger.info(f"✅ Database update completed:")
            logger.info(f"   → Records saved: {records_saved}")
            logger.info(f"   → Records skipped (duplicates): {records_skipped}")
            
            return records_saved
            
        except Exception as e:
            logger.error(f"❌ Database save failed: {e}")
            return 0
    
    def update_database(self, table_name: str = 'nasa_power_data'):
        """Main update method for NASA POWER data collection - FIXED date logic"""
        
        print("🛰️ NASA POWER Data Updater (Fixed Date Logic)")
        print("=" * 50)
        
        last_date = self.get_last_record_date(table_name)
        
        # NASA POWER has about 1-2 days latency, and we can't get future data
        today = datetime.now().date()
        target_date = today - timedelta(days=2)  # Conservative approach
        
        print(f"📅 Last record date: {last_date - timedelta(days=1)}")
        print(f"📅 Target end date: {target_date}")
        print(f"📅 Today's date: {today}")
        
        if last_date > target_date:
            print("✅ Database is up to date!")
            return True
        
        # Find any gaps in existing data - but only look back reasonably
        gap_search_start = max(last_date - timedelta(days=30), datetime(2024, 1, 1).date())
        gaps = self.find_data_gaps(table_name, gap_search_start, target_date)
        missing_days = (target_date - last_date).days + 1
        total_work = len(gaps) + missing_days
        
        print(f"📊 Data Analysis (Realistic Date Range):")
        print(f"   → Missing recent days: {missing_days}")
        print(f"   → Data gaps found (last 30 days): {len(gaps)}")
        print(f"   → Total work: {total_work} days")
        
        if total_work == 0:
            print("✅ No work needed!")
            return True
        
        # Process data gaps first
        total_saved = 0
        successful_chunks = 0
        failed_chunks = 0
        
        if gaps:
            print(f"\n🔧 Processing {len(gaps)} recent data gaps...")
            for gap_date in gaps:
                print(f"🔄 Processing gap: {gap_date}")
                try:
                    api_response = self.fetch_nasa_power_chunk(gap_date, gap_date)
                    df = self.process_nasa_power_chunk(api_response, gap_date, gap_date)
                    records_saved = self.save_to_database(df, table_name)
                    total_saved += records_saved
                    successful_chunks += 1
                    
                    print(f"✅ Gap completed: {records_saved} records saved")
                    
                except Exception as e:
                    print(f"❌ Gap failed: {e}")
                    failed_chunks += 1
                
                # Conservative rate limiting
                time.sleep(5)
        
        # Process new data in chunks
        if missing_days > 0:
            print(f"\n🔄 Processing {missing_days} new days...")
            chunk_size = 7  # 7 days at a time
            current_start = last_date
            
            while current_start <= target_date:
                current_end = min(current_start + timedelta(days=chunk_size-1), target_date)
                
                print(f"🔄 Processing chunk: {current_start} to {current_end}")
                
                try:
                    api_response = self.fetch_nasa_power_chunk(current_start, current_end)
                    df = self.process_nasa_power_chunk(api_response, current_start, current_end)
                    records_saved = self.save_to_database(df, table_name)
                    total_saved += records_saved
                    successful_chunks += 1
                    
                    print(f"✅ Chunk completed: {records_saved} records saved")
                    
                except Exception as e:
                    print(f"❌ Chunk failed: {e}")
                    failed_chunks += 1
                
                current_start = current_end + timedelta(days=1)
                
                if current_start <= target_date:
                    print("⏳ Waiting 5 seconds before next chunk...")
                    time.sleep(5)
        
        # Final summary
        print(f"\n📊 NASA POWER Collection Summary:")
        print(f"   → Period processed: {last_date} to {target_date}")
        print(f"   → Total work: {total_work} days")
        print(f"   → Successful chunks: {successful_chunks}")
        print(f"   → Failed chunks: {failed_chunks}")
        print(f"   → Total records saved: {total_saved}")
        
        if successful_chunks > 0:
            print(f"\n✅ NASA POWER database update completed successfully!")
            print(f"   → Database now contains NASA POWER data up to {target_date}")
            return True
        else:
            print(f"\n❌ NASA POWER database update failed!")
            print(f"   → No data was successfully processed")
            return False

def main():
    """Main function"""
    try:
        collector = NASAPowerCollectorFixed()
        print("🛰️ NASA POWER Fixed Collector initialized successfully!")
        
        # Run the actual data collection
        success = collector.update_database()
        return success
    except Exception as e:
        logger.error(f"❌ NASA POWER collection failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

