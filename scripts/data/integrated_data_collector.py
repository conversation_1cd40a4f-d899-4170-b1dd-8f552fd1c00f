#!/usr/bin/env python3
"""
Integrated Data Collector - Fixed Version
Collects and integrates data from multiple sources with correct database configuration
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import psycopg2
import time

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class IntegratedDataCollector:
    """Integrated data collector with correct database configuration"""
    
    def __init__(self):
        # Updated database configuration to match system
        self.db_config = {
            'host': os.getenv('DB_HOST', 'localhost'),
            'port': int(os.getenv('DB_PORT', '5432')),  # Correct port for Docker
            'database': os.getenv('DB_NAME', 'solar_prediction'),
            'user': os.getenv('DB_USER', 'postgres'),
            'password': os.getenv('DB_PASSWORD', 'postgres')
        }
        
        # Data sources configuration
        self.data_sources = {
            'era5': {
                'table': 'era5_data',
                'source_filter': 'era5_reanalysis',
                'variables': {
                    'ghi': 'surface_solar_radiation',
                    'temperature': 'temperature_2m',
                    'cloud_cover': 'total_cloud_cover',
                    'humidity': 'relative_humidity',
                    'wind_speed': 'wind_speed_10m',
                    'pressure': 'surface_pressure'
                }
            },
            'nasa_power': {
                'table': 'nasa_power_data',
                'source_filter': 'nasa_power_api',
                'variables': {
                    'ghi': 'ghi',
                    'temperature': 'temperature',
                    'cloud_cover': None,  # Not available in NASA POWER
                    'humidity': 'relative_humidity',
                    'wind_speed': 'wind_speed',
                    'pressure': 'surface_pressure',
                    'clear_sky_ghi': 'clear_sky_ghi',
                    'clearness_index': 'clearness_index'
                }
            },
            'cams_aerosol': {
                'table': 'cams_aerosol_data',
                'source_filter': 'cams_aerosol',
                'variables': {
                    'aerosol_optical_depth': 'total_aerosol_optical_depth',
                    'dust_aerosol': 'dust_aerosol_optical_depth'
                }
            },
            'satellite': {
                'table': 'satellite_data',
                'source_filter': 'sentinel_satellite',
                'variables': {
                    'cloud_cover': 'cloud_cover',
                    'solar_irradiance': 'solar_irradiance'
                }
            }
        }
    
    def test_database_connection(self) -> bool:
        """Test database connection with correct configuration"""
        
        try:
            conn = psycopg2.connect(**self.db_config)
            with conn.cursor() as cur:
                cur.execute("SELECT version();")
                version = cur.fetchone()[0]
                logger.info(f"✅ Database connection successful: {version}")
            conn.close()
            return True
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            return False
    
    def get_available_data(self, variable: str, start_time: datetime, end_time: datetime) -> dict:
        """Get available data for a specific variable from all sources"""
        
        available_data = {}
        
        try:
            conn = psycopg2.connect(**self.db_config)
            
            for source_name, source_config in self.data_sources.items():
                if variable not in source_config['variables']:
                    continue
                
                column_name = source_config['variables'][variable]
                if column_name is None:
                    continue
                
                table_name = source_config['table']
                source_filter = source_config['source_filter']
                
                with conn.cursor() as cur:
                    query = f"""
                        SELECT COUNT(*) as count,
                               MIN(timestamp) as min_time,
                               MAX(timestamp) as max_time,
                               AVG({column_name}) as avg_value
                        FROM {table_name}
                        WHERE source = %s
                        AND timestamp BETWEEN %s AND %s
                        AND {column_name} IS NOT NULL
                    """
                    
                    cur.execute(query, (source_filter, start_time, end_time))
                    result = cur.fetchone()
                    
                    if result and result[0] > 0:
                        available_data[source_name] = {
                            'count': result[0],
                            'min_time': result[1],
                            'max_time': result[2],
                            'avg_value': float(result[3]) if result[3] else None,
                            'table': table_name,
                            'column': column_name
                        }
            
            conn.close()
            return available_data
            
        except Exception as e:
            logger.error(f"❌ Error getting available data for {variable}: {e}")
            return {}
    
    def collect_integrated_data(self, hours_back: int = 24) -> dict:
        """Collect and integrate data from multiple sources"""
        
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=hours_back)
        
        print(f"🔄 Integrated Data Collection - {datetime.now()}")
        print("=" * 60)
        print(f"📅 Collection period: {start_time.strftime('%Y-%m-%d %H:%M')} to {end_time.strftime('%Y-%m-%d %H:%M')}")
        
        # Test database connection first
        if not self.test_database_connection():
            print("❌ Database connection failed - cannot proceed")
            return {'status': 'failed', 'error': 'Database connection failed'}
        
        # Variables to integrate
        variables = ['ghi', 'temperature', 'cloud_cover', 'humidity', 'wind_speed']
        print(f"🔧 Integrating variables: {', '.join(variables)}")
        
        integration_results = {}
        
        print(f"\n📊 Data Availability Check:")
        
        for variable in variables:
            print(f"\n🔍 Checking {variable}:")
            
            try:
                available_data = self.get_available_data(variable, start_time, end_time)
                
                if available_data:
                    integration_results[variable] = available_data
                    
                    for source, data in available_data.items():
                        print(f"   ✅ {source}: {data['count']} records, avg: {data['avg_value']:.2f}")
                else:
                    print(f"   ❌ No data available for {variable}")
                    integration_results[variable] = {}
                    
            except Exception as e:
                print(f"   ❌ Error checking {variable}: {e}")
                integration_results[variable] = {}
        
        # Summary
        total_sources = sum(len(data) for data in integration_results.values())
        total_variables = len([v for v in integration_results.values() if v])
        
        print(f"\n📈 Integration Summary:")
        print(f"   → Variables with data: {total_variables}/{len(variables)}")
        print(f"   → Total data sources: {total_sources}")
        print(f"   → Collection period: {hours_back} hours")
        
        if total_sources > 0:
            print(f"\n✅ Integrated data collection completed successfully!")
            return {
                'status': 'success',
                'variables': integration_results,
                'summary': {
                    'variables_with_data': total_variables,
                    'total_sources': total_sources,
                    'collection_period_hours': hours_back,
                    'start_time': start_time.isoformat(),
                    'end_time': end_time.isoformat()
                }
            }
        else:
            print(f"\n⚠️ No integrated data available for the specified period")
            return {
                'status': 'no_data',
                'variables': integration_results,
                'summary': {
                    'variables_with_data': 0,
                    'total_sources': 0,
                    'collection_period_hours': hours_back
                }
            }
    
    def get_data_quality_report(self) -> dict:
        """Generate a data quality report for all sources"""
        
        print(f"\n📊 Data Quality Report")
        print("=" * 40)
        
        if not self.test_database_connection():
            return {'status': 'failed', 'error': 'Database connection failed'}
        
        quality_report = {}
        
        try:
            conn = psycopg2.connect(**self.db_config)
            
            for source_name, source_config in self.data_sources.items():
                table_name = source_config['table']
                source_filter = source_config['source_filter']
                
                with conn.cursor() as cur:
                    # Get basic statistics
                    cur.execute(f"""
                        SELECT 
                            COUNT(*) as total_records,
                            MIN(timestamp) as first_record,
                            MAX(timestamp) as last_record,
                            COUNT(DISTINCT DATE(timestamp)) as unique_days
                        FROM {table_name}
                        WHERE source = %s
                    """, (source_filter,))
                    
                    result = cur.fetchone()
                    
                    if result and result[0] > 0:
                        quality_report[source_name] = {
                            'total_records': result[0],
                            'first_record': result[1].isoformat() if result[1] else None,
                            'last_record': result[2].isoformat() if result[2] else None,
                            'unique_days': result[3],
                            'table': table_name,
                            'source_filter': source_filter
                        }
                        
                        print(f"✅ {source_name}:")
                        print(f"   → Records: {result[0]:,}")
                        print(f"   → Period: {result[1]} to {result[2]}")
                        print(f"   → Days: {result[3]}")
                    else:
                        print(f"❌ {source_name}: No data available")
                        quality_report[source_name] = {'status': 'no_data'}
            
            conn.close()
            
            print(f"\n📈 Quality Report Summary:")
            active_sources = len([s for s in quality_report.values() if 'total_records' in s])
            total_records = sum(s.get('total_records', 0) for s in quality_report.values())
            
            print(f"   → Active sources: {active_sources}/{len(self.data_sources)}")
            print(f"   → Total records: {total_records:,}")
            
            return {
                'status': 'success',
                'sources': quality_report,
                'summary': {
                    'active_sources': active_sources,
                    'total_sources': len(self.data_sources),
                    'total_records': total_records
                }
            }
            
        except Exception as e:
            logger.error(f"❌ Error generating quality report: {e}")
            return {'status': 'failed', 'error': str(e)}

def main():
    """Main function"""
    try:
        collector = IntegratedDataCollector()
        
        # Run integrated data collection
        result = collector.collect_integrated_data(hours_back=24)
        
        if result['status'] == 'success':
            # Also generate quality report
            quality_report = collector.get_data_quality_report()
            return True
        else:
            logger.error("Collection failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Integrated data collection failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

