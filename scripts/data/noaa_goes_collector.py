#!/usr/bin/env python3
"""
NOAA GOES Data Collector
Collects GOES-East/West satellite data from AWS Open Data
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import boto3
from botocore import UNSIGNED
from botocore.config import Config
import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import psycopg2
import time
from typing import Dict, List, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class NOAAGOESCollector:
    """NOAA GOES satellite data collector from AWS Open Data"""
    
    def __init__(self):
        # AWS S3 client for anonymous access
        self.s3_client = boto3.client(
            's3',
            config=Config(signature_version=UNSIGNED)
        )

        # Database connection using environment variables
        self.db_config = {
            'host': os.getenv('DB_HOST', 'solar-prediction-db'),
            'port': int(os.getenv('DB_PORT', 5432)),
            'database': os.getenv('DB_NAME', 'solar_prediction'),
            'user': os.getenv('DB_USER', 'postgres'),
            'password': os.getenv('DB_PASSWORD', 'postgres')
        }
        
        # GOES buckets
        self.buckets = {
            'goes16': 'noaa-goes16',  # GOES-East
            'goes17': 'noaa-goes17',  # GOES-West (backup)
            'goes18': 'noaa-goes18',  # GOES-West
        }
        
        # Location
        self.latitude = 38.141348260997596
        self.longitude = 24.0071653937747
        
        # Products of interest for solar forecasting
        self.products = {
            'cloud_mask': 'ABI-L2-ACMF',      # Cloud Mask
            'cloud_top': 'ABI-L2-ACTPF',      # Cloud Top Properties
            'solar_insolation': 'ABI-L2-DSIF', # Downward Shortwave Irradiance
        }
    
    def list_available_files(self, bucket: str, product: str, target_date: datetime, max_files: int = 5) -> List[str]:
        """List available files for a specific product and date"""

        logger.info(f"📋 Listing {product} files for {target_date.date()}")

        try:
            # GOES file structure: PRODUCT/YYYY/DDD/HH/
            year = target_date.year
            day_of_year = target_date.timetuple().tm_yday
            hour = target_date.hour

            prefix = f"{product}/{year}/{day_of_year:03d}/{hour:02d}/"

            response = self.s3_client.list_objects_v2(
                Bucket=bucket,
                Prefix=prefix,
                MaxKeys=max_files
            )

            files = []
            if 'Contents' in response:
                for obj in response['Contents']:
                    if obj['Key'].endswith('.nc'):  # NetCDF files
                        files.append(obj['Key'])

            logger.info(f"✅ Found {len(files)} files")
            return files

        except Exception as e:
            logger.error(f"❌ Error listing files: {e}")
            return []
    
    def download_goes_file(self, bucket: str, file_key: str, local_path: str) -> bool:
        """Download GOES file from S3"""

        logger.info(f"📥 Downloading {file_key}")

        try:
            self.s3_client.download_file(bucket, file_key, local_path)

            file_size = os.path.getsize(local_path)
            logger.info(f"✅ Downloaded {file_size} bytes")
            return True

        except Exception as e:
            logger.error(f"❌ Download error: {e}")
            return False
    
    def extract_goes_data(self, file_path: str, file_key: str) -> Dict:
        """Extract relevant data from GOES NetCDF file"""
        
        logger.info(f"🔧 Extracting data from {file_path}")
        
        try:
            # This is a simplified extraction - in practice you'd use
            # xarray, netCDF4, or satpy for proper GOES data processing
            
            file_size = os.path.getsize(file_path)
            
            # Extract timestamp from filename
            # GOES filename format: OR_ABI-L2-ACMF-M6_G16_s20241581200000_e20241581209999_c20241581210000.nc
            parts = os.path.basename(file_key).split('_')
            if len(parts) >= 4:
                time_str = parts[3][1:]  # Remove 's' prefix
                if len(time_str) >= 13:
                    year = int(time_str[:4])
                    day_of_year = int(time_str[4:7])
                    hour = int(time_str[7:9])
                    minute = int(time_str[9:11])
                    
                    timestamp = datetime(year, 1, 1) + timedelta(days=day_of_year-1, hours=hour, minutes=minute)
                else:
                    timestamp = datetime.now()
            else:
                timestamp = datetime.now()
            
            extracted_data = {
                'timestamp': timestamp,
                'latitude': self.latitude,
                'longitude': self.longitude,
                'file_key': file_key,
                'file_size': file_size,
                'satellite': 'goes16' if 'G16' in file_key else 'goes18',
                'product': file_key.split('/')[0] if '/' in file_key else 'unknown',
                'cloud_mask': None,  # Would extract from NetCDF
                'solar_irradiance': None,  # Would extract from NetCDF
                'source': 'noaa_goes',
                'ingestion_run_id': f"goes_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            }
            
            logger.info(f"✅ Extracted metadata for {timestamp}")
            return extracted_data
            
        except Exception as e:
            logger.error(f"❌ Data extraction error: {e}")
            return {}
    
    def save_goes_data(self, data: Dict) -> bool:
        """Save GOES data to database"""
        
        logger.info("💾 Saving GOES data to database...")
        
        try:
            conn = psycopg2.connect(**self.db_config)
            
            # Use the same satellite_data table as EUMETSAT
            with conn.cursor() as cur:
                cur.execute("""
                    INSERT INTO satellite_data (
                        timestamp, latitude, longitude, product_id, collection,
                        cloud_cover, solar_irradiance, file_size, source, ingestion_run_id
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (product_id, source) DO NOTHING
                """, (
                    data['timestamp'],
                    data['latitude'],
                    data['longitude'],
                    data['file_key'],
                    data['product'],
                    data['cloud_mask'],
                    data['solar_irradiance'],
                    data['file_size'],
                    data['source'],
                    data['ingestion_run_id']
                ))
            
            conn.commit()
            conn.close()
            
            logger.info("✅ GOES data saved to database")
            return True
            
        except Exception as e:
            logger.error(f"❌ Database save error: {e}")
            return False
    
    def collect_goes_data(self, satellite: str, product: str, target_date: datetime) -> Dict:
        """Collect GOES data for a specific satellite, product, and date"""
        
        logger.info(f"🛰️ Starting GOES collection: {satellite} {product}")
        
        try:
            bucket = self.buckets.get(satellite)
            if not bucket:
                return {
                    'status': 'failed',
                    'error': f'Unknown satellite: {satellite}'
                }
            
            # List available files
            files = self.list_available_files(bucket, product, target_date, max_files=3)
            
            if not files:
                return {
                    'status': 'no_data',
                    'satellite': satellite,
                    'product': product,
                    'files_found': 0,
                    'files_processed': 0
                }
            
            # Process files (limit to avoid overwhelming)
            processed = 0
            
            for file_key in files:
                try:
                    local_path = f"/tmp/goes_{os.path.basename(file_key)}"
                    
                    # Download file
                    if self.download_goes_file(bucket, file_key, local_path):
                        # Extract data
                        extracted_data = self.extract_goes_data(local_path, file_key)
                        
                        if extracted_data:
                            # Save to database
                            if self.save_goes_data(extracted_data):
                                processed += 1
                        
                        # Clean up file
                        if os.path.exists(local_path):
                            os.remove(local_path)
                    
                    # Rate limiting delay
                    time.sleep(1)  # 1 second between downloads
                    
                except Exception as e:
                    logger.error(f"Error processing file {file_key}: {e}")
                    continue
            
            return {
                'status': 'success',
                'satellite': satellite,
                'product': product,
                'files_found': len(files),
                'files_processed': processed,
                'target_date': target_date.date()
            }
            
        except Exception as e:
            logger.error(f"❌ Collection failed: {e}")
            return {
                'status': 'failed',
                'error': str(e),
                'satellite': satellite,
                'product': product
            }


def main():
    """Test NOAA GOES data collection"""
    
    print("🛰️ NOAA GOES Data Collector Test")
    print("=" * 40)
    
    # Initialize collector
    collector = NOAAGOESCollector()
    
    # Test with recent date
    target_date = datetime(2024, 6, 15, 12, 0)  # Yesterday noon
    
    print(f"🧪 Testing collection for: {target_date}")
    
    # Test GOES-16 cloud mask (most relevant for solar forecasting)
    satellite = 'goes16'
    product = collector.products['cloud_mask']
    
    result = collector.collect_goes_data(satellite, product, target_date)
    
    print(f"\n📊 Collection Results:")
    print(f"   Status: {result['status']}")
    print(f"   Satellite: {result.get('satellite', 'N/A')}")
    print(f"   Product: {result.get('product', 'N/A')}")
    
    if result['status'] == 'success':
        print(f"   Files found: {result['files_found']}")
        print(f"   Files processed: {result['files_processed']}")
        print(f"   Target date: {result['target_date']}")
        
        print("\n✅ NOAA GOES integration successful!")
        print("   → Satellite data collection working")
        print("   → AWS S3 access confirmed")
        print("   → Database integration complete")
        
        return True
    elif result['status'] == 'no_data':
        print(f"   Files found: {result['files_found']}")
        print("\n⚠️ No data available for target date")
        print("   → Try different date or satellite")
        print("   → GOES data may have delay")
        
        return True  # Not an error, just no data
    else:
        print(f"   Error: {result.get('error', 'Unknown error')}")
        print("\n❌ NOAA GOES integration needs work")
        print("   → Check AWS connectivity")
        print("   → Check date/product availability")
        
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
