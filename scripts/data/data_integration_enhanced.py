#!/usr/bin/env python3
"""
Enhanced data integration module.

This module provides functions for integrating data from multiple sources
and adding enhanced features for the solar prediction model.
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Any, Optional
import logging
import math
from tqdm import tqdm

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()],
)
logger = logging.getLogger(__name__)


def integrate_data(
    solax_data: pd.DataFrame,
    solax_data2: pd.DataFrame,
    cams_data: pd.DataFrame,
) -> pd.DataFrame:
    """
    Integrate data from multiple sources.

    Args:
        solax_data: DataFrame with SolaxData records
        solax_data2: DataFrame with SolaxData2 records
        cams_data: DataFrame with CAMSRadiationData records

    Returns:
        DataFrame with integrated data
    """
    logger.info("Integrating data from multiple sources...")

    # Create a copy of solax_data to avoid modifying the original
    integrated_data = solax_data.copy()

    # Add source record IDs
    integrated_data["solax_data_id"] = integrated_data["id"]
    integrated_data.drop(columns=["id"], inplace=True)

    # Add solax_data2 records
    if not solax_data2.empty:
        # Rename columns to avoid conflicts
        solax_data2_renamed = solax_data2.rename(
            columns={
                "id": "solax_data2_id",
                "ac_power": "ac_power2",
                "yield_today": "yield_today2",
                "soc": "soc2",
                "bat_power": "bat_power2",
                "powerdc1": "powerdc1_2",
                "powerdc2": "powerdc2_2",
                "feedin_power": "feedin_power2",
                "consume_energy": "consume_energy2",
                "feedin_energy": "feedin_energy2",
            }
        )

        # Merge with solax_data based on timestamp
        # Use outer join to keep all records from both tables
        integrated_data = pd.merge(
            integrated_data,
            solax_data2_renamed,
            on="timestamp",
            how="outer",
            suffixes=("", "_2"),
        )

    # Add CAMS data
    if not cams_data.empty:
        # Rename columns to avoid conflicts
        cams_data_renamed = cams_data.rename(
            columns={
                "id": "cams_data_id",
                "temperature": "cams_temperature",
                "cloud_cover": "cams_cloud_cover",
            }
        )

        # Merge with integrated_data based on timestamp
        # Use left join to keep all records from integrated_data
        integrated_data = pd.merge(
            integrated_data,
            cams_data_renamed,
            on="timestamp",
            how="left",
        )

    # Fill missing values for temperature and cloud_cover
    if "cams_temperature" in integrated_data.columns:
        integrated_data["temperature"].fillna(
            integrated_data["cams_temperature"], inplace=True
        )
        integrated_data.drop(columns=["cams_temperature"], inplace=True)

    if "cams_cloud_cover" in integrated_data.columns:
        integrated_data["cloud_cover"].fillna(
            integrated_data["cams_cloud_cover"], inplace=True
        )
        integrated_data.drop(columns=["cams_cloud_cover"], inplace=True)

    # Add time features
    integrated_data["hour"] = integrated_data["timestamp"].dt.hour
    integrated_data["day_of_year"] = integrated_data["timestamp"].dt.dayofyear
    integrated_data["month"] = integrated_data["timestamp"].dt.month
    integrated_data["is_weekend"] = (
        integrated_data["timestamp"].dt.dayofweek.isin([5, 6]).astype(int)
    )

    # Add season (0=spring, 1=summer, 2=fall, 3=winter)
    integrated_data["season"] = (integrated_data["month"] % 12 + 3) // 3 % 4

    logger.info(f"Integrated data shape: {integrated_data.shape}")

    return integrated_data


def add_astronomical_features(integrated_data: pd.DataFrame) -> pd.DataFrame:
    """
    Add astronomical features to the integrated data.

    Args:
        integrated_data: DataFrame with integrated data

    Returns:
        DataFrame with added astronomical features
    """
    logger.info("Adding astronomical features...")

    # Create a copy to avoid modifying the original
    data = integrated_data.copy()

    # Calculate sun altitude for each timestamp
    data["sun_altitude"] = data.apply(
        lambda row: calculate_sun_altitude(
            row["timestamp"],
            row.get("latitude", 38.141367951893024),
            row.get("longitude", 24.00715534164505),
        ),
        axis=1,
    )

    # Calculate sun angle (angle of incidence)
    data["sun_angle"] = data.apply(
        lambda row: calculate_sun_angle(
            row["sun_altitude"],
            row["timestamp"],
            row.get("latitude", 38.141367951893024),
        ),
        axis=1,
    )

    # Calculate day duration (hours of daylight)
    data["day_duration"] = data.apply(
        lambda row: calculate_day_duration(
            row["timestamp"],
            row.get("latitude", 38.141367951893024),
        ),
        axis=1,
    )

    logger.info("Astronomical features added")

    return data


def calculate_sun_altitude(
    timestamp: datetime,
    latitude: float,
    longitude: float,
) -> float:
    """
    Calculate the sun altitude in degrees.

    Args:
        timestamp: Datetime
        latitude: Latitude in degrees
        longitude: Longitude in degrees

    Returns:
        Sun altitude in degrees
    """
    # Convert to radians
    lat_rad = math.radians(latitude)

    # Get day of year
    day_of_year = timestamp.timetuple().tm_yday

    # Calculate declination angle
    declination = 23.45 * math.sin(math.radians((360 / 365) * (day_of_year - 81)))
    declination_rad = math.radians(declination)

    # Calculate hour angle
    hour = timestamp.hour + timestamp.minute / 60 + timestamp.second / 3600
    hour_angle = 15 * (hour - 12)  # 15 degrees per hour
    hour_angle_rad = math.radians(hour_angle)

    # Calculate sun altitude
    sin_altitude = math.sin(lat_rad) * math.sin(declination_rad) + math.cos(
        lat_rad
    ) * math.cos(declination_rad) * math.cos(hour_angle_rad)
    altitude = math.degrees(math.asin(sin_altitude))

    return altitude


def calculate_sun_angle(
    sun_altitude: float,
    timestamp: datetime,
    latitude: float,
) -> float:
    """
    Calculate the sun angle (angle of incidence) in degrees.

    Args:
        sun_altitude: Sun altitude in degrees
        timestamp: Datetime
        latitude: Latitude in degrees

    Returns:
        Sun angle in degrees
    """
    # For simplicity, we'll use a basic approximation
    # The angle of incidence is the complement of the sun altitude for a horizontal surface
    # For a tilted surface, we need to consider the tilt angle and orientation

    # Assume a tilt angle of 30 degrees and south-facing orientation
    tilt_angle = 30

    # Convert to radians
    sun_altitude_rad = math.radians(sun_altitude)
    tilt_angle_rad = math.radians(tilt_angle)

    # Calculate the angle of incidence
    cos_angle = math.cos(math.radians(90) - sun_altitude_rad) * math.cos(tilt_angle_rad)
    cos_angle += math.sin(math.radians(90) - sun_altitude_rad) * math.sin(
        tilt_angle_rad
    )

    # Ensure the value is within the valid range for arccos
    cos_angle = max(min(cos_angle, 1.0), -1.0)

    angle = math.degrees(math.acos(cos_angle))

    return angle


def calculate_day_duration(
    timestamp: datetime,
    latitude: float,
) -> float:
    """
    Calculate the duration of daylight in hours.

    Args:
        timestamp: Datetime
        latitude: Latitude in degrees

    Returns:
        Duration of daylight in hours
    """
    # Get day of year
    day_of_year = timestamp.timetuple().tm_yday

    # Calculate declination angle
    declination = 23.45 * math.sin(math.radians((360 / 365) * (day_of_year - 81)))
    declination_rad = math.radians(declination)

    # Convert latitude to radians
    lat_rad = math.radians(latitude)

    # Calculate day duration
    cos_hour_angle = -math.tan(lat_rad) * math.tan(declination_rad)

    # Ensure the value is within the valid range for arccos
    cos_hour_angle = max(min(cos_hour_angle, 1.0), -1.0)

    hour_angle = math.acos(cos_hour_angle)
    day_duration = 2 * math.degrees(hour_angle) / 15  # 15 degrees per hour

    return day_duration


def add_pv_system_features(integrated_data: pd.DataFrame) -> pd.DataFrame:
    """
    Add photovoltaic system features to the integrated data.

    Args:
        integrated_data: DataFrame with integrated data

    Returns:
        DataFrame with added PV system features
    """
    logger.info("Adding PV system features...")

    # Create a copy to avoid modifying the original
    data = integrated_data.copy()

    # Calculate panel temperature
    data["panel_temp"] = data.apply(
        lambda row: calculate_panel_temperature(
            row.get("temperature", 25),
            row.get("ghi", 0),
        ),
        axis=1,
    )

    # Calculate panel efficiency
    data["panel_efficiency"] = data.apply(
        lambda row: calculate_panel_efficiency(
            row.get("panel_temp", 25),
        ),
        axis=1,
    )

    # Calculate inverter efficiency
    data["inverter_efficiency"] = data.apply(
        lambda row: calculate_inverter_efficiency(
            (0 if row.get("powerdc1") is None else row.get("powerdc1", 0))
            + (0 if row.get("powerdc2") is None else row.get("powerdc2", 0)),
        ),
        axis=1,
    )

    # Calculate cloud impact
    data["cloud_impact"] = data.apply(
        lambda row: (
            row.get("ghi", 0) * (1 - row.get("cloud_cover", 0) / 100)
            if row.get("ghi") is not None
            and row.get("cloud_cover") is not None
            and not pd.isna(row.get("ghi"))
            and not pd.isna(row.get("cloud_cover"))
            else 0
        ),
        axis=1,
    )

    # Calculate charge rate
    data["charge_rate"] = data.apply(
        lambda row: calculate_charge_rate(
            row.get("bat_power", 0),
            row.get("soc", 50),
        ),
        axis=1,
    )

    # Calculate battery cycles and max capacity
    data = calculate_battery_metrics(data)

    logger.info("PV system features added")

    return data


def calculate_panel_temperature(
    ambient_temp: float,
    ghi: float,
) -> float:
    """
    Calculate the panel temperature based on ambient temperature and solar radiation.

    Args:
        ambient_temp: Ambient temperature in Celsius
        ghi: Global Horizontal Irradiance in W/m²

    Returns:
        Panel temperature in Celsius
    """
    # Handle None values
    if ambient_temp is None:
        ambient_temp = 25  # Default ambient temperature

    if ghi is None:
        ghi = 0  # Default GHI

    # NOCT (Nominal Operating Cell Temperature) method
    # Typical NOCT value is around 45-48°C
    noct = 45

    # Calculate panel temperature
    # T_panel = T_ambient + (NOCT - 20) / 800 * GHI
    panel_temp = ambient_temp + (noct - 20) / 800 * ghi

    return panel_temp


def calculate_panel_efficiency(panel_temp: float) -> float:
    """
    Calculate the panel efficiency based on temperature.

    Args:
        panel_temp: Panel temperature in Celsius

    Returns:
        Panel efficiency (0-1)
    """
    # Handle None values
    if panel_temp is None:
        panel_temp = 25  # Default panel temperature

    # Typical temperature coefficient for power is around -0.4% to -0.5% per °C
    temp_coefficient = -0.004

    # Reference temperature (25°C)
    ref_temp = 25

    # Nominal efficiency at reference temperature (typically 15-20%)
    nominal_efficiency = 0.18

    # Calculate efficiency
    efficiency = nominal_efficiency * (1 + temp_coefficient * (panel_temp - ref_temp))

    # Ensure efficiency is within reasonable bounds
    efficiency = max(
        min(efficiency, nominal_efficiency * 1.1), nominal_efficiency * 0.7
    )

    return efficiency


def calculate_inverter_efficiency(dc_power: float) -> float:
    """
    Calculate the inverter efficiency based on DC power input.

    Args:
        dc_power: DC power input in watts

    Returns:
        Inverter efficiency (0-1)
    """
    # Maximum DC power (system capacity * 1.1)
    max_dc_power = 10500 * 1.1

    # Calculate efficiency using the formula from the design document
    # η_inv = 0.96 - 0.03 * (P_dc / P_dc_max)
    if dc_power > 0 and max_dc_power > 0:
        efficiency = 0.96 - 0.03 * (dc_power / max_dc_power)

        # Ensure efficiency is within reasonable bounds
        efficiency = max(min(efficiency, 0.98), 0.90)
    else:
        efficiency = 0.96  # Default efficiency

    return efficiency


def calculate_charge_rate(bat_power: float, soc: float) -> float:
    """
    Calculate the battery charge rate.

    Args:
        bat_power: Battery power in watts (positive for charging, negative for discharging)
        soc: State of charge (0-100)

    Returns:
        Charge rate (-1 to 1)
    """
    # Handle None values
    if bat_power is None:
        bat_power = 0

    # Maximum battery power (12 kWh battery with 0.5C charge/discharge rate)
    max_bat_power = 6000

    # Calculate charge rate
    if max_bat_power > 0:
        charge_rate = bat_power / max_bat_power

        # Ensure charge rate is within bounds
        charge_rate = max(min(charge_rate, 1.0), -1.0)
    else:
        charge_rate = 0

    return charge_rate


def calculate_battery_metrics(data: pd.DataFrame) -> pd.DataFrame:
    """
    Calculate battery cycles and maximum capacity.

    Args:
        data: DataFrame with integrated data

    Returns:
        DataFrame with added battery metrics
    """
    # Create a copy to avoid modifying the original
    result = data.copy()

    # Calculate battery cycles
    if "soc" in result.columns:
        # Fill NaN values in SOC with 50 (default value)
        result["soc"] = result["soc"].fillna(50)

        # Calculate the difference in SOC
        result["soc_diff"] = result["soc"].diff().fillna(0)

        # Identify charge/discharge cycles (when SOC direction changes)
        result["cycle_increment"] = (
            (result["soc_diff"].shift(1) * result["soc_diff"]) < 0
        ).astype(int)

        # Cumulative sum of cycle increments
        result["battery_cycles"] = result["cycle_increment"].cumsum()

        # Calculate maximum battery capacity based on cycles
        # B_max = 12 * (0.98 ** (n_cycles / 1000))
        result["battery_max_capacity"] = 12 * (
            0.98 ** (result["battery_cycles"] / 1000)
        )

        # Drop temporary columns
        result.drop(columns=["soc_diff", "cycle_increment"], inplace=True)
    else:
        # If SOC is not available, set default values
        result["battery_cycles"] = 0
        result["battery_max_capacity"] = 12

    return result


def apply_mathematical_model(integrated_data: pd.DataFrame) -> pd.DataFrame:
    """
    Apply the mathematical model for panel production and battery.

    Args:
        integrated_data: DataFrame with integrated data

    Returns:
        DataFrame with applied mathematical model
    """
    logger.info("Applying mathematical model...")

    # Create a copy to avoid modifying the original
    data = integrated_data.copy()

    # TODO: Implement the mathematical model
    # This will be implemented in a separate function

    logger.info("Mathematical model applied")

    return data
