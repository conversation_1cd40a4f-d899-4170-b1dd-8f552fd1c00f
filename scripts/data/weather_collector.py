#!/usr/bin/env python3
"""
Weather Data Collector
Collects current weather data from Open-Meteo API and saves to database
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import requests
import json
import psycopg2
from datetime import datetime, timedelta
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def collect_weather_data():
    """Collect current weather data from Open-Meteo API"""

    try:
        # Marathon, Attica coordinates
        latitude = 38.141367951893024
        longitude = 24.00715534164505

        # Use current weather API for real-time data
        url = "https://api.open-meteo.com/v1/forecast"

        params = {
            'latitude': latitude,
            'longitude': longitude,
            'current': [
                'temperature_2m',
                'relative_humidity_2m',
                'cloud_cover',
                'wind_speed_10m',
                'pressure_msl'
            ],
            'hourly': [
                'temperature_2m',
                'relative_humidity_2m',
                'cloud_cover',
                'shortwave_radiation',
                'wind_speed_10m',
                'pressure_msl'
            ],
            'timezone': 'Europe/Athens',
            'forecast_days': 1  # Just today
        }

        logger.info(f"🌤️ Fetching weather data for {latitude}, {longitude}")

        response = requests.get(url, params=params, timeout=30)
        response.raise_for_status()

        data = response.json()

        if 'current' not in data:
            logger.error("❌ No current weather data in response")
            return False

        current = data['current']

        # Save current weather to database - use environment variables
        db_host = os.getenv('DATABASE_HOST', 'localhost')
        db_port = int(os.getenv('DATABASE_PORT', 5432))
        db_name = os.getenv('DATABASE_NAME', 'solar_prediction')
        db_user = os.getenv('DATABASE_USER', 'postgres')
        db_password = os.getenv('DATABASE_PASSWORD', 'postgres')

        conn = psycopg2.connect(
            host=db_host, port=db_port,
            database=db_name,
            user=db_user,
            password=db_password
        )

        with conn.cursor() as cur:
            timestamp = datetime.now()

            # Get shortwave radiation from hourly data if available
            shortwave_radiation = 0
            ghi = 0
            if 'hourly' in data and data['hourly'].get('shortwave_radiation'):
                # Use current hour's shortwave radiation
                current_hour = datetime.now().hour
                if current_hour < len(data['hourly']['shortwave_radiation']):
                    shortwave_radiation = data['hourly']['shortwave_radiation'][current_hour] or 0
                    ghi = shortwave_radiation  # Map shortwave_radiation to GHI

            cur.execute("""
                INSERT INTO weather_data (
                    timestamp, temperature_2m, relative_humidity_2m, cloud_cover,
                    shortwave_radiation, global_horizontal_irradiance, is_forecast, created_at, raw_data
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s
                )
            """, (
                timestamp,
                current.get('temperature_2m'),
                current.get('relative_humidity_2m'),
                current.get('cloud_cover'),
                shortwave_radiation,
                ghi,  # Now properly mapped
                False,  # Current data, not forecast
                timestamp,
                json.dumps(data)
            ))

        conn.commit()
        conn.close()

        logger.info(f"✅ Weather data saved: {current.get('temperature_2m', 0):.1f}°C, "
                   f"Cloud: {current.get('cloud_cover', 0):.0f}%, "
                   f"Humidity: {current.get('relative_humidity_2m', 0):.0f}%")

        return True

    except Exception as e:
        logger.error(f"❌ Weather collection failed: {e}")
        return False

if __name__ == "__main__":
    print(f"🌤️ Weather Collection - {datetime.now()}")
    success = collect_weather_data()
    if success:
        print("✅ Weather collection completed successfully")
    else:
        print("❌ Weather collection failed")
        sys.exit(1)
