#!/usr/bin/env python3
"""
CAMS Solar Radiation Collector - Final Version
Collects CAMS solar radiation data from Atmosphere Data Store (ADS)
Follows the same philosophy as ERA5, CAMS Aerosol, NASA POWER, and Satellite collectors
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import cdsapi
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import psycopg2
from tenacity import retry, wait_exponential, stop_after_attempt
import time
import io

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CAMSSolarCollector:
    """CAMS solar radiation data collector using ADS API with complete functionality"""
    
    def __init__(self):
        # ADS API configuration
        self.ads_url = 'https://ads.atmosphere.copernicus.eu/api/v2'
        self.client = cdsapi.Client(url=self.ads_url)
        
        self.latitude = 38.141348260997596
        self.longitude = 24.0071653937747
        
        # CAMS solar radiation variables
        self.variables = [
            'GHI',  # Global Horizontal Irradiance
            'BNI',  # Beam Normal Irradiance (Direct Normal)
            'DHI',  # Diffuse Horizontal Irradiance
            'BHI'   # Beam Horizontal Irradiance
        ]
        
        # Updated database configuration
        self.db_config = {
            'host': os.getenv('DATABASE_HOST', 'localhost'),
            'port': int(os.getenv('DATABASE_PORT', '5432')),
            'database': os.getenv('DATABASE_NAME', 'solar_prediction'),
            'user': os.getenv('DATABASE_USER', 'postgres'),
            'password': os.getenv('DATABASE_PASSWORD', 'postgres')
        }
    
    def get_last_record_date(self, table_name: str) -> datetime.date:
        """Get the date of the last record with actual data"""
        
        try:
            conn = psycopg2.connect(**self.db_config)
            
            with conn.cursor() as cur:
                cur.execute(f"""
                    SELECT MAX(DATE(timestamp)) 
                    FROM {table_name} 
                    WHERE source = 'cams_solar_radiation'
                    AND ghi IS NOT NULL
                """)
                
                result = cur.fetchone()[0]
                
            conn.close()
            
            if result:
                logger.info(f"✅ Last CAMS solar record found: {result}")
                return result + timedelta(days=1)
            else:
                default_date = datetime(2025, 6, 1).date()
                logger.info(f"📅 No CAMS solar records found, starting from: {default_date}")
                return default_date
                
        except Exception as e:
            logger.error(f"❌ Error getting last record date: {e}")
            return datetime(2025, 6, 1).date()
    
    def find_data_gaps(self, table_name: str, start_date: datetime.date, end_date: datetime.date) -> list:
        """Find gaps in CAMS solar data between start and end dates"""
        
        try:
            conn = psycopg2.connect(**self.db_config)
            
            with conn.cursor() as cur:
                cur.execute(f"""
                    SELECT DATE(timestamp) as record_date
                    FROM {table_name}
                    WHERE source = 'cams_solar_radiation' 
                    AND ghi IS NOT NULL
                    AND DATE(timestamp) BETWEEN %s AND %s
                    GROUP BY DATE(timestamp)
                    ORDER BY record_date
                """, (start_date, end_date))
                
                existing_dates = {row[0] for row in cur.fetchall()}
                
            conn.close()
            
            # Generate all dates in range
            all_dates = set()
            current = start_date
            while current <= end_date:
                all_dates.add(current)
                current += timedelta(days=1)
            
            # Find missing dates
            missing_dates = sorted(all_dates - existing_dates)
            
            if missing_dates:
                logger.info(f"📊 Found {len(missing_dates)} missing CAMS solar dates")
                return missing_dates
            else:
                logger.info("✅ No CAMS solar data gaps found")
                return []
                
        except Exception as e:
            logger.error(f"❌ Error finding CAMS solar data gaps: {e}")
            return []
    
    @retry(wait=wait_exponential(multiplier=1, min=4, max=120), stop=stop_after_attempt(3))
    def fetch_cams_solar_timeseries(self, start_date: datetime.date, end_date: datetime.date) -> str:
        """Fetch CAMS solar radiation time-series data from ADS"""
        
        logger.info(f"☀️ Fetching CAMS solar data: {start_date} to {end_date}")
        
        # Output file
        output_file = f"/tmp/cams_solar_{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}.csv"
        
        # Format date for ADS API
        single_date = start_date.strftime("%Y-%m-%d")

        request = {
            'format': 'csv',
            'latitude': self.latitude,
            'longitude': self.longitude,
            'date': single_date,  # Single day only
            'time_step': '1hour',  # Hourly data
            'time_reference': 'universal_time',  # UTC
            'sky_type': 'observed_cloud',  # All-sky conditions (with clouds)
            'variable': self.variables
        }
        
        logger.info(f"📡 CAMS solar request: {len(self.variables)} variables")
        logger.info(f"📍 Location: {self.latitude}, {self.longitude}")
        logger.info(f"📅 Date: {single_date}")
        
        try:
            self.client.retrieve(
                'cams-solar-radiation-timeseries',  # Correct dataset ID for ADS
                request,
                output_file
            )
            
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                logger.info(f"✅ CAMS solar data retrieved: {file_size:,} bytes")
                return output_file
            else:
                raise Exception("CAMS solar output file not created")
                
        except Exception as e:
            logger.error(f"❌ CAMS solar retrieval failed: {e}")
            raise
    
    def preprocess_cams_csv(self, csv_file: str) -> pd.DataFrame:
        """Preprocess CAMS CSV data with correct column mapping"""
        
        logger.info("🔧 Preprocessing CAMS solar data...")
        
        try:
            # Read CSV, skipping comment lines
            df = pd.read_csv(csv_file, comment='#')
            
            logger.info(f"📋 CSV columns: {list(df.columns)}")
            
            # Parse timestamp - try different column names
            timestamp_col = None
            for col in ['Timestamp', 'Time', 'DateTime', 'Date']:
                if col in df.columns:
                    timestamp_col = col
                    break
            
            if timestamp_col:
                df['timestamp'] = pd.to_datetime(df[timestamp_col])
            else:
                # Try to construct from date/time columns
                if 'Date' in df.columns and 'Time' in df.columns:
                    df['timestamp'] = pd.to_datetime(df['Date'] + ' ' + df['Time'])
                else:
                    raise Exception("Cannot parse timestamp from CAMS CSV")
            
            # Map CAMS columns to our schema
            column_mapping = {
                'Global horizontal irradiance': 'ghi',
                'Direct normal irradiance': 'dni', 
                'Diffuse horizontal irradiance': 'dhi',
                'Beam horizontal irradiance': 'bhi',
                'Cloud cover': 'cloud_cover',
                'Temperature': 'temperature',
                'GHI': 'ghi',
                'BNI': 'dni',
                'DHI': 'dhi',
                'BHI': 'bhi'
            }
            
            # Rename columns
            for old_col, new_col in column_mapping.items():
                if old_col in df.columns:
                    df[new_col] = df[old_col]
                    logger.info(f"✅ Mapped {old_col} → {new_col}")
            
            # Add metadata
            df['latitude'] = self.latitude
            df['longitude'] = self.longitude
            df['source'] = 'cams_solar_radiation'
            df['ingestion_run_id'] = f"cams_solar_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Select relevant columns
            output_columns = [
                'timestamp', 'latitude', 'longitude', 'ghi', 'dni', 'dhi', 'bhi',
                'cloud_cover', 'temperature', 'source', 'ingestion_run_id'
            ]
            
            # Keep only columns that exist
            available_columns = [col for col in output_columns if col in df.columns]
            df = df[available_columns]
            
            # Log data quality
            if 'ghi' in df.columns:
                non_null_ghi = df['ghi'].dropna()
                if len(non_null_ghi) > 0:
                    logger.info(f"   GHI range: {non_null_ghi.min():.1f} - {non_null_ghi.max():.1f} W/m²")
                else:
                    logger.warning("   No valid GHI values found!")
            
            logger.info(f"✅ CAMS solar preprocessing completed: {len(df)} records")
            logger.info(f"   Final columns: {list(df.columns)}")
            
            return df
            
        except Exception as e:
            logger.error(f"❌ CAMS solar preprocessing failed: {e}")
            raise
    
    def save_to_database(self, df: pd.DataFrame, table_name: str) -> int:
        """Save CAMS solar data to database"""
        
        if df.empty:
            logger.info("📭 No CAMS solar data to save")
            return 0
        
        logger.info(f"💾 Saving {len(df)} CAMS solar records to {table_name}...")
        
        try:
            conn = psycopg2.connect(**self.db_config)
            records_saved = 0
            records_skipped = 0
            
            for _, row in df.iterrows():
                try:
                    # Check if record exists
                    with conn.cursor() as cur:
                        cur.execute(f"""
                            SELECT COUNT(*) FROM {table_name} 
                            WHERE timestamp = %s AND source = 'cams_solar_radiation'
                        """, (row['timestamp'],))
                        
                        if cur.fetchone()[0] > 0:
                            records_skipped += 1
                            continue
                    
                    # Insert new record
                    with conn.cursor() as cur:
                        cur.execute(f"""
                            INSERT INTO {table_name} (
                                timestamp, latitude, longitude, ghi, dni, dhi, bhi,
                                cloud_cover, temperature, source, ingestion_run_id
                            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """, (
                            row['timestamp'],
                            row['latitude'],
                            row['longitude'],
                            row.get('ghi'),
                            row.get('dni'),
                            row.get('dhi'),
                            row.get('bhi'),
                            row.get('cloud_cover'),
                            row.get('temperature'),
                            row['source'],
                            row['ingestion_run_id']
                        ))
                    
                    records_saved += 1
                    
                except Exception as e:
                    logger.warning(f"Error saving record {row.get('timestamp', 'unknown')}: {e}")
                    continue
            
            conn.commit()
            conn.close()
            
            logger.info(f"✅ Database update completed:")
            logger.info(f"   → Records saved: {records_saved}")
            logger.info(f"   → Records skipped (duplicates): {records_skipped}")
            
            return records_saved
            
        except Exception as e:
            logger.error(f"❌ Database save failed: {e}")
            return 0
    
    def update_database(self, table_name: str = 'cams_radiation_data'):
        """Main update method for CAMS solar data collection with comprehensive strategy"""
        
        print("☀️ CAMS Solar Radiation Updater (ADS)")
        print("=" * 45)
        
        last_date = self.get_last_record_date(table_name)
        target_date = datetime.now().date() - timedelta(days=2)  # CAMS has ~2 days latency
        
        print(f"📅 Last record date: {last_date - timedelta(days=1)}")
        print(f"📅 Target end date: {target_date}")
        
        if last_date > target_date:
            print("✅ Database is up to date!")
            return True
        
        # Find any gaps in existing data
        gaps = self.find_data_gaps(table_name, datetime(2025, 6, 1).date(), target_date)
        missing_days = (target_date - last_date).days + 1
        total_work = len(gaps) + missing_days
        
        print(f"📊 Data Analysis:")
        print(f"   → Missing recent days: {missing_days}")
        print(f"   → Data gaps found: {len(gaps)}")
        print(f"   → Total work: {total_work} days")
        
        if total_work == 0:
            print("✅ No work needed!")
            return True
        
        # Process data gaps first
        total_saved = 0
        successful_chunks = 0
        failed_chunks = 0
        
        if gaps:
            print(f"\n🔧 Processing {len(gaps)} data gaps...")
            for gap_date in gaps:
                print(f"🔄 Processing gap: {gap_date}")
                try:
                    csv_file = self.fetch_cams_solar_timeseries(gap_date, gap_date)
                    df = self.preprocess_cams_csv(csv_file)
                    records_saved = self.save_to_database(df, table_name)
                    total_saved += records_saved
                    successful_chunks += 1
                    
                    # Clean up temporary file
                    if os.path.exists(csv_file):
                        os.remove(csv_file)
                    
                    print(f"✅ Gap completed: {records_saved} records saved")
                    
                except Exception as e:
                    print(f"❌ Gap failed: {e}")
                    failed_chunks += 1
                
                # Respect API limits
                time.sleep(10)
        
        # Process new data in chunks
        if missing_days > 0:
            print(f"\n🔄 Processing {missing_days} new days...")
            current_start = last_date
            
            while current_start <= target_date:
                # Process one day at a time for CAMS solar
                current_end = current_start
                
                print(f"🔄 Processing day: {current_start}")
                
                try:
                    csv_file = self.fetch_cams_solar_timeseries(current_start, current_end)
                    df = self.preprocess_cams_csv(csv_file)
                    records_saved = self.save_to_database(df, table_name)
                    total_saved += records_saved
                    successful_chunks += 1
                    
                    # Clean up temporary file
                    if os.path.exists(csv_file):
                        os.remove(csv_file)
                    
                    print(f"✅ Day completed: {records_saved} records saved")
                    
                except Exception as e:
                    print(f"❌ Day failed: {e}")
                    failed_chunks += 1
                
                current_start = current_end + timedelta(days=1)
                
                if current_start <= target_date:
                    print("⏳ Waiting 10 seconds before next day...")
                    time.sleep(10)
        
        # Final summary
        print(f"\n📊 CAMS Solar Collection Summary:")
        print(f"   → Period processed: {last_date} to {target_date}")
        print(f"   → Total work: {total_work} days")
        print(f"   → Successful chunks: {successful_chunks}")
        print(f"   → Failed chunks: {failed_chunks}")
        print(f"   → Total records saved: {total_saved}")
        
        if successful_chunks > 0:
            print(f"\n✅ CAMS solar database update completed successfully!")
            print(f"   → Database now contains solar data up to {target_date}")
            return True
        else:
            print(f"\n❌ CAMS solar database update failed!")
            print(f"   → No data was successfully processed")
            return False

def main():
    """Main function to run CAMS solar data collection"""
    try:
        collector = CAMSSolarCollector()
        print("☀️ CAMS Solar Radiation Collector initialized successfully!")
        
        # Run the actual data collection
        success = collector.update_database()
        return success
    except Exception as e:
        logger.error(f"❌ CAMS solar collection failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

