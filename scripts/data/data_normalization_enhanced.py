#!/usr/bin/env python3
"""
Enhanced data normalization module.

This module provides functions for normalizing data for the enhanced
solar prediction model.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional
import logging
from sklearn.preprocessing import MinMaxScaler

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()],
)
logger = logging.getLogger(__name__)


def normalize_data(
    integrated_data: pd.DataFrame,
    feature_columns: Optional[List[str]] = None,
) -> Tuple[pd.DataFrame, Dict[str, Any]]:
    """
    Normalize the integrated data for model training.

    Args:
        integrated_data: DataFrame with integrated data
        feature_columns: List of columns to normalize (if None, use default set)

    Returns:
        Tuple of (normalized data DataFrame, normalization parameters)
    """
    logger.info("Normalizing data...")
    
    # Create a copy to avoid modifying the original
    data = integrated_data.copy()
    
    # Define default feature columns if not provided
    if feature_columns is None:
        feature_columns = [
            "hour", "day_of_year", "month", "is_weekend", "season", "day_duration",
            "ac_power", "soc", "bat_power", "powerdc1", "powerdc2",
            "feedin_power", "consume_energy", "feedin_energy",
            "ghi", "dni", "dhi", "aod", "ghi_realtime",
            "temperature", "cloud_cover",
            "panel_temp", "sun_altitude", "sun_angle", "cloud_impact",
            "panel_efficiency", "inverter_efficiency",
            "charge_rate", "battery_cycles", "battery_max_capacity",
            "theoretical_production", "battery_state", "grid_power",
        ]
    
    # Filter to include only columns that exist in the data
    feature_columns = [col for col in feature_columns if col in data.columns]
    
    # Initialize the scaler
    scaler = MinMaxScaler()
    
    # Create a DataFrame for the normalized data
    normalized_data = pd.DataFrame()
    normalized_data["timestamp"] = data["timestamp"]
    
    # Add the original ac_power column (target variable)
    if "ac_power" in data.columns:
        normalized_data["ac_power"] = data["ac_power"]
    
    # Normalize each feature column
    normalization_params = {}
    for col in feature_columns:
        if col in data.columns:
            # Skip columns with all NaN values
            if data[col].isna().all():
                logger.warning(f"Column {col} has all NaN values, skipping normalization")
                continue
            
            # Get the values as a 2D array for the scaler
            values = data[col].values.reshape(-1, 1)
            
            # Fill NaN values with the mean
            values_no_nan = values[~np.isnan(values)]
            if len(values_no_nan) > 0:
                mean_value = np.mean(values_no_nan)
                values = np.nan_to_num(values, nan=mean_value)
            else:
                # If all values are NaN, use 0
                values = np.zeros_like(values)
            
            # Fit and transform the values
            normalized_values = scaler.fit_transform(values)
            
            # Store the normalized values
            normalized_data[f"{col}_normalized"] = normalized_values.flatten()
            
            # Store the normalization parameters
            normalization_params[col] = {
                "min": scaler.data_min_[0],
                "max": scaler.data_max_[0],
                "mean": mean_value if len(values_no_nan) > 0 else 0,
            }
    
    # Add a special column for boolean features
    if "has_pocket_dongle" in data.columns:
        normalized_data["has_pocket_dongle_normalized"] = data["has_pocket_dongle"].astype(float)
        normalization_params["has_pocket_dongle"] = {
            "min": 0,
            "max": 1,
            "mean": data["has_pocket_dongle"].astype(float).mean(),
        }
    
    logger.info(f"Normalized data shape: {normalized_data.shape}")
    
    return normalized_data, normalization_params


def split_data(
    normalized_data: pd.DataFrame,
    train_ratio: float = 0.7,
    val_ratio: float = 0.15,
    test_ratio: float = 0.15,
) -> pd.DataFrame:
    """
    Split the normalized data into training, validation, and test sets.

    Args:
        normalized_data: DataFrame with normalized data
        train_ratio: Ratio of data to use for training
        val_ratio: Ratio of data to use for validation
        test_ratio: Ratio of data to use for testing

    Returns:
        DataFrame with added 'dataset' column
    """
    logger.info("Splitting data into training, validation, and test sets...")
    
    # Create a copy to avoid modifying the original
    data = normalized_data.copy()
    
    # Sort by timestamp
    data = data.sort_values("timestamp")
    
    # Calculate the split indices
    n = len(data)
    train_end = int(n * train_ratio)
    val_end = train_end + int(n * val_ratio)
    
    # Add the dataset column
    data["dataset"] = "train"
    data.iloc[train_end:val_end, data.columns.get_loc("dataset")] = "validation"
    data.iloc[val_end:, data.columns.get_loc("dataset")] = "test"
    
    logger.info(f"Data split: {data['dataset'].value_counts()}")
    
    return data
