#!/usr/bin/env python3
"""
NASA POWER Historical Data Collection Script
Collects historical weather and radiation data for enhanced solar prediction
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import asyncio
import pandas as pd
from datetime import datetime, timedelta
import logging
from typing import Optional

from src.services.nasa_power_connector import NASAPowerConnector
from src.models.database import get_db_session, NASAPowerData

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class NASAPowerHistoricalCollector:
    """Collects historical NASA POWER data for model training"""
    
    def __init__(self):
        self.connector = NASAPowerConnector()
        self.start_date = datetime(2024, 3, 1)  # Start from when we have solar data
        self.end_date = datetime.now()
        
    async def collect_historical_data(self, chunk_days: int = 7) -> bool:
        """
        Collect historical NASA POWER data in small chunks (conservative approach)

        Args:
            chunk_days: Number of days per API request (default 7 days to respect NASA POWER)

        Returns:
            True if successful, False otherwise
        """
        logger.info("🚀 Starting NASA POWER historical data collection")
        logger.info(f"📅 Period: {self.start_date.date()} to {self.end_date.date()}")
        
        total_records = 0
        total_chunks = 0
        failed_chunks = 0
        
        current_date = self.start_date
        
        while current_date < self.end_date:
            chunk_end = min(current_date + timedelta(days=chunk_days), self.end_date)
            
            logger.info(f"📦 Processing chunk: {current_date.date()} to {chunk_end.date()}")
            
            try:
                # Fetch data for this chunk
                df = await self.connector.fetch_radiation_data(current_date, chunk_end)
                
                if not df.empty:
                    # Save to database
                    success = await self.connector.save_to_database(df)
                    
                    if success:
                        chunk_records = len(df)
                        total_records += chunk_records
                        logger.info(f"✅ Saved {chunk_records} records for chunk")
                    else:
                        logger.error(f"❌ Failed to save chunk data")
                        failed_chunks += 1
                else:
                    logger.warning(f"⚠️ No data returned for chunk")
                    failed_chunks += 1
                
                total_chunks += 1

                # Conservative rate limiting for NASA POWER API
                # NASA POWER monitors usage and may throttle aggressive requests
                logger.info("Waiting 5 seconds between chunks (NASA POWER best practice)...")
                await asyncio.sleep(5)  # Increased delay to be respectful
                
            except Exception as e:
                logger.error(f"❌ Error processing chunk {current_date.date()}: {e}")
                failed_chunks += 1
            
            # Move to next chunk
            current_date = chunk_end + timedelta(days=1)
        
        # Summary
        logger.info("📊 NASA POWER Historical Collection Summary")
        logger.info("=" * 50)
        logger.info(f"Total chunks processed: {total_chunks}")
        logger.info(f"Failed chunks: {failed_chunks}")
        logger.info(f"Success rate: {((total_chunks - failed_chunks) / total_chunks * 100):.1f}%")
        logger.info(f"Total records collected: {total_records}")
        
        return failed_chunks == 0
    
    def check_existing_data(self) -> dict:
        """Check what NASA POWER data already exists in database"""
        
        try:
            with get_db_session() as db:
                # Get data range
                min_timestamp = db.query(NASAPowerData.timestamp).order_by(NASAPowerData.timestamp.asc()).first()
                max_timestamp = db.query(NASAPowerData.timestamp).order_by(NASAPowerData.timestamp.desc()).first()
                total_records = db.query(NASAPowerData).count()
                
                # Get data by source
                sources = db.query(NASAPowerData.source, db.func.count(NASAPowerData.id)).group_by(NASAPowerData.source).all()
                
                result = {
                    'total_records': total_records,
                    'date_range': {
                        'earliest': min_timestamp[0] if min_timestamp else None,
                        'latest': max_timestamp[0] if max_timestamp else None
                    },
                    'sources': dict(sources) if sources else {}
                }
                
                logger.info("📊 Existing NASA POWER Data Summary")
                logger.info("=" * 40)
                logger.info(f"Total records: {result['total_records']}")
                if result['date_range']['earliest']:
                    logger.info(f"Date range: {result['date_range']['earliest']} to {result['date_range']['latest']}")
                logger.info(f"Sources: {result['sources']}")
                
                return result
                
        except Exception as e:
            logger.error(f"Error checking existing data: {e}")
            return {'total_records': 0, 'date_range': {}, 'sources': {}}
    
    def compare_with_existing_weather_data(self) -> dict:
        """Compare NASA POWER data coverage with existing weather data"""
        
        try:
            with get_db_session() as db:
                # Check weather_data table
                weather_query = """
                SELECT 
                    COUNT(*) as total_records,
                    MIN(timestamp) as earliest,
                    MAX(timestamp) as latest
                FROM weather_data
                """
                
                # Check nasa_power_data table
                nasa_query = """
                SELECT 
                    COUNT(*) as total_records,
                    MIN(timestamp) as earliest,
                    MAX(timestamp) as latest
                FROM nasa_power_data
                """
                
                weather_result = db.execute(weather_query).fetchone()
                nasa_result = db.execute(nasa_query).fetchone()
                
                comparison = {
                    'weather_data': {
                        'records': weather_result[0] if weather_result else 0,
                        'earliest': weather_result[1] if weather_result else None,
                        'latest': weather_result[2] if weather_result else None
                    },
                    'nasa_power_data': {
                        'records': nasa_result[0] if nasa_result else 0,
                        'earliest': nasa_result[1] if nasa_result else None,
                        'latest': nasa_result[2] if nasa_result else None
                    }
                }
                
                logger.info("📊 Weather Data Sources Comparison")
                logger.info("=" * 45)
                logger.info(f"Open-Meteo (weather_data): {comparison['weather_data']['records']} records")
                if comparison['weather_data']['earliest']:
                    logger.info(f"  Range: {comparison['weather_data']['earliest']} to {comparison['weather_data']['latest']}")
                
                logger.info(f"NASA POWER (nasa_power_data): {comparison['nasa_power_data']['records']} records")
                if comparison['nasa_power_data']['earliest']:
                    logger.info(f"  Range: {comparison['nasa_power_data']['earliest']} to {comparison['nasa_power_data']['latest']}")
                
                return comparison
                
        except Exception as e:
            logger.error(f"Error comparing data sources: {e}")
            return {}
    
    async def validate_data_quality(self) -> dict:
        """Validate the quality of collected NASA POWER data"""
        
        try:
            with get_db_session() as db:
                # Get sample of recent data
                recent_data = db.query(NASAPowerData).order_by(NASAPowerData.timestamp.desc()).limit(100).all()
                
                if not recent_data:
                    logger.warning("No NASA POWER data found for validation")
                    return {'status': 'no_data'}
                
                # Convert to DataFrame for analysis
                data_list = []
                for record in recent_data:
                    data_list.append({
                        'timestamp': record.timestamp,
                        'ghi': record.ghi,
                        'temperature': record.temperature,
                        'clearness_index': record.clearness_index,
                        'module_temperature': record.module_temperature,
                        'wind_speed': record.wind_speed
                    })
                
                df = pd.DataFrame(data_list)
                
                # Quality checks
                quality_report = {
                    'total_records': len(df),
                    'missing_values': {
                        'ghi': df['ghi'].isna().sum(),
                        'temperature': df['temperature'].isna().sum(),
                        'clearness_index': df['clearness_index'].isna().sum(),
                        'module_temperature': df['module_temperature'].isna().sum()
                    },
                    'value_ranges': {
                        'ghi': {'min': df['ghi'].min(), 'max': df['ghi'].max(), 'mean': df['ghi'].mean()},
                        'temperature': {'min': df['temperature'].min(), 'max': df['temperature'].max(), 'mean': df['temperature'].mean()},
                        'clearness_index': {'min': df['clearness_index'].min(), 'max': df['clearness_index'].max(), 'mean': df['clearness_index'].mean()}
                    },
                    'data_completeness': (1 - df.isna().sum().sum() / (len(df) * len(df.columns))) * 100
                }
                
                logger.info("📊 NASA POWER Data Quality Report")
                logger.info("=" * 40)
                logger.info(f"Records analyzed: {quality_report['total_records']}")
                logger.info(f"Data completeness: {quality_report['data_completeness']:.1f}%")
                logger.info(f"GHI range: {quality_report['value_ranges']['ghi']['min']:.1f} - {quality_report['value_ranges']['ghi']['max']:.1f} W/m²")
                logger.info(f"Temperature range: {quality_report['value_ranges']['temperature']['min']:.1f} - {quality_report['value_ranges']['temperature']['max']:.1f} °C")
                logger.info(f"Clearness Index range: {quality_report['value_ranges']['clearness_index']['min']:.3f} - {quality_report['value_ranges']['clearness_index']['max']:.3f}")
                
                return quality_report
                
        except Exception as e:
            logger.error(f"Error validating data quality: {e}")
            return {'status': 'error', 'message': str(e)}


async def main():
    """Main execution function"""
    
    collector = NASAPowerHistoricalCollector()
    
    print("🌟 NASA POWER Historical Data Collection")
    print("=" * 50)
    
    # Check existing data
    print("\n1️⃣ Checking existing data...")
    existing_data = collector.check_existing_data()
    
    # Compare with weather data
    print("\n2️⃣ Comparing with existing weather sources...")
    comparison = collector.compare_with_existing_weather_data()
    
    # Ask user if they want to proceed
    if existing_data['total_records'] > 0:
        print(f"\n⚠️ Found {existing_data['total_records']} existing NASA POWER records")
        proceed = input("Do you want to continue and collect more data? (y/n): ").lower().strip()
        if proceed != 'y':
            print("❌ Collection cancelled by user")
            return
    
    # Collect historical data
    print("\n3️⃣ Starting historical data collection...")
    success = await collector.collect_historical_data(chunk_days=30)
    
    if success:
        print("\n4️⃣ Validating collected data...")
        quality_report = await collector.validate_data_quality()
        
        print("\n✅ NASA POWER historical data collection completed successfully!")
        print("🎯 Next steps:")
        print("   1. Run feature engineering script")
        print("   2. Compare NASA POWER vs Open-Meteo accuracy")
        print("   3. Retrain models with enhanced features")
    else:
        print("\n❌ Some errors occurred during collection")
        print("🔧 Check logs for details and retry failed chunks")


if __name__ == "__main__":
    asyncio.run(main())
