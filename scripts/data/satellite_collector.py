#!/usr/bin/env python3
"""
European Satellite Data Collector - Final Schema-Matched Version
Collects satellite data from Copernicus/Sentinel for European coverage
Matches existing database schema exactly
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import psycopg2
from tenacity import retry, wait_exponential, stop_after_attempt
import time
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class EuropeanSatelliteCollector:
    """European satellite data collector using Copernicus/Sentinel APIs with correct schema mapping"""
    
    def __init__(self):
        self.latitude = 38.141348260997596
        self.longitude = 24.0071653937747
        
        # Area of interest around Athens, Greece
        self.bbox = [
            self.longitude - 0.1,  # West
            self.latitude - 0.1,   # South
            self.longitude + 0.1,  # East
            self.latitude + 0.1    # North
        ]
        
        # Copernicus API endpoints
        self.copernicus_api = "https://catalogue.dataspace.copernicus.eu/odata/v1"
        
        # Updated database configuration
        self.db_config = {
            'host': os.getenv('DB_HOST', 'localhost'),
            'port': int(os.getenv('DB_PORT', '5432')),
            'database': os.getenv('DB_NAME', 'solar_prediction'),
            'user': os.getenv('DB_USER', 'postgres'),
            'password': os.getenv('DB_PASSWORD', 'postgres')
        }
    
    def get_last_record_date(self, table_name: str) -> datetime.date:
        """Get the date of the last record in satellite table with enhanced error handling"""
        
        try:
            conn = psycopg2.connect(**self.db_config)
            
            with conn.cursor() as cur:
                cur.execute(f"""
                    SELECT MAX(DATE(timestamp)) 
                    FROM {table_name} 
                    WHERE source = 'sentinel_satellite'
                """)
                
                result = cur.fetchone()[0]
                
            conn.close()
            
            if result:
                logger.info(f"✅ Last satellite record found: {result}")
                return result + timedelta(days=1)
            else:
                default_date = datetime(2025, 6, 1).date()
                logger.info(f"📅 No satellite records found, starting from: {default_date}")
                return default_date
                
        except Exception as e:
            logger.error(f"❌ Error getting last record date: {e}")
            return datetime(2025, 6, 1).date()
    
    def find_data_gaps(self, table_name: str, start_date: datetime.date, end_date: datetime.date) -> list:
        """Find gaps in satellite data between start and end dates"""
        
        try:
            conn = psycopg2.connect(**self.db_config)
            
            with conn.cursor() as cur:
                cur.execute(f"""
                    SELECT DATE(timestamp) as record_date
                    FROM {table_name}
                    WHERE source = 'sentinel_satellite' 
                    AND DATE(timestamp) BETWEEN %s AND %s
                    ORDER BY record_date
                """, (start_date, end_date))
                
                existing_dates = {row[0] for row in cur.fetchall()}
                
            conn.close()
            
            # Generate all dates in range
            all_dates = set()
            current = start_date
            while current <= end_date:
                all_dates.add(current)
                current += timedelta(days=1)
            
            # Find missing dates
            missing_dates = sorted(all_dates - existing_dates)
            
            if missing_dates:
                logger.info(f"📊 Found {len(missing_dates)} missing satellite dates")
                return missing_dates
            else:
                logger.info("✅ No satellite data gaps found")
                return []
                
        except Exception as e:
            logger.error(f"❌ Error finding satellite data gaps: {e}")
            return []
    
    @retry(wait=wait_exponential(multiplier=1, min=4, max=120), stop=stop_after_attempt(3))
    def fetch_sentinel_data(self, start_date: datetime.date, end_date: datetime.date) -> dict:
        """Fetch Sentinel satellite data from Copernicus API with retry logic"""
        
        logger.info(f"🛰️ Fetching Sentinel data: {start_date} to {end_date}")
        
        # Format dates for API
        start_str = start_date.strftime('%Y-%m-%dT00:00:00.000Z')
        end_str = end_date.strftime('%Y-%m-%dT23:59:59.999Z')
        
        # Query Copernicus Data Space for Sentinel-2 data
        query_params = {
            '$filter': f"Collection/Name eq 'SENTINEL-2' and "
                      f"ContentDate/Start ge {start_str} and "
                      f"ContentDate/Start le {end_str} and "
                      f"OData.CSC.Intersects(area=geography'SRID=4326;POLYGON(("
                      f"{self.bbox[0]} {self.bbox[1]},"
                      f"{self.bbox[2]} {self.bbox[1]},"
                      f"{self.bbox[2]} {self.bbox[3]},"
                      f"{self.bbox[0]} {self.bbox[3]},"
                      f"{self.bbox[0]} {self.bbox[1]}))')",
            '$orderby': 'ContentDate/Start desc',
            '$top': 20
        }
        
        try:
            response = requests.get(
                f"{self.copernicus_api}/Products",
                params=query_params,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                logger.info(f"✅ Found {len(data.get('value', []))} Sentinel products")
                return data
            else:
                logger.warning(f"⚠️ API returned status {response.status_code}")
                return {'value': []}
                
        except Exception as e:
            logger.error(f"❌ Sentinel data retrieval failed: {e}")
            raise
    
    def calculate_solar_irradiance(self, cloud_cover: float) -> float:
        """Calculate estimated solar irradiance based on cloud cover"""
        
        # Clear sky irradiance for Athens, Greece (typical values)
        clear_sky_irradiance = 1000  # W/m² at solar noon
        
        # Simple model: linear reduction based on cloud cover
        if cloud_cover is None or cloud_cover < 0:
            cloud_cover = 0
        elif cloud_cover > 100:
            cloud_cover = 100
        
        # Cloud reduction factor (empirical model)
        cloud_factor = 1 - (cloud_cover / 100) * 0.8  # 80% max reduction
        estimated_irradiance = clear_sky_irradiance * cloud_factor
        
        return max(0, estimated_irradiance)  # Ensure non-negative
    
    def process_sentinel_data(self, api_data: dict) -> pd.DataFrame:
        """Process Sentinel API data with correct field mapping for database schema"""
        
        logger.info("🔧 Processing Sentinel satellite data...")
        
        try:
            products = api_data.get('value', [])
            
            if not products:
                logger.warning("⚠️ No Sentinel products found")
                return pd.DataFrame()
            
            records = []
            
            for product in products:
                # Extract cloud coverage
                cloud_cover = product.get('CloudCoveragePercentage', 0)
                
                # Calculate estimated solar irradiance
                solar_irradiance = self.calculate_solar_irradiance(cloud_cover)
                
                # Extract file size from ContentLength if available
                content_length = product.get('ContentLength', 0)
                file_size = int(content_length) if content_length else None
                
                # Build record matching database schema exactly
                record = {
                    'timestamp': pd.to_datetime(product.get('ContentDate', {}).get('Start')),
                    'latitude': self.latitude,
                    'longitude': self.longitude,
                    'product_id': product.get('Id'),
                    'collection': product.get('Name', '').split('_')[0] if product.get('Name') else 'SENTINEL-2',
                    'cloud_cover': cloud_cover,
                    'solar_irradiance': solar_irradiance,
                    'file_size': file_size,
                    'source': 'sentinel_satellite',
                    'ingestion_run_id': f"sentinel_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                }
                records.append(record)
            
            df = pd.DataFrame(records)
            
            logger.info(f"✅ Processed {len(df)} Sentinel records")
            logger.info(f"   Average cloud cover: {df['cloud_cover'].mean():.1f}%")
            logger.info(f"   Average solar irradiance: {df['solar_irradiance'].mean():.1f} W/m²")
            
            return df
            
        except Exception as e:
            logger.error(f"❌ Sentinel data processing failed: {e}")
            raise
    
    def save_to_database(self, df: pd.DataFrame, table_name: str) -> int:
        """Save satellite data to database with exact schema matching"""
        
        if df.empty:
            logger.info("📭 No satellite data to save")
            return 0
        
        logger.info(f"💾 Saving {len(df)} satellite records to {table_name}...")
        
        try:
            conn = psycopg2.connect(**self.db_config)
            records_saved = 0
            records_skipped = 0
            
            for _, row in df.iterrows():
                try:
                    # Check if record exists
                    with conn.cursor() as cur:
                        cur.execute(f"""
                            SELECT COUNT(*) FROM {table_name} 
                            WHERE timestamp = %s AND source = 'sentinel_satellite'
                        """, (row['timestamp'],))
                        
                        if cur.fetchone()[0] > 0:
                            records_skipped += 1
                            continue
                    
                    # Insert with exact column names matching database schema
                    with conn.cursor() as cur:
                        cur.execute(f"""
                            INSERT INTO {table_name} (
                                timestamp, latitude, longitude, 
                                product_id, collection, cloud_cover,
                                solar_irradiance, file_size,
                                source, ingestion_run_id
                            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """, (
                            row['timestamp'],
                            row['latitude'],
                            row['longitude'],
                            row['product_id'],
                            row['collection'],
                            row['cloud_cover'],
                            row['solar_irradiance'],
                            row['file_size'],
                            row['source'],
                            row['ingestion_run_id']
                        ))
                    
                    records_saved += 1
                    
                except Exception as e:
                    logger.warning(f"Error saving record {row.get('timestamp', 'unknown')}: {e}")
                    continue
            
            conn.commit()
            conn.close()
            
            logger.info(f"✅ Database update completed:")
            logger.info(f"   → Records saved: {records_saved}")
            logger.info(f"   → Records skipped (duplicates): {records_skipped}")
            
            return records_saved
            
        except Exception as e:
            logger.error(f"❌ Database save failed: {e}")
            return 0
    
    def update_database(self, table_name: str = 'satellite_data'):
        """Main update method for satellite data collection with comprehensive strategy"""
        
        print("🛰️ European Satellite Data Updater")
        print("=" * 40)
        
        last_date = self.get_last_record_date(table_name)
        target_date = datetime.now().date() - timedelta(days=1)  # Sentinel has ~1 day latency
        
        print(f"📅 Last record date: {last_date - timedelta(days=1)}")
        print(f"📅 Target end date: {target_date}")
        
        if last_date > target_date:
            print("✅ Database is up to date!")
            return True
        
        # Find any gaps in existing data
        gaps = self.find_data_gaps(table_name, datetime(2025, 6, 1).date(), target_date)
        missing_days = (target_date - last_date).days + 1
        total_work = len(gaps) + missing_days
        
        print(f"📊 Data Analysis:")
        print(f"   → Missing recent days: {missing_days}")
        print(f"   → Data gaps found: {len(gaps)}")
        print(f"   → Total work: {total_work} days")
        
        if total_work == 0:
            print("✅ No work needed!")
            return True
        
        # Process data gaps first
        total_saved = 0
        successful_chunks = 0
        failed_chunks = 0
        
        if gaps:
            print(f"\n🔧 Processing {len(gaps)} data gaps...")
            for gap_date in gaps:
                print(f"🔄 Processing gap: {gap_date}")
                try:
                    api_data = self.fetch_sentinel_data(gap_date, gap_date)
                    df = self.process_sentinel_data(api_data)
                    records_saved = self.save_to_database(df, table_name)
                    total_saved += records_saved
                    successful_chunks += 1
                    
                    print(f"✅ Gap completed: {records_saved} records saved")
                    
                except Exception as e:
                    print(f"❌ Gap failed: {e}")
                    failed_chunks += 1
                
                # Respect API limits
                time.sleep(10)
        
        # Process new data in chunks
        if missing_days > 0:
            print(f"\n🔄 Processing {missing_days} new days...")
            chunk_size = 5  # 5 days at a time
            current_start = last_date
            
            while current_start <= target_date:
                current_end = min(current_start + timedelta(days=chunk_size-1), target_date)
                
                print(f"🔄 Processing chunk: {current_start} to {current_end}")
                
                try:
                    api_data = self.fetch_sentinel_data(current_start, current_end)
                    df = self.process_sentinel_data(api_data)
                    records_saved = self.save_to_database(df, table_name)
                    total_saved += records_saved
                    successful_chunks += 1
                    
                    print(f"✅ Chunk completed: {records_saved} records saved")
                    
                except Exception as e:
                    print(f"❌ Chunk failed: {e}")
                    failed_chunks += 1
                
                current_start = current_end + timedelta(days=1)
                
                if current_start <= target_date:
                    print("⏳ Waiting 10 seconds before next chunk...")
                    time.sleep(10)
        
        # Final summary
        print(f"\n📊 Satellite Collection Summary:")
        print(f"   → Period processed: {last_date} to {target_date}")
        print(f"   → Total work: {total_work} days")
        print(f"   → Successful chunks: {successful_chunks}")
        print(f"   → Failed chunks: {failed_chunks}")
        print(f"   → Total records saved: {total_saved}")
        
        if successful_chunks > 0:
            print(f"\n✅ Satellite database update completed successfully!")
            print(f"   → Database now contains satellite data up to {target_date}")
            return True
        else:
            print(f"\n❌ Satellite database update failed!")
            print(f"   → No data was successfully processed")
            return False

def main():
    """Main function to run satellite data collection"""
    try:
        collector = EuropeanSatelliteCollector()
        print("🛰️ European Satellite Collector initialized successfully!")
        
        # Run the actual data collection
        success = collector.update_database()
        return success
    except Exception as e:
        logger.error(f"❌ Satellite collection failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

