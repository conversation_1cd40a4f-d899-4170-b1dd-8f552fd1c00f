#!/usr/bin/env python3
"""
Simple NASA POWER CSV Import
Simplified version for reliable import
"""

import pandas as pd
import numpy as np
from datetime import datetime
import psycopg2
import sys

def main():
    print("🚀 Simple NASA POWER Import")
    print("=" * 30)
    
    # File path
    csv_file = "/home/<USER>/solar-prediction-project/data/raw/NASA/POWER_Point_Hourly_20240301_20250607_038d14N_024d00E_LST (1).csv"
    
    print("📁 Reading CSV file...")
    try:
        # Read CSV, skipping header
        df = pd.read_csv(csv_file, skiprows=26)
        print(f"✅ Loaded {len(df)} rows")
        
        # Create timestamp
        df['timestamp'] = pd.to_datetime(df[['YEAR', 'MO', 'DY', 'HR']].rename(columns={
            'YEAR': 'year', 'MO': 'month', 'DY': 'day', 'HR': 'hour'
        }))
        
        # Map key columns
        df_clean = pd.DataFrame({
            'timestamp': df['timestamp'],
            'latitude': 38.1419,
            'longitude': 24.0046,
            'ghi': df['ALLSKY_SFC_SW_DWN'].replace(-999.0, None),
            'temperature': df['T2M'].replace(-999.0, None),
            'wind_speed': df['WS10M'].replace(-999.0, None),
            'relative_humidity': df['RH2M'].replace(-999.0, None),
            'surface_pressure': df['PS'].replace(-999.0, None),
            'clear_sky_ghi': df['CLRSKY_SFC_SW_DWN'].replace(-999.0, None),
            'clearness_index': df['ALLSKY_KT'].replace(-999.0, None),
            'source': 'nasa_power_csv',
            'ingestion_run_id': f"simple_import_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        })
        
        print(f"📊 Processed data: {len(df_clean)} records")
        print(f"   Date range: {df_clean['timestamp'].min()} to {df_clean['timestamp'].max()}")
        
        # Check existing data
        print("\n🔍 Checking existing data...")
        try:
            conn = psycopg2.connect(
                host='localhost', port=5433,
                database='solar_prediction',
                user='postgres',
                password='postgres'
            )
            
            with conn.cursor() as cur:
                cur.execute("SELECT COUNT(*) FROM nasa_power_data;")
                existing_count = cur.fetchone()[0]
            
            print(f"   Existing records: {existing_count}")
            
            if existing_count > 0:
                response = input(f"⚠️ Found {existing_count} existing records. Continue? (y/n): ")
                if response.lower() != 'y':
                    print("❌ Import cancelled")
                    return False
            
            # Import in small batches
            print(f"\n💾 Importing {len(df_clean)} records...")
            
            batch_size = 100
            total_inserted = 0
            
            for i in range(0, len(df_clean), batch_size):
                batch = df_clean.iloc[i:i+batch_size]
                
                try:
                    with conn.cursor() as cur:
                        for _, row in batch.iterrows():
                            cur.execute("""
                                INSERT INTO nasa_power_data (
                                    timestamp, latitude, longitude, ghi, temperature, 
                                    wind_speed, relative_humidity, surface_pressure, 
                                    clear_sky_ghi, clearness_index, source, ingestion_run_id
                                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                            """, (
                                row['timestamp'],
                                row['latitude'],
                                row['longitude'],
                                row['ghi'],
                                row['temperature'],
                                row['wind_speed'],
                                row['relative_humidity'],
                                row['surface_pressure'],
                                row['clear_sky_ghi'],
                                row['clearness_index'],
                                row['source'],
                                row['ingestion_run_id']
                            ))
                    
                    conn.commit()
                    total_inserted += len(batch)
                    
                    if total_inserted % 1000 == 0:
                        print(f"   Inserted {total_inserted}/{len(df_clean)} records...")
                
                except Exception as e:
                    print(f"❌ Error in batch {i//batch_size + 1}: {e}")
                    conn.rollback()
                    continue
            
            # Final count
            with conn.cursor() as cur:
                cur.execute("SELECT COUNT(*) FROM nasa_power_data;")
                final_count = cur.fetchone()[0]
            
            conn.close()
            
            print(f"\n✅ Import completed!")
            print(f"   Records inserted: {total_inserted}")
            print(f"   Total in database: {final_count}")
            
            return True
            
        except Exception as e:
            print(f"❌ Database error: {e}")
            return False
            
    except Exception as e:
        print(f"❌ CSV reading error: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
