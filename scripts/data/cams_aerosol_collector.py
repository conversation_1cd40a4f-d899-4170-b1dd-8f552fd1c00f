#!/usr/bin/env python3
"""
CAMS Aerosol Collector - Final Version
Collects CAMS aerosol data from Atmosphere Data Store (ADS) and updates database
Handles ZIP archives, correct variable mapping, and incremental updates
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import cdsapi
import xarray as xr
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import psycopg2
from tenacity import retry, wait_exponential, stop_after_attempt
import time
import zipfile
import tempfile

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CAMSAerosolCollector:
    """CAMS Aerosol data collector using ADS API with complete functionality"""
    
    def __init__(self):
        # ADS API configuration
        self.ads_url = 'https://ads.atmosphere.copernicus.eu/api'
        self.client = cdsapi.Client(url=self.ads_url)
        
        self.latitude = 38.141348260997596
        self.longitude = 24.0071653937747
        
        # Area for CAMS data (optimized for API limits)
        self.area = [
            self.latitude + 0.1,   # North
            self.longitude - 0.1,  # West
            self.latitude - 0.1,   # South
            self.longitude + 0.1   # East
        ]
        
        # CAMS aerosol variables (using correct ADS parameter names)
        self.variables = [
            'total_aerosol_optical_depth_550nm',
            'dust_aerosol_optical_depth_550nm'
        ]
        
        # Updated database configuration with environment variables
        self.db_config = {
            'host': os.getenv('DB_HOST', 'solar-prediction-db'),
            'port': int(os.getenv('DB_PORT', 5432)),
            'database': os.getenv('DB_NAME', 'solar_prediction'),
            'user': os.getenv('DB_USER', 'postgres'),
            'password': os.getenv('DB_PASSWORD', 'postgres')
        }
    
    def get_last_record_date(self, table_name: str) -> datetime.date:
        """Get the date of the last record in specified table with enhanced error handling"""
        
        try:
            conn = psycopg2.connect(**self.db_config)
            
            with conn.cursor() as cur:
                cur.execute(f"""
                    SELECT MAX(DATE(timestamp)) 
                    FROM {table_name} 
                    WHERE source = 'cams_aerosol'
                """)
                
                result = cur.fetchone()[0]
                
            conn.close()
            
            if result:
                logger.info(f"✅ Last CAMS record found: {result}")
                return result + timedelta(days=1)
            else:
                default_date = datetime(2025, 6, 1).date()
                logger.info(f"📅 No CAMS records found, starting from: {default_date}")
                return default_date
                
        except psycopg2.Error as e:
            logger.error(f"❌ Database error getting last record: {e}")
            return datetime(2025, 6, 1).date()
        except Exception as e:
            logger.error(f"❌ Unexpected error getting last record: {e}")
            return datetime(2025, 6, 1).date()
    
    def find_data_gaps(self, table_name: str, start_date: datetime.date, end_date: datetime.date) -> list:
        """Find gaps in CAMS data between start and end dates"""
        
        try:
            conn = psycopg2.connect(**self.db_config)
            
            with conn.cursor() as cur:
                cur.execute(f"""
                    SELECT DATE(timestamp) as record_date
                    FROM {table_name}
                    WHERE source = 'cams_aerosol' 
                    AND DATE(timestamp) BETWEEN %s AND %s
                    ORDER BY record_date
                """, (start_date, end_date))
                
                existing_dates = {row[0] for row in cur.fetchall()}
                
            conn.close()
            
            # Generate all dates in range
            all_dates = set()
            current = start_date
            while current <= end_date:
                all_dates.add(current)
                current += timedelta(days=1)
            
            # Find missing dates
            missing_dates = sorted(all_dates - existing_dates)
            
            if missing_dates:
                logger.info(f"📊 Found {len(missing_dates)} missing CAMS dates")
                return missing_dates
            else:
                logger.info("✅ No CAMS data gaps found")
                return []
                
        except Exception as e:
            logger.error(f"❌ Error finding CAMS data gaps: {e}")
            return []
    
    @retry(wait=wait_exponential(multiplier=1, min=4, max=120), stop=stop_after_attempt(3))
    def fetch_cams_chunk(self, start_date: datetime.date, end_date: datetime.date) -> str:
        """Fetch CAMS aerosol data from ADS with retry logic"""

        logger.info(f"🌫️ Fetching CAMS aerosol data: {start_date} to {end_date}")

        # Format date range for ADS
        if start_date == end_date:
            date_range = start_date.strftime('%Y-%m-%d')
        else:
            date_range = f"{start_date.strftime('%Y-%m-%d')}/{end_date.strftime('%Y-%m-%d')}"

        output_file = f"/tmp/cams_aerosol_{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}.zip"

        # Corrected request structure for ADS
        request = {
            'variable': self.variables,
            'date': date_range,
            'time': ['12:00'],  # Noon data
            'leadtime_hour': ['0'],  # Analysis (0-hour forecast)
            'type': 'forecast',
            'data_format': 'netcdf_zip',
            'area': self.area
        }

        logger.info(f"📡 CAMS request: {len(self.variables)} variables")
        logger.info(f"📍 Area: {self.area}")
        logger.info(f"📅 Date range: {date_range}")

        try:
            self.client.retrieve('cams-global-atmospheric-composition-forecasts', request, output_file)

            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                logger.info(f"✅ CAMS data retrieved: {file_size:,} bytes")
                return output_file
            else:
                raise Exception("CAMS output file not created")

        except Exception as e:
            logger.error(f"❌ CAMS retrieval failed: {e}")
            raise
    
    def preprocess_cams_chunk(self, downloaded_file: str) -> pd.DataFrame:
        """Preprocess CAMS aerosol data with correct column mapping"""

        logger.info("🔧 Preprocessing CAMS aerosol data...")

        try:
            # Extract ZIP contents
            with zipfile.ZipFile(downloaded_file, 'r') as zip_ref:
                file_list = zip_ref.namelist()
                logger.info(f"📋 ZIP contents: {file_list}")

                # Extract NetCDF file
                netcdf_file = None
                for file_name in file_list:
                    if file_name.endswith('.nc'):
                        with tempfile.NamedTemporaryFile(suffix='.nc', delete=False) as temp_file:
                            temp_file.write(zip_ref.read(file_name))
                            netcdf_file = temp_file.name
                        logger.info(f"✅ Extracted: {file_name}")
                        break

                if not netcdf_file:
                    raise Exception("No NetCDF file found in ZIP")

            # Open and process NetCDF
            ds = xr.open_dataset(netcdf_file, engine='netcdf4', decode_timedelta=False)
            logger.info(f"✅ Opened NetCDF with variables: {list(ds.data_vars.keys())}")
            logger.info(f"   Dimensions: {list(ds.dims.keys())}")

            # Extract data at location
            point_data = ds.sel(
                latitude=self.latitude,
                longitude=self.longitude,
                method='nearest'
            )

            # Convert to DataFrame
            df = point_data.to_dataframe().reset_index()

            # Correct column mapping for CAMS variables (NetCDF names → Database names)
            column_mapping = {
                'aod550': 'total_aerosol_optical_depth',
                'duaod550': 'dust_aerosol_optical_depth',
                'omaod550': 'organic_matter_aerosol_optical_depth',
                'ssaod550': 'sea_salt_aerosol_optical_depth'
            }

            # Apply column mapping
            for old_name, new_name in column_mapping.items():
                if old_name in df.columns:
                    df = df.rename(columns={old_name: new_name})
                    logger.info(f"✅ Mapped {old_name} → {new_name}")

            # Handle time dimension properly
            time_col = None
            for col in ['time', 'valid_time', 'forecast_reference_time']:
                if col in df.columns:
                    time_col = col
                    break

            if time_col and time_col != 'timestamp':
                df = df.rename(columns={time_col: 'timestamp'})

            # Convert timestamp if needed
            if 'timestamp' in df.columns:
                if df['timestamp'].dtype == 'int64':
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s')
                elif df['timestamp'].dtype == 'object':
                    df['timestamp'] = pd.to_datetime(df['timestamp'])

            # Add metadata
            df['latitude'] = self.latitude
            df['longitude'] = self.longitude
            df['source'] = 'cams_aerosol'
            df['ingestion_run_id'] = f"cams_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # Clean up
            ds.close()
            os.unlink(netcdf_file)

            logger.info(f"✅ CAMS preprocessing completed: {len(df)} records")
            logger.info(f"   Final columns: {list(df.columns)}")

            return df

        except Exception as e:
            logger.error(f"❌ CAMS preprocessing failed: {e}")
            raise
    
    def save_to_database(self, df: pd.DataFrame, table_name: str) -> int:
        """Save CAMS data to database with correct column names"""
        
        logger.info(f"💾 Saving {len(df)} CAMS records to {table_name}...")
        
        try:
            conn = psycopg2.connect(**self.db_config)
            records_saved = 0
            records_skipped = 0
            
            for _, row in df.iterrows():
                try:
                    # Check if record exists
                    with conn.cursor() as cur:
                        cur.execute(f"""
                            SELECT COUNT(*) FROM {table_name} 
                            WHERE timestamp = %s AND source = 'cams_aerosol'
                        """, (row['timestamp'],))
                        
                        if cur.fetchone()[0] > 0:
                            records_skipped += 1
                            continue
                    
                    # Insert with correct column names (matching your database schema)
                    with conn.cursor() as cur:
                        cur.execute(f"""
                            INSERT INTO {table_name} (
                                timestamp, latitude, longitude, 
                                total_aerosol_optical_depth,
                                dust_aerosol_optical_depth,
                                organic_matter_aerosol_optical_depth,
                                sea_salt_aerosol_optical_depth,
                                source, ingestion_run_id
                            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """, (
                            row['timestamp'],
                            row['latitude'],
                            row['longitude'],
                            row.get('total_aerosol_optical_depth'),
                            row.get('dust_aerosol_optical_depth'),
                            row.get('organic_matter_aerosol_optical_depth'),
                            row.get('sea_salt_aerosol_optical_depth'),
                            row['source'],
                            row['ingestion_run_id']
                        ))
                    
                    records_saved += 1
                    
                except Exception as e:
                    logger.warning(f"Error saving record {row.get('timestamp', 'unknown')}: {e}")
                    continue
            
            conn.commit()
            conn.close()
            
            logger.info(f"✅ Database update completed:")
            logger.info(f"   → Records saved: {records_saved}")
            logger.info(f"   → Records skipped (duplicates): {records_skipped}")
            
            return records_saved
            
        except Exception as e:
            logger.error(f"❌ Database save failed: {e}")
            return 0
    
    def update_database(self, table_name: str = 'cams_aerosol_data'):
        """Main update method for CAMS aerosol data collection with comprehensive strategy"""
        
        print("🌫️ CAMS Aerosol Data Updater (ADS)")
        print("=" * 40)
        
        last_date = self.get_last_record_date(table_name)
        target_date = datetime.now().date() - timedelta(days=2)  # CAMS has ~2 days latency
        
        print(f"📅 Last record date: {last_date - timedelta(days=1)}")
        print(f"📅 Target end date: {target_date}")
        
        if last_date > target_date:
            print("✅ Database is up to date!")
            return True
        
        # Find any gaps in existing data
        gaps = self.find_data_gaps(table_name, datetime(2025, 6, 1).date(), target_date)
        missing_days = (target_date - last_date).days + 1
        total_work = len(gaps) + missing_days
        
        print(f"📊 Data Analysis:")
        print(f"   → Missing recent days: {missing_days}")
        print(f"   → Data gaps found: {len(gaps)}")
        print(f"   → Total work: {total_work} days")
        
        if total_work == 0:
            print("✅ No work needed!")
            return True
        
        # Process data gaps first
        total_saved = 0
        successful_chunks = 0
        failed_chunks = 0
        
        if gaps:
            print(f"\n🔧 Processing {len(gaps)} data gaps...")
            for gap_date in gaps:
                print(f"🔄 Processing gap: {gap_date}")
                try:
                    downloaded_file = self.fetch_cams_chunk(gap_date, gap_date)
                    df = self.preprocess_cams_chunk(downloaded_file)
                    records_saved = self.save_to_database(df, table_name)
                    total_saved += records_saved
                    successful_chunks += 1
                    
                    if os.path.exists(downloaded_file):
                        os.remove(downloaded_file)
                    
                    print(f"✅ Gap completed: {records_saved} records saved")
                    
                except Exception as e:
                    print(f"❌ Gap failed: {e}")
                    failed_chunks += 1
                
                # Respect API limits
                time.sleep(30)
        
        # Process new data in chunks
        if missing_days > 0:
            print(f"\n🔄 Processing {missing_days} new days...")
            chunk_size = 3  # 3 days at a time
            current_start = last_date
            
            while current_start <= target_date:
                current_end = min(current_start + timedelta(days=chunk_size-1), target_date)
                
                print(f"🔄 Processing chunk: {current_start} to {current_end}")
                
                try:
                    downloaded_file = self.fetch_cams_chunk(current_start, current_end)
                    df = self.preprocess_cams_chunk(downloaded_file)
                    records_saved = self.save_to_database(df, table_name)
                    total_saved += records_saved
                    successful_chunks += 1
                    
                    # Clean up temporary file
                    if os.path.exists(downloaded_file):
                        os.remove(downloaded_file)
                    
                    print(f"✅ Chunk completed: {records_saved} records saved")
                    
                except Exception as e:
                    print(f"❌ Chunk failed: {e}")
                    failed_chunks += 1
                    # Continue with next chunk instead of stopping
                
                # Move to next chunk
                current_start = current_end + timedelta(days=1)
                
                # Add delay between chunks to respect API limits
                if current_start <= target_date:
                    print("⏳ Waiting 30 seconds before next chunk...")
                    time.sleep(30)
        
        # Final summary
        print(f"\n📊 CAMS Collection Summary:")
        print(f"   → Period processed: {last_date} to {target_date}")
        print(f"   → Total work: {total_work} days")
        print(f"   → Successful chunks: {successful_chunks}")
        print(f"   → Failed chunks: {failed_chunks}")
        print(f"   → Total records saved: {total_saved}")
        
        if successful_chunks > 0:
            print(f"\n✅ CAMS aerosol database update completed successfully!")
            print(f"   → Database now contains aerosol data up to {target_date}")
            return True
        else:
            print(f"\n❌ CAMS aerosol database update failed!")
            print(f"   → No data was successfully processed")
            return False

def main():
    """Main function to run CAMS aerosol data collection"""
    try:
        collector = CAMSAerosolCollector()
        print("🌫️ CAMS Aerosol Collector initialized successfully!")
        
        # Run the actual data collection
        success = collector.update_database()
        return success
    except Exception as e:
        logger.error(f"❌ CAMS collection failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

