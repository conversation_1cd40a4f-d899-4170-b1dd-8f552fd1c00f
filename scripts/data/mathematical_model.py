#!/usr/bin/env python3
"""
Mathematical model for photovoltaic system.

This module implements the mathematical model for the photovoltaic system,
including panel production, battery management, and grid interaction.
"""

import numpy as np
import pandas as pd
from datetime import datetime
from typing import Dict, List, Tuple, Any, Optional
import logging
import math

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()],
)
logger = logging.getLogger(__name__)


def apply_pv_model(data: pd.DataFrame) -> pd.DataFrame:
    """
    Apply the photovoltaic system mathematical model.

    Args:
        data: DataFrame with integrated data

    Returns:
        DataFrame with applied mathematical model
    """
    logger.info("Applying photovoltaic system mathematical model...")
    
    # Create a copy to avoid modifying the original
    result = data.copy()
    
    # Apply the model to each row
    for i in range(len(result)):
        # Get the current row
        row = result.iloc[i]
        
        # Calculate theoretical panel production
        theoretical_production = calculate_theoretical_production(
            row.get("ghi", 0),
            row.get("panel_efficiency", 0.18),
            row.get("sun_angle", 90),
            row.get("panel_temp", 25),
        )
        
        # Apply physical constraints
        theoretical_production = apply_physical_constraints(
            theoretical_production,
            row.get("sun_altitude", 0),
            row.get("ghi", 0),
        )
        
        # Store the theoretical production
        result.at[i, "theoretical_production"] = theoretical_production
        
        # Calculate battery state
        if i > 0:
            prev_row = result.iloc[i-1]
            battery_state = calculate_battery_state(
                theoretical_production,
                row.get("consume_energy", 0) - prev_row.get("consume_energy", 0),
                prev_row.get("battery_state", row.get("soc", 50) / 100 * row.get("battery_max_capacity", 12)),
                row.get("battery_max_capacity", 12),
            )
        else:
            # For the first row, use the current SOC
            battery_state = row.get("soc", 50) / 100 * row.get("battery_max_capacity", 12)
        
        # Store the battery state
        result.at[i, "battery_state"] = battery_state
        
        # Calculate grid interaction
        grid_power = calculate_grid_interaction(
            theoretical_production,
            row.get("consume_energy", 0) - (prev_row.get("consume_energy", 0) if i > 0 else 0),
            battery_state,
            row.get("battery_max_capacity", 12),
        )
        
        # Store the grid power
        result.at[i, "grid_power"] = grid_power
    
    logger.info("Photovoltaic system mathematical model applied")
    
    return result


def calculate_theoretical_production(
    ghi: float,
    panel_efficiency: float,
    sun_angle: float,
    panel_temp: float,
) -> float:
    """
    Calculate the theoretical panel production.

    Args:
        ghi: Global Horizontal Irradiance in W/m²
        panel_efficiency: Panel efficiency (0-1)
        sun_angle: Angle of incidence in degrees
        panel_temp: Panel temperature in Celsius

    Returns:
        Theoretical production in watts
    """
    # Panel area (m²) for a 10.5 kWp system with 18% efficiency
    # Area = Capacity / (Efficiency * 1000)
    panel_area = 10.5 / (0.18 * 1)
    
    # Temperature coefficient (typically -0.4% to -0.5% per °C)
    temp_coefficient = -0.004
    
    # Reference temperature (25°C)
    ref_temp = 25
    
    # Calculate angle factor (cosine of incidence angle)
    angle_factor = max(0, math.cos(math.radians(min(sun_angle, 90))))
    
    # Calculate temperature factor
    temp_factor = 1 + temp_coefficient * (panel_temp - ref_temp)
    
    # Calculate theoretical production
    # P = GHI * Area * Efficiency * AngleFactor * TempFactor
    theoretical_production = ghi * panel_area * panel_efficiency * angle_factor * temp_factor
    
    return theoretical_production


def apply_physical_constraints(
    theoretical_production: float,
    sun_altitude: float,
    ghi: float,
) -> float:
    """
    Apply physical constraints to the theoretical production.

    Args:
        theoretical_production: Theoretical production in watts
        sun_altitude: Sun altitude in degrees
        ghi: Global Horizontal Irradiance in W/m²

    Returns:
        Constrained production in watts
    """
    # Zero production at night (sun below horizon)
    if sun_altitude <= 0:
        return 0
    
    # Zero production with zero radiation
    if ghi <= 0:
        return 0
    
    # Maximum production (system capacity with some margin)
    max_production = 12000  # 10.5 kWp system with some margin
    
    # Constrain the production
    constrained_production = max(0, min(theoretical_production, max_production))
    
    return constrained_production


def calculate_battery_state(
    pv_production: float,
    consumption: float,
    prev_battery_state: float,
    battery_max_capacity: float,
) -> float:
    """
    Calculate the battery state based on production, consumption, and previous state.

    Args:
        pv_production: PV production in watts
        consumption: Energy consumption in watts
        prev_battery_state: Previous battery state in kWh
        battery_max_capacity: Maximum battery capacity in kWh

    Returns:
        New battery state in kWh
    """
    # Battery efficiency (typically 90-95%)
    battery_efficiency = 0.95
    
    # Minimum battery state (10% of capacity)
    battery_min = 0.1 * battery_max_capacity
    
    # Calculate energy balance
    energy_balance = pv_production - consumption
    
    # Convert from watts to kWh (assuming 1-hour intervals)
    energy_balance_kwh = energy_balance / 1000
    
    # Update battery state
    if energy_balance > 0:
        # Charging (with efficiency loss)
        new_battery_state = prev_battery_state + energy_balance_kwh * battery_efficiency
    else:
        # Discharging
        new_battery_state = prev_battery_state + energy_balance_kwh / battery_efficiency
    
    # Constrain battery state
    new_battery_state = max(battery_min, min(new_battery_state, battery_max_capacity))
    
    return new_battery_state


def calculate_grid_interaction(
    pv_production: float,
    consumption: float,
    battery_state: float,
    battery_max_capacity: float,
) -> float:
    """
    Calculate the grid interaction based on production, consumption, and battery state.

    Args:
        pv_production: PV production in watts
        consumption: Energy consumption in watts
        battery_state: Battery state in kWh
        battery_max_capacity: Maximum battery capacity in kWh

    Returns:
        Grid power in watts (positive for import, negative for export)
    """
    # Minimum battery state (10% of capacity)
    battery_min = 0.1 * battery_max_capacity
    
    # Calculate energy balance
    energy_balance = pv_production - consumption
    
    # Calculate available battery capacity
    available_battery_capacity = (battery_state - battery_min) * 1000  # Convert to watts
    
    # Calculate grid interaction
    if energy_balance > 0:
        # Production exceeds consumption
        if battery_state < battery_max_capacity:
            # Battery can be charged
            excess_energy = max(0, energy_balance - (battery_max_capacity - battery_state) * 1000)
            grid_power = -excess_energy  # Export excess energy
        else:
            # Battery is full, export all excess energy
            grid_power = -energy_balance
    else:
        # Consumption exceeds production
        energy_deficit = -energy_balance
        if available_battery_capacity > energy_deficit:
            # Battery can cover the deficit
            grid_power = 0
        else:
            # Need to import energy from the grid
            grid_power = energy_deficit - available_battery_capacity
    
    return grid_power
