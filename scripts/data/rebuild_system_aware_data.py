#!/usr/bin/env python3
"""
Rebuild System-Aware Data - Create training data with system_id

This script:
1. Processes the Excel files to extract system-specific data
2. Maps data to system_id (1 = Σπίτι Πάνω, 2 = Σπίτι Κάτω)
3. Rebuilds the training data with proper system identification
4. Creates new normalized training data for Enhanced Model v3
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import logging
import psycopg2
from psycopg2.extras import RealDictCursor
from dotenv import load_dotenv

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SystemAwareDataBuilder:
    """Build system-aware training data from Excel files"""
    
    def __init__(self):
        load_dotenv()
        self.connection = None
        self.system_mapping = {
            'Σπίτι Πάνω': 1,
            'Σπίτι Κάτω': 2
        }
        
        # File paths
        self.file_paths = {
            'system1': [
                "/home/<USER>/Downloads/new bot/solax/Plant Reports 2024-03-01-2024-06-28.xlsx",
                "/home/<USER>/Downloads/new bot/solax/Plant Reports 2024-07-01-2024-10-28.xlsx",
                "/home/<USER>/Downloads/new bot/solax/Plant Reports 2024-10-28-2025-02-24.xlsx",
                "/home/<USER>/Downloads/new bot/solax/Plant Reports 2025-02-24-2025-04-11.xlsx"
            ],
            'system2': [
                "/home/<USER>/Downloads/new bot/backup_old_files/data_exports/H34A15J2850106-2024-03-01-2024-06-28.xlsx",
                "/home/<USER>/Downloads/new bot/backup_old_files/data_exports/H34A15J2850106-2024-10-25-2025-02-21.xlsx",
                "/home/<USER>/Downloads/new bot/backup_old_files/data_exports/Plant Reports 2024-06-28-2024-10-25.xlsx",
                "/home/<USER>/Downloads/new bot/backup_old_files/data_exports/Plant Reports 2025-02-22-2025-04-14.xlsx"
            ]
        }
    
    def get_db_connection(self):
        """Get database connection"""
        if self.connection is None:
            self.connection = psycopg2.connect(
                host=os.getenv('DB_HOST', 'localhost'),
                database=os.getenv('DB_NAME', 'solar_prediction'),
                user=os.getenv('DB_USER', 'postgres'),
                password=os.getenv('DB_PASSWORD', 'postgres')
            )
        return self.connection
    
    def parse_excel_file(self, file_path: str, system_id: int) -> pd.DataFrame:
        """Parse Excel file and extract system data"""
        logger.info(f"📊 Processing: {Path(file_path).name}")
        
        try:
            # Read Excel file
            df = pd.read_excel(file_path)
            
            # Identify system name from first column
            first_col = df.columns[0]
            if 'Σπίτι Πάνω' in first_col:
                system_name = 'Σπίτι Πάνω'
            elif 'Σπίτι Κάτω' in first_col:
                system_name = 'Σπίτι Κάτω'
            else:
                logger.warning(f"Cannot identify system from {first_col}")
                return pd.DataFrame()
            
            logger.info(f"   System: {system_name} (ID: {system_id})")
            
            # Find header row (usually row 1 or 2)
            header_row = None
            for i in range(min(5, len(df))):
                if 'Update time' in str(df.iloc[i, 1]):
                    header_row = i
                    break
            
            if header_row is None:
                logger.error(f"Cannot find header row in {file_path}")
                return pd.DataFrame()
            
            # Set proper headers
            new_headers = df.iloc[header_row].values
            df_data = df.iloc[header_row + 1:].copy()
            df_data.columns = new_headers
            
            # Clean and standardize column names
            column_mapping = {
                'Update time': 'timestamp',
                'Daily PV Yield(kWh)': 'yield_today',
                'Daily PV Yield (kWh)': 'yield_today',
                'Daily inverter output (kWh)': 'inverter_output',
                'Daily exported energy(kWh)': 'feedin_energy',
                'Daily exported energy (kWh)': 'feedin_energy',
                'Daily consumed energy(kWh)': 'consume_energy',
                'Daily consumed energy (kWh)': 'consume_energy',
                'Export power(W)': 'feedin_power',
                'Export power (W)': 'feedin_power',
                'AC power(W)': 'ac_power',
                'AC power (W)': 'ac_power',
                'SOC(%)': 'soc',
                'SOC (%)': 'soc',
                'Battery power(W)': 'bat_power',
                'Battery power (W)': 'bat_power'
            }
            
            # Rename columns
            df_data = df_data.rename(columns=column_mapping)
            
            # Add system identification
            df_data['system_id'] = system_id
            df_data['system_name'] = system_name
            
            # Convert timestamp
            df_data['timestamp'] = pd.to_datetime(df_data['timestamp'], errors='coerce')
            
            # Convert numeric columns
            numeric_columns = ['yield_today', 'inverter_output', 'feedin_energy', 'consume_energy', 
                             'feedin_power', 'ac_power', 'soc', 'bat_power']
            
            for col in numeric_columns:
                if col in df_data.columns:
                    df_data[col] = pd.to_numeric(df_data[col], errors='coerce')
            
            # Remove rows with invalid timestamps
            df_data = df_data.dropna(subset=['timestamp'])
            
            # Calculate AC power if missing (from export power)
            if 'ac_power' not in df_data.columns and 'feedin_power' in df_data.columns:
                df_data['ac_power'] = df_data['feedin_power'].abs()
            
            # Fill missing values with defaults
            df_data['soc'] = df_data['soc'].fillna(50)  # Default SOC
            df_data['bat_power'] = df_data['bat_power'].fillna(0)  # Default battery power
            
            logger.info(f"   Processed: {len(df_data)} records from {df_data['timestamp'].min()} to {df_data['timestamp'].max()}")
            
            return df_data[['timestamp', 'system_id', 'system_name', 'ac_power', 'yield_today', 
                           'soc', 'bat_power', 'feedin_power', 'feedin_energy', 'consume_energy']]
            
        except Exception as e:
            logger.error(f"Error processing {file_path}: {e}")
            return pd.DataFrame()
    
    def process_all_files(self) -> pd.DataFrame:
        """Process all Excel files and combine data"""
        logger.info("🔄 PROCESSING ALL SYSTEM FILES")
        logger.info("=" * 50)
        
        all_data = []
        
        # Process System 1 files
        logger.info("📊 Processing System 1 (Σπίτι Πάνω) files...")
        for file_path in self.file_paths['system1']:
            if Path(file_path).exists():
                df = self.parse_excel_file(file_path, 1)
                if not df.empty:
                    all_data.append(df)
            else:
                logger.warning(f"File not found: {file_path}")
        
        # Process System 2 files
        logger.info("📊 Processing System 2 (Σπίτι Κάτω) files...")
        for file_path in self.file_paths['system2']:
            if Path(file_path).exists():
                df = self.parse_excel_file(file_path, 2)
                if not df.empty:
                    all_data.append(df)
            else:
                logger.warning(f"File not found: {file_path}")
        
        if not all_data:
            logger.error("No data processed from any files!")
            return pd.DataFrame()
        
        # Combine all data
        combined_df = pd.concat(all_data, ignore_index=True)
        
        # Remove duplicates based on timestamp and system_id
        combined_df = combined_df.drop_duplicates(subset=['timestamp', 'system_id'])
        
        # Sort by timestamp
        combined_df = combined_df.sort_values(['system_id', 'timestamp'])
        
        logger.info(f"✅ Combined data: {len(combined_df)} total records")
        logger.info(f"   System 1: {len(combined_df[combined_df['system_id'] == 1])} records")
        logger.info(f"   System 2: {len(combined_df[combined_df['system_id'] == 2])} records")
        logger.info(f"   Date range: {combined_df['timestamp'].min()} to {combined_df['timestamp'].max()}")
        
        return combined_df
    
    def add_consumption_patterns(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add consumption pattern features based on analysis"""
        logger.info("🏠 Adding consumption pattern features...")
        
        # Historical consumption patterns (from yesterday's analysis)
        consumption_patterns = {
            1: {  # System 1 (Σπίτι Πάνω)
                'daily_consumption_avg': 22.45,
                'grid_dependency_ratio': 0.0,
                'self_sufficiency': 1.0,
                'consumption_pattern': 'low_steady'
            },
            2: {  # System 2 (Σπίτι Κάτω)
                'daily_consumption_avg': 35.29,
                'grid_dependency_ratio': 0.395,
                'self_sufficiency': 0.605,
                'consumption_pattern': 'high_variable'
            }
        }
        
        # Add consumption features
        for system_id, patterns in consumption_patterns.items():
            mask = df['system_id'] == system_id
            df.loc[mask, 'daily_consumption_avg'] = patterns['daily_consumption_avg']
            df.loc[mask, 'grid_dependency_ratio'] = patterns['grid_dependency_ratio']
            df.loc[mask, 'self_sufficiency'] = patterns['self_sufficiency']
            df.loc[mask, 'consumption_pattern_encoded'] = 1 if patterns['consumption_pattern'] == 'low_steady' else 2
        
        return df
    
    def merge_with_weather_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Merge with weather data from database"""
        logger.info("🌤️  Merging with weather data...")
        
        conn = self.get_db_connection()
        
        # Get weather data
        weather_query = """
        SELECT timestamp, ghi, dni, dhi, temperature, cloud_cover, aod
        FROM cams_radiation_data
        WHERE timestamp >= %s AND timestamp <= %s
        ORDER BY timestamp
        """
        
        min_date = df['timestamp'].min()
        max_date = df['timestamp'].max()
        
        weather_df = pd.read_sql(weather_query, conn, params=[min_date, max_date])
        
        logger.info(f"   Weather data: {len(weather_df)} records")
        
        # Merge on hourly basis
        df['hour_timestamp'] = df['timestamp'].dt.floor('h')
        weather_df['hour_timestamp'] = pd.to_datetime(weather_df['timestamp']).dt.floor('h')
        
        merged_df = df.merge(weather_df, on='hour_timestamp', how='left', suffixes=('', '_weather'))
        
        # Fill missing weather data with interpolation
        weather_columns = ['ghi', 'dni', 'dhi', 'temperature', 'cloud_cover', 'aod']
        for col in weather_columns:
            if col in merged_df.columns:
                merged_df[col] = merged_df[col].interpolate()
        
        logger.info(f"   Merged data: {len(merged_df)} records with weather")
        
        return merged_df
    
    def save_to_database(self, df: pd.DataFrame):
        """Save system-aware data to new database table"""
        logger.info("💾 Saving to database...")
        
        conn = self.get_db_connection()
        cursor = conn.cursor()
        
        # Create new table for system-aware data
        create_table_query = """
        CREATE TABLE IF NOT EXISTS system_aware_training_data (
            id SERIAL PRIMARY KEY,
            timestamp TIMESTAMP NOT NULL,
            system_id INTEGER NOT NULL,
            system_name VARCHAR(50),
            ac_power FLOAT,
            yield_today FLOAT,
            soc FLOAT,
            bat_power FLOAT,
            feedin_power FLOAT,
            feedin_energy FLOAT,
            consume_energy FLOAT,
            daily_consumption_avg FLOAT,
            grid_dependency_ratio FLOAT,
            self_sufficiency FLOAT,
            consumption_pattern_encoded INTEGER,
            ghi FLOAT,
            dni FLOAT,
            dhi FLOAT,
            temperature FLOAT,
            cloud_cover FLOAT,
            aod FLOAT,
            created_at TIMESTAMP DEFAULT NOW()
        );
        
        CREATE INDEX IF NOT EXISTS idx_system_aware_timestamp ON system_aware_training_data(timestamp);
        CREATE INDEX IF NOT EXISTS idx_system_aware_system_id ON system_aware_training_data(system_id);
        """
        
        cursor.execute(create_table_query)
        
        # Clear existing data
        cursor.execute("DELETE FROM system_aware_training_data")
        
        # Insert new data
        insert_query = """
        INSERT INTO system_aware_training_data (
            timestamp, system_id, system_name, ac_power, yield_today, soc, bat_power,
            feedin_power, feedin_energy, consume_energy, daily_consumption_avg,
            grid_dependency_ratio, self_sufficiency, consumption_pattern_encoded,
            ghi, dni, dhi, temperature, cloud_cover, aod
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        # Prepare data for insertion
        records = []
        for _, row in df.iterrows():
            records.append((
                row['timestamp'], row['system_id'], row['system_name'],
                row['ac_power'], row['yield_today'], row['soc'], row['bat_power'],
                row.get('feedin_power'), row.get('feedin_energy'), row.get('consume_energy'),
                row.get('daily_consumption_avg'), row.get('grid_dependency_ratio'),
                row.get('self_sufficiency'), row.get('consumption_pattern_encoded'),
                row.get('ghi'), row.get('dni'), row.get('dhi'),
                row.get('temperature'), row.get('cloud_cover'), row.get('aod')
            ))
        
        cursor.executemany(insert_query, records)
        conn.commit()
        
        logger.info(f"✅ Saved {len(records)} records to system_aware_training_data table")

def main():
    """Main function"""
    logger.info("🚀 REBUILDING SYSTEM-AWARE TRAINING DATA")
    logger.info("=" * 70)
    
    try:
        builder = SystemAwareDataBuilder()
        
        # 1. Process all Excel files
        combined_df = builder.process_all_files()
        
        if combined_df.empty:
            logger.error("❌ No data processed. Check file paths and formats.")
            return
        
        # 2. Add consumption patterns
        combined_df = builder.add_consumption_patterns(combined_df)
        
        # 3. Merge with weather data
        final_df = builder.merge_with_weather_data(combined_df)
        
        # 4. Save to database
        builder.save_to_database(final_df)
        
        logger.info("✅ SYSTEM-AWARE DATA REBUILD COMPLETE!")
        logger.info("🎯 Ready to train Enhanced Model v3 with system identification!")
        
    except Exception as e:
        logger.error(f"❌ Rebuild failed: {e}")
        raise

if __name__ == "__main__":
    main()
