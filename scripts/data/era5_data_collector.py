#!/usr/bin/env python3
"""
ERA5 Complete Data Collector - Fixed Version
Collects ERA5 reanalysis data from Climate Data Store (CDS) and updates database
Handles ZIP archives, correct variable mapping, and incremental updates
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import cdsapi
import xarray as xr
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import psycopg2
from tenacity import retry, wait_exponential, stop_after_attempt
import time
import zipfile
import tempfile

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ERA5CompleteUpdater:
    """ERA5 updater with all required variables and enhanced incremental capabilities"""
    
    def __init__(self):
        self.client = cdsapi.Client()
        self.latitude = 38.141348260997596
        self.longitude = 24.0071653937747
        
        # Optimized area to avoid cost limits
        self.area = [
            self.latitude + 0.01,   # North
            self.longitude - 0.01,  # West
            self.latitude - 0.01,   # South
            self.longitude + 0.01   # East
        ]
        
        # Complete variable list for all database columns
        self.variables = [
            '2m_temperature',
            'total_cloud_cover',
            'surface_solar_radiation_downwards',
            '10m_u_component_of_wind',
            '10m_v_component_of_wind',
            'surface_pressure'
        ]
        
        # Updated database configuration with environment variables
        self.db_config = {
            'host': os.getenv('DB_HOST', 'solar-prediction-db'),
            'port': int(os.getenv('DB_PORT', 5432)),
            'database': os.getenv('DB_NAME', 'solar_prediction'),
            'user': os.getenv('DB_USER', 'postgres'),
            'password': os.getenv('DB_PASSWORD', 'postgres')
        }
    
    def get_last_record_date(self) -> datetime.date:
        """Get the date of the last record in database with enhanced error handling"""
        
        try:
            conn = psycopg2.connect(**self.db_config)
            
            with conn.cursor() as cur:
                cur.execute("""
                    SELECT MAX(DATE(timestamp)) 
                    FROM era5_data 
                    WHERE source = 'era5_reanalysis'
                """)
                
                result = cur.fetchone()[0]
                
            conn.close()
            
            if result:
                logger.info(f"✅ Last ERA5 record found: {result}")
                return result + timedelta(days=1)
            else:
                default_date = datetime(2025, 6, 1).date()
                logger.info(f"📅 No ERA5 records found, starting from: {default_date}")
                return default_date
                
        except psycopg2.Error as e:
            logger.error(f"❌ Database error getting last record: {e}")
            return datetime(2025, 6, 1).date()
        except Exception as e:
            logger.error(f"❌ Unexpected error getting last record: {e}")
            return datetime(2025, 6, 1).date()
    
    def find_data_gaps(self, start_date: datetime.date, end_date: datetime.date) -> list:
        """Find gaps in ERA5 data between start and end dates"""
        
        try:
            conn = psycopg2.connect(**self.db_config)
            
            with conn.cursor() as cur:
                cur.execute("""
                    SELECT DATE(timestamp) as record_date
                    FROM era5_data
                    WHERE source = 'era5_reanalysis' 
                    AND DATE(timestamp) BETWEEN %s AND %s
                    ORDER BY record_date
                """, (start_date, end_date))
                
                existing_dates = {row[0] for row in cur.fetchall()}
                
            conn.close()
            
            # Generate all dates in range
            all_dates = set()
            current = start_date
            while current <= end_date:
                all_dates.add(current)
                current += timedelta(days=1)
            
            # Find missing dates
            missing_dates = sorted(all_dates - existing_dates)
            
            if missing_dates:
                logger.info(f"📊 Found {len(missing_dates)} missing ERA5 dates")
                return missing_dates
            else:
                logger.info("✅ No ERA5 data gaps found")
                return []
                
        except Exception as e:
            logger.error(f"❌ Error finding ERA5 data gaps: {e}")
            return []
    
    @retry(wait=wait_exponential(multiplier=1, min=4, max=120), stop=stop_after_attempt(3))
    def fetch_era5_chunk(self, start_date: datetime.date, end_date: datetime.date) -> str:
        """Fetch ERA5 data chunk with corrected parameters and retry logic"""
        
        logger.info(f"🌍 Fetching ERA5 data: {start_date} to {end_date}")
        
        # Generate specific date list (not all months/days)
        date_list = []
        current_date = start_date
        while current_date <= end_date:
            date_list.append(current_date.strftime('%Y-%m-%d'))
            current_date += timedelta(days=1)
        
        output_file = f"/tmp/era5_complete_{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}.zip"
        
        # Corrected request structure for new CDS API
        request = {
            'product_type': 'reanalysis',
            'format': 'netcdf',
            'variable': self.variables,
            'date': date_list,  # Specific dates only
            'time': ['06:00', '12:00', '18:00'],  # Limited hours to avoid cost limits
            'area': self.area,
        }
        
        logger.info(f"📡 ERA5 request: {len(self.variables)} variables, {len(date_list)} days")
        logger.info(f"📍 Area: {self.area}")
        
        try:
            self.client.retrieve('reanalysis-era5-single-levels', request, output_file)
            
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                logger.info(f"✅ ERA5 data retrieved: {file_size:,} bytes")
                return output_file
            else:
                raise Exception("ERA5 output file not created")
                
        except Exception as e:
            logger.error(f"❌ ERA5 retrieval failed: {e}")
            raise
    
    def preprocess_era5_chunk(self, downloaded_file: str) -> pd.DataFrame:
        """Preprocess ERA5 data with FIXED temporary file handling"""
        
        logger.info("🔧 Preprocessing ERA5 data...")
        
        try:
            # Check if file is ZIP archive
            if not zipfile.is_zipfile(downloaded_file):
                raise Exception(f"Downloaded file is not a ZIP archive: {downloaded_file}")
            
            # Extract ZIP contents to a temporary directory
            temp_dir = tempfile.mkdtemp()
            extracted_files = []
            
            with zipfile.ZipFile(downloaded_file, 'r') as zip_ref:
                file_list = zip_ref.namelist()
                logger.info(f"📋 ZIP contents: {file_list}")
                
                for file_name in file_list:
                    if file_name.endswith('.nc'):
                        extracted_path = zip_ref.extract(file_name, temp_dir)
                        extracted_files.append(extracted_path)
                        logger.info(f"✅ Extracted: {file_name} to {extracted_path}")
            
            if not extracted_files:
                raise Exception("No NetCDF files found in ZIP archive")
            
            # Process each NetCDF file and combine data
            all_datasets = []
            
            for file_path in extracted_files:
                logger.info(f"📊 Processing {os.path.basename(file_path)}...")
                
                # Try different engines to open NetCDF
                engines_to_try = ['netcdf4', 'h5netcdf', 'scipy']
                ds = None
                
                for engine in engines_to_try:
                    try:
                        ds = xr.open_dataset(file_path, engine=engine)
                        logger.info(f"✅ Opened {os.path.basename(file_path)} with {engine} engine")
                        break
                    except Exception as e:
                        logger.warning(f"❌ {engine} engine failed for {os.path.basename(file_path)}: {e}")
                        continue
                
                if ds is None:
                    logger.warning(f"❌ Could not open {os.path.basename(file_path)} with any engine")
                    continue
                
                try:
                    # Check available dimensions and coordinates
                    logger.info(f"   Dimensions: {list(ds.dims.keys())}")
                    logger.info(f"   Variables: {list(ds.data_vars.keys())}")
                    
                    # Use valid_time instead of time (ERA5 specific)
                    if 'valid_time' not in ds.coords:
                        logger.warning(f"❌ No valid_time coordinate in {os.path.basename(file_path)}")
                        ds.close()
                        continue
                    
                    # Extract data at location (nearest point)
                    point_data = ds.sel(
                        latitude=self.latitude,
                        longitude=self.longitude,
                        method='nearest'
                    )
                    
                    # Load data into memory before closing dataset
                    point_data = point_data.load()
                    all_datasets.append(point_data)
                    logger.info(f"✅ Processed {os.path.basename(file_path)}: {len(point_data.valid_time)} time steps")
                    
                except Exception as e:
                    logger.warning(f"❌ Error extracting location data from {os.path.basename(file_path)}: {e}")
                
                finally:
                    if ds is not None:
                        ds.close()
            
            # Clean up extracted files
            for file_path in extracted_files:
                try:
                    os.unlink(file_path)
                except:
                    pass
            
            try:
                os.rmdir(temp_dir)
            except:
                pass
            
            if not all_datasets:
                raise Exception("No data could be extracted from any NetCDF file")
            
            # Combine datasets
            if len(all_datasets) == 1:
                combined_data = all_datasets[0]
            else:
                combined_data = xr.merge(all_datasets)
            
            # Convert to DataFrame
            df = combined_data.to_dataframe().reset_index()
            
            # Rename valid_time to time for consistency
            if 'valid_time' in df.columns:
                df = df.rename(columns={'valid_time': 'time'})
            
            # Apply column mapping for ERA5 variable names
            column_mapping = {
                't2m': 'temperature_2m',
                'ssrd': 'surface_solar_radiation',
                'tcc': 'total_cloud_cover',
                'u10': 'u_wind_10m',
                'v10': 'v_wind_10m',
                'sp': 'surface_pressure',
                'r': 'relative_humidity'
            }
            
            # Rename columns if they exist
            for old_name, new_name in column_mapping.items():
                if old_name in df.columns:
                    df = df.rename(columns={old_name: new_name})
                    logger.info(f"✅ Mapped {old_name} → {new_name}")
            
            # Convert units
            if 'temperature_2m' in df.columns:
                df['temperature_2m'] = df['temperature_2m'] - 273.15  # Kelvin to Celsius
                logger.info("✅ Converted temperature from K to °C")
            
            if 'surface_solar_radiation' in df.columns:
                df['surface_solar_radiation'] = df['surface_solar_radiation'] / 3600  # J/m² to W/m²
                logger.info("✅ Converted solar radiation from J/m² to W/m²")
            
            if 'total_cloud_cover' in df.columns:
                df['total_cloud_cover'] = df['total_cloud_cover'] * 100  # fraction to %
                logger.info("✅ Converted cloud cover to percentage")
            
            if 'surface_pressure' in df.columns:
                df['surface_pressure'] = df['surface_pressure'] / 100  # Pa to hPa
                logger.info("✅ Converted pressure from Pa to hPa")
            
            # Calculate wind speed if wind components are available
            if 'u_wind_10m' in df.columns and 'v_wind_10m' in df.columns:
                df['wind_speed_10m'] = np.sqrt(df['u_wind_10m']**2 + df['v_wind_10m']**2)
                logger.info("✅ Calculated wind speed from components")
            
            # Add metadata columns
            df['latitude'] = self.latitude
            df['longitude'] = self.longitude
            df['source'] = 'era5_reanalysis'
            df['ingestion_run_id'] = f"era5_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Rename time to timestamp for database
            if 'time' in df.columns:
                df = df.rename(columns={'time': 'timestamp'})
            
            # Fill NaN values
            df = df.fillna(method='ffill').fillna(method='bfill').fillna(0)
            
            logger.info(f"✅ ERA5 preprocessing completed: {len(df)} records")
            logger.info(f"   Final columns: {list(df.columns)}")
            
            return df
            
        except Exception as e:
            logger.error(f"❌ ERA5 preprocessing failed: {e}")
            raise
    
    def save_to_database(self, df: pd.DataFrame) -> int:
        """Save ERA5 data to database with duplicate checking"""
        
        if df.empty:
            logger.info("📭 No ERA5 data to save")
            return 0
        
        logger.info(f"💾 Saving {len(df)} ERA5 records to database...")
        
        try:
            conn = psycopg2.connect(**self.db_config)
            records_saved = 0
            records_skipped = 0
            
            for _, row in df.iterrows():
                try:
                    # Check if record already exists
                    with conn.cursor() as cur:
                        cur.execute("""
                            SELECT COUNT(*) FROM era5_data 
                            WHERE timestamp = %s AND source = 'era5_reanalysis'
                        """, (row['timestamp'],))
                        
                        if cur.fetchone()[0] > 0:
                            records_skipped += 1
                            continue
                    
                    # Insert new record with all variables
                    with conn.cursor() as cur:
                        cur.execute("""
                            INSERT INTO era5_data (
                                timestamp, latitude, longitude, temperature_2m, 
                                surface_solar_radiation, total_cloud_cover, wind_speed_10m,
                                surface_pressure, relative_humidity, source, ingestion_run_id
                            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """, (
                            row['timestamp'],
                            row['latitude'],
                            row['longitude'],
                            row.get('temperature_2m'),
                            row.get('surface_solar_radiation'),
                            row.get('total_cloud_cover'),
                            row.get('wind_speed_10m'),
                            row.get('surface_pressure'),
                            row.get('relative_humidity'),
                            row['source'],
                            row['ingestion_run_id']
                        ))
                    
                    records_saved += 1
                    
                except Exception as e:
                    logger.warning(f"Error saving record {row['timestamp']}: {e}")
                    continue
            
            conn.commit()
            conn.close()
            
            logger.info(f"✅ Database update completed:")
            logger.info(f"   → Records saved: {records_saved}")
            logger.info(f"   → Records skipped (duplicates): {records_skipped}")
            
            return records_saved
            
        except Exception as e:
            logger.error(f"❌ Database save failed: {e}")
            return 0
    
    def update_database(self):
        """Main update method with comprehensive incremental strategy"""
        
        print("🌍 ERA5 Complete Database Updater")
        print("=" * 40)
        
        last_date = self.get_last_record_date()
        target_date = datetime.now().date() - timedelta(days=5)  # ERA5 has 5-day latency
        
        print(f"📅 Last record date: {last_date - timedelta(days=1)}")
        print(f"📅 Target end date: {target_date}")
        
        if last_date > target_date:
            print("✅ Database is up to date!")
            return True
        
        # Find any gaps in existing data
        gaps = self.find_data_gaps(datetime(2025, 6, 1).date(), target_date)
        missing_days = (target_date - last_date).days + 1
        total_work = len(gaps) + missing_days
        
        print(f"📊 Data Analysis:")
        print(f"   → Missing recent days: {missing_days}")
        print(f"   → Data gaps found: {len(gaps)}")
        print(f"   → Total work: {total_work} days")
        
        if total_work == 0:
            print("✅ No work needed!")
            return True
        
        # Process data gaps first
        total_saved = 0
        successful_chunks = 0
        failed_chunks = 0
        
        if gaps:
            print(f"\n🔧 Processing {len(gaps)} data gaps...")
            for gap_date in gaps:
                print(f"🔄 Processing gap: {gap_date}")
                try:
                    downloaded_file = self.fetch_era5_chunk(gap_date, gap_date)
                    df = self.preprocess_era5_chunk(downloaded_file)
                    records_saved = self.save_to_database(df)
                    total_saved += records_saved
                    successful_chunks += 1
                    
                    if os.path.exists(downloaded_file):
                        os.remove(downloaded_file)
                    
                    print(f"✅ Gap completed: {records_saved} records saved")
                    
                except Exception as e:
                    print(f"❌ Gap failed: {e}")
                    failed_chunks += 1
                
                # Respect API limits
                time.sleep(25)
        
        # Process new data
        if missing_days > 0:
            print(f"\n🔄 Processing {missing_days} new days...")
            chunk_size = 1  # 1 day at a time due to increased variable count
            current_start = last_date
            
            while current_start <= target_date:
                current_end = min(current_start + timedelta(days=chunk_size-1), target_date)
                
                print(f"🔄 Processing chunk: {current_start} to {current_end}")
                
                try:
                    downloaded_file = self.fetch_era5_chunk(current_start, current_end)
                    df = self.preprocess_era5_chunk(downloaded_file)
                    records_saved = self.save_to_database(df)
                    total_saved += records_saved
                    successful_chunks += 1
                    
                    if os.path.exists(downloaded_file):
                        os.remove(downloaded_file)
                    
                    print(f"✅ Chunk completed: {records_saved} records saved")
                    
                except Exception as e:
                    print(f"❌ Chunk failed: {e}")
                    failed_chunks += 1
                
                current_start = current_end + timedelta(days=1)
                
                if current_start <= target_date:
                    print("⏳ Waiting 25 seconds before next chunk...")
                    time.sleep(25)
        
        # Final summary
        print(f"\n📊 ERA5 Collection Summary:")
        print(f"   → Period processed: {last_date} to {target_date}")
        print(f"   → Total work: {total_work} days")
        print(f"   → Successful chunks: {successful_chunks}")
        print(f"   → Failed chunks: {failed_chunks}")
        print(f"   → Total records saved: {total_saved}")
        
        if successful_chunks > 0:
            print(f"\n✅ ERA5 database update completed successfully!")
            print(f"   → Database now contains data up to {target_date}")
            return True
        else:
            print(f"\n❌ ERA5 database update failed!")
            print(f"   → No data was successfully processed")
            return False

def main():
    """Main function"""
    try:
        updater = ERA5CompleteUpdater()
        success = updater.update_database()
        return success
    except Exception as e:
        logger.error(f"❌ ERA5 update failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

