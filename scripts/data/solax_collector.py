#!/usr/bin/env python3
"""
SolaX Real-time Data Collector
Collects data from both SolaX systems and saves to database
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import requests
import json
import psycopg2
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def collect_solax_data():
    """Collect data from both SolaX systems"""

    try:
        # System configurations
        systems = {
            1: {
                "name": "Σπίτι Πάνω",
                "wifi_sn": "SRFQDPDN9W",
                "table": "solax_data"
            },
            2: {
                "name": "Σπίτι Κάτω",
                "wifi_sn": "SRCV9TUD6S",
                "table": "solax_data2"
            }
        }

        token_id = "20250410220826567911082"
        api_url = "https://www.solaxcloud.com:9443/proxy/api/getRealtimeInfo.do"

        success_count = 0

        for system_id, system in systems.items():
            try:
                # Prepare request as GET params
                params = {
                    "tokenId": token_id,
                    "sn": system["wifi_sn"]
                }

                # Make API call
                response = requests.get(api_url, params=params, timeout=30)

                if response.status_code == 200:
                    data = response.json()

                    if data.get("success") and data.get("result"):
                        result = data["result"]

                        # Save to database - use environment variables
                        db_host = os.getenv('DATABASE_HOST', 'localhost')
                        db_port = int(os.getenv('DATABASE_PORT', 5433))
                        db_name = os.getenv('DATABASE_NAME', 'solar_prediction')
                        db_user = os.getenv('DATABASE_USER', 'postgres')
                        db_password = os.getenv('DATABASE_PASSWORD', 'postgres')

                        conn = psycopg2.connect(
                            host=db_host, port=db_port,
                            database=db_name,
                            user=db_user,
                            password=db_password
                        )

                        with conn.cursor() as cur:
                            timestamp = datetime.now()

                            # Use SAME schema for both tables (FIXED)
                            if system["table"] == "solax_data2":
                                # FIXED: Include ALL fields for solax_data2
                                cur.execute(f"""
                                    INSERT INTO {system["table"]} (
                                        timestamp, inverter_sn, wifi_sn, inverter_type, inverter_status,
                                        upload_time, ac_power, powerdc1, powerdc2, yield_today, yield_total,
                                        soc, bat_power, feedin_power, feedin_energy, consume_energy,
                                        temperature, raw_data, created_at
                                    ) VALUES (
                                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                                    )
                                """, (
                                    timestamp,
                                    result.get('inverterSN', ''),
                                    result.get('wifiSN', ''),
                                    result.get('inverterType', ''),
                                    result.get('inverterStatus', 0),
                                    result.get('uploadTime', ''),
                                    result.get('acpower', 0),
                                    result.get('powerdc1', 0),
                                    result.get('powerdc2', 0),
                                    result.get('yieldtoday', 0),
                                    result.get('yieldtotal', 0),
                                    result.get('soc', 0),
                                    result.get('batPower', 0),
                                    result.get('feedinpower', 0),
                                    result.get('feedinenergy', 0),
                                    result.get('consumeenergy', 0),
                                    result.get('temperature', result.get('batTemperature', 0)),
                                    json.dumps(data),
                                    timestamp
                                ))
                            else:
                                # Full schema for solax_data
                                cur.execute(f"""
                                    INSERT INTO {system["table"]} (
                                        timestamp, inverter_sn, wifi_sn, inverter_type, inverter_status,
                                        upload_time, ac_power, powerdc1, powerdc2, yield_today, yield_total,
                                        soc, bat_power, feedin_power, feedin_energy, consume_energy,
                                        temperature, raw_data, created_at, system_id
                                    ) VALUES (
                                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                                    )
                                """, (
                                    timestamp,
                                    result.get('inverterSN', ''),
                                    result.get('wifiSN', ''),
                                    result.get('inverterType', ''),
                                    result.get('inverterStatus', 0),
                                    result.get('uploadTime', ''),
                                    result.get('acpower', 0),
                                    result.get('powerdc1', 0),
                                    result.get('powerdc2', 0),
                                    result.get('yieldtoday', 0),
                                    result.get('yieldtotal', 0),
                                    result.get('soc', 0),
                                    result.get('batPower', 0),
                                    result.get('feedinpower', 0),
                                    result.get('feedinenergy', 0),
                                    result.get('consumeenergy', 0),
                                    result.get('temperature', result.get('batTemperature', 0)),
                                    json.dumps(data),
                                    timestamp,
                                    system_id
                                ))

                        conn.commit()
                        conn.close()

                        success_count += 1
                        logger.info(f"✅ {system['name']}: {result.get('acpower', 0)}W, SOC: {result.get('soc', 0)}%")

                    else:
                        logger.error(f"❌ {system['name']}: API returned no data")

                else:
                    logger.error(f"❌ {system['name']}: HTTP {response.status_code}")

            except Exception as e:
                logger.error(f"❌ {system['name']}: {e}")
                continue

        logger.info(f"☀️ SolaX Collection completed: {success_count}/2 systems successful")
        return success_count > 0

    except Exception as e:
        logger.error(f"❌ SolaX Collection failed: {e}")
        return False

if __name__ == "__main__":
    print(f"☀️ SolaX Collection - {datetime.now()}")
    success = collect_solax_data()
    if success:
        print("✅ SolaX collection completed successfully")
    else:
        print("❌ SolaX collection failed")
        sys.exit(1)
