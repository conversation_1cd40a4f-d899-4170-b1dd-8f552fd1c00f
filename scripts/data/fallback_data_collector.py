#!/usr/bin/env python3
"""
Fallback Data Collector
Collects data from alternative sources when primary APIs fail
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import requests
import json
import psycopg2
from datetime import datetime, timedelta
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FallbackDataCollector:
    """Fallback data collector using alternative APIs"""
    
    def __init__(self):
        self.latitude = 38.141348260997596
        self.longitude = 24.0071653937747
    
    def collect_openweather_solar_data(self, date):
        """Collect solar-like data from OpenWeatherMap API"""
        
        try:
            # Use OpenWeatherMap One Call API (free tier)
            # This is a fallback for CAMS solar data
            
            # For demo purposes, we'll use Open-Meteo which is free
            url = "https://archive-api.open-meteo.com/v1/archive"
            
            params = {
                'latitude': self.latitude,
                'longitude': self.longitude,
                'start_date': date.strftime('%Y-%m-%d'),
                'end_date': date.strftime('%Y-%m-%d'),
                'hourly': [
                    'shortwave_radiation',
                    'direct_radiation',
                    'diffuse_radiation',
                    'temperature_2m',
                    'cloud_cover'
                ],
                'timezone': 'Europe/Athens'
            }
            
            logger.info(f"🌤️ Fetching fallback solar data for {date.strftime('%Y-%m-%d')}")
            
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            if 'hourly' in data:
                hourly = data['hourly']
                times = hourly['time']
                
                records_saved = 0
                
                # Save to CAMS radiation table as fallback data
                conn = psycopg2.connect(
                    host=os.getenv('DB_HOST', 'solar-prediction-db'),
                    port=int(os.getenv('DB_PORT', 5432)),
                    database=os.getenv('DB_NAME', 'solar_prediction'),
                    user=os.getenv('DB_USER', 'postgres'),
                    password=os.getenv('DB_PASSWORD', 'postgres')
                )
                
                for i, time_str in enumerate(times):
                    timestamp = datetime.fromisoformat(time_str.replace('T', ' '))
                    
                    with conn.cursor() as cur:
                        cur.execute("""
                            INSERT INTO cams_radiation_data (
                                timestamp, latitude, longitude, ghi, dni, dhi,
                                temperature, cloud_cover, source, ingestion_run_id
                            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                            ON CONFLICT (timestamp, source) DO NOTHING
                        """, (
                            timestamp,
                            self.latitude,
                            self.longitude,
                            hourly['shortwave_radiation'][i] if hourly['shortwave_radiation'][i] else 0,
                            hourly['direct_radiation'][i] if hourly['direct_radiation'][i] else 0,
                            hourly['diffuse_radiation'][i] if hourly['diffuse_radiation'][i] else 0,
                            hourly['temperature_2m'][i] if hourly['temperature_2m'][i] else 0,
                            hourly['cloud_cover'][i] if hourly['cloud_cover'][i] else 0,
                            'fallback_openmeteo',
                            f"fallback_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                        ))
                    
                    records_saved += 1
                
                conn.commit()
                conn.close()
                
                logger.info(f"✅ Fallback solar data saved: {records_saved} records")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"❌ Fallback solar collection failed: {e}")
            return False
    
    def collect_synthetic_aerosol_data(self, date):
        """Generate synthetic aerosol data based on weather patterns"""
        
        try:
            logger.info(f"🌫️ Generating synthetic aerosol data for {date.strftime('%Y-%m-%d')}")
            
            # Generate synthetic aerosol data based on typical Mediterranean values
            conn = psycopg2.connect(
                host=os.getenv('DB_HOST', 'solar-prediction-db'),
                port=int(os.getenv('DB_PORT', 5432)),
                database=os.getenv('DB_NAME', 'solar_prediction'),
                user=os.getenv('DB_USER', 'postgres'),
                password=os.getenv('DB_PASSWORD', 'postgres')
            )
            
            # Generate data for noon
            timestamp = datetime.combine(date.date(), datetime.min.time().replace(hour=12))
            
            with conn.cursor() as cur:
                cur.execute("""
                    INSERT INTO cams_aerosol_data (
                        timestamp, latitude, longitude, total_cloud_cover,
                        total_aerosol_optical_depth, source, ingestion_run_id, created_at
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (timestamp, source) DO NOTHING
                """, (
                    timestamp,
                    self.latitude,
                    self.longitude,
                    30.0,  # Typical cloud cover for Greece
                    0.15,  # Typical AOD for Mediterranean
                    'synthetic_fallback',
                    f"synthetic_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    datetime.now()
                ))
            
            conn.commit()
            conn.close()
            
            logger.info(f"✅ Synthetic aerosol data saved")
            return True
            
        except Exception as e:
            logger.error(f"❌ Synthetic aerosol generation failed: {e}")
            return False
    
    def collect_synthetic_satellite_data(self, date):
        """Generate synthetic satellite data"""
        
        try:
            logger.info(f"🛰️ Generating synthetic satellite data for {date.strftime('%Y-%m-%d')}")
            
            conn = psycopg2.connect(
                host=os.getenv('DB_HOST', 'solar-prediction-db'),
                port=int(os.getenv('DB_PORT', 5432)),
                database=os.getenv('DB_NAME', 'solar_prediction'),
                user=os.getenv('DB_USER', 'postgres'),
                password=os.getenv('DB_PASSWORD', 'postgres')
            )
            
            timestamp = datetime.combine(date.date(), datetime.min.time().replace(hour=12))
            
            with conn.cursor() as cur:
                cur.execute("""
                    INSERT INTO satellite_data (
                        timestamp, latitude, longitude, product_id, collection,
                        cloud_cover, source, ingestion_run_id, created_at
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (product_id, source) DO NOTHING
                """, (
                    timestamp,
                    self.latitude,
                    self.longitude,
                    f"SYNTHETIC_{date.strftime('%Y%m%d')}_12",
                    'synthetic_satellite',
                    25.0,  # Typical cloud cover
                    'synthetic_fallback',
                    f"synthetic_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    datetime.now()
                ))
            
            conn.commit()
            conn.close()
            
            logger.info(f"✅ Synthetic satellite data saved")
            return True
            
        except Exception as e:
            logger.error(f"❌ Synthetic satellite generation failed: {e}")
            return False

def main():
    """Main fallback collection function"""
    
    print("🔄 Fallback Data Collector")
    print("=" * 40)
    
    collector = FallbackDataCollector()
    
    # Test with yesterday's date
    test_date = datetime.now() - timedelta(days=1)
    
    print(f"🧪 Testing fallback collection for: {test_date.strftime('%Y-%m-%d')}")
    
    # Test all fallback methods
    solar_result = collector.collect_openweather_solar_data(test_date)
    aerosol_result = collector.collect_synthetic_aerosol_data(test_date)
    satellite_result = collector.collect_synthetic_satellite_data(test_date)
    
    print(f"\n📊 Fallback Collection Results:")
    print(f"   Solar (Open-Meteo): {'success' if solar_result else 'failed'}")
    print(f"   Aerosol (Synthetic): {'success' if aerosol_result else 'failed'}")
    print(f"   Satellite (Synthetic): {'success' if satellite_result else 'failed'}")
    
    success_count = sum([solar_result, aerosol_result, satellite_result])
    
    if success_count >= 2:
        print(f"\n✅ Fallback collection successful ({success_count}/3)")
        return True
    else:
        print(f"\n❌ Fallback collection failed ({success_count}/3)")
        return False

if __name__ == "__main__":
    print(f"🔄 Fallback Collection - {datetime.now()}")
    success = main()
    if success:
        print("✅ Fallback collection completed successfully")
    else:
        print("❌ Fallback collection failed")
        sys.exit(1)
