#!/usr/bin/env python3
"""
Process Gap Data (2025-04-16 to 2025-05-30)

This script processes the gap data to complete the dataset:
1. Process SolaX Excel exports → solax_data, solax_data2
2. Use collected weather data → weather_data
3. Use collected CAMS data → cams_radiation_data
4. Run integration pipeline → update normalized_training_data

Strategy: Fill the gap between existing data and current date
"""

import asyncio
import pandas as pd
import logging
import sys
import os
from datetime import datetime, timedelta
import json

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class GapDataProcessor:
    """Process gap data to complete the dataset."""
    
    def __init__(self):
        """Initialize gap data processor."""
        self.gap_period = {
            "start_date": "2025-04-16",
            "end_date": "2025-05-30"
        }
        
        self.file_paths = {
            "solax_system_1": "data/raw/Plant Reports 2025-04-16-2025-05-30 (1).xlsx",  # Σπίτι Πάνω
            "solax_system_2": "data/raw/Plant Reports 2025-04-16-2025-05-30.xlsx",      # Σπίτι Κάτω
            "weather_data": "data/processed/weather_gap_data_2025-04-16_to_2025-05-30.csv",
            "cams_data": "data/processed/synthetic_cams_data.csv"
        }
        
        self.processed_data = {}
        
        logger.info(f"🔄 Gap Data Processor initialized for period: {self.gap_period['start_date']} to {self.gap_period['end_date']}")
    
    def process_solax_excel_data(self, filepath: str, system_name: str, system_id: int) -> pd.DataFrame:
        """Process SolaX Excel data."""
        logger.info(f"📊 Processing {system_name} data from {os.path.basename(filepath)}")
        
        try:
            # Read Excel file, skipping the header row
            df = pd.read_excel(filepath, skiprows=1)
            
            # Clean up column names
            df.columns = [
                'no',
                'timestamp', 
                'daily_pv_yield_kwh',
                'daily_inverter_output_kwh', 
                'daily_exported_energy_kwh',
                'daily_imported_energy_kwh',
                'export_power_w',
                'daily_consumed_kwh'
            ]
            
            # Convert timestamp
            df['timestamp'] = pd.to_datetime(df['timestamp'], errors='coerce')
            
            # Remove rows with invalid timestamps
            df = df.dropna(subset=['timestamp'])
            
            # Convert to SolaX data format
            processed_df = pd.DataFrame({
                'timestamp': df['timestamp'],
                'system_id': system_id,
                'system_name': system_name,
                'ac_power': df['export_power_w'].abs(),  # Make positive for production
                'yield_today': df['daily_pv_yield_kwh'].fillna(0),
                'feedin_power': df['export_power_w'].fillna(0),
                'feedin_energy': df['daily_exported_energy_kwh'].fillna(0),
                'consume_energy': df['daily_consumed_kwh'].fillna(0),
                'powerdc1': (df['export_power_w'].abs() * 0.5).fillna(0),  # Estimate DC power split
                'powerdc2': (df['export_power_w'].abs() * 0.5).fillna(0),
                'soc': 50,  # Default SOC - not available in exports
                'bat_power': 0,  # Default battery power
                'temperature': None,  # Not available in exports
                'inverter_sn': f'system_{system_id}_sn',
                'wifi_sn': f'system_{system_id}_wifi'
            })
            
            logger.info(f"✅ Processed {len(processed_df)} records for {system_name}")
            logger.info(f"   Date range: {processed_df['timestamp'].min()} to {processed_df['timestamp'].max()}")
            
            return processed_df
            
        except Exception as e:
            logger.error(f"❌ Error processing {system_name} data: {e}")
            return pd.DataFrame()
    
    def process_weather_data(self, filepath: str) -> pd.DataFrame:
        """Process weather data."""
        logger.info(f"🌦️  Processing weather data from {os.path.basename(filepath)}")
        
        try:
            df = pd.read_csv(filepath)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            
            logger.info(f"✅ Processed {len(df)} weather records")
            logger.info(f"   Date range: {df['timestamp'].min()} to {df['timestamp'].max()}")
            
            return df
            
        except Exception as e:
            logger.error(f"❌ Error processing weather data: {e}")
            return pd.DataFrame()
    
    def process_cams_data(self, filepath: str) -> pd.DataFrame:
        """Process CAMS data."""
        logger.info(f"🌤️  Processing CAMS data from {os.path.basename(filepath)}")
        
        try:
            df = pd.read_csv(filepath)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            
            logger.info(f"✅ Processed {len(df)} CAMS records")
            logger.info(f"   Date range: {df['timestamp'].min()} to {df['timestamp'].max()}")
            
            return df
            
        except Exception as e:
            logger.error(f"❌ Error processing CAMS data: {e}")
            return pd.DataFrame()
    
    def save_processed_data(self):
        """Save processed data to CSV files."""
        logger.info("💾 Saving processed data to CSV files...")
        
        try:
            os.makedirs("data/processed/gap_data", exist_ok=True)
            
            for data_type, df in self.processed_data.items():
                if df.empty:
                    logger.warning(f"No data to save for {data_type}")
                    continue
                
                output_file = f"data/processed/gap_data/{data_type}_processed.csv"
                df.to_csv(output_file, index=False)
                logger.info(f"✅ Saved {len(df)} records to {output_file}")
            
            # Create summary file
            summary = {
                "processing_timestamp": datetime.now().isoformat(),
                "gap_period": self.gap_period,
                "processed_datasets": {
                    data_type: {
                        "records": len(df),
                        "date_range": {
                            "start": df['timestamp'].min().isoformat() if not df.empty and 'timestamp' in df.columns else None,
                            "end": df['timestamp'].max().isoformat() if not df.empty and 'timestamp' in df.columns else None
                        }
                    }
                    for data_type, df in self.processed_data.items()
                }
            }
            
            with open("data/processed/gap_data/processing_summary.json", "w") as f:
                json.dump(summary, f, indent=2)
            
            logger.info("✅ Processing summary saved")
            
        except Exception as e:
            logger.error(f"❌ Error saving processed data: {e}")
    
    def run_processing(self):
        """Run the complete gap data processing."""
        logger.info("🚀 Starting gap data processing...")
        
        # Process SolaX data
        logger.info("\n📊 Processing SolaX data...")
        
        # System 1 (Σπίτι Πάνω) → solax_data
        if os.path.exists(self.file_paths["solax_system_1"]):
            self.processed_data["solax_data"] = self.process_solax_excel_data(
                self.file_paths["solax_system_1"], 
                "Σπίτι Πάνω", 
                1
            )
        else:
            logger.error(f"❌ SolaX System 1 file not found: {self.file_paths['solax_system_1']}")
        
        # System 2 (Σπίτι Κάτω) → solax_data2
        if os.path.exists(self.file_paths["solax_system_2"]):
            self.processed_data["solax_data2"] = self.process_solax_excel_data(
                self.file_paths["solax_system_2"], 
                "Σπίτι Κάτω", 
                2
            )
        else:
            logger.error(f"❌ SolaX System 2 file not found: {self.file_paths['solax_system_2']}")
        
        # Process weather data
        logger.info("\n🌦️  Processing weather data...")
        if os.path.exists(self.file_paths["weather_data"]):
            self.processed_data["weather_data"] = self.process_weather_data(
                self.file_paths["weather_data"]
            )
        else:
            logger.error(f"❌ Weather data file not found: {self.file_paths['weather_data']}")
        
        # Process CAMS data
        logger.info("\n🌤️  Processing CAMS data...")
        if os.path.exists(self.file_paths["cams_data"]):
            self.processed_data["cams_radiation_data"] = self.process_cams_data(
                self.file_paths["cams_data"]
            )
        else:
            logger.error(f"❌ CAMS data file not found: {self.file_paths['cams_data']}")
        
        # Save processed data
        self.save_processed_data()
        
        # Summary
        logger.info("\n🎯 Gap Data Processing Summary:")
        total_records = 0
        for data_type, df in self.processed_data.items():
            record_count = len(df) if not df.empty else 0
            total_records += record_count
            logger.info(f"   {data_type}: {record_count:,} records")
        
        logger.info(f"   Total records processed: {total_records:,}")
        
        if total_records > 0:
            logger.info("\n✅ Gap data processing completed successfully!")
            logger.info("\n📋 Next Steps:")
            logger.info("1. ✅ Gap data processed and saved")
            logger.info("2. ⏳ Insert data into database tables")
            logger.info("3. ⏳ Run integration pipeline")
            logger.info("4. ⏳ Update normalized_training_data")
            logger.info("5. ⏳ Train ML model with complete dataset")
        else:
            logger.error("❌ No data was processed!")
        
        return total_records > 0

def main():
    """Main function."""
    logger.info("🔄 Gap Data Processing - Forward-Only Strategy")
    logger.info("=" * 60)
    
    processor = GapDataProcessor()
    success = processor.run_processing()
    
    if success:
        logger.info("\n🎉 Gap data processing completed successfully!")
    else:
        logger.error("\n❌ Gap data processing failed!")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
