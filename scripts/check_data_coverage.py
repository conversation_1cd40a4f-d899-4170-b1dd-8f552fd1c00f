#!/usr/bin/env python3
"""
Check data coverage from March 2024 to present
"""

import psycopg2
from datetime import datetime

def check_data_coverage():
    """Check complete data coverage from March 2024"""
    print("📊 COMPLETE DATA COVERAGE ANALYSIS")
    print("=" * 50)
    print("Target: March 2024 to Present")
    
    try:
        conn = psycopg2.connect(
            host="localhost",
            database="solar_prediction", 
            user="postgres",
            password="postgres"
        )
        cursor = conn.cursor()
        
        tables = [
            ('solax_data', 'System 1 (Σπίτι Πάνω)'),
            ('solax_data2', 'System 2 (Σπίτι Κάτω)'),
            ('weather_data', 'Weather Data')
        ]
        
        print(f"\n🔍 DATA COVERAGE ANALYSIS:")
        
        for table_name, description in tables:
            print(f"\n📋 {description} ({table_name}):")
            
            # Total records
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            total = cursor.fetchone()[0]
            
            # Date range
            cursor.execute(f"SELECT MIN(timestamp), MAX(timestamp) FROM {table_name}")
            min_date, max_date = cursor.fetchone()
            
            # Records from March 2024
            cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE timestamp >= '2024-03-01'")
            march_count = cursor.fetchone()[0]
            
            # Records from April 2025 (recent data)
            cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE timestamp >= '2025-04-01'")
            april_count = cursor.fetchone()[0]
            
            print(f"   📊 Total records: {total:,}")
            print(f"   📅 Date range: {min_date} to {max_date}")
            print(f"   🎯 From March 2024: {march_count:,} records")
            print(f"   🆕 From April 2025: {april_count:,} records")
            
            # Check for gaps
            if march_count == 0:
                print(f"   ❌ NO DATA from March 2024!")
            elif march_count < 1000:
                print(f"   ⚠️  LIMITED DATA from March 2024")
            else:
                print(f"   ✅ Good coverage from March 2024")
        
        # Check for historical data in other tables
        print(f"\n🔍 CHECKING OTHER HISTORICAL DATA SOURCES:")
        
        # Check cams_radiation_data
        cursor.execute("SELECT COUNT(*), MIN(timestamp), MAX(timestamp) FROM cams_radiation_data WHERE timestamp >= '2024-03-01'")
        cams_count, cams_min, cams_max = cursor.fetchone()
        print(f"   📡 CAMS Radiation: {cams_count:,} records ({cams_min} to {cams_max})")
        
        # Check if there are any backup or historical tables
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name LIKE '%backup%' 
            OR table_name LIKE '%historical%'
            OR table_name LIKE '%archive%'
        """)
        backup_tables = cursor.fetchall()
        
        if backup_tables:
            print(f"\n📦 BACKUP/HISTORICAL TABLES FOUND:")
            for (table_name,) in backup_tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"   📋 {table_name}: {count:,} records")
        
        # Summary and recommendations
        print(f"\n📋 SUMMARY & RECOMMENDATIONS:")
        
        # Check if we have March 2024 data
        cursor.execute("SELECT COUNT(*) FROM solax_data WHERE timestamp >= '2024-03-01' AND timestamp < '2024-04-01'")
        march_2024_solax = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM solax_data2 WHERE timestamp >= '2024-03-01' AND timestamp < '2024-04-01'")
        march_2024_solax2 = cursor.fetchone()[0]
        
        if march_2024_solax == 0 and march_2024_solax2 == 0:
            print("   ❌ CRITICAL: No solar data from March 2024")
            print("   🔧 ACTION NEEDED: Import historical data from backup files")
            print("   📁 Check: /home/<USER>/Downloads/new bot/solax")
            print("   📁 Check: /home/<USER>/Downloads/new bot/backup_old_files/data_exports")
        else:
            print("   ✅ Solar data available from March 2024")
            print("   🎯 Ready for Enhanced Model v3 training")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    check_data_coverage()
