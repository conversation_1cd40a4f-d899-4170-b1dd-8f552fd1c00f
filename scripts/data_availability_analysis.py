#!/usr/bin/env python3
"""
Data Availability Analysis
Comprehensive analysis of all available data sources
"""

import os
import subprocess
from datetime import datetime, timedelta

def run_sql_query(query, description):
    """Run SQL query and return results"""
    print(f"\n📊 {description}")
    print("=" * (len(description) + 4))
    
    try:
        cmd = ['psql', '-h', 'localhost', '-U', 'postgres', '-d', 'solar_prediction', '-c', query]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print(result.stdout)
            return True
        else:
            print(f"❌ Query failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def analyze_data_availability():
    """Analyze all data availability"""
    print("🚀 COMPREHENSIVE DATA AVAILABILITY ANALYSIS")
    print("=" * 55)
    print(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. System 1 (solax_data) Analysis
    run_sql_query("""
        SELECT 
            'System 1 (solax_data)' as system,
            COUNT(*) as total_records,
            MIN(DATE(timestamp)) as earliest_date,
            MAX(DATE(timestamp)) as latest_date,
            COUNT(DISTINCT DATE(timestamp)) as unique_days,
            ROUND(AVG(yield_today), 2) as avg_yield,
            ROUND(MIN(yield_today), 2) as min_yield,
            ROUND(MAX(yield_today), 2) as max_yield
        FROM solax_data
        WHERE yield_today >= 0 AND yield_today < 100;
    """, "SYSTEM 1 OVERALL DATA")
    
    # 2. System 2 (solax_data2) Analysis
    run_sql_query("""
        SELECT 
            'System 2 (solax_data2)' as system,
            COUNT(*) as total_records,
            MIN(DATE(timestamp)) as earliest_date,
            MAX(DATE(timestamp)) as latest_date,
            COUNT(DISTINCT DATE(timestamp)) as unique_days,
            ROUND(AVG(yield_today), 2) as avg_yield,
            ROUND(MIN(yield_today), 2) as min_yield,
            ROUND(MAX(yield_today), 2) as max_yield
        FROM solax_data2
        WHERE yield_today >= 0 AND yield_today < 100;
    """, "SYSTEM 2 OVERALL DATA")
    
    # 3. Monthly breakdown for System 1
    run_sql_query("""
        SELECT 
            EXTRACT(year FROM timestamp) as year,
            EXTRACT(month FROM timestamp) as month,
            COUNT(*) as records,
            COUNT(DISTINCT DATE(timestamp)) as unique_days,
            ROUND(AVG(yield_today), 2) as avg_yield,
            MIN(DATE(timestamp)) as first_date,
            MAX(DATE(timestamp)) as last_date
        FROM solax_data
        WHERE yield_today >= 0 AND yield_today < 100
        GROUP BY EXTRACT(year FROM timestamp), EXTRACT(month FROM timestamp)
        ORDER BY year, month;
    """, "SYSTEM 1 MONTHLY BREAKDOWN")
    
    # 4. Monthly breakdown for System 2
    run_sql_query("""
        SELECT 
            EXTRACT(year FROM timestamp) as year,
            EXTRACT(month FROM timestamp) as month,
            COUNT(*) as records,
            COUNT(DISTINCT DATE(timestamp)) as unique_days,
            ROUND(AVG(yield_today), 2) as avg_yield,
            MIN(DATE(timestamp)) as first_date,
            MAX(DATE(timestamp)) as last_date
        FROM solax_data2
        WHERE yield_today >= 0 AND yield_today < 100
        GROUP BY EXTRACT(year FROM timestamp), EXTRACT(month FROM timestamp)
        ORDER BY year, month;
    """, "SYSTEM 2 MONTHLY BREAKDOWN")
    
    # 5. Weather data analysis
    run_sql_query("""
        SELECT 
            'Weather Data' as data_type,
            COUNT(*) as total_records,
            MIN(DATE(timestamp)) as earliest_date,
            MAX(DATE(timestamp)) as latest_date,
            COUNT(DISTINCT DATE(timestamp)) as unique_days,
            ROUND(AVG(temperature_2m), 2) as avg_temperature,
            ROUND(AVG(global_horizontal_irradiance), 2) as avg_ghi,
            ROUND(AVG(cloud_cover), 2) as avg_cloud_cover
        FROM weather_data
        WHERE timestamp IS NOT NULL;
    """, "WEATHER DATA OVERALL")
    
    # 6. Weather data monthly breakdown
    run_sql_query("""
        SELECT 
            EXTRACT(year FROM timestamp) as year,
            EXTRACT(month FROM timestamp) as month,
            COUNT(*) as records,
            COUNT(DISTINCT DATE(timestamp)) as unique_days,
            ROUND(AVG(temperature_2m), 2) as avg_temp,
            ROUND(AVG(global_horizontal_irradiance), 2) as avg_ghi,
            MIN(DATE(timestamp)) as first_date,
            MAX(DATE(timestamp)) as last_date
        FROM weather_data
        WHERE timestamp IS NOT NULL
        GROUP BY EXTRACT(year FROM timestamp), EXTRACT(month FROM timestamp)
        ORDER BY year, month;
    """, "WEATHER DATA MONTHLY BREAKDOWN")
    
    # 7. Data gaps analysis - System 1
    run_sql_query("""
        WITH date_series AS (
            SELECT generate_series(
                '2024-03-01'::date,
                CURRENT_DATE,
                '1 day'::interval
            )::date as expected_date
        ),
        actual_dates AS (
            SELECT DISTINCT DATE(timestamp) as actual_date
            FROM solax_data
            WHERE yield_today >= 0 AND yield_today < 100
        )
        SELECT 
            COUNT(*) as total_expected_days,
            COUNT(actual_date) as days_with_data,
            COUNT(*) - COUNT(actual_date) as missing_days,
            ROUND(COUNT(actual_date)::numeric / COUNT(*) * 100, 1) as coverage_percent
        FROM date_series ds
        LEFT JOIN actual_dates ad ON ds.expected_date = ad.actual_date;
    """, "SYSTEM 1 DATA COVERAGE (Mar 2024 - Today)")
    
    # 8. Data gaps analysis - System 2
    run_sql_query("""
        WITH date_series AS (
            SELECT generate_series(
                '2024-03-01'::date,
                CURRENT_DATE,
                '1 day'::interval
            )::date as expected_date
        ),
        actual_dates AS (
            SELECT DISTINCT DATE(timestamp) as actual_date
            FROM solax_data2
            WHERE yield_today >= 0 AND yield_today < 100
        )
        SELECT 
            COUNT(*) as total_expected_days,
            COUNT(actual_date) as days_with_data,
            COUNT(*) - COUNT(actual_date) as missing_days,
            ROUND(COUNT(actual_date)::numeric / COUNT(*) * 100, 1) as coverage_percent
        FROM date_series ds
        LEFT JOIN actual_dates ad ON ds.expected_date = ad.actual_date;
    """, "SYSTEM 2 DATA COVERAGE (Mar 2024 - Today)")
    
    # 9. Weather data coverage
    run_sql_query("""
        WITH date_series AS (
            SELECT generate_series(
                '2024-03-01'::date,
                CURRENT_DATE,
                '1 day'::interval
            )::date as expected_date
        ),
        actual_dates AS (
            SELECT DISTINCT DATE(timestamp) as actual_date
            FROM weather_data
            WHERE timestamp IS NOT NULL
        )
        SELECT 
            COUNT(*) as total_expected_days,
            COUNT(actual_date) as days_with_data,
            COUNT(*) - COUNT(actual_date) as missing_days,
            ROUND(COUNT(actual_date)::numeric / COUNT(*) * 100, 1) as coverage_percent
        FROM date_series ds
        LEFT JOIN actual_dates ad ON ds.expected_date = ad.actual_date;
    """, "WEATHER DATA COVERAGE (Mar 2024 - Today)")
    
    # 10. Specific period checks
    run_sql_query("""
        -- April 2024 data availability
        SELECT 
            'April 2024' as period,
            (SELECT COUNT(*) FROM solax_data 
             WHERE EXTRACT(year FROM timestamp) = 2024 AND EXTRACT(month FROM timestamp) = 4 
             AND yield_today >= 0) as system1_records,
            (SELECT COUNT(*) FROM solax_data2 
             WHERE EXTRACT(year FROM timestamp) = 2024 AND EXTRACT(month FROM timestamp) = 4 
             AND yield_today >= 0) as system2_records,
            (SELECT COUNT(*) FROM weather_data 
             WHERE EXTRACT(year FROM timestamp) = 2024 AND EXTRACT(month FROM timestamp) = 4) as weather_records
        
        UNION ALL
        
        -- April 2025 data availability
        SELECT 
            'April 2025' as period,
            (SELECT COUNT(*) FROM solax_data 
             WHERE EXTRACT(year FROM timestamp) = 2025 AND EXTRACT(month FROM timestamp) = 4 
             AND yield_today >= 0) as system1_records,
            (SELECT COUNT(*) FROM solax_data2 
             WHERE EXTRACT(year FROM timestamp) = 2025 AND EXTRACT(month FROM timestamp) = 4 
             AND yield_today >= 0) as system2_records,
            (SELECT COUNT(*) FROM weather_data 
             WHERE EXTRACT(year FROM timestamp) = 2025 AND EXTRACT(month FROM timestamp) = 4) as weather_records
        
        UNION ALL
        
        -- Current month data availability
        SELECT 
            'Current Month' as period,
            (SELECT COUNT(*) FROM solax_data 
             WHERE EXTRACT(year FROM timestamp) = EXTRACT(year FROM CURRENT_DATE) 
             AND EXTRACT(month FROM timestamp) = EXTRACT(month FROM CURRENT_DATE)
             AND yield_today >= 0) as system1_records,
            (SELECT COUNT(*) FROM solax_data2 
             WHERE EXTRACT(year FROM timestamp) = EXTRACT(year FROM CURRENT_DATE) 
             AND EXTRACT(month FROM timestamp) = EXTRACT(month FROM CURRENT_DATE)
             AND yield_today >= 0) as system2_records,
            (SELECT COUNT(*) FROM weather_data 
             WHERE EXTRACT(year FROM timestamp) = EXTRACT(year FROM CURRENT_DATE) 
             AND EXTRACT(month FROM timestamp) = EXTRACT(month FROM CURRENT_DATE)) as weather_records;
    """, "SPECIFIC PERIODS DATA AVAILABILITY")
    
    # 11. Data quality check
    run_sql_query("""
        SELECT 
            'System 1' as system,
            COUNT(*) as total_records,
            COUNT(*) FILTER (WHERE yield_today = 0) as zero_yield_records,
            COUNT(*) FILTER (WHERE yield_today < 0) as negative_yield_records,
            COUNT(*) FILTER (WHERE yield_today > 100) as excessive_yield_records,
            COUNT(*) FILTER (WHERE yield_today BETWEEN 0.1 AND 100) as valid_records
        FROM solax_data
        
        UNION ALL
        
        SELECT 
            'System 2' as system,
            COUNT(*) as total_records,
            COUNT(*) FILTER (WHERE yield_today = 0) as zero_yield_records,
            COUNT(*) FILTER (WHERE yield_today < 0) as negative_yield_records,
            COUNT(*) FILTER (WHERE yield_today > 100) as excessive_yield_records,
            COUNT(*) FILTER (WHERE yield_today BETWEEN 0.1 AND 100) as valid_records
        FROM solax_data2;
    """, "DATA QUALITY ANALYSIS")
    
    print(f"\n🎉 DATA AVAILABILITY ANALYSIS COMPLETED!")
    print("=" * 45)

def main():
    """Main analysis function"""
    analyze_data_availability()

if __name__ == "__main__":
    main()
