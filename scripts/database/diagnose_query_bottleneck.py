#!/usr/bin/env python3
"""
Diagnose Query Bottleneck
Find why queries are hanging and provide solutions
"""

import psycopg2
from psycopg2.extras import RealDictCursor
import time
from datetime import datetime

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 5433,
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

def get_db_connection():
    """Get database connection"""
    try:
        return psycopg2.connect(**DB_CONFIG)
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return None

def check_active_queries():
    """Check for hanging/long-running queries"""
    print("\n" + "="*60)
    print("🔍 ACTIVE QUERIES ANALYSIS")
    print("="*60)
    
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        
        # Check all active queries
        cur.execute("""
            SELECT 
                pid,
                now() - query_start as duration,
                state,
                query,
                wait_event_type,
                wait_event
            FROM pg_stat_activity 
            WHERE state != 'idle'
            AND pid != pg_backend_pid()
            ORDER BY query_start
        """)
        
        active_queries = cur.fetchall()
        
        print(f"   Active queries: {len(active_queries)}")
        
        for query in active_queries:
            print(f"\n   PID {query['pid']}:")
            print(f"      Duration: {query['duration']}")
            print(f"      State: {query['state']}")
            print(f"      Wait Event: {query['wait_event_type']}/{query['wait_event']}")
            print(f"      Query: {query['query'][:100]}...")
            
            # Check if it's our UPDATE query
            if 'UPDATE' in query['query'] and 'billing_cost' in query['query']:
                print(f"      🚨 FOUND HANGING UPDATE QUERY!")
                return query['pid']
        
        conn.close()
        return None
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def check_locks():
    """Check for blocking locks"""
    print("\n" + "="*60)
    print("🔒 LOCK ANALYSIS")
    print("="*60)
    
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        
        # Check for blocking locks
        cur.execute("""
            SELECT 
                blocked_locks.pid AS blocked_pid,
                blocked_activity.usename AS blocked_user,
                blocking_locks.pid AS blocking_pid,
                blocking_activity.usename AS blocking_user,
                blocked_activity.query AS blocked_statement,
                blocking_activity.query AS current_statement_in_blocking_process,
                blocked_activity.application_name AS blocked_application,
                blocking_activity.application_name AS blocking_application
            FROM pg_catalog.pg_locks blocked_locks
            JOIN pg_catalog.pg_stat_activity blocked_activity 
                ON blocked_activity.pid = blocked_locks.pid
            JOIN pg_catalog.pg_locks blocking_locks 
                ON blocking_locks.locktype = blocked_locks.locktype
                AND blocking_locks.database IS NOT DISTINCT FROM blocked_locks.database
                AND blocking_locks.relation IS NOT DISTINCT FROM blocked_locks.relation
                AND blocking_locks.page IS NOT DISTINCT FROM blocked_locks.page
                AND blocking_locks.tuple IS NOT DISTINCT FROM blocked_locks.tuple
                AND blocking_locks.virtualxid IS NOT DISTINCT FROM blocked_locks.virtualxid
                AND blocking_locks.transactionid IS NOT DISTINCT FROM blocked_locks.transactionid
                AND blocking_locks.classid IS NOT DISTINCT FROM blocked_locks.classid
                AND blocking_locks.objid IS NOT DISTINCT FROM blocked_locks.objid
                AND blocking_locks.objsubid IS NOT DISTINCT FROM blocked_locks.objsubid
                AND blocking_locks.pid != blocked_locks.pid
            JOIN pg_catalog.pg_stat_activity blocking_activity 
                ON blocking_activity.pid = blocking_locks.pid
            WHERE NOT blocked_locks.granted
        """)
        
        blocking_locks = cur.fetchall()
        
        if blocking_locks:
            print(f"   🚨 FOUND {len(blocking_locks)} BLOCKING LOCKS!")
            for lock in blocking_locks:
                print(f"\n   Blocked PID {lock['blocked_pid']} by PID {lock['blocking_pid']}")
                print(f"      Blocked query: {lock['blocked_statement'][:100]}...")
                print(f"      Blocking query: {lock['current_statement_in_blocking_process'][:100]}...")
        else:
            print("   ✅ No blocking locks found")
        
        # Check lock counts by type
        cur.execute("""
            SELECT 
                locktype,
                mode,
                granted,
                COUNT(*) as count
            FROM pg_locks 
            GROUP BY locktype, mode, granted
            ORDER BY count DESC
        """)
        
        lock_stats = cur.fetchall()
        
        print(f"\n   Lock statistics:")
        for stat in lock_stats[:10]:
            status = "✅ GRANTED" if stat['granted'] else "❌ WAITING"
            print(f"      {stat['locktype']}/{stat['mode']}: {stat['count']} ({status})")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

def check_table_stats():
    """Check table statistics and indexes"""
    print("\n" + "="*60)
    print("📊 TABLE STATISTICS")
    print("="*60)
    
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        
        # Check table sizes and stats
        for table in ['solax_data', 'solax_data2']:
            print(f"\n   📋 {table}:")
            
            # Table size
            cur.execute(f"""
                SELECT 
                    pg_size_pretty(pg_total_relation_size('{table}')) as total_size,
                    pg_size_pretty(pg_relation_size('{table}')) as table_size,
                    pg_size_pretty(pg_total_relation_size('{table}') - pg_relation_size('{table}')) as index_size
            """)
            
            size_info = cur.fetchone()
            print(f"      Total size: {size_info['total_size']}")
            print(f"      Table size: {size_info['table_size']}")
            print(f"      Index size: {size_info['index_size']}")
            
            # Row count
            cur.execute(f"SELECT COUNT(*) as row_count FROM {table}")
            row_count = cur.fetchone()['row_count']
            print(f"      Row count: {row_count:,}")
            
            # Check indexes
            cur.execute(f"""
                SELECT 
                    indexname,
                    indexdef
                FROM pg_indexes 
                WHERE tablename = '{table}'
                ORDER BY indexname
            """)
            
            indexes = cur.fetchall()
            print(f"      Indexes: {len(indexes)}")
            for idx in indexes:
                print(f"         {idx['indexname']}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

def check_postgres_config():
    """Check PostgreSQL configuration"""
    print("\n" + "="*60)
    print("⚙️ POSTGRESQL CONFIGURATION")
    print("="*60)
    
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        
        # Key configuration parameters
        config_params = [
            'max_connections',
            'shared_buffers',
            'work_mem',
            'maintenance_work_mem',
            'effective_cache_size',
            'random_page_cost',
            'checkpoint_completion_target',
            'wal_buffers',
            'default_statistics_target',
            'lock_timeout',
            'statement_timeout'
        ]
        
        for param in config_params:
            cur.execute("SELECT name, setting, unit FROM pg_settings WHERE name = %s", (param,))
            result = cur.fetchone()
            if result:
                unit = result['unit'] or ''
                print(f"   {result['name']}: {result['setting']}{unit}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

def kill_hanging_queries():
    """Kill hanging queries if found"""
    print("\n" + "="*60)
    print("💀 KILL HANGING QUERIES")
    print("="*60)
    
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        
        # Find long-running UPDATE queries
        cur.execute("""
            SELECT 
                pid,
                now() - query_start as duration,
                query
            FROM pg_stat_activity 
            WHERE state = 'active'
            AND query ILIKE '%UPDATE%billing_cost%'
            AND now() - query_start > interval '5 minutes'
        """)
        
        hanging_queries = cur.fetchall()
        
        if hanging_queries:
            print(f"   Found {len(hanging_queries)} hanging UPDATE queries")
            
            for query in hanging_queries:
                print(f"\n   Killing PID {query['pid']} (running for {query['duration']})")
                try:
                    cur.execute("SELECT pg_terminate_backend(%s)", (query['pid'],))
                    result = cur.fetchone()[0]
                    if result:
                        print(f"      ✅ Successfully killed PID {query['pid']}")
                    else:
                        print(f"      ❌ Failed to kill PID {query['pid']}")
                except Exception as e:
                    print(f"      ❌ Error killing PID {query['pid']}: {e}")
        else:
            print("   ✅ No hanging queries found")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

def suggest_optimizations():
    """Suggest optimizations"""
    print("\n" + "="*60)
    print("💡 OPTIMIZATION SUGGESTIONS")
    print("="*60)
    
    print("   🔧 IMMEDIATE FIXES:")
    print("      1. Kill hanging queries: pg_terminate_backend(pid)")
    print("      2. Add indexes on billing_cost column")
    print("      3. Use smaller batch sizes (1000 records)")
    print("      4. Add WHERE clauses with timestamp limits")
    print("")
    print("   ⚙️ POSTGRESQL TUNING:")
    print("      1. Increase work_mem (current: check above)")
    print("      2. Increase maintenance_work_mem for large updates")
    print("      3. Set statement_timeout to prevent hanging")
    print("      4. Optimize checkpoint settings")
    print("")
    print("   📊 QUERY OPTIMIZATION:")
    print("      1. CREATE INDEX ON solax_data(billing_cost) WHERE billing_cost > 10;")
    print("      2. UPDATE in smaller batches with LIMIT")
    print("      3. Use VACUUM ANALYZE after large updates")
    print("      4. Consider using COPY for bulk operations")
    print("")
    print("   🚀 ALTERNATIVE APPROACH:")
    print("      1. Create new table with corrected data")
    print("      2. Rename tables atomically")
    print("      3. Drop old table")

def main():
    """Main diagnostic function"""
    print("🔍 QUERY BOTTLENECK DIAGNOSIS")
    print("=" * 60)
    print(f"Time: {datetime.now()}")
    
    # Run diagnostics
    hanging_pid = check_active_queries()
    check_locks()
    check_table_stats()
    check_postgres_config()
    
    # Kill hanging queries if found
    if hanging_pid:
        print(f"\n🚨 Found hanging query PID: {hanging_pid}")
        kill_hanging_queries()
    
    suggest_optimizations()
    
    print("\n" + "="*60)
    print("🎯 DIAGNOSIS COMPLETED")
    print("="*60)
    print("Check the suggestions above to resolve the bottleneck!")

if __name__ == "__main__":
    main()
