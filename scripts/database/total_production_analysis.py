#!/usr/bin/env python3
"""
TOTAL PRODUCTION & SAVINGS ANALYSIS
Αναλύει τη συνολική παραγωγή και εξοικονόμηση ανά σύστημα
"""

import psycopg2
from datetime import datetime, date, timedelta
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 5433,
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

def analyze_total_production():
    """Αναλύει τη συνολική παραγωγή και εξοικονόμηση"""
    
    print("📊 TOTAL PRODUCTION & SAVINGS ANALYSIS")
    print("=" * 80)
    
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()
        
        print("✅ Connected to database")
        
        # 1. ΣΥΝΟΛΙΚΗ ΠΑΡΑΓΩΓΗ ΕΝΕΡΓΕΙΑΣ
        print("\n⚡ TOTAL ENERGY PRODUCTION")
        print("-" * 50)
        
        total_production_all = 0
        total_savings_all = 0
        
        for table, system_name in [('solax_data', 'System 1'), ('solax_data2', 'System 2')]:
            print(f"\n🏠 {system_name}:")
            
            # Συνολική παραγωγή με τη σωστή μέθοδο
            cur.execute(f"""
                WITH daily_production AS (
                    SELECT 
                        DATE(timestamp) as date,
                        MAX(yield_today) - MIN(yield_today) as daily_production
                    FROM {table}
                    WHERE timestamp >= '2024-01-01'
                    AND yield_today >= 0
                    GROUP BY DATE(timestamp)
                    HAVING MAX(yield_today) - MIN(yield_today) > 0
                )
                SELECT 
                    COUNT(*) as total_days,
                    SUM(daily_production) as total_production,
                    AVG(daily_production) as avg_daily_production,
                    MIN(daily_production) as min_daily_production,
                    MAX(daily_production) as max_daily_production,
                    MIN(date) as start_date,
                    MAX(date) as end_date
                FROM daily_production
            """)
            
            result = cur.fetchone()
            if result:
                total_days, total_production, avg_daily, min_daily, max_daily, start_date, end_date = result
                
                print(f"   📅 Period: {start_date} to {end_date}")
                print(f"   📊 Total days: {total_days}")
                print(f"   ⚡ Total production: {total_production:.2f} kWh")
                print(f"   📈 Average daily: {avg_daily:.2f} kWh/day")
                print(f"   📉 Min daily: {min_daily:.2f} kWh")
                print(f"   📈 Max daily: {max_daily:.2f} kWh")
                
                # Υπολογισμός ετήσιας παραγωγής
                if start_date and end_date:
                    operational_days = (end_date - start_date).days
                    operational_years = operational_days / 365.25
                    annual_production = total_production / operational_years if operational_years > 0 else 0
                    print(f"   🗓️  Operational years: {operational_years:.2f}")
                    print(f"   📊 Annual production: {annual_production:.2f} kWh/year")
                
                total_production_all += total_production
        
        print(f"\n🎯 COMBINED TOTAL PRODUCTION: {total_production_all:.2f} kWh")
        
        # 2. ΣΥΝΟΛΙΚΗ ΕΞΟΙΚΟΝΟΜΗΣΗ
        print("\n💰 TOTAL SAVINGS ANALYSIS")
        print("-" * 50)
        
        for table, system_name in [('solax_data', 'System 1'), ('solax_data2', 'System 2')]:
            print(f"\n🏠 {system_name}:")
            
            # Συνολική εξοικονόμηση από billing fields
            cur.execute(f"""
                SELECT 
                    SUM(COALESCE(billing_benefit, 0)) as total_savings,
                    COUNT(CASE WHEN billing_benefit > 0 THEN 1 END) as records_with_savings,
                    AVG(CASE WHEN billing_benefit > 0 THEN billing_benefit END) as avg_savings_per_record,
                    MAX(billing_benefit) as max_savings_record,
                    COUNT(DISTINCT DATE(timestamp)) as days_with_data,
                    MIN(timestamp) as start_date,
                    MAX(timestamp) as end_date
                FROM {table}
                WHERE timestamp >= '2024-01-01'
                AND billing_benefit IS NOT NULL
            """)
            
            result = cur.fetchone()
            if result:
                total_savings, records_with_savings, avg_savings_per_record, max_savings_record, days_with_data, start_date, end_date = result
                
                print(f"   💰 Total savings: €{total_savings:.2f}")
                print(f"   📊 Records with savings: {records_with_savings:,}")
                print(f"   📈 Avg savings per record: €{avg_savings_per_record:.6f}")
                print(f"   📈 Max savings record: €{max_savings_record:.6f}")
                print(f"   📅 Days with data: {days_with_data}")
                
                # Υπολογισμός ετήσιας εξοικονόμησης
                if start_date and end_date:
                    operational_days = (end_date - start_date).days
                    operational_years = operational_days / 365.25
                    annual_savings = total_savings / operational_years if operational_years > 0 else 0
                    daily_savings = total_savings / days_with_data if days_with_data > 0 else 0
                    
                    print(f"   📊 Annual savings: €{annual_savings:.2f}/year")
                    print(f"   📊 Daily savings: €{daily_savings:.2f}/day")
                
                total_savings_all += total_savings
        
        print(f"\n🎯 COMBINED TOTAL SAVINGS: €{total_savings_all:.2f}")
        
        # 3. ΚΟΣΤΟΣ ΑΝΑ kWh ΑΝΑΛΥΣΗ
        print("\n💡 COST PER kWh ANALYSIS")
        print("-" * 50)
        
        if total_production_all > 0 and total_savings_all > 0:
            effective_rate_per_kwh = total_savings_all / total_production_all
            print(f"\n🎯 EFFECTIVE RATE CALCULATION:")
            print(f"   Total Savings: €{total_savings_all:.2f}")
            print(f"   Total Production: {total_production_all:.2f} kWh")
            print(f"   Effective Rate: €{effective_rate_per_kwh:.4f}/kWh")
            
            # Σύγκριση με τιμές αγοράς ρεύματος
            grid_rate_day = 0.142  # €/kWh
            grid_rate_night = 0.120  # €/kWh
            grid_rate_avg = (grid_rate_day + grid_rate_night) / 2
            
            print(f"\n📊 COMPARISON WITH GRID RATES:")
            print(f"   Grid day rate: €{grid_rate_day:.4f}/kWh")
            print(f"   Grid night rate: €{grid_rate_night:.4f}/kWh")
            print(f"   Grid average rate: €{grid_rate_avg:.4f}/kWh")
            print(f"   Solar effective rate: €{effective_rate_per_kwh:.4f}/kWh")
            
            savings_vs_day = ((grid_rate_day - effective_rate_per_kwh) / grid_rate_day * 100)
            savings_vs_avg = ((grid_rate_avg - effective_rate_per_kwh) / grid_rate_avg * 100)
            
            print(f"\n💰 SAVINGS PERCENTAGE:")
            print(f"   vs Day rate: {savings_vs_day:.1f}% savings")
            print(f"   vs Average rate: {savings_vs_avg:.1f}% savings")
        
        # 4. ΑΝΑΛΥΣΗ ΑΝΑ ΣΥΣΤΗΜΑ
        print("\n🔍 PER-SYSTEM DETAILED ANALYSIS")
        print("-" * 50)
        
        system_data = {}
        
        for table, system_name in [('solax_data', 'System 1'), ('solax_data2', 'System 2')]:
            print(f"\n🏠 {system_name} DETAILED:")
            
            # Παραγωγή
            cur.execute(f"""
                WITH daily_production AS (
                    SELECT 
                        DATE(timestamp) as date,
                        MAX(yield_today) - MIN(yield_today) as daily_production
                    FROM {table}
                    WHERE timestamp >= '2024-01-01'
                    AND yield_today >= 0
                    GROUP BY DATE(timestamp)
                    HAVING MAX(yield_today) - MIN(yield_today) > 0
                )
                SELECT SUM(daily_production) as total_production
                FROM daily_production
            """)
            
            result = cur.fetchone()
            system_production = float(result[0] or 0) if result else 0
            
            # Εξοικονόμηση
            cur.execute(f"""
                SELECT SUM(COALESCE(billing_benefit, 0)) as total_savings
                FROM {table}
                WHERE timestamp >= '2024-01-01'
                AND billing_benefit IS NOT NULL
            """)
            
            result = cur.fetchone()
            system_savings = float(result[0] or 0) if result else 0
            
            # Υπολογισμοί
            system_effective_rate = system_savings / system_production if system_production > 0 else 0
            
            print(f"   ⚡ Production: {system_production:.2f} kWh")
            print(f"   💰 Savings: €{system_savings:.2f}")
            print(f"   💡 Effective rate: €{system_effective_rate:.4f}/kWh")
            
            # Self-consumption analysis
            expected_self_consumption_rate = 0.281 if table == 'solax_data' else 0.292
            expected_self_consumption = system_production * expected_self_consumption_rate
            expected_savings = expected_self_consumption * 0.1659
            
            print(f"   📊 Expected self-consumption: {expected_self_consumption:.2f} kWh ({expected_self_consumption_rate*100:.1f}%)")
            print(f"   📊 Expected savings: €{expected_savings:.2f}")
            
            accuracy = (system_savings / expected_savings * 100) if expected_savings > 0 else 0
            print(f"   🎯 Billing accuracy: {accuracy:.1f}%")
            
            system_data[system_name] = {
                'production': system_production,
                'savings': system_savings,
                'effective_rate': system_effective_rate
            }
        
        # 5. ΣΥΓΚΡΙΣΗ ΣΥΣΤΗΜΑΤΩΝ
        print("\n⚖️  SYSTEM COMPARISON")
        print("-" * 50)
        
        if len(system_data) == 2:
            system1_data = system_data['System 1']
            system2_data = system_data['System 2']
            
            production_ratio = system1_data['production'] / system2_data['production'] if system2_data['production'] > 0 else 0
            savings_ratio = system1_data['savings'] / system2_data['savings'] if system2_data['savings'] > 0 else 0
            
            print(f"   Production ratio (S1/S2): {production_ratio:.2f}")
            print(f"   Savings ratio (S1/S2): {savings_ratio:.2f}")
            print(f"   System 1 effective rate: €{system1_data['effective_rate']:.4f}/kWh")
            print(f"   System 2 effective rate: €{system2_data['effective_rate']:.4f}/kWh")
            
            if abs(production_ratio - savings_ratio) > 0.1:
                print(f"   ⚠️  Ratio mismatch detected!")
            else:
                print(f"   ✅ Ratios are consistent")
        
        conn.close()
        
        print("\n🎯 ANALYSIS SUMMARY")
        print("-" * 50)
        print(f"✅ Total production analyzed: {total_production_all:.2f} kWh")
        print(f"✅ Total savings analyzed: €{total_savings_all:.2f}")
        if total_production_all > 0:
            print(f"✅ Overall effective rate: €{total_savings_all/total_production_all:.4f}/kWh")
        print("✅ Per-system analysis completed")
        print("✅ System comparison completed")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    analyze_total_production()
