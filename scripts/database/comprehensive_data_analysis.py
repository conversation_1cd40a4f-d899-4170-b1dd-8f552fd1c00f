#!/usr/bin/env python3
"""
COMPREHENSIVE DATA ANALYSIS
Αναλύει τα δεδομένα για να βρει τα προβλήματα στα billing fields
"""

import psycopg2
from datetime import datetime, date, timedelta
import pandas as pd
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 5433,
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

def analyze_data_quality():
    """Αναλύει την ποιότητα των δεδομένων"""
    
    print("🔍 COMPREHENSIVE DATA ANALYSIS")
    print("=" * 80)
    
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()
        
        # 1. ΑΝΑΛΥΣΗ ΠΟΛΛΩΝ ΗΜΕΡΩΝ
        print("\n📅 MULTI-DAY ANALYSIS")
        print("-" * 40)
        
        # Επιλογή τυχαίων ημερών με πλήρη δεδομένα
        test_dates = [
            '2025-06-20',  # Πρόσφατη ημέρα
            '2025-06-15',  # Μέση ημέρα
            '2025-06-10',  # Παλαιότερη ημέρα
            '2025-05-25',  # Διαφορετικός μήνας
            '2025-04-15'   # Ακόμα παλαιότερη
        ]
        
        for test_date in test_dates:
            print(f"\n📊 ANALYZING DATE: {test_date}")
            print("-" * 30)
            
            for table, system_name in [('solax_data', 'System 1'), ('solax_data2', 'System 2')]:
                print(f"\n🏠 {system_name}:")
                
                # Check data availability
                cur.execute(f"""
                    SELECT 
                        COUNT(*) as total_records,
                        MIN(timestamp) as first_record,
                        MAX(timestamp) as last_record,
                        MIN(yield_today) as min_yield,
                        MAX(yield_today) as max_yield,
                        COUNT(CASE WHEN yield_today > 0 THEN 1 END) as records_with_yield
                    FROM {table}
                    WHERE DATE(timestamp) = %s
                """, (test_date,))
                
                result = cur.fetchone()
                if result:
                    total_records, first_record, last_record, min_yield, max_yield, records_with_yield = result
                    print(f"   Records: {total_records}, With yield: {records_with_yield}")
                    print(f"   Yield range: {min_yield:.2f} - {max_yield:.2f} kWh")
                    
                    if total_records > 0:
                        # ΣΩΣΤΟΣ ΥΠΟΛΟΓΙΣΜΟΣ ΜΕ RESET LOGIC
                        cur.execute(f"""
                            WITH daily_data AS (
                                SELECT 
                                    timestamp,
                                    yield_today,
                                    LAG(yield_today) OVER (ORDER BY timestamp) as prev_yield
                                FROM {table}
                                WHERE DATE(timestamp) = %s
                                ORDER BY timestamp
                            ),
                            reset_points AS (
                                SELECT 
                                    timestamp,
                                    yield_today,
                                    prev_yield,
                                    CASE 
                                        WHEN yield_today < prev_yield THEN 1 
                                        ELSE 0 
                                    END as is_reset
                                FROM daily_data
                            ),
                            with_reset_groups AS (
                                SELECT 
                                    timestamp,
                                    yield_today,
                                    SUM(is_reset) OVER (ORDER BY timestamp) as reset_group
                                FROM reset_points
                            ),
                            daily_production AS (
                                SELECT 
                                    reset_group,
                                    MIN(yield_today) as group_min,
                                    MAX(yield_today) as group_max,
                                    MAX(yield_today) - MIN(yield_today) as group_production
                                FROM with_reset_groups
                                GROUP BY reset_group
                                HAVING MAX(yield_today) - MIN(yield_today) > 0
                            )
                            SELECT 
                                COUNT(*) as reset_groups,
                                COALESCE(SUM(group_production), 0) as total_daily_production,
                                ARRAY_AGG(group_production ORDER BY reset_group) as group_productions
                            FROM daily_production
                        """, (test_date,))
                        
                        result = cur.fetchone()
                        if result:
                            reset_groups, daily_production, group_productions = result
                            print(f"   Reset groups: {reset_groups}")
                            print(f"   Daily production: {daily_production:.2f} kWh")
                            if group_productions:
                                print(f"   Group productions: {[f'{p:.2f}' for p in group_productions]}")
                        
                        # BILLING FIELDS ANALYSIS
                        cur.execute(f"""
                            SELECT 
                                SUM(COALESCE(billing_benefit, 0)) as total_benefit,
                                COUNT(CASE WHEN billing_benefit > 0 THEN 1 END) as records_with_benefit,
                                AVG(CASE WHEN billing_benefit > 0 THEN billing_benefit END) as avg_benefit,
                                MAX(billing_benefit) as max_benefit
                            FROM {table}
                            WHERE DATE(timestamp) = %s
                        """, (test_date,))
                        
                        result = cur.fetchone()
                        if result:
                            total_benefit, records_with_benefit, avg_benefit, max_benefit = result
                            print(f"   Billing benefit: €{total_benefit:.2f}")
                            print(f"   Records with benefit: {records_with_benefit}")
                            if avg_benefit:
                                print(f"   Avg benefit per record: €{avg_benefit:.6f}")
                            if max_benefit:
                                print(f"   Max benefit: €{max_benefit:.6f}")
                            
                            # MANUAL CALCULATION
                            if daily_production and daily_production > 0:
                                # Σωστά rates
                                self_consumption_rate = 0.281 if table == 'solax_data' else 0.292
                                total_rate = 0.1659  # €0.142 + €0.0069 + €0.017

                                expected_self_consumption = float(daily_production) * self_consumption_rate
                                expected_benefit = expected_self_consumption * total_rate
                                
                                print(f"   Expected self-consumption: {expected_self_consumption:.2f} kWh")
                                print(f"   Expected benefit: €{expected_benefit:.2f}")
                                
                                if total_benefit > 0:
                                    accuracy = (float(total_benefit) / expected_benefit * 100)
                                    print(f"   Billing accuracy: {accuracy:.1f}%")
                                    
                                    if abs(accuracy - 100) > 10:  # More than 10% difference
                                        print(f"   ⚠️  SIGNIFICANT DEVIATION!")
                else:
                    print(f"   ❌ No data for {test_date}")
        
        # 2. RESET PATTERN ANALYSIS
        print(f"\n🔄 RESET PATTERN ANALYSIS")
        print("-" * 40)
        
        for table, system_name in [('solax_data', 'System 1'), ('solax_data2', 'System 2')]:
            print(f"\n🏠 {system_name} Reset Patterns:")
            
            # Find reset patterns in recent days
            cur.execute(f"""
                WITH daily_resets AS (
                    SELECT 
                        DATE(timestamp) as date,
                        COUNT(CASE WHEN yield_today < LAG(yield_today) OVER (ORDER BY timestamp) THEN 1 END) as reset_count,
                        MIN(CASE WHEN yield_today < LAG(yield_today) OVER (ORDER BY timestamp) THEN timestamp END) as first_reset,
                        MAX(yield_today) as max_yield
                    FROM {table}
                    WHERE timestamp >= CURRENT_DATE - INTERVAL '7 days'
                    GROUP BY DATE(timestamp)
                    ORDER BY date DESC
                )
                SELECT 
                    date,
                    reset_count,
                    first_reset,
                    max_yield
                FROM daily_resets
                WHERE max_yield > 5  -- Filter out very low production days
                LIMIT 5
            """)
            
            results = cur.fetchall()
            for result in results:
                date_val, reset_count, first_reset, max_yield = result
                print(f"   {date_val}: {reset_count} resets, max yield: {max_yield:.2f} kWh")
                if first_reset:
                    print(f"      First reset at: {first_reset}")
        
        # 3. HOURLY PATTERN ANALYSIS
        print(f"\n⏰ HOURLY PATTERN ANALYSIS")
        print("-" * 40)
        
        # Analyze a specific day with good data
        analysis_date = '2025-06-20'
        
        for table, system_name in [('solax_data', 'System 1'), ('solax_data2', 'System 2')]:
            print(f"\n🏠 {system_name} Hourly Analysis ({analysis_date}):")
            
            cur.execute(f"""
                WITH hourly_data AS (
                    SELECT 
                        EXTRACT(HOUR FROM timestamp) as hour,
                        MIN(yield_today) as hour_min,
                        MAX(yield_today) as hour_max,
                        MAX(yield_today) - MIN(yield_today) as hour_production,
                        COUNT(*) as records_count,
                        SUM(COALESCE(billing_benefit, 0)) as hour_benefit
                    FROM {table}
                    WHERE DATE(timestamp) = %s
                    GROUP BY EXTRACT(HOUR FROM timestamp)
                    ORDER BY hour
                )
                SELECT 
                    hour,
                    hour_min,
                    hour_max,
                    hour_production,
                    records_count,
                    hour_benefit
                FROM hourly_data
                WHERE hour_production > 0.1  -- Only hours with significant production
            """, (analysis_date,))
            
            results = cur.fetchall()
            total_hourly_production = 0
            total_hourly_benefit = 0
            
            for result in results:
                hour, hour_min, hour_max, hour_production, records_count, hour_benefit = result
                total_hourly_production += hour_production
                total_hourly_benefit += hour_benefit
                print(f"   {hour:02d}:00 - Production: {hour_production:.2f} kWh, Benefit: €{hour_benefit:.4f}")
            
            print(f"   Total hourly production: {total_hourly_production:.2f} kWh")
            print(f"   Total hourly benefit: €{total_hourly_benefit:.2f}")
        
        # 4. BILLING FIELDS VALIDATION
        print(f"\n💰 BILLING FIELDS VALIDATION")
        print("-" * 40)
        
        validation_date = '2025-06-20'
        
        for table, system_name in [('solax_data', 'System 1'), ('solax_data2', 'System 2')]:
            print(f"\n🏠 {system_name} Billing Validation ({validation_date}):")
            
            # Get actual billing fields
            cur.execute(f"""
                SELECT 
                    SUM(COALESCE(billing_benefit, 0)) as total_benefit,
                    COUNT(CASE WHEN billing_benefit > 0 THEN 1 END) as benefit_records,
                    AVG(COALESCE(billing_tariff, 0)) as avg_tariff,
                    AVG(COALESCE(billing_network_charge, 0)) as avg_network,
                    AVG(COALESCE(billing_etmear, 0)) as avg_etmear
                FROM {table}
                WHERE DATE(timestamp) = %s
            """, (validation_date,))
            
            result = cur.fetchone()
            if result:
                total_benefit, benefit_records, avg_tariff, avg_network, avg_etmear = result
                print(f"   Billing benefit: €{total_benefit:.2f}")
                print(f"   Records with benefit: {benefit_records}")
                print(f"   Avg rates - Energy: €{avg_tariff:.4f}, Network: €{avg_network:.4f}, ETMEAR: €{avg_etmear:.4f}")
                
                # Calculate expected rates
                expected_total_rate = 0.142 + 0.0069 + 0.017  # €0.1659
                actual_total_rate = (avg_tariff or 0) + (avg_network or 0) + (avg_etmear or 0)
                
                print(f"   Expected total rate: €{expected_total_rate:.4f}/kWh")
                print(f"   Actual total rate: €{actual_total_rate:.4f}/kWh")
                
                if abs(actual_total_rate - expected_total_rate) > 0.001:
                    print(f"   ⚠️  RATE MISMATCH!")
        
        conn.close()
        
        print("\n🎯 ANALYSIS SUMMARY")
        print("-" * 40)
        print("✅ Multi-day analysis completed")
        print("✅ Reset pattern analysis completed") 
        print("✅ Hourly pattern analysis completed")
        print("✅ Billing fields validation completed")
        print("\n📋 Next steps:")
        print("1. Review any significant deviations found")
        print("2. Fix billing calculation logic if needed")
        print("3. Implement automated validation system")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    analyze_data_quality()
