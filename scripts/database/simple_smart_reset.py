#!/usr/bin/env python3
"""
SIMPLE SMART RESET LOGIC
Απλή και αποτελεσματική διόρθωση της reset logic
"""

import psycopg2
from datetime import datetime, date, timedelta
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 5433,
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

def simple_smart_reset():
    """Απλή έξυπνη reset logic"""
    
    print("🧠 SIMPLE SMART RESET LOGIC")
    print("=" * 50)
    
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()
        
        print("✅ Connected to database")
        
        # 1. ΔΟΚΙΜΗ ΑΠΛΗΣ ΜΕΘΟΔΟΥ
        print("\n🧪 Testing simple method...")
        
        test_dates = ['2025-06-15', '2025-06-10', '2025-05-25']
        
        for test_date in test_dates:
            print(f"\n📅 {test_date}:")
            
            for table, system_name in [('solax_data', 'System 1'), ('solax_data2', 'System 2')]:
                print(f"\n🏠 {system_name}:")
                
                # ΜΕΘΟΔΟΣ 1: Απλή - μόνο το τελικό yield μείον το αρχικό
                cur.execute(f"""
                    SELECT 
                        MIN(yield_today) as min_yield,
                        MAX(yield_today) as max_yield,
                        MAX(yield_today) - MIN(yield_today) as simple_production
                    FROM {table}
                    WHERE DATE(timestamp) = %s
                    AND yield_today >= 0
                """, (test_date,))
                
                result = cur.fetchone()
                if result:
                    min_yield, max_yield, simple_production = result
                    print(f"   Simple method: {simple_production:.2f} kWh (range: {min_yield:.1f}-{max_yield:.1f})")
                
                # ΜΕΘΟΔΟΣ 2: Έξυπνη - αγνοεί μικρές διακυμάνσεις
                cur.execute(f"""
                    WITH filtered_data AS (
                        SELECT 
                            timestamp,
                            yield_today,
                            LAG(yield_today) OVER (ORDER BY timestamp) as prev_yield
                        FROM {table}
                        WHERE DATE(timestamp) = %s
                        ORDER BY timestamp
                    ),
                    significant_resets AS (
                        SELECT 
                            timestamp,
                            yield_today,
                            prev_yield,
                            CASE 
                                WHEN yield_today < prev_yield AND (prev_yield - yield_today) > 5 
                                THEN 1 
                                ELSE 0 
                            END as is_significant_reset
                        FROM filtered_data
                    ),
                    reset_groups AS (
                        SELECT 
                            timestamp,
                            yield_today,
                            SUM(is_significant_reset) OVER (ORDER BY timestamp) as reset_group
                        FROM significant_resets
                    ),
                    group_production AS (
                        SELECT 
                            reset_group,
                            MIN(yield_today) as group_min,
                            MAX(yield_today) as group_max,
                            MAX(yield_today) - MIN(yield_today) as production
                        FROM reset_groups
                        GROUP BY reset_group
                        HAVING MAX(yield_today) - MIN(yield_today) > 1  -- Ignore very small productions
                    )
                    SELECT 
                        COUNT(*) as groups,
                        COALESCE(SUM(production), 0) as smart_production,
                        ARRAY_AGG(production ORDER BY reset_group) as group_productions
                    FROM group_production
                """, (test_date,))
                
                result = cur.fetchone()
                if result:
                    groups, smart_production, group_productions = result
                    print(f"   Smart method: {smart_production:.2f} kWh ({groups} groups)")
                    if group_productions and len(group_productions) <= 5:  # Show only if reasonable
                        print(f"      Groups: {[f'{p:.1f}' for p in group_productions]}")
                
                # ΜΕΘΟΔΟΣ 3: Υβριδική - χρησιμοποιεί την απλή αν η έξυπνη δίνει πολλές ομάδες
                if result:
                    if groups <= 3:  # Reasonable number of groups
                        final_production = smart_production
                        method_used = "Smart"
                    else:  # Too many groups, use simple
                        final_production = simple_production
                        method_used = "Simple (fallback)"
                    
                    print(f"   Final method: {method_used} = {final_production:.2f} kWh")
                    
                    # Calculate expected billing
                    self_consumption_rate = 0.281 if table == 'solax_data' else 0.292
                    total_rate = 0.1659
                    expected_self_consumption = final_production * self_consumption_rate
                    expected_benefit = expected_self_consumption * total_rate
                    
                    print(f"   Expected benefit: €{expected_benefit:.2f}")
                    
                    # Compare with actual billing
                    cur.execute(f"""
                        SELECT SUM(COALESCE(billing_benefit, 0)) as actual_benefit
                        FROM {table}
                        WHERE DATE(timestamp) = %s
                    """, (test_date,))
                    
                    result = cur.fetchone()
                    if result and result[0]:
                        actual_benefit = float(result[0])
                        accuracy = (actual_benefit / expected_benefit * 100) if expected_benefit > 0 else 0
                        print(f"   Actual benefit: €{actual_benefit:.2f}")
                        print(f"   Accuracy: {accuracy:.1f}%")
                        
                        if abs(accuracy - 100) > 20:
                            print(f"   ⚠️  NEEDS CORRECTION!")
        
        # 2. ΔΗΜΙΟΥΡΓΙΑ HYBRID BILLING FUNCTION
        print("\n💰 Creating hybrid billing function...")
        
        cur.execute("""
            CREATE OR REPLACE FUNCTION calculate_hybrid_billing_fields()
            RETURNS TRIGGER AS $$
            DECLARE
                daily_production NUMERIC := 0;
                self_consumption_kwh NUMERIC := 0;
                self_consumption_rate NUMERIC;
                records_count INTEGER;
                
                -- Rates
                energy_rate NUMERIC := 0.142;
                network_rate NUMERIC := 0.0069;
                etmear_rate NUMERIC := 0.017;
                total_rate NUMERIC;
                
                hour_of_day INTEGER;
                month_of_year INTEGER;
                schedule_name TEXT := 'summer_day';
            BEGIN
                -- Get time components
                hour_of_day := EXTRACT(HOUR FROM NEW.timestamp);
                month_of_year := EXTRACT(MONTH FROM NEW.timestamp);
                
                -- Set self-consumption rate
                IF TG_TABLE_NAME = 'solax_data' THEN
                    self_consumption_rate := 0.281;  -- 28.1% for System 1
                ELSE
                    self_consumption_rate := 0.292;  -- 29.2% for System 2
                END IF;
                
                -- Seasonal tariff calculation
                IF month_of_year IN (11, 12, 1, 2, 3) THEN -- Winter
                    IF hour_of_day IN (2, 3, 4) OR hour_of_day IN (12, 13, 14) THEN
                        energy_rate := 0.120;
                        schedule_name := 'winter_night';
                    ELSE
                        energy_rate := 0.142;
                        schedule_name := 'winter_day';
                    END IF;
                ELSE -- Summer
                    IF hour_of_day IN (2, 3) OR hour_of_day IN (11, 12, 13, 14) THEN
                        energy_rate := 0.132;
                        schedule_name := 'summer_night';
                    ELSE
                        energy_rate := 0.142;
                        schedule_name := 'summer_day';
                    END IF;
                END IF;
                
                total_rate := energy_rate + network_rate + etmear_rate;
                
                -- Calculate daily production using hybrid method
                EXECUTE format('
                    WITH smart_calculation AS (
                        SELECT 
                            COUNT(CASE WHEN yield_today < LAG(yield_today) OVER (ORDER BY timestamp) 
                                       AND (LAG(yield_today) OVER (ORDER BY timestamp) - yield_today) > 5 
                                       THEN 1 END) as significant_resets,
                            MAX(yield_today) - MIN(yield_today) as simple_production
                        FROM %I
                        WHERE DATE(timestamp) = $1
                    )
                    SELECT 
                        CASE 
                            WHEN significant_resets <= 2 THEN simple_production
                            ELSE simple_production / GREATEST(significant_resets, 1)
                        END as daily_production
                    FROM smart_calculation
                ', TG_TABLE_NAME)
                INTO daily_production
                USING DATE(NEW.timestamp);
                
                -- Get records count for this day
                EXECUTE format('
                    SELECT COUNT(*) FROM %I WHERE DATE(timestamp) = $1
                ', TG_TABLE_NAME)
                INTO records_count
                USING DATE(NEW.timestamp);
                
                -- Calculate self-consumption for this record
                IF daily_production > 0 AND records_count > 0 THEN
                    self_consumption_kwh := (daily_production * self_consumption_rate) / records_count;
                ELSE
                    self_consumption_kwh := 0;
                END IF;
                
                -- Set billing fields
                NEW.billing_tariff := energy_rate;
                NEW.billing_network_charge := network_rate;
                NEW.billing_etmear := etmear_rate;
                NEW.billing_schedule := schedule_name;
                NEW.billing_cost := 0.000;
                NEW.billing_benefit := self_consumption_kwh * total_rate;
                NEW.billing_net_metering_credit := 0.000;
                
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;
        """)
        
        print("   ✅ Hybrid billing function created")
        
        conn.commit()
        conn.close()
        
        print("\n🎉 SIMPLE SMART RESET COMPLETED!")
        print("✅ Tested multiple methods")
        print("✅ Hybrid billing function created")
        print("✅ Ready for implementation")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()

if __name__ == "__main__":
    simple_smart_reset()
