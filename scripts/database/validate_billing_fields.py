#!/usr/bin/env python3
"""
Validate Billing Fields
Check if billing fields match the correct dynamic calculation
"""

import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime, date

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 5433,
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

def get_db_connection():
    """Get database connection"""
    try:
        return psycopg2.connect(**DB_CONFIG)
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return None

def get_dynamic_tariff(timestamp):
    """Get dynamic tariff based on timestamp"""
    month = timestamp.month
    hour = timestamp.hour
    
    # Greek electricity tariffs 2025
    base_rates = {
        'network_rate': 0.0069,  # €/kWh (tier 1)
        'etmear_rate': 0.017     # €/kWh
    }
    
    # Seasonal and time-based variations
    if month in [11, 12, 1, 2, 3]:  # Winter
        if hour in [2, 3, 4] or hour in [12, 13, 14]:  # Night hours winter
            energy_rate = 0.120
            schedule = 'winter_night'
        else:
            energy_rate = 0.142
            schedule = 'winter_day'
    else:  # Summer
        if hour in [2, 3] or hour in [11, 12, 13, 14]:  # Night hours summer
            energy_rate = 0.132
            schedule = 'summer_night'
        else:
            energy_rate = 0.142
            schedule = 'summer_day'
    
    total_rate = energy_rate + base_rates['network_rate'] + base_rates['etmear_rate']
    
    return {
        'energy_rate': energy_rate,
        'network_rate': base_rates['network_rate'],
        'etmear_rate': base_rates['etmear_rate'],
        'total_rate': total_rate,
        'schedule': schedule
    }

def calculate_correct_self_consumption(production, hour, avg_soc, system_name):
    """Calculate correct dynamic self-consumption"""
    hour_of_day = hour.hour
    
    # Base self-consumption rate by time of day
    if 6 <= hour_of_day <= 18:  # Daytime
        base_rate = 0.35
    elif 19 <= hour_of_day <= 22:  # Evening
        base_rate = 0.80
    else:  # Night
        base_rate = 0.95
    
    # Adjust based on production level
    if production > 8:
        production_factor = 0.8
    elif production > 4:
        production_factor = 1.0
    else:
        production_factor = 1.2
    
    # Adjust based on battery SOC
    if avg_soc < 20:
        soc_factor = 1.3
    elif avg_soc > 80:
        soc_factor = 0.7
    else:
        soc_factor = 1.0
    
    # Calculate dynamic self-consumption rate
    dynamic_rate = min(1.0, base_rate * production_factor * soc_factor)
    return production * dynamic_rate

def validate_billing_fields_sample():
    """Validate billing fields against correct calculation for sample records"""
    print("\n" + "="*60)
    print("🔍 VALIDATING BILLING FIELDS")
    print("="*60)
    
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        
        target_date = date(2025, 6, 24)
        
        for table, system_name in [('solax_data', 'System 1'), ('solax_data2', 'System 2')]:
            print(f"\n📊 Validating {system_name}...")
            
            # Get sample records with billing fields
            cur.execute(f"""
                SELECT 
                    id,
                    timestamp,
                    yield_today,
                    LAG(yield_today) OVER (ORDER BY timestamp) as prev_yield,
                    CASE 
                        WHEN yield_today > LAG(yield_today) OVER (ORDER BY timestamp) 
                        THEN yield_today - LAG(yield_today) OVER (ORDER BY timestamp)
                        ELSE 0
                    END as hourly_production,
                    ac_power,
                    soc,
                    billing_cost,
                    billing_benefit,
                    billing_tariff,
                    billing_network_charge,
                    billing_etmear,
                    billing_schedule
                FROM {table}
                WHERE DATE(timestamp) = %s
                AND billing_cost IS NOT NULL
                ORDER BY timestamp
                LIMIT 10
            """, (target_date,))
            
            records = cur.fetchall()
            
            if not records:
                print(f"   ❌ No billing records found for {system_name}")
                continue
            
            print(f"   Found {len(records)} records with billing fields")
            
            total_errors = 0
            
            for record in records:
                timestamp = record['timestamp']
                hourly_production = float(record['hourly_production'] or 0)
                avg_soc = float(record['soc'] or 50)
                
                # Get correct tariff for this timestamp
                correct_tariff = get_dynamic_tariff(timestamp)
                
                # Calculate correct self-consumption
                if hourly_production > 0:
                    correct_self_consumption = calculate_correct_self_consumption(
                        hourly_production, timestamp, avg_soc, system_name
                    )
                else:
                    correct_self_consumption = 0
                
                # Calculate correct billing values
                correct_billing_cost = 0.000  # Assume no grid import for solar systems
                correct_billing_benefit = correct_self_consumption * correct_tariff['total_rate']
                
                # Compare with database values
                db_cost = float(record['billing_cost'] or 0)
                db_benefit = float(record['billing_benefit'] or 0)
                db_tariff = float(record['billing_tariff'] or 0)
                db_schedule = record['billing_schedule'] or ''
                
                # Check for errors
                cost_error = abs(db_cost - correct_billing_cost)
                benefit_error = abs(db_benefit - correct_billing_benefit) if correct_billing_benefit > 0 else 0
                tariff_error = abs(db_tariff - correct_tariff['energy_rate'])
                schedule_error = db_schedule != correct_tariff['schedule']
                
                has_error = (cost_error > 0.001 or benefit_error > 0.01 or 
                           tariff_error > 0.001 or schedule_error)
                
                if has_error:
                    total_errors += 1
                    print(f"\n   ❌ ERROR in record {record['id']} ({timestamp.strftime('%H:%M')}):")
                    print(f"      Production: {hourly_production:.2f} kWh")
                    print(f"      Correct self-consumption: {correct_self_consumption:.3f} kWh")
                    print(f"      DB cost: €{db_cost:.4f} | Correct: €{correct_billing_cost:.4f} | Error: €{cost_error:.4f}")
                    print(f"      DB benefit: €{db_benefit:.4f} | Correct: €{correct_billing_benefit:.4f} | Error: €{benefit_error:.4f}")
                    print(f"      DB tariff: €{db_tariff:.4f} | Correct: €{correct_tariff['energy_rate']:.4f} | Error: €{tariff_error:.4f}")
                    print(f"      DB schedule: '{db_schedule}' | Correct: '{correct_tariff['schedule']}' | Match: {not schedule_error}")
                else:
                    print(f"   ✅ Record {record['id']} ({timestamp.strftime('%H:%M')}): OK")
            
            print(f"\n   📊 {system_name} Validation Summary:")
            print(f"      Records checked: {len(records)}")
            print(f"      Records with errors: {total_errors}")
            print(f"      Error rate: {total_errors/len(records)*100:.1f}%")
            
            if total_errors == 0:
                print(f"      ✅ All billing fields are CORRECT!")
            else:
                print(f"      ❌ Billing fields need correction!")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

def check_billing_field_patterns():
    """Check patterns in billing fields to identify systematic errors"""
    print("\n" + "="*60)
    print("📈 BILLING FIELD PATTERNS ANALYSIS")
    print("="*60)
    
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        
        target_date = date(2025, 6, 24)
        
        for table, system_name in [('solax_data', 'System 1'), ('solax_data2', 'System 2')]:
            print(f"\n📊 {system_name} Patterns:")
            
            # Check billing field statistics
            cur.execute(f"""
                SELECT 
                    COUNT(*) as total_records,
                    COUNT(billing_cost) as has_cost,
                    COUNT(billing_benefit) as has_benefit,
                    COUNT(billing_schedule) as has_schedule,
                    ROUND(AVG(COALESCE(billing_cost, 0))::numeric, 6) as avg_cost,
                    ROUND(AVG(COALESCE(billing_benefit, 0))::numeric, 6) as avg_benefit,
                    ROUND(MIN(COALESCE(billing_cost, 0))::numeric, 6) as min_cost,
                    ROUND(MAX(COALESCE(billing_cost, 0))::numeric, 6) as max_cost,
                    ROUND(MIN(COALESCE(billing_benefit, 0))::numeric, 6) as min_benefit,
                    ROUND(MAX(COALESCE(billing_benefit, 0))::numeric, 6) as max_benefit,
                    COUNT(DISTINCT billing_schedule) as unique_schedules
                FROM {table}
                WHERE DATE(timestamp) = %s
            """, (target_date,))
            
            stats = cur.fetchone()
            
            if stats:
                coverage_cost = (stats['has_cost'] / stats['total_records'] * 100) if stats['total_records'] > 0 else 0
                coverage_benefit = (stats['has_benefit'] / stats['total_records'] * 100) if stats['total_records'] > 0 else 0
                
                print(f"   Total records: {stats['total_records']:,}")
                print(f"   Cost coverage: {coverage_cost:.1f}%")
                print(f"   Benefit coverage: {coverage_benefit:.1f}%")
                print(f"   Avg cost: €{stats['avg_cost']}")
                print(f"   Avg benefit: €{stats['avg_benefit']}")
                print(f"   Cost range: €{stats['min_cost']} - €{stats['max_cost']}")
                print(f"   Benefit range: €{stats['min_benefit']} - €{stats['max_benefit']}")
                print(f"   Unique schedules: {stats['unique_schedules']}")
                
                # Check if values are realistic
                if stats['max_cost'] > 1.0:
                    print(f"   ⚠️ High cost values detected (max €{stats['max_cost']})")
                
                if stats['max_benefit'] > 10.0:
                    print(f"   ⚠️ High benefit values detected (max €{stats['max_benefit']})")
                
                if coverage_cost < 95 or coverage_benefit < 95:
                    print(f"   ⚠️ Low coverage detected")
                
                # Check schedule distribution
                cur.execute(f"""
                    SELECT 
                        billing_schedule,
                        COUNT(*) as count,
                        ROUND(AVG(COALESCE(billing_benefit, 0))::numeric, 4) as avg_benefit
                    FROM {table}
                    WHERE DATE(timestamp) = %s
                    AND billing_schedule IS NOT NULL
                    GROUP BY billing_schedule
                    ORDER BY count DESC
                """, (target_date,))
                
                schedules = cur.fetchall()
                
                print(f"\n   📅 Schedule distribution:")
                for schedule in schedules:
                    print(f"      {schedule['billing_schedule']}: {schedule['count']} records (avg benefit: €{schedule['avg_benefit']})")
                
                # Check if all records have same schedule (error indicator)
                if len(schedules) == 1:
                    print(f"   ⚠️ All records have same schedule - may indicate static calculation")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

def recommend_billing_corrections():
    """Recommend corrections for billing fields"""
    print("\n" + "="*60)
    print("🔧 BILLING CORRECTION RECOMMENDATIONS")
    print("="*60)
    
    print(f"\n📋 IDENTIFIED ISSUES:")
    print(f"   1. Billing fields may use static calculation instead of dynamic")
    print(f"   2. Self-consumption rates may be fixed instead of hourly")
    print(f"   3. Tariff schedules may not reflect actual time-based rates")
    print(f"   4. Production calculations may not use proper reset logic")
    
    print(f"\n🔧 RECOMMENDED CORRECTIONS:")
    print(f"   1. Update billing calculation function to use dynamic self-consumption")
    print(f"   2. Apply correct hourly production calculation (yield differences)")
    print(f"   3. Use time-based tariff schedules (summer_day, summer_night, etc.)")
    print(f"   4. Recalculate all billing fields with corrected logic")
    
    print(f"\n💡 IMPLEMENTATION STEPS:")
    print(f"   1. Create new dynamic billing calculation function")
    print(f"   2. Update triggers to use dynamic calculation")
    print(f"   3. Recalculate historical billing fields")
    print(f"   4. Validate results against manual calculation")

def main():
    """Main validation function"""
    print("🔍 BILLING FIELDS VALIDATION")
    print("=" * 60)
    print("Checking if billing fields match correct dynamic calculation")
    print(f"Target Date: 24/6/2025")
    print(f"Time: {datetime.now()}")
    
    # Step 1: Validate sample records
    validate_billing_fields_sample()
    
    # Step 2: Check patterns
    check_billing_field_patterns()
    
    # Step 3: Recommend corrections
    recommend_billing_corrections()
    
    print("\n" + "="*60)
    print("🎯 VALIDATION COMPLETED")
    print("="*60)
    print("✅ Billing fields checked against dynamic calculation")
    print("✅ Patterns analyzed for systematic errors")
    print("✅ Correction recommendations provided")

if __name__ == "__main__":
    main()
