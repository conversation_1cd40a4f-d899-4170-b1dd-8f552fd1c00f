#!/usr/bin/env python3
"""
SMART RESET LOGIC FIX
Διορθώνει τη reset logic για να αγνοεί false resets και να υπολογίζει σωστά την παραγωγή
"""

import psycopg2
from datetime import datetime, date, timedelta
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 5433,
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

def fix_reset_logic_smart():
    """Διορθώνει τη reset logic με έξυπνη ανίχνευση"""
    
    print("🧠 SMART RESET LOGIC FIX")
    print("=" * 50)
    
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()
        
        print("✅ Connected to database")
        
        # 1. ΑΝΑΛΥΣΗ ΠΡΟΒΛΗΜΑΤΙΚΩΝ ΗΜΕΡΩΝ
        print("\n🔍 Analyzing problematic days...")
        
        problematic_dates = ['2025-06-15', '2025-06-10', '2025-06-20']
        good_dates = ['2025-05-25', '2025-04-15']
        
        for test_date in problematic_dates + good_dates:
            print(f"\n📅 {test_date}:")
            
            for table, system_name in [('solax_data', 'System 1'), ('solax_data2', 'System 2')]:
                # Count resets
                cur.execute(f"""
                    WITH daily_data AS (
                        SELECT 
                            timestamp,
                            yield_today,
                            LAG(yield_today) OVER (ORDER BY timestamp) as prev_yield
                        FROM {table}
                        WHERE DATE(timestamp) = %s
                        ORDER BY timestamp
                    )
                    SELECT 
                        COUNT(CASE WHEN yield_today < prev_yield THEN 1 END) as reset_count,
                        MIN(yield_today) as min_yield,
                        MAX(yield_today) as max_yield
                    FROM daily_data
                """, (test_date,))
                
                result = cur.fetchone()
                if result:
                    reset_count, min_yield, max_yield = result
                    print(f"   {system_name}: {reset_count} resets, range: {min_yield:.1f}-{max_yield:.1f} kWh")
        
        # 2. ΔΗΜΙΟΥΡΓΙΑ ΕΞΥΠΝΗΣ RESET LOGIC
        print("\n🧠 Creating smart reset detection...")
        
        cur.execute("""
            CREATE OR REPLACE FUNCTION calculate_smart_daily_production(
                table_name TEXT,
                target_date DATE
            ) RETURNS NUMERIC AS $$
            DECLARE
                daily_production NUMERIC := 0;
                first_reset_time TIMESTAMP;
                last_yield NUMERIC;
                first_yield_after_reset NUMERIC;
            BEGIN
                -- Find the first significant reset (> 10 kWh drop)
                SELECT timestamp INTO first_reset_time
                FROM (
                    SELECT 
                        timestamp,
                        yield_today,
                        LAG(yield_today) OVER (ORDER BY timestamp) as prev_yield
                    FROM (
                        SELECT * FROM solax_data WHERE DATE(timestamp) = target_date
                        UNION ALL
                        SELECT * FROM solax_data2 WHERE DATE(timestamp) = target_date
                    ) combined
                    WHERE combined.id IN (
                        SELECT id FROM solax_data WHERE DATE(timestamp) = target_date
                        UNION ALL
                        SELECT id FROM solax_data2 WHERE DATE(timestamp) = target_date
                    )
                    ORDER BY timestamp
                ) reset_analysis
                WHERE yield_today < prev_yield 
                AND (prev_yield - yield_today) > 10  -- Significant reset
                ORDER BY timestamp
                LIMIT 1;
                
                -- If no significant reset found, use midnight
                IF first_reset_time IS NULL THEN
                    first_reset_time := target_date::timestamp;
                END IF;
                
                -- Get first yield after reset and last yield of day
                EXECUTE format('
                    SELECT 
                        MIN(yield_today) as first_yield,
                        MAX(yield_today) as last_yield
                    FROM %I
                    WHERE DATE(timestamp) = $1
                    AND timestamp >= $2
                ', table_name)
                INTO first_yield_after_reset, last_yield
                USING target_date, first_reset_time;
                
                -- Calculate production
                daily_production := COALESCE(last_yield, 0) - COALESCE(first_yield_after_reset, 0);
                
                -- Ensure non-negative
                IF daily_production < 0 THEN
                    daily_production := 0;
                END IF;
                
                RETURN daily_production;
            END;
            $$ LANGUAGE plpgsql;
        """)
        
        print("   ✅ Smart reset function created")
        
        # 3. ΔΟΚΙΜΗ ΤΗΣ ΕΞΥΠΝΗΣ LOGIC
        print("\n🧪 Testing smart reset logic...")
        
        for test_date in ['2025-06-15', '2025-06-10', '2025-05-25']:
            print(f"\n📅 {test_date}:")
            
            for table, system_name in [('solax_data', 'System 1'), ('solax_data2', 'System 2')]:
                # Test smart calculation
                cur.execute("SELECT calculate_smart_daily_production(%s, %s)", (table, test_date))
                result = cur.fetchone()
                if result:
                    smart_production = float(result[0] or 0)
                    print(f"   {system_name}: Smart production = {smart_production:.2f} kWh")
                    
                    # Compare with old method
                    cur.execute(f"""
                        WITH daily_data AS (
                            SELECT 
                                timestamp,
                                yield_today,
                                LAG(yield_today) OVER (ORDER BY timestamp) as prev_yield
                            FROM {table}
                            WHERE DATE(timestamp) = %s
                            ORDER BY timestamp
                        ),
                        reset_points AS (
                            SELECT 
                                timestamp,
                                yield_today,
                                prev_yield,
                                CASE 
                                    WHEN yield_today < prev_yield THEN 1 
                                    ELSE 0 
                                END as is_reset
                            FROM daily_data
                        ),
                        with_reset_groups AS (
                            SELECT 
                                timestamp,
                                yield_today,
                                SUM(is_reset) OVER (ORDER BY timestamp) as reset_group
                            FROM reset_points
                        ),
                        daily_production AS (
                            SELECT 
                                reset_group,
                                MAX(yield_today) - MIN(yield_today) as group_production
                            FROM with_reset_groups
                            GROUP BY reset_group
                            HAVING MAX(yield_today) - MIN(yield_today) > 0
                        )
                        SELECT 
                            COALESCE(SUM(group_production), 0) as old_production
                        FROM daily_production
                    """, (test_date,))
                    
                    result = cur.fetchone()
                    if result:
                        old_production = float(result[0] or 0)
                        print(f"      Old method = {old_production:.2f} kWh")
                        
                        if old_production > 0:
                            improvement = ((old_production - smart_production) / old_production * 100)
                            print(f"      Improvement: {improvement:.1f}% reduction")
        
        # 4. ΔΗΜΙΟΥΡΓΙΑ ΝΕΑΣ BILLING FUNCTION
        print("\n💰 Creating new billing function with smart reset...")
        
        cur.execute("""
            CREATE OR REPLACE FUNCTION calculate_smart_billing_fields()
            RETURNS TRIGGER AS $$
            DECLARE
                daily_production NUMERIC := 0;
                self_consumption_kwh NUMERIC := 0;
                self_consumption_rate NUMERIC;
                
                -- Rates
                energy_rate NUMERIC := 0.142;
                network_rate NUMERIC := 0.0069;
                etmear_rate NUMERIC := 0.017;
                total_rate NUMERIC;
                
                hour_of_day INTEGER;
                month_of_year INTEGER;
                schedule_name TEXT := 'summer_day';
            BEGIN
                -- Get time components
                hour_of_day := EXTRACT(HOUR FROM NEW.timestamp);
                month_of_year := EXTRACT(MONTH FROM NEW.timestamp);
                
                -- Set self-consumption rate
                IF TG_TABLE_NAME = 'solax_data' THEN
                    self_consumption_rate := 0.281;  -- 28.1% for System 1
                ELSE
                    self_consumption_rate := 0.292;  -- 29.2% for System 2
                END IF;
                
                -- Seasonal tariff calculation
                IF month_of_year IN (11, 12, 1, 2, 3) THEN -- Winter
                    IF hour_of_day IN (2, 3, 4) OR hour_of_day IN (12, 13, 14) THEN
                        energy_rate := 0.120;
                        schedule_name := 'winter_night';
                    ELSE
                        energy_rate := 0.142;
                        schedule_name := 'winter_day';
                    END IF;
                ELSE -- Summer
                    IF hour_of_day IN (2, 3) OR hour_of_day IN (11, 12, 13, 14) THEN
                        energy_rate := 0.132;
                        schedule_name := 'summer_night';
                    ELSE
                        energy_rate := 0.142;
                        schedule_name := 'summer_day';
                    END IF;
                END IF;
                
                total_rate := energy_rate + network_rate + etmear_rate;
                
                -- Calculate daily production using smart method
                daily_production := calculate_smart_daily_production(TG_TABLE_NAME, DATE(NEW.timestamp));
                
                -- Calculate self-consumption for this record's proportion
                IF daily_production > 0 THEN
                    -- Simple proportional allocation based on records per day
                    self_consumption_kwh := (daily_production * self_consumption_rate) / 
                        (SELECT COUNT(*) FROM (
                            SELECT 1 FROM solax_data WHERE DATE(timestamp) = DATE(NEW.timestamp)
                            UNION ALL
                            SELECT 1 FROM solax_data2 WHERE DATE(timestamp) = DATE(NEW.timestamp)
                        ) daily_records);
                ELSE
                    self_consumption_kwh := 0;
                END IF;
                
                -- Set billing fields
                NEW.billing_tariff := energy_rate;
                NEW.billing_network_charge := network_rate;
                NEW.billing_etmear := etmear_rate;
                NEW.billing_schedule := schedule_name;
                NEW.billing_cost := 0.000;
                NEW.billing_benefit := self_consumption_kwh * total_rate;
                NEW.billing_net_metering_credit := 0.000;
                
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;
        """)
        
        print("   ✅ Smart billing function created")
        
        conn.commit()
        conn.close()
        
        print("\n🎉 SMART RESET LOGIC COMPLETED!")
        print("✅ Smart reset detection function created")
        print("✅ Smart billing calculation function created")
        print("✅ Ready for testing and implementation")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()

if __name__ == "__main__":
    fix_reset_logic_smart()
