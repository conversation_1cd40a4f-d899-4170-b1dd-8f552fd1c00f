#!/usr/bin/env python3
"""
FINAL CORRECT BILLING FIELDS FIX
Διορθώνει τα billing fields με σωστή yield differences logic και αληθινά self-consumption rates
"""

import psycopg2
from datetime import datetime, date
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 5433,
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

def fix_billing_fields_final():
    """Διορθώνει τα billing fields με σωστή λογική"""
    
    print("🔧 FINAL BILLING FIELDS CORRECTION")
    print("=" * 50)
    
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()
        
        print("✅ Connected to database")
        
        # 1. ΚΑΘΑΡΙΣΜΟΣ ΠΑΛΙΩΝ TRIGGERS
        print("\n🧹 Cleaning old triggers...")
        
        triggers_to_drop = [
            'trg_calculate_corrected_billing_solax_data',
            'trg_dynamic_billing_solax_data', 
            'trg_realistic_billing_solax_data',
            'trg_calculate_corrected_billing_solax_data2',
            'trg_dynamic_billing_solax_data2',
            'trg_realistic_billing_solax_data2'
        ]
        
        for trigger in triggers_to_drop:
            table = 'solax_data' if 'data2' not in trigger else 'solax_data2'
            cur.execute(f"DROP TRIGGER IF EXISTS {trigger} ON {table};")
            print(f"   ✅ Dropped {trigger}")
        
        # 2. ΔΗΜΙΟΥΡΓΙΑ ΣΩΣΤΗΣ FUNCTION
        print("\n🔧 Creating correct billing function...")
        
        cur.execute("""
            CREATE OR REPLACE FUNCTION calculate_final_correct_billing_fields()
            RETURNS TRIGGER AS $$
            DECLARE
                hourly_production NUMERIC := 0;
                prev_yield NUMERIC := 0;
                self_consumption_kwh NUMERIC := 0;
                self_consumption_rate NUMERIC;
                
                -- Σωστά tariff rates για Ελλάδα 2025
                energy_rate NUMERIC := 0.142;      -- €/kWh energy
                network_rate NUMERIC := 0.0069;    -- €/kWh network (tier 1)
                etmear_rate NUMERIC := 0.017;      -- €/kWh ETMEAR
                total_rate NUMERIC;
                
                hour_of_day INTEGER;
                month_of_year INTEGER;
                schedule_name TEXT := 'summer_day';
            BEGIN
                -- Get time components
                hour_of_day := EXTRACT(HOUR FROM NEW.timestamp);
                month_of_year := EXTRACT(MONTH FROM NEW.timestamp);
                
                -- Calculate hourly production (yield difference)
                IF TG_TABLE_NAME = 'solax_data' THEN
                    SELECT COALESCE(yield_today, 0) INTO prev_yield
                    FROM solax_data 
                    WHERE timestamp < NEW.timestamp 
                    AND DATE(timestamp) = DATE(NEW.timestamp)
                    ORDER BY timestamp DESC 
                    LIMIT 1;
                    
                    -- Σωστό self-consumption rate για System 1
                    self_consumption_rate := 0.281;  -- 28.1% από αληθινά δεδομένα
                ELSE
                    SELECT COALESCE(yield_today, 0) INTO prev_yield
                    FROM solax_data2 
                    WHERE timestamp < NEW.timestamp 
                    AND DATE(timestamp) = DATE(NEW.timestamp)
                    ORDER BY timestamp DESC 
                    LIMIT 1;
                    
                    -- Σωστό self-consumption rate για System 2
                    self_consumption_rate := 0.292;  -- 29.2% από αληθινά δεδομένα
                END IF;
                
                -- Calculate hourly production (yield difference)
                IF COALESCE(NEW.yield_today, 0) > prev_yield THEN
                    hourly_production := COALESCE(NEW.yield_today, 0) - prev_yield;
                ELSIF COALESCE(NEW.yield_today, 0) < prev_yield THEN
                    -- Reset case - use current yield
                    hourly_production := COALESCE(NEW.yield_today, 0);
                ELSE
                    hourly_production := 0;
                END IF;
                
                -- Seasonal tariff calculation
                IF month_of_year IN (11, 12, 1, 2, 3) THEN -- Winter
                    IF hour_of_day IN (2, 3, 4) OR hour_of_day IN (12, 13, 14) THEN
                        energy_rate := 0.120;  -- Winter night
                        schedule_name := 'winter_night';
                    ELSE
                        energy_rate := 0.142;  -- Winter day
                        schedule_name := 'winter_day';
                    END IF;
                ELSE -- Summer
                    IF hour_of_day IN (2, 3) OR hour_of_day IN (11, 12, 13, 14) THEN
                        energy_rate := 0.132;  -- Summer night
                        schedule_name := 'summer_night';
                    ELSE
                        energy_rate := 0.142;  -- Summer day
                        schedule_name := 'summer_day';
                    END IF;
                END IF;
                
                total_rate := energy_rate + network_rate + etmear_rate;
                
                -- Calculate self-consumption from hourly production
                IF hourly_production > 0 THEN
                    self_consumption_kwh := hourly_production * self_consumption_rate;
                ELSE
                    self_consumption_kwh := 0;
                END IF;
                
                -- Set billing fields
                NEW.billing_tariff := energy_rate;
                NEW.billing_network_charge := network_rate;
                NEW.billing_etmear := etmear_rate;
                NEW.billing_schedule := schedule_name;
                
                -- Calculate costs and benefits
                NEW.billing_cost := 0.000;  -- No grid import for solar systems
                NEW.billing_benefit := self_consumption_kwh * total_rate;
                NEW.billing_net_metering_credit := 0.000;  -- Greek Net Metering
                
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;
        """)
        
        print("   ✅ Created final correct billing function")
        
        # 3. ΔΗΜΙΟΥΡΓΙΑ ΝΕΩΝ TRIGGERS
        print("\n🔗 Creating new triggers...")
        
        cur.execute("""
            CREATE TRIGGER trg_final_correct_billing_solax_data
                BEFORE INSERT OR UPDATE ON solax_data
                FOR EACH ROW
                EXECUTE FUNCTION calculate_final_correct_billing_fields();
        """)
        
        cur.execute("""
            CREATE TRIGGER trg_final_correct_billing_solax_data2
                BEFORE INSERT OR UPDATE ON solax_data2
                FOR EACH ROW
                EXECUTE FUNCTION calculate_final_correct_billing_fields();
        """)
        
        print("   ✅ Created new triggers")
        
        conn.commit()
        print("\n✅ Database changes committed")
        
        # 4. ΕΝΗΜΕΡΩΣΗ ΥΠΑΡΧΟΝΤΩΝ RECORDS
        print("\n🔄 Updating existing records...")
        
        for table, system_name in [('solax_data', 'System 1'), ('solax_data2', 'System 2')]:
            print(f"\n📊 Updating {system_name}...")
            
            # Update all records with correct billing calculation
            cur.execute(f"""
                UPDATE {table} SET 
                    billing_benefit = billing_benefit + 0.000001,
                    timestamp = timestamp
                WHERE timestamp >= '2024-01-01'
                AND yield_today > 0
            """)
            
            updated_count = cur.rowcount
            print(f"   ✅ Updated {updated_count:,} records")
        
        conn.commit()
        print("\n✅ All records updated successfully")
        
        # 5. VALIDATION
        print("\n🔍 Validating corrected billing fields...")
        
        target_date = '2025-06-24'  # Η ημέρα που ξέρουμε τα αληθινά δεδομένα
        
        for table, system_name in [('solax_data', 'System 1'), ('solax_data2', 'System 2')]:
            print(f"\n📊 {system_name} Validation:")
            
            # Calculate using yield differences
            cur.execute(f"""
                WITH yield_differences AS (
                    SELECT 
                        timestamp,
                        yield_today,
                        LAG(yield_today) OVER (ORDER BY timestamp) as prev_yield,
                        CASE 
                            WHEN yield_today > LAG(yield_today) OVER (ORDER BY timestamp) 
                            THEN yield_today - LAG(yield_today) OVER (ORDER BY timestamp)
                            WHEN yield_today < LAG(yield_today) OVER (ORDER BY timestamp)
                            THEN yield_today  -- Reset case
                            ELSE 0
                        END as hourly_production,
                        billing_benefit
                    FROM {table}
                    WHERE DATE(timestamp) = %s
                    ORDER BY timestamp
                )
                SELECT 
                    SUM(hourly_production) as daily_production,
                    SUM(billing_benefit) as daily_benefit,
                    COUNT(*) as total_records,
                    ROUND(AVG(billing_benefit)::numeric, 6) as avg_benefit
                FROM yield_differences
            """, (target_date,))
            
            result = cur.fetchone()
            if result:
                daily_production, daily_benefit, total_records, avg_benefit = result
                print(f"   Production: {daily_production:.2f} kWh")
                print(f"   Daily Benefit: €{daily_benefit:.2f}")
                print(f"   Records: {total_records}")
                print(f"   Avg Benefit: €{avg_benefit:.6f}")
                
                if daily_production and daily_production > 0:
                    effective_rate = daily_benefit / daily_production
                    print(f"   Effective Rate: €{effective_rate:.4f}/kWh")
        
        conn.close()
        
        print("\n🎉 BILLING FIELDS CORRECTION COMPLETED!")
        print("✅ All triggers cleaned and recreated")
        print("✅ Correct function with yield differences")
        print("✅ Real self-consumption rates (28.1% & 29.2%)")
        print("✅ All existing records updated")
        print("✅ Validation completed")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()

def calculate_corrected_roi():
    """Υπολογίζει ROI με τα διορθωμένα billing fields"""

    print("\n💰 CALCULATING CORRECTED ROI & DAILY COST")
    print("=" * 50)

    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()

        # Αληθινά δεδομένα για validation
        real_data = {
            'system1': {'yield': 66.60, 'self_consumption': 18.69},
            'system2': {'yield': 66.90, 'self_consumption': 19.53}
        }

        target_date = '2025-06-24'
        investment_cost = 12500.0  # €12,500 per system

        print(f"\n📅 DAILY COST ANALYSIS ({target_date}):")

        total_daily_benefit = 0
        total_daily_production = 0

        for table, system_name, system_id in [
            ('solax_data', 'System 1', 'system1'),
            ('solax_data2', 'System 2', 'system2')
        ]:
            print(f"\n🏠 {system_name}:")

            # Get corrected billing data
            cur.execute(f"""
                WITH yield_differences AS (
                    SELECT
                        timestamp,
                        yield_today,
                        LAG(yield_today) OVER (ORDER BY timestamp) as prev_yield,
                        CASE
                            WHEN yield_today > LAG(yield_today) OVER (ORDER BY timestamp)
                            THEN yield_today - LAG(yield_today) OVER (ORDER BY timestamp)
                            WHEN yield_today < LAG(yield_today) OVER (ORDER BY timestamp)
                            THEN yield_today  -- Reset case
                            ELSE 0
                        END as hourly_production,
                        billing_benefit
                    FROM {table}
                    WHERE DATE(timestamp) = %s
                    ORDER BY timestamp
                )
                SELECT
                    SUM(hourly_production) as daily_production,
                    SUM(billing_benefit) as daily_benefit
                FROM yield_differences
            """, (target_date,))

            result = cur.fetchone()
            if result:
                daily_production, daily_benefit = result
                daily_production = float(daily_production or 0)
                daily_benefit = float(daily_benefit or 0)

                # Compare with real data
                real_yield = real_data[system_id]['yield']
                real_self_consumption = real_data[system_id]['self_consumption']

                print(f"   Production: {daily_production:.2f} kWh (Real: {real_yield:.2f} kWh)")
                print(f"   Daily Benefit: €{daily_benefit:.2f}")

                if daily_production > 0:
                    effective_rate = daily_benefit / daily_production
                    print(f"   Effective Rate: €{effective_rate:.4f}/kWh")

                    # Calculate expected benefit with real self-consumption
                    expected_benefit = real_self_consumption * 0.1659  # €0.1659/kWh total rate
                    print(f"   Expected Benefit: €{expected_benefit:.2f} (Real self-consumption)")

                    accuracy = (daily_benefit / expected_benefit * 100) if expected_benefit > 0 else 0
                    print(f"   Accuracy: {accuracy:.1f}%")

                total_daily_benefit += daily_benefit
                total_daily_production += daily_production

        print(f"\n🎯 COMBINED DAILY:")
        print(f"   Total Production: {total_daily_production:.2f} kWh")
        print(f"   Total Daily Benefit: €{total_daily_benefit:.2f}")

        if total_daily_production > 0:
            combined_effective_rate = total_daily_benefit / total_daily_production
            print(f"   Combined Effective Rate: €{combined_effective_rate:.4f}/kWh")

        # HISTORICAL ROI CALCULATION
        print(f"\n📈 HISTORICAL ROI CALCULATION:")

        total_annual_benefit = 0

        for table, system_name in [('solax_data', 'System 1'), ('solax_data2', 'System 2')]:
            print(f"\n🏠 {system_name}:")

            # Get historical totals using corrected billing fields
            cur.execute(f"""
                SELECT
                    COUNT(DISTINCT DATE(timestamp)) as total_days,
                    SUM(COALESCE(billing_benefit, 0)) as total_benefit,
                    MIN(timestamp) as start_date,
                    MAX(timestamp) as end_date
                FROM {table}
                WHERE timestamp >= '2024-01-01'
                AND billing_benefit IS NOT NULL
                AND billing_benefit > 0
            """)

            result = cur.fetchone()
            if result:
                total_days, total_benefit, start_date, end_date = result
                total_benefit = float(total_benefit or 0)

                # Calculate operational period
                if start_date and end_date:
                    operational_days = (end_date - start_date).days
                    operational_years = operational_days / 365.25
                else:
                    operational_years = 1

                # Calculate annual benefit
                annual_benefit = total_benefit / operational_years if operational_years > 0 else 0

                # Calculate ROI
                roi_percentage = (annual_benefit / investment_cost * 100) if investment_cost > 0 else 0
                payback_years = investment_cost / annual_benefit if annual_benefit > 0 else float('inf')

                print(f"   Operational Days: {total_days}")
                print(f"   Total Benefit: €{total_benefit:.2f}")
                print(f"   Annual Benefit: €{annual_benefit:.2f}")
                print(f"   ROI: {roi_percentage:.2f}%")
                print(f"   Payback: {payback_years:.1f} years")

                total_annual_benefit += annual_benefit

        # COMBINED ROI
        print(f"\n🎯 COMBINED ROI:")
        total_investment = investment_cost * 2  # Two systems
        combined_roi = (total_annual_benefit / total_investment * 100) if total_investment > 0 else 0
        combined_payback = total_investment / total_annual_benefit if total_annual_benefit > 0 else float('inf')

        print(f"   Total Investment: €{total_investment:,.0f}")
        print(f"   Total Annual Benefit: €{total_annual_benefit:.2f}")
        print(f"   Combined ROI: {combined_roi:.2f}%")
        print(f"   Combined Payback: {combined_payback:.1f} years")

        conn.close()

        print("\n✅ ROI CALCULATION COMPLETED!")

    except Exception as e:
        print(f"❌ Error: {e}")
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    fix_billing_fields_final()
    calculate_corrected_roi()
