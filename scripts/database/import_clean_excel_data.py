#!/usr/bin/env python3
"""
IMPORT CLEAN EXCEL DATA
Εισάγει τα καθαρά Excel data στη βάση δεδομένων
"""

import pandas as pd
import psycopg2
import os
import glob
from datetime import datetime, timedelta
import numpy as np

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 5433,
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

def process_excel_to_database_format(filepath, system_table):
    """Επεξεργάζεται Excel file και το μετατρέπει σε database format"""
    
    filename = os.path.basename(filepath)
    print(f"\n📁 Processing: {filename} → {system_table}")
    
    try:
        # Read Excel file
        df = pd.read_excel(filepath, skiprows=1)
        
        # Handle different column formats
        if len(df.columns) == 8:
            df.columns = [
                'no', 'timestamp', 'daily_pv_yield_kwh', 'daily_inverter_output_kwh', 
                'daily_exported_energy_kwh', 'daily_imported_energy_kwh', 
                'export_power_w', 'daily_consumed_kwh'
            ]
        elif len(df.columns) == 7:
            df.columns = [
                'no', 'timestamp', 'daily_pv_yield_kwh', 'daily_inverter_output_kwh',
                'daily_exported_energy_kwh', 'daily_consumed_kwh', 'daily_imported_energy_kwh'
            ]
        else:
            print(f"   ❌ Unexpected column count: {len(df.columns)}")
            return []
        
        # Convert timestamp and sort
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df = df.sort_values('timestamp').reset_index(drop=True)
        
        # Convert to database records format
        database_records = []
        
        for _, row in df.iterrows():
            # Map Excel columns to database columns (using actual column names)
            record = {
                'timestamp': row['timestamp'],
                'yield_today': float(row['daily_pv_yield_kwh']) if pd.notna(row['daily_pv_yield_kwh']) else 0.0,
                'feedin_energy': float(row['daily_exported_energy_kwh']) if pd.notna(row['daily_exported_energy_kwh']) else 0.0,
                'consume_energy': float(row['daily_consumed_kwh']) if pd.notna(row['daily_consumed_kwh']) else 0.0,
                'ac_power': 0.0,  # Not available in Excel data
                'soc': 0.0,  # Not available in Excel data (using 'soc' not 'battery_soc')
                'bat_power': 0.0,  # Not available in Excel data
                'powerdc1': 0.0,  # Not available in Excel data
                'powerdc2': 0.0,  # Not available in Excel data
                'feedin_power': 0.0,  # Not available in Excel data
                'temperature': 0.0,  # Not available in Excel data
                'yield_total': 0.0,  # Not available in Excel data
                'inverter_sn': '',  # Not available in Excel data
                'wifi_sn': ''  # Not available in Excel data
            }
            
            database_records.append(record)
        
        print(f"   ✅ Processed {len(database_records):,} records")
        return database_records
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return []

def import_to_database():
    """Εισάγει όλα τα Excel data στη βάση δεδομένων"""
    
    print("📊 IMPORTING CLEAN EXCEL DATA TO DATABASE")
    print("=" * 80)
    
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()
        
        print("✅ Connected to database")
        
        # Clear existing data first
        print("\n🧹 Clearing existing data...")
        cur.execute("DELETE FROM solax_data WHERE timestamp >= '2024-03-01'")
        cur.execute("DELETE FROM solax_data2 WHERE timestamp >= '2024-03-01'")
        
        deleted_1 = cur.rowcount
        print(f"   Cleared solax_data: {deleted_1:,} records")
        
        # Define file mappings
        file_mappings = [
            # System 1 files
            ("/home/<USER>/solar-prediction-project/data/raw/System1/*.xlsx", "solax_data"),
            ("/home/<USER>/solar-prediction-project/data/raw/new/System1*.xlsx", "solax_data"),
            ("/home/<USER>/solar-prediction-project/data/raw/new2/Plant Reports 2025-02-26-2025-06-25.xlsx", "solax_data"),
            ("/home/<USER>/solar-prediction-project/data/raw/Plant Reports 2025-04-16-2025-05-30.xlsx", "solax_data"),
            
            # System 2 files  
            ("/home/<USER>/solar-prediction-project/data/raw/System2/*.xlsx", "solax_data2"),
            ("/home/<USER>/solar-prediction-project/data/raw/new/System 2*.xlsx", "solax_data2"),
            ("/home/<USER>/solar-prediction-project/data/raw/new2/Plant Reports 2025-02-26-2025-06-25 (1).xlsx", "solax_data2"),
            ("/home/<USER>/solar-prediction-project/data/raw/Plant Reports 2025-04-16-2025-05-30 (1).xlsx", "solax_data2"),
        ]
        
        total_imported = 0
        
        # Process each file mapping
        for pattern, table_name in file_mappings:
            if '*' in pattern:
                files = glob.glob(pattern)
            else:
                files = [pattern] if os.path.exists(pattern) else []
            
            for filepath in sorted(files):
                records = process_excel_to_database_format(filepath, table_name)
                
                if records:
                    # Insert records into database
                    print(f"   📥 Inserting {len(records):,} records into {table_name}...")
                    
                    # Prepare insert statement (using actual column names)
                    if table_name == 'solax_data':
                        # solax_data has unique constraint on timestamp
                        insert_sql = f"""
                            INSERT INTO {table_name} (
                                timestamp, yield_today, feedin_energy, consume_energy,
                                ac_power, soc, bat_power, powerdc1, powerdc2, feedin_power,
                                temperature, yield_total, inverter_sn, wifi_sn
                            ) VALUES (
                                %(timestamp)s, %(yield_today)s, %(feedin_energy)s, %(consume_energy)s,
                                %(ac_power)s, %(soc)s, %(bat_power)s, %(powerdc1)s,
                                %(powerdc2)s, %(feedin_power)s, %(temperature)s,
                                %(yield_total)s, %(inverter_sn)s, %(wifi_sn)s
                            )
                            ON CONFLICT (timestamp) DO UPDATE SET
                                yield_today = EXCLUDED.yield_today,
                                feedin_energy = EXCLUDED.feedin_energy,
                                consume_energy = EXCLUDED.consume_energy
                        """
                    else:
                        # solax_data2 doesn't have unique constraint, so just insert
                        insert_sql = f"""
                            INSERT INTO {table_name} (
                                timestamp, yield_today, feedin_energy, consume_energy,
                                ac_power, soc, bat_power, powerdc1, powerdc2, feedin_power,
                                temperature, yield_total, inverter_sn, wifi_sn
                            ) VALUES (
                                %(timestamp)s, %(yield_today)s, %(feedin_energy)s, %(consume_energy)s,
                                %(ac_power)s, %(soc)s, %(bat_power)s, %(powerdc1)s,
                                %(powerdc2)s, %(feedin_power)s, %(temperature)s,
                                %(yield_total)s, %(inverter_sn)s, %(wifi_sn)s
                            )
                        """
                    
                    # Execute batch insert
                    cur.executemany(insert_sql, records)
                    inserted_count = cur.rowcount
                    total_imported += inserted_count
                    
                    print(f"   ✅ Inserted {inserted_count:,} records")
        
        conn.commit()
        
        # Verify import
        print(f"\n🔍 VERIFYING IMPORT...")
        
        for table_name in ['solax_data', 'solax_data2']:
            cur.execute(f"""
                SELECT 
                    COUNT(*) as total_records,
                    MIN(timestamp) as start_date,
                    MAX(timestamp) as end_date,
                    SUM(CASE WHEN yield_today > 0 THEN 1 ELSE 0 END) as records_with_yield
                FROM {table_name}
                WHERE timestamp >= '2024-03-01'
            """)
            
            result = cur.fetchone()
            if result:
                total_records, start_date, end_date, records_with_yield = result
                print(f"   {table_name}:")
                print(f"      Total records: {total_records:,}")
                print(f"      Date range: {start_date} to {end_date}")
                print(f"      Records with yield: {records_with_yield:,}")
        
        conn.close()
        
        print(f"\n🎉 IMPORT COMPLETED!")
        print(f"✅ Total records imported: {total_imported:,}")
        print("✅ Clean Excel data now in database")
        print("✅ Ready for billing fields calculation")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()

if __name__ == "__main__":
    import_to_database()
