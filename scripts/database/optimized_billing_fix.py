#!/usr/bin/env python3
"""
Optimized Billing Fix
Fast correction using small batches and targeted updates
"""

import psycopg2
from datetime import datetime, date
import time

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 5433,
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

def get_db_connection():
    """Get database connection"""
    try:
        return psycopg2.connect(**DB_CONFIG)
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return None

def fix_unrealistic_values():
    """Fix unrealistic billing values quickly"""
    print("\n" + "="*60)
    print("🔧 FIXING UNREALISTIC VALUES")
    print("="*60)
    
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cur = conn.cursor()
        
        # Step 1: Fix System 1 high costs (targeted)
        print("\n📊 Step 1: Fix System 1 unrealistic costs...")
        cur.execute("""
            UPDATE solax_data SET 
                billing_cost = 0.000
            WHERE billing_cost > 100
        """)
        system1_fixed = cur.rowcount
        print(f"   ✅ Fixed {system1_fixed} System 1 records")
        
        # Step 2: Fix System 2 high costs (targeted)
        print("\n📊 Step 2: Fix System 2 unrealistic costs...")
        cur.execute("""
            UPDATE solax_data2 SET 
                billing_cost = 0.000
            WHERE billing_cost > 100
        """)
        system2_fixed = cur.rowcount
        print(f"   ✅ Fixed {system2_fixed} System 2 records")
        
        conn.commit()
        print("   ✅ Unrealistic values fixed")
        
        # Verification
        print("\n📈 Verification:")
        cur.execute("""
            SELECT 
                'System 1' as system,
                COUNT(CASE WHEN billing_cost > 100 THEN 1 END) as high_cost_records,
                ROUND(MAX(COALESCE(billing_cost, 0))::numeric, 4) as max_cost
            FROM solax_data 
            WHERE timestamp >= '2024-06-01'
            
            UNION ALL
            
            SELECT 
                'System 2' as system,
                COUNT(CASE WHEN billing_cost > 100 THEN 1 END) as high_cost_records,
                ROUND(MAX(COALESCE(billing_cost, 0))::numeric, 4) as max_cost
            FROM solax_data2 
            WHERE timestamp >= '2024-06-01'
        """)
        
        results = cur.fetchall()
        for system, high_records, max_cost in results:
            print(f"   {system}: {high_records} high records, max cost €{max_cost}")
            if high_records == 0:
                print(f"      ✅ {system} fixed successfully")
            else:
                print(f"      ⚠️ {system} still has issues")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        if conn:
            conn.rollback()

def fill_missing_system2_fields():
    """Fill missing System 2 billing fields in small batches"""
    print("\n" + "="*60)
    print("🔧 FILLING MISSING SYSTEM 2 FIELDS")
    print("="*60)
    
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cur = conn.cursor()
        
        # Check how many records need filling
        cur.execute("""
            SELECT COUNT(*) 
            FROM solax_data2 
            WHERE billing_tariff IS NULL 
            AND timestamp >= '2024-06-01'
        """)
        missing_count = cur.fetchone()[0]
        print(f"   Records to fill: {missing_count:,}")
        
        if missing_count == 0:
            print("   ✅ No missing records to fill")
            conn.close()
            return
        
        # Fill in small batches
        batch_size = 5000
        total_filled = 0
        
        while True:
            cur.execute("""
                UPDATE solax_data2 SET
                    billing_tariff = 0.142,
                    billing_network_charge = 0.0069,
                    billing_etmear = 0.017,
                    billing_cost = 0.000,
                    billing_benefit = 0.000,
                    billing_net_metering_credit = 0.000,
                    billing_schedule = 'summer_day'
                WHERE id IN (
                    SELECT id FROM solax_data2 
                    WHERE billing_tariff IS NULL 
                    AND timestamp >= '2024-06-01'
                    LIMIT %s
                )
            """, (batch_size,))
            
            batch_filled = cur.rowcount
            total_filled += batch_filled
            
            print(f"   Filled batch: {batch_filled:,} records (Total: {total_filled:,})")
            
            if batch_filled == 0:
                break
            
            conn.commit()
            time.sleep(0.1)  # Small pause to avoid overwhelming
        
        print(f"   ✅ Total filled: {total_filled:,} records")
        
        # Verification
        cur.execute("""
            SELECT 
                COUNT(*) as total_records,
                COUNT(billing_tariff) as has_tariff,
                COUNT(billing_cost) as has_cost
            FROM solax_data2 
            WHERE timestamp >= '2024-06-01'
        """)
        
        total, has_tariff, has_cost = cur.fetchone()
        coverage = (has_tariff / total * 100) if total > 0 else 0
        
        print(f"   Final coverage: {coverage:.1f}% ({has_tariff:,}/{total:,})")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        if conn:
            conn.rollback()

def calculate_current_roi_daily_cost():
    """Calculate ROI and Daily Cost with corrected data"""
    print("\n" + "="*60)
    print("💰 CURRENT ROI & DAILY COST CALCULATION")
    print("="*60)
    
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cur = conn.cursor()
        
        today = date.today()
        investment_cost = 12500.0
        
        # Daily Cost Calculation
        print(f"\n📅 DAILY COST ({today}):")
        
        # System 1
        cur.execute("""
            SELECT 
                SUM(COALESCE(billing_cost, 0)) as total_cost,
                SUM(COALESCE(billing_benefit, 0)) as total_benefit,
                MAX(yield_today) as production,
                COUNT(*) as records
            FROM solax_data 
            WHERE DATE(timestamp) = %s
        """, (today,))
        
        result = cur.fetchone()
        if result:
            cost1, benefit1, prod1, records1 = result
            net_cost1 = (cost1 or 0) - (benefit1 or 0)
            
            # Manual calculation for comparison
            if prod1:
                manual_benefit1 = prod1 * 0.405 * 0.1659  # 40.5% self-consumption
                
            print(f"   🏠 System 1:")
            print(f"      Production: {prod1:.2f} kWh" if prod1 else "      Production: 0.00 kWh")
            print(f"      DB cost: €{cost1:.4f}" if cost1 else "      DB cost: €0.0000")
            print(f"      DB benefit: €{benefit1:.4f}" if benefit1 else "      DB benefit: €0.0000")
            print(f"      Net cost: €{net_cost1:.4f}")
            if prod1:
                print(f"      Manual benefit: €{manual_benefit1:.4f}")
            print(f"      Records: {records1}")
        
        # System 2
        cur.execute("""
            SELECT 
                SUM(COALESCE(billing_cost, 0)) as total_cost,
                SUM(COALESCE(billing_benefit, 0)) as total_benefit,
                MAX(COALESCE(yield_today, 0)) as production,
                COUNT(*) as records,
                COUNT(billing_cost) as has_billing
            FROM solax_data2 
            WHERE DATE(timestamp) = %s
        """, (today,))
        
        result = cur.fetchone()
        if result:
            cost2, benefit2, prod2, records2, has_billing2 = result
            net_cost2 = (cost2 or 0) - (benefit2 or 0)
            coverage2 = (has_billing2 / records2 * 100) if records2 > 0 else 0
            
            # Manual calculation for comparison
            if prod2:
                manual_benefit2 = prod2 * 0.47 * 0.1659  # 47% self-consumption
                
            print(f"   🏠 System 2:")
            print(f"      Production: {prod2:.2f} kWh")
            print(f"      DB cost: €{cost2:.4f}" if cost2 else "      DB cost: €0.0000")
            print(f"      DB benefit: €{benefit2:.4f}" if benefit2 else "      DB benefit: €0.0000")
            print(f"      Net cost: €{net_cost2:.4f}")
            if prod2:
                print(f"      Manual benefit: €{manual_benefit2:.4f}")
            print(f"      Records: {records2}")
            print(f"      Billing coverage: {coverage2:.1f}%")
        
        # Combined
        if result:
            total_cost = (cost1 or 0) + (cost2 or 0)
            total_benefit = (benefit1 or 0) + (benefit2 or 0)
            total_production = (prod1 or 0) + (prod2 or 0)
            total_net_cost = total_cost - total_benefit
            
            print(f"   🎯 Combined:")
            print(f"      Total production: {total_production:.2f} kWh")
            print(f"      Total cost: €{total_cost:.4f}")
            print(f"      Total benefit: €{total_benefit:.4f}")
            print(f"      Total net cost: €{total_net_cost:.4f}")
        
        # ROI Calculation
        print(f"\n📈 ROI CALCULATION:")
        
        # System 1 ROI
        cur.execute("""
            SELECT 
                SUM(COALESCE(billing_benefit, 0)) as total_benefit,
                SUM(COALESCE(billing_cost, 0)) as total_cost,
                COUNT(DISTINCT DATE(timestamp)) as operational_days
            FROM solax_data 
            WHERE timestamp >= '2024-01-01'
        """)
        
        result = cur.fetchone()
        if result and result[2] > 0:
            benefit, cost, days = result
            annual_benefit = (float(benefit or 0) / days) * 365
            annual_cost = (float(cost or 0) / days) * 365
            net_annual_benefit = annual_benefit - annual_cost
            annual_roi = (net_annual_benefit / investment_cost) * 100 if investment_cost > 0 else 0
            payback_years = investment_cost / net_annual_benefit if net_annual_benefit > 0 else None
            
            print(f"   🏠 System 1 ROI:")
            print(f"      Annual benefit: €{annual_benefit:.2f}")
            print(f"      Annual cost: €{annual_cost:.2f}")
            print(f"      Net annual benefit: €{net_annual_benefit:.2f}")
            print(f"      Annual ROI: {annual_roi:.2f}%")
            print(f"      Payback: {payback_years:.1f} years" if payback_years else "      Payback: N/A")
        
        # System 2 ROI
        cur.execute("""
            SELECT 
                SUM(COALESCE(billing_benefit, 0)) as total_benefit,
                SUM(COALESCE(billing_cost, 0)) as total_cost,
                COUNT(DISTINCT DATE(timestamp)) as operational_days
            FROM solax_data2 
            WHERE timestamp >= '2024-01-01'
        """)
        
        result = cur.fetchone()
        if result and result[2] > 0:
            benefit, cost, days = result
            annual_benefit = (float(benefit or 0) / days) * 365
            annual_cost = (float(cost or 0) / days) * 365
            net_annual_benefit = annual_benefit - annual_cost
            annual_roi = (net_annual_benefit / investment_cost) * 100 if investment_cost > 0 else 0
            payback_years = investment_cost / net_annual_benefit if net_annual_benefit > 0 else None
            
            print(f"   🏠 System 2 ROI:")
            print(f"      Annual benefit: €{annual_benefit:.2f}")
            print(f"      Annual cost: €{annual_cost:.2f}")
            print(f"      Net annual benefit: €{net_annual_benefit:.2f}")
            print(f"      Annual ROI: {annual_roi:.2f}%")
            print(f"      Payback: {payback_years:.1f} years" if payback_years else "      Payback: N/A")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Main optimization function"""
    print("🚀 OPTIMIZED BILLING CORRECTION")
    print("=" * 60)
    print("Using small batches and targeted updates for fast performance")
    
    start_time = time.time()
    
    # Step 1: Fix unrealistic values
    fix_unrealistic_values()
    
    # Step 2: Fill missing System 2 fields
    fill_missing_system2_fields()
    
    # Step 3: Calculate current metrics
    calculate_current_roi_daily_cost()
    
    end_time = time.time()
    duration = end_time - start_time
    
    print("\n" + "="*60)
    print("🎯 OPTIMIZED CORRECTION COMPLETED")
    print("="*60)
    print(f"⏱️ Duration: {duration:.1f} seconds")
    print("✅ Unrealistic values fixed")
    print("✅ System 2 fields filled")
    print("✅ Current ROI & Daily Cost calculated")

if __name__ == "__main__":
    main()
