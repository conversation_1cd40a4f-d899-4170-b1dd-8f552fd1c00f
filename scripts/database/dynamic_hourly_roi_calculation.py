#!/usr/bin/env python3
"""
Dynamic Hourly ROI Calculation
Calculate ROI using real hourly data with seasonal/time-based tariffs
"""

import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime, date, time
import calendar

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 5433,
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

def get_db_connection():
    """Get database connection"""
    try:
        return psycopg2.connect(**DB_CONFIG)
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return None

def get_dynamic_tariff(timestamp):
    """Get dynamic tariff based on timestamp (seasonal + time-based)"""
    month = timestamp.month
    hour = timestamp.hour
    
    # Greek electricity tariffs 2025
    base_rates = {
        'energy_rate': 0.142,  # €/kWh
        'network_rate': 0.0069,  # €/kWh (tier 1)
        'etmear_rate': 0.017   # €/kWh
    }
    
    # Seasonal and time-based variations
    if month in [11, 12, 1, 2, 3]:  # Winter (November-March)
        if hour in [2, 3, 4] or hour in [12, 13, 14]:  # Night hours winter
            energy_rate = 0.120  # Night rate
            schedule = 'winter_night'
        else:
            energy_rate = 0.142  # Day rate
            schedule = 'winter_day'
    else:  # Summer (April-October)
        if hour in [2, 3] or hour in [11, 12, 13, 14]:  # Night hours summer
            energy_rate = 0.132  # Night rate
            schedule = 'summer_night'
        else:
            energy_rate = 0.142  # Day rate
            schedule = 'summer_day'
    
    total_rate = energy_rate + base_rates['network_rate'] + base_rates['etmear_rate']
    
    return {
        'energy_rate': energy_rate,
        'network_rate': base_rates['network_rate'],
        'etmear_rate': base_rates['etmear_rate'],
        'total_rate': total_rate,
        'schedule': schedule
    }

def calculate_hourly_production_consumption():
    """Calculate hourly production and consumption for 24/6/2025"""
    print("\n" + "="*60)
    print("⏰ HOURLY PRODUCTION & CONSUMPTION ANALYSIS")
    print("="*60)
    
    conn = get_db_connection()
    if not conn:
        return None
    
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        
        target_date = date(2025, 6, 24)
        
        # Get hourly data for both systems
        hourly_data = {}
        
        for table, system_name in [('solax_data', 'System 1'), ('solax_data2', 'System 2')]:
            print(f"\n📊 Processing {system_name}...")
            
            # Get records with yield differences (hourly production)
            cur.execute(f"""
                WITH hourly_production AS (
                    SELECT 
                        DATE_TRUNC('hour', timestamp) as hour,
                        MIN(timestamp) as first_record,
                        MAX(timestamp) as last_record,
                        COUNT(*) as records_count,
                        MIN(yield_today) as min_yield,
                        MAX(yield_today) as max_yield,
                        MAX(yield_today) - MIN(yield_today) as hourly_production,
                        AVG(COALESCE(ac_power, 0)) as avg_ac_power,
                        AVG(COALESCE(soc, 0)) as avg_soc,
                        -- Estimate consumption based on battery and grid data
                        AVG(COALESCE(consume_energy, 0)) as avg_consumption
                    FROM {table}
                    WHERE DATE(timestamp) = %s
                    GROUP BY DATE_TRUNC('hour', timestamp)
                    ORDER BY hour
                )
                SELECT * FROM hourly_production
                WHERE hourly_production > 0  -- Only hours with actual production
            """, (target_date,))
            
            system_hourly = cur.fetchall()
            
            if system_hourly:
                hourly_data[system_name] = system_hourly
                print(f"   Found {len(system_hourly)} productive hours")
                
                total_production = sum(float(h['hourly_production']) for h in system_hourly)
                print(f"   Total daily production: {total_production:.2f} kWh")
            else:
                print(f"   ❌ No hourly data found")
        
        conn.close()
        return hourly_data
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def calculate_dynamic_self_consumption(hourly_data):
    """Calculate dynamic self-consumption based on actual hourly patterns"""
    print("\n" + "="*60)
    print("🔄 DYNAMIC SELF-CONSUMPTION CALCULATION")
    print("="*60)
    
    if not hourly_data:
        return None
    
    results = {}
    
    for system_name, hours in hourly_data.items():
        print(f"\n🏠 {system_name} Dynamic Analysis:")
        
        total_production = 0
        total_self_consumption = 0
        hourly_details = []
        
        for hour_data in hours:
            hour = hour_data['hour']
            production = float(hour_data['hourly_production'])
            avg_power = float(hour_data['avg_ac_power'] or 0)
            avg_soc = float(hour_data['avg_soc'] or 0)
            
            # Dynamic self-consumption calculation based on:
            # 1. Time of day (higher consumption during day)
            # 2. Production level (higher production = lower self-consumption %)
            # 3. Battery SOC (lower SOC = more self-consumption)
            
            hour_of_day = hour.hour
            
            # Base self-consumption rate by time of day
            if 6 <= hour_of_day <= 18:  # Daytime
                base_self_consumption_rate = 0.35  # Lower during high production
            elif 19 <= hour_of_day <= 22:  # Evening
                base_self_consumption_rate = 0.80  # Higher during evening consumption
            else:  # Night
                base_self_consumption_rate = 0.95  # Almost all self-consumed at night
            
            # Adjust based on production level
            if production > 8:  # High production hour
                production_factor = 0.8  # Lower self-consumption %
            elif production > 4:  # Medium production
                production_factor = 1.0  # Normal self-consumption %
            else:  # Low production
                production_factor = 1.2  # Higher self-consumption %
            
            # Adjust based on battery SOC
            if avg_soc < 20:  # Low battery
                soc_factor = 1.3  # More self-consumption (charging)
            elif avg_soc > 80:  # High battery
                soc_factor = 0.7  # Less self-consumption (full battery)
            else:  # Normal battery
                soc_factor = 1.0
            
            # Calculate dynamic self-consumption rate
            dynamic_rate = min(1.0, base_self_consumption_rate * production_factor * soc_factor)
            self_consumption = production * dynamic_rate
            
            total_production += production
            total_self_consumption += self_consumption
            
            hourly_details.append({
                'hour': hour,
                'production': production,
                'self_consumption': self_consumption,
                'self_consumption_rate': dynamic_rate,
                'avg_power': avg_power,
                'avg_soc': avg_soc
            })
            
            print(f"   {hour.strftime('%H:00')}: {production:.1f}kWh → {self_consumption:.1f}kWh ({dynamic_rate:.1%})")
        
        overall_self_consumption_rate = total_self_consumption / total_production if total_production > 0 else 0
        
        print(f"\n   📊 {system_name} Summary:")
        print(f"      Total production: {total_production:.2f} kWh")
        print(f"      Total self-consumption: {total_self_consumption:.2f} kWh")
        print(f"      Dynamic self-consumption rate: {overall_self_consumption_rate:.1%}")
        
        results[system_name] = {
            'total_production': total_production,
            'total_self_consumption': total_self_consumption,
            'self_consumption_rate': overall_self_consumption_rate,
            'hourly_details': hourly_details
        }
    
    return results

def calculate_dynamic_financial_benefit(self_consumption_data):
    """Calculate financial benefit using dynamic tariffs"""
    print("\n" + "="*60)
    print("💰 DYNAMIC FINANCIAL BENEFIT CALCULATION")
    print("="*60)
    
    if not self_consumption_data:
        return None
    
    total_daily_benefit = 0
    total_daily_production = 0
    total_daily_self_consumption = 0
    
    for system_name, data in self_consumption_data.items():
        print(f"\n🏠 {system_name} Financial Analysis:")
        
        system_daily_benefit = 0
        
        for hour_detail in data['hourly_details']:
            hour = hour_detail['hour']
            self_consumption = hour_detail['self_consumption']
            
            # Get dynamic tariff for this hour
            tariff = get_dynamic_tariff(hour)
            
            # Calculate benefit for this hour
            hourly_benefit = self_consumption * tariff['total_rate']
            system_daily_benefit += hourly_benefit
            
            print(f"   {hour.strftime('%H:00')}: {self_consumption:.1f}kWh × €{tariff['total_rate']:.4f} = €{hourly_benefit:.3f} ({tariff['schedule']})")
        
        print(f"\n   💰 {system_name} Daily Benefit: €{system_daily_benefit:.2f}")
        
        total_daily_benefit += system_daily_benefit
        total_daily_production += data['total_production']
        total_daily_self_consumption += data['total_self_consumption']
    
    # Calculate dynamic effective rate
    dynamic_effective_rate = total_daily_benefit / total_daily_production if total_daily_production > 0 else 0
    self_consumption_effective_rate = total_daily_benefit / total_daily_self_consumption if total_daily_self_consumption > 0 else 0
    
    print(f"\n🎯 COMBINED DYNAMIC RESULTS:")
    print(f"   Total daily production: {total_daily_production:.2f} kWh")
    print(f"   Total daily self-consumption: {total_daily_self_consumption:.2f} kWh")
    print(f"   Total daily benefit: €{total_daily_benefit:.2f}")
    print(f"   Dynamic effective rate (on production): €{dynamic_effective_rate:.6f}/kWh")
    print(f"   Dynamic effective rate (on self-consumption): €{self_consumption_effective_rate:.6f}/kWh")
    print(f"   Overall self-consumption rate: {total_daily_self_consumption/total_daily_production:.1%}")
    
    return {
        'total_daily_production': total_daily_production,
        'total_daily_self_consumption': total_daily_self_consumption,
        'total_daily_benefit': total_daily_benefit,
        'dynamic_effective_rate': dynamic_effective_rate,
        'self_consumption_effective_rate': self_consumption_effective_rate,
        'overall_self_consumption_rate': total_daily_self_consumption/total_daily_production
    }

def calculate_dynamic_annual_roi(daily_results):
    """Calculate annual ROI using dynamic daily results"""
    print("\n" + "="*60)
    print("📈 DYNAMIC ANNUAL ROI CALCULATION")
    print("="*60)
    
    if not daily_results:
        return
    
    # Project annual results
    annual_production = daily_results['total_daily_production'] * 365
    annual_self_consumption = daily_results['total_daily_self_consumption'] * 365
    annual_benefit = daily_results['total_daily_benefit'] * 365
    
    # Investment details
    total_investment = 25000.0  # €25,000 for both systems
    
    # Calculate ROI
    annual_roi = (annual_benefit / total_investment) * 100
    payback_years = total_investment / annual_benefit if annual_benefit > 0 else None
    
    print(f"\n📊 DYNAMIC ANNUAL PROJECTIONS:")
    print(f"   Annual production: {annual_production:,.0f} kWh")
    print(f"   Annual self-consumption: {annual_self_consumption:,.0f} kWh")
    print(f"   Annual benefit: €{annual_benefit:.2f}")
    print(f"   Dynamic ROI: {annual_roi:.2f}%")
    print(f"   Dynamic payback: {payback_years:.1f} years" if payback_years else "   Dynamic payback: N/A")
    
    print(f"\n⚖️ COMPARISON WITH STATIC CALCULATION:")
    static_effective_rate = 0.072653  # From previous static calculation
    static_annual_benefit = annual_production * static_effective_rate
    static_roi = (static_annual_benefit / total_investment) * 100
    
    print(f"   Static annual benefit: €{static_annual_benefit:.2f}")
    print(f"   Static ROI: {static_roi:.2f}%")
    print(f"   Dynamic vs Static difference: €{annual_benefit - static_annual_benefit:.2f} ({((annual_benefit/static_annual_benefit-1)*100):+.1f}%)")

def main():
    """Main dynamic calculation function"""
    print("⚡ DYNAMIC HOURLY ROI CALCULATION")
    print("=" * 60)
    print("Using real hourly data with seasonal/time-based tariffs")
    print(f"Target Date: 24/6/2025")
    print(f"Time: {datetime.now()}")
    
    # Step 1: Get hourly production data
    hourly_data = calculate_hourly_production_consumption()
    
    if not hourly_data:
        print("❌ Could not get hourly data")
        return
    
    # Step 2: Calculate dynamic self-consumption
    self_consumption_data = calculate_dynamic_self_consumption(hourly_data)
    
    if not self_consumption_data:
        print("❌ Could not calculate self-consumption")
        return
    
    # Step 3: Calculate dynamic financial benefit
    daily_results = calculate_dynamic_financial_benefit(self_consumption_data)
    
    if not daily_results:
        print("❌ Could not calculate financial benefit")
        return
    
    # Step 4: Calculate annual ROI
    calculate_dynamic_annual_roi(daily_results)
    
    print("\n" + "="*60)
    print("🎯 DYNAMIC CALCULATION COMPLETED")
    print("="*60)
    print("✅ Hourly production analyzed")
    print("✅ Dynamic self-consumption calculated")
    print("✅ Seasonal/time-based tariffs applied")
    print("✅ Real dynamic ROI calculated")
    print("\n📝 This is the TRUE dynamic calculation!")

if __name__ == "__main__":
    main()
