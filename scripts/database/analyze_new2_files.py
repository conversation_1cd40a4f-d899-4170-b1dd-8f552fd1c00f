#!/usr/bin/env python3
"""
ANALYZE NEW2 FILES
Αναλύει τα νέα αρχεία στο new2 directory
"""

import pandas as pd
import os
from datetime import datetime

def analyze_new2_file(filepath):
    """Αναλύει ένα αρχείο από το new2 directory"""
    
    filename = os.path.basename(filepath)
    print(f"\n📁 {filename}")
    print("-" * 80)
    
    try:
        # Read Excel file
        df = pd.read_excel(filepath, skiprows=1)
        
        print(f"   📊 Raw columns ({len(df.columns)}): {list(df.columns)}")
        print(f"   📊 Total records: {len(df):,}")
        
        # Show first few rows to understand structure
        print(f"\n   📋 FIRST 5 ROWS:")
        for i in range(min(5, len(df))):
            print(f"      Row {i+1}: {list(df.iloc[i])}")
        
        # Try to identify timestamp column
        timestamp_col = None
        for col in df.columns:
            if any(keyword in str(col).lower() for keyword in ['time', 'date', 'timestamp', 'update']):
                timestamp_col = col
                break
        
        if timestamp_col:
            print(f"\n   📅 TIMESTAMP ANALYSIS:")
            print(f"      Timestamp column: {timestamp_col}")
            
            # Convert to datetime
            df[timestamp_col] = pd.to_datetime(df[timestamp_col])
            
            start_date = df[timestamp_col].min()
            end_date = df[timestamp_col].max()
            total_days = (end_date - start_date).days + 1
            
            print(f"      Start date: {start_date}")
            print(f"      End date: {end_date}")
            print(f"      Total days: {total_days}")
            print(f"      Records per day: {len(df)/total_days:.1f}")
        
        # Try to identify energy columns
        energy_cols = []
        for col in df.columns:
            col_str = str(col).lower()
            if any(keyword in col_str for keyword in ['kwh', 'yield', 'energy', 'power', 'consumed', 'exported', 'imported']):
                energy_cols.append(col)
        
        if energy_cols:
            print(f"\n   ⚡ ENERGY COLUMNS FOUND:")
            for col in energy_cols:
                try:
                    values = pd.to_numeric(df[col], errors='coerce')
                    non_null_count = values.notna().sum()
                    if non_null_count > 0:
                        min_val = values.min()
                        max_val = values.max()
                        avg_val = values.mean()
                        print(f"      {col}:")
                        print(f"         Non-null records: {non_null_count:,}")
                        print(f"         Range: {min_val:.2f} to {max_val:.2f}")
                        print(f"         Average: {avg_val:.2f}")
                except:
                    print(f"      {col}: Cannot analyze (non-numeric)")
        
        # Check if this looks like daily data or cumulative
        if energy_cols and timestamp_col:
            print(f"\n   🔍 DATA TYPE ANALYSIS:")
            
            # Sort by timestamp
            df_sorted = df.sort_values(timestamp_col)
            
            # Check first energy column for patterns
            first_energy_col = energy_cols[0]
            values = pd.to_numeric(df_sorted[first_energy_col], errors='coerce').dropna()
            
            if len(values) > 10:
                # Check if values generally increase (cumulative) or fluctuate (daily)
                increasing_count = 0
                decreasing_count = 0
                
                for i in range(1, min(100, len(values))):  # Check first 100 values
                    if values.iloc[i] > values.iloc[i-1]:
                        increasing_count += 1
                    elif values.iloc[i] < values.iloc[i-1]:
                        decreasing_count += 1
                
                total_checks = increasing_count + decreasing_count
                if total_checks > 0:
                    increasing_pct = (increasing_count / total_checks) * 100
                    print(f"      Increasing values: {increasing_pct:.1f}%")
                    
                    if increasing_pct > 80:
                        print(f"      📈 LIKELY CUMULATIVE DATA")
                    elif increasing_pct < 20:
                        print(f"      📉 LIKELY DAILY RESET DATA")
                    else:
                        print(f"      📊 MIXED PATTERN DATA")
        
        # Try to determine which system this might be
        print(f"\n   🏠 SYSTEM IDENTIFICATION:")
        
        # Check filename patterns
        if '(1)' in filename:
            print(f"      Filename suggests: Possibly System 2 (has '(1)' suffix)")
        else:
            print(f"      Filename suggests: Possibly System 1 (no suffix)")
        
        # Check for any system identifiers in the data
        for col in df.columns:
            sample_values = df[col].dropna().head(10)
            for val in sample_values:
                val_str = str(val).upper()
                if any(keyword in val_str for keyword in ['SYSTEM', 'ΣΠΙΤΙ', 'ΠΑΝΩ', 'ΚΑΤΩ', 'SN', 'SERIAL']):
                    print(f"      Found identifier in {col}: {val}")
        
        return {
            'filepath': filepath,
            'filename': filename,
            'total_records': len(df),
            'columns': list(df.columns),
            'energy_columns': energy_cols,
            'timestamp_column': timestamp_col,
            'start_date': start_date if timestamp_col else None,
            'end_date': end_date if timestamp_col else None,
            'total_days': total_days if timestamp_col else None
        }
        
    except Exception as e:
        print(f"   ❌ Error analyzing file: {e}")
        return None

def main():
    """Αναλύει όλα τα αρχεία στο new2 directory"""
    
    print("📊 NEW2 DIRECTORY ANALYSIS")
    print("=" * 80)
    
    new2_dir = "/home/<USER>/solar-prediction-project/data/raw/new2"
    
    if not os.path.exists(new2_dir):
        print(f"❌ Directory not found: {new2_dir}")
        return
    
    files = [f for f in os.listdir(new2_dir) if f.endswith('.xlsx')]
    
    print(f"🔍 Found {len(files)} Excel files in new2 directory")
    
    results = []
    
    for filename in sorted(files):
        filepath = os.path.join(new2_dir, filename)
        result = analyze_new2_file(filepath)
        if result:
            results.append(result)
    
    # Summary comparison
    if len(results) >= 2:
        print(f"\n🔍 COMPARISON SUMMARY")
        print("=" * 80)
        
        for i, result in enumerate(results, 1):
            print(f"\n📁 FILE {i}: {result['filename']}")
            print(f"   Records: {result['total_records']:,}")
            if result['start_date'] and result['end_date']:
                print(f"   Period: {result['start_date'].date()} to {result['end_date'].date()}")
                print(f"   Days: {result['total_days']}")
            print(f"   Energy columns: {len(result['energy_columns'])}")
        
        print(f"\n🎯 RECOMMENDATIONS:")
        print(f"   1. Both files cover similar recent period (2025-02-26 to 2025-06-25)")
        print(f"   2. These likely represent the two systems")
        print(f"   3. File without '(1)' suffix is probably System 1")
        print(f"   4. File with '(1)' suffix is probably System 2")
        print(f"   5. These files can fill the gap from June 1-25, 2025")
        
        print(f"\n📋 NEXT STEPS:")
        print(f"   1. Confirm system identification")
        print(f"   2. Process these files with daily difference calculation")
        print(f"   3. Integrate with existing Excel data analysis")
        print(f"   4. Complete the full 15-month dataset")
    
    print(f"\n🎉 NEW2 ANALYSIS COMPLETED!")

if __name__ == "__main__":
    main()
