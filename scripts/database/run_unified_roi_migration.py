#!/usr/bin/env python3
"""
Unified ROI Migration Runner
Executes the database migration for the unified ROI system
Date: June 2025
"""

import os
import sys
import psycopg2
from datetime import datetime
from dotenv import load_dotenv

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

def load_database_config():
    """Load database configuration from environment"""
    load_dotenv()
    
    return {
        'host': os.getenv('DB_HOST', 'localhost'),
        'database': os.getenv('DB_NAME', 'solar_prediction'),
        'user': os.getenv('DB_USER', 'postgres'),
        'password': os.getenv('DB_PASSWORD', 'postgres'),
        'port': os.getenv('DB_PORT', '5432')
    }

def create_database_backup(conn, backup_name):
    """Create database backup before migration"""
    try:
        print(f"📦 Creating database backup: {backup_name}")
        
        # Use pg_dump to create backup
        db_config = load_database_config()
        backup_file = f"/tmp/{backup_name}.sql"
        
        os.system(f"pg_dump -h {db_config['host']} -p {db_config['port']} -U {db_config['user']} -d {db_config['database']} > {backup_file}")
        
        if os.path.exists(backup_file):
            print(f"✅ Backup created successfully: {backup_file}")
            return backup_file
        else:
            print("❌ Backup creation failed")
            return None
            
    except Exception as e:
        print(f"❌ Backup error: {e}")
        return None

def check_existing_tables(conn):
    """Check if migration tables already exist"""
    try:
        cur = conn.cursor()
        
        # Check for tariff_configs table
        cur.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'tariff_configs'
            );
        """)
        
        tariff_configs_exists = cur.fetchone()[0]
        
        # Check for billing columns in solax_data
        cur.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.columns 
                WHERE table_name = 'solax_data' 
                AND column_name = 'billing_tariff'
            );
        """)
        
        billing_columns_exist = cur.fetchone()[0]
        
        return {
            'tariff_configs_exists': tariff_configs_exists,
            'billing_columns_exist': billing_columns_exist
        }
        
    except Exception as e:
        print(f"❌ Error checking existing tables: {e}")
        return None

def execute_migration_sql(conn, sql_file_path):
    """Execute the migration SQL file"""
    try:
        print(f"🚀 Executing migration SQL: {sql_file_path}")
        
        with open(sql_file_path, 'r', encoding='utf-8') as file:
            sql_content = file.read()
        
        cur = conn.cursor()
        cur.execute(sql_content)
        conn.commit()
        
        print("✅ Migration SQL executed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Migration SQL execution failed: {e}")
        conn.rollback()
        return False

def validate_migration(conn):
    """Validate that migration completed successfully"""
    try:
        print("🔍 Validating migration...")
        
        cur = conn.cursor()
        
        # Check tariff_configs table
        cur.execute("SELECT COUNT(*) FROM tariff_configs;")
        tariff_count = cur.fetchone()[0]
        print(f"📊 Tariff configs created: {tariff_count}")
        
        # Check billing columns
        cur.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'solax_data' 
            AND column_name LIKE 'billing_%';
        """)
        billing_columns = [row[0] for row in cur.fetchall()]
        print(f"🔧 Billing columns added: {billing_columns}")
        
        # Check functions
        cur.execute("""
            SELECT routine_name 
            FROM information_schema.routines 
            WHERE routine_type = 'FUNCTION' 
            AND routine_name LIKE '%billing%';
        """)
        functions = [row[0] for row in cur.fetchall()]
        print(f"⚡ Functions created: {functions}")
        
        # Check triggers
        cur.execute("""
            SELECT trigger_name 
            FROM information_schema.triggers 
            WHERE trigger_name LIKE '%billing%';
        """)
        triggers = [row[0] for row in cur.fetchall()]
        print(f"🎯 Triggers created: {triggers}")
        
        # Validation criteria
        validation_passed = (
            tariff_count >= 10 and  # At least 10 default tariffs
            len(billing_columns) >= 4 and  # At least 4 billing columns
            len(functions) >= 3 and  # At least 3 billing functions
            len(triggers) >= 2  # At least 2 triggers
        )
        
        if validation_passed:
            print("✅ Migration validation passed!")
            return True
        else:
            print("❌ Migration validation failed!")
            return False
            
    except Exception as e:
        print(f"❌ Validation error: {e}")
        return False

def backfill_historical_data(conn, limit=1000):
    """Backfill billing data for recent historical records"""
    try:
        print(f"📈 Backfilling billing data for last {limit} records...")
        
        cur = conn.cursor()
        
        # Update solax_data
        cur.execute(f"""
            UPDATE solax_data 
            SET 
                billing_schedule = get_billing_schedule(timestamp),
                billing_period = get_billing_period(timestamp),
                billing_tariff = get_tariff_value('system1', timestamp, 'energy', get_billing_schedule(timestamp)),
                billing_network_charge = get_tariff_value('system1', timestamp, 'networkcharge', 'tier1'),
                billing_etmear = get_tariff_value('system1', timestamp, 'etmear')
            WHERE billing_tariff IS NULL
            AND id IN (
                SELECT id FROM solax_data 
                WHERE billing_tariff IS NULL 
                ORDER BY timestamp DESC 
                LIMIT {limit}
            );
        """)
        
        updated_solax_data = cur.rowcount
        
        # Update solax_data2
        cur.execute(f"""
            UPDATE solax_data2 
            SET 
                billing_schedule = get_billing_schedule(timestamp),
                billing_period = get_billing_period(timestamp),
                billing_tariff = get_tariff_value('system2', timestamp, 'energy', get_billing_schedule(timestamp)),
                billing_network_charge = get_tariff_value('system2', timestamp, 'networkcharge', 'tier1'),
                billing_etmear = get_tariff_value('system2', timestamp, 'etmear')
            WHERE billing_tariff IS NULL
            AND id IN (
                SELECT id FROM solax_data2 
                WHERE billing_tariff IS NULL 
                ORDER BY timestamp DESC 
                LIMIT {limit}
            );
        """)
        
        updated_solax_data2 = cur.rowcount
        
        conn.commit()
        
        print(f"✅ Backfilled {updated_solax_data} solax_data records")
        print(f"✅ Backfilled {updated_solax_data2} solax_data2 records")
        
        return True
        
    except Exception as e:
        print(f"❌ Backfill error: {e}")
        conn.rollback()
        return False

def main():
    """Main migration execution"""
    print("🌞 UNIFIED ROI SYSTEM MIGRATION")
    print("=" * 50)
    print(f"🕐 Started at: {datetime.now()}")
    print()
    
    # Load database configuration
    db_config = load_database_config()
    
    try:
        # Connect to database
        print("🔌 Connecting to database...")
        conn = psycopg2.connect(**db_config)
        print("✅ Database connection established")
        
        # Check existing state
        existing_state = check_existing_tables(conn)
        if existing_state:
            print(f"📊 Existing state:")
            print(f"   - tariff_configs table: {'EXISTS' if existing_state['tariff_configs_exists'] else 'NOT EXISTS'}")
            print(f"   - billing columns: {'EXISTS' if existing_state['billing_columns_exist'] else 'NOT EXISTS'}")
            
            if existing_state['tariff_configs_exists']:
                proceed = input("\n⚠️  Migration tables already exist. Continue anyway? (y/N): ").lower().strip()
                if proceed != 'y':
                    print("❌ Migration cancelled by user")
                    return False
        
        # Create backup
        backup_name = f"solar_prediction_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        backup_file = create_database_backup(conn, backup_name)
        
        if not backup_file:
            proceed = input("\n⚠️  Backup failed. Continue without backup? (y/N): ").lower().strip()
            if proceed != 'y':
                print("❌ Migration cancelled due to backup failure")
                return False
        
        # Execute migration
        migration_sql_path = os.path.join(os.path.dirname(__file__), 'unified_roi_migration.sql')
        
        if not os.path.exists(migration_sql_path):
            print(f"❌ Migration SQL file not found: {migration_sql_path}")
            return False
        
        success = execute_migration_sql(conn, migration_sql_path)
        if not success:
            print("❌ Migration failed")
            return False
        
        # Validate migration
        if not validate_migration(conn):
            print("❌ Migration validation failed")
            return False
        
        # Backfill historical data
        if not backfill_historical_data(conn, limit=5000):
            print("⚠️  Historical data backfill failed, but migration can continue")
        
        print()
        print("🎉 MIGRATION COMPLETED SUCCESSFULLY!")
        print("=" * 50)
        print("✅ Database schema updated")
        print("✅ Versioned tariff system created")
        print("✅ Billing fields added to production tables")
        print("✅ Automatic billing population enabled")
        print("✅ Audit trail system active")
        print()
        print("🎯 Next steps:")
        print("   1. Implement UnifiedROICalculator class")
        print("   2. Update Enhanced Billing System")
        print("   3. Test ROI calculations")
        print()
        
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        return False
        
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
