#!/usr/bin/env python3
"""
Standardized Billing API
Uses pre-calculated billing fields from database for consistent results
"""

import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime, date
from typing import Dict, Optional
import logging

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 5433,
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

logger = logging.getLogger(__name__)

class StandardizedBillingService:
    """Standardized billing service using pre-calculated fields"""
    
    def __init__(self):
        self.db_config = DB_CONFIG
    
    def get_db_connection(self):
        """Get database connection"""
        return psycopg2.connect(**self.db_config)
    
    def get_daily_cost(self, system_id: str, target_date: date) -> Dict:
        """Get daily cost using pre-calculated billing fields"""
        try:
            conn = self.get_db_connection()
            cur = conn.cursor(cursor_factory=RealDictCursor)
            
            # Get table name
            table_name = 'solax_data' if system_id in ['system1', '1'] else 'solax_data2'
            
            # Get daily totals from pre-calculated fields
            cur.execute(f"""
                SELECT 
                    SUM(COALESCE(billing_cost, 0)) as total_cost,
                    SUM(COALESCE(billing_benefit, 0)) as total_benefit,
                    SUM(COALESCE(billing_net_metering_credit, 0)) as total_credit,
                    MAX(yield_today) as daily_production,
                    COUNT(*) as records,
                    AVG(COALESCE(billing_tariff, 0.142)) as avg_tariff,
                    AVG(COALESCE(billing_network_charge, 0.0069)) as avg_network,
                    AVG(COALESCE(billing_etmear, 0.017)) as avg_etmear
                FROM {table_name}
                WHERE DATE(timestamp) = %s
            """, (target_date,))
            
            result = cur.fetchone()
            
            if result and result['records'] > 0:
                total_cost = float(result['total_cost'] or 0)
                total_benefit = float(result['total_benefit'] or 0)
                total_credit = float(result['total_credit'] or 0)
                production = float(result['daily_production'] or 0)
                
                # Net cost calculation
                net_cost = total_cost - total_benefit - total_credit
                
                # Estimate consumption (simplified)
                if system_id in ['system1', '1']:
                    self_consumption_rate = 0.405  # 40.5% for System 1
                else:
                    self_consumption_rate = 0.47   # 47% for System 2
                
                estimated_consumption = production * self_consumption_rate
                
                conn.close()
                
                return {
                    "status": "success",
                    "system_id": system_id,
                    "date": str(target_date),
                    "cost_breakdown": {
                        "total_cost": round(total_cost, 4),
                        "total_benefit": round(total_benefit, 4),
                        "total_credit": round(total_credit, 4),
                        "net_cost": round(net_cost, 4)
                    },
                    "energy_data": {
                        "production": round(production, 2),
                        "estimated_consumption": round(estimated_consumption, 2),
                        "estimated_surplus": round(max(0, production - estimated_consumption), 2)
                    },
                    "tariff_info": {
                        "avg_energy_rate": round(float(result['avg_tariff'] or 0.142), 4),
                        "avg_network_rate": round(float(result['avg_network'] or 0.0069), 4),
                        "avg_etmear_rate": round(float(result['avg_etmear'] or 0.017), 4)
                    },
                    "records_processed": result['records'],
                    "calculation_method": "pre_calculated_billing_fields"
                }
            else:
                return {
                    "status": "no_data",
                    "system_id": system_id,
                    "date": str(target_date),
                    "message": "No data found for the specified date"
                }
                
        except Exception as e:
            logger.error(f"Error calculating daily cost: {e}")
            return {
                "status": "error",
                "system_id": system_id,
                "date": str(target_date),
                "error": str(e)
            }
    
    def get_roi_calculation(self, system_id: str, investment_cost: float = 12500.0) -> Dict:
        """Get ROI calculation using pre-calculated billing fields"""
        try:
            conn = self.get_db_connection()
            cur = conn.cursor(cursor_factory=RealDictCursor)
            
            # Get table name
            table_name = 'solax_data' if system_id in ['system1', '1'] else 'solax_data2'
            
            # Get lifetime totals from pre-calculated fields
            cur.execute(f"""
                SELECT 
                    SUM(COALESCE(billing_benefit, 0)) as total_benefit,
                    SUM(COALESCE(billing_cost, 0)) as total_cost,
                    COUNT(DISTINCT DATE(timestamp)) as operational_days,
                    MIN(timestamp) as start_date,
                    MAX(timestamp) as end_date,
                    SUM(CASE WHEN yield_today > 0 THEN yield_today ELSE 0 END) as total_production
                FROM {table_name}
                WHERE timestamp >= '2024-01-01'
            """)
            
            result = cur.fetchone()
            
            if result and result['operational_days'] > 0:
                total_benefit = float(result['total_benefit'] or 0)
                total_cost = float(result['total_cost'] or 0)
                operational_days = result['operational_days']
                total_production = float(result['total_production'] or 0)
                
                # Calculate annual metrics
                annual_benefit = (total_benefit / operational_days) * 365
                annual_cost = (total_cost / operational_days) * 365
                net_annual_benefit = annual_benefit - annual_cost
                
                # ROI calculation
                annual_roi_percent = (net_annual_benefit / investment_cost) * 100 if investment_cost > 0 else 0
                payback_years = investment_cost / net_annual_benefit if net_annual_benefit > 0 else None
                
                # Production metrics
                annual_production = (total_production / operational_days) * 365
                
                conn.close()
                
                return {
                    "status": "success",
                    "system_id": system_id,
                    "investment_cost_eur": investment_cost,
                    "operational_period": {
                        "start_date": str(result['start_date']),
                        "end_date": str(result['end_date']),
                        "days": operational_days
                    },
                    "financial": {
                        "annual_benefit_eur": round(annual_benefit, 2),
                        "annual_cost_eur": round(annual_cost, 2),
                        "net_annual_benefit_eur": round(net_annual_benefit, 2),
                        "annual_roi_percent": round(annual_roi_percent, 2),
                        "payback_years": round(payback_years, 1) if payback_years else None
                    },
                    "production": {
                        "annual_production_kwh": round(annual_production, 2),
                        "total_production_kwh": round(total_production, 2)
                    },
                    "calculation_method": "pre_calculated_billing_fields",
                    "timestamp": datetime.now().isoformat()
                }
            else:
                return {
                    "status": "no_data",
                    "system_id": system_id,
                    "message": "Insufficient data for ROI calculation"
                }
                
        except Exception as e:
            logger.error(f"Error calculating ROI: {e}")
            return {
                "status": "error",
                "system_id": system_id,
                "error": str(e)
            }

# Global instance
standardized_billing_service = StandardizedBillingService()

def get_daily_cost_standardized(system_id: str, target_date: date = None) -> Dict:
    """Get daily cost using standardized method"""
    if target_date is None:
        target_date = date.today()
    return standardized_billing_service.get_daily_cost(system_id, target_date)

def get_roi_standardized(system_id: str, investment_cost: float = 12500.0) -> Dict:
    """Get ROI using standardized method"""
    return standardized_billing_service.get_roi_calculation(system_id, investment_cost)

if __name__ == "__main__":
    # Test the standardized API
    print("Testing Standardized Billing API...")
    
    # Test daily cost
    today_cost = get_daily_cost_standardized('system1')
    print(f"Today's cost: {today_cost}")
    
    # Test ROI
    roi_result = get_roi_standardized('system1')
    print(f"ROI result: {roi_result}")
