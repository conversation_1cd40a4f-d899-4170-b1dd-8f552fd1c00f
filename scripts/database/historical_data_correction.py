#!/usr/bin/env python3
"""
Historical Data Correction
Fix billing fields for historical data using corrected logic
"""

import psycopg2
import pandas as pd
from datetime import datetime, date, timedelta
import sys
import os
import time

# Add project root to path
sys.path.append('/home/<USER>/solar-prediction-project')

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 5433,
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

def get_db_connection():
    """Get database connection"""
    try:
        return psycopg2.connect(**DB_CONFIG)
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return None

def correct_historical_billing_data():
    """Correct historical billing data in batches"""
    print("\n" + "="*60)
    print("🔧 HISTORICAL DATA CORRECTION")
    print("="*60)
    
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cur = conn.cursor()
        
        # Step 1: Fix System 1 billing fields using simplified logic
        print("\n📊 Step 1: Correcting System 1 billing fields...")
        
        # Update billing_schedule first
        print("   Updating billing_schedule...")
        cur.execute("""
            UPDATE solax_data SET
                billing_schedule = CASE 
                    WHEN EXTRACT(MONTH FROM timestamp) IN (11,12,1,2,3) THEN 'winter'
                    ELSE 'summer'
                END || '_' || CASE 
                    WHEN (EXTRACT(MONTH FROM timestamp) IN (11,12,1,2,3) AND 
                          (EXTRACT(HOUR FROM timestamp) BETWEEN 2 AND 4 OR EXTRACT(HOUR FROM timestamp) BETWEEN 12 AND 14)) OR
                         (EXTRACT(MONTH FROM timestamp) NOT IN (11,12,1,2,3) AND 
                          (EXTRACT(HOUR FROM timestamp) BETWEEN 2 AND 3 OR EXTRACT(HOUR FROM timestamp) BETWEEN 11 AND 14))
                    THEN 'night'
                    ELSE 'day'
                END
            WHERE timestamp >= '2024-06-01' AND billing_schedule IS NULL
        """)
        schedule_updated = cur.rowcount
        print(f"   ✅ Updated {schedule_updated:,} billing_schedule records")
        
        # Update tariff rates
        print("   Updating tariff rates...")
        cur.execute("""
            UPDATE solax_data SET
                billing_tariff = CASE 
                    WHEN billing_schedule LIKE '%_night' THEN 
                        CASE WHEN billing_schedule LIKE 'winter_%' THEN 0.120 ELSE 0.132 END
                    ELSE 0.142
                END,
                billing_network_charge = 0.0069,
                billing_etmear = 0.017
            WHERE timestamp >= '2024-06-01' AND billing_tariff IS NULL
        """)
        tariff_updated = cur.rowcount
        print(f"   ✅ Updated {tariff_updated:,} tariff records")
        
        # Fix billing_cost using existing grid_usage_kwh (which is more reliable)
        print("   Fixing billing_cost...")
        cur.execute("""
            UPDATE solax_data SET
                billing_cost = CASE 
                    WHEN COALESCE(grid_usage_kwh, 0) > 0 THEN 
                        grid_usage_kwh * (
                            COALESCE(billing_tariff, 0.142) + 
                            COALESCE(billing_network_charge, 0.0069) + 
                            COALESCE(billing_etmear, 0.017)
                        ) * 1.24
                    ELSE 0.000
                END
            WHERE timestamp >= '2024-06-01' AND billing_cost > 100
        """)
        cost_updated = cur.rowcount
        print(f"   ✅ Fixed {cost_updated:,} billing_cost records (removed huge values)")
        
        # Fix billing_benefit using realistic self-consumption calculation
        print("   Fixing billing_benefit...")
        cur.execute("""
            UPDATE solax_data SET
                billing_benefit = CASE 
                    WHEN COALESCE(yield_today, 0) > 0 THEN 
                        -- Use 40.5% self-consumption rate for System 1
                        -- Approximate hourly from daily yield
                        GREATEST(0, yield_today * 0.405 * (
                            COALESCE(billing_tariff, 0.142) + 
                            COALESCE(billing_network_charge, 0.0069) + 
                            COALESCE(billing_etmear, 0.017)
                        ) / 288)  -- 288 = 24h * 12 (5min intervals)
                    ELSE 0.000
                END
            WHERE timestamp >= '2024-06-01' AND billing_benefit > 10
        """)
        benefit_updated = cur.rowcount
        print(f"   ✅ Fixed {benefit_updated:,} billing_benefit records")
        
        # Set net metering credit to 0 (Greek Net Metering)
        print("   Setting net metering credit...")
        cur.execute("""
            UPDATE solax_data SET
                billing_net_metering_credit = 0.000
            WHERE timestamp >= '2024-06-01'
        """)
        credit_updated = cur.rowcount
        print(f"   ✅ Updated {credit_updated:,} net metering credit records")
        
        conn.commit()
        print("   ✅ System 1 corrections committed")
        
        # Step 2: Fix System 2 billing fields
        print("\n📊 Step 2: Correcting System 2 billing fields...")
        
        # Fill missing billing fields for System 2
        cur.execute("""
            UPDATE solax_data2 SET
                billing_tariff = CASE 
                    WHEN EXTRACT(HOUR FROM timestamp) BETWEEN 2 AND 3 OR 
                         EXTRACT(HOUR FROM timestamp) BETWEEN 11 AND 14 THEN 0.132  -- Summer night
                    ELSE 0.142  -- Day rate
                END,
                billing_network_charge = 0.0069,
                billing_etmear = 0.017,
                billing_schedule = CASE 
                    WHEN EXTRACT(MONTH FROM timestamp) IN (11,12,1,2,3) THEN 'winter'
                    ELSE 'summer'
                END || '_' || CASE 
                    WHEN EXTRACT(HOUR FROM timestamp) BETWEEN 2 AND 3 OR 
                         EXTRACT(HOUR FROM timestamp) BETWEEN 11 AND 14 THEN 'night'
                    ELSE 'day'
                END,
                billing_cost = 0.000,  -- Will be calculated by triggers for new records
                billing_benefit = CASE 
                    WHEN COALESCE(yield_today, 0) > 0 THEN 
                        -- Use 47% self-consumption rate for System 2
                        GREATEST(0, yield_today * 0.47 * 0.1659 / 288)
                    ELSE 0.000
                END,
                billing_net_metering_credit = 0.000
            WHERE billing_tariff IS NULL
        """)
        system2_updated = cur.rowcount
        print(f"   ✅ Updated {system2_updated:,} System 2 records")
        
        conn.commit()
        print("   ✅ System 2 corrections committed")
        
        # Step 3: Verification
        print("\n✅ Step 3: Verification of corrections...")
        
        # Check System 1
        cur.execute("""
            SELECT 
                'System 1' as system,
                COUNT(*) as total_records,
                COUNT(billing_cost) as has_cost,
                COUNT(billing_benefit) as has_benefit,
                COUNT(billing_schedule) as has_schedule,
                ROUND(AVG(COALESCE(billing_cost, 0))::numeric, 6) as avg_cost,
                ROUND(AVG(COALESCE(billing_benefit, 0))::numeric, 6) as avg_benefit,
                ROUND(MAX(COALESCE(billing_cost, 0))::numeric, 4) as max_cost,
                ROUND(MAX(COALESCE(billing_benefit, 0))::numeric, 4) as max_benefit
            FROM solax_data 
            WHERE timestamp >= '2024-06-01'
        """)
        
        system1_stats = cur.fetchone()
        print("   System 1 verification:")
        print(f"      Total records: {system1_stats[1]:,}")
        print(f"      Has billing_cost: {system1_stats[2]:,} ({system1_stats[2]/system1_stats[1]*100:.1f}%)")
        print(f"      Has billing_benefit: {system1_stats[3]:,} ({system1_stats[3]/system1_stats[1]*100:.1f}%)")
        print(f"      Has billing_schedule: {system1_stats[4]:,} ({system1_stats[4]/system1_stats[1]*100:.1f}%)")
        print(f"      Avg cost: €{system1_stats[5]}")
        print(f"      Avg benefit: €{system1_stats[6]}")
        print(f"      Max cost: €{system1_stats[7]}")
        print(f"      Max benefit: €{system1_stats[8]}")
        
        # Check System 2
        cur.execute("""
            SELECT 
                'System 2' as system,
                COUNT(*) as total_records,
                COUNT(billing_cost) as has_cost,
                COUNT(billing_benefit) as has_benefit,
                COUNT(billing_schedule) as has_schedule,
                ROUND(AVG(COALESCE(billing_cost, 0))::numeric, 6) as avg_cost,
                ROUND(AVG(COALESCE(billing_benefit, 0))::numeric, 6) as avg_benefit
            FROM solax_data2 
            WHERE timestamp >= '2024-06-01'
        """)
        
        system2_stats = cur.fetchone()
        print("   System 2 verification:")
        print(f"      Total records: {system2_stats[1]:,}")
        print(f"      Has billing_cost: {system2_stats[2]:,} ({system2_stats[2]/system2_stats[1]*100:.1f}%)")
        print(f"      Has billing_benefit: {system2_stats[3]:,} ({system2_stats[3]/system2_stats[1]*100:.1f}%)")
        print(f"      Has billing_schedule: {system2_stats[4]:,} ({system2_stats[4]/system2_stats[1]*100:.1f}%)")
        print(f"      Avg cost: €{system2_stats[5]}")
        print(f"      Avg benefit: €{system2_stats[6]}")
        
        # Sample today's data
        print("\n📊 Sample corrected data (today):")
        cur.execute("""
            SELECT 
                'System 1' as system,
                ROUND(SUM(COALESCE(billing_cost, 0))::numeric, 4) as total_cost,
                ROUND(SUM(COALESCE(billing_benefit, 0))::numeric, 4) as total_benefit,
                MAX(yield_today) as production,
                COUNT(*) as records
            FROM solax_data 
            WHERE DATE(timestamp) = CURRENT_DATE
            
            UNION ALL
            
            SELECT 
                'System 2' as system,
                ROUND(SUM(COALESCE(billing_cost, 0))::numeric, 4) as total_cost,
                ROUND(SUM(COALESCE(billing_benefit, 0))::numeric, 4) as total_benefit,
                MAX(COALESCE(yield_today, 0)) as production,
                COUNT(*) as records
            FROM solax_data2 
            WHERE DATE(timestamp) = CURRENT_DATE
        """)
        
        today_data = cur.fetchall()
        for system, cost, benefit, production, records in today_data:
            print(f"   {system}: Cost €{cost}, Benefit €{benefit}, Production {production:.1f} kWh, Records {records}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        if conn:
            conn.rollback()

def main():
    """Main correction function"""
    correct_historical_billing_data()
    
    print("\n" + "="*60)
    print("🎯 HISTORICAL DATA CORRECTION ΟΛΟΚΛΗΡΩΘΗΚΕ")
    print("="*60)
    print("✅ Billing fields corrected for both systems")
    print("✅ Unrealistic values removed")
    print("✅ Proper tariff rates applied")
    print("✅ Future records will use corrected functions")

if __name__ == "__main__":
    main()
