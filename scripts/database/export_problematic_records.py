#!/usr/bin/env python3
"""
Export Problematic Records for Analysis
Export records from 24/6/2025 that cause prediction errors
"""

import psycopg2
from psycopg2.extras import RealDictCursor
import csv
import json
from datetime import datetime, date
import os

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 5433,
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

def get_db_connection():
    """Get database connection"""
    try:
        return psycopg2.connect(**DB_CONFIG)
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return None

def export_daily_records():
    """Export all records from 24/6/2025 for analysis"""
    print("\n" + "="*60)
    print("📊 EXPORTING DAILY RECORDS (24/6/2025)")
    print("="*60)
    
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        
        target_date = date(2025, 6, 24)
        export_dir = "data/exports"
        os.makedirs(export_dir, exist_ok=True)
        
        # Export System 1 records
        print(f"\n📋 Exporting System 1 records...")
        cur.execute("""
            SELECT
                id,
                timestamp,
                inverter_sn,
                wifi_sn,
                yield_today,
                yield_total,
                ac_power,
                bat_power,
                soc,
                grid_usage_kwh,
                consume_energy,
                billing_cost,
                billing_benefit,
                billing_net_metering_credit,
                billing_tariff,
                billing_network_charge,
                billing_etmear,
                billing_schedule
            FROM solax_data
            WHERE DATE(timestamp) = %s
            ORDER BY timestamp
        """, (target_date,))
        
        system1_records = cur.fetchall()
        
        # Write System 1 CSV
        system1_file = f"{export_dir}/system1_records_20250624.csv"
        with open(system1_file, 'w', newline='', encoding='utf-8') as csvfile:
            if system1_records:
                fieldnames = system1_records[0].keys()
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                for record in system1_records:
                    # Convert datetime and decimal objects to strings
                    row = {}
                    for key, value in record.items():
                        if value is None:
                            row[key] = ''
                        elif isinstance(value, datetime):
                            row[key] = value.isoformat()
                        else:
                            row[key] = str(value)
                    writer.writerow(row)
        
        print(f"   ✅ System 1: {len(system1_records):,} records exported to {system1_file}")
        
        # Export System 2 records
        print(f"\n📋 Exporting System 2 records...")
        cur.execute("""
            SELECT
                id,
                timestamp,
                inverter_sn,
                wifi_sn,
                yield_today,
                yield_total,
                ac_power,
                bat_power,
                soc,
                grid_usage_kwh,
                consume_energy,
                billing_cost,
                billing_benefit,
                billing_net_metering_credit,
                billing_tariff,
                billing_network_charge,
                billing_etmear,
                billing_schedule
            FROM solax_data2
            WHERE DATE(timestamp) = %s
            ORDER BY timestamp
        """, (target_date,))
        
        system2_records = cur.fetchall()
        
        # Write System 2 CSV
        system2_file = f"{export_dir}/system2_records_20250624.csv"
        with open(system2_file, 'w', newline='', encoding='utf-8') as csvfile:
            if system2_records:
                fieldnames = system2_records[0].keys()
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                for record in system2_records:
                    # Convert datetime and decimal objects to strings
                    row = {}
                    for key, value in record.items():
                        if value is None:
                            row[key] = ''
                        elif isinstance(value, datetime):
                            row[key] = value.isoformat()
                        else:
                            row[key] = str(value)
                    writer.writerow(row)
        
        print(f"   ✅ System 2: {len(system2_records):,} records exported to {system2_file}")
        
        conn.close()
        return system1_records, system2_records, export_dir
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None, None, None

def export_problematic_patterns():
    """Export specific problematic patterns"""
    print("\n" + "="*60)
    print("🚨 EXPORTING PROBLEMATIC PATTERNS")
    print("="*60)
    
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        
        export_dir = "data/exports"
        target_date = date(2025, 6, 24)
        
        # 1. Export records with unrealistic billing values
        print(f"\n🔍 Finding unrealistic billing values...")
        cur.execute("""
            SELECT 
                'System 1' as system,
                timestamp,
                yield_today,
                ac_power,
                soc,
                grid_usage_kwh,
                consume_energy,
                billing_cost,
                billing_benefit,
                billing_cost + billing_benefit as total_billing,
                CASE 
                    WHEN billing_cost > 1 THEN 'High Cost'
                    WHEN billing_benefit > 10 THEN 'High Benefit'
                    WHEN billing_cost + billing_benefit > 5 THEN 'High Total'
                    ELSE 'Normal'
                END as issue_type
            FROM solax_data 
            WHERE DATE(timestamp) = %s
            AND (billing_cost > 1 OR billing_benefit > 10 OR billing_cost + billing_benefit > 5)
            
            UNION ALL
            
            SELECT 
                'System 2' as system,
                timestamp,
                yield_today,
                ac_power,
                soc,
                grid_usage_kwh,
                consume_energy,
                billing_cost,
                billing_benefit,
                billing_cost + billing_benefit as total_billing,
                CASE 
                    WHEN billing_cost > 1 THEN 'High Cost'
                    WHEN billing_benefit > 10 THEN 'High Benefit'
                    WHEN billing_cost + billing_benefit > 5 THEN 'High Total'
                    ELSE 'Normal'
                END as issue_type
            FROM solax_data2 
            WHERE DATE(timestamp) = %s
            AND (billing_cost > 1 OR billing_benefit > 10 OR billing_cost + billing_benefit > 5)
            ORDER BY total_billing DESC
        """, (target_date, target_date))
        
        problematic_billing = cur.fetchall()
        
        # Write problematic billing CSV
        billing_file = f"{export_dir}/problematic_billing_20250624.csv"
        with open(billing_file, 'w', newline='', encoding='utf-8') as csvfile:
            if problematic_billing:
                fieldnames = problematic_billing[0].keys()
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                for record in problematic_billing:
                    row = {}
                    for key, value in record.items():
                        if value is None:
                            row[key] = ''
                        elif isinstance(value, datetime):
                            row[key] = value.isoformat()
                        else:
                            row[key] = str(value)
                    writer.writerow(row)
        
        print(f"   ✅ Problematic billing: {len(problematic_billing):,} records exported to {billing_file}")
        
        # 2. Export yield anomalies
        print(f"\n🔍 Finding yield anomalies...")
        cur.execute("""
            SELECT 
                'System 1' as system,
                timestamp,
                yield_today,
                LAG(yield_today) OVER (ORDER BY timestamp) as prev_yield,
                yield_today - LAG(yield_today) OVER (ORDER BY timestamp) as yield_diff,
                ac_power,
                bat_power,
                soc,
                CASE 
                    WHEN yield_today < LAG(yield_today) OVER (ORDER BY timestamp) THEN 'Reset'
                    WHEN yield_today - LAG(yield_today) OVER (ORDER BY timestamp) > 5 THEN 'High Jump'
                    WHEN yield_today - LAG(yield_today) OVER (ORDER BY timestamp) < 0 THEN 'Negative'
                    ELSE 'Normal'
                END as anomaly_type
            FROM solax_data 
            WHERE DATE(timestamp) = %s
            
            UNION ALL
            
            SELECT 
                'System 2' as system,
                timestamp,
                yield_today,
                LAG(yield_today) OVER (ORDER BY timestamp) as prev_yield,
                yield_today - LAG(yield_today) OVER (ORDER BY timestamp) as yield_diff,
                ac_power,
                bat_power,
                soc,
                CASE 
                    WHEN yield_today < LAG(yield_today) OVER (ORDER BY timestamp) THEN 'Reset'
                    WHEN yield_today - LAG(yield_today) OVER (ORDER BY timestamp) > 5 THEN 'High Jump'
                    WHEN yield_today - LAG(yield_today) OVER (ORDER BY timestamp) < 0 THEN 'Negative'
                    ELSE 'Normal'
                END as anomaly_type
            FROM solax_data2 
            WHERE DATE(timestamp) = %s
            ORDER BY system, timestamp
        """, (target_date, target_date))
        
        yield_anomalies = cur.fetchall()
        
        # Filter only anomalies
        actual_anomalies = [r for r in yield_anomalies if r['anomaly_type'] != 'Normal']
        
        # Write yield anomalies CSV
        yield_file = f"{export_dir}/yield_anomalies_20250624.csv"
        with open(yield_file, 'w', newline='', encoding='utf-8') as csvfile:
            if actual_anomalies:
                fieldnames = actual_anomalies[0].keys()
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                for record in actual_anomalies:
                    row = {}
                    for key, value in record.items():
                        if value is None:
                            row[key] = ''
                        elif isinstance(value, datetime):
                            row[key] = value.isoformat()
                        else:
                            row[key] = str(value)
                    writer.writerow(row)
        
        print(f"   ✅ Yield anomalies: {len(actual_anomalies):,} records exported to {yield_file}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

def create_analysis_summary():
    """Create analysis summary with statistics"""
    print("\n" + "="*60)
    print("📈 CREATING ANALYSIS SUMMARY")
    print("="*60)
    
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        
        export_dir = "data/exports"
        target_date = date(2025, 6, 24)
        
        # Collect statistics
        summary = {
            "export_date": datetime.now().isoformat(),
            "target_date": target_date.isoformat(),
            "systems": {}
        }
        
        for table, system_name in [('solax_data', 'System 1'), ('solax_data2', 'System 2')]:
            print(f"\n📊 Analyzing {system_name}...")
            
            # Basic statistics
            cur.execute(f"""
                SELECT 
                    COUNT(*) as total_records,
                    MIN(timestamp) as first_record,
                    MAX(timestamp) as last_record,
                    MIN(yield_today) as min_yield,
                    MAX(yield_today) as max_yield,
                    MAX(yield_today) - MIN(yield_today) as daily_production,
                    AVG(ac_power) as avg_ac_power,
                    MAX(ac_power) as max_ac_power,
                    AVG(soc) as avg_soc,
                    COUNT(CASE WHEN billing_cost > 1 THEN 1 END) as high_cost_records,
                    COUNT(CASE WHEN billing_benefit > 10 THEN 1 END) as high_benefit_records,
                    AVG(COALESCE(billing_cost, 0)) as avg_billing_cost,
                    AVG(COALESCE(billing_benefit, 0)) as avg_billing_benefit
                FROM {table}
                WHERE DATE(timestamp) = %s
            """, (target_date,))
            
            stats = cur.fetchone()
            
            system_data = {
                "total_records": stats['total_records'],
                "time_range": {
                    "first": stats['first_record'].isoformat() if stats['first_record'] else None,
                    "last": stats['last_record'].isoformat() if stats['last_record'] else None
                },
                "yield": {
                    "min": float(stats['min_yield'] or 0),
                    "max": float(stats['max_yield'] or 0),
                    "daily_production": float(stats['daily_production'] or 0)
                },
                "power": {
                    "avg_ac_power": float(stats['avg_ac_power'] or 0),
                    "max_ac_power": float(stats['max_ac_power'] or 0)
                },
                "battery": {
                    "avg_soc": float(stats['avg_soc'] or 0)
                },
                "billing": {
                    "high_cost_records": stats['high_cost_records'],
                    "high_benefit_records": stats['high_benefit_records'],
                    "avg_cost": float(stats['avg_billing_cost'] or 0),
                    "avg_benefit": float(stats['avg_billing_benefit'] or 0)
                }
            }
            
            summary["systems"][system_name] = system_data
            
            print(f"   Records: {stats['total_records']:,}")
            print(f"   Daily production: {stats['daily_production']:.2f} kWh")
            print(f"   Avg AC power: {stats['avg_ac_power']:.1f} W")
            print(f"   Problematic billing: {stats['high_cost_records'] + stats['high_benefit_records']} records")
        
        # Write summary JSON
        summary_file = f"{export_dir}/analysis_summary_20250624.json"
        with open(summary_file, 'w', encoding='utf-8') as jsonfile:
            json.dump(summary, jsonfile, indent=2, ensure_ascii=False)
        
        print(f"\n   ✅ Analysis summary exported to {summary_file}")
        
        # Create README
        readme_file = f"{export_dir}/README_20250624.md"
        with open(readme_file, 'w', encoding='utf-8') as readme:
            readme.write(f"""# Solar Data Export - 24/6/2025

## Files Exported

1. **system1_records_20250624.csv** - All System 1 records for 24/6/2025
2. **system2_records_20250624.csv** - All System 2 records for 24/6/2025
3. **problematic_billing_20250624.csv** - Records with unrealistic billing values
4. **yield_anomalies_20250624.csv** - Records with yield calculation anomalies
5. **analysis_summary_20250624.json** - Statistical summary of the data
6. **README_20250624.md** - This file

## Key Issues Identified

### System 1 (Σπίτι Πάνω)
- Total Records: {summary['systems']['System 1']['total_records']:,}
- Daily Production: {summary['systems']['System 1']['yield']['daily_production']:.2f} kWh
- Problematic Billing Records: {summary['systems']['System 1']['billing']['high_cost_records'] + summary['systems']['System 1']['billing']['high_benefit_records']}

### System 2 (Σπίτι Κάτω)
- Total Records: {summary['systems']['System 2']['total_records']:,}
- Daily Production: {summary['systems']['System 2']['yield']['daily_production']:.2f} kWh
- Problematic Billing Records: {summary['systems']['System 2']['billing']['high_cost_records'] + summary['systems']['System 2']['billing']['high_benefit_records']}

## Analysis Notes

1. **Billing Field Issues**: Some records show unrealistic billing costs/benefits
2. **Yield Calculation**: Daily yield calculated as max - min for the day
3. **Data Quality**: Check for missing values and anomalies in the CSV files
4. **Timestamp Issues**: Verify timestamp consistency and gaps

## Recommendations

1. Review problematic_billing_20250624.csv for billing calculation errors
2. Analyze yield_anomalies_20250624.csv for data collection issues
3. Check timestamp gaps in the main record files
4. Validate billing field calculations against manual calculations

Export Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
""")
        
        print(f"   ✅ README created: {readme_file}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Main export function"""
    print("📊 EXPORTING PROBLEMATIC RECORDS FOR ANALYSIS")
    print("=" * 60)
    print("Target Date: 24/6/2025")
    print(f"Export Time: {datetime.now()}")
    
    # Export all daily records
    system1_records, system2_records, export_dir = export_daily_records()
    
    if system1_records is not None and system2_records is not None:
        # Export problematic patterns
        export_problematic_patterns()
        
        # Create analysis summary
        create_analysis_summary()
        
        print("\n" + "="*60)
        print("🎯 EXPORT COMPLETED SUCCESSFULLY")
        print("="*60)
        print(f"📁 Export directory: {export_dir}")
        print(f"📊 System 1 records: {len(system1_records):,}")
        print(f"📊 System 2 records: {len(system2_records):,}")
        print("📋 Files created:")
        print("   - system1_records_20250624.csv")
        print("   - system2_records_20250624.csv") 
        print("   - problematic_billing_20250624.csv")
        print("   - yield_anomalies_20250624.csv")
        print("   - analysis_summary_20250624.json")
        print("   - README_20250624.md")
        print("\n🔍 Review the files to identify prediction error causes!")
    else:
        print("\n❌ Export failed!")

if __name__ == "__main__":
    main()
