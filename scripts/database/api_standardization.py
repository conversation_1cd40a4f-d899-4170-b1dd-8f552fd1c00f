#!/usr/bin/env python3
"""
API Standardization
Update APIs to use pre-calculated billing fields instead of manual calculations
"""

import os
import sys
import shutil
from datetime import datetime

# Add project root to path
sys.path.append('/home/<USER>/solar-prediction-project')

def backup_api_files():
    """Backup API files before modification"""
    print("\n" + "="*60)
    print("💾 BACKING UP API FILES")
    print("="*60)
    
    backup_dir = f"backups/api_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    os.makedirs(backup_dir, exist_ok=True)
    
    files_to_backup = [
        'enhanced_billing_service.py',
        'scripts/frontend_system/enhanced_billing_system.py',
        'scripts/frontend_system/unified_roi_calculator.py'
    ]
    
    for file_path in files_to_backup:
        if os.path.exists(file_path):
            backup_path = os.path.join(backup_dir, os.path.basename(file_path))
            shutil.copy2(file_path, backup_path)
            print(f"   ✅ Backed up {file_path} → {backup_path}")
        else:
            print(f"   ⚠️ File not found: {file_path}")
    
    print(f"   📁 Backup directory: {backup_dir}")
    return backup_dir

def create_standardized_billing_api():
    """Create standardized billing API that uses pre-calculated fields"""
    print("\n" + "="*60)
    print("🔧 CREATING STANDARDIZED BILLING API")
    print("="*60)
    
    api_content = '''#!/usr/bin/env python3
"""
Standardized Billing API
Uses pre-calculated billing fields from database for consistent results
"""

import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime, date
from typing import Dict, Optional
import logging

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 5433,
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

logger = logging.getLogger(__name__)

class StandardizedBillingService:
    """Standardized billing service using pre-calculated fields"""
    
    def __init__(self):
        self.db_config = DB_CONFIG
    
    def get_db_connection(self):
        """Get database connection"""
        return psycopg2.connect(**self.db_config)
    
    def get_daily_cost(self, system_id: str, target_date: date) -> Dict:
        """Get daily cost using pre-calculated billing fields"""
        try:
            conn = self.get_db_connection()
            cur = conn.cursor(cursor_factory=RealDictCursor)
            
            # Get table name
            table_name = 'solax_data' if system_id in ['system1', '1'] else 'solax_data2'
            
            # Get daily totals from pre-calculated fields
            cur.execute(f"""
                SELECT 
                    SUM(COALESCE(billing_cost, 0)) as total_cost,
                    SUM(COALESCE(billing_benefit, 0)) as total_benefit,
                    SUM(COALESCE(billing_net_metering_credit, 0)) as total_credit,
                    MAX(yield_today) as daily_production,
                    COUNT(*) as records,
                    AVG(COALESCE(billing_tariff, 0.142)) as avg_tariff,
                    AVG(COALESCE(billing_network_charge, 0.0069)) as avg_network,
                    AVG(COALESCE(billing_etmear, 0.017)) as avg_etmear
                FROM {table_name}
                WHERE DATE(timestamp) = %s
            """, (target_date,))
            
            result = cur.fetchone()
            
            if result and result['records'] > 0:
                total_cost = float(result['total_cost'] or 0)
                total_benefit = float(result['total_benefit'] or 0)
                total_credit = float(result['total_credit'] or 0)
                production = float(result['daily_production'] or 0)
                
                # Net cost calculation
                net_cost = total_cost - total_benefit - total_credit
                
                # Estimate consumption (simplified)
                if system_id in ['system1', '1']:
                    self_consumption_rate = 0.405  # 40.5% for System 1
                else:
                    self_consumption_rate = 0.47   # 47% for System 2
                
                estimated_consumption = production * self_consumption_rate
                
                conn.close()
                
                return {
                    "status": "success",
                    "system_id": system_id,
                    "date": str(target_date),
                    "cost_breakdown": {
                        "total_cost": round(total_cost, 4),
                        "total_benefit": round(total_benefit, 4),
                        "total_credit": round(total_credit, 4),
                        "net_cost": round(net_cost, 4)
                    },
                    "energy_data": {
                        "production": round(production, 2),
                        "estimated_consumption": round(estimated_consumption, 2),
                        "estimated_surplus": round(max(0, production - estimated_consumption), 2)
                    },
                    "tariff_info": {
                        "avg_energy_rate": round(float(result['avg_tariff'] or 0.142), 4),
                        "avg_network_rate": round(float(result['avg_network'] or 0.0069), 4),
                        "avg_etmear_rate": round(float(result['avg_etmear'] or 0.017), 4)
                    },
                    "records_processed": result['records'],
                    "calculation_method": "pre_calculated_billing_fields"
                }
            else:
                return {
                    "status": "no_data",
                    "system_id": system_id,
                    "date": str(target_date),
                    "message": "No data found for the specified date"
                }
                
        except Exception as e:
            logger.error(f"Error calculating daily cost: {e}")
            return {
                "status": "error",
                "system_id": system_id,
                "date": str(target_date),
                "error": str(e)
            }
    
    def get_roi_calculation(self, system_id: str, investment_cost: float = 12500.0) -> Dict:
        """Get ROI calculation using pre-calculated billing fields"""
        try:
            conn = self.get_db_connection()
            cur = conn.cursor(cursor_factory=RealDictCursor)
            
            # Get table name
            table_name = 'solax_data' if system_id in ['system1', '1'] else 'solax_data2'
            
            # Get lifetime totals from pre-calculated fields
            cur.execute(f"""
                SELECT 
                    SUM(COALESCE(billing_benefit, 0)) as total_benefit,
                    SUM(COALESCE(billing_cost, 0)) as total_cost,
                    COUNT(DISTINCT DATE(timestamp)) as operational_days,
                    MIN(timestamp) as start_date,
                    MAX(timestamp) as end_date,
                    SUM(CASE WHEN yield_today > 0 THEN yield_today ELSE 0 END) as total_production
                FROM {table_name}
                WHERE timestamp >= '2024-01-01'
            """)
            
            result = cur.fetchone()
            
            if result and result['operational_days'] > 0:
                total_benefit = float(result['total_benefit'] or 0)
                total_cost = float(result['total_cost'] or 0)
                operational_days = result['operational_days']
                total_production = float(result['total_production'] or 0)
                
                # Calculate annual metrics
                annual_benefit = (total_benefit / operational_days) * 365
                annual_cost = (total_cost / operational_days) * 365
                net_annual_benefit = annual_benefit - annual_cost
                
                # ROI calculation
                annual_roi_percent = (net_annual_benefit / investment_cost) * 100 if investment_cost > 0 else 0
                payback_years = investment_cost / net_annual_benefit if net_annual_benefit > 0 else None
                
                # Production metrics
                annual_production = (total_production / operational_days) * 365
                
                conn.close()
                
                return {
                    "status": "success",
                    "system_id": system_id,
                    "investment_cost_eur": investment_cost,
                    "operational_period": {
                        "start_date": str(result['start_date']),
                        "end_date": str(result['end_date']),
                        "days": operational_days
                    },
                    "financial": {
                        "annual_benefit_eur": round(annual_benefit, 2),
                        "annual_cost_eur": round(annual_cost, 2),
                        "net_annual_benefit_eur": round(net_annual_benefit, 2),
                        "annual_roi_percent": round(annual_roi_percent, 2),
                        "payback_years": round(payback_years, 1) if payback_years else None
                    },
                    "production": {
                        "annual_production_kwh": round(annual_production, 2),
                        "total_production_kwh": round(total_production, 2)
                    },
                    "calculation_method": "pre_calculated_billing_fields",
                    "timestamp": datetime.now().isoformat()
                }
            else:
                return {
                    "status": "no_data",
                    "system_id": system_id,
                    "message": "Insufficient data for ROI calculation"
                }
                
        except Exception as e:
            logger.error(f"Error calculating ROI: {e}")
            return {
                "status": "error",
                "system_id": system_id,
                "error": str(e)
            }

# Global instance
standardized_billing_service = StandardizedBillingService()

def get_daily_cost_standardized(system_id: str, target_date: date = None) -> Dict:
    """Get daily cost using standardized method"""
    if target_date is None:
        target_date = date.today()
    return standardized_billing_service.get_daily_cost(system_id, target_date)

def get_roi_standardized(system_id: str, investment_cost: float = 12500.0) -> Dict:
    """Get ROI using standardized method"""
    return standardized_billing_service.get_roi_calculation(system_id, investment_cost)

if __name__ == "__main__":
    # Test the standardized API
    print("Testing Standardized Billing API...")
    
    # Test daily cost
    today_cost = get_daily_cost_standardized('system1')
    print(f"Today's cost: {today_cost}")
    
    # Test ROI
    roi_result = get_roi_standardized('system1')
    print(f"ROI result: {roi_result}")
'''
    
    # Write the standardized API
    with open('scripts/database/standardized_billing_api.py', 'w') as f:
        f.write(api_content)
    
    print("   ✅ Created standardized_billing_api.py")
    print("   📝 This API uses only pre-calculated billing fields")
    print("   🎯 Consistent results across all endpoints")

def create_api_migration_guide():
    """Create migration guide for updating existing APIs"""
    print("\n" + "="*60)
    print("📋 CREATING API MIGRATION GUIDE")
    print("="*60)
    
    guide_content = '''# API Migration Guide - Billing Fields Standardization

## Overview
This guide shows how to update existing APIs to use pre-calculated billing fields instead of manual calculations.

## Key Changes

### Before (Manual Calculation)
```python
# Manual calculation - AVOID
energy_cost = grid_usage * energy_rate
network_cost = grid_usage * network_rate
total_cost = energy_cost + network_cost + etmear_cost
```

### After (Pre-calculated Fields)
```python
# Use pre-calculated fields - RECOMMENDED
cur.execute("""
    SELECT 
        SUM(billing_cost) as total_cost,
        SUM(billing_benefit) as total_benefit,
        SUM(billing_net_metering_credit) as total_credit
    FROM solax_data 
    WHERE DATE(timestamp) = %s
""", (target_date,))
```

## Files to Update

### 1. enhanced_billing_service.py
- Replace manual calculations with SUM(billing_cost)
- Use SUM(billing_benefit) for self-consumption benefits
- Remove hardcoded tariff rates

### 2. unified_roi_calculator.py
- Use pre-calculated billing fields for ROI calculations
- Remove complex tariff lookup logic
- Simplify consumption rate calculations

### 3. enhanced_billing_system.py
- Update daily cost endpoints to use billing fields
- Remove estimation logic
- Use standardized_billing_api.py functions

## Benefits
- ✅ Consistent results across all APIs
- ✅ Faster response times (no complex calculations)
- ✅ Accurate historical data (no estimation)
- ✅ Easier maintenance (single source of truth)

## Testing
After migration, verify:
1. Daily cost calculations match between old and new APIs
2. ROI calculations are consistent
3. Response times are improved
4. No breaking changes in API responses

## Rollback Plan
If issues occur:
1. Restore files from backup directory
2. Restart services
3. Investigate and fix issues
4. Re-apply changes gradually
'''
    
    with open('docs/API_MIGRATION_GUIDE.md', 'w') as f:
        f.write(guide_content)
    
    print("   ✅ Created API_MIGRATION_GUIDE.md")
    print("   📖 Guide available in docs/API_MIGRATION_GUIDE.md")

def main():
    """Main API standardization function"""
    print("🚀 STARTING API STANDARDIZATION")
    
    # Step 1: Backup existing files
    backup_dir = backup_api_files()
    
    # Step 2: Create standardized API
    create_standardized_billing_api()
    
    # Step 3: Create migration guide
    create_api_migration_guide()
    
    print("\n" + "="*60)
    print("🎯 API STANDARDIZATION ΟΛΟΚΛΗΡΩΘΗΚΕ")
    print("="*60)
    print("✅ Standardized billing API created")
    print("✅ Migration guide created")
    print("✅ Original files backed up")
    print(f"📁 Backup location: {backup_dir}")
    print("\n📝 Next steps:")
    print("   1. Test standardized API")
    print("   2. Update existing APIs gradually")
    print("   3. Verify consistency")

if __name__ == "__main__":
    main()
'''
