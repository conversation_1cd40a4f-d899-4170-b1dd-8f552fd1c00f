#!/usr/bin/env python3
"""
Fix Billing Fields with Dynamic Calculation
Apply the correct dynamic hourly calculation to billing fields
"""

import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime, date
import time

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 5433,
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

def get_db_connection():
    """Get database connection"""
    try:
        return psycopg2.connect(**DB_CONFIG)
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return None

def create_dynamic_billing_function():
    """Create the correct dynamic billing calculation function"""
    print("\n" + "="*60)
    print("🔧 CREATING DYNAMIC BILLING FUNCTION")
    print("="*60)
    
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cur = conn.cursor()
        
        # Create the correct dynamic billing function
        cur.execute("""
            CREATE OR REPLACE FUNCTION calculate_dynamic_billing_fields()
            RETURNS TRIGGER AS $$
            DECLARE
                hourly_production NUMERIC := 0;
                prev_yield NUMERIC := 0;
                dynamic_self_consumption NUMERIC := 0;
                base_self_consumption_rate NUMERIC := 0.35;
                production_factor NUMERIC := 1.0;
                soc_factor NUMERIC := 1.0;
                dynamic_rate NUMERIC := 0.35;
                
                -- Tariff variables
                energy_rate NUMERIC := 0.142;
                network_rate NUMERIC := 0.0069;
                etmear_rate NUMERIC := 0.017;
                total_rate NUMERIC;
                schedule_name TEXT := 'summer_day';
                
                hour_of_day INTEGER;
                month_of_year INTEGER;
                current_soc NUMERIC;
            BEGIN
                -- Get time components
                hour_of_day := EXTRACT(HOUR FROM NEW.timestamp);
                month_of_year := EXTRACT(MONTH FROM NEW.timestamp);
                current_soc := COALESCE(NEW.soc, 50);
                
                -- Calculate hourly production (yield difference)
                SELECT COALESCE(yield_today, 0) INTO prev_yield
                FROM (
                    SELECT yield_today 
                    FROM solax_data 
                    WHERE timestamp < NEW.timestamp 
                    AND DATE(timestamp) = DATE(NEW.timestamp)
                    ORDER BY timestamp DESC 
                    LIMIT 1
                ) prev;
                
                -- Handle System 2 table
                IF TG_TABLE_NAME = 'solax_data2' THEN
                    SELECT COALESCE(yield_today, 0) INTO prev_yield
                    FROM (
                        SELECT yield_today 
                        FROM solax_data2 
                        WHERE timestamp < NEW.timestamp 
                        AND DATE(timestamp) = DATE(NEW.timestamp)
                        ORDER BY timestamp DESC 
                        LIMIT 1
                    ) prev;
                END IF;
                
                -- Calculate hourly production
                IF COALESCE(NEW.yield_today, 0) > prev_yield THEN
                    hourly_production := COALESCE(NEW.yield_today, 0) - prev_yield;
                ELSE
                    hourly_production := 0;
                END IF;
                
                -- Dynamic tariff calculation
                IF month_of_year IN (11, 12, 1, 2, 3) THEN -- Winter
                    IF hour_of_day IN (2, 3, 4) OR hour_of_day IN (12, 13, 14) THEN
                        energy_rate := 0.120;
                        schedule_name := 'winter_night';
                    ELSE
                        energy_rate := 0.142;
                        schedule_name := 'winter_day';
                    END IF;
                ELSE -- Summer
                    IF hour_of_day IN (2, 3) OR hour_of_day IN (11, 12, 13, 14) THEN
                        energy_rate := 0.132;
                        schedule_name := 'summer_night';
                    ELSE
                        energy_rate := 0.142;
                        schedule_name := 'summer_day';
                    END IF;
                END IF;
                
                total_rate := energy_rate + network_rate + etmear_rate;
                
                -- Dynamic self-consumption calculation
                IF hourly_production > 0 THEN
                    -- Base rate by time of day
                    IF hour_of_day BETWEEN 6 AND 18 THEN -- Daytime
                        base_self_consumption_rate := 0.35;
                    ELSIF hour_of_day BETWEEN 19 AND 22 THEN -- Evening
                        base_self_consumption_rate := 0.80;
                    ELSE -- Night
                        base_self_consumption_rate := 0.95;
                    END IF;
                    
                    -- Production factor
                    IF hourly_production > 8 THEN
                        production_factor := 0.8;
                    ELSIF hourly_production > 4 THEN
                        production_factor := 1.0;
                    ELSE
                        production_factor := 1.2;
                    END IF;
                    
                    -- SOC factor
                    IF current_soc < 20 THEN
                        soc_factor := 1.3;
                    ELSIF current_soc > 80 THEN
                        soc_factor := 0.7;
                    ELSE
                        soc_factor := 1.0;
                    END IF;
                    
                    -- Calculate dynamic rate
                    dynamic_rate := LEAST(1.0, base_self_consumption_rate * production_factor * soc_factor);
                    dynamic_self_consumption := hourly_production * dynamic_rate;
                ELSE
                    dynamic_self_consumption := 0;
                END IF;
                
                -- Set billing fields
                NEW.billing_tariff := energy_rate;
                NEW.billing_network_charge := network_rate;
                NEW.billing_etmear := etmear_rate;
                NEW.billing_schedule := schedule_name;
                
                -- Calculate costs and benefits
                NEW.billing_cost := 0.000; -- Assume no grid import for solar systems
                NEW.billing_benefit := dynamic_self_consumption * total_rate;
                NEW.billing_net_metering_credit := 0.000; -- Greek Net Metering
                
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;
        """)
        
        print("   ✅ Dynamic billing function created")
        
        # Update triggers
        cur.execute("""
            DROP TRIGGER IF EXISTS trg_dynamic_billing_solax_data ON solax_data;
            CREATE TRIGGER trg_dynamic_billing_solax_data
                BEFORE INSERT OR UPDATE ON solax_data
                FOR EACH ROW
                EXECUTE FUNCTION calculate_dynamic_billing_fields();
        """)
        
        cur.execute("""
            DROP TRIGGER IF EXISTS trg_dynamic_billing_solax_data2 ON solax_data2;
            CREATE TRIGGER trg_dynamic_billing_solax_data2
                BEFORE INSERT OR UPDATE ON solax_data2
                FOR EACH ROW
                EXECUTE FUNCTION calculate_dynamic_billing_fields();
        """)
        
        print("   ✅ Dynamic billing triggers updated")
        
        conn.commit()
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        if conn:
            conn.rollback()

def recalculate_billing_fields_for_date():
    """Recalculate billing fields for 24/6/2025 using dynamic calculation"""
    print("\n" + "="*60)
    print("🔄 RECALCULATING BILLING FIELDS")
    print("="*60)
    
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cur = conn.cursor()
        
        target_date = date(2025, 6, 24)
        
        for table, system_name in [('solax_data', 'System 1'), ('solax_data2', 'System 2')]:
            print(f"\n🔧 Recalculating {system_name}...")
            
            # Force trigger execution by updating records
            cur.execute(f"""
                UPDATE {table} 
                SET timestamp = timestamp 
                WHERE DATE(timestamp) = %s
            """, (target_date,))
            
            updated_count = cur.rowcount
            print(f"   ✅ Updated {updated_count:,} records")
            
            conn.commit()
            time.sleep(1)  # Small pause between systems
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        if conn:
            conn.rollback()

def validate_corrected_billing():
    """Validate the corrected billing fields"""
    print("\n" + "="*60)
    print("✅ VALIDATING CORRECTED BILLING")
    print("="*60)
    
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        
        target_date = date(2025, 6, 24)
        
        for table, system_name in [('solax_data', 'System 1'), ('solax_data2', 'System 2')]:
            print(f"\n📊 {system_name} Validation:")
            
            # Check corrected values
            cur.execute(f"""
                SELECT 
                    COUNT(*) as total_records,
                    COUNT(CASE WHEN billing_cost > 1 THEN 1 END) as high_cost_records,
                    COUNT(CASE WHEN billing_benefit > 10 THEN 1 END) as high_benefit_records,
                    ROUND(SUM(COALESCE(billing_benefit, 0))::numeric, 2) as total_daily_benefit,
                    ROUND(AVG(COALESCE(billing_benefit, 0))::numeric, 6) as avg_benefit,
                    COUNT(DISTINCT billing_schedule) as unique_schedules
                FROM {table}
                WHERE DATE(timestamp) = %s
            """, (target_date,))
            
            result = cur.fetchone()
            
            if result:
                print(f"   Total records: {result['total_records']:,}")
                print(f"   High cost records (>€1): {result['high_cost_records']}")
                print(f"   High benefit records (>€10): {result['high_benefit_records']}")
                print(f"   Total daily benefit: €{result['total_daily_benefit']}")
                print(f"   Average benefit: €{result['avg_benefit']}")
                print(f"   Unique schedules: {result['unique_schedules']}")
                
                if result['high_cost_records'] == 0 and result['high_benefit_records'] == 0:
                    print(f"   ✅ No unrealistic values")
                else:
                    print(f"   ⚠️ Still has unrealistic values")
                
                if result['unique_schedules'] > 1:
                    print(f"   ✅ Dynamic scheduling working")
                else:
                    print(f"   ⚠️ Static scheduling detected")
            
            # Show schedule distribution
            cur.execute(f"""
                SELECT 
                    billing_schedule,
                    COUNT(*) as count,
                    ROUND(SUM(COALESCE(billing_benefit, 0))::numeric, 2) as total_benefit
                FROM {table}
                WHERE DATE(timestamp) = %s
                GROUP BY billing_schedule
                ORDER BY count DESC
            """, (target_date,))
            
            schedules = cur.fetchall()
            
            print(f"   📅 Schedule distribution:")
            for schedule in schedules:
                print(f"      {schedule['billing_schedule']}: {schedule['count']} records (€{schedule['total_benefit']} benefit)")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Main correction function"""
    print("🔧 FIXING BILLING FIELDS WITH DYNAMIC CALCULATION")
    print("=" * 60)
    print("Applying correct dynamic hourly calculation to billing fields")
    print(f"Target Date: 24/6/2025")
    print(f"Time: {datetime.now()}")
    
    # Step 1: Create dynamic billing function
    create_dynamic_billing_function()
    
    # Step 2: Recalculate billing fields
    recalculate_billing_fields_for_date()
    
    # Step 3: Validate corrections
    validate_corrected_billing()
    
    print("\n" + "="*60)
    print("🎯 BILLING FIELDS CORRECTION COMPLETED")
    print("="*60)
    print("✅ Dynamic billing function created")
    print("✅ Triggers updated for dynamic calculation")
    print("✅ Historical billing fields recalculated")
    print("✅ Validation completed")
    print("\n📝 Billing fields now use correct dynamic calculation!")

if __name__ == "__main__":
    main()
