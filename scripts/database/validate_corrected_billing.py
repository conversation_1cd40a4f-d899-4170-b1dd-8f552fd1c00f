#!/usr/bin/env python3
"""
VALIDATE CORRECTED BILLING FIELDS
Επαληθεύει τα διορθωμένα billing fields με σωστή reset logic
"""

import psycopg2
from datetime import datetime, date
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 5433,
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

def validate_corrected_billing():
    """Επαληθεύει τα διορθωμένα billing fields"""
    
    print("🔍 VALIDATING CORRECTED BILLING FIELDS")
    print("=" * 50)
    
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()
        
        # Αληθινά δεδομένα για validation
        real_data = {
            'system1': {'yield': 66.60, 'self_consumption': 18.69},
            'system2': {'yield': 66.90, 'self_consumption': 19.53}
        }
        
        target_date = '2025-06-24'
        
        print(f"\n📅 DAILY VALIDATION ({target_date}):")
        
        total_daily_benefit = 0
        total_daily_production = 0
        
        for table, system_name, system_id in [
            ('solax_data', 'System 1', 'system1'), 
            ('solax_data2', 'System 2', 'system2')
        ]:
            print(f"\n🏠 {system_name}:")
            
            # ΣΩΣΤΗ RESET LOGIC - όπως στο correct_yield_calculation.py
            cur.execute(f"""
                WITH daily_data AS (
                    SELECT 
                        timestamp,
                        yield_today,
                        LAG(yield_today) OVER (ORDER BY timestamp) as prev_yield
                    FROM {table}
                    WHERE DATE(timestamp) = %s
                    ORDER BY timestamp
                ),
                reset_points AS (
                    SELECT 
                        timestamp,
                        yield_today,
                        prev_yield,
                        CASE 
                            WHEN yield_today < prev_yield THEN 1 
                            ELSE 0 
                        END as is_reset
                    FROM daily_data
                ),
                with_reset_groups AS (
                    SELECT 
                        timestamp,
                        yield_today,
                        SUM(is_reset) OVER (ORDER BY timestamp) as reset_group
                    FROM reset_points
                ),
                daily_production AS (
                    SELECT 
                        reset_group,
                        MAX(yield_today) - MIN(yield_today) as group_production
                    FROM with_reset_groups
                    GROUP BY reset_group
                    HAVING MAX(yield_today) - MIN(yield_today) > 0
                )
                SELECT 
                    COALESCE(SUM(group_production), 0) as total_daily_production
                FROM daily_production
            """, (target_date,))
            
            result = cur.fetchone()
            correct_daily_production = float(result[0] or 0) if result else 0
            
            # Get billing benefit sum
            cur.execute(f"""
                SELECT 
                    SUM(COALESCE(billing_benefit, 0)) as total_benefit
                FROM {table}
                WHERE DATE(timestamp) = %s
            """, (target_date,))
            
            result = cur.fetchone()
            daily_benefit = float(result[0] or 0) if result else 0
            
            # Compare with real data
            real_yield = real_data[system_id]['yield']
            real_self_consumption = real_data[system_id]['self_consumption']
            
            print(f"   Calculated Production: {correct_daily_production:.2f} kWh")
            print(f"   Real Production: {real_yield:.2f} kWh")
            print(f"   Production Accuracy: {(correct_daily_production/real_yield*100):.1f}%")
            
            print(f"   Daily Benefit: €{daily_benefit:.2f}")
            
            if correct_daily_production > 0:
                effective_rate = daily_benefit / correct_daily_production
                print(f"   Effective Rate: €{effective_rate:.4f}/kWh")
                
                # Calculate expected benefit with real self-consumption
                expected_benefit = real_self_consumption * 0.1659  # €0.1659/kWh total rate
                print(f"   Expected Benefit: €{expected_benefit:.2f} (Real self-consumption)")
                
                accuracy = (daily_benefit / expected_benefit * 100) if expected_benefit > 0 else 0
                print(f"   Benefit Accuracy: {accuracy:.1f}%")
            
            total_daily_benefit += daily_benefit
            total_daily_production += correct_daily_production
        
        print(f"\n🎯 COMBINED DAILY:")
        print(f"   Total Production: {total_daily_production:.2f} kWh")
        print(f"   Real Combined: {real_data['system1']['yield'] + real_data['system2']['yield']:.2f} kWh")
        print(f"   Production Accuracy: {(total_daily_production/(real_data['system1']['yield'] + real_data['system2']['yield'])*100):.1f}%")
        print(f"   Total Daily Benefit: €{total_daily_benefit:.2f}")
        
        if total_daily_production > 0:
            combined_effective_rate = total_daily_benefit / total_daily_production
            print(f"   Combined Effective Rate: €{combined_effective_rate:.4f}/kWh")
        
        # Expected combined benefit
        expected_combined = (real_data['system1']['self_consumption'] + real_data['system2']['self_consumption']) * 0.1659
        print(f"   Expected Combined Benefit: €{expected_combined:.2f}")
        
        if expected_combined > 0:
            combined_accuracy = (total_daily_benefit / expected_combined * 100)
            print(f"   Combined Benefit Accuracy: {combined_accuracy:.1f}%")
        
        # HISTORICAL ROI CALCULATION με σωστή reset logic
        print(f"\n📈 HISTORICAL ROI CALCULATION:")
        
        total_annual_benefit = 0
        investment_cost = 12500.0
        
        for table, system_name in [('solax_data', 'System 1'), ('solax_data2', 'System 2')]:
            print(f"\n🏠 {system_name}:")
            
            # Get historical totals using corrected billing fields
            cur.execute(f"""
                SELECT 
                    COUNT(DISTINCT DATE(timestamp)) as total_days,
                    SUM(COALESCE(billing_benefit, 0)) as total_benefit,
                    MIN(timestamp) as start_date,
                    MAX(timestamp) as end_date
                FROM {table}
                WHERE timestamp >= '2024-01-01'
                AND billing_benefit IS NOT NULL
            """)
            
            result = cur.fetchone()
            if result:
                total_days, total_benefit, start_date, end_date = result
                total_benefit = float(total_benefit or 0)
                
                # Calculate operational period
                if start_date and end_date:
                    operational_days = (end_date - start_date).days
                    operational_years = operational_days / 365.25
                else:
                    operational_years = 1
                
                # Calculate annual benefit
                annual_benefit = total_benefit / operational_years if operational_years > 0 else 0
                
                # Calculate ROI
                roi_percentage = (annual_benefit / investment_cost * 100) if investment_cost > 0 else 0
                payback_years = investment_cost / annual_benefit if annual_benefit > 0 else float('inf')
                
                print(f"   Operational Days: {total_days}")
                print(f"   Total Benefit: €{total_benefit:.2f}")
                print(f"   Annual Benefit: €{annual_benefit:.2f}")
                print(f"   ROI: {roi_percentage:.2f}%")
                print(f"   Payback: {payback_years:.1f} years")
                
                total_annual_benefit += annual_benefit
        
        # COMBINED ROI
        print(f"\n🎯 COMBINED ROI:")
        total_investment = investment_cost * 2  # Two systems
        combined_roi = (total_annual_benefit / total_investment * 100) if total_investment > 0 else 0
        combined_payback = total_investment / total_annual_benefit if total_annual_benefit > 0 else float('inf')
        
        print(f"   Total Investment: €{total_investment:,.0f}")
        print(f"   Total Annual Benefit: €{total_annual_benefit:.2f}")
        print(f"   Combined ROI: {combined_roi:.2f}%")
        print(f"   Combined Payback: {combined_payback:.1f} years")
        
        # ΣΥΓΚΡΙΣΗ ΜΕ ΑΛΗΘΙΝΑ ΔΕΔΟΜΕΝΑ
        print(f"\n📊 COMPARISON WITH REAL DATA:")
        real_combined_self_consumption = real_data['system1']['self_consumption'] + real_data['system2']['self_consumption']
        real_daily_benefit = real_combined_self_consumption * 0.1659
        real_annual_benefit = real_daily_benefit * 365.25
        real_combined_roi = (real_annual_benefit / total_investment * 100)
        real_payback = total_investment / real_annual_benefit
        
        print(f"   Real Daily Benefit: €{real_daily_benefit:.2f}")
        print(f"   Real Annual Benefit: €{real_annual_benefit:.2f}")
        print(f"   Real Combined ROI: {real_combined_roi:.2f}%")
        print(f"   Real Payback: {real_payback:.1f} years")
        
        print(f"\n🔍 ACCURACY ANALYSIS:")
        print(f"   Daily Benefit Accuracy: {(total_daily_benefit/real_daily_benefit*100):.1f}%")
        print(f"   Annual Benefit Accuracy: {(total_annual_benefit/real_annual_benefit*100):.1f}%")
        print(f"   ROI Accuracy: {(combined_roi/real_combined_roi*100):.1f}%")
        
        conn.close()
        
        print("\n✅ VALIDATION COMPLETED!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    validate_corrected_billing()
