#!/usr/bin/env python3
"""
CORRECT SELF-CONSUMPTION ANALYSIS
Σωστή ανάλυση της αυτοκατανάλωσης (self-consumption) vs παραγωγής
"""

import psycopg2
from datetime import datetime, date, timedelta
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 5433,
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

def analyze_self_consumption():
    """Σωστή ανάλυση αυτοκατανάλωσης vs παραγωγής"""
    
    print("🏠 CORRECT SELF-CONSUMPTION ANALYSIS")
    print("=" * 80)
    
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()
        
        print("✅ Connected to database")
        
        # 1. ΣΥΝΟΛΙΚΗ ΠΑΡΑΓΩΓΗ ΕΝΕΡΓΕΙΑΣ
        print("\n⚡ TOTAL ENERGY PRODUCTION")
        print("-" * 50)
        
        total_production_all = 0
        total_self_consumption_all = 0
        
        for table, system_name in [('solax_data', 'System 1'), ('solax_data2', 'System 2')]:
            print(f"\n🏠 {system_name}:")
            
            # Συνολική παραγωγή
            cur.execute(f"""
                WITH daily_production AS (
                    SELECT 
                        DATE(timestamp) as date,
                        MAX(yield_today) - MIN(yield_today) as daily_production
                    FROM {table}
                    WHERE timestamp >= '2024-01-01'
                    AND yield_today >= 0
                    GROUP BY DATE(timestamp)
                    HAVING MAX(yield_today) - MIN(yield_today) > 0
                )
                SELECT 
                    COUNT(*) as total_days,
                    SUM(daily_production) as total_production,
                    AVG(daily_production) as avg_daily_production
                FROM daily_production
            """)
            
            result = cur.fetchone()
            if result:
                total_days, total_production, avg_daily = result
                total_production = float(total_production or 0)
                avg_daily = float(avg_daily or 0)
                
                print(f"   📊 Total days: {total_days}")
                print(f"   ⚡ Total production: {total_production:.2f} kWh")
                print(f"   📈 Average daily: {avg_daily:.2f} kWh/day")
                
                total_production_all += total_production
        
        print(f"\n🎯 COMBINED TOTAL PRODUCTION: {total_production_all:.2f} kWh")
        
        # 2. ΑΥΤΟΚΑΤΑΝΑΛΩΣΗ (SELF-CONSUMPTION)
        print("\n🏠 SELF-CONSUMPTION ANALYSIS")
        print("-" * 50)
        
        for table, system_name in [('solax_data', 'System 1'), ('solax_data2', 'System 2')]:
            print(f"\n🏠 {system_name}:")
            
            # Υπολογισμός αυτοκατανάλωσης από billing fields
            cur.execute(f"""
                SELECT 
                    SUM(COALESCE(billing_benefit, 0)) as total_savings,
                    COUNT(CASE WHEN billing_benefit > 0 THEN 1 END) as records_with_savings,
                    COUNT(DISTINCT DATE(timestamp)) as days_with_data
                FROM {table}
                WHERE timestamp >= '2024-01-01'
                AND billing_benefit IS NOT NULL
            """)
            
            result = cur.fetchone()
            if result:
                total_savings, records_with_savings, days_with_data = result
                total_savings = float(total_savings or 0)
                
                # Υπολογισμός αυτοκατανάλωσης από τα savings
                # billing_benefit = self_consumption_kwh * total_rate
                # άρα self_consumption_kwh = billing_benefit / total_rate
                total_rate = 0.1659  # €0.142 + €0.0069 + €0.017
                calculated_self_consumption = total_savings / total_rate
                
                print(f"   💰 Total savings: €{total_savings:.2f}")
                print(f"   🏠 Calculated self-consumption: {calculated_self_consumption:.2f} kWh")
                print(f"   📊 Days with data: {days_with_data}")
                
                if days_with_data > 0:
                    daily_self_consumption = calculated_self_consumption / days_with_data
                    print(f"   📈 Daily self-consumption: {daily_self_consumption:.2f} kWh/day")
                
                total_self_consumption_all += calculated_self_consumption
        
        print(f"\n🎯 COMBINED TOTAL SELF-CONSUMPTION: {total_self_consumption_all:.2f} kWh")
        
        # 3. ΣΩΣΤΟΣ ΥΠΟΛΟΓΙΣΜΟΣ ΚΟΣΤΟΥΣ kWh
        print("\n💡 CORRECT COST PER kWh ANALYSIS")
        print("-" * 50)
        
        if total_self_consumption_all > 0:
            # Σωστός υπολογισμός: savings / self-consumption (όχι total production!)
            correct_effective_rate = (total_self_consumption_all * 0.1659) / total_self_consumption_all
            
            print(f"\n🎯 CORRECT CALCULATION:")
            print(f"   Total Self-Consumption: {total_self_consumption_all:.2f} kWh")
            print(f"   Total Savings: €{total_self_consumption_all * 0.1659:.2f}")
            print(f"   Effective Rate: €{correct_effective_rate:.4f}/kWh")
            
            # Αυτό πρέπει να είναι ίσο με το total rate (0.1659)
            print(f"   Expected Rate: €0.1659/kWh")
            print(f"   Difference: €{abs(correct_effective_rate - 0.1659):.6f}/kWh")
            
            if abs(correct_effective_rate - 0.1659) < 0.001:
                print(f"   ✅ PERFECT MATCH!")
            else:
                print(f"   ⚠️  MISMATCH DETECTED!")
        
        # 4. SELF-CONSUMPTION RATES
        print("\n📊 SELF-CONSUMPTION RATES")
        print("-" * 50)
        
        system_data = {}
        
        for table, system_name in [('solax_data', 'System 1'), ('solax_data2', 'System 2')]:
            print(f"\n🏠 {system_name}:")
            
            # Παραγωγή
            cur.execute(f"""
                WITH daily_production AS (
                    SELECT 
                        DATE(timestamp) as date,
                        MAX(yield_today) - MIN(yield_today) as daily_production
                    FROM {table}
                    WHERE timestamp >= '2024-01-01'
                    AND yield_today >= 0
                    GROUP BY DATE(timestamp)
                    HAVING MAX(yield_today) - MIN(yield_today) > 0
                )
                SELECT SUM(daily_production) as total_production
                FROM daily_production
            """)
            
            result = cur.fetchone()
            system_production = float(result[0] or 0) if result else 0
            
            # Αυτοκατανάλωση
            cur.execute(f"""
                SELECT SUM(COALESCE(billing_benefit, 0)) as total_savings
                FROM {table}
                WHERE timestamp >= '2024-01-01'
                AND billing_benefit IS NOT NULL
            """)
            
            result = cur.fetchone()
            system_savings = float(result[0] or 0) if result else 0
            system_self_consumption = system_savings / 0.1659
            
            # Self-consumption rate
            self_consumption_rate = (system_self_consumption / system_production * 100) if system_production > 0 else 0
            
            # Surplus to grid
            surplus_to_grid = system_production - system_self_consumption
            surplus_rate = (surplus_to_grid / system_production * 100) if system_production > 0 else 0
            
            print(f"   ⚡ Total production: {system_production:.2f} kWh")
            print(f"   🏠 Self-consumption: {system_self_consumption:.2f} kWh ({self_consumption_rate:.1f}%)")
            print(f"   🔌 Surplus to grid: {surplus_to_grid:.2f} kWh ({surplus_rate:.1f}%)")
            print(f"   💰 Savings: €{system_savings:.2f}")
            
            # Υπολογισμός τι θα κόστιζε αν αγόραζε από το δίκτυο
            cost_if_bought_from_grid = system_self_consumption * 0.142  # Day rate
            print(f"   💸 Cost if bought from grid: €{cost_if_bought_from_grid:.2f}")
            print(f"   💰 Actual savings: €{system_savings:.2f}")
            
            # Verification
            expected_savings = system_self_consumption * 0.1659
            accuracy = (system_savings / expected_savings * 100) if expected_savings > 0 else 0
            print(f"   🎯 Billing accuracy: {accuracy:.1f}%")
            
            system_data[system_name] = {
                'production': system_production,
                'self_consumption': system_self_consumption,
                'self_consumption_rate': self_consumption_rate,
                'savings': system_savings
            }
        
        # 5. ΣΥΓΚΡΙΣΗ ΜΕ ΤΙΜΕΣ ΔΙΚΤΥΟΥ
        print("\n💰 COMPARISON WITH GRID PRICES")
        print("-" * 50)
        
        grid_day_rate = 0.142  # €/kWh
        grid_night_rate = 0.120  # €/kWh
        grid_avg_rate = (grid_day_rate + grid_night_rate) / 2
        solar_effective_rate = 0.1659  # What we save per kWh consumed
        
        print(f"   Grid day rate: €{grid_day_rate:.4f}/kWh")
        print(f"   Grid night rate: €{grid_night_rate:.4f}/kWh")
        print(f"   Grid average rate: €{grid_avg_rate:.4f}/kWh")
        print(f"   Solar effective rate: €{solar_effective_rate:.4f}/kWh")
        
        # Σωστή σύγκριση: πόσο εξοικονομούμε ανά kWh που καταναλώνουμε
        savings_vs_day = solar_effective_rate / grid_day_rate * 100
        savings_vs_avg = solar_effective_rate / grid_avg_rate * 100
        
        print(f"\n📊 SAVINGS ANALYSIS:")
        print(f"   Solar rate vs Day rate: {savings_vs_day:.1f}% of day rate")
        print(f"   Solar rate vs Avg rate: {savings_vs_avg:.1f}% of avg rate")
        print(f"   Effective discount: {100 - savings_vs_day:.1f}% off day rate")
        print(f"   Effective discount: {100 - savings_vs_avg:.1f}% off avg rate")
        
        # 6. ΣΥΝΟΛΙΚΗ ΑΞΙΟΛΟΓΗΣΗ
        print("\n🎯 OVERALL ASSESSMENT")
        print("-" * 50)
        
        if len(system_data) == 2:
            total_production = sum(data['production'] for data in system_data.values())
            total_self_consumption = sum(data['self_consumption'] for data in system_data.values())
            total_savings = sum(data['savings'] for data in system_data.values())
            
            overall_self_consumption_rate = (total_self_consumption / total_production * 100) if total_production > 0 else 0
            
            print(f"   📊 Combined production: {total_production:.2f} kWh")
            print(f"   🏠 Combined self-consumption: {total_self_consumption:.2f} kWh")
            print(f"   📈 Overall self-consumption rate: {overall_self_consumption_rate:.1f}%")
            print(f"   💰 Total savings: €{total_savings:.2f}")
            
            # Τι θα κόστιζε αν αγόραζε όλη την ενέργεια από το δίκτυο
            cost_from_grid = total_self_consumption * grid_day_rate
            actual_cost = 0  # Δωρεάν από φωτοβολταϊκά
            net_savings = cost_from_grid - actual_cost
            
            print(f"\n💸 FINANCIAL IMPACT:")
            print(f"   Cost if bought from grid: €{cost_from_grid:.2f}")
            print(f"   Actual cost (solar): €{actual_cost:.2f}")
            print(f"   Net savings: €{net_savings:.2f}")
            print(f"   Savings rate: {(net_savings/cost_from_grid*100):.1f}%")
        
        conn.close()
        
        print("\n🎉 CORRECT ANALYSIS COMPLETED!")
        print("-" * 50)
        print("✅ Self-consumption (not total production) analyzed")
        print("✅ Correct cost per kWh calculated")
        print("✅ Proper comparison with grid rates")
        print("✅ Financial impact assessed")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    analyze_self_consumption()
