#!/usr/bin/env python3
"""
Diagnose Consumption Data Problem
Investigate why consume_energy is showing 7,964 kWh/day
"""

import psycopg2
import pandas as pd
from datetime import datetime, date, timedelta
import sys
import os

# Add project root to path
sys.path.append('/home/<USER>/solar-prediction-project')

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 5433,
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

def get_db_connection():
    """Get database connection"""
    try:
        return psycopg2.connect(**DB_CONFIG)
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return None

def diagnose_consumption_data():
    """Diagnose consumption data issues"""
    print("\n" + "="*60)
    print("🔍 ΔΙΑΓΝΩΣΗ CONSUMPTION DATA ΠΡΟΒΛΗΜΑΤΟΣ")
    print("="*60)
    
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cur = conn.cursor()
        
        # Check 1: Daily consumption patterns
        print("\n📊 Check 1: Daily consumption patterns (last 7 days)")
        cur.execute("""
            SELECT 
                DATE(timestamp) as date,
                MIN(consume_energy) as min_consume,
                MAX(consume_energy) as max_consume,
                MAX(consume_energy) - MIN(consume_energy) as daily_consumption,
                COUNT(*) as records,
                ROUND(AVG(consume_energy)::numeric, 2) as avg_consume
            FROM solax_data 
            WHERE timestamp >= CURRENT_DATE - INTERVAL '7 days'
            GROUP BY DATE(timestamp)
            ORDER BY date DESC
        """)
        
        daily_data = cur.fetchall()
        print("   System 1 daily patterns:")
        for date_val, min_val, max_val, daily, records, avg_val in daily_data:
            print(f"   {date_val}: Min {min_val:.1f}, Max {max_val:.1f}, Daily {daily:.1f} kWh, Avg {avg_val:.1f}, Records {records}")
        
        # Check 2: Hourly consumption increments
        print("\n📈 Check 2: Hourly consumption increments (today)")
        cur.execute("""
            WITH hourly_data AS (
                SELECT 
                    timestamp,
                    consume_energy,
                    LAG(consume_energy) OVER (ORDER BY timestamp) as prev_consume,
                    consume_energy - LAG(consume_energy) OVER (ORDER BY timestamp) as hourly_increment
                FROM solax_data 
                WHERE DATE(timestamp) = CURRENT_DATE
                ORDER BY timestamp
            )
            SELECT 
                DATE_TRUNC('hour', timestamp) as hour,
                COUNT(*) as records,
                ROUND(AVG(COALESCE(hourly_increment, 0))::numeric, 4) as avg_increment,
                ROUND(MAX(COALESCE(hourly_increment, 0))::numeric, 4) as max_increment,
                ROUND(MIN(COALESCE(hourly_increment, 0))::numeric, 4) as min_increment
            FROM hourly_data
            WHERE hourly_increment IS NOT NULL
            GROUP BY DATE_TRUNC('hour', timestamp)
            ORDER BY hour DESC
            LIMIT 10
        """)
        
        hourly_data = cur.fetchall()
        print("   System 1 hourly increments:")
        for hour, records, avg_inc, max_inc, min_inc in hourly_data:
            print(f"   {hour}: Avg {avg_inc} kWh, Max {max_inc} kWh, Min {min_inc} kWh, Records {records}")
        
        # Check 3: Reset detection
        print("\n🔄 Check 3: Reset detection (consume_energy decreases)")
        cur.execute("""
            WITH resets AS (
                SELECT 
                    timestamp,
                    consume_energy,
                    LAG(consume_energy) OVER (ORDER BY timestamp) as prev_consume,
                    CASE WHEN consume_energy < LAG(consume_energy) OVER (ORDER BY timestamp) 
                         THEN 'RESET' ELSE 'NORMAL' END as reset_type
                FROM solax_data 
                WHERE DATE(timestamp) >= CURRENT_DATE - INTERVAL '3 days'
                ORDER BY timestamp
            )
            SELECT 
                DATE(timestamp) as date,
                COUNT(*) as total_records,
                COUNT(CASE WHEN reset_type = 'RESET' THEN 1 END) as reset_count,
                MIN(CASE WHEN reset_type = 'RESET' THEN consume_energy END) as min_after_reset,
                MAX(CASE WHEN reset_type = 'RESET' THEN prev_consume END) as max_before_reset
            FROM resets
            GROUP BY DATE(timestamp)
            ORDER BY date DESC
        """)
        
        reset_data = cur.fetchall()
        print("   System 1 reset patterns:")
        for date_val, total, resets, min_after, max_before in reset_data:
            print(f"   {date_val}: {resets} resets out of {total} records")
            if min_after and max_before:
                print(f"      Reset: {max_before:.1f} → {min_after:.1f} kWh")
        
        # Check 4: Compare with System 2
        print("\n🔄 Check 4: System 2 comparison")
        cur.execute("""
            SELECT 
                DATE(timestamp) as date,
                MIN(consume_energy) as min_consume,
                MAX(consume_energy) as max_consume,
                MAX(consume_energy) - MIN(consume_energy) as daily_consumption,
                COUNT(*) as records
            FROM solax_data2 
            WHERE timestamp >= CURRENT_DATE - INTERVAL '3 days'
                AND consume_energy IS NOT NULL
            GROUP BY DATE(timestamp)
            ORDER BY date DESC
        """)
        
        system2_data = cur.fetchall()
        print("   System 2 daily patterns:")
        for date_val, min_val, max_val, daily, records in system2_data:
            print(f"   {date_val}: Min {min_val:.1f}, Max {max_val:.1f}, Daily {daily:.1f} kWh, Records {records}")
        
        # Check 5: Data source investigation
        print("\n🔍 Check 5: Data source investigation")
        cur.execute("""
            SELECT 
                'System 1' as system,
                MIN(timestamp) as earliest_record,
                MAX(timestamp) as latest_record,
                COUNT(*) as total_records,
                COUNT(DISTINCT DATE(timestamp)) as total_days,
                ROUND(AVG(consume_energy)::numeric, 2) as avg_consume_energy,
                ROUND(MAX(consume_energy)::numeric, 2) as max_consume_energy
            FROM solax_data
            WHERE timestamp >= '2024-06-01'
            
            UNION ALL
            
            SELECT 
                'System 2' as system,
                MIN(timestamp) as earliest_record,
                MAX(timestamp) as latest_record,
                COUNT(*) as total_records,
                COUNT(DISTINCT DATE(timestamp)) as total_days,
                ROUND(AVG(COALESCE(consume_energy, 0))::numeric, 2) as avg_consume_energy,
                ROUND(MAX(COALESCE(consume_energy, 0))::numeric, 2) as max_consume_energy
            FROM solax_data2
            WHERE timestamp >= '2024-06-01'
        """)
        
        source_data = cur.fetchall()
        print("   Data source comparison:")
        for system, earliest, latest, total, days, avg_consume, max_consume in source_data:
            print(f"   {system}:")
            print(f"      Period: {earliest} to {latest}")
            print(f"      Records: {total:,} over {days} days")
            print(f"      Avg consume_energy: {avg_consume} kWh")
            print(f"      Max consume_energy: {max_consume} kWh")
        
        # Check 6: Sample raw data
        print("\n📋 Check 6: Sample raw data (last 10 records)")
        cur.execute("""
            SELECT 
                timestamp,
                ROUND(consume_energy::numeric, 2) as consume_energy,
                ROUND(feedin_energy::numeric, 2) as feedin_energy,
                ROUND(yield_today::numeric, 2) as yield_today,
                ROUND(COALESCE(grid_usage_kwh, 0)::numeric, 4) as grid_usage_kwh
            FROM solax_data 
            ORDER BY timestamp DESC 
            LIMIT 10
        """)
        
        sample_data = cur.fetchall()
        print("   System 1 sample data:")
        for ts, consume, feedin, yield_val, grid in sample_data:
            print(f"   {ts}: Consume {consume}, Feedin {feedin}, Yield {yield_val}, Grid {grid}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Main diagnosis function"""
    diagnose_consumption_data()
    
    print("\n" + "="*60)
    print("🎯 ΔΙΑΓΝΩΣΗ ΟΛΟΚΛΗΡΩΘΗΚΕ")
    print("="*60)
    print("Ελέγχω τα αποτελέσματα για να καθορίσω την επόμενη δράση...")

if __name__ == "__main__":
    main()
