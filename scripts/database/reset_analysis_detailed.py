#!/usr/bin/env python3
"""
DETAILED RESET ANALYSIS
Αναλύει πώς αποτυπώνεται το reset στη βάση και πώς το αναγνωρίζει το function
"""

import psycopg2
from datetime import datetime, date, timedelta
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 5433,
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

def analyze_reset_patterns():
    """Αναλύει τα reset patterns στη βάση"""
    
    print("🔄 DETAILED RESET ANALYSIS")
    print("=" * 80)
    
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()
        
        print("✅ Connected to database")
        
        # 1. ΑΝΑΛΥΣΗ RESET PATTERNS
        print("\n📊 RESET PATTERN ANALYSIS")
        print("-" * 50)
        
        # Επιλογή μιας καλής ημέρας για ανάλυση
        analysis_date = '2025-06-20'
        
        for table, system_name in [('solax_data', 'System 1'), ('solax_data2', 'System 2')]:
            print(f"\n🏠 {system_name} - {analysis_date}:")
            
            # Δείχνω τα πρώτα 20 records της ημέρας
            cur.execute(f"""
                SELECT 
                    timestamp,
                    yield_today,
                    LAG(yield_today) OVER (ORDER BY timestamp) as prev_yield,
                    yield_today - LAG(yield_today) OVER (ORDER BY timestamp) as yield_diff,
                    CASE 
                        WHEN yield_today < LAG(yield_today) OVER (ORDER BY timestamp) 
                        THEN 'RESET' 
                        ELSE 'NORMAL' 
                    END as reset_status
                FROM {table}
                WHERE DATE(timestamp) = %s
                ORDER BY timestamp
                LIMIT 20
            """, (analysis_date,))
            
            results = cur.fetchall()
            print("   Time        | Yield | Prev  | Diff  | Status")
            print("   ------------|-------|-------|-------|--------")
            
            for result in results:
                timestamp, yield_today, prev_yield, yield_diff, reset_status = result
                time_str = timestamp.strftime("%H:%M:%S")
                yield_str = f"{yield_today:.1f}" if yield_today else "NULL"
                prev_str = f"{prev_yield:.1f}" if prev_yield else "NULL"
                diff_str = f"{yield_diff:.1f}" if yield_diff else "NULL"
                
                print(f"   {time_str} | {yield_str:>5} | {prev_str:>5} | {diff_str:>5} | {reset_status}")
        
        # 2. RESET DETECTION LOGIC
        print(f"\n🧠 RESET DETECTION LOGIC")
        print("-" * 50)
        
        for table, system_name in [('solax_data', 'System 1'), ('solax_data2', 'System 2')]:
            print(f"\n🏠 {system_name} - Reset Detection:")
            
            # Βρίσκω όλα τα resets της ημέρας
            cur.execute(f"""
                WITH reset_detection AS (
                    SELECT 
                        timestamp,
                        yield_today,
                        LAG(yield_today) OVER (ORDER BY timestamp) as prev_yield,
                        CASE 
                            WHEN yield_today < LAG(yield_today) OVER (ORDER BY timestamp) 
                            THEN LAG(yield_today) OVER (ORDER BY timestamp) - yield_today
                            ELSE 0 
                        END as reset_drop
                    FROM {table}
                    WHERE DATE(timestamp) = %s
                    ORDER BY timestamp
                )
                SELECT 
                    timestamp,
                    yield_today,
                    prev_yield,
                    reset_drop
                FROM reset_detection
                WHERE reset_drop > 0
                ORDER BY timestamp
            """, (analysis_date,))
            
            results = cur.fetchall()
            if results:
                print("   Reset Time      | Current | Previous | Drop")
                print("   ----------------|---------|----------|------")
                for result in results:
                    timestamp, yield_today, prev_yield, reset_drop = result
                    time_str = timestamp.strftime("%H:%M:%S")
                    print(f"   {time_str} | {yield_today:>7.1f} | {prev_yield:>8.1f} | {reset_drop:>4.1f}")
            else:
                print("   ✅ No resets detected")
        
        # 3. CURRENT FUNCTION LOGIC
        print(f"\n⚙️  CURRENT FUNCTION LOGIC")
        print("-" * 50)
        
        print("Η τρέχουσα function χρησιμοποιεί:")
        print("1. MAX(yield_today) - MIN(yield_today) ανά ημέρα")
        print("2. Αγνοεί όλα τα resets")
        print("3. Απλή και αξιόπιστη μέθοδος")
        
        for table, system_name in [('solax_data', 'System 1'), ('solax_data2', 'System 2')]:
            cur.execute(f"""
                SELECT 
                    MIN(yield_today) as min_yield,
                    MAX(yield_today) as max_yield,
                    MAX(yield_today) - MIN(yield_today) as daily_production,
                    COUNT(*) as total_records
                FROM {table}
                WHERE DATE(timestamp) = %s
                AND yield_today >= 0
            """, (analysis_date,))
            
            result = cur.fetchone()
            if result:
                min_yield, max_yield, daily_production, total_records = result
                print(f"\n   {system_name}:")
                print(f"      Min yield: {min_yield:.2f} kWh")
                print(f"      Max yield: {max_yield:.2f} kWh") 
                print(f"      Daily production: {daily_production:.2f} kWh")
                print(f"      Total records: {total_records}")
        
        # 4. RESET TYPES CLASSIFICATION
        print(f"\n📋 RESET TYPES CLASSIFICATION")
        print("-" * 50)
        
        # Αναλύω διαφορετικούς τύπους resets
        test_dates = ['2025-06-20', '2025-06-15', '2025-06-10']
        
        for test_date in test_dates:
            print(f"\n📅 {test_date}:")
            
            for table, system_name in [('solax_data', 'System 1'), ('solax_data2', 'System 2')]:
                cur.execute(f"""
                    WITH reset_analysis AS (
                        SELECT 
                            timestamp,
                            yield_today,
                            LAG(yield_today) OVER (ORDER BY timestamp) as prev_yield,
                            CASE 
                                WHEN yield_today < LAG(yield_today) OVER (ORDER BY timestamp) 
                                THEN LAG(yield_today) OVER (ORDER BY timestamp) - yield_today
                                ELSE 0 
                            END as reset_drop
                        FROM {table}
                        WHERE DATE(timestamp) = %s
                        ORDER BY timestamp
                    )
                    SELECT 
                        COUNT(CASE WHEN reset_drop > 0 THEN 1 END) as total_resets,
                        COUNT(CASE WHEN reset_drop > 50 THEN 1 END) as major_resets,
                        COUNT(CASE WHEN reset_drop BETWEEN 5 AND 50 THEN 1 END) as medium_resets,
                        COUNT(CASE WHEN reset_drop BETWEEN 0.1 AND 5 THEN 1 END) as minor_resets,
                        MAX(reset_drop) as max_reset_drop,
                        AVG(CASE WHEN reset_drop > 0 THEN reset_drop END) as avg_reset_drop
                    FROM reset_analysis
                """, (test_date,))
                
                result = cur.fetchone()
                if result:
                    total_resets, major_resets, medium_resets, minor_resets, max_reset_drop, avg_reset_drop = result
                    print(f"   {system_name}:")
                    print(f"      Total resets: {total_resets}")
                    print(f"      Major (>50kWh): {major_resets}")
                    print(f"      Medium (5-50kWh): {medium_resets}")
                    print(f"      Minor (0.1-5kWh): {minor_resets}")
                    if max_reset_drop:
                        print(f"      Max drop: {max_reset_drop:.2f} kWh")
                    if avg_reset_drop:
                        print(f"      Avg drop: {avg_reset_drop:.2f} kWh")
        
        conn.close()
        
        print("\n🎯 RESET ANALYSIS SUMMARY")
        print("-" * 50)
        print("✅ Reset = όταν yield_today < previous_yield")
        print("✅ Κανονικό reset = μεγάλη πτώση (>50kWh) στα μεσάνυχτα")
        print("✅ False reset = μικρές πτώσεις (<5kWh) κατά τη διάρκεια της ημέρας")
        print("✅ Current function αγνοεί όλα τα resets και χρησιμοποιεί MAX-MIN")
        print("✅ Αυτό είναι σωστό γιατί αποφεύγει τα false resets")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    analyze_reset_patterns()
