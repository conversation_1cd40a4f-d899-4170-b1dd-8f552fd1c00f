#!/usr/bin/env python3
"""
Database Migration: Create NASA POWER Data Table
Creates the nasa_power_data table for enhanced weather features
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def get_db_connection():
    """Get database connection"""
    try:
        conn = psycopg2.connect(
            host=os.getenv('DB_HOST', 'localhost'),
            database=os.getenv('DB_NAME', 'solar_prediction'),
            user=os.getenv('DB_USER', 'postgres'),
            password=os.getenv('DB_PASSWORD', 'postgres')
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        return conn
    except Exception as e:
        logger.error(f"Failed to connect to database: {e}")
        return None


def check_table_exists(conn, table_name):
    """Check if table already exists"""
    try:
        with conn.cursor() as cur:
            cur.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = %s
                );
            """, (table_name,))
            return cur.fetchone()[0]
    except Exception as e:
        logger.error(f"Error checking table existence: {e}")
        return False


def create_nasa_power_table(conn):
    """Create the nasa_power_data table"""
    
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS nasa_power_data (
        id SERIAL PRIMARY KEY,
        timestamp TIMESTAMP NOT NULL,
        
        -- Location Information
        latitude FLOAT,
        longitude FLOAT,
        
        -- Primary NASA POWER Parameters
        ghi FLOAT,                              -- All Sky Surface Shortwave Downward Irradiance (W/m²)
        temperature FLOAT,                      -- Temperature at 2 Meters (°C)
        wind_speed FLOAT,                       -- Wind Speed at 10 Meters (m/s)
        relative_humidity FLOAT,                -- Relative Humidity at 2 Meters (%)
        surface_pressure FLOAT,                 -- Surface Pressure (kPa)
        clear_sky_ghi FLOAT,                    -- Clear Sky Surface Shortwave Downward Irradiance (W/m²)
        clearness_index FLOAT,                  -- All Sky Clearness Index (KT)
        
        -- Physics-Based Derived Features
        module_temperature FLOAT,               -- Calculated module temperature (°C)
        temperature_efficiency_factor FLOAT,    -- Temperature effect on efficiency (0-1)
        wind_cooling_factor FLOAT,              -- Wind cooling effect factor (>1)
        air_mass FLOAT,                         -- Atmospheric air mass
        
        -- Data Source Information
        source VARCHAR(50) DEFAULT 'nasa_power',
        ingestion_run_id VARCHAR(255),
        
        -- Metadata
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        raw_data JSONB
    );
    """
    
    try:
        with conn.cursor() as cur:
            cur.execute(create_table_sql)
            logger.info("✅ nasa_power_data table created successfully")
            return True
    except Exception as e:
        logger.error(f"❌ Error creating nasa_power_data table: {e}")
        return False


def create_indexes(conn):
    """Create indexes for better performance"""
    
    indexes = [
        "CREATE INDEX IF NOT EXISTS idx_nasa_power_timestamp ON nasa_power_data(timestamp);",
        "CREATE INDEX IF NOT EXISTS idx_nasa_power_ghi ON nasa_power_data(ghi);",
        "CREATE INDEX IF NOT EXISTS idx_nasa_power_clearness_index ON nasa_power_data(clearness_index);",
        "CREATE INDEX IF NOT EXISTS idx_nasa_power_timestamp_ghi ON nasa_power_data(timestamp, ghi);",
        "CREATE INDEX IF NOT EXISTS idx_nasa_power_timestamp_kt ON nasa_power_data(timestamp, clearness_index);",
        "CREATE INDEX IF NOT EXISTS idx_nasa_power_source ON nasa_power_data(source);",
        "CREATE INDEX IF NOT EXISTS idx_nasa_power_ingestion_run ON nasa_power_data(ingestion_run_id);"
    ]
    
    try:
        with conn.cursor() as cur:
            for index_sql in indexes:
                cur.execute(index_sql)
                logger.info(f"✅ Index created: {index_sql.split('idx_')[1].split(' ')[0]}")
        return True
    except Exception as e:
        logger.error(f"❌ Error creating indexes: {e}")
        return False


def add_table_comments(conn):
    """Add comments to table and columns for documentation"""
    
    comments = [
        "COMMENT ON TABLE nasa_power_data IS 'NASA POWER data for enhanced solar prediction with physics-based features';",
        "COMMENT ON COLUMN nasa_power_data.ghi IS 'All Sky Surface Shortwave Downward Irradiance (W/m²)';",
        "COMMENT ON COLUMN nasa_power_data.clearness_index IS 'All Sky Clearness Index (KT) - ratio of actual to clear sky irradiance';",
        "COMMENT ON COLUMN nasa_power_data.module_temperature IS 'Calculated PV module temperature using NOCT model (°C)';",
        "COMMENT ON COLUMN nasa_power_data.temperature_efficiency_factor IS 'Temperature effect on PV efficiency (0-1)';",
        "COMMENT ON COLUMN nasa_power_data.wind_cooling_factor IS 'Wind cooling effect factor (>1 means cooling)';",
        "COMMENT ON COLUMN nasa_power_data.air_mass IS 'Atmospheric air mass coefficient';"
    ]
    
    try:
        with conn.cursor() as cur:
            for comment_sql in comments:
                cur.execute(comment_sql)
        logger.info("✅ Table comments added successfully")
        return True
    except Exception as e:
        logger.error(f"❌ Error adding table comments: {e}")
        return False


def verify_table_structure(conn):
    """Verify the created table structure"""
    
    try:
        with conn.cursor() as cur:
            # Get table structure
            cur.execute("""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns
                WHERE table_name = 'nasa_power_data'
                ORDER BY ordinal_position;
            """)
            
            columns = cur.fetchall()
            
            logger.info("📊 NASA POWER Table Structure")
            logger.info("=" * 50)
            for col in columns:
                logger.info(f"  {col[0]:<25} {col[1]:<15} {'NULL' if col[2] == 'YES' else 'NOT NULL':<10}")
            
            # Get indexes
            cur.execute("""
                SELECT indexname, indexdef
                FROM pg_indexes
                WHERE tablename = 'nasa_power_data';
            """)
            
            indexes = cur.fetchall()
            
            logger.info("\n📊 NASA POWER Table Indexes")
            logger.info("=" * 50)
            for idx in indexes:
                logger.info(f"  {idx[0]}")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ Error verifying table structure: {e}")
        return False


def check_existing_data_compatibility():
    """Check compatibility with existing weather data sources"""
    
    logger.info("🔍 Checking compatibility with existing data sources...")
    
    try:
        conn = get_db_connection()
        if not conn:
            return False
        
        with conn.cursor() as cur:
            # Check existing weather data
            cur.execute("SELECT COUNT(*) FROM weather_data;")
            weather_count = cur.fetchone()[0]
            
            # Check existing CAMS data
            cur.execute("SELECT COUNT(*) FROM cams_radiation_data;")
            cams_count = cur.fetchone()[0]
            
            logger.info(f"📊 Existing Data Sources")
            logger.info(f"  Open-Meteo (weather_data): {weather_count:,} records")
            logger.info(f"  CAMS (cams_radiation_data): {cams_count:,} records")
            logger.info(f"  NASA POWER (nasa_power_data): Will be populated")
            
        conn.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Error checking data compatibility: {e}")
        return False


def main():
    """Main migration function"""
    
    print("🚀 NASA POWER Database Migration")
    print("=" * 40)
    
    # Check existing data compatibility
    print("\n1️⃣ Checking existing data sources...")
    if not check_existing_data_compatibility():
        print("❌ Failed to check existing data sources")
        return False
    
    # Connect to database
    print("\n2️⃣ Connecting to database...")
    conn = get_db_connection()
    if not conn:
        print("❌ Failed to connect to database")
        return False
    
    try:
        # Check if table already exists
        print("\n3️⃣ Checking if table exists...")
        if check_table_exists(conn, 'nasa_power_data'):
            print("⚠️ nasa_power_data table already exists")
            proceed = input("Do you want to continue anyway? (y/n): ").lower().strip()
            if proceed != 'y':
                print("❌ Migration cancelled by user")
                return False
        
        # Create table
        print("\n4️⃣ Creating nasa_power_data table...")
        if not create_nasa_power_table(conn):
            print("❌ Failed to create table")
            return False
        
        # Create indexes
        print("\n5️⃣ Creating indexes...")
        if not create_indexes(conn):
            print("❌ Failed to create indexes")
            return False
        
        # Add comments
        print("\n6️⃣ Adding table documentation...")
        if not add_table_comments(conn):
            print("❌ Failed to add comments")
            return False
        
        # Verify structure
        print("\n7️⃣ Verifying table structure...")
        if not verify_table_structure(conn):
            print("❌ Failed to verify table structure")
            return False
        
        print("\n✅ NASA POWER database migration completed successfully!")
        print("\n🎯 Next steps:")
        print("   1. Run: python scripts/data/nasa_power_historical_collection.py")
        print("   2. Collect historical NASA POWER data")
        print("   3. Compare with existing weather sources")
        print("   4. Implement enhanced feature engineering")
        
        return True
        
    finally:
        conn.close()


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
