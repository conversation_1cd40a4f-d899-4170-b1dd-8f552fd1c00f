#!/usr/bin/env python3
"""
EVALUATE BACKUP DATA
Αξιολογεί τα database backups για καθαρά δεδομένα συστήματος
"""

import os
import gzip
import re
from datetime import datetime

def analyze_backup_file(backup_path):
    """Αναλύει ένα backup file για δεδομένα συστήματος"""
    
    print(f"\n📁 ANALYZING: {os.path.basename(backup_path)}")
    print("-" * 60)
    
    if not os.path.exists(backup_path):
        print(f"❌ File not found: {backup_path}")
        return None
    
    file_size = os.path.getsize(backup_path) / 1024 / 1024  # MB
    print(f"📊 File size: {file_size:.1f} MB")
    
    try:
        # Determine if file is compressed
        is_compressed = backup_path.endswith('.gz')
        
        if is_compressed:
            file_handle = gzip.open(backup_path, 'rt', encoding='utf-8')
        else:
            file_handle = open(backup_path, 'r', encoding='utf-8')
        
        # Analysis variables
        tables_found = {}
        solax_data_records = 0
        solax_data2_records = 0
        weather_data_records = 0
        date_range = {'min': None, 'max': None}
        billing_fields_present = False
        
        line_count = 0
        in_data_section = False
        current_table = None
        
        print("🔍 Scanning backup content...")
        
        for line in file_handle:
            line_count += 1
            
            # Limit scan to avoid memory issues
            if line_count > 100000:
                print("⚠️  Scan limited to first 100,000 lines")
                break
            
            line = line.strip()
            
            # Detect table creation
            if line.startswith("CREATE TABLE"):
                table_match = re.search(r'CREATE TABLE (?:public\.)?(\w+)', line)
                if table_match:
                    table_name = table_match.group(1)
                    tables_found[table_name] = {'created': True, 'records': 0}
            
            # Detect data copy start
            elif line.startswith("COPY public."):
                copy_match = re.search(r'COPY public\.(\w+)', line)
                if copy_match:
                    current_table = copy_match.group(1)
                    in_data_section = True
                    if current_table not in tables_found:
                        tables_found[current_table] = {'created': False, 'records': 0}
            
            # Detect data copy end
            elif line == "\\." and in_data_section:
                in_data_section = False
                current_table = None
            
            # Count records and analyze data
            elif in_data_section and current_table:
                tables_found[current_table]['records'] += 1
                
                # Analyze solax_data records
                if current_table == 'solax_data':
                    solax_data_records += 1
                    # Try to extract timestamp for date range
                    parts = line.split('\t')
                    if len(parts) > 1:
                        try:
                            # Timestamp is usually in the second column
                            timestamp_str = parts[1]
                            if timestamp_str and timestamp_str != '\\N':
                                timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                                if date_range['min'] is None or timestamp < date_range['min']:
                                    date_range['min'] = timestamp
                                if date_range['max'] is None or timestamp > date_range['max']:
                                    date_range['max'] = timestamp
                        except:
                            pass
                    
                    # Check for billing fields
                    if 'billing_benefit' in line or 'billing_cost' in line:
                        billing_fields_present = True
                
                elif current_table == 'solax_data2':
                    solax_data2_records += 1
                    # Similar analysis for solax_data2
                    parts = line.split('\t')
                    if len(parts) > 1:
                        try:
                            timestamp_str = parts[1]
                            if timestamp_str and timestamp_str != '\\N':
                                timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                                if date_range['min'] is None or timestamp < date_range['min']:
                                    date_range['min'] = timestamp
                                if date_range['max'] is None or timestamp > date_range['max']:
                                    date_range['max'] = timestamp
                        except:
                            pass
                
                elif current_table == 'weather_data':
                    weather_data_records += 1
        
        file_handle.close()
        
        # Print analysis results
        print(f"\n📋 TABLES FOUND: {len(tables_found)}")
        
        # Solar-related tables
        solar_tables = [t for t in tables_found.keys() if any(keyword in t.lower() for keyword in ['solax', 'solar', 'weather', 'prediction'])]
        if solar_tables:
            print(f"🌞 Solar-related tables: {len(solar_tables)}")
            for table in sorted(solar_tables):
                records = tables_found[table]['records']
                print(f"   {table}: {records:,} records")
        
        # Key metrics
        print(f"\n📊 KEY METRICS:")
        print(f"   solax_data records: {solax_data_records:,}")
        print(f"   solax_data2 records: {solax_data2_records:,}")
        print(f"   weather_data records: {weather_data_records:,}")
        
        if date_range['min'] and date_range['max']:
            total_days = (date_range['max'] - date_range['min']).days
            print(f"   Date range: {date_range['min'].date()} to {date_range['max'].date()}")
            print(f"   Total days: {total_days}")
            
            # Calculate average records per day
            if total_days > 0:
                avg_records_per_day = (solax_data_records + solax_data2_records) / total_days
                print(f"   Avg records/day: {avg_records_per_day:.1f}")
        
        print(f"   Billing fields present: {'Yes' if billing_fields_present else 'No'}")
        
        # Data quality assessment
        print(f"\n🎯 DATA QUALITY ASSESSMENT:")
        
        if solax_data_records > 10000 and solax_data2_records > 10000:
            print("   ✅ GOOD: Both systems have substantial data")
        elif solax_data_records > 1000 or solax_data2_records > 1000:
            print("   ⚠️  MODERATE: Limited data available")
        else:
            print("   ❌ POOR: Very little data available")
        
        if date_range['min'] and date_range['max']:
            if total_days > 300:
                print("   ✅ GOOD: Long-term data coverage (>300 days)")
            elif total_days > 100:
                print("   ⚠️  MODERATE: Medium-term data coverage (100-300 days)")
            else:
                print("   ❌ POOR: Short-term data coverage (<100 days)")
        
        if not billing_fields_present:
            print("   ✅ EXCELLENT: No corrupted billing fields")
        else:
            print("   ⚠️  WARNING: Billing fields present (may be corrupted)")
        
        return {
            'file_path': backup_path,
            'file_size_mb': file_size,
            'tables_found': tables_found,
            'solax_data_records': solax_data_records,
            'solax_data2_records': solax_data2_records,
            'weather_data_records': weather_data_records,
            'date_range': date_range,
            'total_days': total_days if date_range['min'] and date_range['max'] else 0,
            'billing_fields_present': billing_fields_present,
            'solar_tables': solar_tables
        }
        
    except Exception as e:
        print(f"❌ Error analyzing backup: {e}")
        return None

def main():
    """Main function to evaluate both backups"""
    
    print("💾 DATABASE BACKUP EVALUATION")
    print("=" * 80)
    print("🎯 Focus: System data quality (not billing fields)")
    
    backup_files = [
        "/home/<USER>/solar-prediction-project/data/Backup/performance_optimization_backup_20250416_160924_full.sql",
        "/home/<USER>/solar-prediction-project/data/Backup/solar_prediction_backup_20250604_130915.sql"
    ]
    
    results = []
    
    for backup_file in backup_files:
        result = analyze_backup_file(backup_file)
        if result:
            results.append(result)
    
    # Comparison and recommendation
    if len(results) >= 2:
        print(f"\n🔍 BACKUP COMPARISON")
        print("=" * 80)
        
        for i, result in enumerate(results, 1):
            backup_date = os.path.basename(result['file_path']).split('_')[2]
            print(f"\n📁 BACKUP {i} ({backup_date}):")
            print(f"   File: {os.path.basename(result['file_path'])}")
            print(f"   Size: {result['file_size_mb']:.1f} MB")
            print(f"   System 1 records: {result['solax_data_records']:,}")
            print(f"   System 2 records: {result['solax_data2_records']:,}")
            print(f"   Total days: {result['total_days']}")
            print(f"   Billing fields: {'Yes' if result['billing_fields_present'] else 'No'}")
            
            if result['date_range']['min'] and result['date_range']['max']:
                print(f"   Date range: {result['date_range']['min'].date()} to {result['date_range']['max'].date()}")
        
        # Recommendation
        print(f"\n🎯 RECOMMENDATION:")
        
        # Find best backup based on criteria
        best_backup = None
        best_score = -1
        
        for result in results:
            score = 0
            
            # More recent data gets higher score
            if '20250604' in result['file_path']:
                score += 3
            elif '20250416' in result['file_path']:
                score += 2
            
            # More records get higher score
            total_records = result['solax_data_records'] + result['solax_data2_records']
            if total_records > 200000:
                score += 3
            elif total_records > 100000:
                score += 2
            elif total_records > 50000:
                score += 1
            
            # Longer time period gets higher score
            if result['total_days'] > 300:
                score += 2
            elif result['total_days'] > 100:
                score += 1
            
            # No billing fields is better
            if not result['billing_fields_present']:
                score += 2
            
            if score > best_score:
                best_score = score
                best_backup = result
        
        if best_backup:
            print(f"   🏆 BEST BACKUP: {os.path.basename(best_backup['file_path'])}")
            print(f"   📊 Score: {best_score}/10")
            print(f"   ✅ Recommended for restore")
            
            print(f"\n📋 NEXT STEPS:")
            print(f"   1. Restore this backup to get clean system data")
            print(f"   2. Analyze the clean data for real consumption patterns")
            print(f"   3. Calculate correct ROI based on clean data")
            print(f"   4. Supplement with Excel exports if needed")
        
    print(f"\n🎉 BACKUP EVALUATION COMPLETED!")

if __name__ == "__main__":
    main()
