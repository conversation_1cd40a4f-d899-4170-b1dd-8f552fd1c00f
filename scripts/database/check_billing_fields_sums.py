#!/usr/bin/env python3
"""
Check Billing Fields Sums
Verify actual billing fields totals and compare with ROI calculations
"""

import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime, date

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 5433,
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

def get_db_connection():
    """Get database connection"""
    try:
        return psycopg2.connect(**DB_CONFIG)
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return None

def check_billing_fields_daily_sums():
    """Check billing fields sums for 24/6/2025"""
    print("\n" + "="*60)
    print("📊 BILLING FIELDS DAILY SUMS (24/6/2025)")
    print("="*60)
    
    conn = get_db_connection()
    if not conn:
        return None
    
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        
        target_date = date(2025, 6, 24)
        daily_totals = {}
        
        for table, system_name in [('solax_data', 'System 1'), ('solax_data2', 'System 2')]:
            print(f"\n🏠 {system_name} Billing Fields Sums:")
            
            # Get billing fields totals
            cur.execute(f"""
                SELECT 
                    COUNT(*) as total_records,
                    ROUND(SUM(COALESCE(billing_cost, 0))::numeric, 4) as total_cost,
                    ROUND(SUM(COALESCE(billing_benefit, 0))::numeric, 4) as total_benefit,
                    ROUND(AVG(COALESCE(billing_benefit, 0))::numeric, 6) as avg_benefit,
                    ROUND(MIN(COALESCE(billing_benefit, 0))::numeric, 6) as min_benefit,
                    ROUND(MAX(COALESCE(billing_benefit, 0))::numeric, 6) as max_benefit,
                    COUNT(CASE WHEN billing_benefit > 0 THEN 1 END) as records_with_benefit
                FROM {table}
                WHERE DATE(timestamp) = %s
            """, (target_date,))
            
            result = cur.fetchone()
            
            if result:
                total_cost = float(result['total_cost'] or 0)
                total_benefit = float(result['total_benefit'] or 0)
                net_benefit = total_benefit - total_cost
                
                print(f"   Total records: {result['total_records']:,}")
                print(f"   Total cost: €{total_cost:.4f}")
                print(f"   Total benefit: €{total_benefit:.4f}")
                print(f"   Net benefit: €{net_benefit:.4f}")
                print(f"   Average benefit: €{result['avg_benefit']:.6f}")
                print(f"   Benefit range: €{result['min_benefit']:.6f} - €{result['max_benefit']:.6f}")
                print(f"   Records with benefit: {result['records_with_benefit']:,}")
                
                daily_totals[system_name] = {
                    'total_cost': total_cost,
                    'total_benefit': total_benefit,
                    'net_benefit': net_benefit,
                    'records': result['total_records']
                }
            else:
                print(f"   ❌ No data found")
                daily_totals[system_name] = None
        
        # Combined totals
        if all(daily_totals.values()):
            combined_cost = sum(data['total_cost'] for data in daily_totals.values())
            combined_benefit = sum(data['total_benefit'] for data in daily_totals.values())
            combined_net = combined_benefit - combined_cost
            
            print(f"\n🎯 COMBINED BILLING FIELDS TOTALS:")
            print(f"   Combined cost: €{combined_cost:.4f}")
            print(f"   Combined benefit: €{combined_benefit:.4f}")
            print(f"   Combined net benefit: €{combined_net:.4f}")
            
            return {
                'combined_cost': combined_cost,
                'combined_benefit': combined_benefit,
                'combined_net': combined_net,
                'system_details': daily_totals
            }
        
        conn.close()
        return daily_totals
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def check_billing_fields_historical_sums():
    """Check billing fields sums for entire historical period"""
    print("\n" + "="*60)
    print("📈 BILLING FIELDS HISTORICAL SUMS")
    print("="*60)
    
    conn = get_db_connection()
    if not conn:
        return None
    
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        
        historical_totals = {}
        
        for table, system_name in [('solax_data', 'System 1'), ('solax_data2', 'System 2')]:
            print(f"\n🏠 {system_name} Historical Billing Sums:")
            
            # Get historical billing totals
            cur.execute(f"""
                SELECT 
                    COUNT(*) as total_records,
                    COUNT(DISTINCT DATE(timestamp)) as total_days,
                    MIN(timestamp) as start_date,
                    MAX(timestamp) as end_date,
                    ROUND(SUM(COALESCE(billing_cost, 0))::numeric, 2) as total_cost,
                    ROUND(SUM(COALESCE(billing_benefit, 0))::numeric, 2) as total_benefit,
                    ROUND(AVG(COALESCE(billing_benefit, 0))::numeric, 6) as avg_benefit_per_record,
                    COUNT(CASE WHEN billing_benefit > 0 THEN 1 END) as records_with_benefit
                FROM {table}
                WHERE timestamp >= '2024-01-01'
                AND billing_benefit IS NOT NULL
            """)
            
            result = cur.fetchone()
            
            if result:
                total_cost = float(result['total_cost'] or 0)
                total_benefit = float(result['total_benefit'] or 0)
                net_benefit = total_benefit - total_cost
                total_days = result['total_days']
                
                # Calculate daily averages
                avg_daily_cost = total_cost / total_days if total_days > 0 else 0
                avg_daily_benefit = total_benefit / total_days if total_days > 0 else 0
                avg_daily_net = net_benefit / total_days if total_days > 0 else 0
                
                print(f"   Period: {result['start_date']} to {result['end_date']}")
                print(f"   Total days: {total_days}")
                print(f"   Total records: {result['total_records']:,}")
                print(f"   Total cost: €{total_cost:.2f}")
                print(f"   Total benefit: €{total_benefit:.2f}")
                print(f"   Net benefit: €{net_benefit:.2f}")
                print(f"   Average daily cost: €{avg_daily_cost:.4f}")
                print(f"   Average daily benefit: €{avg_daily_benefit:.4f}")
                print(f"   Average daily net: €{avg_daily_net:.4f}")
                print(f"   Records with benefit: {result['records_with_benefit']:,}")
                
                historical_totals[system_name] = {
                    'total_cost': total_cost,
                    'total_benefit': total_benefit,
                    'net_benefit': net_benefit,
                    'total_days': total_days,
                    'avg_daily_cost': avg_daily_cost,
                    'avg_daily_benefit': avg_daily_benefit,
                    'avg_daily_net': avg_daily_net,
                    'start_date': result['start_date'],
                    'end_date': result['end_date']
                }
            else:
                print(f"   ❌ No historical data found")
                historical_totals[system_name] = None
        
        # Combined historical totals
        if all(historical_totals.values()):
            combined_cost = sum(data['total_cost'] for data in historical_totals.values())
            combined_benefit = sum(data['total_benefit'] for data in historical_totals.values())
            combined_net = combined_benefit - combined_cost
            avg_days = sum(data['total_days'] for data in historical_totals.values()) / 2
            
            combined_avg_daily_cost = combined_cost / avg_days if avg_days > 0 else 0
            combined_avg_daily_benefit = combined_benefit / avg_days if avg_days > 0 else 0
            combined_avg_daily_net = combined_net / avg_days if avg_days > 0 else 0
            
            print(f"\n🎯 COMBINED HISTORICAL BILLING TOTALS:")
            print(f"   Combined total cost: €{combined_cost:.2f}")
            print(f"   Combined total benefit: €{combined_benefit:.2f}")
            print(f"   Combined net benefit: €{combined_net:.2f}")
            print(f"   Combined avg daily cost: €{combined_avg_daily_cost:.4f}")
            print(f"   Combined avg daily benefit: €{combined_avg_daily_benefit:.4f}")
            print(f"   Combined avg daily net: €{combined_avg_daily_net:.4f}")
            
            return {
                'combined_cost': combined_cost,
                'combined_benefit': combined_benefit,
                'combined_net': combined_net,
                'combined_avg_daily_cost': combined_avg_daily_cost,
                'combined_avg_daily_benefit': combined_avg_daily_benefit,
                'combined_avg_daily_net': combined_avg_daily_net,
                'system_details': historical_totals
            }
        
        conn.close()
        return historical_totals
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def compare_billing_vs_roi_calculations(daily_billing, historical_billing):
    """Compare billing fields with ROI calculations"""
    print("\n" + "="*60)
    print("🔍 BILLING FIELDS vs ROI CALCULATIONS COMPARISON")
    print("="*60)
    
    # Real data from previous analysis
    real_daily_benefit = 6.34  # €6.34 from real data calculation
    real_annual_benefit = 1726.82  # €1,726.82 from ROI calculation
    
    print(f"\n📊 DAILY COMPARISON (24/6/2025):")
    if daily_billing:
        billing_daily_benefit = daily_billing['combined_benefit']
        daily_difference = abs(billing_daily_benefit - real_daily_benefit)
        daily_percentage = (daily_difference / real_daily_benefit * 100) if real_daily_benefit > 0 else 0
        
        print(f"   Real data calculation: €{real_daily_benefit:.2f}")
        print(f"   Billing fields sum: €{billing_daily_benefit:.2f}")
        print(f"   Difference: €{daily_difference:.2f} ({daily_percentage:.1f}%)")
        
        if daily_percentage < 10:
            print(f"   ✅ Daily billing fields are consistent with real data")
        else:
            print(f"   ❌ Daily billing fields differ significantly from real data")
    
    print(f"\n📈 ANNUAL COMPARISON:")
    if historical_billing:
        # Calculate annual benefit from historical billing
        billing_annual_benefit = historical_billing['combined_avg_daily_benefit'] * 365
        annual_difference = abs(billing_annual_benefit - real_annual_benefit)
        annual_percentage = (annual_difference / real_annual_benefit * 100) if real_annual_benefit > 0 else 0
        
        print(f"   Real data ROI calculation: €{real_annual_benefit:.2f}")
        print(f"   Billing fields annual projection: €{billing_annual_benefit:.2f}")
        print(f"   Difference: €{annual_difference:.2f} ({annual_percentage:.1f}%)")
        
        if annual_percentage < 20:
            print(f"   ✅ Annual billing fields are reasonably consistent")
        else:
            print(f"   ❌ Annual billing fields differ significantly from ROI calculation")
        
        # Calculate ROI from billing fields
        total_investment = 25000.0
        billing_roi = (billing_annual_benefit / total_investment) * 100
        real_roi = 6.91  # From previous calculation
        
        print(f"\n💰 ROI COMPARISON:")
        print(f"   Real data ROI: {real_roi:.2f}%")
        print(f"   Billing fields ROI: {billing_roi:.2f}%")
        print(f"   ROI difference: {abs(billing_roi - real_roi):.2f}%")

def identify_billing_calculation_issues():
    """Identify potential issues with billing calculations"""
    print("\n" + "="*60)
    print("🚨 BILLING CALCULATION ISSUES ANALYSIS")
    print("="*60)
    
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        
        target_date = date(2025, 6, 24)
        
        for table, system_name in [('solax_data', 'System 1'), ('solax_data2', 'System 2')]:
            print(f"\n🔍 {system_name} Issues Analysis:")
            
            # Check for unrealistic values
            cur.execute(f"""
                SELECT 
                    COUNT(CASE WHEN billing_benefit > 50 THEN 1 END) as high_benefit_records,
                    COUNT(CASE WHEN billing_benefit < 0 THEN 1 END) as negative_benefit_records,
                    COUNT(CASE WHEN billing_cost > 10 THEN 1 END) as high_cost_records,
                    COUNT(CASE WHEN yield_today > 100 THEN 1 END) as unrealistic_yield_records,
                    COUNT(CASE WHEN billing_benefit > 0 AND yield_today = 0 THEN 1 END) as benefit_without_yield,
                    COUNT(CASE WHEN billing_benefit = 0 AND yield_today > 0 THEN 1 END) as yield_without_benefit
                FROM {table}
                WHERE DATE(timestamp) = %s
            """, (target_date,))
            
            issues = cur.fetchone()
            
            if issues:
                print(f"   High benefit records (>€50): {issues['high_benefit_records']}")
                print(f"   Negative benefit records: {issues['negative_benefit_records']}")
                print(f"   High cost records (>€10): {issues['high_cost_records']}")
                print(f"   Unrealistic yield records (>100kWh): {issues['unrealistic_yield_records']}")
                print(f"   Benefit without yield: {issues['benefit_without_yield']}")
                print(f"   Yield without benefit: {issues['yield_without_benefit']}")
                
                total_issues = sum(issues.values())
                if total_issues == 0:
                    print(f"   ✅ No obvious data quality issues")
                else:
                    print(f"   ⚠️ Found {total_issues} potential data quality issues")
            
            # Sample some records to check calculation logic
            cur.execute(f"""
                SELECT 
                    timestamp,
                    yield_today,
                    billing_benefit,
                    billing_tariff,
                    billing_schedule,
                    soc
                FROM {table}
                WHERE DATE(timestamp) = %s
                AND billing_benefit > 0
                ORDER BY billing_benefit DESC
                LIMIT 5
            """, (target_date,))
            
            samples = cur.fetchall()
            
            print(f"   📋 Sample high benefit records:")
            for sample in samples:
                print(f"      {sample['timestamp'].strftime('%H:%M')} | Yield: {sample['yield_today']} | Benefit: €{sample['billing_benefit']:.4f} | Tariff: €{sample['billing_tariff']:.4f}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Main analysis function"""
    print("🔍 BILLING FIELDS SUMS CHECK")
    print("=" * 60)
    print("Checking actual billing fields totals vs ROI calculations")
    print(f"Time: {datetime.now()}")
    
    # Step 1: Check daily billing sums
    daily_billing = check_billing_fields_daily_sums()
    
    # Step 2: Check historical billing sums
    historical_billing = check_billing_fields_historical_sums()
    
    # Step 3: Compare with ROI calculations
    compare_billing_vs_roi_calculations(daily_billing, historical_billing)
    
    # Step 4: Identify potential issues
    identify_billing_calculation_issues()
    
    print("\n" + "="*60)
    print("🎯 BILLING FIELDS ANALYSIS COMPLETED")
    print("="*60)
    print("✅ Checked actual billing fields sums")
    print("✅ Compared with ROI calculations")
    print("✅ Identified potential calculation issues")
    print("\n📝 Now we can see where the discrepancy lies!")

if __name__ == "__main__":
    main()
