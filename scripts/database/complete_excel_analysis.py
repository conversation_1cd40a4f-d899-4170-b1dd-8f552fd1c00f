#!/usr/bin/env python3
"""
COMPLETE EXCEL ANALYSIS
Πλήρη ανάλυση όλων των Excel files με σωστή daily difference calculation
"""

import pandas as pd
import os
import glob
from datetime import datetime, timedelta
import numpy as np

def process_excel_file(filepath, system_name):
    """Επεξεργάζεται ένα Excel file με σωστή daily difference calculation"""
    
    filename = os.path.basename(filepath)
    print(f"\n📁 {system_name}: {filename}")
    print("-" * 60)
    
    try:
        # Read Excel file
        df = pd.read_excel(filepath, skiprows=1)
        
        # Handle different column formats
        if len(df.columns) == 8:
            # Old format (8 columns)
            df.columns = [
                'no', 'timestamp', 'daily_pv_yield_kwh', 'daily_inverter_output_kwh', 
                'daily_exported_energy_kwh', 'daily_imported_energy_kwh', 
                'export_power_w', 'daily_consumed_kwh'
            ]
        elif len(df.columns) == 7:
            # New format (7 columns)
            df.columns = [
                'no', 'timestamp', 'daily_pv_yield_kwh', 'daily_inverter_output_kwh',
                'daily_exported_energy_kwh', 'daily_consumed_kwh', 'daily_imported_energy_kwh'
            ]
        else:
            print(f"   ⚠️  Unexpected column count: {len(df.columns)}")
            return None
        
        # Convert timestamp and sort
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df = df.sort_values('timestamp').reset_index(drop=True)
        
        # Add date column for grouping
        df['date'] = df['timestamp'].dt.date
        
        print(f"   📊 Total records: {len(df):,}")
        print(f"   📅 Period: {df['timestamp'].min().date()} to {df['timestamp'].max().date()}")
        
        # Calculate daily differences (reset logic)
        daily_data = []
        
        for date in df['date'].unique():
            day_df = df[df['date'] == date].copy()
            
            if len(day_df) == 0:
                continue
            
            # Find reset points (where values decrease significantly)
            day_df['prev_yield'] = day_df['daily_pv_yield_kwh'].shift(1)
            day_df['is_reset'] = (day_df['daily_pv_yield_kwh'] < day_df['prev_yield']) & \
                               ((day_df['prev_yield'] - day_df['daily_pv_yield_kwh']) > 5)
            
            # Group by reset periods
            day_df['reset_group'] = day_df['is_reset'].cumsum()
            
            # Calculate production for each reset group
            daily_production = 0
            daily_exported = 0
            daily_consumed = 0
            daily_imported = 0
            
            for group in day_df['reset_group'].unique():
                group_df = day_df[day_df['reset_group'] == group]
                
                if len(group_df) > 0:
                    # Calculate differences for this group
                    group_production = group_df['daily_pv_yield_kwh'].max() - group_df['daily_pv_yield_kwh'].min()
                    group_exported = group_df['daily_exported_energy_kwh'].max() - group_df['daily_exported_energy_kwh'].min()
                    group_consumed = group_df['daily_consumed_kwh'].max() - group_df['daily_consumed_kwh'].min()
                    group_imported = group_df['daily_imported_energy_kwh'].max() - group_df['daily_imported_energy_kwh'].min()
                    
                    # Only add positive differences
                    if group_production > 0:
                        daily_production += group_production
                    if group_exported > 0:
                        daily_exported += group_exported
                    if group_consumed > 0:
                        daily_consumed += group_consumed
                    if group_imported > 0:
                        daily_imported += group_imported
            
            # Calculate self-consumption
            daily_self_consumption = daily_production - daily_exported
            
            # Store daily data
            if daily_production > 0:  # Only include days with production
                daily_data.append({
                    'date': date,
                    'production': daily_production,
                    'exported': daily_exported,
                    'consumed': daily_consumed,
                    'imported': daily_imported,
                    'self_consumption': daily_self_consumption,
                    'records': len(day_df)
                })
        
        if not daily_data:
            print(f"   ❌ No valid daily data found")
            return None
        
        # Convert to DataFrame
        daily_df = pd.DataFrame(daily_data)
        
        # Calculate totals
        total_days = len(daily_df)
        total_production = daily_df['production'].sum()
        total_exported = daily_df['exported'].sum()
        total_consumed = daily_df['consumed'].sum()
        total_imported = daily_df['imported'].sum()
        total_self_consumption = daily_df['self_consumption'].sum()
        
        # Calculate rates
        self_consumption_rate = (total_self_consumption / total_production * 100) if total_production > 0 else 0
        solar_coverage = (total_self_consumption / total_consumed * 100) if total_consumed > 0 else 0
        
        print(f"\n   ⚡ CORRECTED ENERGY ANALYSIS:")
        print(f"      Valid days: {total_days}")
        print(f"      Total production: {total_production:.2f} kWh")
        print(f"      Total exported: {total_exported:.2f} kWh")
        print(f"      Total consumed: {total_consumed:.2f} kWh")
        print(f"      Total imported: {total_imported:.2f} kWh")
        print(f"      Self-consumption: {total_self_consumption:.2f} kWh")
        print(f"      Self-consumption rate: {self_consumption_rate:.1f}%")
        print(f"      Solar coverage: {solar_coverage:.1f}%")
        
        # Daily averages
        avg_production = total_production / total_days if total_days > 0 else 0
        avg_self_consumption = total_self_consumption / total_days if total_days > 0 else 0
        
        print(f"\n   📈 DAILY AVERAGES:")
        print(f"      Production: {avg_production:.2f} kWh/day")
        print(f"      Self-consumption: {avg_self_consumption:.2f} kWh/day")
        
        return {
            'filepath': filepath,
            'filename': filename,
            'system': system_name,
            'total_days': total_days,
            'start_date': daily_df['date'].min(),
            'end_date': daily_df['date'].max(),
            'total_production': total_production,
            'total_exported': total_exported,
            'total_consumed': total_consumed,
            'total_imported': total_imported,
            'total_self_consumption': total_self_consumption,
            'self_consumption_rate': self_consumption_rate,
            'solar_coverage': solar_coverage,
            'avg_production': avg_production,
            'avg_self_consumption': avg_self_consumption,
            'daily_data': daily_df
        }
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return None

def main():
    """Πλήρη ανάλυση όλων των Excel files"""
    
    print("📊 COMPLETE EXCEL ANALYSIS WITH CORRECT CALCULATIONS")
    print("=" * 80)
    
    # Define all Excel file locations with system mapping
    file_mappings = [
        # System 1 files
        ("/home/<USER>/solar-prediction-project/data/raw/System1/*.xlsx", "System 1"),
        ("/home/<USER>/solar-prediction-project/data/raw/new/System1*.xlsx", "System 1"),
        ("/home/<USER>/solar-prediction-project/data/raw/new2/Plant Reports 2025-02-26-2025-06-25.xlsx", "System 1"),
        
        # System 2 files  
        ("/home/<USER>/solar-prediction-project/data/raw/System2/*.xlsx", "System 2"),
        ("/home/<USER>/solar-prediction-project/data/raw/new/System 2*.xlsx", "System 2"),
        ("/home/<USER>/solar-prediction-project/data/raw/new2/Plant Reports 2025-02-26-2025-06-25 (1).xlsx", "System 2"),
    ]
    
    # Collect all files
    all_results = []
    
    for pattern, system_name in file_mappings:
        if '*' in pattern:
            files = glob.glob(pattern)
        else:
            files = [pattern] if os.path.exists(pattern) else []
        
        for filepath in sorted(files):
            result = process_excel_file(filepath, system_name)
            if result:
                all_results.append(result)
    
    # Handle unclassified files in /data/raw/
    unclassified_files = [
        "/home/<USER>/solar-prediction-project/data/raw/Plant Reports 2025-04-16-2025-05-30.xlsx",
        "/home/<USER>/solar-prediction-project/data/raw/Plant Reports 2025-04-16-2025-05-30 (1).xlsx"
    ]
    
    print(f"\n🔍 PROCESSING UNCLASSIFIED FILES:")
    print("Based on filename patterns:")
    print("- File without '(1)' suffix → System 1")
    print("- File with '(1)' suffix → System 2")
    
    for filepath in unclassified_files:
        if os.path.exists(filepath):
            system_name = "System 2" if "(1)" in filepath else "System 1"
            result = process_excel_file(filepath, system_name)
            if result:
                all_results.append(result)
    
    # Group results by system
    system1_results = [r for r in all_results if r['system'] == 'System 1']
    system2_results = [r for r in all_results if r['system'] == 'System 2']
    
    print(f"\n🎯 SYSTEM SUMMARIES")
    print("=" * 80)
    
    systems_summary = {}
    
    for system_name, results in [("System 1", system1_results), ("System 2", system2_results)]:
        if not results:
            print(f"\n🏠 {system_name}: No data found")
            continue
            
        print(f"\n🏠 {system_name}:")
        print(f"   Files processed: {len(results)}")
        
        # Combine all daily data
        all_daily_data = []
        for result in results:
            all_daily_data.extend(result['daily_data'].to_dict('records'))
        
        if all_daily_data:
            combined_df = pd.DataFrame(all_daily_data)
            
            # Remove duplicates by date (keep last)
            combined_df = combined_df.drop_duplicates(subset=['date'], keep='last')
            combined_df = combined_df.sort_values('date')
            
            # Calculate totals
            total_days = len(combined_df)
            total_production = combined_df['production'].sum()
            total_exported = combined_df['exported'].sum()
            total_consumed = combined_df['consumed'].sum()
            total_imported = combined_df['imported'].sum()
            total_self_consumption = combined_df['self_consumption'].sum()
            
            # Calculate rates
            self_consumption_rate = (total_self_consumption / total_production * 100) if total_production > 0 else 0
            
            # Date range
            start_date = combined_df['date'].min()
            end_date = combined_df['date'].max()
            
            print(f"   📅 Period: {start_date} to {end_date}")
            print(f"   📅 Total days: {total_days}")
            print(f"   ⚡ Total production: {total_production:.2f} kWh")
            print(f"   🏠 Total self-consumption: {total_self_consumption:.2f} kWh")
            print(f"   📊 Self-consumption rate: {self_consumption_rate:.1f}%")
            print(f"   📈 Avg daily production: {total_production/total_days:.2f} kWh/day")
            print(f"   📈 Avg daily self-consumption: {total_self_consumption/total_days:.2f} kWh/day")
            
            systems_summary[system_name] = {
                'total_days': total_days,
                'start_date': start_date,
                'end_date': end_date,
                'total_production': total_production,
                'total_self_consumption': total_self_consumption,
                'self_consumption_rate': self_consumption_rate,
                'avg_daily_production': total_production/total_days,
                'avg_daily_self_consumption': total_self_consumption/total_days
            }
    
    # Combined analysis
    if len(systems_summary) >= 2:
        print(f"\n🎯 COMBINED ANALYSIS")
        print("=" * 80)
        
        combined_production = sum(data['total_production'] for data in systems_summary.values())
        combined_self_consumption = sum(data['total_self_consumption'] for data in systems_summary.values())
        
        earliest_date = min(data['start_date'] for data in systems_summary.values())
        latest_date = max(data['end_date'] for data in systems_summary.values())
        
        print(f"   📅 Combined period: {earliest_date} to {latest_date}")
        print(f"   ⚡ Combined production: {combined_production:.2f} kWh")
        print(f"   🏠 Combined self-consumption: {combined_self_consumption:.2f} kWh")
        
        if combined_production > 0:
            combined_self_consumption_rate = (combined_self_consumption / combined_production * 100)
            print(f"   📊 Combined self-consumption rate: {combined_self_consumption_rate:.1f}%")
        
        # Financial analysis with correct rates
        print(f"\n💰 FINANCIAL ANALYSIS")
        print("-" * 40)
        
        # Use dynamic billing rates (seasonal/time-based average)
        total_rate = 0.1659  # €/kWh (energy + network + ETMEAR)
        annual_savings = combined_self_consumption * total_rate
        
        print(f"   🏠 Self-consumption: {combined_self_consumption:.2f} kWh")
        print(f"   💡 Rate: €{total_rate:.4f}/kWh (dynamic average)")
        print(f"   💰 Total savings: €{annual_savings:.2f}")
        
        # ROI calculation
        investment = 25000  # €25,000 for both systems
        roi_percentage = (annual_savings / investment * 100)
        payback_years = investment / annual_savings if annual_savings > 0 else float('inf')
        
        print(f"   💸 Investment: €{investment:,}")
        print(f"   📈 ROI: {roi_percentage:.2f}%")
        print(f"   ⏰ Payback: {payback_years:.1f} years")
        
        # Comparison with grid costs
        print(f"\n📊 GRID COST COMPARISON:")
        grid_day_rate = 0.142  # €/kWh day rate
        grid_cost_if_bought = combined_self_consumption * grid_day_rate
        net_savings = annual_savings - 0  # Solar is free
        
        print(f"   💸 Cost if bought from grid: €{grid_cost_if_bought:.2f}")
        print(f"   💰 Actual cost (solar): €0.00")
        print(f"   💵 Net savings: €{net_savings:.2f}")
        print(f"   📊 Savings rate: 100.0%")
        
        # Data coverage assessment
        total_period_days = (latest_date - earliest_date).days + 1
        print(f"\n📅 DATA COVERAGE:")
        print(f"   Period span: {total_period_days} days")
        print(f"   Coverage: Complete Excel data available")
        print(f"   Quality: Processed with correct daily difference calculation")
    
    print(f"\n🎉 COMPLETE ANALYSIS FINISHED!")
    print("✅ All Excel files processed with correct calculations")
    print("✅ Daily difference calculation applied")
    print("✅ Realistic values obtained")
    print("✅ Financial analysis completed")

if __name__ == "__main__":
    main()
