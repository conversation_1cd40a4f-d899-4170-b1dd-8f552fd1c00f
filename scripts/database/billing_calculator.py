#!/usr/bin/env python3
"""
BILLING CALCULATOR
Dynamic cost/benefit calculation χωρίς billing fields στη βάση
"""

from datetime import datetime, time
import calendar

class BillingCalculator:
    """Υπολογιστής δυναμικού κόστους/οφέλους"""
    
    def __init__(self):
        """Αρχικοποίηση με τα ελληνικά tariffs 2025"""
        
        # Base energy rates (€/kWh)
        self.winter_day_rate = 0.142    # November-March, day hours
        self.winter_night_rate = 0.120  # November-March, night hours
        self.summer_day_rate = 0.142    # April-October, day hours  
        self.summer_night_rate = 0.132  # April-October, night hours
        
        # Additional charges
        self.etmear_rate = 0.017        # €/kWh ETMEAR
        self.vat_rate = 0.24            # 24% VAT
        
        # Network charges (tiered, €/kWh)
        self.network_tier1_rate = 0.0089  # First 1600 kWh/year
        self.network_tier2_rate = 0.0178  # Above 1600 kWh/year
        self.network_tier1_limit = 1600   # kWh/year
        
        # Night hours by season (varies seasonally)
        self.winter_night_start = time(23, 0)   # 23:00
        self.winter_night_end = time(7, 0)      # 07:00
        self.summer_night_start = time(23, 30)  # 23:30
        self.summer_night_end = time(7, 0)      # 07:00
    
    def is_winter(self, timestamp):
        """Ελέγχει αν η ημερομηνία είναι χειμώνας (Νοέμβριος-Μάρτιος)"""
        month = timestamp.month
        return month in [11, 12, 1, 2, 3]
    
    def is_night_hours(self, timestamp):
        """Ελέγχει αν η ώρα είναι νυχτερινή (εποχιακά)"""
        time_of_day = timestamp.time()
        
        if self.is_winter(timestamp):
            # Winter night hours: 23:00-07:00
            return time_of_day >= self.winter_night_start or time_of_day < self.winter_night_end
        else:
            # Summer night hours: 23:30-07:00
            return time_of_day >= self.summer_night_start or time_of_day < self.summer_night_end
    
    def get_base_energy_rate(self, timestamp):
        """Επιστρέφει το βασικό energy rate για timestamp"""
        
        if self.is_winter(timestamp):
            if self.is_night_hours(timestamp):
                return self.winter_night_rate
            else:
                return self.winter_day_rate
        else:
            if self.is_night_hours(timestamp):
                return self.summer_night_rate
            else:
                return self.summer_day_rate
    
    def get_network_charge(self, annual_consumption_kwh):
        """Υπολογίζει το network charge βάσει ετήσιας κατανάλωσης"""
        
        if annual_consumption_kwh <= self.network_tier1_limit:
            return self.network_tier1_rate
        else:
            # Weighted average για mixed tier consumption
            tier1_portion = self.network_tier1_limit / annual_consumption_kwh
            tier2_portion = 1 - tier1_portion
            
            weighted_rate = (tier1_portion * self.network_tier1_rate + 
                           tier2_portion * self.network_tier2_rate)
            return weighted_rate
    
    def calculate_total_rate(self, timestamp, annual_consumption_kwh=3000):
        """Υπολογίζει το συνολικό rate (energy + network + ETMEAR + VAT)"""
        
        # Base energy rate
        base_rate = self.get_base_energy_rate(timestamp)
        
        # Network charge
        network_charge = self.get_network_charge(annual_consumption_kwh)
        
        # Total before VAT
        subtotal = base_rate + network_charge + self.etmear_rate
        
        # Add VAT
        total_rate = subtotal * (1 + self.vat_rate)
        
        return {
            'base_energy_rate': base_rate,
            'network_charge': network_charge,
            'etmear': self.etmear_rate,
            'subtotal': subtotal,
            'vat_amount': subtotal * self.vat_rate,
            'total_rate': total_rate,
            'season': 'winter' if self.is_winter(timestamp) else 'summer',
            'time_period': 'night' if self.is_night_hours(timestamp) else 'day'
        }
    
    def calculate_savings(self, timestamp, self_consumption_kwh, annual_consumption_kwh=3000):
        """Υπολογίζει την εξοικονόμηση από self-consumption"""
        
        if self_consumption_kwh <= 0:
            return {
                'self_consumption_kwh': 0,
                'total_rate': 0,
                'savings_euro': 0,
                'rate_breakdown': {}
            }
        
        rate_info = self.calculate_total_rate(timestamp, annual_consumption_kwh)
        savings_euro = self_consumption_kwh * rate_info['total_rate']
        
        return {
            'self_consumption_kwh': self_consumption_kwh,
            'total_rate': rate_info['total_rate'],
            'savings_euro': savings_euro,
            'rate_breakdown': rate_info
        }
    
    def calculate_period_savings(self, data_records, annual_consumption_kwh=3000):
        """Υπολογίζει συνολικές εξοικονομήσεις για περίοδο"""
        
        total_savings = 0
        total_self_consumption = 0
        daily_breakdown = []
        
        for record in data_records:
            timestamp = record['timestamp']
            self_consumption = record.get('self_consumption_kwh', 0)
            
            if self_consumption > 0:
                savings_info = self.calculate_savings(timestamp, self_consumption, annual_consumption_kwh)
                
                total_savings += savings_info['savings_euro']
                total_self_consumption += self_consumption
                
                daily_breakdown.append({
                    'timestamp': timestamp,
                    'self_consumption_kwh': self_consumption,
                    'rate': savings_info['total_rate'],
                    'savings_euro': savings_info['savings_euro'],
                    'season': savings_info['rate_breakdown']['season'],
                    'time_period': savings_info['rate_breakdown']['time_period']
                })
        
        # Calculate average rate
        avg_rate = total_savings / total_self_consumption if total_self_consumption > 0 else 0
        
        return {
            'total_self_consumption_kwh': total_self_consumption,
            'total_savings_euro': total_savings,
            'average_rate': avg_rate,
            'records_processed': len(data_records),
            'records_with_savings': len([r for r in daily_breakdown if r['savings_euro'] > 0]),
            'daily_breakdown': daily_breakdown
        }

def test_billing_calculator():
    """Test function για τον billing calculator"""
    
    print("🧮 TESTING BILLING CALCULATOR")
    print("=" * 60)
    
    calc = BillingCalculator()
    
    # Test different scenarios
    test_cases = [
        {
            'name': 'Winter Day',
            'timestamp': datetime(2025, 1, 15, 14, 0),  # January 15, 14:00
            'self_consumption': 10.0
        },
        {
            'name': 'Winter Night', 
            'timestamp': datetime(2025, 1, 15, 2, 0),   # January 15, 02:00
            'self_consumption': 5.0
        },
        {
            'name': 'Summer Day',
            'timestamp': datetime(2025, 6, 15, 14, 0),  # June 15, 14:00
            'self_consumption': 15.0
        },
        {
            'name': 'Summer Night',
            'timestamp': datetime(2025, 6, 15, 2, 0),   # June 15, 02:00
            'self_consumption': 8.0
        }
    ]
    
    for test_case in test_cases:
        print(f"\n📊 {test_case['name']}:")
        
        result = calc.calculate_savings(
            test_case['timestamp'], 
            test_case['self_consumption']
        )
        
        breakdown = result['rate_breakdown']
        
        print(f"   Timestamp: {test_case['timestamp']}")
        print(f"   Self-consumption: {test_case['self_consumption']} kWh")
        print(f"   Season: {breakdown['season']}")
        print(f"   Time period: {breakdown['time_period']}")
        print(f"   Base energy rate: €{breakdown['base_energy_rate']:.4f}/kWh")
        print(f"   Network charge: €{breakdown['network_charge']:.4f}/kWh")
        print(f"   ETMEAR: €{breakdown['etmear']:.4f}/kWh")
        print(f"   VAT: €{breakdown['vat_amount']:.4f}/kWh")
        print(f"   Total rate: €{breakdown['total_rate']:.4f}/kWh")
        print(f"   Savings: €{result['savings_euro']:.4f}")
    
    print(f"\n🎉 BILLING CALCULATOR TEST COMPLETED!")

def analyze_database_with_dynamic_billing():
    """Αναλύει τα δεδομένα της βάσης με dynamic billing"""

    import psycopg2
    from psycopg2.extras import RealDictCursor

    print("📊 ANALYZING DATABASE WITH DYNAMIC BILLING")
    print("=" * 80)

    # Database configuration
    DB_CONFIG = {
        'host': 'localhost',
        'port': 5433,
        'database': 'solar_prediction',
        'user': 'postgres',
        'password': 'postgres'
    }

    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor(cursor_factory=RealDictCursor)

        print("✅ Connected to database")

        calc = BillingCalculator()

        # Analyze each system
        systems = [
            {'table': 'solax_data', 'name': 'System 1'},
            {'table': 'solax_data2', 'name': 'System 2'}
        ]

        total_combined_savings = 0
        total_combined_self_consumption = 0

        for system in systems:
            table_name = system['table']
            system_name = system['name']

            print(f"\n🏠 {system_name} ({table_name}):")
            print("-" * 40)

            # Get daily data with self-consumption calculation
            cur.execute(f"""
                WITH daily_data AS (
                    SELECT
                        DATE(timestamp) as date,
                        MAX(yield_today) - MIN(yield_today) as daily_production,
                        MAX(feedin_energy) - MIN(feedin_energy) as daily_exported,
                        MAX(consume_energy) - MIN(consume_energy) as daily_consumed,
                        COUNT(*) as records_count
                    FROM {table_name}
                    WHERE timestamp >= '2024-03-01'
                    AND yield_today >= 0
                    GROUP BY DATE(timestamp)
                    HAVING MAX(yield_today) - MIN(yield_today) > 0
                )
                SELECT
                    date,
                    daily_production,
                    daily_exported,
                    daily_consumed,
                    (daily_production - daily_exported) as daily_self_consumption,
                    records_count
                FROM daily_data
                WHERE daily_production > 0
                ORDER BY date
            """)

            daily_records = cur.fetchall()

            if not daily_records:
                print(f"   ❌ No valid daily data found")
                continue

            print(f"   📅 Valid days: {len(daily_records)}")
            print(f"   📅 Period: {daily_records[0]['date']} to {daily_records[-1]['date']}")

            # Calculate savings for each day using dynamic billing
            daily_savings_data = []

            for record in daily_records:
                # Use noon time for daily rate calculation
                timestamp = datetime.combine(record['date'], time(12, 0))
                self_consumption = float(record['daily_self_consumption']) if record['daily_self_consumption'] else 0

                if self_consumption > 0:
                    savings_info = calc.calculate_savings(timestamp, self_consumption)
                    daily_savings_data.append({
                        'timestamp': timestamp,
                        'self_consumption_kwh': self_consumption,
                        'savings_euro': savings_info['savings_euro'],
                        'rate': savings_info['total_rate']
                    })

            # Calculate totals
            total_self_consumption = sum(r['self_consumption_kwh'] for r in daily_savings_data)
            total_savings = sum(r['savings_euro'] for r in daily_savings_data)
            avg_rate = total_savings / total_self_consumption if total_self_consumption > 0 else 0

            print(f"   ⚡ Total production: {sum(float(r['daily_production']) for r in daily_records):.2f} kWh")
            print(f"   📤 Total exported: {sum(float(r['daily_exported']) for r in daily_records):.2f} kWh")
            print(f"   🏠 Total self-consumption: {total_self_consumption:.2f} kWh")
            print(f"   💰 Total savings (dynamic): €{total_savings:.2f}")
            print(f"   📊 Average rate: €{avg_rate:.4f}/kWh")
            print(f"   📈 Daily avg self-consumption: {total_self_consumption/len(daily_records):.2f} kWh/day")

            total_combined_savings += total_savings
            total_combined_self_consumption += total_self_consumption

        # Combined analysis
        print(f"\n🎯 COMBINED ANALYSIS")
        print("=" * 40)

        combined_avg_rate = total_combined_savings / total_combined_self_consumption if total_combined_self_consumption > 0 else 0

        print(f"   🏠 Combined self-consumption: {total_combined_self_consumption:.2f} kWh")
        print(f"   💰 Combined savings (dynamic): €{total_combined_savings:.2f}")
        print(f"   📊 Combined average rate: €{combined_avg_rate:.4f}/kWh")

        # ROI calculation
        investment = 25000  # €25,000 for both systems
        roi_percentage = (total_combined_savings / investment * 100)
        payback_years = investment / total_combined_savings if total_combined_savings > 0 else float('inf')

        print(f"\n💰 ROI ANALYSIS (DYNAMIC BILLING)")
        print("-" * 40)
        print(f"   💸 Investment: €{investment:,}")
        print(f"   💰 Annual savings: €{total_combined_savings:.2f}")
        print(f"   📈 ROI: {roi_percentage:.2f}%")
        print(f"   ⏰ Payback: {payback_years:.1f} years")

        # Comparison with previous static calculation
        static_rate = 0.1659  # Previous static rate
        static_savings = total_combined_self_consumption * static_rate
        static_roi = (static_savings / investment * 100)

        print(f"\n📊 COMPARISON: DYNAMIC vs STATIC")
        print("-" * 40)
        print(f"   Dynamic billing: €{total_combined_savings:.2f} (€{combined_avg_rate:.4f}/kWh)")
        print(f"   Static billing: €{static_savings:.2f} (€{static_rate:.4f}/kWh)")
        print(f"   Difference: €{total_combined_savings - static_savings:+.2f}")
        print(f"   Dynamic ROI: {roi_percentage:.2f}%")
        print(f"   Static ROI: {static_roi:.2f}%")
        print(f"   ROI difference: {roi_percentage - static_roi:+.2f}%")

        conn.close()

        print(f"\n🎉 DYNAMIC BILLING ANALYSIS COMPLETED!")
        print("✅ Clean data from database")
        print("✅ Dynamic seasonal/time-based rates")
        print("✅ Accurate ROI calculation")

    except Exception as e:
        print(f"❌ Error: {e}")
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    # test_billing_calculator()
    analyze_database_with_dynamic_billing()
