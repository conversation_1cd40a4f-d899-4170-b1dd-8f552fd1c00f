#!/usr/bin/env python3
"""
ANALYZE ALL EXCEL FILES
Αναλύει όλα τα Excel files για να δει την πλήρη κάλυψη δεδομένων
"""

import pandas as pd
import os
from datetime import datetime, timedelta
import glob

def analyze_excel_file(filepath):
    """Αναλύει ένα Excel file"""
    
    filename = os.path.basename(filepath)
    print(f"\n📁 {filename}")
    print("-" * 60)
    
    try:
        # Read Excel file
        df = pd.read_excel(filepath, skiprows=1)
        
        # Clean column names
        if len(df.columns) >= 8:
            df.columns = [
                'no',
                'timestamp', 
                'daily_pv_yield_kwh',
                'daily_inverter_output_kwh', 
                'daily_exported_energy_kwh',
                'daily_imported_energy_kwh',
                'export_power_w',
                'daily_consumed_kwh'
            ]
        else:
            print(f"   ⚠️  Unexpected column count: {len(df.columns)}")
            print(f"   Columns: {list(df.columns)}")
            return None
        
        # Convert timestamp
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        # Basic info
        total_records = len(df)
        start_date = df['timestamp'].min()
        end_date = df['timestamp'].max()
        total_days = (end_date - start_date).days + 1
        
        print(f"   📊 Records: {total_records:,}")
        print(f"   📅 Period: {start_date.date()} to {end_date.date()}")
        print(f"   📅 Total days: {total_days}")
        print(f"   📈 Records/day: {total_records/total_days:.1f}")
        
        # Data analysis
        total_production = df['daily_pv_yield_kwh'].sum()
        total_exported = df['daily_exported_energy_kwh'].sum()
        total_consumed = df['daily_consumed_kwh'].sum()
        total_self_consumption = total_production - total_exported
        
        print(f"\n   ⚡ ENERGY ANALYSIS:")
        print(f"      Total production: {total_production:.2f} kWh")
        print(f"      Total exported: {total_exported:.2f} kWh")
        print(f"      Total consumed: {total_consumed:.2f} kWh")
        print(f"      Self-consumption: {total_self_consumption:.2f} kWh")
        
        if total_production > 0:
            self_consumption_rate = (total_self_consumption / total_production * 100)
            print(f"      Self-consumption rate: {self_consumption_rate:.1f}%")
        
        if total_consumed > 0:
            solar_coverage = (total_self_consumption / total_consumed * 100)
            print(f"      Solar coverage: {solar_coverage:.1f}%")
        
        # Daily averages
        avg_production = total_production / total_days
        avg_self_consumption = total_self_consumption / total_days
        
        print(f"\n   📈 DAILY AVERAGES:")
        print(f"      Production: {avg_production:.2f} kWh/day")
        print(f"      Self-consumption: {avg_self_consumption:.2f} kWh/day")
        
        return {
            'filepath': filepath,
            'filename': filename,
            'records': total_records,
            'start_date': start_date,
            'end_date': end_date,
            'total_days': total_days,
            'total_production': total_production,
            'total_exported': total_exported,
            'total_consumed': total_consumed,
            'total_self_consumption': total_self_consumption,
            'self_consumption_rate': self_consumption_rate if total_production > 0 else 0,
            'avg_production': avg_production,
            'avg_self_consumption': avg_self_consumption
        }
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return None

def main():
    """Αναλύει όλα τα Excel files"""
    
    print("📊 COMPLETE EXCEL FILES ANALYSIS")
    print("=" * 80)
    
    # Define all Excel file locations
    excel_locations = [
        "/home/<USER>/solar-prediction-project/data/raw/System1/*.xlsx",
        "/home/<USER>/solar-prediction-project/data/raw/System2/*.xlsx", 
        "/home/<USER>/solar-prediction-project/data/raw/new/*.xlsx",
        "/home/<USER>/solar-prediction-project/data/raw/*.xlsx"
    ]
    
    all_files = []
    for location in excel_locations:
        files = glob.glob(location)
        all_files.extend(files)
    
    # Remove duplicates and sort
    all_files = sorted(list(set(all_files)))
    
    print(f"🔍 Found {len(all_files)} Excel files")
    
    # Separate by system
    system1_files = []
    system2_files = []
    
    for filepath in all_files:
        filename = os.path.basename(filepath).lower()
        path = filepath.lower()
        
        # Determine system based on path and filename
        if 'system1' in path or 'σπίτι πάνω' in filename or 'system 1' in filename:
            system1_files.append(filepath)
        elif 'system2' in path or 'σπίτι κάτω' in filename or 'system 2' in filename:
            system2_files.append(filepath)
        elif '/system1/' in path:
            system1_files.append(filepath)
        elif '/system2/' in path:
            system2_files.append(filepath)
        else:
            # Try to determine from content or ask user
            print(f"⚠️  Cannot determine system for: {filename}")
    
    print(f"\n📊 SYSTEM CLASSIFICATION:")
    print(f"   System 1 files: {len(system1_files)}")
    print(f"   System 2 files: {len(system2_files)}")
    
    # Analyze each system
    systems_data = {}
    
    for system_name, files in [("System 1", system1_files), ("System 2", system2_files)]:
        print(f"\n🏠 {system_name.upper()} ANALYSIS")
        print("=" * 60)
        
        system_results = []
        
        for filepath in sorted(files):
            result = analyze_excel_file(filepath)
            if result:
                system_results.append(result)
        
        if system_results:
            # Calculate totals for system
            total_production = sum(r['total_production'] for r in system_results)
            total_self_consumption = sum(r['total_self_consumption'] for r in system_results)
            total_days = sum(r['total_days'] for r in system_results)
            
            earliest_date = min(r['start_date'] for r in system_results)
            latest_date = max(r['end_date'] for r in system_results)
            
            print(f"\n🎯 {system_name.upper()} SUMMARY:")
            print(f"   Files analyzed: {len(system_results)}")
            print(f"   Date range: {earliest_date.date()} to {latest_date.date()}")
            print(f"   Total days covered: {total_days}")
            print(f"   Total production: {total_production:.2f} kWh")
            print(f"   Total self-consumption: {total_self_consumption:.2f} kWh")
            
            if total_production > 0:
                overall_self_consumption_rate = (total_self_consumption / total_production * 100)
                print(f"   Overall self-consumption rate: {overall_self_consumption_rate:.1f}%")
            
            # Daily averages
            if total_days > 0:
                avg_daily_production = total_production / total_days
                avg_daily_self_consumption = total_self_consumption / total_days
                print(f"   Avg daily production: {avg_daily_production:.2f} kWh/day")
                print(f"   Avg daily self-consumption: {avg_daily_self_consumption:.2f} kWh/day")
            
            systems_data[system_name] = {
                'files': len(system_results),
                'earliest_date': earliest_date,
                'latest_date': latest_date,
                'total_days': total_days,
                'total_production': total_production,
                'total_self_consumption': total_self_consumption,
                'self_consumption_rate': overall_self_consumption_rate if total_production > 0 else 0,
                'avg_daily_production': avg_daily_production if total_days > 0 else 0,
                'avg_daily_self_consumption': avg_daily_self_consumption if total_days > 0 else 0
            }
    
    # Overall analysis
    if len(systems_data) >= 2:
        print(f"\n🎯 COMBINED ANALYSIS")
        print("=" * 60)
        
        combined_production = sum(data['total_production'] for data in systems_data.values())
        combined_self_consumption = sum(data['total_self_consumption'] for data in systems_data.values())
        
        earliest_overall = min(data['earliest_date'] for data in systems_data.values())
        latest_overall = max(data['latest_date'] for data in systems_data.values())
        
        print(f"   Combined date range: {earliest_overall.date()} to {latest_overall.date()}")
        print(f"   Combined production: {combined_production:.2f} kWh")
        print(f"   Combined self-consumption: {combined_self_consumption:.2f} kWh")
        
        if combined_production > 0:
            combined_self_consumption_rate = (combined_self_consumption / combined_production * 100)
            print(f"   Combined self-consumption rate: {combined_self_consumption_rate:.1f}%")
        
        # Calculate potential savings
        total_rate = 0.1659  # €/kWh including all charges
        annual_savings = combined_self_consumption * total_rate
        
        print(f"\n💰 FINANCIAL ANALYSIS:")
        print(f"   Self-consumption: {combined_self_consumption:.2f} kWh")
        print(f"   Savings at €{total_rate:.4f}/kWh: €{annual_savings:.2f}")
        
        # ROI calculation
        investment = 25000  # €25,000 for both systems
        roi_percentage = (annual_savings / investment * 100)
        payback_years = investment / annual_savings if annual_savings > 0 else float('inf')
        
        print(f"   Investment: €{investment:,}")
        print(f"   ROI: {roi_percentage:.2f}%")
        print(f"   Payback: {payback_years:.1f} years")
        
        # Coverage analysis
        total_period_days = (latest_overall - earliest_overall).days + 1
        print(f"\n📅 COVERAGE ANALYSIS:")
        print(f"   Period span: {total_period_days} days")
        print(f"   Data coverage: Complete Excel exports available")
        
        # Check for gaps
        print(f"\n🔍 DATA COMPLETENESS:")
        for system_name, data in systems_data.items():
            print(f"   {system_name}:")
            print(f"      Files: {data['files']}")
            print(f"      Period: {data['earliest_date'].date()} to {data['latest_date'].date()}")
            print(f"      Days: {data['total_days']}")
    
    print(f"\n🎉 COMPLETE ANALYSIS FINISHED!")
    print("✅ All Excel files analyzed")
    print("✅ System data separated")
    print("✅ Financial analysis completed")
    print("✅ Coverage assessment done")

if __name__ == "__main__":
    main()
