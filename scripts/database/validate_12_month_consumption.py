#!/usr/bin/env python3
"""
VALIDATE 12-MONTH CONSUMPTION
Ελέγχει την κατανάλωση από φωτοβολταϊκά των τελευταίων 12 μηνών
"""

import psycopg2
from datetime import datetime, date, timedelta
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 5433,
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

def validate_12_month_consumption():
    """Ελέγχει την κατανάλωση των τελευταίων 12 μηνών"""
    
    print("📊 12-MONTH CONSUMPTION VALIDATION")
    print("=" * 80)
    
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()
        
        print("✅ Connected to database")
        
        # Υπολογισμός 12-month period
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=365)
        
        print(f"\n📅 Analysis Period: {start_date} to {end_date}")
        print(f"📅 Total days: 365")
        
        # 1. ΠΡΑΓΜΑΤΙΚΗ ΚΑΤΑΝΑΛΩΣΗ ΑΠΟ ΦΩΤΟΒΟΛΤΑΙΚΑ
        print("\n🏠 ACTUAL CONSUMPTION FROM SOLAR (Last 12 Months)")
        print("-" * 60)
        
        total_solar_consumption_all = 0
        total_billing_savings_all = 0
        
        for table, system_name in [('solax_data', 'System 1'), ('solax_data2', 'System 2')]:
            print(f"\n🏠 {system_name}:")
            
            # Μέθοδος 1: Από billing fields (που χρησιμοποίησα)
            cur.execute(f"""
                SELECT 
                    SUM(COALESCE(billing_benefit, 0)) as total_billing_savings,
                    COUNT(CASE WHEN billing_benefit > 0 THEN 1 END) as records_with_savings,
                    COUNT(DISTINCT DATE(timestamp)) as days_with_data
                FROM {table}
                WHERE timestamp >= %s AND timestamp <= %s
                AND billing_benefit IS NOT NULL
            """, (start_date, end_date))
            
            result = cur.fetchone()
            if result:
                billing_savings, records_with_savings, days_with_data = result
                billing_savings = float(billing_savings or 0)
                
                # Υπολογισμός self-consumption από billing fields
                # billing_benefit = self_consumption × 0.1659
                calculated_self_consumption = billing_savings / 0.1659 if billing_savings > 0 else 0
                
                print(f"   📊 Days with data: {days_with_data}")
                print(f"   💰 Billing savings: €{billing_savings:.2f}")
                print(f"   🏠 Calculated self-consumption: {calculated_self_consumption:.2f} kWh")
                
                total_solar_consumption_all += calculated_self_consumption
                total_billing_savings_all += billing_savings
            
            # Μέθοδος 2: Από πραγματικά consumption data (αν υπάρχουν)
            if table == 'solax_data':
                # System 1 έχει feedin_energy και consume_energy - απλούστερη προσέγγιση
                cur.execute(f"""
                    WITH daily_data AS (
                        SELECT
                            DATE(timestamp) as date,
                            MAX(yield_today) - MIN(yield_today) as daily_production
                        FROM {table}
                        WHERE timestamp >= %s AND timestamp <= %s
                        AND yield_today >= 0
                        GROUP BY DATE(timestamp)
                        HAVING MAX(yield_today) - MIN(yield_today) > 0
                    )
                    SELECT
                        SUM(daily_production) as total_production,
                        COUNT(*) as days_calculated
                    FROM daily_data
                """, (start_date, end_date))
                
                result = cur.fetchone()
                if result:
                    total_production, days_calculated = result

                    if total_production and total_production > 0:
                        print(f"\n   📊 PRODUCTION DATA ANALYSIS:")
                        print(f"      Days calculated: {days_calculated}")
                        print(f"      Total production: {total_production:.2f} kWh")

                        # Εκτίμηση self-consumption με το γνωστό rate 28.1%
                        estimated_self_consumption = total_production * 0.281
                        print(f"      Estimated self-consumption (28.1%): {estimated_self_consumption:.2f} kWh")

                        # Σύγκριση με billing calculation
                        if calculated_self_consumption > 0:
                            accuracy = (calculated_self_consumption / estimated_self_consumption * 100) if estimated_self_consumption > 0 else 0
                            print(f"      Billing vs Estimated accuracy: {accuracy:.1f}%")
            
            elif table == 'solax_data2':
                # System 2 έχει feedin_energy και consume_energy
                cur.execute(f"""
                    SELECT 
                        SUM(COALESCE(feedin_energy, 0)) as total_feedin,
                        SUM(COALESCE(consume_energy, 0)) as total_consumption,
                        COUNT(CASE WHEN feedin_energy IS NOT NULL THEN 1 END) as records_with_feedin,
                        COUNT(CASE WHEN consume_energy IS NOT NULL THEN 1 END) as records_with_consumption
                    FROM {table}
                    WHERE timestamp >= %s AND timestamp <= %s
                """, (start_date, end_date))
                
                result = cur.fetchone()
                if result:
                    total_feedin, total_consumption, records_feedin, records_consumption = result
                    
                    if total_consumption and total_consumption > 0:
                        print(f"\n   📊 CONSUMPTION DATA ANALYSIS:")
                        print(f"      Records with feedin: {records_feedin:,}")
                        print(f"      Records with consumption: {records_consumption:,}")
                        print(f"      Total feedin: {total_feedin:.2f} kWh")
                        print(f"      Total consumption: {total_consumption:.2f} kWh")
                        
                        # Υπολογισμός παραγωγής για System 2
                        cur.execute(f"""
                            WITH daily_production AS (
                                SELECT 
                                    DATE(timestamp) as date,
                                    MAX(yield_today) - MIN(yield_today) as daily_production
                                FROM {table}
                                WHERE timestamp >= %s AND timestamp <= %s
                                AND yield_today >= 0
                                GROUP BY DATE(timestamp)
                                HAVING MAX(yield_today) - MIN(yield_today) > 0
                            )
                            SELECT SUM(daily_production) as total_production
                            FROM daily_production
                        """, (start_date, end_date))
                        
                        prod_result = cur.fetchone()
                        if prod_result and prod_result[0]:
                            total_production = float(prod_result[0])
                            real_self_consumption = total_production - total_feedin

                            print(f"      Total production: {total_production:.2f} kWh")
                            print(f"      Real self-consumption: {real_self_consumption:.2f} kWh")

                            # Υπολογισμός self-consumption rate
                            if total_consumption > 0:
                                self_consumption_rate = (real_self_consumption / total_consumption * 100)
                                print(f"      Self-consumption rate: {self_consumption_rate:.1f}%")

                            # Σύγκριση με billing calculation
                            if calculated_self_consumption > 0:
                                accuracy = (calculated_self_consumption / real_self_consumption * 100) if real_self_consumption > 0 else 0
                                print(f"      Billing vs Real accuracy: {accuracy:.1f}%")
        
        print(f"\n🎯 COMBINED 12-MONTH TOTALS:")
        print(f"   Total solar consumption (from billing): {total_solar_consumption_all:.2f} kWh")
        print(f"   Total billing savings: €{total_billing_savings_all:.2f}")
        
        # 2. ΣΥΓΚΡΙΣΗ ΜΕ ΠΡΟΗΓΟΥΜΕΝΟ ΥΠΟΛΟΓΙΣΜΟ
        print("\n🔍 COMPARISON WITH PREVIOUS CALCULATION")
        print("-" * 60)
        
        # Από τον προηγούμενο υπολογισμό
        previous_calculation = 13185.27  # kWh
        previous_savings = 2187.44  # €
        
        print(f"\n📊 COMPARISON:")
        print(f"   Previous calculation: {previous_calculation:.2f} kWh")
        print(f"   12-month actual: {total_solar_consumption_all:.2f} kWh")
        
        if previous_calculation > 0:
            accuracy_percentage = (total_solar_consumption_all / previous_calculation * 100)
            difference_kwh = total_solar_consumption_all - previous_calculation
            difference_eur = total_billing_savings_all - previous_savings
            
            print(f"   Accuracy: {accuracy_percentage:.1f}%")
            print(f"   Difference: {difference_kwh:+.2f} kWh")
            print(f"   Savings difference: €{difference_eur:+.2f}")
            
            if abs(accuracy_percentage - 100) < 10:  # Within 10%
                print(f"   ✅ EXCELLENT MATCH!")
            elif abs(accuracy_percentage - 100) < 20:  # Within 20%
                print(f"   ✅ GOOD MATCH!")
            else:
                print(f"   ⚠️  SIGNIFICANT DIFFERENCE!")
        
        # 3. ΜΗΝΙΑΙΑ ΑΝΑΛΥΣΗ
        print("\n📅 MONTHLY BREAKDOWN")
        print("-" * 60)
        
        for table, system_name in [('solax_data', 'System 1'), ('solax_data2', 'System 2')]:
            print(f"\n🏠 {system_name} Monthly Analysis:")
            
            cur.execute(f"""
                SELECT 
                    DATE_TRUNC('month', timestamp) as month,
                    SUM(COALESCE(billing_benefit, 0)) as monthly_savings,
                    COUNT(DISTINCT DATE(timestamp)) as days_in_month
                FROM {table}
                WHERE timestamp >= %s AND timestamp <= %s
                AND billing_benefit IS NOT NULL
                GROUP BY DATE_TRUNC('month', timestamp)
                ORDER BY month DESC
                LIMIT 12
            """, (start_date, end_date))
            
            results = cur.fetchall()
            total_monthly_consumption = 0
            
            for result in results:
                month, monthly_savings, days_in_month = result
                monthly_savings = float(monthly_savings or 0)
                monthly_consumption = monthly_savings / 0.1659 if monthly_savings > 0 else 0
                total_monthly_consumption += monthly_consumption
                
                print(f"   {month.strftime('%Y-%m')}: {monthly_consumption:.1f} kWh (€{monthly_savings:.2f}, {days_in_month} days)")
            
            print(f"   Total: {total_monthly_consumption:.2f} kWh")
        
        # 4. ΕΤΗΣΙΑ ΠΡΟΒΟΛΗ
        print("\n📈 ANNUAL PROJECTION")
        print("-" * 60)
        
        if total_solar_consumption_all > 0:
            # Υπολογισμός ημερών στην ανάλυση
            actual_days = (end_date - start_date).days
            daily_average = total_solar_consumption_all / actual_days
            annual_projection = daily_average * 365.25
            annual_savings_projection = total_billing_savings_all / actual_days * 365.25
            
            print(f"\n🎯 ANNUAL PROJECTIONS:")
            print(f"   Analysis period: {actual_days} days")
            print(f"   Daily average consumption: {daily_average:.2f} kWh/day")
            print(f"   Annual projection: {annual_projection:.2f} kWh/year")
            print(f"   Annual savings projection: €{annual_savings_projection:.2f}/year")
            
            # ROI calculation
            investment_cost = 25000  # €25,000 for both systems
            roi_percentage = (annual_savings_projection / investment_cost * 100)
            payback_years = investment_cost / annual_savings_projection if annual_savings_projection > 0 else float('inf')
            
            print(f"\n💰 ROI CALCULATION:")
            print(f"   Investment: €{investment_cost:,}")
            print(f"   Annual savings: €{annual_savings_projection:.2f}")
            print(f"   ROI: {roi_percentage:.2f}%")
            print(f"   Payback: {payback_years:.1f} years")
        
        conn.close()
        
        print("\n🎉 12-MONTH VALIDATION COMPLETED!")
        print("-" * 60)
        print("✅ Real consumption data analyzed")
        print("✅ Billing fields validated")
        print("✅ Monthly breakdown provided")
        print("✅ Annual projections calculated")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    validate_12_month_consumption()
