#!/usr/bin/env python3
"""
FIX BILLING WITH CORRECT RESET LOGIC
Διορθώνει τα billing fields με τη σωστή reset logic από το correct_yield_calculation.py
"""

import psycopg2
from datetime import datetime, date
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 5433,
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

def fix_billing_with_correct_reset():
    """Διορθώνει τα billing fields με σωστή reset logic"""
    
    print("🔧 FIXING BILLING WITH CORRECT RESET LOGIC")
    print("=" * 50)
    
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()
        
        print("✅ Connected to database")
        
        # 1. ΚΑΘΑΡΙΣΜΟΣ ΠΑΛΙΩΝ TRIGGERS
        print("\n🧹 Cleaning all triggers...")
        
        cur.execute("DROP TRIGGER IF EXISTS trg_final_correct_billing_solax_data ON solax_data;")
        cur.execute("DROP TRIGGER IF EXISTS trg_final_correct_billing_solax_data2 ON solax_data2;")
        
        print("   ✅ All triggers dropped")
        
        # 2. BATCH UPDATE ΜΕ ΣΩΣΤΗ RESET LOGIC
        print("\n🔄 Batch updating billing fields with correct reset logic...")
        
        target_date = '2025-06-24'  # Test date
        
        for table, system_name in [('solax_data', 'System 1'), ('solax_data2', 'System 2')]:
            print(f"\n📊 Processing {system_name}...")
            
            # Σωστό self-consumption rate
            self_consumption_rate = 0.281 if table == 'solax_data' else 0.292
            total_rate = 0.1659  # €0.142 + €0.0069 + €0.017
            
            # Update billing fields για την ημέρα test με σωστή λογική
            cur.execute(f"""
                WITH daily_data AS (
                    SELECT 
                        id,
                        timestamp,
                        yield_today,
                        LAG(yield_today) OVER (ORDER BY timestamp) as prev_yield
                    FROM {table}
                    WHERE DATE(timestamp) = %s
                    ORDER BY timestamp
                ),
                reset_points AS (
                    SELECT 
                        id,
                        timestamp,
                        yield_today,
                        prev_yield,
                        CASE 
                            WHEN yield_today < prev_yield THEN 1 
                            ELSE 0 
                        END as is_reset
                    FROM daily_data
                ),
                with_reset_groups AS (
                    SELECT 
                        id,
                        timestamp,
                        yield_today,
                        prev_yield,
                        SUM(is_reset) OVER (ORDER BY timestamp) as reset_group
                    FROM reset_points
                ),
                hourly_production AS (
                    SELECT 
                        id,
                        timestamp,
                        yield_today,
                        prev_yield,
                        reset_group,
                        CASE 
                            WHEN yield_today > COALESCE(prev_yield, 0) 
                            THEN yield_today - COALESCE(prev_yield, 0)
                            WHEN yield_today < COALESCE(prev_yield, 0)
                            THEN yield_today  -- Reset case
                            ELSE 0
                        END as hourly_production_kwh
                    FROM with_reset_groups
                ),
                billing_calculation AS (
                    SELECT 
                        id,
                        hourly_production_kwh,
                        hourly_production_kwh * %s as self_consumption_kwh,
                        hourly_production_kwh * %s * %s as billing_benefit
                    FROM hourly_production
                    WHERE hourly_production_kwh > 0
                )
                UPDATE {table} 
                SET 
                    billing_benefit = COALESCE(bc.billing_benefit, 0),
                    billing_cost = 0.000,
                    billing_tariff = 0.142,
                    billing_network_charge = 0.0069,
                    billing_etmear = 0.017,
                    billing_schedule = 'summer_day',
                    billing_net_metering_credit = 0.000
                FROM billing_calculation bc
                WHERE {table}.id = bc.id
            """, (target_date, self_consumption_rate, self_consumption_rate, total_rate))
            
            updated_count = cur.rowcount
            print(f"   ✅ Updated {updated_count} records with correct billing")
            
            # Set zero billing for records without production
            cur.execute(f"""
                UPDATE {table} 
                SET 
                    billing_benefit = 0.000,
                    billing_cost = 0.000,
                    billing_tariff = 0.142,
                    billing_network_charge = 0.0069,
                    billing_etmear = 0.017,
                    billing_schedule = 'summer_day',
                    billing_net_metering_credit = 0.000
                WHERE DATE(timestamp) = %s
                AND id NOT IN (
                    SELECT id FROM (
                        WITH daily_data AS (
                            SELECT 
                                id,
                                timestamp,
                                yield_today,
                                LAG(yield_today) OVER (ORDER BY timestamp) as prev_yield
                            FROM {table}
                            WHERE DATE(timestamp) = %s
                            ORDER BY timestamp
                        ),
                        hourly_production AS (
                            SELECT 
                                id,
                                CASE 
                                    WHEN yield_today > COALESCE(prev_yield, 0) 
                                    THEN yield_today - COALESCE(prev_yield, 0)
                                    WHEN yield_today < COALESCE(prev_yield, 0)
                                    THEN yield_today
                                    ELSE 0
                                END as hourly_production_kwh
                            FROM daily_data
                        )
                        SELECT id FROM hourly_production WHERE hourly_production_kwh > 0
                    ) productive_records
                )
            """, (target_date, target_date))
            
            zero_count = cur.rowcount
            print(f"   ✅ Set zero billing for {zero_count} non-productive records")
        
        conn.commit()
        print("\n✅ Batch update completed")
        
        # 3. VALIDATION
        print("\n🔍 Validating corrected billing...")
        
        for table, system_name, system_id in [
            ('solax_data', 'System 1', 'system1'), 
            ('solax_data2', 'System 2', 'system2')
        ]:
            print(f"\n📊 {system_name} Validation:")
            
            # ΣΩΣΤΗ RESET LOGIC για production
            cur.execute(f"""
                WITH daily_data AS (
                    SELECT 
                        timestamp,
                        yield_today,
                        LAG(yield_today) OVER (ORDER BY timestamp) as prev_yield
                    FROM {table}
                    WHERE DATE(timestamp) = %s
                    ORDER BY timestamp
                ),
                reset_points AS (
                    SELECT 
                        timestamp,
                        yield_today,
                        prev_yield,
                        CASE 
                            WHEN yield_today < prev_yield THEN 1 
                            ELSE 0 
                        END as is_reset
                    FROM daily_data
                ),
                with_reset_groups AS (
                    SELECT 
                        timestamp,
                        yield_today,
                        SUM(is_reset) OVER (ORDER BY timestamp) as reset_group
                    FROM reset_points
                ),
                daily_production AS (
                    SELECT 
                        reset_group,
                        MAX(yield_today) - MIN(yield_today) as group_production
                    FROM with_reset_groups
                    GROUP BY reset_group
                    HAVING MAX(yield_today) - MIN(yield_today) > 0
                )
                SELECT 
                    COALESCE(SUM(group_production), 0) as total_daily_production
                FROM daily_production
            """, (target_date,))
            
            result = cur.fetchone()
            correct_daily_production = float(result[0] or 0) if result else 0
            
            # Get billing benefit sum
            cur.execute(f"""
                SELECT 
                    SUM(COALESCE(billing_benefit, 0)) as total_benefit
                FROM {table}
                WHERE DATE(timestamp) = %s
            """, (target_date,))
            
            result = cur.fetchone()
            daily_benefit = float(result[0] or 0) if result else 0
            
            # Real data comparison
            real_data = {
                'system1': {'yield': 66.60, 'self_consumption': 18.69},
                'system2': {'yield': 66.90, 'self_consumption': 19.53}
            }
            
            real_yield = real_data[system_id]['yield']
            real_self_consumption = real_data[system_id]['self_consumption']
            expected_benefit = real_self_consumption * 0.1659
            
            print(f"   Calculated Production: {correct_daily_production:.2f} kWh")
            print(f"   Real Production: {real_yield:.2f} kWh")
            print(f"   Production Accuracy: {(correct_daily_production/real_yield*100):.1f}%")
            
            print(f"   Daily Benefit: €{daily_benefit:.2f}")
            print(f"   Expected Benefit: €{expected_benefit:.2f}")
            print(f"   Benefit Accuracy: {(daily_benefit/expected_benefit*100):.1f}%")
            
            if correct_daily_production > 0:
                effective_rate = daily_benefit / correct_daily_production
                print(f"   Effective Rate: €{effective_rate:.4f}/kWh")
        
        conn.close()
        
        print("\n🎉 BILLING CORRECTION WITH RESET LOGIC COMPLETED!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()

if __name__ == "__main__":
    fix_billing_with_correct_reset()
