#!/usr/bin/env python3
"""
CORRECT SAVINGS FROM BILLING FIELDS
Υπολογίζει τη σωστή εξοικονόμηση από τα dynamic billing fields
"""

import psycopg2
from datetime import datetime, date, timedelta
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 5433,
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

def calculate_correct_savings():
    """Υπολογίζει τη σωστή εξοικονόμηση από τα billing fields"""
    
    print("💰 CORRECT SAVINGS FROM BILLING FIELDS")
    print("=" * 80)
    
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()
        
        print("✅ Connected to database")
        
        # 1. ΑΝΑΛΥΣΗ BILLING FIELDS
        print("\n📊 BILLING FIELDS ANALYSIS")
        print("-" * 50)
        
        total_savings_all = 0
        total_self_consumption_all = 0
        
        for table, system_name in [('solax_data', 'System 1'), ('solax_data2', 'System 2')]:
            print(f"\n🏠 {system_name}:")
            
            # Συνολική εξοικονόμηση από billing fields
            cur.execute(f"""
                SELECT 
                    SUM(COALESCE(billing_benefit, 0)) as total_savings,
                    COUNT(CASE WHEN billing_benefit > 0 THEN 1 END) as records_with_savings,
                    AVG(CASE WHEN billing_benefit > 0 THEN billing_benefit END) as avg_savings_per_record,
                    MIN(CASE WHEN billing_benefit > 0 THEN billing_benefit END) as min_savings,
                    MAX(billing_benefit) as max_savings,
                    COUNT(DISTINCT DATE(timestamp)) as days_with_data,
                    MIN(timestamp) as start_date,
                    MAX(timestamp) as end_date
                FROM {table}
                WHERE timestamp >= '2024-01-01'
                AND billing_benefit IS NOT NULL
            """)
            
            result = cur.fetchone()
            if result:
                total_savings, records_with_savings, avg_savings_per_record, min_savings, max_savings, days_with_data, start_date, end_date = result
                total_savings = float(total_savings or 0)
                
                print(f"   💰 Total savings: €{total_savings:.2f}")
                print(f"   📊 Records with savings: {records_with_savings:,}")
                print(f"   📈 Avg savings per record: €{avg_savings_per_record:.6f}")
                print(f"   📉 Min savings: €{min_savings:.6f}")
                print(f"   📈 Max savings: €{max_savings:.6f}")
                print(f"   📅 Days with data: {days_with_data}")
                print(f"   📅 Period: {start_date} to {end_date}")
                
                # Υπολογισμός ετήσιας εξοικονόμησης
                if start_date and end_date:
                    operational_days = (end_date - start_date).days
                    operational_years = operational_days / 365.25
                    annual_savings = total_savings / operational_years if operational_years > 0 else 0
                    daily_savings = total_savings / days_with_data if days_with_data > 0 else 0
                    
                    print(f"   📊 Operational years: {operational_years:.2f}")
                    print(f"   📊 Annual savings: €{annual_savings:.2f}/year")
                    print(f"   📊 Daily savings: €{daily_savings:.2f}/day")
                
                total_savings_all += total_savings
        
        print(f"\n🎯 COMBINED TOTAL SAVINGS: €{total_savings_all:.2f}")
        
        # 2. ΑΝΑΛΥΣΗ TARIFF RATES ΑΠΟ BILLING FIELDS
        print("\n📊 TARIFF ANALYSIS FROM BILLING FIELDS")
        print("-" * 50)
        
        for table, system_name in [('solax_data', 'System 1'), ('solax_data2', 'System 2')]:
            print(f"\n🏠 {system_name} Tariff Analysis:")
            
            # Ανάλυση των rates που χρησιμοποιήθηκαν
            cur.execute(f"""
                SELECT 
                    billing_schedule,
                    COUNT(*) as records,
                    AVG(COALESCE(billing_tariff, 0)) as avg_energy_rate,
                    AVG(COALESCE(billing_network_charge, 0)) as avg_network_rate,
                    AVG(COALESCE(billing_etmear, 0)) as avg_etmear_rate,
                    AVG(COALESCE(billing_tariff, 0) + COALESCE(billing_network_charge, 0) + COALESCE(billing_etmear, 0)) as avg_total_rate,
                    SUM(COALESCE(billing_benefit, 0)) as schedule_savings
                FROM {table}
                WHERE timestamp >= '2024-01-01'
                AND billing_benefit IS NOT NULL
                AND billing_schedule IS NOT NULL
                GROUP BY billing_schedule
                ORDER BY records DESC
            """)
            
            results = cur.fetchall()
            for result in results:
                schedule, records, avg_energy, avg_network, avg_etmear, avg_total, schedule_savings = result
                print(f"   {schedule}:")
                print(f"      Records: {records:,}")
                print(f"      Energy rate: €{avg_energy:.4f}/kWh")
                print(f"      Network rate: €{avg_network:.4f}/kWh")
                print(f"      ETMEAR rate: €{avg_etmear:.4f}/kWh")
                print(f"      Total rate: €{avg_total:.4f}/kWh")
                print(f"      Savings: €{schedule_savings:.2f}")
        
        # 3. ΥΠΟΛΟΓΙΣΜΟΣ SELF-CONSUMPTION ΑΠΟ BILLING FIELDS
        print("\n🏠 SELF-CONSUMPTION FROM BILLING FIELDS")
        print("-" * 50)
        
        for table, system_name in [('solax_data', 'System 1'), ('solax_data2', 'System 2')]:
            print(f"\n🏠 {system_name}:")
            
            # Υπολογισμός self-consumption από billing fields
            cur.execute(f"""
                SELECT 
                    SUM(COALESCE(billing_benefit, 0)) as total_savings,
                    AVG(COALESCE(billing_tariff, 0) + COALESCE(billing_network_charge, 0) + COALESCE(billing_etmear, 0)) as avg_total_rate
                FROM {table}
                WHERE timestamp >= '2024-01-01'
                AND billing_benefit IS NOT NULL
                AND billing_benefit > 0
            """)
            
            result = cur.fetchone()
            if result:
                total_savings, avg_total_rate = result
                total_savings = float(total_savings or 0)
                avg_total_rate = float(avg_total_rate or 0)
                
                # Υπολογισμός self-consumption
                # billing_benefit = self_consumption_kwh × total_rate
                # άρα self_consumption_kwh = billing_benefit / total_rate
                if avg_total_rate > 0:
                    calculated_self_consumption = total_savings / avg_total_rate
                    print(f"   💰 Total savings: €{total_savings:.2f}")
                    print(f"   💡 Average total rate: €{avg_total_rate:.4f}/kWh")
                    print(f"   🏠 Calculated self-consumption: {calculated_self_consumption:.2f} kWh")
                    
                    total_self_consumption_all += calculated_self_consumption
                else:
                    print(f"   ⚠️  No valid rate data found")
        
        print(f"\n🎯 COMBINED SELF-CONSUMPTION: {total_self_consumption_all:.2f} kWh")
        
        # 4. EFFECTIVE RATE CALCULATION
        print("\n💡 EFFECTIVE RATE CALCULATION")
        print("-" * 50)
        
        if total_self_consumption_all > 0 and total_savings_all > 0:
            effective_rate = total_savings_all / total_self_consumption_all
            print(f"\n🎯 EFFECTIVE RATE CALCULATION:")
            print(f"   Total Savings: €{total_savings_all:.2f}")
            print(f"   Total Self-Consumption: {total_self_consumption_all:.2f} kWh")
            print(f"   Effective Rate: €{effective_rate:.4f}/kWh")
            
            print(f"\n📊 RATE BREAKDOWN:")
            print(f"   This rate includes:")
            print(f"   • Dynamic energy rates (day/night, winter/summer)")
            print(f"   • Tiered network charges")
            print(f"   • ETMEAR charges")
            print(f"   • Proper time-based application")
            print(f"   • NO VAT (self-consumption doesn't pay VAT)")
        
        # 5. COMPARISON WITH GRID COSTS
        print("\n💰 COMPARISON WITH GRID COSTS")
        print("-" * 50)
        
        # Υπολογισμός τι θα κόστιζε αν αγόραζε από το δίκτυο
        if total_self_consumption_all > 0:
            # Χρησιμοποιώ τα billing fields για να υπολογίσω το κόστος δικτύου
            print(f"\n🔍 Grid Cost Analysis:")
            print(f"   Self-consumption: {total_self_consumption_all:.2f} kWh")
            print(f"   Actual cost (solar): €0.00")
            print(f"   Cost if bought from grid: €{total_savings_all:.2f}")
            print(f"   Net savings: €{total_savings_all:.2f}")
            print(f"   Savings rate: 100.0%")
            
            print(f"\n💡 This is the CORRECT calculation because:")
            print(f"   • Uses dynamic tariff rates")
            print(f"   • Includes seasonal variations")
            print(f"   • Includes time-based pricing")
            print(f"   • Includes tiered network charges")
            print(f"   • Based on actual consumption patterns")
        
        # 6. ROI CALCULATION
        print("\n📈 ROI CALCULATION")
        print("-" * 50)
        
        investment_cost = 12500.0  # €12,500 per system
        total_investment = investment_cost * 2  # Two systems
        
        # Ετήσια εξοικονόμηση
        operational_years = 1.27  # Από προηγούμενη ανάλυση
        annual_savings = total_savings_all / operational_years
        
        # ROI υπολογισμός
        roi_percentage = (annual_savings / total_investment * 100)
        payback_years = total_investment / annual_savings if annual_savings > 0 else float('inf')
        
        print(f"\n🎯 CORRECT ROI CALCULATION:")
        print(f"   Total Investment: €{total_investment:,.0f}")
        print(f"   Total Savings: €{total_savings_all:.2f}")
        print(f"   Annual Savings: €{annual_savings:.2f}")
        print(f"   ROI: {roi_percentage:.2f}%")
        print(f"   Payback: {payback_years:.1f} years")
        
        conn.close()
        
        print("\n🎉 CORRECT SAVINGS ANALYSIS COMPLETED!")
        print("-" * 50)
        print("✅ Used dynamic billing fields (not static rates)")
        print("✅ Included seasonal and time-based variations")
        print("✅ Included tiered network charges")
        print("✅ Based on actual consumption patterns")
        print("✅ Proper financial analysis")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    calculate_correct_savings()
