#!/usr/bin/env python3
"""
Correct Yield Reset Calculation
Apply the proper reset logic: Daily production = MAX - MIN after reset point
"""

import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime, date, timedelta
import pandas as pd

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 5433,
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

def get_db_connection():
    """Get database connection"""
    try:
        return psycopg2.connect(**DB_CONFIG)
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return None

def analyze_reset_pattern():
    """Analyze the reset pattern for 24/6/2025"""
    print("\n" + "="*60)
    print("🔍 ANALYZING RESET PATTERN (24/6/2025)")
    print("="*60)
    
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        
        target_date = date(2025, 6, 24)
        
        for table, system_name in [('solax_data', 'System 1'), ('solax_data2', 'System 2')]:
            print(f"\n📊 {system_name} Reset Analysis:")
            
            # Get all records for the day ordered by timestamp
            cur.execute(f"""
                SELECT 
                    timestamp,
                    yield_today,
                    LAG(yield_today) OVER (ORDER BY timestamp) as prev_yield,
                    CASE 
                        WHEN yield_today < LAG(yield_today) OVER (ORDER BY timestamp) 
                        THEN 'RESET'
                        ELSE 'NORMAL'
                    END as reset_flag
                FROM {table}
                WHERE DATE(timestamp) = %s
                ORDER BY timestamp
            """, (target_date,))
            
            records = cur.fetchall()
            
            if not records:
                print(f"   ❌ No records found for {system_name}")
                continue
            
            print(f"   Total records: {len(records)}")
            print(f"   Time range: {records[0]['timestamp']} → {records[-1]['timestamp']}")
            print(f"   First yield: {records[0]['yield_today']:.2f} kWh")
            print(f"   Last yield: {records[-1]['yield_today']:.2f} kWh")
            
            # Find reset points
            reset_points = []
            for i, record in enumerate(records):
                if record['reset_flag'] == 'RESET':
                    reset_points.append({
                        'index': i,
                        'timestamp': record['timestamp'],
                        'yield_today': record['yield_today'],
                        'prev_yield': record['prev_yield']
                    })
            
            print(f"\n   🔄 Reset points found: {len(reset_points)}")
            for reset in reset_points[:5]:  # Show first 5
                print(f"      {reset['timestamp']}: {reset['prev_yield']:.2f} → {reset['yield_today']:.2f} kWh")
            
            if len(reset_points) > 5:
                print(f"      ... and {len(reset_points) - 5} more resets")
            
            # Calculate correct daily production using reset logic
            if reset_points:
                # Find the main reset (usually the first one or the one with biggest drop)
                main_reset = reset_points[0]  # Assume first reset is the main daily reset
                reset_index = main_reset['index']
                
                # Get records after the main reset
                post_reset_records = records[reset_index:]
                
                if post_reset_records:
                    min_yield_after_reset = min(r['yield_today'] for r in post_reset_records)
                    max_yield_after_reset = max(r['yield_today'] for r in post_reset_records)
                    correct_daily_production = max_yield_after_reset - min_yield_after_reset
                    
                    print(f"\n   ✅ CORRECT CALCULATION:")
                    print(f"      Main reset at: {main_reset['timestamp']}")
                    print(f"      Min yield after reset: {min_yield_after_reset:.2f} kWh")
                    print(f"      Max yield after reset: {max_yield_after_reset:.2f} kWh")
                    print(f"      CORRECT Daily Production: {correct_daily_production:.2f} kWh")
                    
                    # Compare with naive calculation
                    naive_calculation = records[-1]['yield_today'] - records[0]['yield_today']
                    print(f"\n   ⚠️ COMPARISON:")
                    print(f"      Naive calculation: {naive_calculation:.2f} kWh")
                    print(f"      Correct calculation: {correct_daily_production:.2f} kWh")
                    print(f"      Difference: {abs(naive_calculation - correct_daily_production):.2f} kWh")
            else:
                # No resets found - use simple max - min
                min_yield = min(r['yield_today'] for r in records)
                max_yield = max(r['yield_today'] for r in records)
                daily_production = max_yield - min_yield
                
                print(f"\n   ✅ NO RESETS - SIMPLE CALCULATION:")
                print(f"      Min yield: {min_yield:.2f} kWh")
                print(f"      Max yield: {max_yield:.2f} kWh")
                print(f"      Daily Production: {daily_production:.2f} kWh")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

def calculate_correct_daily_production():
    """Calculate correct daily production using proper reset logic"""
    print("\n" + "="*60)
    print("📊 CORRECT DAILY PRODUCTION CALCULATION")
    print("="*60)
    
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        
        target_date = date(2025, 6, 24)
        results = {}
        
        for table, system_name in [('solax_data', 'System 1'), ('solax_data2', 'System 2')]:
            print(f"\n🔧 Processing {system_name}...")
            
            # Get all records with reset detection
            cur.execute(f"""
                WITH reset_detection AS (
                    SELECT 
                        timestamp,
                        yield_today,
                        LAG(yield_today) OVER (ORDER BY timestamp) as prev_yield,
                        CASE 
                            WHEN yield_today < LAG(yield_today) OVER (ORDER BY timestamp) 
                            THEN 1 
                            ELSE 0 
                        END as is_reset,
                        ROW_NUMBER() OVER (ORDER BY timestamp) as row_num
                    FROM {table}
                    WHERE DATE(timestamp) = %s
                ),
                reset_groups AS (
                    SELECT 
                        *,
                        SUM(is_reset) OVER (ORDER BY timestamp ROWS UNBOUNDED PRECEDING) as reset_group
                    FROM reset_detection
                )
                SELECT 
                    reset_group,
                    MIN(timestamp) as group_start,
                    MAX(timestamp) as group_end,
                    MIN(yield_today) as min_yield,
                    MAX(yield_today) as max_yield,
                    MAX(yield_today) - MIN(yield_today) as group_production,
                    COUNT(*) as records_count
                FROM reset_groups
                GROUP BY reset_group
                ORDER BY reset_group
            """, (target_date,))
            
            reset_groups = cur.fetchall()
            
            if not reset_groups:
                print(f"   ❌ No data found for {system_name}")
                continue
            
            print(f"   Reset groups found: {len(reset_groups)}")
            
            total_production = 0
            main_production_group = None
            
            for group in reset_groups:
                group_prod = float(group['group_production'])
                print(f"      Group {group['reset_group']}: {group['group_start'].strftime('%H:%M')} - {group['group_end'].strftime('%H:%M')}")
                print(f"         Yield: {group['min_yield']:.2f} → {group['max_yield']:.2f} kWh")
                print(f"         Production: {group_prod:.2f} kWh")
                print(f"         Records: {group['records_count']}")
                
                # The main production group is usually the one with the most records or highest production
                if main_production_group is None or group['records_count'] > main_production_group['records_count']:
                    main_production_group = group
            
            if main_production_group:
                correct_daily_production = float(main_production_group['group_production'])
                
                print(f"\n   ✅ FINAL RESULT for {system_name}:")
                print(f"      Main production group: {main_production_group['reset_group']}")
                print(f"      Time period: {main_production_group['group_start'].strftime('%H:%M')} - {main_production_group['group_end'].strftime('%H:%M')}")
                print(f"      CORRECT Daily Production: {correct_daily_production:.2f} kWh")
                
                results[system_name] = {
                    'daily_production': correct_daily_production,
                    'time_start': main_production_group['group_start'],
                    'time_end': main_production_group['group_end'],
                    'min_yield': float(main_production_group['min_yield']),
                    'max_yield': float(main_production_group['max_yield']),
                    'records_count': main_production_group['records_count']
                }
        
        conn.close()
        return results
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def calculate_correct_roi_with_reset_logic():
    """Calculate ROI using correct reset logic"""
    print("\n" + "="*60)
    print("💰 CORRECT ROI CALCULATION WITH RESET LOGIC")
    print("="*60)
    
    # Get correct daily production
    daily_results = calculate_correct_daily_production()
    
    if not daily_results:
        print("❌ Could not get daily production results")
        return
    
    # Calculate financial metrics
    investment_cost = 12500.0  # €12,500 per system
    energy_rate = 0.142  # €/kWh
    network_rate = 0.0069  # €/kWh  
    etmear_rate = 0.017  # €/kWh
    total_rate = energy_rate + network_rate + etmear_rate  # €0.1659/kWh
    
    print(f"\n📊 CORRECTED FINANCIAL ANALYSIS:")
    
    total_daily_production = 0
    total_daily_benefit = 0
    
    for system_name, data in daily_results.items():
        daily_production = data['daily_production']
        
        # Self-consumption rates
        if system_name == 'System 1':
            self_consumption_rate = 0.405  # 40.5%
        else:
            self_consumption_rate = 0.47   # 47%
        
        # Calculate daily benefit
        daily_self_consumption = daily_production * self_consumption_rate
        daily_benefit = daily_self_consumption * total_rate
        
        # Annual projections
        annual_production = daily_production * 365
        annual_benefit = daily_benefit * 365
        annual_roi = (annual_benefit / investment_cost) * 100
        payback_years = investment_cost / annual_benefit if annual_benefit > 0 else None
        
        print(f"\n🏠 {system_name} (CORRECTED):")
        print(f"   Daily production: {daily_production:.2f} kWh")
        print(f"   Self-consumption: {daily_self_consumption:.2f} kWh ({self_consumption_rate:.1%})")
        print(f"   Daily benefit: €{daily_benefit:.2f}")
        print(f"   Annual production: {annual_production:,.0f} kWh")
        print(f"   Annual benefit: €{annual_benefit:.2f}")
        print(f"   Annual ROI: {annual_roi:.2f}%")
        print(f"   Payback period: {payback_years:.1f} years" if payback_years else "   Payback period: N/A")
        
        total_daily_production += daily_production
        total_daily_benefit += daily_benefit
    
    # Combined results
    total_investment = 25000.0  # €25,000 total
    total_annual_benefit = total_daily_benefit * 365
    combined_roi = (total_annual_benefit / total_investment) * 100
    combined_payback = total_investment / total_annual_benefit if total_annual_benefit > 0 else None
    
    print(f"\n🎯 COMBINED SYSTEMS (CORRECTED):")
    print(f"   Total daily production: {total_daily_production:.2f} kWh")
    print(f"   Total daily benefit: €{total_daily_benefit:.2f}")
    print(f"   Total annual production: {total_daily_production * 365:,.0f} kWh")
    print(f"   Total annual benefit: €{total_annual_benefit:.2f}")
    print(f"   Combined ROI: {combined_roi:.2f}%")
    print(f"   Combined payback: {combined_payback:.1f} years" if combined_payback else "   Combined payback: N/A")

def main():
    """Main function"""
    print("🔧 CORRECT YIELD RESET CALCULATION")
    print("=" * 60)
    print("Applying proper reset logic: Daily production = MAX - MIN after reset point")
    print(f"Target Date: 24/6/2025")
    print(f"Time: {datetime.now()}")
    
    # Step 1: Analyze reset patterns
    analyze_reset_pattern()
    
    # Step 2: Calculate correct daily production
    daily_results = calculate_correct_daily_production()
    
    # Step 3: Calculate correct ROI
    calculate_correct_roi_with_reset_logic()
    
    print("\n" + "="*60)
    print("🎯 CORRECT CALCULATION COMPLETED")
    print("="*60)
    print("✅ Reset logic properly applied")
    print("✅ Daily production calculated correctly")
    print("✅ ROI calculated with accurate data")
    print("\n📝 Key insight: Reset logic is crucial for accurate calculations!")

if __name__ == "__main__":
    main()
