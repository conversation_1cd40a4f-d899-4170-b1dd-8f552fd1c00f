#!/usr/bin/env python3
"""
Unified Data Collection System - Forward-Only Approach

This script implements the complete data collection strategy:
1. Dual SolaX systems (solax_data, solax_data2)
2. CAMS radiation data (cams_radiation_data)
3. Weather data (weather_data)
4. Automated scheduling and integration

Strategy: Build Forward, Not Backward
- Use existing 784,349 normalized_training_data records
- Collect new data from today onwards
- Each system saves to its own table
- Continuous automated collection
"""

import asyncio
import logging
import sys
import os
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
import json

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class UnifiedDataCollectionSystem:
    """Unified system for collecting data from all sources."""
    
    def __init__(self):
        """Initialize the unified data collection system."""
        self.is_running = False
        self.services = {}
        self.collection_stats = {
            "solax_system_1": {"success": 0, "errors": 0, "last_run": None},
            "solax_system_2": {"success": 0, "errors": 0, "last_run": None},
            "cams_radiation": {"success": 0, "errors": 0, "last_run": None},
            "weather_data": {"success": 0, "errors": 0, "last_run": None}
        }
        
        # Collection intervals (seconds)
        self.intervals = {
            "solax": 30,      # Every 30 seconds
            "cams": 3600,     # Every hour
            "weather": 3600,  # Every hour
            "integration": 86400  # Daily
        }
        
        logger.info("🚀 Unified Data Collection System initialized")
    
    async def initialize_services(self):
        """Initialize all data collection services."""
        logger.info("🔧 Initializing data collection services...")
        
        try:
            # Import services
            from src.services.dual_solax_service import get_dual_solax_service
            from src.services.cams_service import get_cams_service
            
            # Initialize services
            self.services['solax'] = await get_dual_solax_service()
            self.services['cams'] = await get_cams_service()
            
            logger.info("✅ All services initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize services: {e}")
            return False
    
    async def test_all_connections(self) -> Dict[str, Any]:
        """Test connections to all data sources."""
        logger.info("🔍 Testing connections to all data sources...")
        
        results = {
            "timestamp": datetime.now().isoformat(),
            "tests": {},
            "summary": {"total": 0, "passed": 0, "failed": 0}
        }
        
        try:
            # Test SolaX systems
            if 'solax' in self.services:
                logger.info("Testing SolaX systems...")
                solax_test = await self.services['solax'].test_connection()
                results["tests"]["solax"] = solax_test
                
                # Count individual system results
                for system_id, system_info in solax_test.get('systems', {}).items():
                    results["summary"]["total"] += 1
                    if system_info.get('status') == 'success':
                        results["summary"]["passed"] += 1
                    else:
                        results["summary"]["failed"] += 1
            
            # Test CAMS service
            if 'cams' in self.services:
                logger.info("Testing CAMS service...")
                # Simple test - try to collect 1 hour of data
                end_time = datetime.now()
                start_time = end_time - timedelta(hours=1)
                
                cams_data = await self.services['cams'].collect_radiation_data(start_time, end_time)
                
                results["tests"]["cams"] = {
                    "status": "success" if cams_data is not None and not cams_data.empty else "failed",
                    "records": len(cams_data) if cams_data is not None else 0,
                    "message": f"Collected {len(cams_data)} CAMS records" if cams_data is not None else "Failed to collect CAMS data"
                }
                
                results["summary"]["total"] += 1
                if results["tests"]["cams"]["status"] == "success":
                    results["summary"]["passed"] += 1
                else:
                    results["summary"]["failed"] += 1
            
            # Calculate success rate
            if results["summary"]["total"] > 0:
                success_rate = (results["summary"]["passed"] / results["summary"]["total"]) * 100
                results["summary"]["success_rate"] = f"{success_rate:.1f}%"
            
            logger.info(f"🎯 Connection tests completed: {results['summary']['passed']}/{results['summary']['total']} passed")
            return results
            
        except Exception as e:
            logger.error(f"❌ Error testing connections: {e}")
            results["error"] = str(e)
            return results
    
    async def collect_solax_data(self) -> bool:
        """Collect data from both SolaX systems."""
        try:
            if 'solax' not in self.services:
                logger.error("SolaX service not initialized")
                return False
            
            logger.debug("📊 Collecting SolaX data from both systems...")
            success = await self.services['solax'].collect_and_save()
            
            # Update stats
            now = datetime.now()
            if success:
                self.collection_stats["solax_system_1"]["success"] += 1
                self.collection_stats["solax_system_2"]["success"] += 1
                self.collection_stats["solax_system_1"]["last_run"] = now
                self.collection_stats["solax_system_2"]["last_run"] = now
                logger.debug("✅ SolaX data collection successful")
            else:
                self.collection_stats["solax_system_1"]["errors"] += 1
                self.collection_stats["solax_system_2"]["errors"] += 1
                logger.warning("❌ SolaX data collection failed")
            
            return success
            
        except Exception as e:
            logger.error(f"Error collecting SolaX data: {e}")
            self.collection_stats["solax_system_1"]["errors"] += 1
            self.collection_stats["solax_system_2"]["errors"] += 1
            return False
    
    async def collect_cams_data(self) -> bool:
        """Collect CAMS radiation data."""
        try:
            if 'cams' not in self.services:
                logger.error("CAMS service not initialized")
                return False
            
            logger.debug("🌤️  Collecting CAMS radiation data...")
            
            # Collect data for the last hour
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=1)
            
            success = await self.services['cams'].collect_and_save(start_time, end_time)
            
            # Update stats
            now = datetime.now()
            if success:
                self.collection_stats["cams_radiation"]["success"] += 1
                self.collection_stats["cams_radiation"]["last_run"] = now
                logger.debug("✅ CAMS data collection successful")
            else:
                self.collection_stats["cams_radiation"]["errors"] += 1
                logger.warning("❌ CAMS data collection failed")
            
            return success
            
        except Exception as e:
            logger.error(f"Error collecting CAMS data: {e}")
            self.collection_stats["cams_radiation"]["errors"] += 1
            return False
    
    async def collect_weather_data(self) -> bool:
        """Collect weather data."""
        try:
            logger.debug("🌦️  Collecting weather data...")
            
            # TODO: Implement weather service integration
            # For now, simulate success
            
            # Update stats
            now = datetime.now()
            self.collection_stats["weather_data"]["success"] += 1
            self.collection_stats["weather_data"]["last_run"] = now
            logger.debug("✅ Weather data collection successful (simulated)")
            
            return True
            
        except Exception as e:
            logger.error(f"Error collecting weather data: {e}")
            self.collection_stats["weather_data"]["errors"] += 1
            return False
    
    async def run_data_integration(self) -> bool:
        """Run the daily data integration pipeline."""
        try:
            logger.info("🔄 Running data integration pipeline...")
            
            # TODO: Implement data integration pipeline
            # This will process raw data into integrated_data and normalized_training_data
            
            logger.info("✅ Data integration pipeline completed (placeholder)")
            return True
            
        except Exception as e:
            logger.error(f"Error in data integration pipeline: {e}")
            return False
    
    async def start_collection_loops(self):
        """Start all data collection loops."""
        if self.is_running:
            logger.warning("Collection system is already running")
            return
        
        self.is_running = True
        logger.info("🚀 Starting unified data collection loops...")
        
        # Create tasks for each collection loop
        tasks = [
            asyncio.create_task(self._solax_collection_loop()),
            asyncio.create_task(self._cams_collection_loop()),
            asyncio.create_task(self._weather_collection_loop()),
            asyncio.create_task(self._integration_loop()),
            asyncio.create_task(self._status_monitor_loop())
        ]
        
        try:
            # Wait for all tasks
            await asyncio.gather(*tasks)
        except Exception as e:
            logger.error(f"Error in collection loops: {e}")
        finally:
            self.is_running = False
    
    async def stop_collection_loops(self):
        """Stop all data collection loops."""
        logger.info("🛑 Stopping data collection loops...")
        self.is_running = False
    
    async def _solax_collection_loop(self):
        """SolaX data collection loop (every 30 seconds)."""
        logger.info(f"📊 Starting SolaX collection loop (interval: {self.intervals['solax']}s)")
        
        while self.is_running:
            try:
                await self.collect_solax_data()
                await asyncio.sleep(self.intervals['solax'])
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in SolaX collection loop: {e}")
                await asyncio.sleep(60)  # Wait 1 minute on error
    
    async def _cams_collection_loop(self):
        """CAMS data collection loop (every hour)."""
        logger.info(f"🌤️  Starting CAMS collection loop (interval: {self.intervals['cams']}s)")
        
        while self.is_running:
            try:
                await self.collect_cams_data()
                await asyncio.sleep(self.intervals['cams'])
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in CAMS collection loop: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes on error
    
    async def _weather_collection_loop(self):
        """Weather data collection loop (every hour)."""
        logger.info(f"🌦️  Starting weather collection loop (interval: {self.intervals['weather']}s)")
        
        while self.is_running:
            try:
                await self.collect_weather_data()
                await asyncio.sleep(self.intervals['weather'])
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in weather collection loop: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes on error
    
    async def _integration_loop(self):
        """Data integration loop (daily)."""
        logger.info(f"🔄 Starting integration loop (interval: {self.intervals['integration']}s)")
        
        while self.is_running:
            try:
                await self.run_data_integration()
                await asyncio.sleep(self.intervals['integration'])
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in integration loop: {e}")
                await asyncio.sleep(3600)  # Wait 1 hour on error
    
    async def _status_monitor_loop(self):
        """Status monitoring loop (every 5 minutes)."""
        while self.is_running:
            try:
                # Save status
                status = self.get_system_status()
                
                os.makedirs("data/status", exist_ok=True)
                with open("data/status/unified_collection_status.json", "w") as f:
                    json.dump(status, f, indent=2, default=str)
                
                logger.debug("📊 System status saved")
                await asyncio.sleep(300)  # 5 minutes
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in status monitor: {e}")
                await asyncio.sleep(60)
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get current system status."""
        return {
            "system": {
                "name": "Unified Data Collection System",
                "is_running": self.is_running,
                "timestamp": datetime.now().isoformat(),
                "strategy": "Forward-Only Approach"
            },
            "collection_stats": self.collection_stats,
            "intervals": self.intervals,
            "services": {
                "solax": "initialized" if 'solax' in self.services else "not_initialized",
                "cams": "initialized" if 'cams' in self.services else "not_initialized",
                "weather": "placeholder",
                "integration": "placeholder"
            }
        }

# Global instance
unified_system = UnifiedDataCollectionSystem()

async def main():
    """Main function for testing the unified system."""
    logger.info("🚀 Unified Data Collection System - Test Run")
    logger.info("=" * 60)
    
    try:
        # Initialize services
        success = await unified_system.initialize_services()
        if not success:
            logger.error("❌ Failed to initialize services")
            return False
        
        # Test connections
        test_results = await unified_system.test_all_connections()
        logger.info(f"🔍 Connection test results: {test_results['summary']}")
        
        # Test individual collections
        logger.info("🧪 Testing individual data collections...")
        
        # Test SolaX collection
        solax_success = await unified_system.collect_solax_data()
        logger.info(f"📊 SolaX collection: {'✅ Success' if solax_success else '❌ Failed'}")
        
        # Test CAMS collection
        cams_success = await unified_system.collect_cams_data()
        logger.info(f"🌤️  CAMS collection: {'✅ Success' if cams_success else '❌ Failed'}")
        
        # Test weather collection
        weather_success = await unified_system.collect_weather_data()
        logger.info(f"🌦️  Weather collection: {'✅ Success' if weather_success else '❌ Failed'}")
        
        # Show final status
        status = unified_system.get_system_status()
        logger.info(f"📊 Final system status: {json.dumps(status, indent=2, default=str)}")
        
        logger.info("✅ Unified system test completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error in unified system test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
