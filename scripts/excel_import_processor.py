#!/usr/bin/env python3
"""
Excel Import Processor - Import historical data από Excel files
"""

import pandas as pd
import psycopg2
import json
from datetime import datetime
import os

def analyze_excel_structure(file_path):
    """Ανάλυση δομής Excel file"""
    print(f"\n🔍 Analyzing: {os.path.basename(file_path)}")
    
    try:
        # Διάβασμα με skiprows=1 όπως στο υπάρχον script
        df = pd.read_excel(file_path, skiprows=1)
        
        print(f"   📊 Shape: {df.shape}")
        print(f"   📋 Columns: {list(df.columns)}")
        
        # Ελέγχω για Plant Name
        if 'Plant Name' in df.columns:
            plants = df['Plant Name'].unique()
            print(f"   🏠 Plants: {plants}")
            
            for plant in plants:
                plant_df = df[df['Plant Name'] == plant]
                print(f"      📊 {plant}: {len(plant_df)} records")
        
        # Ελέγχω για timestamp columns
        time_columns = [col for col in df.columns if 'time' in col.lower() or 'date' in col.lower()]
        print(f"   📅 Time columns: {time_columns}")
        
        # Δείγμα δεδομένων
        print(f"   📋 Sample data:")
        print(df.head(2).to_string())
        
        return df
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return None

def map_plant_to_system(plant_name):
    """Mapping plant names σε system IDs"""
    mapping = {
        'Σπίτι Πάνω': 1,
        'Σπίτι Κάτω': 2,
        # Προσθήκη άλλων mappings αν χρειάζεται
    }
    
    return mapping.get(plant_name, None)

def import_excel_to_database(file_path, dry_run=True):
    """Import Excel file στη βάση δεδομένων"""
    print(f"\n📥 Importing: {os.path.basename(file_path)}")
    print(f"   🔧 Dry run: {dry_run}")
    
    try:
        # Διάβασμα Excel
        df = pd.read_excel(file_path, skiprows=1)
        
        if 'Plant Name' not in df.columns:
            print(f"   ❌ No 'Plant Name' column found")
            return False
        
        # Ομαδοποίηση ανά plant
        plants = df['Plant Name'].unique()
        total_imported = 0
        
        for plant in plants:
            plant_df = df[df['Plant Name'] == plant]
            system_id = map_plant_to_system(plant)
            
            if not system_id:
                print(f"   ⚠️  Unknown plant: {plant} - skipping")
                continue
            
            print(f"   🏠 Processing {plant} → System {system_id}")
            print(f"      📊 Records: {len(plant_df)}")
            
            if not dry_run:
                # Πραγματικό import στη βάση
                imported_count = import_plant_data(plant_df, system_id, plant)
                total_imported += imported_count
                print(f"      ✅ Imported: {imported_count} records")
            else:
                print(f"      🔧 Would import: {len(plant_df)} records")
        
        print(f"   📊 Total imported: {total_imported}")
        return True
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def import_plant_data(plant_df, system_id, plant_name):
    """Import δεδομένων ενός plant στη βάση"""
    
    try:
        conn = psycopg2.connect(
            host="localhost",
            database="solar_prediction",
            user="postgres",
            password="postgres"
        )
        cursor = conn.cursor()
        
        # Καθορισμός target table
        target_table = "solax_data" if system_id == 1 else "solax_data2"
        
        imported_count = 0
        
        for _, row in plant_df.iterrows():
            try:
                # Mapping των columns (θα χρειαστεί προσαρμογή βάσει της πραγματικής δομής)
                timestamp = row.get('Time', None)
                if pd.isna(timestamp):
                    continue
                
                # Δημιουργία record
                record_data = {
                    'timestamp': timestamp,
                    'ac_power': row.get('AC Power(W)', 0),
                    'powerdc1': row.get('PV1 Power(W)', 0),
                    'powerdc2': row.get('PV2 Power(W)', 0),
                    'yield_today': row.get('Today Yield(kWh)', 0),
                    'yield_total': row.get('Total Yield(kWh)', 0),
                    'soc': row.get('SOC(%)', 0),
                    'bat_power': row.get('Battery Power(W)', 0),
                    'feedin_power': row.get('Feed-in Power(W)', 0),
                    'feedin_energy': row.get('Feed-in Energy(kWh)', 0),
                    'consume_energy': row.get('Consumed Energy(kWh)', 0),
                    'temperature': row.get('Temperature(℃)', 0),
                    'system_id': system_id,
                    'inverter_sn': f"EXCEL_IMPORT_{system_id}",
                    'wifi_sn': f"EXCEL_WIFI_{system_id}",
                    'raw_data': json.dumps({
                        'plant_name': plant_name,
                        'source': 'excel_import',
                        'file': os.path.basename(file_path),
                        'imported_at': datetime.now().isoformat()
                    })
                }
                
                # Insert στη βάση
                insert_query = f"""
                    INSERT INTO {target_table} (
                        timestamp, ac_power, powerdc1, powerdc2, yield_today, yield_total,
                        soc, bat_power, feedin_power, feedin_energy, consume_energy, 
                        temperature, system_id, inverter_sn, wifi_sn, raw_data
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                    ) ON CONFLICT (timestamp) DO NOTHING
                """
                
                cursor.execute(insert_query, (
                    record_data['timestamp'],
                    record_data['ac_power'],
                    record_data['powerdc1'],
                    record_data['powerdc2'],
                    record_data['yield_today'],
                    record_data['yield_total'],
                    record_data['soc'],
                    record_data['bat_power'],
                    record_data['feedin_power'],
                    record_data['feedin_energy'],
                    record_data['consume_energy'],
                    record_data['temperature'],
                    record_data['system_id'],
                    record_data['inverter_sn'],
                    record_data['wifi_sn'],
                    record_data['raw_data']
                ))
                
                imported_count += 1
                
            except Exception as e:
                print(f"      ⚠️  Error importing row: {e}")
                continue
        
        conn.commit()
        cursor.close()
        conn.close()
        
        return imported_count
        
    except Exception as e:
        print(f"   ❌ Database error: {e}")
        return 0

def main():
    """Κύρια συνάρτηση"""
    print("📥 EXCEL IMPORT PROCESSOR")
    print("=" * 50)
    
    # Λίστα Excel files για import από data/raw
    excel_files = []

    # Scan System1 and System2 directories
    for system_id in [1, 2]:
        system_dir = f"data/raw/System{system_id}"
        if os.path.exists(system_dir):
            for file in os.listdir(system_dir):
                if file.endswith('.xlsx'):
                    excel_files.append(os.path.join(system_dir, file))

    print(f"Found {len(excel_files)} Excel files to process")
    
    # Φάση 1: Ανάλυση δομής
    print("\n🔍 ΦΑΣΗ 1: ΑΝΑΛΥΣΗ ΔΟΜΗΣ EXCEL FILES")
    
    for file_path in excel_files:
        if os.path.exists(file_path):
            analyze_excel_structure(file_path)
        else:
            print(f"\n❌ File not found: {os.path.basename(file_path)}")
    
    # Φάση 2: Dry run import
    print("\n🔧 ΦΑΣΗ 2: DRY RUN IMPORT")
    
    for file_path in excel_files:
        if os.path.exists(file_path):
            import_excel_to_database(file_path, dry_run=True)
    
    print("\n🎯 ΕΠΟΜΕΝΟ ΒΗΜΑ:")
    print("   Αν η ανάλυση είναι σωστή, τρέξε με dry_run=False για πραγματικό import")

if __name__ == "__main__":
    main()
