#!/usr/bin/env python3
"""
Fixed Comprehensive Analysis
Proper analysis with working queries and realistic predictions
"""

import os
import subprocess
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class FixedAnalyzer:
    """Fixed analyzer with working queries"""
    
    def __init__(self):
        pass
    
    def get_system1_annual_data(self):
        """Get System 1 annual data with simple query"""
        print("📊 SYSTEM 1 ANNUAL DATA")
        print("=" * 25)
        
        try:
            cmd = ['psql', '-h', 'localhost', '-U', 'postgres', '-d', 'solar_prediction', '-c', 
                   "SELECT EXTRACT(year FROM timestamp) as year, COUNT(*) as records, ROUND(AVG(yield_today), 2) as avg_yield FROM solax_data WHERE yield_today >= 0 GROUP BY EXTRACT(year FROM timestamp) ORDER BY year;"]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print("✅ System 1 Annual Performance:")
                print(result.stdout)
                return True
            else:
                print("❌ System 1 query failed")
                return False
        
        except Exception as e:
            print(f"❌ Error: {e}")
            return False
    
    def get_weather_data_simple(self):
        """Get weather data with simple query"""
        print("\n🌤️  WEATHER DATA ANALYSIS")
        print("=" * 25)
        
        try:
            cmd = ['psql', '-h', 'localhost', '-U', 'postgres', '-d', 'solar_prediction', '-c', 
                   "SELECT COUNT(*) as total_weather_records, MIN(timestamp), MAX(timestamp) FROM weather_data;"]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print("✅ Weather Data Summary:")
                print(result.stdout)
                return True
            else:
                print("❌ Weather query failed")
                return False
        
        except Exception as e:
            print(f"❌ Error: {e}")
            return False
    
    def create_annual_comparison_table(self):
        """Create annual comparison table"""
        print("\n📊 ANNUAL COMPARISON TABLE")
        print("=" * 30)
        
        print("System 1 Annual Performance:")
        print("Year | Avg Daily Yield | Records | Performance")
        print("-----|----------------|---------|------------")
        
        # System 1 by year
        for year in [2024, 2025]:
            try:
                cmd = ['psql', '-h', 'localhost', '-U', 'postgres', '-d', 'solar_prediction', '-c', 
                       f"SELECT COUNT(*), ROUND(AVG(yield_today), 2) FROM solax_data WHERE EXTRACT(year FROM timestamp) = {year} AND yield_today >= 0;"]
                
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)
                
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines:
                        if '|' in line and not line.startswith('-'):
                            parts = [p.strip() for p in line.split('|')]
                            if len(parts) >= 2:
                                records = parts[0]
                                avg_yield = parts[1]
                                performance = "Good" if float(avg_yield) > 20 else "Low"
                                print(f"{year} | {avg_yield} kWh/day    | {records}   | {performance}")
            except:
                print(f"{year} | No data        | 0       | N/A")
        
        print("\nSystem 2 Annual Performance:")
        print("Year | Avg Daily Yield | Records | Performance")
        print("-----|----------------|---------|------------")
        
        # System 2 by year
        for year in [2024, 2025]:
            try:
                cmd = ['psql', '-h', 'localhost', '-U', 'postgres', '-d', 'solar_prediction', '-c', 
                       f"SELECT COUNT(*), ROUND(AVG(yield_today), 2) FROM solax_data2 WHERE EXTRACT(year FROM timestamp) = {year} AND yield_today >= 0;"]
                
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)
                
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines:
                        if '|' in line and not line.startswith('-'):
                            parts = [p.strip() for p in line.split('|')]
                            if len(parts) >= 2:
                                records = parts[0]
                                avg_yield = parts[1]
                                performance = "Good" if float(avg_yield) > 20 else "Low"
                                print(f"{year} | {avg_yield} kWh/day    | {records}   | {performance}")
            except:
                print(f"{year} | No data        | 0       | N/A")
    
    def create_monthly_comparison_table(self):
        """Create monthly comparison table"""
        print("\n📅 MONTHLY COMPARISON TABLE")
        print("=" * 32)
        
        print("System 1 Monthly Performance (2024-2025):")
        print("Month    | 2024 Avg | 2025 Avg | Change")
        print("---------|----------|----------|--------")
        
        months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
        
        for month_num in range(1, 13):
            month_name = months[month_num - 1]
            
            # Get 2024 data
            try:
                cmd = ['psql', '-h', 'localhost', '-U', 'postgres', '-d', 'solar_prediction', '-c', 
                       f"SELECT ROUND(AVG(yield_today), 1) FROM solax_data WHERE EXTRACT(year FROM timestamp) = 2024 AND EXTRACT(month FROM timestamp) = {month_num} AND yield_today >= 0;"]
                
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                avg_2024 = "N/A"
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines:
                        if line.strip() and not line.startswith('-') and line.strip() != 'avg':
                            avg_2024 = line.strip()
                            break
            except:
                avg_2024 = "N/A"
            
            # Get 2025 data
            try:
                cmd = ['psql', '-h', 'localhost', '-U', 'postgres', '-d', 'solar_prediction', '-c', 
                       f"SELECT ROUND(AVG(yield_today), 1) FROM solax_data WHERE EXTRACT(year FROM timestamp) = 2025 AND EXTRACT(month FROM timestamp) = {month_num} AND yield_today >= 0;"]
                
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                avg_2025 = "N/A"
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines:
                        if line.strip() and not line.startswith('-') and line.strip() != 'avg':
                            avg_2025 = line.strip()
                            break
            except:
                avg_2025 = "N/A"
            
            # Calculate change
            change = "N/A"
            if avg_2024 != "N/A" and avg_2025 != "N/A" and avg_2024.strip() and avg_2025.strip():
                try:
                    val_2024 = float(avg_2024)
                    val_2025 = float(avg_2025)
                    change_val = ((val_2025 - val_2024) / val_2024) * 100
                    change = f"{change_val:+.1f}%"
                except:
                    change = "N/A"
            
            print(f"{month_name:8} | {avg_2024:8} | {avg_2025:8} | {change}")
        
        print("\nSystem 2 Monthly Performance (2024-2025):")
        print("Month    | 2024 Avg | 2025 Avg | Change")
        print("---------|----------|----------|--------")
        
        for month_num in range(1, 13):
            month_name = months[month_num - 1]
            
            # Get 2024 data
            try:
                cmd = ['psql', '-h', 'localhost', '-U', 'postgres', '-d', 'solar_prediction', '-c', 
                       f"SELECT ROUND(AVG(yield_today), 1) FROM solax_data2 WHERE EXTRACT(year FROM timestamp) = 2024 AND EXTRACT(month FROM timestamp) = {month_num} AND yield_today >= 0;"]
                
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                avg_2024 = "N/A"
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines:
                        if line.strip() and not line.startswith('-') and line.strip() != 'avg':
                            avg_2024 = line.strip()
                            break
            except:
                avg_2024 = "N/A"
            
            # Get 2025 data
            try:
                cmd = ['psql', '-h', 'localhost', '-U', 'postgres', '-d', 'solar_prediction', '-c', 
                       f"SELECT ROUND(AVG(yield_today), 1) FROM solax_data2 WHERE EXTRACT(year FROM timestamp) = 2025 AND EXTRACT(month FROM timestamp) = {month_num} AND yield_today >= 0;"]
                
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                avg_2025 = "N/A"
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines:
                        if line.strip() and not line.startswith('-') and line.strip() != 'avg':
                            avg_2025 = line.strip()
                            break
            except:
                avg_2025 = "N/A"
            
            # Calculate change
            change = "N/A"
            if avg_2024 != "N/A" and avg_2025 != "N/A" and avg_2024.strip() and avg_2025.strip():
                try:
                    val_2024 = float(avg_2024)
                    val_2025 = float(avg_2025)
                    change_val = ((val_2025 - val_2024) / val_2024) * 100
                    change = f"{change_val:+.1f}%"
                except:
                    change = "N/A"
            
            print(f"{month_name:8} | {avg_2024:8} | {avg_2025:8} | {change}")
    
    def create_realistic_predictions_2026(self):
        """Create realistic predictions for 2026 based on historical data"""
        print("\n🔮 REALISTIC 2026 PREDICTIONS")
        print("=" * 32)
        print("Based on historical averages, NOT arbitrary recovery")
        
        # Get historical averages for each system
        systems_data = {}
        
        for system_num in [1, 2]:
            table_name = 'solax_data' if system_num == 1 else 'solax_data2'
            
            try:
                cmd = ['psql', '-h', 'localhost', '-U', 'postgres', '-d', 'solar_prediction', '-c', 
                       f"SELECT ROUND(AVG(yield_today), 2) as historical_avg FROM {table_name} WHERE yield_today >= 0;"]
                
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)
                
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines:
                        if line.strip() and not line.startswith('-') and line.strip() != 'historical_avg':
                            historical_avg = float(line.strip())
                            systems_data[system_num] = historical_avg
                            break
            except:
                systems_data[system_num] = None
        
        print("2026 Predictions (Conservative, based on historical averages):")
        print("System | Historical Avg | 2026 Prediction | Method")
        print("-------|----------------|-----------------|--------")
        
        for system_num in [1, 2]:
            if system_num in systems_data and systems_data[system_num] is not None:
                historical_avg = systems_data[system_num]
                
                # Conservative prediction: slight decline due to aging
                predicted_2026 = historical_avg * 0.98  # 2% decline for aging
                
                print(f"Sys {system_num}  | {historical_avg:.1f} kWh/day    | {predicted_2026:.1f} kWh/day     | Historical avg -2%")
            else:
                print(f"Sys {system_num}  | No data        | No prediction   | Insufficient data")
        
        print("\nApril 2026 Specific Predictions:")
        print("System | April Avg (Historical) | April 2026 Prediction")
        print("-------|------------------------|----------------------")
        
        for system_num in [1, 2]:
            table_name = 'solax_data' if system_num == 1 else 'solax_data2'
            
            try:
                cmd = ['psql', '-h', 'localhost', '-U', 'postgres', '-d', 'solar_prediction', '-c', 
                       f"SELECT ROUND(AVG(yield_today), 1) FROM {table_name} WHERE EXTRACT(month FROM timestamp) = 4 AND yield_today >= 0;"]
                
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)
                
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines:
                        if line.strip() and not line.startswith('-') and line.strip() != 'avg':
                            april_avg = float(line.strip())
                            april_2026_pred = april_avg * 0.98  # Conservative 2% decline
                            print(f"Sys {system_num}  | {april_avg:.1f} kWh/day          | {april_2026_pred:.1f} kWh/day")
                            break
            except:
                print(f"Sys {system_num}  | No April data         | No prediction")

def main():
    """Main analysis function"""
    print("🔍 FIXED COMPREHENSIVE ANALYSIS")
    print("=" * 40)
    print(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🎯 Proper analysis with realistic predictions")
    
    try:
        analyzer = FixedAnalyzer()
        
        # Step 1: Get System 1 annual data
        analyzer.get_system1_annual_data()
        
        # Step 2: Get weather data
        analyzer.get_weather_data_simple()
        
        # Step 3: Create annual comparison table
        analyzer.create_annual_comparison_table()
        
        # Step 4: Create monthly comparison table
        analyzer.create_monthly_comparison_table()
        
        # Step 5: Create realistic 2026 predictions
        analyzer.create_realistic_predictions_2026()
        
        print(f"\n🎉 FIXED ANALYSIS COMPLETED!")
        print("✅ All data properly analyzed")
        print("📊 Realistic predictions generated")
        print("🔄 No arbitrary recovery assumptions")
        
        return True
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
