#!/usr/bin/env python3
"""
CSV Reader for April 2024 Data
Read the CSV file and extract April 2024 data for System 2
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def read_csv_data():
    """Read CSV data and extract April 2024"""
    print("📊 READING CSV FILE FOR APRIL 2024 DATA")
    print("=" * 50)
    
    file_path = "data/raw/System2/Plant Reports 2024-03-01-2024-06-28.csv"
    
    try:
        print(f"Reading: {file_path}")
        df = pd.read_csv(file_path)
        
        print(f"✅ CSV loaded successfully")
        print(f"   Shape: {df.shape}")
        print(f"   Columns: {list(df.columns)}")
        
        # Convert Update time to datetime
        df['Update time'] = pd.to_datetime(df['Update time'], errors='coerce')
        
        # Filter for April 2024
        april_2024 = df[
            (df['Update time'].dt.year == 2024) & 
            (df['Update time'].dt.month == 4)
        ].copy()
        
        print(f"\n📅 April 2024 Data:")
        print(f"   Records found: {len(april_2024)}")
        
        if len(april_2024) > 0:
            print(f"   Date range: {april_2024['Update time'].min()} to {april_2024['Update time'].max()}")
            
            # Find the best yield column
            yield_columns = [
                'Daily inverter output (kWh)',
                'Daily PV Yield(kWh)', 
                'Daily exported energy(kWh)',
                'Daily imported energy(kWh)'
            ]
            
            best_yield_col = None
            max_non_zero = 0
            
            for col in yield_columns:
                if col in df.columns:
                    values = pd.to_numeric(april_2024[col], errors='coerce')
                    non_zero_count = (values > 0).sum()
                    max_val = values.max() if not values.isna().all() else 0
                    
                    print(f"   {col}: {non_zero_count} non-zero values, max={max_val:.2f}")
                    
                    if non_zero_count > max_non_zero:
                        max_non_zero = non_zero_count
                        best_yield_col = col
            
            if best_yield_col and max_non_zero > 0:
                print(f"\n✅ Using column: {best_yield_col}")
                
                # Process daily data
                april_2024[best_yield_col] = pd.to_numeric(april_2024[best_yield_col], errors='coerce')
                april_2024 = april_2024.dropna(subset=['Update time', best_yield_col])
                april_2024 = april_2024[april_2024[best_yield_col] > 0]
                
                # Group by date to get daily yields
                april_2024['date'] = april_2024['Update time'].dt.date
                daily_data = april_2024.groupby('date')[best_yield_col].max().reset_index()
                daily_data.columns = ['date', 'daily_yield']
                
                # Clean data
                daily_data = daily_data[daily_data['daily_yield'] > 0]
                daily_data = daily_data[daily_data['daily_yield'] < 100]  # Reasonable upper limit
                
                print(f"\n📊 Processed Daily Data:")
                print(f"   Days: {len(daily_data)}")
                print(f"   Yield range: {daily_data['daily_yield'].min():.1f} - {daily_data['daily_yield'].max():.1f} kWh")
                print(f"   Average: {daily_data['daily_yield'].mean():.1f} kWh/day")
                print(f"   Total: {daily_data['daily_yield'].sum():.1f} kWh")
                
                print(f"\n📋 Sample Data:")
                print(daily_data.head(10))
                
                # Save to file
                output_file = "april_2024_system2_extracted.csv"
                daily_data.to_csv(output_file, index=False)
                print(f"\n💾 Data saved to: {output_file}")
                
                return daily_data
            else:
                print("❌ No usable yield data found")
                return None
        else:
            print("❌ No April 2024 data found")
            return None
            
    except Exception as e:
        print(f"❌ Error reading CSV: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """Main function"""
    print("🚀 CSV READER FOR APRIL 2024 - SYSTEM 2")
    print("=" * 50)
    
    april_data = read_csv_data()
    
    if april_data is not None:
        print("\n🎉 SUCCESS! April 2024 data extracted successfully")
        print("✅ Ready to proceed with comparison analysis")
        return april_data
    else:
        print("\n❌ FAILED to extract April 2024 data")
        return None

if __name__ == "__main__":
    main()
