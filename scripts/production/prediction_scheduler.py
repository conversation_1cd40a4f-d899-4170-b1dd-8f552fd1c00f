#!/usr/bin/env python3
"""
Prediction Scheduler Service
===========================

Automated scheduling system for solar predictions:
- Daily prediction refresh at 6:00 AM
- Manual trigger via API or Telegram
- Batch processing for all systems and periods
- Performance monitoring and alerts
- Fallback mechanisms for failures

Created: June 9, 2025
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import asyncio
import logging
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import aiohttp
import schedule
from threading import Thread

# FastAPI
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# Database
import asyncpg

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PredictionScheduler:
    """
    Automated prediction scheduling service
    """
    
    def __init__(self):
        """Initialize prediction scheduler"""
        self.app = FastAPI(
            title="Prediction Scheduler",
            description="Automated solar prediction scheduling service",
            version="1.0.0"
        )
        
        # Add CORS middleware
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # Configuration with environment variable support
        self.config = {
            'daily_schedule_time': os.getenv('SCHEDULE_TIME', '06:00'),  # 6:00 AM daily refresh
            'systems': ['system1', 'system2'],
            'prediction_periods': [24, 48, 72, 168],  # hours
            'apis': {
                'gpu_prediction': os.getenv('GPU_PREDICTION_URL', 'http://solar-prediction-gpu:8105'),
                'production_scripts': os.getenv('PRODUCTION_SCRIPTS_URL', 'http://solar-prediction-main:8100'),
                'telegram_bot': os.getenv('TELEGRAM_BOT_URL', 'http://solar-prediction-telegram:8106')  # If available
            },
            'retry_attempts': int(os.getenv('RETRY_ATTEMPTS', '3')),
            'retry_delay': int(os.getenv('RETRY_DELAY', '30'))  # seconds
        }
        
        # Status tracking
        self.last_run = None
        self.next_run = None
        self.run_history = []
        self.is_running = False
        
        # Database connection
        self.db_pool = None
        
        # Setup routes
        self._setup_routes()
        
        # Setup scheduler
        self._setup_scheduler()
        
        logger.info("📅 Prediction Scheduler initialized")
    
    def _setup_routes(self):
        """Setup API routes"""
        
        @self.app.on_event("startup")
        async def startup_event():
            """Startup event handler"""
            try:
                # Initialize database connection
                await self._init_database()
                
                # Start scheduler thread
                self._start_scheduler_thread()
                
                logger.info("📅 Prediction Scheduler startup completed")
                
            except Exception as e:
                logger.error(f"❌ Startup failed: {e}")
                raise
        
        @self.app.get("/health")
        async def health_check():
            """Health check endpoint"""
            return {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "last_run": self.last_run.isoformat() if self.last_run else None,
                "next_run": self.next_run.isoformat() if self.next_run else None,
                "is_running": self.is_running,
                "runs_today": len([r for r in self.run_history if r['timestamp'].date() == datetime.now().date()]),
                "config": self.config
            }
        
        @self.app.post("/schedule/run")
        async def manual_run(background_tasks: BackgroundTasks):
            """Manually trigger prediction refresh"""
            if self.is_running:
                raise HTTPException(status_code=409, detail="Prediction refresh already running")
            
            background_tasks.add_task(self._run_prediction_refresh, manual=True)
            
            return {
                "status": "started",
                "message": "Manual prediction refresh started",
                "timestamp": datetime.now().isoformat()
            }
        
        @self.app.get("/schedule/status")
        async def get_status():
            """Get scheduler status"""
            return {
                "is_running": self.is_running,
                "last_run": self.last_run.isoformat() if self.last_run else None,
                "next_run": self.next_run.isoformat() if self.next_run else None,
                "daily_schedule": self.config['daily_schedule_time'],
                "run_history": [
                    {
                        "timestamp": r['timestamp'].isoformat(),
                        "duration_seconds": r['duration_seconds'],
                        "success": r['success'],
                        "predictions_generated": r['predictions_generated'],
                        "manual": r.get('manual', False)
                    }
                    for r in self.run_history[-10:]  # Last 10 runs
                ]
            }
        
        @self.app.post("/schedule/config")
        async def update_config(config: dict):
            """Update scheduler configuration"""
            try:
                # Validate config
                if 'daily_schedule_time' in config:
                    # Validate time format
                    datetime.strptime(config['daily_schedule_time'], '%H:%M')
                
                # Update configuration
                self.config.update(config)
                
                # Reschedule if time changed
                if 'daily_schedule_time' in config:
                    self._setup_scheduler()
                
                return {
                    "status": "success",
                    "message": "Configuration updated",
                    "config": self.config
                }
                
            except Exception as e:
                logger.error(f"❌ Config update failed: {e}")
                raise HTTPException(status_code=400, detail=str(e))
        
        @self.app.get("/schedule/history")
        async def get_history(days: int = 7):
            """Get prediction run history"""
            cutoff_date = datetime.now() - timedelta(days=days)
            
            filtered_history = [
                {
                    "timestamp": r['timestamp'].isoformat(),
                    "duration_seconds": r['duration_seconds'],
                    "success": r['success'],
                    "predictions_generated": r['predictions_generated'],
                    "manual": r.get('manual', False),
                    "error": r.get('error')
                }
                for r in self.run_history
                if r['timestamp'] >= cutoff_date
            ]
            
            return {
                "history": filtered_history,
                "total_runs": len(filtered_history),
                "success_rate": sum(1 for r in filtered_history if r['success']) / len(filtered_history) if filtered_history else 0,
                "days": days
            }
    
    def _setup_scheduler(self):
        """Setup daily scheduler"""
        try:
            # Clear existing schedule
            schedule.clear()
            
            # Schedule daily prediction refresh
            schedule.every().day.at(self.config['daily_schedule_time']).do(
                self._schedule_prediction_refresh
            )
            
            # Calculate next run time
            self._update_next_run_time()
            
            logger.info(f"📅 Scheduled daily prediction refresh at {self.config['daily_schedule_time']}")
            
        except Exception as e:
            logger.error(f"❌ Scheduler setup failed: {e}")
    
    def _start_scheduler_thread(self):
        """Start scheduler in background thread"""
        def run_scheduler():
            while True:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
        
        scheduler_thread = Thread(target=run_scheduler, daemon=True)
        scheduler_thread.start()
        
        logger.info("📅 Scheduler thread started")
    
    def _schedule_prediction_refresh(self):
        """Scheduled prediction refresh (called by schedule)"""
        asyncio.create_task(self._run_prediction_refresh(manual=False))
    
    async def _run_prediction_refresh(self, manual: bool = False):
        """Run prediction refresh for all systems and periods"""
        if self.is_running:
            logger.warning("⚠️ Prediction refresh already running, skipping")
            return
        
        self.is_running = True
        start_time = time.time()
        predictions_generated = 0
        success = True
        error_message = None
        
        try:
            logger.info(f"🚀 Starting prediction refresh (manual: {manual})")
            
            # Use GPU prediction service
            try:
                predictions_generated = await self._refresh_gpu_predictions()
                logger.info(f"✅ GPU predictions completed: {predictions_generated}")
            except Exception as e:
                logger.error(f"❌ GPU predictions failed: {e}")
                success = False
                error_message = str(e)
            
            # Update last run time
            self.last_run = datetime.now()
            self._update_next_run_time()
            
        except Exception as e:
            logger.error(f"❌ Prediction refresh failed: {e}")
            success = False
            error_message = str(e)
        
        finally:
            self.is_running = False
            duration = time.time() - start_time
            
            # Record run history
            run_record = {
                'timestamp': datetime.now(),
                'duration_seconds': round(duration, 2),
                'success': success,
                'predictions_generated': predictions_generated,
                'manual': manual,
                'error': error_message
            }
            
            self.run_history.append(run_record)
            
            # Keep only last 100 runs
            if len(self.run_history) > 100:
                self.run_history = self.run_history[-100:]
            
            # Log to database
            await self._log_run_to_database(run_record)
            
            logger.info(f"📅 Prediction refresh completed in {duration:.2f}s (success: {success})")
    
    async def _refresh_gpu_predictions(self) -> int:
        """Refresh predictions using GPU service"""
        predictions_count = 0
        
        async with aiohttp.ClientSession() as session:
            # Batch refresh all predictions
            batch_request = {
                'systems': self.config['systems'],
                'hours_list': self.config['prediction_periods']
            }
            
            async with session.post(
                f"{self.config['apis']['gpu_prediction']}/api/gpu/predict/batch",
                json=batch_request,
                timeout=300  # 5 minutes timeout
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    predictions_count = result.get('systems_processed', 0) * result.get('periods_processed', 0)
                else:
                    raise Exception(f"GPU batch prediction failed: {response.status}")
        
        return predictions_count
    
    async def _refresh_production_predictions(self) -> int:
        """Refresh predictions using production scripts API"""
        predictions_count = 0
        
        async with aiohttp.ClientSession() as session:
            for system_id in self.config['systems']:
                for hours in self.config['prediction_periods']:
                    try:
                        async with session.get(
                            f"{self.config['apis']['production_scripts']}/api/v1/forecast/{hours}h/{system_id}",
                            timeout=60
                        ) as response:
                            if response.status == 200:
                                predictions_count += 1
                            else:
                                logger.warning(f"⚠️ Prediction failed for {system_id} {hours}h: {response.status}")
                    except Exception as e:
                        logger.warning(f"⚠️ Prediction error for {system_id} {hours}h: {e}")
        
        return predictions_count
    
    def _update_next_run_time(self):
        """Update next scheduled run time"""
        try:
            # Get next scheduled run
            jobs = schedule.get_jobs()
            if jobs:
                self.next_run = jobs[0].next_run
            else:
                self.next_run = None
        except Exception as e:
            logger.error(f"❌ Next run time update failed: {e}")
            self.next_run = None
    
    async def _init_database(self):
        """Initialize database connection"""
        try:
            # Use environment variables for database connection
            db_host = os.getenv('DB_HOST', os.getenv('DATABASE_HOST', 'localhost'))
            db_name = os.getenv('DB_NAME', os.getenv('DATABASE_NAME', 'solar_prediction'))
            db_user = os.getenv('DB_USER', os.getenv('DATABASE_USER', 'postgres'))
            db_password = os.getenv('DB_PASSWORD', os.getenv('DATABASE_PASSWORD', 'postgres'))
            db_port = int(os.getenv('DB_PORT', os.getenv('DATABASE_PORT', '5433')))

            self.db_pool = await asyncpg.create_pool(
                host=db_host,
                port=db_port,
                database=db_name,
                user=db_user,
                password=db_password,
                min_size=1,
                max_size=5
            )
            
            # Create scheduler_runs table if not exists
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    CREATE TABLE IF NOT EXISTS scheduler_runs (
                        id SERIAL PRIMARY KEY,
                        timestamp TIMESTAMP DEFAULT NOW(),
                        duration_seconds FLOAT NOT NULL,
                        success BOOLEAN NOT NULL,
                        predictions_generated INTEGER NOT NULL,
                        manual BOOLEAN DEFAULT FALSE,
                        error_message TEXT
                    )
                """)
            
            logger.info("✅ Database connection initialized")
            
        except Exception as e:
            logger.warning(f"⚠️ Database connection failed: {e}")
    
    async def _log_run_to_database(self, run_record: Dict):
        """Log run record to database"""
        try:
            if not self.db_pool:
                return
            
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO scheduler_runs 
                    (timestamp, duration_seconds, success, predictions_generated, manual, error_message)
                    VALUES ($1, $2, $3, $4, $5, $6)
                """, 
                run_record['timestamp'],
                run_record['duration_seconds'],
                run_record['success'],
                run_record['predictions_generated'],
                run_record['manual'],
                run_record.get('error')
                )
                
        except Exception as e:
            logger.error(f"❌ Database logging failed: {e}")

# Global app instance
scheduler = PredictionScheduler()
app = scheduler.app

def main():
    """Main function"""
    logger.info("📅 Starting Prediction Scheduler Service")
    
    uvicorn.run(
        "scripts.production.prediction_scheduler:app",
        host="0.0.0.0",
        port=8106,
        reload=False,
        access_log=True
    )

if __name__ == "__main__":
    main()
