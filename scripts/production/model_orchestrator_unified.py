#!/usr/bin/env python3
"""
Unified Model Orchestrator με Unified Preprocessing Pipeline
===========================================================

Ενημερωμένο model orchestrator που χρησιμοποιεί το unified preprocessing pipeline
για consistent feature engineering και normalization σε όλες τις προβλέψεις.

Βασισμένο στο: model_orchestrator.py
Ενημερώθηκε: 2025-06-05

Features:
- Unified preprocessing pipeline integration
- Automatic model selection (seasonal vs multi-horizon)
- Version-controlled scalers
- Consistent feature engineering
- Fallback mechanisms
"""

import os
import sys
import json
import joblib
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
import pandas as pd
import numpy as np

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

# Import unified preprocessing pipeline
from src.preprocessing.unified_pipeline import create_unified_pipeline
from src.preprocessing.pipeline_config import create_default_pipeline_config, PipelineValidator

logger = logging.getLogger(__name__)

class UnifiedModelOrchestrator:
    """
    Unified Model Orchestrator με Preprocessing Pipeline Integration
    
    Features:
    - Auto-selects appropriate models based on time horizon and season
    - Uses unified preprocessing pipeline για consistent feature engineering
    - Caches loaded models and scalers για performance
    - Unified prediction interface με fallback mechanisms
    - Version-controlled preprocessing
    """
    
    def __init__(self, models_dir: str = "models", pipeline_version: str = "v1.0.0"):
        self.models_dir = Path(models_dir)
        self.pipeline_version = pipeline_version
        
        # Initialize unified preprocessing pipeline
        self.pipeline = create_unified_pipeline(pipeline_version)
        self.pipeline_loaded = False
        
        # Model caches
        self.loaded_models = {}
        self.available_models = {}
        
        # Season mapping
        self.season_months = {
            'spring': [3, 4, 5],
            'summer': [6, 7, 8], 
            'autumn': [9, 10, 11],
            'winter': [12, 1, 2]
        }
        
        # Initialize
        self._discover_models()
        self._load_preprocessing_pipeline()
        
        logger.info(f"🏗️ Initialized UnifiedModelOrchestrator with pipeline {pipeline_version}")
    
    def _discover_models(self):
        """Discover available models"""
        logger.info("🔍 Discovering available models...")
        
        # Discover multi-horizon models
        for system_id in [1, 2]:
            for horizon in ['hourly', 'daily', 'monthly', 'yearly']:
                model_dir = self.models_dir / f"multi_horizon_{horizon}_system{system_id}"
                if model_dir.exists() and (model_dir / "model.joblib").exists():
                    model_name = f"multi_horizon_{horizon}_system{system_id}"
                    self.available_models[model_name] = {
                        'path': model_dir,
                        'type': 'multi_horizon',
                        'system_id': system_id,
                        'horizon': horizon
                    }
        
        # Discover seasonal models
        seasonal_dir = self.models_dir / "seasonal_models"
        if seasonal_dir.exists():
            for system_id in [1, 2]:
                for season in ['spring', 'summer', 'autumn', 'winter']:
                    model_dir = seasonal_dir / f"{season}_system{system_id}"
                    if model_dir.exists() and (model_dir / "model.joblib").exists():
                        model_name = f"{season}_system{system_id}"
                        self.available_models[model_name] = {
                            'path': model_dir,
                            'type': 'seasonal',
                            'system_id': system_id,
                            'season': season
                        }
        
        logger.info(f"📊 Found {len(self.available_models)} available models")
        
        # Log model summary
        multi_horizon_count = len([m for m in self.available_models.values() if m['type'] == 'multi_horizon'])
        seasonal_count = len([m for m in self.available_models.values() if m['type'] == 'seasonal'])
        logger.info(f"   Multi-horizon: {multi_horizon_count}, Seasonal: {seasonal_count}")
    
    def _load_preprocessing_pipeline(self):
        """Load unified preprocessing pipeline με scalers"""
        logger.info("⚖️ Loading unified preprocessing pipeline...")
        
        try:
            # Load scalers από preprocessing directory
            preprocessing_dir = self.models_dir / "preprocessing"
            
            if preprocessing_dir.exists():
                loaded_scalers = self.pipeline.load_scalers(preprocessing_dir, self.pipeline_version)
                
                if loaded_scalers:
                    self.pipeline_loaded = True
                    logger.info(f"✅ Loaded {len(loaded_scalers)} scalers for pipeline {self.pipeline_version}")
                else:
                    logger.warning("⚠️ No scalers found, will use individual model scalers")
            else:
                logger.warning(f"⚠️ Preprocessing directory not found: {preprocessing_dir}")
                
        except Exception as e:
            logger.error(f"❌ Failed to load preprocessing pipeline: {e}")
            self.pipeline_loaded = False
    
    def _get_season(self, timestamp: datetime) -> str:
        """Determine season από timestamp"""
        month = timestamp.month
        
        for season, months in self.season_months.items():
            if month in months:
                return season
        
        return 'spring'  # Default fallback
    
    def _load_model(self, model_name: str) -> Dict[str, Any]:
        """Load model components με caching"""
        
        if model_name in self.loaded_models:
            return self.loaded_models[model_name]
        
        if model_name not in self.available_models:
            raise ValueError(f"Model {model_name} not available")
        
        model_info = self.available_models[model_name]
        model_dir = model_info['path']
        
        try:
            # Load model
            model = joblib.load(model_dir / "model.joblib")
            
            # Load scaler (prefer unified pipeline scaler)
            scaler = None
            if self.pipeline_loaded:
                # Use unified pipeline scaler
                if model_info['type'] == 'multi_horizon':
                    scaler_key = f"multi_horizon_{self.pipeline_version}"
                else:
                    scaler_key = f"seasonal_{self.pipeline_version}"
                
                scaler = self.pipeline.scalers.get(scaler_key)
                
            if scaler is None:
                # Fallback to individual model scaler
                scaler_path = model_dir / "scaler.joblib"
                if scaler_path.exists():
                    scaler = joblib.load(scaler_path)
                    logger.info(f"📂 Using individual scaler for {model_name}")
                else:
                    raise FileNotFoundError(f"No scaler found for {model_name}")
            
            # Load metadata
            metadata = {}
            metadata_path = model_dir / "metadata.json"
            if metadata_path.exists():
                with open(metadata_path, 'r') as f:
                    metadata = json.load(f)
            
            # Cache loaded components
            components = {
                'model': model,
                'scaler': scaler,
                'metadata': metadata,
                'model_info': model_info
            }
            
            self.loaded_models[model_name] = components
            logger.info(f"📂 Loaded model: {model_name}")
            
            return components
            
        except Exception as e:
            logger.error(f"❌ Failed to load model {model_name}: {e}")
            raise
    
    def _select_best_model(self, system_id: int, timestamp: datetime, 
                          prediction_type: str = "daily") -> str:
        """Select the best model για given parameters"""
        
        # Determine season
        season = self._get_season(timestamp)
        
        # Prefer seasonal models για daily predictions
        if prediction_type == "daily":
            seasonal_model = f"{season}_system{system_id}"
            if seasonal_model in self.available_models:
                return seasonal_model
        
        # Fallback to multi-horizon models
        horizon_map = {
            "hourly": "hourly",
            "daily": "daily", 
            "monthly": "monthly",
            "yearly": "yearly"
        }
        
        horizon = horizon_map.get(prediction_type, "daily")
        multi_horizon_model = f"multi_horizon_{horizon}_system{system_id}"
        
        if multi_horizon_model in self.available_models:
            return multi_horizon_model
        
        # System fallback
        fallback_system = 2 if system_id == 1 else 1
        
        # Try seasonal fallback
        if prediction_type == "daily":
            fallback_seasonal = f"{season}_system{fallback_system}"
            if fallback_seasonal in self.available_models:
                logger.warning(f"Using system fallback: {fallback_seasonal}")
                return fallback_seasonal
        
        # Try multi-horizon fallback
        fallback_multi_horizon = f"multi_horizon_{horizon}_system{fallback_system}"
        if fallback_multi_horizon in self.available_models:
            logger.warning(f"Using system fallback: {fallback_multi_horizon}")
            return fallback_multi_horizon
        
        # Last resort: any available model για the system
        available_for_system = [name for name, info in self.available_models.items() 
                               if info['system_id'] == system_id]
        
        if available_for_system:
            fallback_model = available_for_system[0]
            logger.warning(f"Using last resort fallback: {fallback_model}")
            return fallback_model
        
        raise ValueError(f"No suitable model found for system {system_id}, type {prediction_type}")
    
    def _prepare_features_unified(self, timestamp: datetime, weather_data: Optional[Dict],
                                 system_id: int, model_info: Dict) -> pd.DataFrame:
        """Prepare features using unified preprocessing pipeline"""
        
        # Create input data DataFrame
        input_data = pd.DataFrame({
            'timestamp': [timestamp],
            'system_id': [system_id],
            'yield_today': [0],  # Placeholder για prediction
            'soc': [weather_data.get('battery_soc', 50) if weather_data else 50],
            'bat_power': [0],  # Placeholder
            'temperature': [weather_data.get('temperature', 20) if weather_data else 20],
            'global_horizontal_irradiance': [weather_data.get('ghi', 500) if weather_data else 500],
            'temperature_2m': [weather_data.get('temperature', 20) if weather_data else 20],
            'relative_humidity_2m': [weather_data.get('humidity', 60) if weather_data else 60],
            'cloud_cover': [weather_data.get('cloud_cover', 30) if weather_data else 30]
        })
        
        # Use unified pipeline για feature engineering
        if self.pipeline_loaded:
            processed_data = self.pipeline.engineer_features(input_data)
            return processed_data
        else:
            # Fallback manual feature engineering
            processed_data = input_data.copy()
            processed_data['hour'] = timestamp.hour
            processed_data['hour_sin'] = np.sin(2 * np.pi * timestamp.hour / 24)
            processed_data['hour_cos'] = np.cos(2 * np.pi * timestamp.hour / 24)
            processed_data['ghi'] = processed_data['global_horizontal_irradiance']
            processed_data['air_temp'] = processed_data['temperature_2m']
            processed_data['humidity'] = processed_data['relative_humidity_2m']
            
            return processed_data
    
    def predict_single(self, system_id: int, timestamp: datetime, 
                      weather_data: Optional[Dict] = None,
                      prediction_type: str = "daily") -> Dict[str, Any]:
        """
        Generate single prediction using unified preprocessing pipeline
        
        Args:
            system_id: Solar system ID (1 or 2)
            timestamp: Prediction timestamp
            weather_data: Optional weather conditions
            prediction_type: Type of prediction (daily, hourly, etc.)
            
        Returns:
            Prediction result dict με unified preprocessing
        """
        try:
            # Select best model
            model_name = self._select_best_model(system_id, timestamp, prediction_type)
            
            # Load model components
            model_components = self._load_model(model_name)
            model = model_components["model"]
            scaler = model_components["scaler"]
            metadata = model_components["metadata"]
            model_info = model_components["model_info"]
            
            # Prepare features using unified pipeline
            processed_data = self._prepare_features_unified(timestamp, weather_data, system_id, model_info)
            
            # Transform features για το συγκεκριμένο model group
            if self.pipeline_loaded:
                if model_info['type'] == 'multi_horizon':
                    features_array = self.pipeline.transform_for_group(processed_data, 'multi_horizon')
                else:
                    features_array = self.pipeline.transform_for_group(processed_data, 'seasonal')
            else:
                # Fallback transformation
                if model_info['type'] == 'multi_horizon':
                    feature_cols = ['soc', 'bat_power', 'temperature', 'ghi', 'air_temp', 'humidity', 'cloud_cover', 'hour']
                    # Ensure all columns exist
                    for col in feature_cols:
                        if col not in processed_data.columns:
                            processed_data[col] = 0
                else:
                    feature_cols = ['hour_sin', 'hour_cos', 'temperature', 'cloud_cover', 'ghi', 'soc']
                    # Ensure all columns exist
                    for col in feature_cols:
                        if col not in processed_data.columns:
                            processed_data[col] = 0

                features_df = processed_data[feature_cols]
                features_array = scaler.transform(features_df)
            
            # Make prediction
            prediction_value = model.predict(features_array)[0]
            prediction_value = max(0, prediction_value)  # Ensure non-negative
            
            # Calculate confidence
            confidence = metadata.get("performance", {}).get("r2", 0.9)
            
            # Determine season για response
            season = self._get_season(timestamp)
            
            return {
                "system_id": system_id,
                "timestamp": timestamp.isoformat(),
                "season": season,
                "prediction": round(prediction_value, 1),
                "confidence": round(confidence, 3),
                "model_used": model_name,
                "model_type": model_info['type'],
                "pipeline_version": self.pipeline_version,
                "preprocessing_method": "unified_pipeline" if self.pipeline_loaded else "fallback",
                "status": "success"
            }
            
        except Exception as e:
            logger.error(f"Prediction failed: {e}")
            return {
                "system_id": system_id,
                "timestamp": timestamp.isoformat(),
                "error": str(e),
                "status": "failed"
            }
    
    def predict_range(self, system_id: int, start_time: datetime, end_time: datetime,
                     granularity: str = "hourly") -> List[Dict[str, Any]]:
        """Generate range predictions με unified preprocessing"""
        
        predictions = []
        current_time = start_time
        
        # Determine time step
        if granularity == "hourly":
            time_step = timedelta(hours=1)
        elif granularity == "daily":
            time_step = timedelta(days=1)
        else:
            time_step = timedelta(hours=1)  # Default
        
        while current_time <= end_time:
            prediction = self.predict_single(
                system_id=system_id,
                timestamp=current_time,
                prediction_type=granularity.rstrip('ly')  # hourly -> hour, daily -> dai
            )
            
            predictions.append(prediction)
            current_time += time_step
        
        return predictions
    
    def get_model_status(self) -> Dict[str, Any]:
        """Get comprehensive model status"""
        
        return {
            "status": "operational" if self.available_models else "no_models",
            "pipeline_version": self.pipeline_version,
            "pipeline_loaded": self.pipeline_loaded,
            "total_models": len(self.available_models),
            "loaded_models": len(self.loaded_models),
            "model_breakdown": {
                "multi_horizon": len([m for m in self.available_models.values() if m['type'] == 'multi_horizon']),
                "seasonal": len([m for m in self.available_models.values() if m['type'] == 'seasonal'])
            },
            "available_models": list(self.available_models.keys()),
            "preprocessing_status": {
                "unified_pipeline": self.pipeline_loaded,
                "scalers_loaded": len(self.pipeline.scalers) if self.pipeline_loaded else 0
            }
        }

# Factory function
def create_unified_orchestrator(models_dir: str = "models", pipeline_version: str = "v1.0.0") -> UnifiedModelOrchestrator:
    """Factory function για unified orchestrator"""
    return UnifiedModelOrchestrator(models_dir=models_dir, pipeline_version=pipeline_version)

if __name__ == "__main__":
    # Demo usage
    orchestrator = create_unified_orchestrator()
    status = orchestrator.get_model_status()
    print(f"🎯 Unified Model Orchestrator Status: {status}")
    
    # Test prediction
    test_prediction = orchestrator.predict_single(
        system_id=1,
        timestamp=datetime.now(),
        weather_data={'temperature': 25, 'cloud_cover': 30, 'battery_soc': 75}
    )
    print(f"🔮 Test prediction: {test_prediction}")
