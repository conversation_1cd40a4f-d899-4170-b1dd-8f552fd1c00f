#!/usr/bin/env python3
"""
Corrected Model Validation
Fix prediction scaling issues and create accurate model
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import pandas as pd
import numpy as np
import psycopg2
import joblib
import json
import lightgbm as lgb
from sklearn.ensemble import VotingRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from datetime import datetime, timedelta
import logging
from pathlib import Path
from dotenv import load_dotenv

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CorrectedModelValidator:
    """Corrected model validation with proper scaling"""
    
    def __init__(self):
        load_dotenv()
        self.project_root = Path("/home/<USER>/solar-prediction-project")
        self.models_dir = self.project_root / "models" / "corrected_final"
        self.models_dir.mkdir(parents=True, exist_ok=True)
        
        # Real data for validation
        self.real_data = {
            '2025-06-01': {'system1': 72.8, 'system2': 67.7},
            '2025-06-02': {'system1': 31.8, 'system2': 34.0}  # partial day
        }
        
    def connect_database(self):
        """Connect to database"""
        try:
            conn = psycopg2.connect(
                host=os.getenv('DB_HOST', 'localhost'),
                database=os.getenv('DB_NAME', 'solar_prediction'),
                user=os.getenv('DB_USER', 'postgres'),
                password=os.getenv('DB_PASSWORD', 'postgres')
            )
            return conn
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            return None
    
    def load_and_analyze_data(self, conn):
        """Load data and analyze the scaling issue"""
        logger.info("🔍 ANALYZING DATA SCALING ISSUE")
        logger.info("=" * 60)
        
        try:
            # Get sample data to understand the scaling
            query = """
            WITH sample_data AS (
                SELECT 
                    timestamp, ac_power, soc, bat_power, powerdc1, powerdc2,
                    yield_today, 1 as system_id
                FROM solax_data
                WHERE DATE(timestamp) = '2025-06-01'
                AND ac_power IS NOT NULL
                ORDER BY timestamp
                LIMIT 100
            ),
            weather_data AS (
                SELECT
                    DATE_TRUNC('hour', timestamp) as hour_timestamp,
                    AVG(COALESCE(ghi, 400)) as ghi,
                    AVG(COALESCE(temperature, 20)) as temperature,
                    AVG(COALESCE(cloud_cover, 50)) as cloud_cover
                FROM cams_radiation_data
                WHERE DATE(timestamp) = '2025-06-01'
                GROUP BY DATE_TRUNC('hour', timestamp)
            )
            SELECT
                sd.*,
                COALESCE(w.ghi, 400) as ghi,
                COALESCE(w.temperature, 20) as temperature,
                COALESCE(w.cloud_cover, 50) as cloud_cover
            FROM sample_data sd
            LEFT JOIN weather_data w ON DATE_TRUNC('hour', sd.timestamp) = w.hour_timestamp
            ORDER BY sd.timestamp
            """
            
            df = pd.read_sql(query, conn)
            
            if len(df) == 0:
                logger.error("❌ No sample data found")
                return None
            
            # Analyze the data
            logger.info(f"📊 Sample data analysis:")
            logger.info(f"   Records: {len(df)}")
            logger.info(f"   AC Power range: {df['ac_power'].min():.0f}W to {df['ac_power'].max():.0f}W")
            logger.info(f"   AC Power mean: {df['ac_power'].mean():.0f}W")
            logger.info(f"   Daily yield: {df['yield_today'].max():.1f}kWh")
            
            # Calculate energy from power (5-minute intervals)
            positive_power = df[df['ac_power'] > 0]['ac_power']
            calculated_energy = positive_power.sum() / 12 / 1000  # 5-min to kWh
            
            logger.info(f"   Calculated energy from power: {calculated_energy:.1f}kWh")
            logger.info(f"   Ratio (calculated/actual): {calculated_energy / df['yield_today'].max():.2f}")
            
            return df
            
        except Exception as e:
            logger.error(f"❌ Data analysis failed: {e}")
            return None
    
    def create_corrected_model(self, conn):
        """Create a corrected model with proper target variable"""
        logger.info("🔧 CREATING CORRECTED MODEL")
        logger.info("=" * 60)
        
        try:
            # Load training data with proper target
            query = """
            WITH system1_data AS (
                SELECT 
                    timestamp, ac_power, soc, bat_power, powerdc1, powerdc2,
                    yield_today, 1 as system_id
                FROM solax_data
                WHERE timestamp >= '2024-03-01'
                AND ac_power IS NOT NULL AND ac_power >= 0 AND ac_power <= 15000
                AND soc IS NOT NULL AND soc >= 0 AND soc <= 100
                AND bat_power IS NOT NULL AND bat_power >= -10000 AND bat_power <= 10000
            ),
            system2_data AS (
                SELECT 
                    timestamp, ac_power, soc, bat_power, powerdc1, powerdc2,
                    yield_today, 2 as system_id
                FROM solax_data2
                WHERE timestamp >= '2024-03-01'
                AND ac_power IS NOT NULL AND ac_power >= 0 AND ac_power <= 15000
                AND soc IS NOT NULL AND soc >= 0 AND soc <= 100
                AND bat_power IS NOT NULL AND bat_power >= -10000 AND bat_power <= 10000
            ),
            combined_data AS (
                SELECT * FROM system1_data
                UNION ALL
                SELECT * FROM system2_data
            ),
            weather_data AS (
                SELECT
                    DATE_TRUNC('hour', timestamp) as hour_timestamp,
                    AVG(COALESCE(ghi, 400)) as ghi,
                    AVG(COALESCE(temperature, 20)) as temperature,
                    AVG(COALESCE(cloud_cover, 50)) as cloud_cover
                FROM cams_radiation_data
                WHERE timestamp >= '2024-03-01'
                GROUP BY DATE_TRUNC('hour', timestamp)
            )
            SELECT
                cd.*,
                COALESCE(w.ghi, 400) as ghi,
                COALESCE(w.temperature, 20) as temperature,
                COALESCE(w.cloud_cover, 50) as cloud_cover
            FROM combined_data cd
            LEFT JOIN weather_data w ON DATE_TRUNC('hour', cd.timestamp) = w.hour_timestamp
            ORDER BY cd.timestamp, cd.system_id
            """
            
            df = pd.read_sql(query, conn)
            logger.info(f"✅ Loaded {len(df):,} training records")
            
            # Create features
            df = self.create_simple_features(df)
            
            # Define feature columns
            feature_columns = [
                'system_1', 'system_2', 'hour_sin', 'hour_cos', 'month_sin', 'month_cos',
                'is_daylight', 'is_peak_solar', 'soc_normalized', 'battery_mode',
                'is_charging', 'is_discharging', 'ghi_norm', 'temp_norm', 'cloud_norm'
            ]
            
            # Prepare training data
            X = df[feature_columns].fillna(0)
            y = df['ac_power']  # Keep as instantaneous power
            
            # Time-based split
            split_idx = int(len(X) * 0.8)
            X_train, X_test = X[:split_idx], X[split_idx:]
            y_train, y_test = y[:split_idx], y[split_idx:]
            
            # Scale features
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)
            
            # Train simple but effective model
            model = lgb.LGBMRegressor(
                n_estimators=150,
                max_depth=10,
                learning_rate=0.1,
                subsample=0.9,
                colsample_bytree=0.9,
                random_state=42,
                verbose=-1
            )
            
            model.fit(X_train_scaled, y_train)
            
            # Evaluate
            y_pred_test = model.predict(X_test_scaled)
            test_r2 = r2_score(y_test, y_pred_test)
            test_rmse = np.sqrt(mean_squared_error(y_test, y_pred_test))
            test_mae = mean_absolute_error(y_test, y_pred_test)
            
            logger.info(f"✅ Corrected model trained:")
            logger.info(f"   R² = {test_r2:.3f}")
            logger.info(f"   RMSE = {test_rmse:.1f}W")
            logger.info(f"   MAE = {test_mae:.1f}W")
            
            # Save model
            model_path = self.models_dir / "corrected_model.joblib"
            joblib.dump(model, model_path)
            
            scaler_path = self.models_dir / "corrected_scaler.joblib"
            joblib.dump(scaler, scaler_path)
            
            features_path = self.models_dir / "corrected_features.json"
            with open(features_path, 'w') as f:
                json.dump(feature_columns, f, indent=2)
            
            logger.info(f"✅ Corrected model saved to {self.models_dir}")
            
            return model, scaler, feature_columns
            
        except Exception as e:
            logger.error(f"❌ Corrected model creation failed: {e}")
            import traceback
            traceback.print_exc()
            return None, None, None
    
    def create_simple_features(self, df):
        """Create simple, reliable features"""
        # Sort data
        df = df.sort_values(['system_id', 'timestamp']).reset_index(drop=True)
        
        # Temporal features
        df['hour'] = df['timestamp'].dt.hour
        df['month'] = df['timestamp'].dt.month
        
        # Cyclical encoding
        df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
        df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
        
        # Time indicators
        df['is_daylight'] = df['hour'].between(6, 20).astype(int)
        df['is_peak_solar'] = df['hour'].between(10, 16).astype(int)
        
        # System features
        df['system_1'] = (df['system_id'] == 1).astype(int)
        df['system_2'] = (df['system_id'] == 2).astype(int)
        
        # Battery features
        df['soc_normalized'] = df['soc'] / 100
        df['battery_mode'] = np.sign(df['bat_power'])
        df['is_charging'] = (df['bat_power'] > 100).astype(int)
        df['is_discharging'] = (df['bat_power'] < -100).astype(int)
        
        # Weather features
        df['ghi_norm'] = df['ghi'] / 1000
        df['temp_norm'] = (df['temperature'] - 20) / 30
        df['cloud_norm'] = df['cloud_cover'] / 100
        
        return df
    
    def validate_corrected_model(self, conn, model, scaler, features):
        """Validate corrected model against real data"""
        logger.info("✅ VALIDATING CORRECTED MODEL")
        logger.info("=" * 60)
        
        validation_results = {}
        
        for date_str, real_yields in self.real_data.items():
            logger.info(f"\n📅 Validating {date_str}...")
            validation_results[date_str] = {}
            
            for system_key, real_yield in real_yields.items():
                system_id = 1 if system_key == 'system1' else 2
                table = 'solax_data' if system_id == 1 else 'solax_data2'
                
                try:
                    # Get data for validation
                    query = f"""
                    WITH system_data AS (
                        SELECT 
                            timestamp, ac_power, soc, bat_power, powerdc1, powerdc2,
                            yield_today, {system_id} as system_id
                        FROM {table}
                        WHERE DATE(timestamp) = %s
                        AND ac_power IS NOT NULL
                        ORDER BY timestamp
                    ),
                    weather_data AS (
                        SELECT
                            DATE_TRUNC('hour', timestamp) as hour_timestamp,
                            AVG(COALESCE(ghi, 400)) as ghi,
                            AVG(COALESCE(temperature, 20)) as temperature,
                            AVG(COALESCE(cloud_cover, 50)) as cloud_cover
                        FROM cams_radiation_data
                        WHERE DATE(timestamp) = %s
                        GROUP BY DATE_TRUNC('hour', timestamp)
                    )
                    SELECT
                        sd.*,
                        COALESCE(w.ghi, 400) as ghi,
                        COALESCE(w.temperature, 20) as temperature,
                        COALESCE(w.cloud_cover, 50) as cloud_cover
                    FROM system_data sd
                    LEFT JOIN weather_data w ON DATE_TRUNC('hour', sd.timestamp) = w.hour_timestamp
                    ORDER BY sd.timestamp
                    """
                    
                    df = pd.read_sql(query, conn, params=[date_str, date_str])
                    
                    if len(df) == 0:
                        continue
                    
                    # Create features
                    df_features = self.create_simple_features(df)
                    
                    # Prepare for prediction
                    X = df_features[features].fillna(0)
                    X_scaled = scaler.transform(X)
                    
                    # Make predictions
                    predictions = model.predict(X_scaled)
                    predictions = np.maximum(predictions, 0)  # Ensure non-negative
                    
                    # Calculate daily energy correctly
                    # Sum positive predictions and convert to kWh (5-minute intervals)
                    predicted_daily_energy = np.sum(predictions) / 12 / 1000
                    
                    # Get actual daily yield
                    actual_daily_yield = df['yield_today'].max()
                    
                    # Calculate accuracy against real data
                    accuracy = (1 - abs(predicted_daily_energy - real_yield) / real_yield) * 100 if real_yield > 0 else 0
                    
                    validation_results[date_str][system_key] = {
                        'predicted_energy_kwh': predicted_daily_energy,
                        'actual_db_yield_kwh': actual_daily_yield,
                        'real_yield_kwh': real_yield,
                        'accuracy_vs_real': accuracy,
                        'max_predicted_power': np.max(predictions),
                        'avg_predicted_power': np.mean(predictions),
                        'records_used': len(df)
                    }
                    
                    logger.info(f"   {system_key.upper()}:")
                    logger.info(f"     Predicted: {predicted_daily_energy:.1f} kWh")
                    logger.info(f"     Real: {real_yield:.1f} kWh")
                    logger.info(f"     Accuracy: {accuracy:.1f}%")
                    logger.info(f"     Max Power: {np.max(predictions):.0f}W")
                    
                except Exception as e:
                    logger.error(f"   {system_key} validation failed: {e}")
        
        return validation_results
    
    def run_corrected_validation(self):
        """Run complete corrected validation"""
        logger.info("🚀 CORRECTED MODEL VALIDATION")
        logger.info("=" * 80)
        logger.info("🎯 Creating and validating corrected model")
        
        try:
            conn = self.connect_database()
            if not conn:
                return False
            
            # Analyze data scaling issue
            sample_data = self.load_and_analyze_data(conn)
            
            # Create corrected model
            model, scaler, features = self.create_corrected_model(conn)
            
            if model is None:
                logger.error("❌ Failed to create corrected model")
                return False
            
            # Validate corrected model
            validation_results = self.validate_corrected_model(conn, model, scaler, features)
            
            conn.close()
            
            # Calculate summary
            total_accuracy = 0
            count = 0
            for date_data in validation_results.values():
                for system_data in date_data.values():
                    if 'accuracy_vs_real' in system_data:
                        total_accuracy += system_data['accuracy_vs_real']
                        count += 1
            
            avg_accuracy = total_accuracy / count if count > 0 else 0
            
            # Save results
            complete_results = {
                'validation_date': datetime.now().isoformat(),
                'model_type': 'Corrected LightGBM',
                'validation_results': validation_results,
                'summary': {
                    'average_accuracy': avg_accuracy,
                    'total_validations': count,
                    'status': 'Excellent' if avg_accuracy > 85 else 'Good' if avg_accuracy > 70 else 'Needs Improvement'
                }
            }
            
            results_file = f"test/results/corrected_validation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            os.makedirs(os.path.dirname(results_file), exist_ok=True)
            
            with open(results_file, 'w') as f:
                json.dump(complete_results, f, indent=2, default=str)
            
            # Display summary
            logger.info("\n" + "=" * 80)
            logger.info("🎉 CORRECTED VALIDATION COMPLETE")
            logger.info(f"📊 Average Accuracy: {avg_accuracy:.1f}%")
            logger.info(f"📊 Status: {complete_results['summary']['status']}")
            logger.info(f"💾 Results saved: {results_file}")
            logger.info("=" * 80)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Corrected validation failed: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """Execute corrected model validation"""
    validator = CorrectedModelValidator()
    success = validator.run_corrected_validation()
    
    if success:
        print("\n🎯 Corrected model validation completed successfully!")
        return True
    else:
        print("\n❌ Corrected model validation failed!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
