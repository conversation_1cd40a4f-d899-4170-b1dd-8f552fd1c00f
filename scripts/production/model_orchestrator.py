#!/usr/bin/env python3
"""
MODEL ORCHESTRATOR - Simple but Powerful
Unified entry point for all solar predictions
Created: June 4, 2025
"""

import os
import sys
import json
import joblib
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

logger = logging.getLogger(__name__)

class ModelOrchestrator:
    """
    Simple Model Orchestrator for Solar Predictions
    
    Features:
    - Auto-selects appropriate models based on time horizon
    - Caches loaded models for performance
    - Unified prediction interface
    - Smart season detection
    """
    
    def __init__(self, models_path: str = None):
        self.models_path = models_path or str(project_root / "models" / "seasonal_models")
        self.loaded_models = {}  # Cache for loaded models
        self.model_metadata = {}  # Cache for model metadata
        self.logger = logger
        
        # Model selection rules
        self.selection_rules = {
            "hourly": {
                "max_hours": 168,  # 1 week max for hourly
                "preferred_models": ["seasonal"],
                "fallback": "daily_interpolation"
            },
            "daily": {
                "max_days": 365,  # 1 year max for daily
                "preferred_models": ["seasonal"],
                "fallback": "monthly_interpolation"
            }
        }
        
        self._initialize_models()
    
    def _initialize_models(self):
        """Initialize and validate available models"""
        try:
            models_dir = Path(self.models_path)
            if not models_dir.exists():
                raise FileNotFoundError(f"Models directory not found: {self.models_path}")
            
            # Scan for available models
            available_models = []
            for model_dir in models_dir.iterdir():
                if model_dir.is_dir():
                    model_file = model_dir / "model.joblib"
                    metadata_file = model_dir / "metadata.json"
                    
                    if model_file.exists() and metadata_file.exists():
                        available_models.append(model_dir.name)
            
            self.logger.info(f"Found {len(available_models)} available models: {available_models}")
            
            # Expected models: 8 seasonal models (4 seasons × 2 systems)
            expected_models = [
                f"{season}_system{system}" 
                for season in ["spring", "summer", "autumn", "winter"]
                for system in [1, 2]
            ]
            
            missing_models = set(expected_models) - set(available_models)
            if missing_models:
                self.logger.warning(f"Missing models: {missing_models}")
            
            self.available_models = available_models
            
        except Exception as e:
            self.logger.error(f"Failed to initialize models: {e}")
            self.available_models = []
    
    def _get_season(self, timestamp: datetime) -> str:
        """Determine season from timestamp"""
        month = timestamp.month
        
        if month in [12, 1, 2]:
            return "winter"
        elif month in [3, 4, 5]:
            return "spring"
        elif month in [6, 7, 8]:
            return "summer"
        else:  # 9, 10, 11
            return "autumn"
    
    def _load_model(self, model_name: str) -> Dict[str, Any]:
        """Load model and its components (cached)"""
        if model_name in self.loaded_models:
            return self.loaded_models[model_name]
        
        try:
            model_dir = Path(self.models_path) / model_name
            
            # Load model components
            model = joblib.load(model_dir / "model.joblib")
            scaler = joblib.load(model_dir / "scaler.joblib")
            
            with open(model_dir / "metadata.json", 'r') as f:
                metadata = json.load(f)
            
            model_components = {
                "model": model,
                "scaler": scaler,
                "metadata": metadata,
                "loaded_at": datetime.now()
            }
            
            # Cache the loaded model
            self.loaded_models[model_name] = model_components
            
            self.logger.info(f"Loaded model: {model_name}")
            return model_components
            
        except Exception as e:
            self.logger.error(f"Failed to load model {model_name}: {e}")
            raise
    
    def _select_best_model(self, system_id: int, timestamp: datetime, 
                          prediction_type: str = "daily") -> str:
        """Select the best model for given parameters"""
        
        # Determine season
        season = self._get_season(timestamp)
        
        # Construct model name
        model_name = f"{season}_system{system_id}"
        
        # Check if model exists
        if model_name in self.available_models:
            return model_name
        
        # Fallback logic
        self.logger.warning(f"Model {model_name} not available, trying fallbacks...")
        
        # Try other system
        fallback_system = 2 if system_id == 1 else 1
        fallback_model = f"{season}_system{fallback_system}"
        
        if fallback_model in self.available_models:
            self.logger.info(f"Using fallback model: {fallback_model}")
            return fallback_model
        
        # Try same system, different season
        for fallback_season in ["summer", "spring", "autumn", "winter"]:
            if fallback_season != season:
                fallback_model = f"{fallback_season}_system{system_id}"
                if fallback_model in self.available_models:
                    self.logger.info(f"Using seasonal fallback: {fallback_model}")
                    return fallback_model
        
        # Last resort - any available model
        if self.available_models:
            fallback_model = self.available_models[0]
            self.logger.warning(f"Using last resort model: {fallback_model}")
            return fallback_model
        
        raise Exception("No models available for prediction")
    
    def predict_single(self, system_id: int, timestamp: datetime, 
                      weather_data: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Make a single prediction
        
        Args:
            system_id: Solar system ID (1 or 2)
            timestamp: Prediction timestamp
            weather_data: Optional weather data dict
            
        Returns:
            Prediction result dict
        """
        try:
            # Select best model
            model_name = self._select_best_model(system_id, timestamp)
            
            # Load model components
            model_components = self._load_model(model_name)
            model = model_components["model"]
            scaler = model_components["scaler"]
            metadata = model_components["metadata"]
            
            # Prepare features based on model requirements
            features = self._prepare_features(timestamp, weather_data, system_id, metadata)
            
            # Scale features
            scaled_features = scaler.transform([features])
            
            # Make prediction
            prediction_value = model.predict(scaled_features)[0]
            prediction_value = max(0, prediction_value)  # Ensure non-negative
            
            # Calculate confidence (simplified)
            confidence = metadata.get("accuracy", 0.9)
            
            # Determine season for response
            season = self._get_season(timestamp)
            
            return {
                "system_id": system_id,
                "timestamp": timestamp.isoformat(),
                "season": season,
                "prediction": round(prediction_value, 1),
                "confidence": round(confidence, 3),
                "model_used": model_name,
                "model_algorithm": metadata.get("algorithm", "RandomForestRegressor"),
                "status": "success"
            }
            
        except Exception as e:
            self.logger.error(f"Prediction failed: {e}")
            return {
                "system_id": system_id,
                "timestamp": timestamp.isoformat(),
                "error": str(e),
                "status": "failed"
            }
    
    def _prepare_features(self, timestamp: datetime, weather_data: Optional[Dict],
                         system_id: int, model_metadata: Dict) -> List[float]:
        """Prepare features for prediction based on model requirements"""

        # Get expected features from model metadata
        expected_features = model_metadata.get("features", [])

        # Basic temporal features
        hour = timestamp.hour

        # Convert hour to sin/cos for cyclical encoding
        import math
        hour_sin = math.sin(2 * math.pi * hour / 24)
        hour_cos = math.cos(2 * math.pi * hour / 24)

        # Weather features (with defaults)
        temperature = weather_data.get("temperature", 20.0) if weather_data else 20.0
        cloud_cover = weather_data.get("cloud_cover", 30.0) if weather_data else 30.0
        ghi = weather_data.get("ghi", 500.0) if weather_data else 500.0

        # System features (with defaults)
        soc = weather_data.get("soc", 75.0) if weather_data else 75.0

        # Build feature vector based on expected features
        feature_map = {
            "hour_sin": hour_sin,
            "hour_cos": hour_cos,
            "hour": hour,
            "temperature": temperature,
            "cloud_cover": cloud_cover,
            "ghi": ghi,
            "soc": soc,
            "system_id": system_id
        }

        # Create feature vector in the correct order
        features = []
        for feature_name in expected_features:
            if feature_name in feature_map:
                features.append(feature_map[feature_name])
            else:
                # Default value for unknown features
                features.append(0.0)
                self.logger.warning(f"Unknown feature '{feature_name}', using default value 0.0")

        self.logger.debug(f"Prepared {len(features)} features: {expected_features}")
        return features
    
    def predict_range(self, system_id: int, start_time: datetime,
                     end_time: datetime, granularity: str = "auto") -> List[Dict[str, Any]]:
        """
        Make predictions for a time range

        Args:
            system_id: Solar system ID (1 or 2)
            start_time: Start of prediction range
            end_time: End of prediction range
            granularity: 'hourly', 'daily', or 'auto'

        Returns:
            List of prediction results
        """
        try:
            duration = end_time - start_time

            # Auto-determine granularity
            if granularity == "auto":
                if duration <= timedelta(days=3):
                    granularity = "hourly"
                else:
                    granularity = "daily"

            predictions = []
            current_time = start_time

            if granularity == "hourly":
                # For hourly predictions, we need to simulate hourly yield
                # Current models are DAILY models, so we need to distribute daily yield across hours
                delta = timedelta(hours=1)

                # Get daily prediction first
                daily_prediction = self.predict_single(system_id, current_time)
                if daily_prediction.get("status") == "success":
                    daily_yield = daily_prediction["prediction"]

                    # Distribute daily yield across hours using solar curve
                    while current_time <= end_time:
                        hourly_yield = self._distribute_daily_to_hourly(daily_yield, current_time)

                        hourly_prediction = {
                            "system_id": system_id,
                            "timestamp": current_time.isoformat(),
                            "season": self._get_season(current_time),
                            "prediction": round(hourly_yield, 1),
                            "confidence": daily_prediction["confidence"] * 0.8,  # Lower confidence for hourly
                            "model_used": daily_prediction["model_used"] + "_hourly_distributed",
                            "model_algorithm": daily_prediction["model_algorithm"],
                            "status": "success",
                            "note": "Distributed from daily prediction"
                        }

                        predictions.append(hourly_prediction)
                        current_time += delta

                        # Update daily prediction for next day
                        if current_time.hour == 0:
                            daily_prediction = self.predict_single(system_id, current_time)
                            if daily_prediction.get("status") == "success":
                                daily_yield = daily_prediction["prediction"]
                else:
                    # Fallback if daily prediction fails
                    while current_time <= end_time:
                        predictions.append({
                            "system_id": system_id,
                            "timestamp": current_time.isoformat(),
                            "error": "Daily prediction failed",
                            "status": "failed"
                        })
                        current_time += delta

            else:  # daily
                delta = timedelta(days=1)
                current_time = current_time.replace(hour=12, minute=0, second=0, microsecond=0)

                while current_time <= end_time:
                    prediction = self.predict_single(system_id, current_time)
                    predictions.append(prediction)
                    current_time += delta

            return predictions

        except Exception as e:
            self.logger.error(f"Range prediction failed: {e}")
            return [{
                "error": str(e),
                "status": "failed"
            }]

    def _distribute_daily_to_hourly(self, daily_yield: float, timestamp: datetime) -> float:
        """
        Distribute daily yield to hourly values using solar curve

        Args:
            daily_yield: Total daily yield in kWh
            timestamp: Hour timestamp

        Returns:
            Hourly yield in kWh
        """
        hour = timestamp.hour

        # Solar production curve (properly normalized to sum to 1.0)
        # Based on typical solar production pattern
        solar_curve_raw = {
            0: 0.0,   1: 0.0,   2: 0.0,   3: 0.0,   4: 0.0,   5: 0.0,
            6: 0.01,  7: 0.05,  8: 0.12,  9: 0.18, 10: 0.22, 11: 0.25,
            12: 0.26, 13: 0.25, 14: 0.22, 15: 0.18, 16: 0.12, 17: 0.05,
            18: 0.01, 19: 0.0,  20: 0.0,  21: 0.0,  22: 0.0,  23: 0.0
        }

        # Normalize to ensure sum = 1.0
        total = sum(solar_curve_raw.values())
        solar_curve = {hour: value / total for hour, value in solar_curve_raw.items()}

        # Get hourly fraction
        hourly_fraction = solar_curve.get(hour, 0.0)

        # Calculate hourly yield
        hourly_yield = daily_yield * hourly_fraction

        return hourly_yield
    
    def get_model_status(self) -> Dict[str, Any]:
        """Get status of all models"""
        return {
            "total_models": len(self.available_models),
            "loaded_models": len(self.loaded_models),
            "available_models": self.available_models,
            "models_path": self.models_path,
            "status": "operational" if self.available_models else "no_models"
        }

def main():
    """Test the orchestrator"""
    logging.basicConfig(level=logging.INFO)
    
    orchestrator = ModelOrchestrator()
    
    # Test single prediction
    result = orchestrator.predict_single(
        system_id=1,
        timestamp=datetime.now(),
        weather_data={"temperature": 25, "cloud_cover": 30}
    )
    
    print("Single Prediction Result:")
    print(json.dumps(result, indent=2))
    
    # Test model status
    status = orchestrator.get_model_status()
    print("\nModel Status:")
    print(json.dumps(status, indent=2))

if __name__ == "__main__":
    main()
