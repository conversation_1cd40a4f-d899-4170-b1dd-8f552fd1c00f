#!/usr/bin/env python3
"""
Comprehensive Hourly Model Evaluation
Test all models with synthetic realistic data based on known patterns
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import numpy as np
import pandas as pd
import joblib
import json
import lightgbm as lgb
import xgboost as xgb
from pathlib import Path
from datetime import datetime, timedelta
import logging
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import warnings
warnings.filterwarnings('ignore')

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ComprehensiveHourlyModelEvaluator:
    """Comprehensive evaluation of all models for hourly predictions"""
    
    def __init__(self):
        self.project_root = Path("/home/<USER>/solar-prediction-project")
        self.models_dir = self.project_root / "models"
        
        # Known real data for validation
        self.known_daily_yields = {
            '2025-06-01': {'system1': 72.8, 'system2': 67.7},  # Real data
            '2025-06-02': {'system1': 31.8, 'system2': 34.0},  # Partial day
            '2024-06-03': {'system1': 68.3, 'system2': 68.3},  # Historical
            '2024-06-04': {'system1': 65.4, 'system2': 65.4}   # Historical
        }
        
        # Realistic hourly patterns based on solar physics
        self.hourly_patterns = self.generate_realistic_hourly_patterns()
        
        # Testing results
        self.evaluation_results = {}
        
    def generate_realistic_hourly_patterns(self):
        """Generate realistic hourly solar production patterns"""
        patterns = {}
        
        # June 1, 2025 - Excellent conditions (72.8 kWh total)
        patterns['2025-06-01'] = {
            'weather': {'temperature': 25, 'cloud_cover': 10, 'ghi': 850},
            'hourly_distribution': {
                6: 0.02, 7: 0.05, 8: 0.08, 9: 0.12, 10: 0.15, 11: 0.18,
                12: 0.20, 13: 0.18, 14: 0.15, 15: 0.12, 16: 0.08, 17: 0.05, 18: 0.02
            }
        }
        
        # June 2, 2025 - Partial day/cloudy (31.8 kWh)
        patterns['2025-06-02'] = {
            'weather': {'temperature': 22, 'cloud_cover': 60, 'ghi': 400},
            'hourly_distribution': {
                6: 0.01, 7: 0.03, 8: 0.06, 9: 0.10, 10: 0.13, 11: 0.16,
                12: 0.18, 13: 0.16, 14: 0.13, 15: 0.10, 16: 0.06, 17: 0.03, 18: 0.01
            }
        }
        
        # June 3, 2024 - Historical excellent (68.3 kWh)
        patterns['2024-06-03'] = {
            'weather': {'temperature': 26, 'cloud_cover': 5, 'ghi': 900},
            'hourly_distribution': {
                6: 0.02, 7: 0.06, 8: 0.09, 9: 0.13, 10: 0.16, 11: 0.19,
                12: 0.20, 13: 0.19, 14: 0.16, 15: 0.13, 16: 0.09, 17: 0.06, 18: 0.02
            }
        }
        
        return patterns
    
    def discover_all_models(self):
        """Discover all available models"""
        logger.info("🔍 DISCOVERING ALL AVAILABLE MODELS")
        logger.info("=" * 70)
        
        model_configs = {
            'production_optimized': {
                'path': 'production_optimized/daily_yield_model.joblib',
                'scaler': 'production_optimized/daily_yield_scaler.joblib',
                'features': 'production_optimized/daily_yield_features.json',
                'type': 'lightgbm_daily'
            },
            'final_solution': {
                'path': 'final_solution/daily_yield_model.joblib',
                'scaler': 'final_solution/daily_yield_scaler.joblib',
                'features': 'final_solution/daily_yield_features.json',
                'type': 'lightgbm_daily'
            },
            'enhanced_v3_system1': {
                'path': 'enhanced_v3_separate_systems/system_1_model.joblib',
                'features': 'enhanced_v3_separate_systems/system_1_features.json',
                'type': 'ensemble'
            },
            'enhanced_v3_system2': {
                'path': 'enhanced_v3_separate_systems/system_2_model.joblib',
                'features': 'enhanced_v3_separate_systems/system_2_features.json',
                'type': 'ensemble'
            },
            'enhanced_v2_lightgbm': {
                'path': 'enhanced_v2_all/lightgbm_model.txt',
                'features': 'enhanced_v2_all/feature_columns.json',
                'type': 'lightgbm'
            }
        }
        
        discovered = {}
        for name, config in model_configs.items():
            model_path = self.models_dir / config['path']
            if model_path.exists():
                discovered[name] = {
                    'model_path': model_path,
                    'scaler_path': self.models_dir / config.get('scaler', '') if config.get('scaler') else None,
                    'features_path': self.models_dir / config.get('features', '') if config.get('features') else None,
                    'type': config['type']
                }
                logger.info(f"   ✅ Found: {name} ({config['type']})")
            else:
                logger.warning(f"   ❌ Missing: {name}")
        
        logger.info(f"\n📊 Total models discovered: {len(discovered)}")
        return discovered
    
    def load_model_safely(self, model_info):
        """Safely load a model"""
        try:
            model_path = model_info['model_path']
            model_type = model_info['type']
            
            # Load main model
            if model_type == 'lightgbm' and model_path.suffix == '.txt':
                model = lgb.Booster(model_file=str(model_path))
            else:
                model = joblib.load(model_path)
            
            # Load scaler
            scaler = None
            if model_info['scaler_path'] and model_info['scaler_path'].exists():
                scaler = joblib.load(model_info['scaler_path'])
            
            # Load features
            features = None
            if model_info['features_path'] and model_info['features_path'].exists():
                with open(model_info['features_path'], 'r') as f:
                    features = json.load(f)
            
            return model, scaler, features
            
        except Exception as e:
            logger.error(f"❌ Failed to load model: {e}")
            return None, None, None
    
    def create_synthetic_features(self, timestamp, weather, system_id, feature_list=None):
        """Create synthetic features for testing"""
        hour = timestamp.hour
        month = timestamp.month
        day_of_year = timestamp.timetuple().tm_yday
        
        # Cyclical encoding
        hour_sin = np.sin(2 * np.pi * hour / 24)
        hour_cos = np.cos(2 * np.pi * hour / 24)
        month_sin = np.sin(2 * np.pi * month / 12)
        month_cos = np.cos(2 * np.pi * month / 12)
        day_sin = np.sin(2 * np.pi * day_of_year / 365)
        day_cos = np.cos(2 * np.pi * day_of_year / 365)
        
        # System features
        system_1 = 1 if system_id == 1 else 0
        system_2 = 1 if system_id == 2 else 0
        
        # Weather features
        temp_norm = (weather['temperature'] - 20) / 30
        cloud_norm = weather['cloud_cover'] / 100
        ghi_norm = weather['ghi'] / 1000
        
        # Battery features (synthetic)
        soc_norm = 0.75  # Typical 75% SOC
        
        # Solar position
        if 6 <= hour <= 18:
            solar_elevation = np.sin(np.pi * (hour - 6) / 12)
        else:
            solar_elevation = 0
        
        # Efficiency factors
        temp_efficiency = 1 - (weather['temperature'] - 25) * 0.004
        temp_efficiency = max(0.7, min(1.1, temp_efficiency))
        
        # Seasonal indicators
        is_summer = 1 if month in [6, 7, 8] else 0
        is_winter = 1 if month in [12, 1, 2] else 0
        
        # Create comprehensive feature set
        all_features = {
            'system_1': system_1, 'system_2': system_2,
            'month_sin': month_sin, 'month_cos': month_cos,
            'day_sin': day_sin, 'day_cos': day_cos,
            'is_summer': is_summer, 'is_winter': is_winter,
            'soc_normalized': soc_norm, 'bat_power_normalized': 0.0,
            'avg_charge_norm': 0.08,
            'ghi_norm': ghi_norm, 'max_ghi_norm': ghi_norm * 1.2,
            'temp_norm': temp_norm, 'cloud_norm': cloud_norm,
            'temp_efficiency': temp_efficiency
        }
        
        # Extract features in correct order
        if feature_list:
            feature_vector = []
            for feature_name in feature_list:
                if feature_name in all_features:
                    feature_vector.append(all_features[feature_name])
                else:
                    feature_vector.append(0.0)
            return np.array(feature_vector)
        
        return np.array(list(all_features.values()))
    
    def evaluate_model_on_synthetic_data(self, model_name, model, scaler, features, model_type):
        """Evaluate model on synthetic realistic data"""
        try:
            logger.info(f"   📊 Evaluating {model_name} on synthetic data...")
            
            all_predictions = []
            all_actuals = []
            date_results = {}
            
            for date_str, known_yield in self.known_daily_yields.items():
                if date_str not in self.hourly_patterns:
                    continue
                
                pattern = self.hourly_patterns[date_str]
                weather = pattern['weather']
                hourly_dist = pattern['hourly_distribution']
                
                date_predictions = []
                date_actuals = []
                
                # Test both systems
                for system_id in [1, 2]:
                    daily_yield = known_yield[f'system{system_id}']
                    
                    for hour, fraction in hourly_dist.items():
                        timestamp = datetime.strptime(date_str, '%Y-%m-%d').replace(hour=hour)
                        
                        # Calculate actual hourly power
                        actual_hourly_kwh = daily_yield * fraction
                        actual_hourly_power = actual_hourly_kwh * 1000  # Convert to watts
                        
                        # Create features
                        if features:
                            feature_vector = self.create_synthetic_features(
                                timestamp, weather, system_id, features
                            )
                        else:
                            feature_vector = self.create_synthetic_features(
                                timestamp, weather, system_id
                            )
                        
                        # Make prediction
                        try:
                            if model_type == 'lightgbm_daily':
                                # Daily model - convert to hourly
                                if scaler:
                                    feature_vector_scaled = scaler.transform([feature_vector])
                                    daily_prediction = model.predict(feature_vector_scaled)[0]
                                else:
                                    daily_prediction = model.predict([feature_vector])[0]
                                
                                # Convert daily to hourly using solar curve
                                solar_factor = np.sin(np.pi * (hour - 6) / 12) if 6 <= hour <= 18 else 0
                                hourly_prediction = (daily_prediction * 1000 * solar_factor) / 8
                                
                            elif model_type == 'lightgbm':
                                if hasattr(model, 'predict'):
                                    hourly_prediction = model.predict([feature_vector])[0]
                                else:
                                    hourly_prediction = model.predict(np.array([feature_vector]))[0]
                                
                                # Denormalize if needed
                                if hourly_prediction < 1:
                                    hourly_prediction *= 12469
                                
                            else:  # ensemble
                                if scaler:
                                    feature_vector_scaled = scaler.transform([feature_vector])
                                    hourly_prediction = model.predict(feature_vector_scaled)[0]
                                else:
                                    hourly_prediction = model.predict([feature_vector])[0]
                                
                                # Denormalize if needed
                                if hourly_prediction < 1:
                                    hourly_prediction *= 12469
                            
                            hourly_prediction = max(0, hourly_prediction)
                            
                            # Store results
                            date_predictions.append(hourly_prediction)
                            date_actuals.append(actual_hourly_power)
                            all_predictions.append(hourly_prediction)
                            all_actuals.append(actual_hourly_power)
                            
                        except Exception as e:
                            logger.warning(f"   ⚠️ Prediction failed for {timestamp}: {e}")
                            continue
                
                # Calculate date accuracy
                if date_predictions and date_actuals:
                    date_accuracy = self.calculate_accuracy(date_predictions, date_actuals)
                    date_results[date_str] = {
                        'accuracy': date_accuracy,
                        'predictions': len(date_predictions),
                        'predicted_total': sum(date_predictions) / 1000,  # kWh
                        'actual_total': sum(date_actuals) / 1000,  # kWh
                        'known_yield': sum(known_yield.values()) / 2  # Average of both systems
                    }
                    logger.info(f"     {date_str}: {date_accuracy:.1f}% accuracy | Pred: {date_results[date_str]['predicted_total']:.1f} kWh | Actual: {date_results[date_str]['actual_total']:.1f} kWh")
            
            # Calculate overall accuracy
            if all_predictions and all_actuals:
                overall_accuracy = self.calculate_accuracy(all_predictions, all_actuals)
                
                return {
                    'model_name': model_name,
                    'model_type': model_type,
                    'average_accuracy': overall_accuracy,
                    'total_predictions': len(all_predictions),
                    'date_results': date_results,
                    'rmse': np.sqrt(mean_squared_error(all_actuals, all_predictions)),
                    'mae': mean_absolute_error(all_actuals, all_predictions),
                    'r2': r2_score(all_actuals, all_predictions) if len(set(all_actuals)) > 1 else 0
                }
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Evaluation of {model_name} failed: {e}")
            return None
    
    def calculate_accuracy(self, predictions, actuals):
        """Calculate prediction accuracy using MAPE"""
        try:
            if not predictions or not actuals:
                return 0.0
            
            mape_errors = []
            for pred, actual in zip(predictions, actuals):
                if actual > 0:
                    mape_errors.append(abs(pred - actual) / actual)
                elif pred == 0 and actual == 0:
                    mape_errors.append(0)
                else:
                    mape_errors.append(1)
            
            mape = np.mean(mape_errors)
            accuracy = max(0, (1 - mape) * 100)
            return accuracy
            
        except Exception as e:
            logger.error(f"❌ Accuracy calculation failed: {e}")
            return 0.0
    
    def run_comprehensive_evaluation(self):
        """Run comprehensive hourly model evaluation"""
        logger.info("🚀 COMPREHENSIVE HOURLY MODEL EVALUATION")
        logger.info("=" * 80)
        logger.info("🎯 Target: 95%+ accuracy using synthetic realistic data")
        
        try:
            # Discover models
            discovered_models = self.discover_all_models()
            
            if not discovered_models:
                logger.error("❌ No models discovered")
                return False
            
            # Evaluate each model
            logger.info(f"\n🔬 EVALUATING {len(discovered_models)} MODELS")
            logger.info("=" * 80)
            
            all_results = {}
            
            for model_name, model_info in discovered_models.items():
                logger.info(f"\n🧪 Evaluating {model_name}...")
                
                # Load model
                model, scaler, features = self.load_model_safely(model_info)
                
                if model is None:
                    logger.warning(f"⚠️ Could not load {model_name}")
                    continue
                
                # Evaluate model
                results = self.evaluate_model_on_synthetic_data(
                    model_name, model, scaler, features, model_info['type']
                )
                
                if results:
                    all_results[model_name] = results
                    accuracy = results['average_accuracy']
                    logger.info(f"   ✅ {model_name}: {accuracy:.1f}% accuracy")
                    
                    if accuracy >= 95.0:
                        logger.info(f"   🎯 TARGET ACHIEVED: {model_name}!")
            
            # Store results
            self.evaluation_results = all_results
            
            # Save results
            results_file = f"test/results/comprehensive_hourly_evaluation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            os.makedirs(os.path.dirname(results_file), exist_ok=True)
            
            with open(results_file, 'w') as f:
                json.dump(all_results, f, indent=2, default=str)
            
            # Display summary
            self.display_evaluation_summary(all_results)
            
            logger.info(f"\n💾 Results saved: {results_file}")
            logger.info("=" * 80)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Comprehensive evaluation failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def display_evaluation_summary(self, all_results):
        """Display comprehensive evaluation summary"""
        logger.info("\n" + "=" * 80)
        logger.info("🎉 COMPREHENSIVE HOURLY MODEL EVALUATION RESULTS")
        logger.info("=" * 80)
        
        if not all_results:
            logger.warning("❌ No results to display")
            return
        
        # Sort by accuracy
        sorted_results = sorted(all_results.items(), key=lambda x: x[1]['average_accuracy'], reverse=True)
        
        logger.info("📊 MODEL RANKING BY ACCURACY:")
        logger.info("-" * 80)
        
        for i, (model_name, results) in enumerate(sorted_results, 1):
            accuracy = results['average_accuracy']
            rmse = results['rmse']
            r2 = results['r2']
            
            status = "🎯 TARGET ACHIEVED" if accuracy >= 95.0 else "⚠️ Below Target" if accuracy >= 85.0 else "❌ Poor"
            
            logger.info(f"{i:2d}. {model_name}")
            logger.info(f"    Accuracy: {accuracy:.1f}% | RMSE: {rmse:.1f}W | R²: {r2:.3f}")
            logger.info(f"    Status: {status}")
            logger.info("")
        
        # Best model details
        if sorted_results:
            best_name, best_results = sorted_results[0]
            logger.info("🏆 BEST MODEL ANALYSIS:")
            logger.info(f"   Model: {best_name}")
            logger.info(f"   Accuracy: {best_results['average_accuracy']:.1f}%")
            logger.info(f"   Type: {best_results['model_type']}")
            
            logger.info("   Date Performance:")
            for date, result in best_results['date_results'].items():
                logger.info(f"     {date}: {result['accuracy']:.1f}% | Pred: {result['predicted_total']:.1f} kWh | Known: {result['known_yield']:.1f} kWh")
        
        # Target achievement
        target_models = [name for name, results in all_results.items() if results['average_accuracy'] >= 95.0]
        
        if target_models:
            logger.info(f"\n🎯 MODELS ACHIEVING 95%+ ACCURACY: {len(target_models)}")
            for model_name in target_models:
                accuracy = all_results[model_name]['average_accuracy']
                logger.info(f"   ✅ {model_name}: {accuracy:.1f}%")
        else:
            logger.info("\n⚠️ NO MODELS ACHIEVED 95%+ ACCURACY TARGET")
            logger.info("   Next Steps:")
            logger.info("   1. Develop dedicated hourly prediction model")
            logger.info("   2. Improve feature engineering for hourly patterns")
            logger.info("   3. Use ensemble approach with best models")

def main():
    """Execute comprehensive hourly model evaluation"""
    evaluator = ComprehensiveHourlyModelEvaluator()
    success = evaluator.run_comprehensive_evaluation()
    
    if success:
        print("\n🎯 Comprehensive hourly model evaluation completed!")
        return True
    else:
        print("\n❌ Evaluation failed!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
