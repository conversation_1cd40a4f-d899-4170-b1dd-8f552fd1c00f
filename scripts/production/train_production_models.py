#!/usr/bin/env python3
"""
Production Model Training Execution
Trains production-ready models with real data
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import joblib
import json
import psycopg2

# ML libraries
from sklearn.ensemble import GradientBoostingRegressor, RandomForestRegressor, VotingRegressor
from sklearn.model_selection import TimeSeriesSplit, cross_val_score
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.preprocessing import StandardScaler

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ProductionModelTrainer:
    """Production model trainer with real data"""
    
    def __init__(self):
        self.db_config = {
            'host': 'localhost',
            'database': 'solar_prediction',
            'user': 'postgres',
            'password': 'postgres'
        }
        self.model_dir = 'models/production'
        os.makedirs(self.model_dir, exist_ok=True)
    
    def load_training_data(self, days_back: int = 90) -> tuple:
        """Load real training data from database"""
        
        logger.info(f"Loading training data for last {days_back} days")
        
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days_back)
        
        conn = psycopg2.connect(**self.db_config)
        
        # Load features from multiple sources
        features_query = """
            SELECT 
                DATE_TRUNC('hour', w.timestamp) as timestamp,
                w.temperature_2m as temperature,
                w.relative_humidity_2m as humidity,
                w.cloud_cover,
                w.shortwave_radiation as ghi,
                EXTRACT(HOUR FROM w.timestamp) as hour,
                EXTRACT(DOY FROM w.timestamp) as day_of_year,
                EXTRACT(MONTH FROM w.timestamp) as month
            FROM weather_data w
            WHERE w.timestamp BETWEEN %s AND %s
            AND w.temperature_2m IS NOT NULL
            ORDER BY timestamp
        """
        
        features_df = pd.read_sql(features_query, conn, params=[start_time, end_time])
        
        # Load targets (yield data) - separate queries for each system
        targets_query1 = """
            SELECT
                DATE_TRUNC('hour', timestamp) as timestamp,
                MAX(yield_today) as yield_value,
                1 as system_id
            FROM solax_data
            WHERE timestamp BETWEEN %s AND %s
            GROUP BY DATE_TRUNC('hour', timestamp)
        """

        targets_query2 = """
            SELECT
                DATE_TRUNC('hour', timestamp) as timestamp,
                MAX(yield_today) as yield_value,
                2 as system_id
            FROM solax_data2
            WHERE timestamp BETWEEN %s AND %s
            GROUP BY DATE_TRUNC('hour', timestamp)
        """

        try:
            targets_df1 = pd.read_sql(targets_query1, conn, params=[start_time, end_time])
            targets_df1['system'] = 'system1'
        except:
            targets_df1 = pd.DataFrame(columns=['timestamp', 'yield_value', 'system_id', 'system'])

        try:
            targets_df2 = pd.read_sql(targets_query2, conn, params=[start_time, end_time])
            targets_df2['system'] = 'system2'
        except:
            targets_df2 = pd.DataFrame(columns=['timestamp', 'yield_value', 'system_id', 'system'])

        conn.close()

        # Combine both systems
        targets_df = pd.concat([targets_df1, targets_df2], ignore_index=True)

        # Aggregate targets (sum both systems)
        if len(targets_df) > 0:
            targets_agg = targets_df.groupby('timestamp')['yield_value'].sum().reset_index()
            targets_agg.rename(columns={'yield_value': 'total_yield'}, inplace=True)
        else:
            targets_agg = pd.DataFrame(columns=['timestamp', 'total_yield'])
        
        # Merge features with targets
        merged_df = pd.merge(features_df, targets_agg[['timestamp', 'total_yield']], on='timestamp', how='inner')
        
        # Feature engineering
        merged_df = self._engineer_features(merged_df)
        
        # Prepare X and y
        feature_columns = [col for col in merged_df.columns if col not in ['timestamp', 'total_yield']]
        X = merged_df[feature_columns].fillna(0)
        y = merged_df['total_yield'].fillna(0)
        
        # Remove rows with zero yield (nighttime)
        valid_mask = (y > 0) & (merged_df['hour'].between(6, 18))
        X = X[valid_mask]
        y = y[valid_mask]
        
        logger.info(f"Loaded {len(X)} training samples with {len(X.columns)} features")
        
        return X, y
    
    def _engineer_features(self, df):
        """Engineer additional features"""
        
        df = df.copy()
        
        # Cyclical encoding for time features
        df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
        df['day_sin'] = np.sin(2 * np.pi * df['day_of_year'] / 365)
        df['day_cos'] = np.cos(2 * np.pi * df['day_of_year'] / 365)
        
        # Solar elevation (simplified)
        df['solar_elevation'] = self._calculate_solar_elevation(df)
        
        # Weather interactions
        if 'ghi' in df.columns and 'cloud_cover' in df.columns:
            df['clear_sky_index'] = df['ghi'] / (df['ghi'].max() * (1 - df['cloud_cover'] / 100))
            df['clear_sky_index'] = df['clear_sky_index'].clip(0, 2)
        
        # Temperature efficiency factor
        if 'temperature' in df.columns:
            df['temp_efficiency'] = 1.0 - (np.abs(df['temperature'] - 25) * 0.01)
            df['temp_efficiency'] = df['temp_efficiency'].clip(0.5, 1.2)
        
        return df
    
    def _calculate_solar_elevation(self, df):
        """Calculate simplified solar elevation"""
        
        hour = df['hour']
        day_of_year = df['day_of_year']
        
        # Solar declination
        declination = 23.45 * np.sin(np.radians(360 * (284 + day_of_year) / 365))
        
        # Hour angle
        hour_angle = 15 * (hour - 12)
        
        # Solar elevation for latitude ~38°
        latitude = 38.14
        elevation = np.arcsin(
            np.sin(np.radians(declination)) * np.sin(np.radians(latitude)) +
            np.cos(np.radians(declination)) * np.cos(np.radians(latitude)) * np.cos(np.radians(hour_angle))
        )
        
        return np.degrees(elevation).clip(0, 90)
    
    def train_production_models(self, X, y):
        """Train production models"""
        
        logger.info("Training production models...")
        
        # Scale features
        scaler = StandardScaler()
        X_scaled = pd.DataFrame(
            scaler.fit_transform(X),
            columns=X.columns,
            index=X.index
        )
        
        # Define models
        models = {
            'gradient_boosting': GradientBoostingRegressor(
                n_estimators=200,
                max_depth=8,
                learning_rate=0.1,
                subsample=0.8,
                random_state=42
            ),
            'random_forest': RandomForestRegressor(
                n_estimators=200,
                max_depth=12,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42,
                n_jobs=-1
            )
        }
        
        # Create ensemble
        models['ensemble'] = VotingRegressor([
            ('gb', models['gradient_boosting']),
            ('rf', models['random_forest'])
        ])
        
        # Train and evaluate models
        results = {}
        tscv = TimeSeriesSplit(n_splits=5)
        
        for name, model in models.items():
            logger.info(f"Training {name}...")
            
            try:
                # Cross-validation
                cv_scores = cross_val_score(
                    model, X_scaled, y,
                    cv=tscv,
                    scoring='neg_mean_absolute_error',
                    n_jobs=-1
                )
                
                # Train on full dataset
                model.fit(X_scaled, y)
                
                # Predictions
                y_pred = model.predict(X_scaled)
                
                # Metrics
                mae = mean_absolute_error(y, y_pred)
                rmse = np.sqrt(mean_squared_error(y, y_pred))
                r2 = r2_score(y, y_pred)
                
                # MAPE
                mape = np.mean(np.abs((y - y_pred) / (y + 1e-8))) * 100
                
                results[name] = {
                    'model': model,
                    'scaler': scaler if name == 'ensemble' else None,  # Save scaler with best model
                    'cv_scores': cv_scores,
                    'cv_mean': -cv_scores.mean(),
                    'cv_std': cv_scores.std(),
                    'mae': mae,
                    'rmse': rmse,
                    'r2': r2,
                    'mape': mape,
                    'feature_importance': self._get_feature_importance(model, X.columns)
                }
                
                logger.info(f"{name} - CV MAE: {-cv_scores.mean():.3f} ± {cv_scores.std():.3f}, R²: {r2:.3f}")
                
            except Exception as e:
                logger.error(f"Training failed for {name}: {e}")
                results[name] = {'error': str(e)}
        
        return results
    
    def _get_feature_importance(self, model, feature_names):
        """Extract feature importance"""
        
        try:
            if hasattr(model, 'feature_importances_'):
                importance = model.feature_importances_
                return dict(zip(feature_names, importance))
            else:
                return {}
        except:
            return {}
    
    def save_production_models(self, results):
        """Save production models"""
        
        # Save models
        for name, result in results.items():
            if 'model' in result:
                model_path = os.path.join(self.model_dir, f'{name}_model.joblib')
                joblib.dump(result['model'], model_path)
                logger.info(f"Saved {name} model")
                
                # Save scaler if available
                if result.get('scaler'):
                    scaler_path = os.path.join(self.model_dir, f'{name}_scaler.joblib')
                    joblib.dump(result['scaler'], scaler_path)
        
        # Save training metadata
        metadata = {
            'training_date': datetime.now().isoformat(),
            'models': {
                name: {
                    'cv_mean': result.get('cv_mean'),
                    'mae': result.get('mae'),
                    'rmse': result.get('rmse'),
                    'r2': result.get('r2'),
                    'mape': result.get('mape'),
                    'feature_importance': result.get('feature_importance', {})
                }
                for name, result in results.items()
                if 'error' not in result
            }
        }
        
        metadata_path = os.path.join(self.model_dir, 'training_metadata.json')
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2)
        
        logger.info(f"Production models saved to {self.model_dir}")

def main():
    """Main training function"""
    
    print("🤖 Production Model Training")
    print("=" * 60)
    print(f"📅 Training Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        trainer = ProductionModelTrainer()
        
        # Load training data
        print("📊 Loading training data...")
        X, y = trainer.load_training_data(days_back=60)  # Last 60 days
        
        if len(X) == 0:
            print("❌ No training data available")
            return False
        
        print(f"✅ Loaded {len(X)} samples with {len(X.columns)} features")
        print(f"📈 Target range: {y.min():.2f} - {y.max():.2f} kWh")
        
        # Train models
        print("\n🚀 Training production models...")
        results = trainer.train_production_models(X, y)
        
        # Display results
        print("\n🎯 Training Results:")
        best_model = None
        best_score = float('inf')
        
        for name, result in results.items():
            if 'error' in result:
                print(f"   ❌ {name}: {result['error']}")
            else:
                mae = result['mae']
                r2 = result['r2']
                mape = result['mape']
                print(f"   ✅ {name}: MAE={mae:.3f}, R²={r2:.3f}, MAPE={mape:.1f}%")
                
                if mae < best_score:
                    best_score = mae
                    best_model = name
        
        if best_model:
            print(f"\n🏆 Best Model: {best_model} (MAE: {best_score:.3f})")
            
            # Show feature importance for best model
            if best_model in results and 'feature_importance' in results[best_model]:
                importance = results[best_model]['feature_importance']
                if importance:
                    print(f"\n📊 Top Features for {best_model}:")
                    sorted_features = sorted(importance.items(), key=lambda x: x[1], reverse=True)
                    for i, (feature, imp) in enumerate(sorted_features[:5]):
                        print(f"   {i+1}. {feature}: {imp:.4f}")
        
        # Save models
        print("\n💾 Saving production models...")
        trainer.save_production_models(results)
        
        print("🎉 Production model training completed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Production training failed: {e}")
        logger.exception("Production training failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
