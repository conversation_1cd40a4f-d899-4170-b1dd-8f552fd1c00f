#!/usr/bin/env python3
"""
Hourly Prediction Analysis
Test Production Optimized Model for hourly predictions with real weather data
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import requests
import psycopg2
import pandas as pd
import numpy as np
import joblib
import json
import lightgbm as lgb
from pathlib import Path
from datetime import datetime, timedelta
import logging
from dotenv import load_dotenv
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HourlyPredictionAnalyzer:
    """Analyze hourly predictions with Production Optimized Model"""
    
    def __init__(self):
        load_dotenv()
        self.project_root = Path("/home/<USER>/solar-prediction-project")
        
        # Weather API configuration
        self.api_url = "https://api.open-meteo.com/v1/forecast"
        self.latitude = 38.141367951893024
        self.longitude = 24.00715534164505
        
        # Load Production Optimized Model
        self.daily_model_path = "models/production_optimized/daily_yield_model.joblib"
        self.daily_scaler_path = "models/production_optimized/daily_yield_scaler.joblib"
        self.daily_features_path = "models/production_optimized/daily_yield_features.json"
        
        self.daily_model = None
        self.daily_scaler = None
        self.daily_features = None
        
        # Results storage
        self.analysis_results = {}
        
    def connect_database(self):
        """Connect to database"""
        try:
            conn = psycopg2.connect(
                host=os.getenv('DB_HOST', 'localhost'),
                database=os.getenv('DB_NAME', 'solar_prediction'),
                user=os.getenv('DB_USER', 'postgres'),
                password=os.getenv('DB_PASSWORD', 'postgres')
            )
            return conn
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            return None
    
    def load_daily_model(self):
        """Load the Production Optimized Daily Yield Model"""
        try:
            self.daily_model = joblib.load(self.daily_model_path)
            self.daily_scaler = joblib.load(self.daily_scaler_path)
            
            with open(self.daily_features_path, 'r') as f:
                self.daily_features = json.load(f)
            
            logger.info(f"✅ Daily model loaded: {len(self.daily_features)} features")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to load daily model: {e}")
            return False
    
    def get_real_weather_data_hourly(self, start_date, end_date):
        """Get real hourly weather data from database"""
        try:
            conn = self.connect_database()
            if not conn:
                return None
            
            cursor = conn.cursor()
            query = """
                SELECT 
                    timestamp,
                    COALESCE(temperature, 25) as temperature,
                    COALESCE(cloud_cover, 30) as cloud_cover,
                    COALESCE(ghi, 650) as ghi,
                    COALESCE(dni, 520) as dni,
                    COALESCE(dhi, 130) as dhi
                FROM cams_radiation_data
                WHERE timestamp >= %s AND timestamp <= %s
                ORDER BY timestamp
            """
            
            cursor.execute(query, (start_date, end_date))
            results = cursor.fetchall()
            conn.close()
            
            if results:
                df = pd.DataFrame(results, columns=[
                    'timestamp', 'temperature', 'cloud_cover', 'ghi', 'dni', 'dhi'
                ])
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                return df
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Failed to get weather data: {e}")
            return None
    
    def get_real_solar_data_hourly(self, start_date, end_date, system_table):
        """Get real hourly solar data from database"""
        try:
            conn = self.connect_database()
            if not conn:
                return None
            
            cursor = conn.cursor()
            query = f"""
                SELECT 
                    timestamp,
                    COALESCE(ac_power, 0) as ac_power,
                    COALESCE(soc, 75) as soc,
                    COALESCE(bat_power, 0) as bat_power,
                    COALESCE(powerdc1, 0) as powerdc1,
                    COALESCE(powerdc2, 0) as powerdc2,
                    COALESCE(yield_today, 0) as yield_today
                FROM {system_table}
                WHERE timestamp >= %s AND timestamp <= %s
                ORDER BY timestamp
            """
            
            cursor.execute(query, (start_date, end_date))
            results = cursor.fetchall()
            conn.close()
            
            if results:
                df = pd.DataFrame(results, columns=[
                    'timestamp', 'ac_power', 'soc', 'bat_power', 'powerdc1', 'powerdc2', 'yield_today'
                ])
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                return df
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Failed to get solar data: {e}")
            return None
    
    def create_hourly_features_from_daily_model(self, timestamp, weather_data, solar_data, system_id):
        """Create hourly features using daily model feature structure"""
        try:
            # Extract time features
            hour = timestamp.hour
            month = timestamp.month
            day_of_year = timestamp.dayofyear
            
            # Cyclical encoding
            month_sin = np.sin(2 * np.pi * month / 12)
            month_cos = np.cos(2 * np.pi * month / 12)
            day_sin = np.sin(2 * np.pi * day_of_year / 365)
            day_cos = np.cos(2 * np.pi * day_of_year / 365)
            
            # Seasonal indicators
            is_summer = 1 if month in [6, 7, 8] else 0
            is_winter = 1 if month in [12, 1, 2] else 0
            
            # System features
            system_1 = 1 if system_id == 1 else 0
            system_2 = 1 if system_id == 2 else 0
            
            # Battery features (from solar data)
            soc_norm = solar_data.get('soc', 75) / 100
            bat_power_norm = abs(solar_data.get('bat_power', 0)) / 5000  # Normalize to 5kW max
            
            # Weather features (from weather data)
            temp_norm = (weather_data.get('temperature', 25) - 20) / 30
            cloud_norm = weather_data.get('cloud_cover', 30) / 100
            ghi_norm = weather_data.get('ghi', 650) / 1000
            
            # Temperature efficiency
            temp_efficiency = 1 - (weather_data.get('temperature', 25) - 25) * 0.004
            temp_efficiency = max(0.7, min(1.1, temp_efficiency))
            
            # Hourly solar position (simplified)
            if 6 <= hour <= 18:
                solar_elevation = np.sin(np.pi * (hour - 6) / 12)  # Simplified solar elevation
            else:
                solar_elevation = 0
            
            # Create feature vector matching daily model structure
            features = np.array([
                system_1, system_2, month_sin, month_cos, day_sin, day_cos,
                is_summer, is_winter, soc_norm, bat_power_norm, 0.08,  # avg_charge_norm placeholder
                ghi_norm, ghi_norm * 1.2, temp_norm, cloud_norm, temp_efficiency  # max_ghi_norm = ghi_norm * 1.2
            ])
            
            return features
            
        except Exception as e:
            logger.error(f"❌ Feature creation failed: {e}")
            return np.zeros(16)  # Return zero vector if failed
    
    def predict_hourly_from_daily_model(self, timestamp, weather_data, solar_data, system_id):
        """Use daily model to predict hourly AC power"""
        try:
            # Create features
            features = self.create_hourly_features_from_daily_model(
                timestamp, weather_data, solar_data, system_id
            )
            
            # Scale features
            features_scaled = self.daily_scaler.transform([features])
            
            # Make prediction (daily yield in kWh)
            daily_yield_prediction = self.daily_model.predict(features_scaled)[0]
            
            # Convert daily yield to hourly AC power
            # Simplified solar curve - peak at noon, zero at night
            hour = timestamp.hour
            if 6 <= hour <= 18:
                # Solar curve: peak at hour 12, zero at hours 6 and 18
                solar_factor = np.sin(np.pi * (hour - 6) / 12)
                # Distribute daily yield across daylight hours with solar curve
                hourly_power = (daily_yield_prediction * 1000 * solar_factor) / 8  # 8 effective daylight hours
            else:
                hourly_power = 0  # No solar production at night
            
            return max(0, hourly_power)  # Ensure non-negative
            
        except Exception as e:
            logger.error(f"❌ Hourly prediction failed: {e}")
            return 0
    
    def analyze_hourly_accuracy_june_1(self):
        """Analyze hourly prediction accuracy for June 1, 2025"""
        logger.info("📊 ANALYZING HOURLY ACCURACY FOR JUNE 1, 2025")
        logger.info("=" * 70)
        
        try:
            if not self.load_daily_model():
                return None
            
            # Define June 1, 2025
            june_1 = datetime(2025, 6, 1)
            june_2 = datetime(2025, 6, 2)
            
            # Get real weather data
            weather_df = self.get_real_weather_data_hourly(june_1, june_2)
            
            # Get real solar data for both systems
            solar_df_1 = self.get_real_solar_data_hourly(june_1, june_2, 'solax_data')
            solar_df_2 = self.get_real_solar_data_hourly(june_1, june_2, 'solax_data2')
            
            if weather_df is None or solar_df_1 is None or solar_df_2 is None:
                logger.error("❌ Could not get required data")
                return None
            
            # Analyze hourly predictions vs actual
            hourly_results = []
            
            for hour in range(24):
                hour_time = june_1.replace(hour=hour)
                
                # Get weather data for this hour
                weather_hour = weather_df[
                    weather_df['timestamp'].dt.floor('H') == hour_time
                ]
                
                if weather_hour.empty:
                    continue
                
                weather_data = weather_hour.iloc[0].to_dict()
                
                # Analyze both systems
                for system_id, solar_df in [(1, solar_df_1), (2, solar_df_2)]:
                    # Get solar data for this hour
                    solar_hour = solar_df[
                        solar_df['timestamp'].dt.floor('H') == hour_time
                    ]
                    
                    if solar_hour.empty:
                        continue
                    
                    solar_data = solar_hour.iloc[0].to_dict()
                    actual_power = solar_data['ac_power']
                    
                    # Make prediction
                    predicted_power = self.predict_hourly_from_daily_model(
                        hour_time, weather_data, solar_data, system_id
                    )
                    
                    # Calculate accuracy
                    if actual_power > 0:
                        accuracy = (1 - abs(predicted_power - actual_power) / actual_power) * 100
                    else:
                        accuracy = 100 if predicted_power == 0 else 0
                    
                    hourly_results.append({
                        'hour': hour,
                        'system': system_id,
                        'actual_power': actual_power,
                        'predicted_power': predicted_power,
                        'accuracy': accuracy,
                        'weather': {
                            'temperature': weather_data['temperature'],
                            'cloud_cover': weather_data['cloud_cover'],
                            'ghi': weather_data['ghi']
                        }
                    })
                    
                    logger.info(f"   Hour {hour:2d} System {system_id}: "
                              f"Actual {actual_power:6.0f}W, Predicted {predicted_power:6.0f}W, "
                              f"Accuracy {accuracy:5.1f}%")
            
            # Calculate summary statistics
            df_results = pd.DataFrame(hourly_results)
            
            summary = {
                'total_hours_analyzed': len(df_results),
                'average_accuracy': df_results['accuracy'].mean(),
                'daylight_hours_accuracy': df_results[df_results['actual_power'] > 100]['accuracy'].mean(),
                'system_comparison': {
                    'system1_accuracy': df_results[df_results['system'] == 1]['accuracy'].mean(),
                    'system2_accuracy': df_results[df_results['system'] == 2]['accuracy'].mean()
                },
                'hourly_breakdown': df_results.groupby('hour').agg({
                    'accuracy': 'mean',
                    'actual_power': 'mean',
                    'predicted_power': 'mean'
                }).to_dict()
            }
            
            logger.info(f"\n📊 HOURLY ANALYSIS SUMMARY:")
            logger.info(f"   Total hours analyzed: {summary['total_hours_analyzed']}")
            logger.info(f"   Average accuracy: {summary['average_accuracy']:.1f}%")
            logger.info(f"   Daylight hours accuracy: {summary['daylight_hours_accuracy']:.1f}%")
            logger.info(f"   System 1 accuracy: {summary['system_comparison']['system1_accuracy']:.1f}%")
            logger.info(f"   System 2 accuracy: {summary['system_comparison']['system2_accuracy']:.1f}%")
            
            return {
                'hourly_results': hourly_results,
                'summary': summary,
                'analysis_date': june_1.strftime('%Y-%m-%d')
            }
            
        except Exception as e:
            logger.error(f"❌ Hourly accuracy analysis failed: {e}")
            return None
    
    def generate_72h_forecast_with_real_weather(self):
        """Generate 72-hour forecast using real weather data"""
        logger.info("🔮 GENERATING 72-HOUR FORECAST WITH REAL WEATHER")
        logger.info("=" * 70)

        try:
            # Get weather forecast for next 72 hours
            params = {
                "latitude": self.latitude,
                "longitude": self.longitude,
                "hourly": "temperature_2m,cloud_cover,shortwave_radiation,direct_radiation,diffuse_radiation",
                "forecast_hours": 72,
                "timezone": "Europe/Athens"
            }

            response = requests.get(self.api_url, params=params, timeout=30)
            response.raise_for_status()

            weather_data = response.json()
            hourly_weather = weather_data.get("hourly", {})

            if not hourly_weather:
                logger.error("❌ No weather forecast data received")
                return None

            # Generate hourly predictions for next 72 hours
            forecast_results = []
            daily_summaries = {}

            timestamps = pd.to_datetime(hourly_weather["time"])

            for i, timestamp in enumerate(timestamps[:72]):  # Limit to 72 hours
                weather_hour = {
                    'temperature': hourly_weather["temperature_2m"][i],
                    'cloud_cover': hourly_weather["cloud_cover"][i],
                    'ghi': hourly_weather["shortwave_radiation"][i] or 0,
                    'dni': hourly_weather["direct_radiation"][i] or 0,
                    'dhi': hourly_weather["diffuse_radiation"][i] or 0
                }

                # Predict for both systems
                for system_id in [1, 2]:
                    # Use typical SOC for future predictions
                    solar_data = {'soc': 75, 'bat_power': 0}

                    predicted_power = self.predict_hourly_from_daily_model(
                        timestamp, weather_hour, solar_data, system_id
                    )

                    forecast_results.append({
                        'timestamp': timestamp.isoformat(),
                        'hour': timestamp.hour,
                        'date': timestamp.date().isoformat(),
                        'system': system_id,
                        'predicted_power_w': predicted_power,
                        'weather': weather_hour,
                        'confidence': max(0.5, 0.95 - i * 0.005)  # Decreasing confidence over time
                    })

            # Calculate daily summaries
            df_forecast = pd.DataFrame(forecast_results)

            for date in df_forecast['date'].unique():
                day_data = df_forecast[df_forecast['date'] == date]

                daily_summaries[date] = {}
                for system_id in [1, 2]:
                    system_data = day_data[day_data['system'] == system_id]
                    daily_yield = system_data['predicted_power_w'].sum() / 1000  # Convert to kWh
                    peak_power = system_data['predicted_power_w'].max()
                    peak_hour = system_data.loc[system_data['predicted_power_w'].idxmax(), 'hour'] if not system_data.empty else 12

                    daily_summaries[date][f'system{system_id}'] = {
                        'daily_yield_kwh': daily_yield,
                        'peak_power_w': peak_power,
                        'peak_hour': peak_hour,
                        'production_hours': len(system_data[system_data['predicted_power_w'] > 100])
                    }

                # Weather summary for the day
                day_weather = day_data.groupby('date').agg({
                    'weather': lambda x: {
                        'avg_temperature': np.mean([w['temperature'] for w in x]),
                        'avg_cloud_cover': np.mean([w['cloud_cover'] for w in x]),
                        'max_ghi': np.max([w['ghi'] for w in x])
                    }
                }).iloc[0]['weather'] if not day_data.empty else {}

                daily_summaries[date]['weather'] = day_weather

            logger.info(f"✅ Generated 72-hour forecast: {len(forecast_results)} hourly predictions")

            return {
                'hourly_forecast': forecast_results,
                'daily_summaries': daily_summaries,
                'forecast_generated': datetime.now().isoformat(),
                'weather_source': 'Open-Meteo API forecast'
            }

        except Exception as e:
            logger.error(f"❌ 72-hour forecast generation failed: {e}")
            return None

    def compare_with_historical_similar_weather(self, forecast_data):
        """Compare forecast with historical data from similar weather conditions"""
        logger.info("📊 COMPARING WITH HISTORICAL SIMILAR WEATHER")
        logger.info("=" * 70)

        try:
            conn = self.connect_database()
            if not conn:
                return None

            # Get historical data for June 2024 (same period last year)
            june_2024_start = datetime(2024, 6, 3)
            june_2024_end = datetime(2024, 6, 5)

            # Get weather and solar data for June 2024
            weather_2024 = self.get_real_weather_data_hourly(june_2024_start, june_2024_end)
            solar_2024_s1 = self.get_real_solar_data_hourly(june_2024_start, june_2024_end, 'solax_data')
            solar_2024_s2 = self.get_real_solar_data_hourly(june_2024_start, june_2024_end, 'solax_data2')

            comparison_results = {
                'june_2024_comparison': {},
                'similar_weather_days': []
            }

            if weather_2024 is not None and solar_2024_s1 is not None:
                # Calculate June 2024 daily yields
                for date in pd.date_range(june_2024_start, june_2024_end - timedelta(days=1)):
                    date_str = date.strftime('%Y-%m-%d')

                    # Get daily yields for both systems
                    s1_day = solar_2024_s1[solar_2024_s1['timestamp'].dt.date == date.date()]
                    s2_day = solar_2024_s2[solar_2024_s2['timestamp'].dt.date == date.date()] if solar_2024_s2 is not None else pd.DataFrame()

                    if not s1_day.empty:
                        s1_yield = s1_day['yield_today'].max()
                        s2_yield = s2_day['yield_today'].max() if not s2_day.empty else 0

                        comparison_results['june_2024_comparison'][date_str] = {
                            'system1_yield_kwh': s1_yield,
                            'system2_yield_kwh': s2_yield,
                            'total_yield_kwh': s1_yield + s2_yield
                        }

                        logger.info(f"   June 2024 {date_str}: S1={s1_yield:.1f} kWh, S2={s2_yield:.1f} kWh")

            # Find similar weather days in recent history
            cursor = conn.cursor()

            # Get forecast weather averages for comparison
            forecast_daily = forecast_data['daily_summaries']

            for forecast_date, forecast_summary in forecast_daily.items():
                if 'weather' in forecast_summary:
                    forecast_weather = forecast_summary['weather']

                    # Find historical days with similar weather
                    query = """
                    WITH daily_weather AS (
                        SELECT
                            DATE(timestamp) as date,
                            AVG(COALESCE(temperature, 25)) as avg_temperature,
                            AVG(COALESCE(cloud_cover, 30)) as avg_cloud_cover,
                            AVG(COALESCE(ghi, 650)) as avg_ghi
                        FROM cams_radiation_data
                        WHERE timestamp >= '2024-03-01'
                        GROUP BY DATE(timestamp)
                    ),
                    system1_daily AS (
                        SELECT
                            DATE(timestamp) as date,
                            MAX(yield_today) as daily_yield
                        FROM solax_data
                        WHERE timestamp >= '2024-03-01'
                        AND yield_today > 0
                        GROUP BY DATE(timestamp)
                        HAVING COUNT(*) > 100
                    )
                    SELECT
                        dw.date,
                        s1.daily_yield,
                        dw.avg_temperature,
                        dw.avg_cloud_cover,
                        dw.avg_ghi,
                        ABS(dw.avg_temperature - %s) as temp_diff,
                        ABS(dw.avg_cloud_cover - %s) as cloud_diff
                    FROM daily_weather dw
                    JOIN system1_daily s1 ON dw.date = s1.date
                    WHERE ABS(dw.avg_temperature - %s) < 5
                    AND ABS(dw.avg_cloud_cover - %s) < 20
                    ORDER BY (ABS(dw.avg_temperature - %s) + ABS(dw.avg_cloud_cover - %s))
                    LIMIT 5
                    """

                    params = [
                        forecast_weather.get('avg_temperature', 25),
                        forecast_weather.get('avg_cloud_cover', 30),
                        forecast_weather.get('avg_temperature', 25),
                        forecast_weather.get('avg_cloud_cover', 30),
                        forecast_weather.get('avg_temperature', 25),
                        forecast_weather.get('avg_cloud_cover', 30)
                    ]

                    cursor.execute(query, params)
                    similar_days = cursor.fetchall()

                    if similar_days:
                        similar_yields = [row[1] for row in similar_days]
                        avg_similar_yield = np.mean(similar_yields)

                        comparison_results['similar_weather_days'].append({
                            'forecast_date': forecast_date,
                            'similar_days_count': len(similar_days),
                            'historical_avg_yield': avg_similar_yield,
                            'forecast_yield_s1': forecast_summary.get('system1', {}).get('daily_yield_kwh', 0),
                            'forecast_yield_s2': forecast_summary.get('system2', {}).get('daily_yield_kwh', 0),
                            'weather_conditions': forecast_weather
                        })

                        logger.info(f"   {forecast_date}: {len(similar_days)} similar days, avg {avg_similar_yield:.1f} kWh")

            conn.close()
            return comparison_results

        except Exception as e:
            logger.error(f"❌ Historical comparison failed: {e}")
            return None

    def run_hourly_prediction_analysis(self):
        """Run complete hourly prediction analysis"""
        logger.info("🚀 HOURLY PREDICTION ANALYSIS")
        logger.info("=" * 80)
        logger.info("🎯 Testing Production Optimized Model for hourly predictions")

        try:
            # Load daily model
            if not self.load_daily_model():
                return False

            # 1. Analyze June 1 hourly accuracy
            june_1_analysis = self.analyze_hourly_accuracy_june_1()

            if june_1_analysis:
                self.analysis_results['june_1_hourly'] = june_1_analysis

            # 2. Generate 72-hour forecast
            forecast_72h = self.generate_72h_forecast_with_real_weather()

            if forecast_72h:
                self.analysis_results['forecast_72h'] = forecast_72h

                # 3. Compare with historical data
                historical_comparison = self.compare_with_historical_similar_weather(forecast_72h)

                if historical_comparison:
                    self.analysis_results['historical_comparison'] = historical_comparison

            # Save results
            results_file = f"test/results/hourly_prediction_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            os.makedirs(os.path.dirname(results_file), exist_ok=True)

            with open(results_file, 'w') as f:
                json.dump(self.analysis_results, f, indent=2, default=str)

            # Display comprehensive summary
            logger.info("\n" + "=" * 80)
            logger.info("🎉 COMPREHENSIVE HOURLY PREDICTION ANALYSIS COMPLETE")
            logger.info("=" * 80)

            if june_1_analysis:
                summary = june_1_analysis['summary']
                logger.info(f"📊 June 1, 2025 Hourly Analysis:")
                logger.info(f"   Average accuracy: {summary['average_accuracy']:.1f}%")
                logger.info(f"   Daylight accuracy: {summary['daylight_hours_accuracy']:.1f}%")
                logger.info(f"   System 1: {summary['system_comparison']['system1_accuracy']:.1f}%")
                logger.info(f"   System 2: {summary['system_comparison']['system2_accuracy']:.1f}%")

            if forecast_72h:
                logger.info(f"\n🔮 72-Hour Forecast Generated:")
                logger.info(f"   Total hourly predictions: {len(forecast_72h['hourly_forecast'])}")
                logger.info(f"   Daily summaries: {len(forecast_72h['daily_summaries'])} days")

                # Show daily forecast summary
                for date, summary in forecast_72h['daily_summaries'].items():
                    s1_yield = summary.get('system1', {}).get('daily_yield_kwh', 0)
                    s2_yield = summary.get('system2', {}).get('daily_yield_kwh', 0)
                    weather = summary.get('weather', {})
                    logger.info(f"   {date}: S1={s1_yield:.1f} kWh, S2={s2_yield:.1f} kWh, "
                              f"T={weather.get('avg_temperature', 0):.1f}°C, "
                              f"Cloud={weather.get('avg_cloud_cover', 0):.0f}%")

            if historical_comparison:
                logger.info(f"\n📊 Historical Comparison:")
                logger.info(f"   June 2024 data: {len(historical_comparison.get('june_2024_comparison', {}))}")
                logger.info(f"   Similar weather days: {len(historical_comparison.get('similar_weather_days', []))}")

            logger.info(f"\n💾 Results saved: {results_file}")
            logger.info("=" * 80)

            return True

        except Exception as e:
            logger.error(f"❌ Hourly prediction analysis failed: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """Execute hourly prediction analysis"""
    analyzer = HourlyPredictionAnalyzer()
    success = analyzer.run_hourly_prediction_analysis()
    
    if success:
        print("\n🎯 Hourly prediction analysis completed successfully!")
        print("📊 Daily model tested for hourly predictions with real weather data")
        return True
    else:
        print("\n❌ Hourly prediction analysis failed!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
