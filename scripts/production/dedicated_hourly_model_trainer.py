#!/usr/bin/env python3
"""
Dedicated Hourly Model Trainer
Simple, focused implementation for training hourly prediction models
"""

import sys
import os
import numpy as np
import pandas as pd
import json
import math
from datetime import datetime, timedelta
from pathlib import Path

# Simple logging
def log(message):
    print(f"{datetime.now().strftime('%H:%M:%S')} - {message}")

class SimpleSolarCalculator:
    """Simple solar position calculations"""
    
    @staticmethod
    def solar_elevation(hour, day_of_year, latitude=38.14):
        """Simplified solar elevation calculation"""
        # Solar declination
        declination = 23.45 * math.sin(math.radians(360 * (284 + day_of_year) / 365))
        
        # Hour angle
        hour_angle = 15 * (hour - 12)
        
        # Solar elevation
        lat_rad = math.radians(latitude)
        dec_rad = math.radians(declination)
        hour_rad = math.radians(hour_angle)
        
        elevation = math.asin(
            math.sin(lat_rad) * math.sin(dec_rad) + 
            math.cos(lat_rad) * math.cos(dec_rad) * math.cos(hour_rad)
        )
        
        return max(0, math.degrees(elevation))

class DedicatedHourlyModelTrainer:
    """Simple dedicated hourly model trainer"""
    
    def __init__(self):
        self.project_root = Path("/home/<USER>/solar-prediction-project")
        self.solar_calc = SimpleSolarCalculator()
        
        # Known real data for training
        self.known_data = {
            '2025-06-01': {'system1': 72.8, 'system2': 67.7, 'temp': 25, 'cloud': 10},
            '2025-06-02': {'system1': 31.8, 'system2': 34.0, 'temp': 22, 'cloud': 60},
            '2024-06-03': {'system1': 68.3, 'system2': 68.3, 'temp': 26, 'cloud': 5},
            '2024-06-04': {'system1': 65.4, 'system2': 65.4, 'temp': 24, 'cloud': 15}
        }
        
        self.training_data = {'system1': [], 'system2': []}
    
    def create_simple_features(self, hour, day_of_year, temperature, cloud_cover, system_id):
        """Create simple but effective features"""
        # Solar position
        solar_elevation = self.solar_calc.solar_elevation(hour, day_of_year)
        
        # Cyclical time encoding
        hour_sin = math.sin(2 * math.pi * hour / 24)
        hour_cos = math.cos(2 * math.pi * hour / 24)
        
        # Solar radiation potential
        solar_potential = math.sin(math.radians(solar_elevation)) if solar_elevation > 0 else 0
        
        # Weather efficiency
        temp_efficiency = 1 - (temperature - 25) * 0.004
        temp_efficiency = max(0.8, min(1.1, temp_efficiency))
        
        cloud_efficiency = 1 - (cloud_cover / 100) * 0.7
        cloud_efficiency = max(0.2, cloud_efficiency)
        
        # Combined efficiency
        weather_efficiency = temp_efficiency * cloud_efficiency
        
        # Time indicators
        is_daylight = 1 if solar_elevation > 0 else 0
        is_peak = 1 if 10 <= hour <= 14 and solar_elevation > 30 else 0
        
        # System indicator
        system_factor = 1.0 if system_id == 1 else 1.0  # Same for both systems
        
        return np.array([
            hour, hour_sin, hour_cos, day_of_year,
            solar_elevation, solar_potential,
            temperature, cloud_cover,
            temp_efficiency, cloud_efficiency, weather_efficiency,
            is_daylight, is_peak, system_factor
        ])
    
    def generate_training_data(self):
        """Generate realistic training data"""
        log("🔬 Generating training data...")
        
        for date_str, data in self.known_data.items():
            date = datetime.strptime(date_str, '%Y-%m-%d')
            day_of_year = date.timetuple().tm_yday
            
            for system_id in [1, 2]:
                daily_yield = data[f'system{system_id}']
                temperature = data['temp']
                cloud_cover = data['cloud']
                
                # Generate hourly data
                for hour in range(24):
                    # Calculate realistic hourly power
                    solar_elevation = self.solar_calc.solar_elevation(hour, day_of_year)
                    
                    if solar_elevation > 0:
                        # Solar curve
                        solar_factor = math.sin(math.radians(solar_elevation))
                        
                        # Weather impact
                        temp_factor = 1 - (temperature - 25) * 0.004
                        temp_factor = max(0.8, min(1.1, temp_factor))
                        cloud_factor = 1 - (cloud_cover / 100) * 0.7
                        cloud_factor = max(0.2, cloud_factor)
                        
                        # Time distribution (more realistic)
                        if hour < 8 or hour > 16:
                            time_factor = 0.6
                        elif 10 <= hour <= 14:
                            time_factor = 1.3
                        else:
                            time_factor = 1.0
                        
                        # Calculate hourly power
                        base_power = solar_factor * temp_factor * cloud_factor * time_factor
                        hourly_power = base_power * (daily_yield * 1000 / 7)  # Distribute over ~7 effective hours
                        hourly_power = max(0, min(6000, hourly_power))  # Limit to system capacity
                        
                        # Add realistic variation
                        variation = np.random.normal(0, hourly_power * 0.1)
                        hourly_power = max(0, hourly_power + variation)
                    else:
                        hourly_power = 0
                    
                    # Create features
                    features = self.create_simple_features(
                        hour, day_of_year, temperature, cloud_cover, system_id
                    )
                    
                    # Store training sample
                    self.training_data[f'system{system_id}'].append({
                        'features': features,
                        'target': hourly_power,
                        'hour': hour,
                        'date': date_str
                    })
        
        # Convert to arrays
        for system_id in [1, 2]:
            data = self.training_data[f'system{system_id}']
            X = np.array([sample['features'] for sample in data])
            y = np.array([sample['target'] for sample in data])
            
            log(f"   System {system_id}: {len(X)} samples, features: {X.shape[1]}")
            log(f"   Power range: {y.min():.0f}W - {y.max():.0f}W")
        
        log("✅ Training data generated")
        return True
    
    def simple_linear_model(self, X, y):
        """Simple linear regression model"""
        # Add bias term
        X_with_bias = np.column_stack([np.ones(X.shape[0]), X])
        
        # Normal equation: w = (X^T X)^(-1) X^T y
        try:
            XtX = np.dot(X_with_bias.T, X_with_bias)
            Xty = np.dot(X_with_bias.T, y)
            weights = np.linalg.solve(XtX, Xty)
            return weights
        except:
            # Fallback to pseudo-inverse
            weights = np.linalg.pinv(X_with_bias).dot(y)
            return weights
    
    def predict_with_model(self, weights, features):
        """Make prediction with linear model"""
        features_with_bias = np.concatenate([[1], features])
        return max(0, np.dot(weights, features_with_bias))
    
    def train_simple_models(self):
        """Train simple but effective models"""
        log("🤖 Training simple hourly models...")
        
        models = {}
        
        for system_id in [1, 2]:
            data = self.training_data[f'system{system_id}']
            X = np.array([sample['features'] for sample in data])
            y = np.array([sample['target'] for sample in data])
            
            # Train simple linear model
            weights = self.simple_linear_model(X, y)
            
            # Test model
            predictions = []
            actuals = []
            
            for i in range(len(X)):
                pred = self.predict_with_model(weights, X[i])
                predictions.append(pred)
                actuals.append(y[i])
            
            # Calculate accuracy
            mape_errors = []
            for pred, actual in zip(predictions, actuals):
                if actual > 0:
                    mape_errors.append(abs(pred - actual) / actual)
                elif pred == 0 and actual == 0:
                    mape_errors.append(0)
                else:
                    mape_errors.append(1)
            
            mape = np.mean(mape_errors)
            accuracy = max(0, (1 - mape) * 100)
            
            # Calculate RMSE
            rmse = np.sqrt(np.mean([(p - a) ** 2 for p, a in zip(predictions, actuals)]))
            
            models[f'system{system_id}'] = {
                'weights': weights,
                'accuracy': accuracy,
                'rmse': rmse,
                'feature_count': X.shape[1]
            }
            
            log(f"   System {system_id}: {accuracy:.1f}% accuracy, RMSE: {rmse:.1f}W")
        
        # Save models
        models_dir = self.project_root / "models" / "simple_hourly"
        models_dir.mkdir(parents=True, exist_ok=True)
        
        for system_id in [1, 2]:
            model_data = models[f'system{system_id}']
            
            # Save model
            model_file = models_dir / f"system{system_id}_simple_hourly.json"
            with open(model_file, 'w') as f:
                json.dump({
                    'weights': model_data['weights'].tolist(),
                    'accuracy': model_data['accuracy'],
                    'rmse': model_data['rmse'],
                    'feature_count': model_data['feature_count'],
                    'training_date': datetime.now().isoformat()
                }, f, indent=2)
            
            log(f"   💾 System {system_id} model saved: {model_data['accuracy']:.1f}% accuracy")
        
        log("✅ Simple hourly models trained and saved")
        return models
    
    def test_models_on_known_data(self, models):
        """Test models on known data"""
        log("🧪 Testing models on known data...")
        
        test_results = {}
        
        for date_str, data in self.known_data.items():
            date = datetime.strptime(date_str, '%Y-%m-%d')
            day_of_year = date.timetuple().tm_yday
            
            date_results = {}
            
            for system_id in [1, 2]:
                model = models[f'system{system_id}']
                weights = np.array(model['weights'])
                
                daily_predictions = []
                
                # Predict for each hour
                for hour in range(24):
                    features = self.create_simple_features(
                        hour, day_of_year, data['temp'], data['cloud'], system_id
                    )
                    
                    prediction = self.predict_with_model(weights, features)
                    daily_predictions.append(prediction)
                
                # Calculate daily total
                predicted_daily = sum(daily_predictions) / 1000  # Convert to kWh
                actual_daily = data[f'system{system_id}']
                
                # Calculate accuracy
                if actual_daily > 0:
                    daily_accuracy = (1 - abs(predicted_daily - actual_daily) / actual_daily) * 100
                else:
                    daily_accuracy = 100 if predicted_daily == 0 else 0
                
                date_results[f'system{system_id}'] = {
                    'predicted': predicted_daily,
                    'actual': actual_daily,
                    'accuracy': daily_accuracy
                }
                
                log(f"   {date_str} System {system_id}: {predicted_daily:.1f} kWh pred vs {actual_daily:.1f} kWh actual ({daily_accuracy:.1f}%)")
            
            test_results[date_str] = date_results
        
        # Calculate overall accuracy
        all_accuracies = []
        for date_results in test_results.values():
            for system_results in date_results.values():
                all_accuracies.append(system_results['accuracy'])
        
        overall_accuracy = np.mean(all_accuracies)
        log(f"📊 Overall accuracy: {overall_accuracy:.1f}%")
        
        return test_results, overall_accuracy
    
    def run_training(self):
        """Run complete training process"""
        log("🚀 DEDICATED HOURLY MODEL TRAINING")
        log("=" * 60)
        
        try:
            # Generate training data
            if not self.generate_training_data():
                return False
            
            # Train models
            models = self.train_simple_models()
            if not models:
                return False
            
            # Test models
            test_results, overall_accuracy = self.test_models_on_known_data(models)
            
            log("\n" + "=" * 60)
            log("🎉 DEDICATED HOURLY MODEL TRAINING COMPLETED")
            log("=" * 60)
            log(f"📊 Overall Accuracy: {overall_accuracy:.1f}%")
            
            if overall_accuracy >= 95.0:
                log("🎯 TARGET ACHIEVED: 95%+ accuracy!")
            elif overall_accuracy >= 85.0:
                log("⚠️ Good performance, but below 95% target")
            else:
                log("❌ Performance below expectations")
            
            log("💾 Models saved in models/simple_hourly/")
            
            return True
            
        except Exception as e:
            log(f"❌ Training failed: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """Execute dedicated hourly model training"""
    trainer = DedicatedHourlyModelTrainer()
    success = trainer.run_training()
    
    if success:
        print("\n🎯 Dedicated hourly model training completed!")
        return True
    else:
        print("\n❌ Training failed!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
