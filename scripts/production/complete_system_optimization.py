#!/usr/bin/env python3
"""
Complete System Optimization Framework
Comprehensive analysis and optimization of all models, data, and features
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import pandas as pd
import numpy as np
import psycopg2
import joblib
import json
import lightgbm as lgb
import xgboost as xgb
from sklearn.ensemble import RandomForestRegressor, VotingRegressor
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.preprocessing import RobustScaler, MinMaxScaler, StandardScaler
from sklearn.model_selection import TimeSeriesSplit
from datetime import datetime, timedelta
import logging
from pathlib import Path
from dotenv import load_dotenv
import warnings
warnings.filterwarnings('ignore')

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CompleteSystemOptimizer:
    """Complete system optimization framework"""
    
    def __init__(self):
        load_dotenv()
        self.project_root = Path("/home/<USER>/solar-prediction-project")
        self.models_dir = self.project_root / "models" / "optimized_final"
        self.models_dir.mkdir(parents=True, exist_ok=True)
        
        # Real data for validation
        self.real_data = {
            '2025-06-01': {'system1': 72.8, 'system2': 67.7},
            '2025-06-02': {'system1': 31.8, 'system2': 34.0}  # partial day
        }
        
        # Optimization results
        self.optimization_results = {
            'data_quality_analysis': {},
            'feature_optimization': {},
            'normalization_testing': {},
            'algorithm_comparison': {},
            'system_specific_models': {},
            'final_recommendations': {}
        }
        
    def connect_database(self):
        """Connect to database"""
        try:
            conn = psycopg2.connect(
                host=os.getenv('DB_HOST', 'localhost'),
                database=os.getenv('DB_NAME', 'solar_prediction'),
                user=os.getenv('DB_USER', 'postgres'),
                password=os.getenv('DB_PASSWORD', 'postgres')
            )
            return conn
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            return None
    
    def analyze_data_quality(self, conn):
        """Comprehensive data quality analysis"""
        logger.info("🔍 PHASE 1: DATA QUALITY ANALYSIS")
        logger.info("=" * 60)
        
        try:
            # Get comprehensive data statistics
            query = """
            WITH system1_stats AS (
                SELECT 
                    'system1' as system,
                    COUNT(*) as total_records,
                    COUNT(CASE WHEN ac_power IS NOT NULL THEN 1 END) as valid_ac_power,
                    COUNT(CASE WHEN soc IS NOT NULL THEN 1 END) as valid_soc,
                    COUNT(CASE WHEN bat_power IS NOT NULL THEN 1 END) as valid_bat_power,
                    MIN(ac_power) as min_ac_power,
                    MAX(ac_power) as max_ac_power,
                    AVG(ac_power) as avg_ac_power,
                    STDDEV(ac_power) as std_ac_power,
                    MIN(timestamp) as earliest_data,
                    MAX(timestamp) as latest_data
                FROM solax_data
                WHERE timestamp >= '2024-03-01'
            ),
            system2_stats AS (
                SELECT 
                    'system2' as system,
                    COUNT(*) as total_records,
                    COUNT(CASE WHEN ac_power IS NOT NULL THEN 1 END) as valid_ac_power,
                    COUNT(CASE WHEN soc IS NOT NULL THEN 1 END) as valid_soc,
                    COUNT(CASE WHEN bat_power IS NOT NULL THEN 1 END) as valid_bat_power,
                    MIN(ac_power) as min_ac_power,
                    MAX(ac_power) as max_ac_power,
                    AVG(ac_power) as avg_ac_power,
                    STDDEV(ac_power) as std_ac_power,
                    MIN(timestamp) as earliest_data,
                    MAX(timestamp) as latest_data
                FROM solax_data2
                WHERE timestamp >= '2024-03-01'
            )
            SELECT * FROM system1_stats
            UNION ALL
            SELECT * FROM system2_stats
            """
            
            df_stats = pd.read_sql(query, conn)
            
            data_quality = {}
            for _, row in df_stats.iterrows():
                system = row['system']
                completeness = (row['valid_ac_power'] / row['total_records']) * 100
                
                data_quality[system] = {
                    'total_records': row['total_records'],
                    'completeness_percent': completeness,
                    'ac_power_range': (row['min_ac_power'], row['max_ac_power']),
                    'ac_power_stats': {
                        'mean': row['avg_ac_power'],
                        'std': row['std_ac_power']
                    },
                    'data_period': (row['earliest_data'], row['latest_data']),
                    'quality_score': min(100, completeness + (100 - abs(row['std_ac_power'] / row['avg_ac_power'] * 100)))
                }
                
                logger.info(f"✅ {system.upper()}: {row['total_records']:,} records, {completeness:.1f}% complete")
                logger.info(f"   AC Power: {row['min_ac_power']:.0f}W to {row['max_ac_power']:.0f}W")
                logger.info(f"   Quality Score: {data_quality[system]['quality_score']:.1f}/100")
            
            self.optimization_results['data_quality_analysis'] = data_quality
            return data_quality
            
        except Exception as e:
            logger.error(f"❌ Data quality analysis failed: {e}")
            return None
    
    def load_and_prepare_data(self, conn, system_id=None):
        """Load and prepare training data"""
        logger.info(f"📊 Loading training data for {'System ' + str(system_id) if system_id else 'Both Systems'}...")
        
        try:
            if system_id:
                table = 'solax_data' if system_id == 1 else 'solax_data2'
                system_filter = ""
            else:
                # Combined query for both systems
                query = f"""
                WITH system1_data AS (
                    SELECT 
                        timestamp, ac_power, soc, bat_power, powerdc1, powerdc2,
                        yield_today, feedin_power, consume_energy, 1 as system_id
                    FROM solax_data
                    WHERE timestamp >= '2024-03-01'
                    AND ac_power IS NOT NULL AND ac_power >= 0 AND ac_power <= 15000
                    AND soc IS NOT NULL AND soc >= 0 AND soc <= 100
                    AND bat_power IS NOT NULL AND bat_power >= -10000 AND bat_power <= 10000
                ),
                system2_data AS (
                    SELECT 
                        timestamp, ac_power, soc, bat_power, powerdc1, powerdc2,
                        yield_today, feedin_power, consume_energy, 2 as system_id
                    FROM solax_data2
                    WHERE timestamp >= '2024-03-01'
                    AND ac_power IS NOT NULL AND ac_power >= 0 AND ac_power <= 15000
                    AND soc IS NOT NULL AND soc >= 0 AND soc <= 100
                    AND bat_power IS NOT NULL AND bat_power >= -10000 AND bat_power <= 10000
                ),
                combined_data AS (
                    SELECT * FROM system1_data
                    UNION ALL
                    SELECT * FROM system2_data
                ),
                weather_data AS (
                    SELECT
                        DATE_TRUNC('hour', timestamp) as hour_timestamp,
                        AVG(COALESCE(ghi, 400)) as ghi,
                        AVG(COALESCE(temperature, 20)) as temperature,
                        AVG(COALESCE(cloud_cover, 50)) as cloud_cover
                    FROM cams_radiation_data
                    WHERE timestamp >= '2024-03-01'
                    GROUP BY DATE_TRUNC('hour', timestamp)
                )
                SELECT
                    cd.*,
                    COALESCE(w.ghi, 400) as ghi,
                    COALESCE(w.temperature, 20) as temperature,
                    COALESCE(w.cloud_cover, 50) as cloud_cover
                FROM combined_data cd
                LEFT JOIN weather_data w ON DATE_TRUNC('hour', cd.timestamp) = w.hour_timestamp
                ORDER BY cd.timestamp, cd.system_id
                """
            
            df = pd.read_sql(query, conn)
            logger.info(f"✅ Loaded {len(df):,} records")
            
            return df
            
        except Exception as e:
            logger.error(f"❌ Data loading failed: {e}")
            return None
    
    def create_optimized_features(self, df):
        """Create optimized feature set"""
        logger.info("🔧 Creating optimized features...")
        
        # Sort by system and timestamp
        df = df.sort_values(['system_id', 'timestamp']).reset_index(drop=True)
        
        # 1. TEMPORAL FEATURES (Optimized)
        df['hour'] = df['timestamp'].dt.hour
        df['month'] = df['timestamp'].dt.month
        df['day_of_year'] = df['timestamp'].dt.dayofyear
        
        # Cyclical encoding (proven effective)
        df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
        df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
        df['day_sin'] = np.sin(2 * np.pi * df['day_of_year'] / 365)
        df['day_cos'] = np.cos(2 * np.pi * df['day_of_year'] / 365)
        
        # Time indicators
        df['is_daylight'] = df['hour'].between(6, 20).astype(int)
        df['is_peak_solar'] = df['hour'].between(10, 16).astype(int)
        df['is_evening'] = df['hour'].between(18, 22).astype(int)
        
        # 2. SYSTEM FEATURES (Critical)
        df['system_1'] = (df['system_id'] == 1).astype(int)
        df['system_2'] = (df['system_id'] == 2).astype(int)
        
        # 3. BATTERY FEATURES (High Impact)
        df['soc_normalized'] = df['soc'] / 100
        df['battery_mode'] = np.sign(df['bat_power'])
        df['is_charging'] = (df['bat_power'] > 100).astype(int)
        df['is_discharging'] = (df['bat_power'] < -100).astype(int)
        df['battery_utilization'] = np.abs(df['bat_power']) / 6000
        
        # 4. PRODUCTION FEATURES
        df['dc_total'] = df['powerdc1'] + df['powerdc2']
        df['efficiency'] = np.where(df['dc_total'] > 0, df['ac_power'] / df['dc_total'], 0)
        df['efficiency_clipped'] = np.clip(df['efficiency'], 0, 1.2)
        
        # 5. WEATHER FEATURES (Normalized)
        df['ghi_norm'] = df['ghi'] / 1000
        df['temp_norm'] = (df['temperature'] - 20) / 30
        df['cloud_norm'] = df['cloud_cover'] / 100
        
        # Weather efficiency
        df['weather_efficiency'] = df['ghi_norm'] * (1 - df['cloud_norm'] * 0.5)
        df['temp_efficiency'] = 1 - (df['temperature'] - 25) * 0.004
        df['temp_efficiency'] = np.clip(df['temp_efficiency'], 0.7, 1.1)
        
        # 6. INTERACTION FEATURES (Selected)
        df['battery_weather'] = df['soc_normalized'] * df['weather_efficiency']
        df['system_weather'] = df['system_id'] * df['weather_efficiency']
        df['time_battery'] = df['hour_sin'] * df['soc_normalized']
        
        # Define optimized feature set
        feature_columns = [
            # System identification (Critical)
            'system_1', 'system_2',
            
            # Temporal features (High impact)
            'hour_sin', 'hour_cos', 'month_sin', 'month_cos', 'day_sin', 'day_cos',
            'is_daylight', 'is_peak_solar', 'is_evening',
            
            # Battery features (High impact)
            'soc_normalized', 'battery_mode', 'is_charging', 'is_discharging', 'battery_utilization',
            
            # Production features
            'efficiency_clipped',
            
            # Weather features
            'ghi_norm', 'temp_norm', 'cloud_norm', 'weather_efficiency', 'temp_efficiency',
            
            # Interaction features
            'battery_weather', 'system_weather', 'time_battery'
        ]
        
        logger.info(f"✅ Created {len(feature_columns)} optimized features")
        return df, feature_columns
    
    def test_normalization_strategies(self, X, y):
        """Test different normalization strategies"""
        logger.info("🧪 PHASE 2: NORMALIZATION STRATEGY TESTING")
        logger.info("=" * 60)
        
        strategies = {
            'minmax': MinMaxScaler(),
            'standard': StandardScaler(),
            'robust': RobustScaler()
        }
        
        normalization_results = {}
        
        # Time-based split
        split_idx = int(len(X) * 0.8)
        X_train, X_test = X[:split_idx], X[split_idx:]
        y_train, y_test = y[:split_idx], y[split_idx:]
        
        for strategy_name, scaler in strategies.items():
            logger.info(f"Testing {strategy_name} normalization...")
            
            try:
                # Fit and transform
                X_train_scaled = scaler.fit_transform(X_train)
                X_test_scaled = scaler.transform(X_test)
                
                # Quick LightGBM test
                model = lgb.LGBMRegressor(
                    n_estimators=100,
                    random_state=42,
                    verbose=-1
                )
                model.fit(X_train_scaled, y_train)
                y_pred = model.predict(X_test_scaled)
                
                r2 = r2_score(y_test, y_pred)
                rmse = np.sqrt(mean_squared_error(y_test, y_pred))
                
                normalization_results[strategy_name] = {
                    'r2': r2,
                    'rmse': rmse,
                    'scaler': scaler
                }
                
                logger.info(f"   {strategy_name}: R² = {r2:.3f}, RMSE = {rmse:.1f}W")
                
            except Exception as e:
                logger.error(f"   {strategy_name} failed: {e}")
        
        # Find best strategy
        best_strategy = max(normalization_results.items(), key=lambda x: x[1]['r2'])
        logger.info(f"🏆 Best normalization: {best_strategy[0]} (R² = {best_strategy[1]['r2']:.3f})")
        
        self.optimization_results['normalization_testing'] = normalization_results
        return best_strategy[0], best_strategy[1]['scaler']
    
    def comprehensive_algorithm_testing(self, X_train, X_test, y_train, y_test):
        """Test multiple algorithms comprehensively"""
        logger.info("🤖 PHASE 3: COMPREHENSIVE ALGORITHM TESTING")
        logger.info("=" * 60)
        
        algorithms = {
            'lightgbm': lgb.LGBMRegressor(
                n_estimators=200,
                max_depth=12,
                learning_rate=0.08,
                subsample=0.9,
                colsample_bytree=0.9,
                random_state=42,
                verbose=-1
            ),
            'xgboost': xgb.XGBRegressor(
                n_estimators=200,
                max_depth=10,
                learning_rate=0.08,
                subsample=0.9,
                colsample_bytree=0.9,
                random_state=42,
                n_jobs=-1
            ),
            'random_forest': RandomForestRegressor(
                n_estimators=200,
                max_depth=20,
                min_samples_split=3,
                min_samples_leaf=1,
                random_state=42,
                n_jobs=-1
            )
        }
        
        algorithm_results = {}
        trained_models = {}
        
        for name, algorithm in algorithms.items():
            logger.info(f"Training {name}...")
            
            try:
                # Train
                algorithm.fit(X_train, y_train)
                
                # Predict
                y_pred_train = algorithm.predict(X_train)
                y_pred_test = algorithm.predict(X_test)
                
                # Evaluate
                train_r2 = r2_score(y_train, y_pred_train)
                test_r2 = r2_score(y_test, y_pred_test)
                test_rmse = np.sqrt(mean_squared_error(y_test, y_pred_test))
                test_mae = mean_absolute_error(y_test, y_pred_test)
                
                algorithm_results[name] = {
                    'train_r2': train_r2,
                    'test_r2': test_r2,
                    'test_rmse': test_rmse,
                    'test_mae': test_mae,
                    'overfitting': train_r2 - test_r2
                }
                
                trained_models[name] = algorithm
                
                logger.info(f"   {name}: R² = {test_r2:.3f}, RMSE = {test_rmse:.1f}W, MAE = {test_mae:.1f}W")
                
            except Exception as e:
                logger.error(f"   {name} failed: {e}")
        
        # Create ensemble
        if len(trained_models) >= 2:
            logger.info("Creating ensemble...")
            
            ensemble = VotingRegressor([
                (name, model) for name, model in trained_models.items()
            ])
            ensemble.fit(X_train, y_train)
            
            y_pred_ensemble = ensemble.predict(X_test)
            ensemble_r2 = r2_score(y_test, y_pred_ensemble)
            ensemble_rmse = np.sqrt(mean_squared_error(y_test, y_pred_ensemble))
            ensemble_mae = mean_absolute_error(y_test, y_pred_ensemble)
            
            algorithm_results['ensemble'] = {
                'test_r2': ensemble_r2,
                'test_rmse': ensemble_rmse,
                'test_mae': ensemble_mae,
                'overfitting': 0  # Ensemble typically reduces overfitting
            }
            
            trained_models['ensemble'] = ensemble
            
            logger.info(f"   ensemble: R² = {ensemble_r2:.3f}, RMSE = {ensemble_rmse:.1f}W, MAE = {ensemble_mae:.1f}W")
        
        # Find best algorithm
        best_algorithm = max(algorithm_results.items(), key=lambda x: x[1]['test_r2'])
        logger.info(f"🏆 Best algorithm: {best_algorithm[0]} (R² = {best_algorithm[1]['test_r2']:.3f})")
        
        self.optimization_results['algorithm_comparison'] = algorithm_results
        return best_algorithm[0], trained_models[best_algorithm[0]], trained_models
    
    def run_complete_optimization(self):
        """Run complete system optimization"""
        logger.info("🚀 COMPLETE SYSTEM OPTIMIZATION")
        logger.info("=" * 80)
        logger.info("🎯 Finding optimal combination of data, features, normalization, and algorithms")
        
        try:
            # Connect to database
            conn = self.connect_database()
            if not conn:
                return False
            
            # Phase 1: Data Quality Analysis
            data_quality = self.analyze_data_quality(conn)
            
            # Load and prepare data
            df = self.load_and_prepare_data(conn)
            if df is None or len(df) == 0:
                logger.error("❌ No data loaded")
                return False
            
            # Create optimized features
            df_features, feature_columns = self.create_optimized_features(df)
            
            # Prepare training data
            X = df_features[feature_columns].fillna(0)
            y = df_features['ac_power']
            
            logger.info(f"📊 Training data: {len(X):,} samples, {len(feature_columns)} features")
            
            # Time-based split
            split_idx = int(len(X) * 0.8)
            X_train, X_test = X[:split_idx], X[split_idx:]
            y_train, y_test = y[:split_idx], y[split_idx:]
            
            # Phase 2: Normalization testing
            best_norm_strategy, best_scaler = self.test_normalization_strategies(X, y)
            
            # Apply best normalization
            X_train_scaled = best_scaler.fit_transform(X_train)
            X_test_scaled = best_scaler.transform(X_test)
            
            # Phase 3: Algorithm testing
            best_algorithm_name, best_model, all_models = self.comprehensive_algorithm_testing(
                X_train_scaled, X_test_scaled, y_train, y_test
            )
            
            conn.close()
            
            # Save optimization results
            self.save_optimization_results(
                best_model, best_scaler, feature_columns, 
                best_algorithm_name, best_norm_strategy
            )
            
            # Display final results
            self.display_optimization_summary()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Complete optimization failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def save_optimization_results(self, model, scaler, features, algorithm_name, norm_strategy):
        """Save optimization results"""
        logger.info("💾 Saving optimization results...")
        
        # Save model
        model_path = self.models_dir / f"optimized_model_{algorithm_name}.joblib"
        joblib.dump(model, model_path)
        
        # Save scaler
        scaler_path = self.models_dir / f"optimized_scaler_{norm_strategy}.joblib"
        joblib.dump(scaler, scaler_path)
        
        # Save features
        features_path = self.models_dir / "optimized_features.json"
        with open(features_path, 'w') as f:
            json.dump(features, f, indent=2)
        
        # Save complete results
        results_path = self.models_dir / "optimization_results.json"
        with open(results_path, 'w') as f:
            json.dump(self.optimization_results, f, indent=2, default=str)
        
        logger.info(f"✅ Results saved to {self.models_dir}")
    
    def display_optimization_summary(self):
        """Display optimization summary"""
        logger.info("\n" + "=" * 80)
        logger.info("🎉 OPTIMIZATION COMPLETE - SUMMARY")
        logger.info("=" * 80)
        
        # Data quality summary
        if 'data_quality_analysis' in self.optimization_results:
            logger.info("📊 Data Quality:")
            for system, quality in self.optimization_results['data_quality_analysis'].items():
                logger.info(f"   {system.upper()}: {quality['quality_score']:.1f}/100 ({quality['total_records']:,} records)")
        
        # Best normalization
        if 'normalization_testing' in self.optimization_results:
            best_norm = max(self.optimization_results['normalization_testing'].items(), 
                          key=lambda x: x[1]['r2'])
            logger.info(f"🔧 Best Normalization: {best_norm[0]} (R² = {best_norm[1]['r2']:.3f})")
        
        # Best algorithm
        if 'algorithm_comparison' in self.optimization_results:
            best_algo = max(self.optimization_results['algorithm_comparison'].items(), 
                          key=lambda x: x[1]['test_r2'])
            logger.info(f"🤖 Best Algorithm: {best_algo[0]} (R² = {best_algo[1]['test_r2']:.3f})")
            logger.info(f"   RMSE: {best_algo[1]['test_rmse']:.1f}W")
            logger.info(f"   MAE: {best_algo[1]['test_mae']:.1f}W")
        
        logger.info("🚀 SYSTEM OPTIMIZED AND READY FOR PRODUCTION!")
        logger.info("=" * 80)

def main():
    """Execute complete system optimization"""
    optimizer = CompleteSystemOptimizer()
    success = optimizer.run_complete_optimization()
    
    if success:
        print("\n🎯 Complete system optimization completed successfully!")
        return True
    else:
        print("\n❌ Complete system optimization failed!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
