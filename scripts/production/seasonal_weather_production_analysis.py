#!/usr/bin/env python3
"""
Seasonal Weather & Production Analysis
Compare production for similar weather conditions across different time periods
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import pandas as pd
import numpy as np
import psycopg2
import json
from datetime import datetime, timedelta
import logging
from pathlib import Path
from dotenv import load_dotenv

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SeasonalWeatherProductionAnalyzer:
    """Analyze production patterns for similar weather conditions"""
    
    def __init__(self):
        load_dotenv()
        self.project_root = Path("/home/<USER>/solar-prediction-project")
        
        # Current predictions from optimized model
        self.current_predictions = {
            '2025-06-03': {'system1': 54.8, 'system2': 54.8},
            '2025-06-04': {'system1': 54.7, 'system2': 54.8}
        }
        
    def connect_database(self):
        """Connect to database"""
        try:
            conn = psycopg2.connect(
                host=os.getenv('DB_HOST', 'localhost'),
                database=os.getenv('DB_NAME', 'solar_prediction'),
                user=os.getenv('DB_USER', 'postgres'),
                password=os.getenv('DB_PASSWORD', 'postgres')
            )
            return conn
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            return None
    
    def get_last_week_production(self, conn):
        """Get last week's production data with weather"""
        logger.info("📊 ANALYZING LAST WEEK'S PRODUCTION")
        logger.info("=" * 60)
        
        try:
            # Get last 7 days of data
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=7)
            
            query = """
            WITH system1_daily AS (
                SELECT 
                    DATE(timestamp) as date,
                    'system1' as system,
                    MAX(yield_today) as daily_yield,
                    AVG(ac_power) as avg_ac_power,
                    MAX(ac_power) as max_ac_power,
                    COUNT(*) as records
                FROM solax_data
                WHERE DATE(timestamp) BETWEEN %s AND %s
                AND yield_today > 0
                GROUP BY DATE(timestamp)
                HAVING COUNT(*) > 100
            ),
            system2_daily AS (
                SELECT 
                    DATE(timestamp) as date,
                    'system2' as system,
                    MAX(yield_today) as daily_yield,
                    AVG(ac_power) as avg_ac_power,
                    MAX(ac_power) as max_ac_power,
                    COUNT(*) as records
                FROM solax_data2
                WHERE DATE(timestamp) BETWEEN %s AND %s
                AND yield_today > 0
                GROUP BY DATE(timestamp)
                HAVING COUNT(*) > 100
            ),
            combined_daily AS (
                SELECT * FROM system1_daily
                UNION ALL
                SELECT * FROM system2_daily
            ),
            weather_daily AS (
                SELECT
                    DATE(timestamp) as date,
                    AVG(COALESCE(ghi, 400)) as avg_ghi,
                    MAX(COALESCE(ghi, 400)) as max_ghi,
                    AVG(COALESCE(temperature, 20)) as avg_temperature,
                    MIN(COALESCE(temperature, 20)) as min_temperature,
                    MAX(COALESCE(temperature, 20)) as max_temperature,
                    AVG(COALESCE(cloud_cover, 50)) as avg_cloud_cover
                FROM cams_radiation_data
                WHERE DATE(timestamp) BETWEEN %s AND %s
                GROUP BY DATE(timestamp)
            )
            SELECT
                cd.*,
                COALESCE(w.avg_ghi, 400) as avg_ghi,
                COALESCE(w.max_ghi, 600) as max_ghi,
                COALESCE(w.avg_temperature, 20) as avg_temperature,
                COALESCE(w.min_temperature, 15) as min_temperature,
                COALESCE(w.max_temperature, 25) as max_temperature,
                COALESCE(w.avg_cloud_cover, 50) as avg_cloud_cover
            FROM combined_daily cd
            LEFT JOIN weather_daily w ON cd.date = w.date
            ORDER BY cd.date, cd.system
            """
            
            df = pd.read_sql(query, conn, params=[start_date, end_date, start_date, end_date, start_date, end_date])
            
            if len(df) == 0:
                logger.warning("⚠️ No data found for last week")
                return None
            
            # Analyze by system
            last_week_analysis = {}
            
            for system in ['system1', 'system2']:
                system_data = df[df['system'] == system]
                
                if len(system_data) > 0:
                    last_week_analysis[system] = {
                        'days_analyzed': len(system_data),
                        'avg_daily_yield': system_data['daily_yield'].mean(),
                        'min_daily_yield': system_data['daily_yield'].min(),
                        'max_daily_yield': system_data['daily_yield'].max(),
                        'total_yield': system_data['daily_yield'].sum(),
                        'avg_weather': {
                            'avg_ghi': system_data['avg_ghi'].mean(),
                            'max_ghi': system_data['max_ghi'].mean(),
                            'avg_temperature': system_data['avg_temperature'].mean(),
                            'min_temperature': system_data['min_temperature'].mean(),
                            'max_temperature': system_data['max_temperature'].mean(),
                            'avg_cloud_cover': system_data['avg_cloud_cover'].mean()
                        },
                        'daily_details': []
                    }
                    
                    # Add daily details
                    for _, row in system_data.iterrows():
                        last_week_analysis[system]['daily_details'].append({
                            'date': str(row['date']),
                            'yield_kwh': row['daily_yield'],
                            'avg_ghi': row['avg_ghi'],
                            'avg_temp': row['avg_temperature'],
                            'cloud_cover': row['avg_cloud_cover']
                        })
                    
                    logger.info(f"✅ {system.upper()}: {len(system_data)} days")
                    logger.info(f"   Average yield: {system_data['daily_yield'].mean():.1f} kWh")
                    logger.info(f"   Range: {system_data['daily_yield'].min():.1f} - {system_data['daily_yield'].max():.1f} kWh")
                    logger.info(f"   Weather: GHI {system_data['avg_ghi'].mean():.0f} W/m², Temp {system_data['avg_temperature'].mean():.1f}°C")
            
            return last_week_analysis
            
        except Exception as e:
            logger.error(f"❌ Last week analysis failed: {e}")
            return None
    
    def get_same_period_last_year(self, conn):
        """Get same period last year (June 2024)"""
        logger.info("📅 ANALYZING SAME PERIOD LAST YEAR (JUNE 2024)")
        logger.info("=" * 60)
        
        try:
            # June 2024 data
            query = """
            WITH system1_june2024 AS (
                SELECT 
                    DATE(timestamp) as date,
                    'system1' as system,
                    MAX(yield_today) as daily_yield,
                    AVG(ac_power) as avg_ac_power,
                    MAX(ac_power) as max_ac_power,
                    COUNT(*) as records
                FROM solax_data
                WHERE timestamp >= '2024-06-01' AND timestamp < '2024-07-01'
                AND yield_today > 0
                GROUP BY DATE(timestamp)
                HAVING COUNT(*) > 100
            ),
            system2_june2024 AS (
                SELECT 
                    DATE(timestamp) as date,
                    'system2' as system,
                    MAX(yield_today) as daily_yield,
                    AVG(ac_power) as avg_ac_power,
                    MAX(ac_power) as max_ac_power,
                    COUNT(*) as records
                FROM solax_data2
                WHERE timestamp >= '2024-06-01' AND timestamp < '2024-07-01'
                AND yield_today > 0
                GROUP BY DATE(timestamp)
                HAVING COUNT(*) > 100
            ),
            combined_june2024 AS (
                SELECT * FROM system1_june2024
                UNION ALL
                SELECT * FROM system2_june2024
            ),
            weather_june2024 AS (
                SELECT
                    DATE(timestamp) as date,
                    AVG(COALESCE(ghi, 400)) as avg_ghi,
                    MAX(COALESCE(ghi, 400)) as max_ghi,
                    AVG(COALESCE(temperature, 20)) as avg_temperature,
                    MIN(COALESCE(temperature, 20)) as min_temperature,
                    MAX(COALESCE(temperature, 20)) as max_temperature,
                    AVG(COALESCE(cloud_cover, 50)) as avg_cloud_cover
                FROM cams_radiation_data
                WHERE timestamp >= '2024-06-01' AND timestamp < '2024-07-01'
                GROUP BY DATE(timestamp)
            )
            SELECT
                cd.*,
                COALESCE(w.avg_ghi, 400) as avg_ghi,
                COALESCE(w.max_ghi, 600) as max_ghi,
                COALESCE(w.avg_temperature, 20) as avg_temperature,
                COALESCE(w.min_temperature, 15) as min_temperature,
                COALESCE(w.max_temperature, 25) as max_temperature,
                COALESCE(w.avg_cloud_cover, 50) as avg_cloud_cover
            FROM combined_june2024 cd
            LEFT JOIN weather_june2024 w ON cd.date = w.date
            ORDER BY cd.date, cd.system
            """
            
            df = pd.read_sql(query, conn)
            
            if len(df) == 0:
                logger.warning("⚠️ No data found for June 2024")
                return None
            
            # Analyze by system
            june2024_analysis = {}
            
            for system in ['system1', 'system2']:
                system_data = df[df['system'] == system]
                
                if len(system_data) > 0:
                    june2024_analysis[system] = {
                        'days_analyzed': len(system_data),
                        'avg_daily_yield': system_data['daily_yield'].mean(),
                        'min_daily_yield': system_data['daily_yield'].min(),
                        'max_daily_yield': system_data['daily_yield'].max(),
                        'total_yield': system_data['daily_yield'].sum(),
                        'avg_weather': {
                            'avg_ghi': system_data['avg_ghi'].mean(),
                            'max_ghi': system_data['max_ghi'].mean(),
                            'avg_temperature': system_data['avg_temperature'].mean(),
                            'min_temperature': system_data['min_temperature'].mean(),
                            'max_temperature': system_data['max_temperature'].mean(),
                            'avg_cloud_cover': system_data['avg_cloud_cover'].mean()
                        }
                    }
                    
                    logger.info(f"✅ {system.upper()}: {len(system_data)} days")
                    logger.info(f"   Average yield: {system_data['daily_yield'].mean():.1f} kWh")
                    logger.info(f"   Range: {system_data['daily_yield'].min():.1f} - {system_data['daily_yield'].max():.1f} kWh")
                    logger.info(f"   Weather: GHI {system_data['avg_ghi'].mean():.0f} W/m², Temp {system_data['avg_temperature'].mean():.1f}°C")
            
            return june2024_analysis
            
        except Exception as e:
            logger.error(f"❌ June 2024 analysis failed: {e}")
            return None
    
    def find_similar_weather_days(self, conn):
        """Find days with similar weather to current predictions"""
        logger.info("🌤️ FINDING SIMILAR WEATHER DAYS")
        logger.info("=" * 60)
        
        try:
            # Define target weather conditions (based on prediction assumptions)
            target_weather = {
                'avg_ghi': 650,  # Good summer day
                'avg_temperature': 28,  # Summer temperature
                'avg_cloud_cover': 25  # Mostly clear
            }
            
            # Find similar days in historical data
            query = """
            WITH daily_weather AS (
                SELECT
                    DATE(timestamp) as date,
                    AVG(COALESCE(ghi, 400)) as avg_ghi,
                    AVG(COALESCE(temperature, 20)) as avg_temperature,
                    AVG(COALESCE(cloud_cover, 50)) as avg_cloud_cover
                FROM cams_radiation_data
                WHERE timestamp >= '2024-03-01'
                GROUP BY DATE(timestamp)
            ),
            system1_daily AS (
                SELECT 
                    DATE(timestamp) as date,
                    'system1' as system,
                    MAX(yield_today) as daily_yield
                FROM solax_data
                WHERE timestamp >= '2024-03-01'
                AND yield_today > 0
                GROUP BY DATE(timestamp)
                HAVING COUNT(*) > 100
            ),
            system2_daily AS (
                SELECT 
                    DATE(timestamp) as date,
                    'system2' as system,
                    MAX(yield_today) as daily_yield
                FROM solax_data2
                WHERE timestamp >= '2024-03-01'
                AND yield_today > 0
                GROUP BY DATE(timestamp)
                HAVING COUNT(*) > 100
            ),
            combined_daily AS (
                SELECT * FROM system1_daily
                UNION ALL
                SELECT * FROM system2_daily
            )
            SELECT
                cd.*,
                w.avg_ghi,
                w.avg_temperature,
                w.avg_cloud_cover,
                ABS(w.avg_ghi - %s) as ghi_diff,
                ABS(w.avg_temperature - %s) as temp_diff,
                ABS(w.avg_cloud_cover - %s) as cloud_diff
            FROM combined_daily cd
            JOIN daily_weather w ON cd.date = w.date
            WHERE ABS(w.avg_ghi - %s) < 150
            AND ABS(w.avg_temperature - %s) < 5
            AND ABS(w.avg_cloud_cover - %s) < 20
            ORDER BY (ABS(w.avg_ghi - %s) + ABS(w.avg_temperature - %s) + ABS(w.avg_cloud_cover - %s))
            """
            
            params = [
                target_weather['avg_ghi'], target_weather['avg_temperature'], target_weather['avg_cloud_cover'],
                target_weather['avg_ghi'], target_weather['avg_temperature'], target_weather['avg_cloud_cover'],
                target_weather['avg_ghi'], target_weather['avg_temperature'], target_weather['avg_cloud_cover']
            ]
            
            df = pd.read_sql(query, conn, params=params)
            
            if len(df) == 0:
                logger.warning("⚠️ No similar weather days found")
                return None
            
            # Analyze similar weather days
            similar_weather_analysis = {}
            
            for system in ['system1', 'system2']:
                system_data = df[df['system'] == system].head(10)  # Top 10 most similar days
                
                if len(system_data) > 0:
                    similar_weather_analysis[system] = {
                        'similar_days_found': len(system_data),
                        'avg_yield_similar_weather': system_data['daily_yield'].mean(),
                        'min_yield_similar_weather': system_data['daily_yield'].min(),
                        'max_yield_similar_weather': system_data['daily_yield'].max(),
                        'target_weather': target_weather,
                        'actual_weather_range': {
                            'ghi_range': (system_data['avg_ghi'].min(), system_data['avg_ghi'].max()),
                            'temp_range': (system_data['avg_temperature'].min(), system_data['avg_temperature'].max()),
                            'cloud_range': (system_data['avg_cloud_cover'].min(), system_data['avg_cloud_cover'].max())
                        },
                        'sample_days': []
                    }
                    
                    # Add sample days
                    for _, row in system_data.head(5).iterrows():
                        similar_weather_analysis[system]['sample_days'].append({
                            'date': str(row['date']),
                            'yield_kwh': row['daily_yield'],
                            'ghi': row['avg_ghi'],
                            'temperature': row['avg_temperature'],
                            'cloud_cover': row['avg_cloud_cover']
                        })
                    
                    logger.info(f"✅ {system.upper()}: {len(system_data)} similar days found")
                    logger.info(f"   Average yield: {system_data['daily_yield'].mean():.1f} kWh")
                    logger.info(f"   Range: {system_data['daily_yield'].min():.1f} - {system_data['daily_yield'].max():.1f} kWh")
            
            return similar_weather_analysis
            
        except Exception as e:
            logger.error(f"❌ Similar weather analysis failed: {e}")
            return None
    
    def compare_predictions_with_historical(self, last_week, june2024, similar_weather):
        """Compare current predictions with historical data"""
        logger.info("🔍 COMPARING PREDICTIONS WITH HISTORICAL DATA")
        logger.info("=" * 60)
        
        comparison_results = {}
        
        for system in ['system1', 'system2']:
            system_key = system
            predicted_avg = (self.current_predictions['2025-06-03'][system] + 
                           self.current_predictions['2025-06-04'][system]) / 2
            
            comparison_results[system] = {
                'current_prediction_avg': predicted_avg,
                'comparisons': {}
            }
            
            # Compare with last week
            if last_week and system in last_week:
                last_week_avg = last_week[system]['avg_daily_yield']
                diff_last_week = predicted_avg - last_week_avg
                accuracy_last_week = (1 - abs(diff_last_week) / last_week_avg) * 100
                
                comparison_results[system]['comparisons']['last_week'] = {
                    'historical_avg': last_week_avg,
                    'difference': diff_last_week,
                    'accuracy_percent': accuracy_last_week,
                    'status': 'Higher' if diff_last_week > 0 else 'Lower'
                }
                
                logger.info(f"📊 {system.upper()} vs Last Week:")
                logger.info(f"   Predicted: {predicted_avg:.1f} kWh")
                logger.info(f"   Last week avg: {last_week_avg:.1f} kWh")
                logger.info(f"   Difference: {diff_last_week:+.1f} kWh ({accuracy_last_week:.1f}% accuracy)")
            
            # Compare with June 2024
            if june2024 and system in june2024:
                june2024_avg = june2024[system]['avg_daily_yield']
                diff_june2024 = predicted_avg - june2024_avg
                accuracy_june2024 = (1 - abs(diff_june2024) / june2024_avg) * 100
                
                comparison_results[system]['comparisons']['june_2024'] = {
                    'historical_avg': june2024_avg,
                    'difference': diff_june2024,
                    'accuracy_percent': accuracy_june2024,
                    'status': 'Higher' if diff_june2024 > 0 else 'Lower'
                }
                
                logger.info(f"📊 {system.upper()} vs June 2024:")
                logger.info(f"   Predicted: {predicted_avg:.1f} kWh")
                logger.info(f"   June 2024 avg: {june2024_avg:.1f} kWh")
                logger.info(f"   Difference: {diff_june2024:+.1f} kWh ({accuracy_june2024:.1f}% accuracy)")
            
            # Compare with similar weather
            if similar_weather and system in similar_weather:
                similar_avg = similar_weather[system]['avg_yield_similar_weather']
                diff_similar = predicted_avg - similar_avg
                accuracy_similar = (1 - abs(diff_similar) / similar_avg) * 100
                
                comparison_results[system]['comparisons']['similar_weather'] = {
                    'historical_avg': similar_avg,
                    'difference': diff_similar,
                    'accuracy_percent': accuracy_similar,
                    'status': 'Higher' if diff_similar > 0 else 'Lower',
                    'sample_size': similar_weather[system]['similar_days_found']
                }
                
                logger.info(f"📊 {system.upper()} vs Similar Weather:")
                logger.info(f"   Predicted: {predicted_avg:.1f} kWh")
                logger.info(f"   Similar weather avg: {similar_avg:.1f} kWh ({similar_weather[system]['similar_days_found']} days)")
                logger.info(f"   Difference: {diff_similar:+.1f} kWh ({accuracy_similar:.1f}% accuracy)")
        
        return comparison_results
    
    def run_seasonal_analysis(self):
        """Run complete seasonal weather production analysis"""
        logger.info("🚀 SEASONAL WEATHER & PRODUCTION ANALYSIS")
        logger.info("=" * 80)
        logger.info("🎯 Comparing predictions with historical data for similar weather")
        
        try:
            conn = self.connect_database()
            if not conn:
                return False
            
            # Get last week's data
            last_week_data = self.get_last_week_production(conn)
            
            # Get same period last year
            june2024_data = self.get_same_period_last_year(conn)
            
            # Find similar weather days
            similar_weather_data = self.find_similar_weather_days(conn)
            
            # Compare predictions with historical data
            comparison_results = self.compare_predictions_with_historical(
                last_week_data, june2024_data, similar_weather_data
            )
            
            conn.close()
            
            # Compile complete results
            complete_analysis = {
                'analysis_date': datetime.now().isoformat(),
                'current_predictions': self.current_predictions,
                'last_week_analysis': last_week_data,
                'june_2024_analysis': june2024_data,
                'similar_weather_analysis': similar_weather_data,
                'prediction_comparison': comparison_results,
                'summary': self.generate_analysis_summary(comparison_results)
            }
            
            # Save results
            results_file = f"test/results/seasonal_weather_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            os.makedirs(os.path.dirname(results_file), exist_ok=True)
            
            with open(results_file, 'w') as f:
                json.dump(complete_analysis, f, indent=2, default=str)
            
            # Display summary
            self.display_analysis_summary(complete_analysis)
            
            logger.info(f"\n💾 Analysis results saved: {results_file}")
            logger.info("=" * 80)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Seasonal analysis failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def generate_analysis_summary(self, comparison_results):
        """Generate analysis summary"""
        summary = {
            'prediction_accuracy': {},
            'key_findings': [],
            'recommendations': []
        }
        
        # Calculate average accuracy across all comparisons
        total_accuracy = 0
        count = 0
        
        for system, data in comparison_results.items():
            system_accuracies = []
            for comparison_type, comparison_data in data['comparisons'].items():
                accuracy = comparison_data['accuracy_percent']
                system_accuracies.append(accuracy)
                total_accuracy += accuracy
                count += 1
            
            if system_accuracies:
                summary['prediction_accuracy'][system] = {
                    'avg_accuracy': np.mean(system_accuracies),
                    'best_comparison': max(data['comparisons'].items(), key=lambda x: x[1]['accuracy_percent'])[0],
                    'worst_comparison': min(data['comparisons'].items(), key=lambda x: x[1]['accuracy_percent'])[0]
                }
        
        if count > 0:
            summary['overall_accuracy'] = total_accuracy / count
        
        # Generate findings and recommendations
        if summary.get('overall_accuracy', 0) > 80:
            summary['key_findings'].append("Predictions are highly accurate compared to historical data")
        elif summary.get('overall_accuracy', 0) > 60:
            summary['key_findings'].append("Predictions show reasonable accuracy but have room for improvement")
        else:
            summary['key_findings'].append("Predictions show significant deviation from historical patterns")
        
        summary['recommendations'] = [
            "Use similar weather historical data for validation",
            "Consider seasonal adjustments to prediction model",
            "Monitor actual results to improve future predictions"
        ]
        
        return summary
    
    def display_analysis_summary(self, analysis):
        """Display analysis summary"""
        logger.info("\n📊 SEASONAL ANALYSIS SUMMARY:")
        
        if 'summary' in analysis and 'overall_accuracy' in analysis['summary']:
            accuracy = analysis['summary']['overall_accuracy']
            logger.info(f"   Overall Prediction Accuracy: {accuracy:.1f}%")
        
        logger.info("\n🔮 CURRENT PREDICTIONS:")
        for date, systems in self.current_predictions.items():
            logger.info(f"   {date}:")
            for system, prediction in systems.items():
                logger.info(f"     {system.upper()}: {prediction:.1f} kWh")
        
        if 'prediction_comparison' in analysis:
            logger.info("\n📈 COMPARISON WITH HISTORICAL DATA:")
            for system, data in analysis['prediction_comparison'].items():
                logger.info(f"   {system.upper()}:")
                for comparison_type, comparison_data in data['comparisons'].items():
                    logger.info(f"     vs {comparison_type}: {comparison_data['accuracy_percent']:.1f}% accuracy")

def main():
    """Execute seasonal weather production analysis"""
    analyzer = SeasonalWeatherProductionAnalyzer()
    success = analyzer.run_seasonal_analysis()
    
    if success:
        print("\n🎯 Seasonal weather production analysis completed!")
        return True
    else:
        print("\n❌ Seasonal weather production analysis failed!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
