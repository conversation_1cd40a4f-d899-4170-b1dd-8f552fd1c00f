#!/usr/bin/env python3
"""
DATABASE SETUP FOR UNIFIED PREDICTION SYSTEM
Setup predictions cache table and required indexes
Created: June 4, 2025
"""

import os
import sys
import psycopg2
import logging
from datetime import datetime
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

logger = logging.getLogger(__name__)

class DatabaseSetup:
    """
    Database setup for unified prediction system
    """
    
    def __init__(self, db_config=None):
        self.db_config = db_config or self._get_default_db_config()
        self.logger = logger
    
    def _get_default_db_config(self):
        """Get default database configuration"""
        return {
            "host": os.getenv("DB_HOST", "localhost"),
            "port": os.getenv("DB_PORT", "5432"),
            "database": os.getenv("DB_NAME", "solar_prediction"),
            "user": os.getenv("DB_USER", "postgres"),
            "password": os.getenv("DB_PASSWORD", "")
        }
    
    def _get_db_connection(self):
        """Get database connection"""
        return psycopg2.connect(**self.db_config)
    
    def test_connection(self):
        """Test database connection"""
        try:
            with self._get_db_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("SELECT version();")
                    version = cursor.fetchone()[0]
                    self.logger.info(f"Database connection successful: {version}")
                    return True
        except Exception as e:
            self.logger.error(f"Database connection failed: {e}")
            return False
    
    def create_predictions_cache_table(self):
        """Create predictions cache table"""
        try:
            with self._get_db_connection() as conn:
                with conn.cursor() as cursor:
                    # Create predictions cache table
                    cursor.execute("""
                        CREATE TABLE IF NOT EXISTS predictions_cache (
                            id SERIAL PRIMARY KEY,
                            system_id INTEGER NOT NULL,
                            timestamp TIMESTAMP NOT NULL,
                            prediction_type VARCHAR(20) NOT NULL,
                            prediction_value FLOAT NOT NULL,
                            confidence FLOAT,
                            model_used VARCHAR(50),
                            season VARCHAR(10),
                            weather_data JSONB,
                            created_at TIMESTAMP DEFAULT NOW(),
                            updated_at TIMESTAMP DEFAULT NOW(),
                            UNIQUE(system_id, timestamp, prediction_type)
                        );
                    """)
                    
                    self.logger.info("Predictions cache table created successfully")
                    conn.commit()
                    return True
                    
        except Exception as e:
            self.logger.error(f"Failed to create predictions cache table: {e}")
            return False
    
    def create_indexes(self):
        """Create performance indexes"""
        try:
            with self._get_db_connection() as conn:
                with conn.cursor() as cursor:
                    # Index for fast lookups by system and time
                    cursor.execute("""
                        CREATE INDEX IF NOT EXISTS idx_predictions_cache_system_time 
                        ON predictions_cache(system_id, timestamp, prediction_type);
                    """)
                    
                    # Index for cleanup operations
                    cursor.execute("""
                        CREATE INDEX IF NOT EXISTS idx_predictions_cache_created 
                        ON predictions_cache(created_at);
                    """)
                    
                    # Index for updated_at for freshness checks
                    cursor.execute("""
                        CREATE INDEX IF NOT EXISTS idx_predictions_cache_updated 
                        ON predictions_cache(updated_at);
                    """)
                    
                    # Index for model performance analysis
                    cursor.execute("""
                        CREATE INDEX IF NOT EXISTS idx_predictions_cache_model 
                        ON predictions_cache(model_used, season);
                    """)
                    
                    self.logger.info("Database indexes created successfully")
                    conn.commit()
                    return True
                    
        except Exception as e:
            self.logger.error(f"Failed to create indexes: {e}")
            return False
    
    def create_historical_data_api_view(self):
        """Create view for unified historical data access (skip if tables don't exist)"""
        try:
            with self._get_db_connection() as conn:
                with conn.cursor() as cursor:
                    # Check if required tables exist
                    cursor.execute("""
                        SELECT table_name
                        FROM information_schema.tables
                        WHERE table_schema = 'public'
                        AND table_name IN ('solax_data', 'solax_data2', 'weather_data');
                    """)

                    existing_tables = [row[0] for row in cursor.fetchall()]

                    if 'solax_data' in existing_tables and 'solax_data2' in existing_tables:
                        # Get column names for solax_data
                        cursor.execute("""
                            SELECT column_name
                            FROM information_schema.columns
                            WHERE table_name = 'solax_data';
                        """)

                        columns = [row[0] for row in cursor.fetchall()]

                        # Create unified historical data view with available columns
                        if 'timestamp' in columns:
                            yield_col = 'yield_today' if 'yield_today' in columns else 'NULL as yield_today'
                            soc_col = 'battery_soc' if 'battery_soc' in columns else 'soc' if 'soc' in columns else 'NULL as battery_soc'
                            power_col = 'ac_power' if 'ac_power' in columns else 'NULL as ac_power'

                            cursor.execute(f"""
                                CREATE OR REPLACE VIEW unified_historical_data AS
                                SELECT
                                    1 as system_id,
                                    timestamp,
                                    {yield_col},
                                    {soc_col},
                                    {power_col},
                                    'system1' as system_name
                                FROM solax_data
                                WHERE timestamp IS NOT NULL

                                UNION ALL

                                SELECT
                                    2 as system_id,
                                    timestamp,
                                    {yield_col},
                                    {soc_col},
                                    {power_col},
                                    'system2' as system_name
                                FROM solax_data2
                                WHERE timestamp IS NOT NULL

                                ORDER BY timestamp DESC;
                            """)

                            self.logger.info("Historical data view created successfully")
                    else:
                        self.logger.warning("SolaX data tables not found, skipping historical data view")

                    # Create weather data view if table exists
                    if 'weather_data' in existing_tables:
                        cursor.execute("""
                            CREATE OR REPLACE VIEW unified_weather_data AS
                            SELECT
                                timestamp,
                                temperature,
                                cloud_cover,
                                ghi,
                                'weather' as data_type
                            FROM weather_data
                            WHERE timestamp IS NOT NULL
                            ORDER BY timestamp DESC;
                        """)

                        self.logger.info("Weather data view created successfully")
                    else:
                        self.logger.warning("Weather data table not found, skipping weather data view")

                    conn.commit()
                    return True

        except Exception as e:
            self.logger.error(f"Failed to create historical data views: {e}")
            return False
    
    def setup_database(self):
        """Complete database setup"""
        self.logger.info("Starting database setup...")
        
        # Test connection
        if not self.test_connection():
            return False
        
        # Create tables
        if not self.create_predictions_cache_table():
            return False
        
        # Create indexes
        if not self.create_indexes():
            return False
        
        # Create views (optional - don't fail if it doesn't work)
        try:
            self.create_historical_data_api_view()
        except Exception as e:
            self.logger.warning(f"Views creation failed (optional): {e}")
        
        self.logger.info("Database setup completed successfully")
        return True
    
    def get_database_stats(self):
        """Get database statistics"""
        try:
            with self._get_db_connection() as conn:
                with conn.cursor() as cursor:
                    stats = {}
                    
                    # Check if tables exist
                    cursor.execute("""
                        SELECT table_name 
                        FROM information_schema.tables 
                        WHERE table_schema = 'public' 
                        AND table_name IN ('predictions_cache', 'solax_data', 'solax_data2', 'weather_data');
                    """)
                    
                    existing_tables = [row[0] for row in cursor.fetchall()]
                    stats['existing_tables'] = existing_tables
                    
                    # Get row counts
                    for table in existing_tables:
                        try:
                            cursor.execute(f"SELECT COUNT(*) FROM {table};")
                            count = cursor.fetchone()[0]
                            stats[f'{table}_count'] = count
                        except Exception as e:
                            stats[f'{table}_count'] = f"Error: {e}"
                    
                    # Check indexes
                    cursor.execute("""
                        SELECT indexname 
                        FROM pg_indexes 
                        WHERE tablename = 'predictions_cache';
                    """)
                    
                    indexes = [row[0] for row in cursor.fetchall()]
                    stats['predictions_cache_indexes'] = indexes
                    
                    # Check views
                    cursor.execute("""
                        SELECT table_name 
                        FROM information_schema.views 
                        WHERE table_schema = 'public' 
                        AND table_name IN ('unified_historical_data', 'unified_weather_data');
                    """)
                    
                    views = [row[0] for row in cursor.fetchall()]
                    stats['existing_views'] = views
                    
                    return stats
                    
        except Exception as e:
            self.logger.error(f"Failed to get database stats: {e}")
            return {"error": str(e)}
    
    def cleanup_old_predictions(self, days_to_keep=7):
        """Clean up old cached predictions"""
        try:
            with self._get_db_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("""
                        DELETE FROM predictions_cache 
                        WHERE created_at < NOW() - INTERVAL '%s days'
                    """, (days_to_keep,))
                    
                    deleted_count = cursor.rowcount
                    conn.commit()
                    
                    self.logger.info(f"Cleaned up {deleted_count} old predictions")
                    return deleted_count
                    
        except Exception as e:
            self.logger.error(f"Failed to cleanup old predictions: {e}")
            return 0

def main():
    """Main function"""
    logging.basicConfig(level=logging.INFO)
    
    db_setup = DatabaseSetup()
    
    print("🗄️  DATABASE SETUP FOR UNIFIED PREDICTION SYSTEM")
    print("=" * 60)
    
    # Setup database
    success = db_setup.setup_database()
    
    if success:
        print("✅ Database setup completed successfully!")
        
        # Show statistics
        print("\n📊 DATABASE STATISTICS:")
        print("=" * 40)
        stats = db_setup.get_database_stats()
        
        for key, value in stats.items():
            if isinstance(value, list):
                print(f"{key}: {', '.join(value) if value else 'None'}")
            else:
                print(f"{key}: {value}")
        
        print("\n🎯 NEXT STEPS:")
        print("1. Test predictions cache with: python3 scripts/production/predictions_cache_manager.py")
        print("2. Setup cron jobs with: ./scripts/automation/setup_monitoring_cron.sh")
        print("3. Start unified API: python3 api/unified_prediction_api.py")
        
    else:
        print("❌ Database setup failed!")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
