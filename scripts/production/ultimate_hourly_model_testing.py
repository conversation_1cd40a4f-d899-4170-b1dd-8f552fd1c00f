#!/usr/bin/env python3
"""
Ultimate Hourly Model Testing Framework
Test all available models for hourly predictions with real data targeting 95%+ accuracy
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import requests
import psycopg2
import pandas as pd
import numpy as np
import joblib
import json
import lightgbm as lgb
import xgboost as xgb
from pathlib import Path
from datetime import datetime, timedelta
import logging
from dotenv import load_dotenv
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler, RobustScaler
import warnings
warnings.filterwarnings('ignore')

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class UltimateHourlyModelTester:
    """Ultimate testing framework for hourly predictions with 95%+ accuracy target"""
    
    def __init__(self):
        load_dotenv()
        self.project_root = Path("/home/<USER>/solar-prediction-project")
        self.models_dir = self.project_root / "models"
        
        # Weather API configuration
        self.api_url = "https://api.open-meteo.com/v1/forecast"
        self.latitude = 38.141367951893024
        self.longitude = 24.00715534164505
        
        # Real data for validation (recent days)
        self.validation_dates = [
            '2025-06-01',  # Full day with known data
            '2025-05-31',  # Previous day
            '2025-05-30',  # Earlier day
        ]
        
        # Known real data for validation
        self.known_real_data = {
            '2025-06-01': {'system1': 72.8, 'system2': 67.7},
            '2025-06-02': {'system1': 31.8, 'system2': 34.0}  # partial day
        }
        
        # Testing results
        self.test_results = {}
        self.best_models = {}
        
    def connect_database(self):
        """Connect to database"""
        try:
            conn = psycopg2.connect(
                host=os.getenv('DB_HOST', 'localhost'),
                database=os.getenv('DB_NAME', 'solar_prediction'),
                user=os.getenv('DB_USER', 'postgres'),
                password=os.getenv('DB_PASSWORD', 'postgres')
            )
            return conn
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            return None
    
    def discover_all_available_models(self):
        """Discover all available models for testing"""
        logger.info("🔍 DISCOVERING ALL AVAILABLE MODELS")
        logger.info("=" * 70)
        
        discovered_models = {}
        
        # Define model categories and their patterns
        model_categories = {
            'production_optimized': {
                'path': 'production_optimized/daily_yield_model.joblib',
                'scaler': 'production_optimized/daily_yield_scaler.joblib',
                'features': 'production_optimized/daily_yield_features.json',
                'type': 'lightgbm_daily',
                'description': 'Production Optimized Daily Yield Model (89.4% accuracy)'
            },
            'final_solution': {
                'path': 'final_solution/daily_yield_model.joblib',
                'scaler': 'final_solution/daily_yield_scaler.joblib',
                'features': 'final_solution/daily_yield_features.json',
                'type': 'lightgbm_daily',
                'description': 'Final Solution Daily Yield Model'
            },
            'enhanced_v3_system1': {
                'path': 'enhanced_v3_separate_systems/system_1_model.joblib',
                'features': 'enhanced_v3_separate_systems/system_1_features.json',
                'type': 'ensemble',
                'description': 'Enhanced v3 System 1 Specific Model'
            },
            'enhanced_v3_system2': {
                'path': 'enhanced_v3_separate_systems/system_2_model.joblib',
                'features': 'enhanced_v3_separate_systems/system_2_features.json',
                'type': 'ensemble',
                'description': 'Enhanced v3 System 2 Specific Model'
            },
            'enhanced_v3_production': {
                'path': 'enhanced_v3_production/enhanced_model_v3_final.joblib',
                'features': 'enhanced_v3_production/feature_columns.json',
                'type': 'ensemble',
                'description': 'Enhanced v3 Production Model'
            },
            'enhanced_v2_lightgbm': {
                'path': 'enhanced_v2_all/lightgbm_model.txt',
                'features': 'enhanced_v2_all/feature_columns.json',
                'type': 'lightgbm',
                'description': 'Enhanced v2 LightGBM Model (97.8% R²)'
            },
            'enhanced_v2_xgboost': {
                'path': 'enhanced_v2_all/xgboost_model.json',
                'features': 'enhanced_v2_all/feature_columns.json',
                'type': 'xgboost',
                'description': 'Enhanced v2 XGBoost Model'
            },
            'optimized_ensemble': {
                'path': 'optimized_final/optimized_model_ensemble.joblib',
                'scaler': 'optimized_final/optimized_scaler_standard.joblib',
                'features': 'optimized_final/optimized_features.json',
                'type': 'ensemble',
                'description': 'Optimized Final Ensemble Model'
            }
        }
        
        # Check which models exist
        for model_name, model_info in model_categories.items():
            model_path = self.models_dir / model_info['path']
            
            if model_path.exists():
                # Check for additional files
                scaler_path = None
                features_path = None
                
                if 'scaler' in model_info:
                    scaler_path = self.models_dir / model_info['scaler']
                    if not scaler_path.exists():
                        scaler_path = None
                
                if 'features' in model_info:
                    features_path = self.models_dir / model_info['features']
                    if not features_path.exists():
                        features_path = None
                
                discovered_models[model_name] = {
                    'model_path': model_path,
                    'scaler_path': scaler_path,
                    'features_path': features_path,
                    'type': model_info['type'],
                    'description': model_info['description']
                }
                
                logger.info(f"   ✅ Found: {model_name} ({model_info['type']})")
            else:
                logger.warning(f"   ❌ Missing: {model_name} - {model_path}")
        
        logger.info(f"\n📊 Total models discovered: {len(discovered_models)}")
        return discovered_models
    
    def get_real_hourly_data(self, date_str, system_table):
        """Get real hourly solar data for specific date"""
        try:
            conn = self.connect_database()
            if not conn:
                return None
            
            cursor = conn.cursor()
            
            # Get hourly data for the specified date
            query = f"""
                SELECT 
                    DATE_TRUNC('hour', timestamp) as hour,
                    AVG(COALESCE(ac_power, 0)) as avg_ac_power,
                    AVG(COALESCE(soc, 75)) as avg_soc,
                    AVG(COALESCE(bat_power, 0)) as avg_bat_power,
                    AVG(COALESCE(powerdc1, 0)) as avg_powerdc1,
                    AVG(COALESCE(powerdc2, 0)) as avg_powerdc2,
                    MAX(COALESCE(yield_today, 0)) as max_yield_today,
                    COUNT(*) as record_count
                FROM {system_table}
                WHERE DATE(timestamp) = %s
                AND timestamp IS NOT NULL
                GROUP BY DATE_TRUNC('hour', timestamp)
                ORDER BY hour
            """
            
            cursor.execute(query, (date_str,))
            results = cursor.fetchall()
            conn.close()
            
            if results:
                df = pd.DataFrame(results, columns=[
                    'hour', 'avg_ac_power', 'avg_soc', 'avg_bat_power', 
                    'avg_powerdc1', 'avg_powerdc2', 'max_yield_today', 'record_count'
                ])
                df['hour'] = pd.to_datetime(df['hour'])
                return df
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Failed to get real hourly data: {e}")
            return None
    
    def get_real_weather_data_hourly(self, date_str):
        """Get real weather data for specific date"""
        try:
            conn = self.connect_database()
            if not conn:
                return None
            
            cursor = conn.cursor()
            
            # Get hourly weather data
            query = """
                SELECT 
                    DATE_TRUNC('hour', timestamp) as hour,
                    AVG(COALESCE(temperature, 25)) as avg_temperature,
                    AVG(COALESCE(cloud_cover, 30)) as avg_cloud_cover,
                    AVG(COALESCE(ghi, 650)) as avg_ghi,
                    AVG(COALESCE(dni, 520)) as avg_dni,
                    AVG(COALESCE(dhi, 130)) as avg_dhi,
                    COUNT(*) as record_count
                FROM cams_radiation_data
                WHERE DATE(timestamp) = %s
                GROUP BY DATE_TRUNC('hour', timestamp)
                ORDER BY hour
            """
            
            cursor.execute(query, (date_str,))
            results = cursor.fetchall()
            conn.close()
            
            if results:
                df = pd.DataFrame(results, columns=[
                    'hour', 'avg_temperature', 'avg_cloud_cover', 'avg_ghi', 
                    'avg_dni', 'avg_dhi', 'record_count'
                ])
                df['hour'] = pd.to_datetime(df['hour'])
                return df
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Failed to get weather data: {e}")
            return None
    
    def load_model_safely(self, model_info):
        """Safely load a model with error handling"""
        try:
            model_path = model_info['model_path']
            model_type = model_info['type']
            
            # Load main model
            if model_type == 'lightgbm' and model_path.suffix == '.txt':
                model = lgb.Booster(model_file=str(model_path))
            elif model_type == 'xgboost' and model_path.suffix == '.json':
                model = xgb.Booster()
                model.load_model(str(model_path))
            else:
                model = joblib.load(model_path)
            
            # Load scaler if available
            scaler = None
            if model_info['scaler_path'] and model_info['scaler_path'].exists():
                scaler = joblib.load(model_info['scaler_path'])
            
            # Load features if available
            features = None
            if model_info['features_path'] and model_info['features_path'].exists():
                with open(model_info['features_path'], 'r') as f:
                    features = json.load(f)
            
            return model, scaler, features
            
        except Exception as e:
            logger.error(f"❌ Failed to load model {model_info['model_path']}: {e}")
            return None, None, None
    
    def create_comprehensive_features(self, timestamp, weather_data, solar_data, system_id, feature_list=None):
        """Create comprehensive features for any model"""
        try:
            # Basic temporal features
            hour = timestamp.hour
            month = timestamp.month
            day_of_year = timestamp.dayofyear
            day_of_week = timestamp.weekday()
            
            # Cyclical encoding
            hour_sin = np.sin(2 * np.pi * hour / 24)
            hour_cos = np.cos(2 * np.pi * hour / 24)
            month_sin = np.sin(2 * np.pi * month / 12)
            month_cos = np.cos(2 * np.pi * month / 12)
            day_sin = np.sin(2 * np.pi * day_of_year / 365)
            day_cos = np.cos(2 * np.pi * day_of_year / 365)
            
            # System features
            system_1 = 1 if system_id == 1 else 0
            system_2 = 1 if system_id == 2 else 0
            
            # Battery features
            soc = solar_data.get('avg_soc', 75)
            soc_normalized = soc / 100
            bat_power = solar_data.get('avg_bat_power', 0)
            bat_power_normalized = abs(bat_power) / 5000
            
            # Weather features
            temperature = weather_data.get('avg_temperature', 25)
            cloud_cover = weather_data.get('avg_cloud_cover', 30)
            ghi = weather_data.get('avg_ghi', 650)
            dni = weather_data.get('avg_dni', 520)
            dhi = weather_data.get('avg_dhi', 130)
            
            # Normalized weather
            temp_norm = (temperature - 20) / 30
            cloud_norm = cloud_cover / 100
            ghi_norm = ghi / 1000
            dni_norm = dni / 1000
            dhi_norm = dhi / 1000
            
            # Solar position (simplified)
            if 6 <= hour <= 18:
                solar_elevation = np.sin(np.pi * (hour - 6) / 12)
            else:
                solar_elevation = 0
            
            # Efficiency factors
            temp_efficiency = 1 - (temperature - 25) * 0.004
            temp_efficiency = max(0.7, min(1.1, temp_efficiency))
            
            # Weather efficiency
            weather_efficiency = ghi_norm * (1 - cloud_norm * 0.5) * temp_efficiency
            
            # Seasonal indicators
            is_summer = 1 if month in [6, 7, 8] else 0
            is_winter = 1 if month in [12, 1, 2] else 0
            is_daylight = 1 if 6 <= hour <= 18 else 0
            is_peak_solar = 1 if 10 <= hour <= 14 else 0
            
            # DC power features
            powerdc1 = solar_data.get('avg_powerdc1', 0)
            powerdc2 = solar_data.get('avg_powerdc2', 0)
            dc_total = powerdc1 + powerdc2
            dc_total_norm = dc_total / 10000  # Normalize to 10kW
            
            # Create comprehensive feature dictionary
            all_features = {
                # Basic temporal
                'hour': hour,
                'month': month,
                'day_of_year': day_of_year,
                'day_of_week': day_of_week,
                
                # Cyclical temporal
                'hour_sin': hour_sin,
                'hour_cos': hour_cos,
                'month_sin': month_sin,
                'month_cos': month_cos,
                'day_sin': day_sin,
                'day_cos': day_cos,
                
                # System identification
                'system_id': system_id,
                'system_1': system_1,
                'system_2': system_2,
                
                # Battery features
                'soc': soc,
                'soc_normalized': soc_normalized,
                'bat_power': bat_power,
                'bat_power_normalized': bat_power_normalized,
                'battery_mode': -1 if bat_power < 0 else (1 if bat_power > 0 else 0),
                'is_charging': 1 if bat_power > 0 else 0,
                'is_discharging': 1 if bat_power < 0 else 0,
                
                # Weather features (raw)
                'temperature': temperature,
                'cloud_cover': cloud_cover,
                'ghi': ghi,
                'dni': dni,
                'dhi': dhi,
                
                # Weather features (normalized)
                'temp_norm': temp_norm,
                'cloud_norm': cloud_norm,
                'ghi_norm': ghi_norm,
                'dni_norm': dni_norm,
                'dhi_norm': dhi_norm,
                'avg_ghi_norm': ghi_norm,
                'max_ghi_norm': ghi_norm * 1.2,
                
                # Solar position
                'solar_elevation': solar_elevation,
                
                # Efficiency factors
                'temp_efficiency': temp_efficiency,
                'weather_efficiency': weather_efficiency,
                
                # Time indicators
                'is_summer': is_summer,
                'is_winter': is_winter,
                'is_daylight': is_daylight,
                'is_peak_solar': is_peak_solar,
                'is_evening': 1 if 16 <= hour <= 20 else 0,
                
                # DC power features
                'powerdc1': powerdc1,
                'powerdc2': powerdc2,
                'dc_total': dc_total,
                'dc_total_norm': dc_total_norm,
                'efficiency': 0.9 if dc_total > 0 else 0,
                'efficiency_clipped': min(0.95, 0.9 if dc_total > 0 else 0),
                
                # Interaction features
                'battery_weather': soc_normalized * weather_efficiency,
                'system_weather': system_id * weather_efficiency,
                'time_battery': hour_sin * soc_normalized,
                'battery_weather_interaction': soc_normalized * weather_efficiency,
                
                # Additional features for compatibility
                'avg_soc_norm': soc_normalized,
                'avg_discharge_norm': max(0, -bat_power_normalized),
                'avg_charge_norm': max(0, bat_power_normalized),
                'panel_efficiency': 0.2,
                'inverter_efficiency': 0.95,
                'battery_utilization': abs(bat_power_normalized)
            }
            
            # If specific feature list provided, extract only those features
            if feature_list:
                feature_vector = []
                for feature_name in feature_list:
                    if feature_name in all_features:
                        feature_vector.append(all_features[feature_name])
                    else:
                        # Default value for missing features
                        feature_vector.append(0.0)
                return np.array(feature_vector)
            
            # Return all features as array (for models without specific feature lists)
            return np.array(list(all_features.values()))
            
        except Exception as e:
            logger.error(f"❌ Feature creation failed: {e}")
            return np.zeros(len(feature_list) if feature_list else 50)
    
    def test_model_on_real_data(self, model_name, model, scaler, features, model_type):
        """Test a specific model on real data"""
        try:
            logger.info(f"   📊 Testing {model_name} on real data...")

            all_predictions = []
            all_actuals = []
            date_results = {}

            for date_str in self.validation_dates:
                # Get real data for this date
                weather_data = self.get_real_weather_data_hourly(date_str)
                solar_data_s1 = self.get_real_hourly_data(date_str, 'solax_data')
                solar_data_s2 = self.get_real_hourly_data(date_str, 'solax_data2')

                if weather_data is None or solar_data_s1 is None:
                    logger.warning(f"   ⚠️ No data for {date_str}")
                    continue

                date_predictions = []
                date_actuals = []

                # Test for both systems
                for system_id, solar_data in [(1, solar_data_s1), (2, solar_data_s2)]:
                    if solar_data is None:
                        continue

                    # Merge weather and solar data by hour
                    merged_data = pd.merge(weather_data, solar_data, on='hour', how='inner')

                    for _, row in merged_data.iterrows():
                        timestamp = row['hour']

                        # Create features
                        if features:
                            feature_vector = self.create_comprehensive_features(
                                timestamp, row.to_dict(), row.to_dict(), system_id, features
                            )
                        else:
                            feature_vector = self.create_comprehensive_features(
                                timestamp, row.to_dict(), row.to_dict(), system_id
                            )

                        # Make prediction
                        try:
                            if model_type == 'lightgbm_daily':
                                # Daily model - convert to hourly
                                if scaler:
                                    feature_vector_scaled = scaler.transform([feature_vector])
                                    daily_prediction = model.predict(feature_vector_scaled)[0]
                                else:
                                    daily_prediction = model.predict([feature_vector])[0]

                                # Convert daily to hourly
                                hour = timestamp.hour
                                if 6 <= hour <= 18:
                                    solar_factor = np.sin(np.pi * (hour - 6) / 12)
                                    hourly_prediction = (daily_prediction * 1000 * solar_factor) / 8
                                else:
                                    hourly_prediction = 0

                            elif model_type == 'lightgbm':
                                if hasattr(model, 'predict'):
                                    hourly_prediction = model.predict([feature_vector])[0]
                                else:
                                    hourly_prediction = model.predict(np.array([feature_vector]))[0]

                                # Denormalize if needed
                                if hourly_prediction < 1:  # Likely normalized
                                    hourly_prediction *= 12469  # Max power

                            elif model_type == 'xgboost':
                                import xgboost as xgb
                                dtest = xgb.DMatrix([feature_vector])
                                hourly_prediction = model.predict(dtest)[0]

                                # Denormalize if needed
                                if hourly_prediction < 1:
                                    hourly_prediction *= 12469

                            else:  # ensemble or other sklearn models
                                if scaler:
                                    feature_vector_scaled = scaler.transform([feature_vector])
                                    hourly_prediction = model.predict(feature_vector_scaled)[0]
                                else:
                                    hourly_prediction = model.predict([feature_vector])[0]

                                # Denormalize if needed
                                if hourly_prediction < 1:
                                    hourly_prediction *= 12469

                            # Ensure non-negative
                            hourly_prediction = max(0, hourly_prediction)

                            # Get actual value
                            actual_power = row['avg_ac_power']

                            # Store predictions and actuals
                            date_predictions.append(hourly_prediction)
                            date_actuals.append(actual_power)
                            all_predictions.append(hourly_prediction)
                            all_actuals.append(actual_power)

                        except Exception as e:
                            logger.warning(f"   ⚠️ Prediction failed for {timestamp}: {e}")
                            continue

                # Calculate date-specific accuracy
                if date_predictions and date_actuals:
                    date_accuracy = self.calculate_accuracy(date_predictions, date_actuals)
                    date_results[date_str] = {
                        'accuracy': date_accuracy,
                        'predictions': len(date_predictions),
                        'avg_predicted': np.mean(date_predictions),
                        'avg_actual': np.mean(date_actuals)
                    }
                    logger.info(f"     {date_str}: {date_accuracy:.1f}% accuracy ({len(date_predictions)} predictions)")

            # Calculate overall accuracy
            if all_predictions and all_actuals:
                overall_accuracy = self.calculate_accuracy(all_predictions, all_actuals)

                return {
                    'model_name': model_name,
                    'model_type': model_type,
                    'average_accuracy': overall_accuracy,
                    'total_predictions': len(all_predictions),
                    'date_results': date_results,
                    'predictions': all_predictions,
                    'actuals': all_actuals,
                    'rmse': np.sqrt(mean_squared_error(all_actuals, all_predictions)),
                    'mae': mean_absolute_error(all_actuals, all_predictions),
                    'r2': r2_score(all_actuals, all_predictions) if len(set(all_actuals)) > 1 else 0
                }

            return None

        except Exception as e:
            logger.error(f"❌ Testing {model_name} failed: {e}")
            return None

    def calculate_accuracy(self, predictions, actuals):
        """Calculate prediction accuracy"""
        try:
            if not predictions or not actuals or len(predictions) != len(actuals):
                return 0.0

            # Calculate MAPE (Mean Absolute Percentage Error)
            mape_errors = []
            for pred, actual in zip(predictions, actuals):
                if actual > 0:  # Avoid division by zero
                    mape_errors.append(abs(pred - actual) / actual)
                elif pred == 0 and actual == 0:
                    mape_errors.append(0)  # Perfect prediction for zero
                else:
                    mape_errors.append(1)  # 100% error for non-zero prediction when actual is zero

            mape = np.mean(mape_errors)
            accuracy = max(0, (1 - mape) * 100)

            return accuracy

        except Exception as e:
            logger.error(f"❌ Accuracy calculation failed: {e}")
            return 0.0

    def display_comprehensive_summary(self, all_results):
        """Display comprehensive summary of all test results"""
        logger.info("\n" + "=" * 80)
        logger.info("🎉 ULTIMATE HOURLY MODEL TESTING RESULTS")
        logger.info("=" * 80)

        if not all_results:
            logger.warning("❌ No results to display")
            return

        # Sort results by accuracy
        sorted_results = sorted(all_results.items(), key=lambda x: x[1].get('average_accuracy', 0), reverse=True)

        logger.info("📊 MODEL RANKING BY ACCURACY:")
        logger.info("-" * 80)

        for i, (model_name, results) in enumerate(sorted_results, 1):
            accuracy = results.get('average_accuracy', 0)
            total_preds = results.get('total_predictions', 0)
            rmse = results.get('rmse', 0)
            r2 = results.get('r2', 0)

            status = "🎯 TARGET ACHIEVED" if accuracy >= 95.0 else "⚠️ Below Target" if accuracy >= 85.0 else "❌ Poor Performance"

            logger.info(f"{i:2d}. {model_name}")
            logger.info(f"    Accuracy: {accuracy:.1f}% | RMSE: {rmse:.1f}W | R²: {r2:.3f} | Predictions: {total_preds}")
            logger.info(f"    Status: {status}")
            logger.info("")

        # Best model analysis
        if sorted_results:
            best_model_name, best_results = sorted_results[0]
            logger.info("🏆 BEST MODEL ANALYSIS:")
            logger.info(f"   Model: {best_model_name}")
            logger.info(f"   Accuracy: {best_results['average_accuracy']:.1f}%")
            logger.info(f"   Type: {best_results['model_type']}")

            # Date breakdown
            logger.info("   Date Breakdown:")
            for date_str, date_result in best_results.get('date_results', {}).items():
                logger.info(f"     {date_str}: {date_result['accuracy']:.1f}% ({date_result['predictions']} predictions)")

        # Target achievement summary
        target_achieved = [name for name, results in all_results.items() if results.get('average_accuracy', 0) >= 95.0]

        if target_achieved:
            logger.info(f"\n🎯 MODELS ACHIEVING 95%+ ACCURACY: {len(target_achieved)}")
            for model_name in target_achieved:
                accuracy = all_results[model_name]['average_accuracy']
                logger.info(f"   ✅ {model_name}: {accuracy:.1f}%")
        else:
            logger.info("\n⚠️ NO MODELS ACHIEVED 95%+ ACCURACY TARGET")
            logger.info("   Recommendations:")
            logger.info("   1. Develop dedicated hourly prediction model")
            logger.info("   2. Improve feature engineering for hourly patterns")
            logger.info("   3. Use ensemble of best performing models")
            logger.info("   4. Collect more granular training data")

    def run_ultimate_hourly_testing(self):
        """Run ultimate hourly model testing framework"""
        logger.info("🚀 ULTIMATE HOURLY MODEL TESTING FRAMEWORK")
        logger.info("=" * 80)
        logger.info("🎯 Target: 95%+ accuracy for hourly predictions with real data")

        try:
            # Discover all available models
            discovered_models = self.discover_all_available_models()

            if not discovered_models:
                logger.error("❌ No models discovered")
                return False

            # Test each model
            logger.info(f"\n🔬 TESTING {len(discovered_models)} MODELS")
            logger.info("=" * 80)

            all_results = {}

            for model_name, model_info in discovered_models.items():
                logger.info(f"\n🧪 Testing {model_name}...")

                # Load model
                model, scaler, features = self.load_model_safely(model_info)

                if model is None:
                    logger.warning(f"⚠️ Could not load {model_name}")
                    continue

                # Test model on validation dates
                model_results = self.test_model_on_real_data(
                    model_name, model, scaler, features, model_info['type']
                )

                if model_results:
                    all_results[model_name] = model_results

                    # Log results
                    avg_accuracy = model_results.get('average_accuracy', 0)
                    logger.info(f"   ✅ {model_name}: {avg_accuracy:.1f}% accuracy")

                    # Check if we reached 95% target
                    if avg_accuracy >= 95.0:
                        logger.info(f"   🎯 TARGET ACHIEVED: {model_name} reached {avg_accuracy:.1f}% accuracy!")
                        self.best_models[model_name] = model_results

            # Store all results
            self.test_results = all_results

            # Save results
            results_file = f"test/results/ultimate_hourly_testing_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            os.makedirs(os.path.dirname(results_file), exist_ok=True)

            with open(results_file, 'w') as f:
                json.dump(all_results, f, indent=2, default=str)

            # Display summary
            self.display_comprehensive_summary(all_results)

            logger.info(f"\n💾 Results saved: {results_file}")
            logger.info("=" * 80)

            return True

        except Exception as e:
            logger.error(f"❌ Ultimate hourly testing failed: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """Execute ultimate hourly model testing"""
    tester = UltimateHourlyModelTester()
    success = tester.run_ultimate_hourly_testing()
    
    if success:
        print("\n🎯 Ultimate hourly model testing completed successfully!")
        print("📊 All models tested with real data for 95%+ accuracy target")
        return True
    else:
        print("\n❌ Ultimate hourly model testing failed!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
