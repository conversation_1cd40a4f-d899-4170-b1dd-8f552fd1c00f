#!/usr/bin/env python3
"""
Real-World Validation of Optimized Model
Test optimized model against real data and generate accurate predictions
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import pandas as pd
import numpy as np
import psycopg2
import joblib
import json
from datetime import datetime, timedelta
import logging
from pathlib import Path
from dotenv import load_dotenv
import requests

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RealWorldValidator:
    """Real-world validation of optimized model"""
    
    def __init__(self):
        load_dotenv()
        self.project_root = Path("/home/<USER>/solar-prediction-project")
        self.models_dir = self.project_root / "models" / "optimized_final"
        
        # Real data for validation
        self.real_data = {
            '2025-06-01': {'system1': 72.8, 'system2': 67.7},
            '2025-06-02': {'system1': 31.8, 'system2': 34.0}  # partial day
        }
        
        # Load optimized model
        self.model = None
        self.scaler = None
        self.features = None
        self.load_optimized_model()
        
    def load_optimized_model(self):
        """Load the optimized model"""
        try:
            # Load model
            model_path = self.models_dir / "optimized_model_ensemble.joblib"
            if model_path.exists():
                self.model = joblib.load(model_path)
                logger.info("✅ Optimized ensemble model loaded")
            
            # Load scaler
            scaler_path = self.models_dir / "optimized_scaler_standard.joblib"
            if scaler_path.exists():
                self.scaler = joblib.load(scaler_path)
                logger.info("✅ Standard scaler loaded")
            
            # Load features
            features_path = self.models_dir / "optimized_features.json"
            if features_path.exists():
                with open(features_path, 'r') as f:
                    self.features = json.load(f)
                logger.info(f"✅ {len(self.features)} features loaded")
            
            return self.model is not None and self.scaler is not None and self.features is not None
            
        except Exception as e:
            logger.error(f"❌ Failed to load optimized model: {e}")
            return False
    
    def connect_database(self):
        """Connect to database"""
        try:
            conn = psycopg2.connect(
                host=os.getenv('DB_HOST', 'localhost'),
                database=os.getenv('DB_NAME', 'solar_prediction'),
                user=os.getenv('DB_USER', 'postgres'),
                password=os.getenv('DB_PASSWORD', 'postgres')
            )
            return conn
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            return None
    
    def create_features_for_data(self, df):
        """Create features for given data"""
        # Sort by system and timestamp
        df = df.sort_values(['system_id', 'timestamp']).reset_index(drop=True)
        
        # 1. TEMPORAL FEATURES
        df['hour'] = df['timestamp'].dt.hour
        df['month'] = df['timestamp'].dt.month
        df['day_of_year'] = df['timestamp'].dt.dayofyear
        
        # Cyclical encoding
        df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
        df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
        df['day_sin'] = np.sin(2 * np.pi * df['day_of_year'] / 365)
        df['day_cos'] = np.cos(2 * np.pi * df['day_of_year'] / 365)
        
        # Time indicators
        df['is_daylight'] = df['hour'].between(6, 20).astype(int)
        df['is_peak_solar'] = df['hour'].between(10, 16).astype(int)
        df['is_evening'] = df['hour'].between(18, 22).astype(int)
        
        # 2. SYSTEM FEATURES
        df['system_1'] = (df['system_id'] == 1).astype(int)
        df['system_2'] = (df['system_id'] == 2).astype(int)
        
        # 3. BATTERY FEATURES
        df['soc_normalized'] = df['soc'] / 100
        df['battery_mode'] = np.sign(df['bat_power'])
        df['is_charging'] = (df['bat_power'] > 100).astype(int)
        df['is_discharging'] = (df['bat_power'] < -100).astype(int)
        df['battery_utilization'] = np.abs(df['bat_power']) / 6000
        
        # 4. PRODUCTION FEATURES
        df['dc_total'] = df['powerdc1'] + df['powerdc2']
        df['efficiency'] = np.where(df['dc_total'] > 0, df['ac_power'] / df['dc_total'], 0)
        df['efficiency_clipped'] = np.clip(df['efficiency'], 0, 1.2)
        
        # 5. WEATHER FEATURES
        df['ghi_norm'] = df['ghi'] / 1000
        df['temp_norm'] = (df['temperature'] - 20) / 30
        df['cloud_norm'] = df['cloud_cover'] / 100
        
        # Weather efficiency
        df['weather_efficiency'] = df['ghi_norm'] * (1 - df['cloud_norm'] * 0.5)
        df['temp_efficiency'] = 1 - (df['temperature'] - 25) * 0.004
        df['temp_efficiency'] = np.clip(df['temp_efficiency'], 0.7, 1.1)
        
        # 6. INTERACTION FEATURES
        df['battery_weather'] = df['soc_normalized'] * df['weather_efficiency']
        df['system_weather'] = df['system_id'] * df['weather_efficiency']
        df['time_battery'] = df['hour_sin'] * df['soc_normalized']
        
        return df
    
    def validate_against_real_data(self, conn):
        """Validate model against real data"""
        logger.info("🔍 VALIDATING AGAINST REAL DATA")
        logger.info("=" * 60)
        
        validation_results = {}
        
        for date_str, real_yields in self.real_data.items():
            logger.info(f"\n📅 Validating {date_str}...")
            
            validation_results[date_str] = {}
            
            for system_key, real_yield in real_yields.items():
                system_id = 1 if system_key == 'system1' else 2
                table = 'solax_data' if system_id == 1 else 'solax_data2'
                
                try:
                    # Get data for this date and system
                    query = f"""
                    WITH system_data AS (
                        SELECT 
                            timestamp, ac_power, soc, bat_power, powerdc1, powerdc2,
                            yield_today, {system_id} as system_id
                        FROM {table}
                        WHERE DATE(timestamp) = %s
                        AND ac_power IS NOT NULL
                        ORDER BY timestamp
                    ),
                    weather_data AS (
                        SELECT
                            DATE_TRUNC('hour', timestamp) as hour_timestamp,
                            AVG(COALESCE(ghi, 400)) as ghi,
                            AVG(COALESCE(temperature, 20)) as temperature,
                            AVG(COALESCE(cloud_cover, 50)) as cloud_cover
                        FROM cams_radiation_data
                        WHERE DATE(timestamp) = %s
                        GROUP BY DATE_TRUNC('hour', timestamp)
                    )
                    SELECT
                        sd.*,
                        COALESCE(w.ghi, 400) as ghi,
                        COALESCE(w.temperature, 20) as temperature,
                        COALESCE(w.cloud_cover, 50) as cloud_cover
                    FROM system_data sd
                    LEFT JOIN weather_data w ON DATE_TRUNC('hour', sd.timestamp) = w.hour_timestamp
                    ORDER BY sd.timestamp
                    """
                    
                    df = pd.read_sql(query, conn, params=[date_str, date_str])
                    
                    if len(df) == 0:
                        logger.warning(f"   No data found for {system_key}")
                        continue
                    
                    # Create features
                    df_features = self.create_features_for_data(df)
                    
                    # Prepare features for prediction
                    X = df_features[self.features].fillna(0)
                    X_scaled = self.scaler.transform(X)
                    
                    # Make predictions
                    predictions = self.model.predict(X_scaled)
                    
                    # Calculate daily yield from predictions
                    # Convert hourly power to energy (simplified)
                    predicted_daily_yield = np.sum(predictions[predictions > 0]) / 12 / 1000  # 5-min intervals to kWh
                    
                    # Get actual daily yield from database
                    actual_daily_yield = df['yield_today'].max()
                    
                    # Calculate accuracy
                    if date_str == '2025-06-02':
                        # For partial day, compare with real partial data
                        comparison_yield = real_yield
                        comparison_type = "partial_day"
                    else:
                        # For full day, compare with database
                        comparison_yield = actual_daily_yield
                        comparison_type = "full_day"
                    
                    if comparison_yield > 0:
                        accuracy = (1 - abs(predicted_daily_yield - comparison_yield) / comparison_yield) * 100
                    else:
                        accuracy = 0
                    
                    validation_results[date_str][system_key] = {
                        'predicted_yield_kwh': predicted_daily_yield,
                        'actual_db_yield_kwh': actual_daily_yield,
                        'real_yield_kwh': real_yield,
                        'comparison_yield_kwh': comparison_yield,
                        'comparison_type': comparison_type,
                        'accuracy_percent': accuracy,
                        'records_used': len(df),
                        'avg_predicted_power': np.mean(predictions),
                        'max_predicted_power': np.max(predictions)
                    }
                    
                    logger.info(f"   {system_key.upper()}:")
                    logger.info(f"     Predicted: {predicted_daily_yield:.1f} kWh")
                    logger.info(f"     Real: {real_yield:.1f} kWh")
                    logger.info(f"     Accuracy: {accuracy:.1f}%")
                    logger.info(f"     Max Power: {np.max(predictions):.0f}W")
                    
                except Exception as e:
                    logger.error(f"   {system_key} validation failed: {e}")
        
        return validation_results
    
    def generate_2day_predictions(self):
        """Generate 2-day predictions using optimized model"""
        logger.info("🔮 GENERATING 2-DAY PREDICTIONS")
        logger.info("=" * 60)
        
        predictions = {}
        
        for day_offset in range(2):
            target_date = datetime.now().date() + timedelta(days=day_offset + 1)
            date_str = target_date.strftime('%Y-%m-%d')
            
            logger.info(f"\n📅 Predicting {date_str}...")
            predictions[date_str] = {}
            
            for system_id in [1, 2]:
                system_key = f'system{system_id}'
                daily_predictions = []
                daily_total = 0
                
                # Generate hourly predictions
                for hour in range(24):
                    # Create sample data for this hour
                    sample_data = {
                        'timestamp': pd.Timestamp(f'{date_str} {hour:02d}:00:00'),
                        'system_id': system_id,
                        'soc': 75.0,  # Assume 75% SOC
                        'bat_power': -500.0 if 18 <= hour <= 22 else 0,  # Evening discharge
                        'powerdc1': 3000 if 6 <= hour <= 20 else 0,  # Daylight DC
                        'powerdc2': 2800 if 6 <= hour <= 20 else 0,
                        'ghi': max(0, 800 * np.sin(np.pi * (hour - 6) / 14)) if 6 <= hour <= 20 else 0,
                        'temperature': 25.0,
                        'cloud_cover': 30.0
                    }
                    
                    # Create DataFrame
                    df_sample = pd.DataFrame([sample_data])
                    
                    # Create features
                    df_features = self.create_features_for_data(df_sample)
                    
                    # Prepare for prediction
                    X = df_features[self.features].fillna(0)
                    X_scaled = self.scaler.transform(X)
                    
                    # Make prediction
                    prediction = self.model.predict(X_scaled)[0]
                    prediction = max(0, prediction)  # Ensure non-negative
                    
                    daily_predictions.append({
                        'hour': hour,
                        'predicted_power_w': prediction,
                        'predicted_energy_kwh': prediction / 1000  # Simplified
                    })
                    
                    # Add to daily total (only positive predictions)
                    if prediction > 0:
                        daily_total += prediction / 12 / 1000  # 5-min intervals to kWh
                
                predictions[date_str][system_key] = {
                    'daily_total_kwh': daily_total,
                    'peak_power_w': max(p['predicted_power_w'] for p in daily_predictions),
                    'peak_hour': max(daily_predictions, key=lambda x: x['predicted_power_w'])['hour'],
                    'hourly_predictions': daily_predictions
                }
                
                logger.info(f"   {system_key.upper()}: {daily_total:.1f} kWh (peak: {predictions[date_str][system_key]['peak_power_w']:.0f}W at {predictions[date_str][system_key]['peak_hour']}:00)")
        
        return predictions
    
    def run_validation(self):
        """Run complete real-world validation"""
        logger.info("🚀 REAL-WORLD MODEL VALIDATION")
        logger.info("=" * 80)
        logger.info("🎯 Testing optimized model against real data")
        
        try:
            if not self.load_optimized_model():
                logger.error("❌ Could not load optimized model")
                return False
            
            # Connect to database
            conn = self.connect_database()
            if not conn:
                return False
            
            # Validate against real data
            validation_results = self.validate_against_real_data(conn)
            
            conn.close()
            
            # Generate 2-day predictions
            future_predictions = self.generate_2day_predictions()
            
            # Compile results
            complete_results = {
                'validation_date': datetime.now().isoformat(),
                'model_info': {
                    'type': 'Optimized Ensemble',
                    'features': len(self.features),
                    'normalization': 'Standard Scaler'
                },
                'validation_results': validation_results,
                'future_predictions': future_predictions,
                'summary': self.calculate_validation_summary(validation_results)
            }
            
            # Save results
            results_file = f"test/results/real_world_validation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            os.makedirs(os.path.dirname(results_file), exist_ok=True)
            
            with open(results_file, 'w') as f:
                json.dump(complete_results, f, indent=2, default=str)
            
            # Display summary
            self.display_validation_summary(complete_results)
            
            logger.info(f"\n💾 Validation results saved: {results_file}")
            logger.info("=" * 80)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Real-world validation failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def calculate_validation_summary(self, validation_results):
        """Calculate validation summary"""
        total_accuracy = 0
        count = 0
        
        for date_data in validation_results.values():
            for system_data in date_data.values():
                if 'accuracy_percent' in system_data:
                    total_accuracy += system_data['accuracy_percent']
                    count += 1
        
        if count > 0:
            avg_accuracy = total_accuracy / count
            if avg_accuracy > 85:
                status = "Excellent"
            elif avg_accuracy > 70:
                status = "Good"
            elif avg_accuracy > 50:
                status = "Acceptable"
            else:
                status = "Needs Improvement"
        else:
            avg_accuracy = 0
            status = "No Data"
        
        return {
            'average_accuracy': avg_accuracy,
            'status': status,
            'total_validations': count
        }
    
    def display_validation_summary(self, results):
        """Display validation summary"""
        logger.info("\n📊 VALIDATION SUMMARY:")
        
        if 'summary' in results:
            summary = results['summary']
            logger.info(f"   Average Accuracy: {summary['average_accuracy']:.1f}%")
            logger.info(f"   Status: {summary['status']}")
            logger.info(f"   Validations: {summary['total_validations']}")
        
        if 'future_predictions' in results:
            logger.info("\n🔮 2-DAY PREDICTIONS:")
            for date, systems in results['future_predictions'].items():
                logger.info(f"   {date}:")
                for system, pred in systems.items():
                    logger.info(f"     {system.upper()}: {pred['daily_total_kwh']:.1f} kWh")

def main():
    """Execute real-world validation"""
    validator = RealWorldValidator()
    success = validator.run_validation()
    
    if success:
        print("\n🎯 Real-world validation completed successfully!")
        return True
    else:
        print("\n❌ Real-world validation failed!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
