#!/usr/bin/env python3
"""
Integrate Enhanced Model v3 into Production System
Update ML service and frontend to use the new optimized model
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import shutil
import json
import logging
from pathlib import Path
from datetime import datetime

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedModelV3Integration:
    """Integrate Enhanced Model v3 into production system"""
    
    def __init__(self):
        self.project_root = Path("/home/<USER>/solar-prediction-project")
        self.models_dir = self.project_root / "models"
        self.src_dir = self.project_root / "src"
        
    def backup_current_model(self):
        """Backup current production model"""
        logger.info("📦 Backing up current production model...")
        
        try:
            backup_dir = self.models_dir / "backup" / f"enhanced_v2_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            # Backup Enhanced Model v2
            v2_dir = self.models_dir / "enhanced_v2"
            if v2_dir.exists():
                shutil.copytree(v2_dir, backup_dir / "enhanced_v2")
                logger.info(f"✅ Enhanced Model v2 backed up to: {backup_dir}")
            
            return backup_dir
            
        except Exception as e:
            logger.error(f"❌ Backup failed: {e}")
            return None
    
    def update_ml_service(self):
        """Update ML service to use Enhanced Model v3"""
        logger.info("🔧 Updating ML service for Enhanced Model v3...")
        
        try:
            ml_service_path = self.src_dir / "services" / "ml_service.py"
            
            # Read current ML service
            with open(ml_service_path, 'r') as f:
                content = f.read()
            
            # Update model path references
            updated_content = content.replace(
                'models/enhanced_v2',
                'models/enhanced_v3_production'
            )
            
            # Update model name references
            updated_content = updated_content.replace(
                'Enhanced Model v2',
                'Enhanced Model v3 Optimized'
            )
            
            # Update performance metrics in comments
            updated_content = updated_content.replace(
                'R² = 0.757',
                'R² = 0.967'
            )
            
            # Write updated ML service
            with open(ml_service_path, 'w') as f:
                f.write(updated_content)
            
            logger.info("✅ ML service updated for Enhanced Model v3")
            return True
            
        except Exception as e:
            logger.error(f"❌ ML service update failed: {e}")
            return False
    
    def update_frontend_references(self):
        """Update frontend to display Enhanced Model v3"""
        logger.info("🎨 Updating frontend references...")
        
        try:
            # Update forecast interface
            forecast_html = self.project_root / "static" / "forecast" / "index.html"
            
            if forecast_html.exists():
                with open(forecast_html, 'r') as f:
                    content = f.read()
                
                # Update model name display
                updated_content = content.replace(
                    'Enhanced Model v2',
                    'Enhanced Model v3 Optimized'
                )
                
                # Update performance display
                updated_content = updated_content.replace(
                    'R² = 0.757',
                    'R² = 0.967'
                )
                
                with open(forecast_html, 'w') as f:
                    f.write(updated_content)
                
                logger.info("✅ Forecast interface updated")
            
            # Update forecast.js if exists
            forecast_js = self.project_root / "static" / "forecast" / "forecast.js"
            
            if forecast_js.exists():
                with open(forecast_js, 'r') as f:
                    content = f.read()
                
                # Update model references
                updated_content = content.replace(
                    'Enhanced Model v2',
                    'Enhanced Model v3 Optimized'
                )
                
                with open(forecast_js, 'w') as f:
                    f.write(updated_content)
                
                logger.info("✅ Forecast JavaScript updated")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Frontend update failed: {e}")
            return False
    
    def create_deployment_info(self):
        """Create deployment information file"""
        logger.info("📋 Creating deployment information...")
        
        try:
            deployment_info = {
                "deployment_date": datetime.now().isoformat(),
                "model_version": "Enhanced Model v3 Optimized",
                "model_path": "models/enhanced_v3_production/enhanced_model_v3_final.joblib",
                "features_path": "models/enhanced_v3_production/feature_columns.json",
                "performance": {
                    "r2_score": 0.967,
                    "rmse_watts": 487.5,
                    "mae_watts": 220.5,
                    "system_1_r2": 0.953,
                    "system_1_rmse": 595.6,
                    "system_2_r2": 0.984,
                    "system_2_rmse": 326.0
                },
                "improvements": {
                    "vs_enhanced_v2": {
                        "r2_improvement": "+27.7%",
                        "rmse_improvement": "-22.5%",
                        "system_awareness": "Added",
                        "battery_modeling": "Enhanced"
                    }
                },
                "features": {
                    "total_features": 34,
                    "categories": [
                        "Temporal (cyclical encoding)",
                        "Battery state and behavior",
                        "System-specific patterns",
                        "Weather integration",
                        "Production efficiency",
                        "Interaction features"
                    ]
                },
                "training_data": {
                    "total_samples": 89265,
                    "data_retention": "72.9%",
                    "date_range": "2024-03-01 to 2025-06-02"
                },
                "deployment_status": "Production Ready",
                "next_retraining": "2025-09-01"
            }
            
            deployment_path = self.models_dir / "enhanced_v3_production" / "deployment_info.json"
            with open(deployment_path, 'w') as f:
                json.dump(deployment_info, f, indent=2)
            
            logger.info(f"✅ Deployment info created: {deployment_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Deployment info creation failed: {e}")
            return False
    
    def run_health_check(self):
        """Run comprehensive health check"""
        logger.info("🏥 Running comprehensive health check...")
        
        try:
            health_status = {
                "timestamp": datetime.now().isoformat(),
                "checks": {}
            }
            
            # Check model files
            model_path = self.models_dir / "enhanced_v3_production" / "enhanced_model_v3_final.joblib"
            features_path = self.models_dir / "enhanced_v3_production" / "feature_columns.json"
            
            health_status["checks"]["model_file"] = {
                "status": "✅ OK" if model_path.exists() else "❌ MISSING",
                "path": str(model_path),
                "size_mb": round(model_path.stat().st_size / 1024 / 1024, 2) if model_path.exists() else 0
            }
            
            health_status["checks"]["features_file"] = {
                "status": "✅ OK" if features_path.exists() else "❌ MISSING",
                "path": str(features_path)
            }
            
            # Check ML service
            ml_service_path = self.src_dir / "services" / "ml_service.py"
            health_status["checks"]["ml_service"] = {
                "status": "✅ OK" if ml_service_path.exists() else "❌ MISSING",
                "path": str(ml_service_path)
            }
            
            # Check frontend files
            forecast_html = self.project_root / "static" / "forecast" / "index.html"
            health_status["checks"]["frontend"] = {
                "status": "✅ OK" if forecast_html.exists() else "❌ MISSING",
                "path": str(forecast_html)
            }
            
            # Overall status
            all_checks_ok = all(
                check["status"].startswith("✅") 
                for check in health_status["checks"].values()
            )
            
            health_status["overall_status"] = "✅ HEALTHY" if all_checks_ok else "❌ ISSUES DETECTED"
            
            # Save health check results
            health_path = self.models_dir / "enhanced_v3_production" / "health_check.json"
            with open(health_path, 'w') as f:
                json.dump(health_status, f, indent=2)
            
            # Display results
            logger.info("🏥 Health Check Results:")
            for check_name, check_info in health_status["checks"].items():
                logger.info(f"   {check_name}: {check_info['status']}")
            
            logger.info(f"Overall Status: {health_status['overall_status']}")
            
            return all_checks_ok
            
        except Exception as e:
            logger.error(f"❌ Health check failed: {e}")
            return False

def main():
    """Execute Enhanced Model v3 integration"""
    logger.info("🚀 ENHANCED MODEL V3 - PRODUCTION INTEGRATION")
    logger.info("=" * 80)
    logger.info(f"📅 Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("🎯 Objective: Integrate Enhanced Model v3 into production system")
    
    try:
        integration = EnhancedModelV3Integration()
        
        # Step 1: Backup current model
        backup_dir = integration.backup_current_model()
        
        # Step 2: Update ML service
        ml_updated = integration.update_ml_service()
        
        # Step 3: Update frontend
        frontend_updated = integration.update_frontend_references()
        
        # Step 4: Create deployment info
        deployment_created = integration.create_deployment_info()
        
        # Step 5: Run health check
        health_ok = integration.run_health_check()
        
        logger.info("\n" + "=" * 80)
        logger.info("🎉 ENHANCED MODEL V3 INTEGRATION COMPLETE!")
        logger.info(f"✅ Backup created: {backup_dir is not None}")
        logger.info(f"✅ ML service updated: {ml_updated}")
        logger.info(f"✅ Frontend updated: {frontend_updated}")
        logger.info(f"✅ Deployment info created: {deployment_created}")
        logger.info(f"✅ Health check: {'PASSED' if health_ok else 'ISSUES DETECTED'}")
        
        if all([ml_updated, frontend_updated, deployment_created, health_ok]):
            logger.info("🚀 ENHANCED MODEL V3 IS NOW LIVE IN PRODUCTION!")
            logger.info("📊 Performance: R² = 0.967, RMSE = 487.5W")
            logger.info("🔋 Battery-aware predictions with system-specific modeling")
        else:
            logger.info("⚠️ Some integration steps failed. Check logs above.")
        
        logger.info("=" * 80)
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Integration failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
