#!/usr/bin/env python3
"""
Enhanced Model v3 - Separate System Models
Train dedicated models for each solar system for optimal performance
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import pandas as pd
import numpy as np
import psycopg2
from dotenv import load_dotenv
import logging
from datetime import datetime, timedelta
from sklearn.ensemble import RandomForestRegressor, VotingRegressor
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import xgboost as xgb
import lightgbm as lgb
import joblib
from pathlib import Path
import json

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SeparateSystemModels:
    """Train separate optimized models for each solar system"""
    
    def __init__(self):
        self.models_dir = Path("models/enhanced_v3_separate_systems")
        self.models_dir.mkdir(parents=True, exist_ok=True)
        
        # System configurations
        self.system_configs = {
            1: {
                'name': 'Σπίτι Πάνω',
                'consumption_pattern': 'low_steady',
                'avg_daily_consumption': 22.45,
                'grid_dependency': 0.0,
                'self_sufficiency': 1.0,
                'table': 'solax_data'
            },
            2: {
                'name': 'Σπίτι Κάτω',
                'consumption_pattern': 'high_variable',
                'avg_daily_consumption': 35.29,
                'grid_dependency': 0.395,
                'self_sufficiency': 0.605,
                'table': 'solax_data2'
            }
        }
        
        self.models = {}
        self.feature_columns = {}
        
    def load_system_data(self, system_id):
        """Load data for specific system"""
        logger.info(f"🔍 Loading data for System {system_id} ({self.system_configs[system_id]['name']})...")
        
        try:
            load_dotenv()
            conn = psycopg2.connect(
                host=os.getenv('DB_HOST', 'localhost'),
                database=os.getenv('DB_NAME', 'solar_prediction'),
                user=os.getenv('DB_USER', 'postgres'),
                password=os.getenv('DB_PASSWORD', 'postgres')
            )
            
            table = self.system_configs[system_id]['table']
            
            # System-specific query with weather data
            query = f"""
            WITH system_data AS (
                SELECT
                    timestamp, ac_power, soc, bat_power, powerdc1, powerdc2, 
                    yield_today, feedin_power, consume_energy
                FROM {table}
                WHERE timestamp >= '2024-03-01'
                AND ac_power IS NOT NULL 
                AND soc IS NOT NULL 
                AND bat_power IS NOT NULL
                AND powerdc1 IS NOT NULL 
                AND powerdc2 IS NOT NULL
            ),
            weather_data AS (
                SELECT
                    DATE_TRUNC('hour', timestamp) as hour_timestamp,
                    AVG(COALESCE(ghi, 400)) as ghi,
                    AVG(COALESCE(temperature, 20)) as temperature,
                    AVG(COALESCE(cloud_cover, 50)) as cloud_cover
                FROM cams_radiation_data
                WHERE timestamp >= '2024-03-01'
                GROUP BY DATE_TRUNC('hour', timestamp)
            )
            SELECT
                sd.*,
                COALESCE(w.ghi, 400) as ghi,
                COALESCE(w.temperature, 20) as temperature,
                COALESCE(w.cloud_cover, 50) as cloud_cover
            FROM system_data sd
            LEFT JOIN weather_data w ON DATE_TRUNC('hour', sd.timestamp) = w.hour_timestamp
            ORDER BY sd.timestamp
            """
            
            df = pd.read_sql(query, conn)
            conn.close()
            
            initial_count = len(df)
            logger.info(f"✅ System {system_id} data loaded: {initial_count:,} records")
            
            # Minimal filtering
            df = df[
                (df['ac_power'] >= 0) & (df['ac_power'] <= 20000) &
                (df['soc'] >= 0) & (df['soc'] <= 100) &
                (df['bat_power'] >= -10000) & (df['bat_power'] <= 10000)
            ]
            
            final_count = len(df)
            retention_rate = (final_count / initial_count) * 100
            
            logger.info(f"✅ System {system_id} after filtering:")
            logger.info(f"   Final records: {final_count:,}")
            logger.info(f"   Retention rate: {retention_rate:.1f}%")
            logger.info(f"   Date range: {df['timestamp'].min()} to {df['timestamp'].max()}")
            
            return df
            
        except Exception as e:
            logger.error(f"❌ System {system_id} data loading failed: {e}")
            raise
    
    def create_system_specific_features(self, df, system_id):
        """Create system-specific features"""
        logger.info(f"🔧 Creating system-specific features for System {system_id}...")
        
        config = self.system_configs[system_id]
        
        # Sort by timestamp
        df = df.sort_values('timestamp').reset_index(drop=True)
        
        # 1. TEMPORAL FEATURES
        df['hour'] = df['timestamp'].dt.hour
        df['month'] = df['timestamp'].dt.month
        df['day_of_year'] = df['timestamp'].dt.dayofyear
        df['day_of_week'] = df['timestamp'].dt.dayofweek
        
        # Cyclical encoding
        df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
        df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
        df['day_of_year_sin'] = np.sin(2 * np.pi * df['day_of_year'] / 365)
        df['day_of_year_cos'] = np.cos(2 * np.pi * df['day_of_year'] / 365)
        
        # Time indicators
        df['is_weekend'] = df['day_of_week'].isin([5, 6]).astype(int)
        df['is_daylight'] = df['hour'].between(6, 20).astype(int)
        df['is_peak_solar'] = df['hour'].between(10, 16).astype(int)
        df['is_evening'] = df['hour'].between(18, 22).astype(int)
        df['is_night'] = df['hour'].isin([22, 23, 0, 1, 2, 3, 4, 5]).astype(int)
        
        # 2. BATTERY FEATURES (System-specific)
        df['soc_normalized'] = df['soc'] / 100
        df['battery_mode'] = np.sign(df['bat_power'])
        df['battery_utilization'] = np.abs(df['bat_power']) / 6000
        df['is_charging'] = (df['bat_power'] > 100).astype(int)
        df['is_discharging'] = (df['bat_power'] < -100).astype(int)
        df['battery_capacity_used'] = (100 - df['soc']) / 100
        
        # SOC patterns specific to system
        df['soc_change'] = df['soc'].diff().fillna(0)
        df['soc_change_rate'] = df['soc_change'] / 5
        
        # System-specific battery behavior
        if system_id == 1:  # Low consumption system
            df['battery_efficiency'] = np.where(df['soc'] > 80, 0.95, 0.90)
            df['expected_discharge_rate'] = np.where(df['is_evening'], 0.3, 0.1)
        else:  # High consumption system
            df['battery_efficiency'] = np.where(df['soc'] > 70, 0.92, 0.88)
            df['expected_discharge_rate'] = np.where(df['is_evening'], 0.6, 0.2)
        
        # 3. SYSTEM-SPECIFIC CONSUMPTION FEATURES
        df['consumption_pattern'] = config['consumption_pattern']
        df['avg_daily_consumption'] = config['avg_daily_consumption']
        df['grid_dependency'] = config['grid_dependency']
        df['self_sufficiency'] = config['self_sufficiency']
        
        # System-specific consumption patterns
        if system_id == 1:
            consumption_hourly = {6: 0.5, 7: 0.7, 8: 0.6, 18: 1.2, 19: 1.5, 20: 1.3, 21: 1.0}
        else:
            consumption_hourly = {6: 0.8, 7: 1.2, 8: 1.0, 18: 2.0, 19: 2.5, 20: 2.2, 21: 1.8}
        
        df['expected_consumption'] = df['hour'].map(consumption_hourly).fillna(1.0)
        
        # 4. PRODUCTION FEATURES
        df['dc_total'] = df['powerdc1'] + df['powerdc2']
        df['efficiency'] = np.where(df['dc_total'] > 0, df['ac_power'] / df['dc_total'], 0)
        df['efficiency_normalized'] = np.clip(df['efficiency'], 0, 1.2)
        
        # Grid interaction (system-specific)
        df['feedin_power_norm'] = df['feedin_power'] / 10000
        df['consume_energy_norm'] = df['consume_energy'] / config['avg_daily_consumption']
        
        # 5. WEATHER FEATURES
        df['ghi_normalized'] = df['ghi'] / 1000
        df['temperature_normalized'] = (df['temperature'] - 20) / 30
        df['cloud_cover_normalized'] = df['cloud_cover'] / 100
        
        # Weather efficiency
        df['weather_efficiency'] = df['ghi_normalized'] * (1 - df['cloud_cover_normalized'] * 0.5)
        df['temperature_efficiency'] = 1 - (df['temperature'] - 25) * 0.004
        df['temperature_efficiency'] = np.clip(df['temperature_efficiency'], 0.7, 1.1)
        
        # 6. SYSTEM-SPECIFIC INTERACTION FEATURES
        df['battery_weather_interaction'] = df['soc_normalized'] * df['weather_efficiency']
        df['consumption_weather_interaction'] = df['expected_consumption'] * df['weather_efficiency']
        df['time_battery_interaction'] = df['hour_sin'] * df['soc_normalized']
        df['efficiency_weather_interaction'] = df['efficiency_normalized'] * df['weather_efficiency']
        
        # 7. LAG FEATURES (Time-series specific)
        df['ac_power_lag_1h'] = df['ac_power'].shift(12)  # 1 hour ago (5-min intervals)
        df['soc_lag_1h'] = df['soc'].shift(12)
        df['weather_lag_1h'] = df['weather_efficiency'].shift(12)
        
        # Rolling features
        df['ac_power_rolling_mean_1h'] = df['ac_power'].rolling(window=12, min_periods=1).mean()
        df['soc_rolling_mean_1h'] = df['soc'].rolling(window=12, min_periods=1).mean()
        
        # Define system-specific feature set
        feature_columns = [
            # Temporal features
            'hour_sin', 'hour_cos', 'month_sin', 'month_cos', 
            'day_of_year_sin', 'day_of_year_cos',
            'is_weekend', 'is_daylight', 'is_peak_solar', 'is_evening', 'is_night',
            
            # Battery features
            'soc_normalized', 'battery_mode', 'battery_utilization',
            'is_charging', 'is_discharging', 'battery_capacity_used', 'soc_change_rate',
            'battery_efficiency', 'expected_discharge_rate',
            
            # System features
            'avg_daily_consumption', 'grid_dependency', 'self_sufficiency',
            'expected_consumption', 'efficiency_normalized',
            'feedin_power_norm', 'consume_energy_norm',
            
            # Weather features
            'ghi_normalized', 'temperature_normalized', 'cloud_cover_normalized',
            'weather_efficiency', 'temperature_efficiency',
            
            # Interaction features
            'battery_weather_interaction', 'consumption_weather_interaction',
            'time_battery_interaction', 'efficiency_weather_interaction',
            
            # Lag features
            'ac_power_lag_1h', 'soc_lag_1h', 'weather_lag_1h',
            'ac_power_rolling_mean_1h', 'soc_rolling_mean_1h'
        ]
        
        self.feature_columns[system_id] = feature_columns
        
        logger.info(f"✅ Created {len(feature_columns)} system-specific features for System {system_id}")
        return df
    
    def train_system_model(self, system_id):
        """Train optimized model for specific system"""
        logger.info(f"🚀 Training optimized model for System {system_id} ({self.system_configs[system_id]['name']})...")
        
        try:
            # Load system data
            df = self.load_system_data(system_id)
            
            # Create system-specific features
            df = self.create_system_specific_features(df, system_id)
            
            # Prepare training data
            feature_cols = self.feature_columns[system_id]
            df_clean = df.dropna(subset=feature_cols + ['ac_power'])
            X = df_clean[feature_cols]
            y = df_clean['ac_power']
            
            logger.info(f"System {system_id} training data: {len(X):,} samples, {len(feature_cols)} features")
            
            # Time-based split
            split_date = df_clean['timestamp'].quantile(0.8)
            train_mask = df_clean['timestamp'] <= split_date
            test_mask = df_clean['timestamp'] > split_date
            
            X_train = X[train_mask]
            X_test = X[test_mask]
            y_train = y[train_mask]
            y_test = y[test_mask]
            
            logger.info(f"System {system_id} - Train: {len(X_train):,}, Test: {len(X_test):,}")
            
            # Train system-specific ensemble
            rf_model = RandomForestRegressor(
                n_estimators=250,
                max_depth=22,
                min_samples_split=2,
                min_samples_leaf=1,
                max_features='sqrt',
                random_state=42,
                n_jobs=-1
            )
            
            xgb_model = xgb.XGBRegressor(
                n_estimators=250,
                max_depth=12,
                learning_rate=0.07,
                subsample=0.9,
                colsample_bytree=0.9,
                random_state=42,
                n_jobs=-1
            )
            
            lgb_model = lgb.LGBMRegressor(
                n_estimators=250,
                max_depth=14,
                learning_rate=0.07,
                subsample=0.9,
                colsample_bytree=0.9,
                random_state=42,
                n_jobs=-1,
                verbose=-1
            )
            
            # Create weighted ensemble
            ensemble_model = VotingRegressor([
                ('rf', rf_model),
                ('xgb', xgb_model),
                ('lgb', lgb_model)
            ])
            
            ensemble_model.fit(X_train, y_train)
            
            # Evaluate
            y_pred_train = ensemble_model.predict(X_train)
            y_pred_test = ensemble_model.predict(X_test)
            
            train_r2 = r2_score(y_train, y_pred_train)
            test_r2 = r2_score(y_test, y_pred_test)
            test_rmse = np.sqrt(mean_squared_error(y_test, y_pred_test))
            test_mae = mean_absolute_error(y_test, y_pred_test)
            
            results = {
                'system_id': system_id,
                'system_name': self.system_configs[system_id]['name'],
                'train_r2': train_r2,
                'test_r2': test_r2,
                'test_rmse': test_rmse,
                'test_mae': test_mae,
                'training_samples': len(X_train),
                'test_samples': len(X_test),
                'feature_count': len(feature_cols),
                'training_date': datetime.now().isoformat()
            }
            
            logger.info(f"✅ System {system_id} Results:")
            logger.info(f"   Test R²: {test_r2:.3f}")
            logger.info(f"   Test RMSE: {test_rmse:.1f}W")
            logger.info(f"   Test MAE: {test_mae:.1f}W")
            logger.info(f"   Training samples: {len(X_train):,}")
            
            # Save system model
            self.models[system_id] = ensemble_model
            model_path = self.models_dir / f"system_{system_id}_model.joblib"
            joblib.dump(ensemble_model, model_path)
            
            features_path = self.models_dir / f"system_{system_id}_features.json"
            with open(features_path, 'w') as f:
                json.dump(feature_cols, f, indent=2)
            
            results_path = self.models_dir / f"system_{system_id}_results.json"
            with open(results_path, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            
            logger.info(f"✅ System {system_id} model saved: {model_path}")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ System {system_id} training failed: {e}")
            import traceback
            traceback.print_exc()
            return None

def main():
    """Train separate models for both systems"""
    logger.info("🚀 ENHANCED MODEL V3 - SEPARATE SYSTEM MODELS")
    logger.info("=" * 80)
    logger.info(f"📅 Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("🎯 Objective: Train dedicated models for each solar system")
    
    try:
        separate_models = SeparateSystemModels()
        
        all_results = {}
        
        # Train models for both systems
        for system_id in [1, 2]:
            results = separate_models.train_system_model(system_id)
            if results:
                all_results[f'system_{system_id}'] = results
        
        # Save combined results
        combined_results_path = separate_models.models_dir / "combined_results.json"
        with open(combined_results_path, 'w') as f:
            json.dump(all_results, f, indent=2, default=str)
        
        logger.info("\n" + "=" * 80)
        logger.info("🎉 SEPARATE SYSTEM MODELS COMPLETE!")
        
        if all_results:
            for system_id in [1, 2]:
                if f'system_{system_id}' in all_results:
                    results = all_results[f'system_{system_id}']
                    logger.info(f"🏆 System {system_id} ({results['system_name']}):")
                    logger.info(f"   R² = {results['test_r2']:.3f}")
                    logger.info(f"   RMSE = {results['test_rmse']:.1f}W")
                    logger.info(f"   MAE = {results['test_mae']:.1f}W")
        
        logger.info("🚀 READY FOR PRODUCTION DEPLOYMENT!")
        logger.info("=" * 80)
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Separate system models training failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
