#!/usr/bin/env python3
"""
PREDICTIONS CACHE MANAGER
Scheduled predictions generation and caching system
Created: June 4, 2025
"""

import os
import sys
import json
import logging
import asyncio
import psycopg2
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from scripts.production.model_orchestrator import ModelOrchestrator

logger = logging.getLogger(__name__)

class PredictionsCacheManager:
    """
    Manages scheduled prediction generation and caching
    
    Features:
    - Pre-computes predictions for next 72 hours (hourly)
    - Pre-computes predictions for next 7 days (daily)
    - Stores in database for fast API access
    - Handles cache invalidation and updates
    - Monitors prediction quality
    """
    
    def __init__(self, db_config: Optional[Dict] = None):
        self.orchestrator = ModelOrchestrator()
        self.db_config = db_config or self._get_default_db_config()
        self.logger = logger
        
        # Cache configuration
        self.cache_config = {
            "hourly": {
                "horizon_hours": 72,  # 3 days
                "update_interval_minutes": 15,
                "granularity": "hourly"
            },
            "daily": {
                "horizon_days": 7,  # 1 week
                "update_interval_minutes": 60,
                "granularity": "daily"
            }
        }
        
        self._ensure_cache_table()
    
    def _get_default_db_config(self) -> Dict[str, str]:
        """Get default database configuration"""
        return {
            "host": os.getenv("DB_HOST", "localhost"),
            "port": os.getenv("DB_PORT", "5432"),
            "database": os.getenv("DB_NAME", "solar_prediction"),
            "user": os.getenv("DB_USER", "postgres"),
            "password": os.getenv("DB_PASSWORD", "")
        }
    
    def _get_db_connection(self):
        """Get database connection"""
        return psycopg2.connect(**self.db_config)
    
    def _ensure_cache_table(self):
        """Ensure predictions cache table exists"""
        try:
            with self._get_db_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("""
                        CREATE TABLE IF NOT EXISTS predictions_cache (
                            id SERIAL PRIMARY KEY,
                            system_id INTEGER NOT NULL,
                            timestamp TIMESTAMP NOT NULL,
                            prediction_type VARCHAR(20) NOT NULL,
                            prediction_value FLOAT NOT NULL,
                            confidence FLOAT,
                            model_used VARCHAR(50),
                            season VARCHAR(10),
                            weather_data JSONB,
                            created_at TIMESTAMP DEFAULT NOW(),
                            updated_at TIMESTAMP DEFAULT NOW(),
                            UNIQUE(system_id, timestamp, prediction_type)
                        );
                    """)
                    
                    # Create indexes for performance
                    cursor.execute("""
                        CREATE INDEX IF NOT EXISTS idx_predictions_cache_system_time 
                        ON predictions_cache(system_id, timestamp, prediction_type);
                    """)
                    
                    cursor.execute("""
                        CREATE INDEX IF NOT EXISTS idx_predictions_cache_created 
                        ON predictions_cache(created_at);
                    """)
                    
                    conn.commit()
                    
            self.logger.info("Predictions cache table ready")
            
        except Exception as e:
            self.logger.error(f"Failed to ensure cache table: {e}")
            raise
    
    def update_hourly_cache(self, systems: List[int] = [1, 2]) -> Dict[str, Any]:
        """Update hourly predictions cache"""
        try:
            start_time = datetime.now()
            cache_start = start_time.replace(minute=0, second=0, microsecond=0)
            cache_end = cache_start + timedelta(hours=self.cache_config["hourly"]["horizon_hours"])
            
            total_predictions = 0
            successful_predictions = 0
            
            with self._get_db_connection() as conn:
                with conn.cursor() as cursor:
                    for system_id in systems:
                        # Generate predictions
                        predictions = self.orchestrator.predict_range(
                            system_id=system_id,
                            start_time=cache_start,
                            end_time=cache_end,
                            granularity="hourly"
                        )
                        
                        # Store in cache
                        for prediction in predictions:
                            if prediction.get("status") == "success":
                                try:
                                    cursor.execute("""
                                        INSERT INTO predictions_cache 
                                        (system_id, timestamp, prediction_type, prediction_value, 
                                         confidence, model_used, season, weather_data, updated_at)
                                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                                        ON CONFLICT (system_id, timestamp, prediction_type)
                                        DO UPDATE SET
                                            prediction_value = EXCLUDED.prediction_value,
                                            confidence = EXCLUDED.confidence,
                                            model_used = EXCLUDED.model_used,
                                            season = EXCLUDED.season,
                                            weather_data = EXCLUDED.weather_data,
                                            updated_at = EXCLUDED.updated_at
                                    """, (
                                        system_id,
                                        prediction["timestamp"],
                                        "hourly",
                                        prediction["prediction"],
                                        prediction["confidence"],
                                        prediction["model_used"],
                                        prediction["season"],
                                        json.dumps({}),  # Weather data placeholder
                                        datetime.now()
                                    ))
                                    successful_predictions += 1
                                except Exception as e:
                                    self.logger.error(f"Failed to store prediction: {e}")
                            
                            total_predictions += 1
                    
                    conn.commit()
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            result = {
                "cache_type": "hourly",
                "systems": systems,
                "time_range": {
                    "start": cache_start.isoformat(),
                    "end": cache_end.isoformat(),
                    "hours": self.cache_config["hourly"]["horizon_hours"]
                },
                "statistics": {
                    "total_predictions": total_predictions,
                    "successful_predictions": successful_predictions,
                    "failed_predictions": total_predictions - successful_predictions,
                    "processing_time_seconds": round(processing_time, 2)
                },
                "status": "success" if successful_predictions > 0 else "failed",
                "updated_at": datetime.now().isoformat()
            }
            
            self.logger.info(f"Hourly cache updated: {successful_predictions}/{total_predictions} predictions")
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to update hourly cache: {e}")
            return {
                "cache_type": "hourly",
                "status": "failed",
                "error": str(e),
                "updated_at": datetime.now().isoformat()
            }
    
    def update_daily_cache(self, systems: List[int] = [1, 2]) -> Dict[str, Any]:
        """Update daily predictions cache"""
        try:
            start_time = datetime.now()
            cache_start = start_time.replace(hour=12, minute=0, second=0, microsecond=0)
            cache_end = cache_start + timedelta(days=self.cache_config["daily"]["horizon_days"])
            
            total_predictions = 0
            successful_predictions = 0
            
            with self._get_db_connection() as conn:
                with conn.cursor() as cursor:
                    for system_id in systems:
                        # Generate predictions
                        predictions = self.orchestrator.predict_range(
                            system_id=system_id,
                            start_time=cache_start,
                            end_time=cache_end,
                            granularity="daily"
                        )
                        
                        # Store in cache
                        for prediction in predictions:
                            if prediction.get("status") == "success":
                                try:
                                    cursor.execute("""
                                        INSERT INTO predictions_cache 
                                        (system_id, timestamp, prediction_type, prediction_value, 
                                         confidence, model_used, season, weather_data, updated_at)
                                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                                        ON CONFLICT (system_id, timestamp, prediction_type)
                                        DO UPDATE SET
                                            prediction_value = EXCLUDED.prediction_value,
                                            confidence = EXCLUDED.confidence,
                                            model_used = EXCLUDED.model_used,
                                            season = EXCLUDED.season,
                                            weather_data = EXCLUDED.weather_data,
                                            updated_at = EXCLUDED.updated_at
                                    """, (
                                        system_id,
                                        prediction["timestamp"],
                                        "daily",
                                        prediction["prediction"],
                                        prediction["confidence"],
                                        prediction["model_used"],
                                        prediction["season"],
                                        json.dumps({}),  # Weather data placeholder
                                        datetime.now()
                                    ))
                                    successful_predictions += 1
                                except Exception as e:
                                    self.logger.error(f"Failed to store prediction: {e}")
                            
                            total_predictions += 1
                    
                    conn.commit()
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            result = {
                "cache_type": "daily",
                "systems": systems,
                "time_range": {
                    "start": cache_start.isoformat(),
                    "end": cache_end.isoformat(),
                    "days": self.cache_config["daily"]["horizon_days"]
                },
                "statistics": {
                    "total_predictions": total_predictions,
                    "successful_predictions": successful_predictions,
                    "failed_predictions": total_predictions - successful_predictions,
                    "processing_time_seconds": round(processing_time, 2)
                },
                "status": "success" if successful_predictions > 0 else "failed",
                "updated_at": datetime.now().isoformat()
            }
            
            self.logger.info(f"Daily cache updated: {successful_predictions}/{total_predictions} predictions")
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to update daily cache: {e}")
            return {
                "cache_type": "daily",
                "status": "failed",
                "error": str(e),
                "updated_at": datetime.now().isoformat()
            }
    
    def get_cached_predictions(self, system_id: int, start_time: datetime, 
                              end_time: datetime, prediction_type: str = "hourly") -> List[Dict[str, Any]]:
        """Get cached predictions from database"""
        try:
            with self._get_db_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("""
                        SELECT system_id, timestamp, prediction_type, prediction_value,
                               confidence, model_used, season, created_at, updated_at
                        FROM predictions_cache
                        WHERE system_id = %s 
                          AND timestamp >= %s 
                          AND timestamp <= %s
                          AND prediction_type = %s
                        ORDER BY timestamp
                    """, (system_id, start_time, end_time, prediction_type))
                    
                    rows = cursor.fetchall()
                    
                    predictions = []
                    for row in rows:
                        predictions.append({
                            "system_id": row[0],
                            "timestamp": row[1].isoformat(),
                            "prediction_type": row[2],
                            "prediction": row[3],
                            "confidence": row[4],
                            "model_used": row[5],
                            "season": row[6],
                            "cached_at": row[7].isoformat(),
                            "updated_at": row[8].isoformat(),
                            "status": "success"
                        })
                    
                    return predictions
                    
        except Exception as e:
            self.logger.error(f"Failed to get cached predictions: {e}")
            return []
    
    def cleanup_old_cache(self, days_to_keep: int = 7) -> Dict[str, Any]:
        """Clean up old cached predictions"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            
            with self._get_db_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("""
                        DELETE FROM predictions_cache 
                        WHERE created_at < %s
                    """, (cutoff_date,))
                    
                    deleted_count = cursor.rowcount
                    conn.commit()
            
            self.logger.info(f"Cleaned up {deleted_count} old cached predictions")
            
            return {
                "operation": "cleanup",
                "deleted_count": deleted_count,
                "cutoff_date": cutoff_date.isoformat(),
                "status": "success"
            }
            
        except Exception as e:
            self.logger.error(f"Failed to cleanup cache: {e}")
            return {
                "operation": "cleanup",
                "status": "failed",
                "error": str(e)
            }
    
    def get_cache_statistics(self) -> Dict[str, Any]:
        """Get cache statistics"""
        try:
            with self._get_db_connection() as conn:
                with conn.cursor() as cursor:
                    # Get counts by type
                    cursor.execute("""
                        SELECT prediction_type, COUNT(*) as count,
                               MIN(timestamp) as earliest,
                               MAX(timestamp) as latest,
                               AVG(confidence) as avg_confidence
                        FROM predictions_cache
                        GROUP BY prediction_type
                    """)
                    
                    type_stats = {}
                    for row in cursor.fetchall():
                        type_stats[row[0]] = {
                            "count": row[1],
                            "earliest": row[2].isoformat() if row[2] else None,
                            "latest": row[3].isoformat() if row[3] else None,
                            "avg_confidence": round(row[4], 3) if row[4] else 0
                        }
                    
                    # Get total count
                    cursor.execute("SELECT COUNT(*) FROM predictions_cache")
                    total_count = cursor.fetchone()[0]
                    
                    # Get cache freshness
                    cursor.execute("""
                        SELECT prediction_type, 
                               MAX(updated_at) as last_update,
                               COUNT(*) as fresh_count
                        FROM predictions_cache
                        WHERE updated_at > NOW() - INTERVAL '1 hour'
                        GROUP BY prediction_type
                    """)
                    
                    freshness = {}
                    for row in cursor.fetchall():
                        freshness[row[0]] = {
                            "last_update": row[1].isoformat() if row[1] else None,
                            "fresh_predictions": row[2]
                        }
            
            return {
                "total_cached_predictions": total_count,
                "by_type": type_stats,
                "freshness": freshness,
                "cache_config": self.cache_config,
                "status": "operational"
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get cache statistics: {e}")
            return {
                "status": "failed",
                "error": str(e)
            }

def main():
    """Test the cache manager"""
    logging.basicConfig(level=logging.INFO)
    
    cache_manager = PredictionsCacheManager()
    
    # Update caches
    print("Updating hourly cache...")
    hourly_result = cache_manager.update_hourly_cache()
    print(json.dumps(hourly_result, indent=2))
    
    print("\nUpdating daily cache...")
    daily_result = cache_manager.update_daily_cache()
    print(json.dumps(daily_result, indent=2))
    
    # Get statistics
    print("\nCache statistics:")
    stats = cache_manager.get_cache_statistics()
    print(json.dumps(stats, indent=2))

if __name__ == "__main__":
    main()
