#!/usr/bin/env python3
"""
Weather Data Recovery
Collect missing weather data and integrate real weather into predictions
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import requests
import psycopg2
import pandas as pd
from datetime import datetime, timedelta
import logging
from dotenv import load_dotenv
import time

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WeatherDataRecovery:
    """Recover missing weather data and integrate real weather"""
    
    def __init__(self):
        load_dotenv()
        self.api_url = "https://api.open-meteo.com/v1/forecast"
        self.archive_url = "https://archive-api.open-meteo.com/v1/archive"
        self.latitude = 38.141367951893024
        self.longitude = 24.00715534164505
        
    def connect_database(self):
        """Connect to database"""
        try:
            conn = psycopg2.connect(
                host=os.getenv('DB_HOST', 'localhost'),
                database=os.getenv('DB_NAME', 'solar_prediction'),
                user=os.getenv('DB_USER', 'postgres'),
                password=os.getenv('DB_PASSWORD', 'postgres')
            )
            return conn
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            return None
    
    def collect_missing_historical_data(self, conn):
        """Collect missing weather data from April 13 to June 2, 2025"""
        logger.info("📅 COLLECTING MISSING HISTORICAL WEATHER DATA")
        logger.info("=" * 60)
        
        try:
            # Define missing period
            start_date = "2025-04-13"
            end_date = "2025-06-02"
            
            logger.info(f"📊 Collecting data from {start_date} to {end_date}")
            
            # Use archive API for historical data
            params = {
                "latitude": self.latitude,
                "longitude": self.longitude,
                "start_date": start_date,
                "end_date": end_date,
                "hourly": "temperature_2m,cloud_cover,shortwave_radiation,direct_radiation,diffuse_radiation",
                "timezone": "Europe/Athens"
            }
            
            logger.info("🌐 Calling Open-Meteo Archive API...")
            response = requests.get(self.archive_url, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            hourly_data = data.get("hourly", {})
            
            if not hourly_data:
                logger.error("❌ No hourly data received")
                return False
            
            # Process data
            timestamps = pd.to_datetime(hourly_data["time"])
            records_to_insert = []
            
            for i, timestamp in enumerate(timestamps):
                record = {
                    'timestamp': timestamp,
                    'temperature': hourly_data["temperature_2m"][i],
                    'cloud_cover': hourly_data["cloud_cover"][i],
                    'ghi': hourly_data["shortwave_radiation"][i],
                    'dni': hourly_data["direct_radiation"][i] if hourly_data["direct_radiation"][i] else 0,
                    'dhi': hourly_data["diffuse_radiation"][i] if hourly_data["diffuse_radiation"][i] else 0
                }
                records_to_insert.append(record)
            
            logger.info(f"📊 Processed {len(records_to_insert)} weather records")
            
            # Insert into database
            cursor = conn.cursor()
            
            insert_query = """
                INSERT INTO cams_radiation_data 
                (timestamp, temperature, cloud_cover, ghi, dni, dhi, source, created_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (timestamp) DO UPDATE SET
                    temperature = EXCLUDED.temperature,
                    cloud_cover = EXCLUDED.cloud_cover,
                    ghi = EXCLUDED.ghi,
                    dni = EXCLUDED.dni,
                    dhi = EXCLUDED.dhi,
                    source = EXCLUDED.source
            """
            
            inserted_count = 0
            for record in records_to_insert:
                try:
                    cursor.execute(insert_query, (
                        record['timestamp'],
                        record['temperature'],
                        record['cloud_cover'],
                        record['ghi'],
                        record['dni'],
                        record['dhi'],
                        'open-meteo-archive',
                        datetime.now()
                    ))
                    inserted_count += 1
                except Exception as e:
                    logger.warning(f"Failed to insert record for {record['timestamp']}: {e}")
            
            conn.commit()
            logger.info(f"✅ Inserted {inserted_count} weather records")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Historical data collection failed: {e}")
            return False
    
    def collect_current_weather_data(self, conn):
        """Collect current and forecast weather data"""
        logger.info("🌐 COLLECTING CURRENT & FORECAST WEATHER DATA")
        logger.info("=" * 60)
        
        try:
            # Get current + 3 days forecast
            params = {
                "latitude": self.latitude,
                "longitude": self.longitude,
                "current": "temperature_2m,cloud_cover,shortwave_radiation",
                "hourly": "temperature_2m,cloud_cover,shortwave_radiation,direct_radiation,diffuse_radiation",
                "forecast_days": 3,
                "timezone": "Europe/Athens"
            }
            
            logger.info("🌐 Calling Open-Meteo Forecast API...")
            response = requests.get(self.api_url, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            # Process current weather
            current = data.get("current", {})
            logger.info(f"🌡️ Current weather:")
            logger.info(f"   Temperature: {current.get('temperature_2m')}°C")
            logger.info(f"   Cloud cover: {current.get('cloud_cover')}%")
            logger.info(f"   Solar radiation: {current.get('shortwave_radiation')} W/m²")
            
            # Process hourly forecast
            hourly_data = data.get("hourly", {})
            if hourly_data:
                timestamps = pd.to_datetime(hourly_data["time"])
                
                cursor = conn.cursor()
                insert_query = """
                    INSERT INTO cams_radiation_data 
                    (timestamp, temperature, cloud_cover, ghi, dni, dhi, source, created_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (timestamp) DO UPDATE SET
                        temperature = EXCLUDED.temperature,
                        cloud_cover = EXCLUDED.cloud_cover,
                        ghi = EXCLUDED.ghi,
                        dni = EXCLUDED.dni,
                        dhi = EXCLUDED.dhi,
                        source = EXCLUDED.source
                """
                
                inserted_count = 0
                for i, timestamp in enumerate(timestamps):
                    try:
                        cursor.execute(insert_query, (
                            timestamp,
                            hourly_data["temperature_2m"][i],
                            hourly_data["cloud_cover"][i],
                            hourly_data["shortwave_radiation"][i],
                            hourly_data["direct_radiation"][i] if hourly_data["direct_radiation"][i] else 0,
                            hourly_data["diffuse_radiation"][i] if hourly_data["diffuse_radiation"][i] else 0,
                            'open-meteo-forecast',
                            datetime.now()
                        ))
                        inserted_count += 1
                    except Exception as e:
                        logger.warning(f"Failed to insert forecast for {timestamp}: {e}")
                
                conn.commit()
                logger.info(f"✅ Inserted {inserted_count} forecast records")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Current weather collection failed: {e}")
            return False
    
    def validate_weather_data_coverage(self, conn):
        """Validate weather data coverage"""
        logger.info("✅ VALIDATING WEATHER DATA COVERAGE")
        logger.info("=" * 60)
        
        try:
            cursor = conn.cursor()
            
            # Check overall coverage
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_records,
                    MIN(timestamp) as earliest,
                    MAX(timestamp) as latest,
                    COUNT(CASE WHEN temperature IS NOT NULL THEN 1 END) as temp_records,
                    COUNT(CASE WHEN cloud_cover IS NOT NULL THEN 1 END) as cloud_records,
                    COUNT(CASE WHEN ghi IS NOT NULL AND ghi > 0 THEN 1 END) as ghi_records
                FROM cams_radiation_data
            """)
            
            result = cursor.fetchone()
            if result:
                total, earliest, latest, temp_count, cloud_count, ghi_count = result
                
                logger.info(f"📊 Weather data coverage:")
                logger.info(f"   Total records: {total:,}")
                logger.info(f"   Date range: {earliest} to {latest}")
                logger.info(f"   Temperature data: {temp_count:,} ({temp_count/total*100:.1f}%)")
                logger.info(f"   Cloud cover data: {cloud_count:,} ({cloud_count/total*100:.1f}%)")
                logger.info(f"   Solar radiation data: {ghi_count:,} ({ghi_count/total*100:.1f}%)")
            
            # Check recent data quality
            cursor.execute("""
                SELECT 
                    DATE(timestamp) as date,
                    COUNT(*) as records,
                    AVG(temperature) as avg_temp,
                    AVG(cloud_cover) as avg_cloud,
                    AVG(ghi) as avg_ghi
                FROM cams_radiation_data
                WHERE timestamp >= NOW() - INTERVAL '7 days'
                AND temperature IS NOT NULL
                GROUP BY DATE(timestamp)
                ORDER BY date DESC
            """)
            
            recent_data = cursor.fetchall()
            if recent_data:
                logger.info(f"\n📈 Recent data quality (last 7 days):")
                logger.info("   Date       | Records | Temp | Cloud | GHI")
                logger.info("   " + "-" * 45)
                for date, records, temp, cloud, ghi in recent_data:
                    logger.info(f"   {date} | {records:7d} | {temp:4.1f} | {cloud:5.0f} | {ghi:3.0f}")
            
            # Check for gaps
            cursor.execute("""
                SELECT 
                    COUNT(*) as gap_count
                FROM cams_radiation_data
                WHERE timestamp >= '2025-04-13'
                AND timestamp <= '2025-06-02'
                AND (temperature IS NULL OR cloud_cover IS NULL)
            """)
            
            gap_result = cursor.fetchone()
            if gap_result and gap_result[0] > 0:
                logger.warning(f"⚠️ Found {gap_result[0]} records with missing data")
            else:
                logger.info("✅ No significant data gaps found")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Data validation failed: {e}")
            return False
    
    def test_weather_integration(self, conn):
        """Test weather data integration for predictions"""
        logger.info("🔮 TESTING WEATHER INTEGRATION")
        logger.info("=" * 60)
        
        try:
            cursor = conn.cursor()
            
            # Test getting weather for specific times
            test_times = [
                datetime.now().replace(hour=12, minute=0, second=0, microsecond=0),
                datetime.now().replace(hour=15, minute=0, second=0, microsecond=0)
            ]
            
            for test_time in test_times:
                cursor.execute("""
                    SELECT temperature, cloud_cover, ghi, dni, dhi
                    FROM cams_radiation_data
                    WHERE timestamp = %s
                """, (test_time,))
                
                result = cursor.fetchone()
                if result:
                    temp, cloud, ghi, dni, dhi = result
                    logger.info(f"✅ Weather for {test_time}:")
                    logger.info(f"   Temperature: {temp}°C")
                    logger.info(f"   Cloud cover: {cloud}%")
                    logger.info(f"   GHI: {ghi} W/m²")
                else:
                    logger.warning(f"⚠️ No weather data for {test_time}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Weather integration test failed: {e}")
            return False
    
    def run_weather_recovery(self):
        """Run complete weather data recovery"""
        logger.info("🚀 WEATHER DATA RECOVERY")
        logger.info("=" * 80)
        logger.info("🎯 Recovering missing weather data and enabling real weather integration")
        
        try:
            # Connect to database
            conn = self.connect_database()
            if not conn:
                return False
            
            # Step 1: Collect missing historical data
            if not self.collect_missing_historical_data(conn):
                logger.error("❌ Historical data collection failed")
                return False
            
            # Step 2: Collect current and forecast data
            if not self.collect_current_weather_data(conn):
                logger.error("❌ Current weather collection failed")
                return False
            
            # Step 3: Validate data coverage
            if not self.validate_weather_data_coverage(conn):
                logger.error("❌ Data validation failed")
                return False
            
            # Step 4: Test integration
            if not self.test_weather_integration(conn):
                logger.error("❌ Integration test failed")
                return False
            
            conn.close()
            
            # Success summary
            logger.info("\n" + "=" * 80)
            logger.info("🎉 WEATHER DATA RECOVERY COMPLETE")
            logger.info("=" * 80)
            logger.info("✅ Missing historical data collected")
            logger.info("✅ Current weather data updated")
            logger.info("✅ Data coverage validated")
            logger.info("✅ Weather integration tested")
            logger.info("🚀 Ready for weather-integrated predictions!")
            logger.info("=" * 80)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Weather recovery failed: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """Execute weather data recovery"""
    recovery = WeatherDataRecovery()
    success = recovery.run_weather_recovery()
    
    if success:
        print("\n🎯 Weather data recovery completed successfully!")
        print("🌐 Real weather data is now available for predictions")
        return True
    else:
        print("\n❌ Weather data recovery failed!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
