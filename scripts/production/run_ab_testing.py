#!/usr/bin/env python3
"""
Production A/B Testing Execution
Runs comprehensive A/B testing between legacy and multi-source systems
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import json

from src.testing.ab_testing_framework import ABTestingFramework

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_comprehensive_ab_testing():
    """Run comprehensive A/B testing suite"""
    
    print("🧪 Comprehensive A/B Testing Execution")
    print("=" * 60)
    print(f"📅 Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        # Initialize A/B testing framework
        ab_tester = ABTestingFramework()
        
        # Define test scenarios
        test_scenarios = [
            {
                'name': '7_day_comparison',
                'description': '7-day performance comparison',
                'days_back': 7
            },
            {
                'name': '14_day_comparison', 
                'description': '14-day performance comparison',
                'days_back': 14
            },
            {
                'name': '30_day_comparison',
                'description': '30-day performance comparison', 
                'days_back': 30
            }
        ]
        
        all_results = {}
        
        for scenario in test_scenarios:
            print(f"\n🔄 Running: {scenario['description']}")
            print("-" * 40)
            
            # Calculate date range
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=scenario['days_back'])).strftime('%Y-%m-%d')
            
            print(f"📅 Period: {start_date} to {end_date}")
            
            try:
                # Run A/B test
                results = ab_tester.run_ab_test(start_date, end_date, scenario['name'])
                all_results[scenario['name']] = results
                
                # Display results
                print(f"🎯 Results for {scenario['description']}:")
                print(f"   Winner: {results.get('winner', 'unknown')}")
                print(f"   Confidence: {results.get('confidence', 'unknown')}")
                
                if 'comparison' in results and 'error' not in results['comparison']:
                    comp = results['comparison']
                    print(f"   MAE Improvement: {comp.get('mae_improvement_pct', 0):+.1f}%")
                    print(f"   RMSE Improvement: {comp.get('rmse_improvement_pct', 0):+.1f}%")
                    print(f"   R² Improvement: {comp.get('r2_improvement_pct', 0):+.1f}%")
                
                # Save individual test results
                ab_tester.save_test_results(scenario['name'])
                print(f"   ✅ Results saved")
                
            except Exception as e:
                print(f"   ❌ Test failed: {e}")
                logger.exception(f"A/B test failed for {scenario['name']}")
                all_results[scenario['name']] = {'error': str(e)}
        
        # Generate comprehensive report
        print(f"\n📊 Comprehensive A/B Testing Report")
        print("=" * 60)
        
        successful_tests = [name for name, result in all_results.items() if 'error' not in result]
        failed_tests = [name for name, result in all_results.items() if 'error' in result]
        
        print(f"✅ Successful tests: {len(successful_tests)}/{len(test_scenarios)}")
        print(f"❌ Failed tests: {len(failed_tests)}")
        
        if successful_tests:
            print(f"\n🏆 Winner Analysis:")
            
            winners = {}
            for test_name in successful_tests:
                result = all_results[test_name]
                winner = result.get('winner', 'unknown')
                if winner not in winners:
                    winners[winner] = 0
                winners[winner] += 1
            
            for winner, count in winners.items():
                print(f"   {winner}: {count} wins")
            
            # Overall recommendation
            if 'multi_source_system' in winners and winners['multi_source_system'] >= len(successful_tests) / 2:
                print(f"\n🎉 RECOMMENDATION: Deploy Multi-Source System")
                print(f"   Multi-source system wins in {winners.get('multi_source_system', 0)}/{len(successful_tests)} tests")
            elif 'legacy_system' in winners and winners['legacy_system'] >= len(successful_tests) / 2:
                print(f"\n⚠️ RECOMMENDATION: Keep Legacy System")
                print(f"   Legacy system wins in {winners.get('legacy_system', 0)}/{len(successful_tests)} tests")
            else:
                print(f"\n🤔 RECOMMENDATION: Inconclusive")
                print(f"   Results are mixed, need more testing or tuning")
        
        # Save comprehensive report
        comprehensive_report = {
            'test_execution_time': datetime.now().isoformat(),
            'scenarios_tested': len(test_scenarios),
            'successful_tests': len(successful_tests),
            'failed_tests': len(failed_tests),
            'test_results': all_results,
            'summary': {
                'winners': winners if successful_tests else {},
                'recommendation': 'multi_source_system' if successful_tests and winners.get('multi_source_system', 0) >= len(successful_tests) / 2 else 'inconclusive'
            }
        }
        
        # Save report
        os.makedirs('logs/ab_testing', exist_ok=True)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = f'logs/ab_testing/comprehensive_ab_report_{timestamp}.json'
        
        with open(report_file, 'w') as f:
            json.dump(comprehensive_report, f, indent=2)
        
        print(f"\n📋 Comprehensive report saved to: {report_file}")
        
        return len(successful_tests) > 0
        
    except Exception as e:
        print(f"\n❌ A/B testing execution failed: {e}")
        logger.exception("A/B testing execution failed")
        return False

def main():
    """Main function"""
    
    success = run_comprehensive_ab_testing()
    
    if success:
        print("\n🎉 A/B Testing completed successfully!")
        print("📊 Review the results to make deployment decisions")
    else:
        print("\n❌ A/B Testing failed!")
        print("🔧 Check logs and fix issues before proceeding")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
