#!/usr/bin/env python3
"""
Comprehensive Model Re-evaluation
Test all available models with correct weather data to find the best combination
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import requests
import psycopg2
import pandas as pd
import numpy as np
import joblib
import json
import lightgbm as lgb
import xgboost as xgb
from pathlib import Path
from datetime import datetime, timedelta
import logging
from dotenv import load_dotenv
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ComprehensiveModelReEvaluator:
    """Re-evaluate all models with correct weather data"""
    
    def __init__(self):
        load_dotenv()
        self.project_root = Path("/home/<USER>/solar-prediction-project")
        self.models_dir = self.project_root / "models"
        
        # Real data for validation
        self.real_data = {
            '2025-06-01': {'system1': 72.8, 'system2': 67.7},
            '2025-06-02': {'system1': 31.8, 'system2': 34.0}  # partial day
        }
        
        # Weather API configuration
        self.api_url = "https://api.open-meteo.com/v1/forecast"
        self.latitude = 38.141367951893024
        self.longitude = 24.00715534164505
        
        self.discovered_models = {}
        self.evaluation_results = {}
        
    def connect_database(self):
        """Connect to database"""
        try:
            conn = psycopg2.connect(
                host=os.getenv('DB_HOST', 'localhost'),
                database=os.getenv('DB_NAME', 'solar_prediction'),
                user=os.getenv('DB_USER', 'postgres'),
                password=os.getenv('DB_PASSWORD', 'postgres')
            )
            return conn
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            return None
    
    def discover_all_models(self):
        """Discover all available models in the models directory"""
        logger.info("🔍 DISCOVERING ALL AVAILABLE MODELS")
        logger.info("=" * 60)
        
        model_patterns = {
            'lightgbm': ['lightgbm_model.txt', '*.joblib'],
            'xgboost': ['xgboost_model.json', '*.joblib'],
            'ensemble': ['*ensemble*.joblib', '*final*.joblib'],
            'daily_yield': ['daily_yield_model.joblib'],
            'enhanced': ['enhanced*.joblib', 'enhanced*.pkl'],
            'random_forest': ['random_forest*.pkl', '*rf*.joblib'],
            'neural_network': ['*neural*.joblib', '*nn*.pkl']
        }
        
        discovered = {}
        
        # Scan all model directories
        for model_dir in self.models_dir.iterdir():
            if model_dir.is_dir() and not model_dir.name.startswith('.'):
                logger.info(f"📁 Scanning {model_dir.name}...")
                
                # Look for model files
                model_files = []
                feature_files = []
                scaler_files = []
                
                for file in model_dir.rglob('*'):
                    if file.is_file():
                        name = file.name.lower()
                        
                        # Model files
                        if any(ext in name for ext in ['.joblib', '.pkl', '.txt', '.json']) and 'model' in name:
                            model_files.append(file)
                        
                        # Feature files
                        if 'feature' in name and name.endswith('.json'):
                            feature_files.append(file)
                        
                        # Scaler files
                        if 'scaler' in name and any(ext in name for ext in ['.joblib', '.pkl']):
                            scaler_files.append(file)
                
                # Create model entries
                for model_file in model_files:
                    model_name = f"{model_dir.name}_{model_file.stem}"
                    
                    # Find corresponding feature and scaler files
                    feature_file = None
                    scaler_file = None
                    
                    for ff in feature_files:
                        if ff.parent == model_file.parent:
                            feature_file = ff
                            break
                    
                    for sf in scaler_files:
                        if sf.parent == model_file.parent:
                            scaler_file = sf
                            break
                    
                    # Determine model type
                    model_type = 'unknown'
                    if 'lightgbm' in model_file.name.lower():
                        model_type = 'lightgbm'
                    elif 'xgboost' in model_file.name.lower():
                        model_type = 'xgboost'
                    elif 'ensemble' in model_file.name.lower():
                        model_type = 'ensemble'
                    elif 'daily_yield' in model_file.name.lower():
                        model_type = 'daily_yield'
                    elif any(x in model_file.name.lower() for x in ['rf', 'random_forest']):
                        model_type = 'random_forest'
                    elif 'neural' in model_file.name.lower():
                        model_type = 'neural_network'
                    elif model_file.suffix == '.joblib':
                        model_type = 'sklearn'
                    elif model_file.suffix == '.txt':
                        model_type = 'lightgbm'
                    elif model_file.suffix == '.json':
                        model_type = 'xgboost'
                    
                    discovered[model_name] = {
                        'model_path': model_file,
                        'features_path': feature_file,
                        'scaler_path': scaler_file,
                        'model_type': model_type,
                        'directory': model_dir.name,
                        'description': f"{model_type.title()} model from {model_dir.name}"
                    }
                    
                    logger.info(f"   ✅ Found: {model_name} ({model_type})")
        
        logger.info(f"\n📊 Total models discovered: {len(discovered)}")
        self.discovered_models = discovered
        return discovered
    
    def get_real_weather_data(self, target_date):
        """Get real weather data for specific date"""
        try:
            # Try database first
            conn = self.connect_database()
            if conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT 
                        AVG(COALESCE(temperature, 25)) as avg_temperature,
                        AVG(COALESCE(cloud_cover, 30)) as avg_cloud_cover,
                        AVG(COALESCE(ghi, 650)) as avg_ghi,
                        MAX(COALESCE(ghi, 800)) as max_ghi
                    FROM cams_radiation_data
                    WHERE DATE(timestamp) = %s
                """, (target_date,))
                
                result = cursor.fetchone()
                conn.close()
                
                if result and result[0]:
                    return {
                        'avg_temperature': result[0],
                        'avg_cloud_cover': result[1],
                        'avg_ghi': result[2],
                        'max_ghi': result[3]
                    }
            
            # Fallback to API for future dates
            if target_date >= datetime.now().date():
                params = {
                    "latitude": self.latitude,
                    "longitude": self.longitude,
                    "daily": "temperature_2m_mean,cloud_cover_mean,shortwave_radiation_sum",
                    "start_date": str(target_date),
                    "end_date": str(target_date),
                    "timezone": "Europe/Athens"
                }
                
                response = requests.get(self.api_url, params=params, timeout=10)
                response.raise_for_status()
                
                data = response.json()
                daily = data.get("daily", {})
                
                if daily and len(daily.get("time", [])) > 0:
                    return {
                        'avg_temperature': daily["temperature_2m_mean"][0] or 27,
                        'avg_cloud_cover': daily["cloud_cover_mean"][0] or 20,
                        'avg_ghi': daily["shortwave_radiation_sum"][0] / 24 or 700,
                        'max_ghi': daily["shortwave_radiation_sum"][0] / 12 or 900
                    }
            
            # Default values
            return {
                'avg_temperature': 27,
                'avg_cloud_cover': 20,
                'avg_ghi': 700,
                'max_ghi': 900
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to get weather data: {e}")
            return {
                'avg_temperature': 27,
                'avg_cloud_cover': 20,
                'avg_ghi': 700,
                'max_ghi': 900
            }
    
    def load_model(self, model_info):
        """Load a specific model"""
        try:
            model_path = model_info['model_path']
            model_type = model_info['model_type']
            
            if model_type == 'lightgbm' and model_path.suffix == '.txt':
                model = lgb.Booster(model_file=str(model_path))
            elif model_type == 'xgboost' and model_path.suffix == '.json':
                model = xgb.Booster()
                model.load_model(str(model_path))
            else:
                model = joblib.load(model_path)
            
            # Load features if available
            features = None
            if model_info['features_path'] and model_info['features_path'].exists():
                with open(model_info['features_path'], 'r') as f:
                    features = json.load(f)
            
            # Load scaler if available
            scaler = None
            if model_info['scaler_path'] and model_info['scaler_path'].exists():
                scaler = joblib.load(model_info['scaler_path'])
            
            return model, features, scaler
            
        except Exception as e:
            logger.error(f"❌ Failed to load model {model_info['model_path']}: {e}")
            return None, None, None
    
    def create_features_for_model(self, target_date, system_id, weather_data, feature_list=None):
        """Create features for model prediction"""
        try:
            # If no feature list provided, create comprehensive features
            if not feature_list:
                return self.create_comprehensive_features(target_date, system_id, weather_data)
            
            # Create features based on the model's expected feature list
            features = {}
            
            # Temporal features
            date_obj = pd.to_datetime(target_date)
            hour = 12  # Assume noon for daily prediction
            month = date_obj.month
            day_of_year = date_obj.dayofyear
            
            # Basic temporal
            features.update({
                'hour': hour,
                'month': month,
                'day_of_year': day_of_year,
                'hour_sin': np.sin(2 * np.pi * hour / 24),
                'hour_cos': np.cos(2 * np.pi * hour / 24),
                'month_sin': np.sin(2 * np.pi * month / 12),
                'month_cos': np.cos(2 * np.pi * month / 12),
                'day_sin': np.sin(2 * np.pi * day_of_year / 365),
                'day_cos': np.cos(2 * np.pi * day_of_year / 365)
            })
            
            # System features
            features.update({
                'system_id': system_id,
                'system_1': 1 if system_id == 1 else 0,
                'system_2': 1 if system_id == 2 else 0
            })
            
            # Battery features
            features.update({
                'soc': 75.0,
                'soc_normalized': 0.75,
                'bat_power': -500.0,
                'battery_mode': -1,
                'is_charging': 0,
                'is_discharging': 1,
                'battery_utilization': 0.08,
                'avg_soc_norm': 0.75,
                'avg_discharge_norm': 0.15,
                'avg_charge_norm': 0.08
            })
            
            # Weather features
            features.update({
                'temperature': weather_data['avg_temperature'],
                'cloud_cover': weather_data['avg_cloud_cover'],
                'ghi': weather_data['avg_ghi'],
                'dni': weather_data['avg_ghi'] * 0.8,
                'dhi': weather_data['avg_ghi'] * 0.2,
                'ghi_norm': weather_data['avg_ghi'] / 1000,
                'temp_norm': (weather_data['avg_temperature'] - 20) / 30,
                'cloud_norm': weather_data['avg_cloud_cover'] / 100,
                'avg_ghi_norm': weather_data['avg_ghi'] / 1000,
                'max_ghi_norm': weather_data['max_ghi'] / 1000,
                'weather_efficiency': weather_data['avg_ghi'] / 1000 * (1 - weather_data['avg_cloud_cover'] / 100 * 0.5),
                'temp_efficiency': 1 - (weather_data['avg_temperature'] - 25) * 0.004
            })
            
            # Time indicators
            features.update({
                'is_daylight': 1,
                'is_peak_solar': 1,
                'is_evening': 0,
                'is_summer': 1 if month in [6, 7, 8] else 0,
                'is_winter': 1 if month in [12, 1, 2] else 0
            })
            
            # Production features
            features.update({
                'powerdc1': 3000,
                'powerdc2': 2800,
                'dc_total': 5800,
                'efficiency': 0.9,
                'efficiency_clipped': 0.9,
                'panel_efficiency': 0.2,
                'inverter_efficiency': 0.95
            })
            
            # Interaction features
            features.update({
                'battery_weather': features['soc_normalized'] * features['weather_efficiency'],
                'system_weather': system_id * features['weather_efficiency'],
                'time_battery': features['hour_sin'] * features['soc_normalized'],
                'battery_weather_interaction': features['soc_normalized'] * features['weather_efficiency']
            })
            
            # Create feature vector based on expected features
            feature_vector = []
            for feature_name in feature_list:
                if feature_name in features:
                    feature_vector.append(features[feature_name])
                else:
                    # Default value for missing features
                    feature_vector.append(0.0)
            
            return np.array(feature_vector)
            
        except Exception as e:
            logger.error(f"❌ Feature creation failed: {e}")
            return np.zeros(len(feature_list) if feature_list else 20)
    
    def create_comprehensive_features(self, target_date, system_id, weather_data):
        """Create comprehensive feature set for models without specific feature lists"""
        date_obj = pd.to_datetime(target_date)
        month = date_obj.month
        day_of_year = date_obj.dayofyear
        
        return np.array([
            system_id,  # system_id
            1 if system_id == 1 else 0,  # system_1
            1 if system_id == 2 else 0,  # system_2
            np.sin(2 * np.pi * month / 12),  # month_sin
            np.cos(2 * np.pi * month / 12),  # month_cos
            np.sin(2 * np.pi * day_of_year / 365),  # day_sin
            np.cos(2 * np.pi * day_of_year / 365),  # day_cos
            1 if month in [6, 7, 8] else 0,  # is_summer
            1 if month in [12, 1, 2] else 0,  # is_winter
            0.75,  # avg_soc_norm
            0.15,  # avg_discharge_norm
            0.08,  # avg_charge_norm
            weather_data['avg_ghi'] / 1000,  # avg_ghi_norm
            weather_data['max_ghi'] / 1000,  # max_ghi_norm
            (weather_data['avg_temperature'] - 20) / 30,  # temp_norm
            weather_data['avg_cloud_cover'] / 100,  # cloud_norm
            1 - (weather_data['avg_temperature'] - 25) * 0.004  # temp_efficiency
        ])
    
    def evaluate_model_with_real_weather(self, model_name, model_info):
        """Evaluate a specific model with real weather data"""
        logger.info(f"🔬 Evaluating {model_name}...")
        
        try:
            # Load model
            model, features, scaler = self.load_model(model_info)
            
            if model is None:
                logger.warning(f"⚠️ Could not load {model_name}")
                return None
            
            # Generate predictions for next 2 days
            predictions = {}
            
            for day_offset in range(2):
                target_date = datetime.now().date() + timedelta(days=day_offset + 1)
                date_str = target_date.strftime('%Y-%m-%d')
                
                # Get real weather data
                weather_data = self.get_real_weather_data(target_date)
                
                predictions[date_str] = {
                    'weather': weather_data,
                    'systems': {}
                }
                
                # Predict for both systems
                for system_id in [1, 2]:
                    try:
                        # Create features
                        feature_vector = self.create_features_for_model(
                            target_date, system_id, weather_data, features
                        )
                        
                        # Scale features if scaler available
                        if scaler:
                            feature_vector = scaler.transform([feature_vector])[0]
                        
                        # Make prediction
                        if model_info['model_type'] == 'lightgbm' and hasattr(model, 'predict'):
                            if len(feature_vector.shape) == 1:
                                prediction = model.predict([feature_vector])[0]
                            else:
                                prediction = model.predict(feature_vector)[0]
                        elif model_info['model_type'] == 'xgboost':
                            import xgboost as xgb
                            dmatrix = xgb.DMatrix([feature_vector])
                            prediction = model.predict(dmatrix)[0]
                        else:
                            prediction = model.predict([feature_vector])[0]
                        
                        # Ensure reasonable range
                        prediction = max(0, min(prediction, 100))  # 0-100 kWh range
                        
                        predictions[date_str]['systems'][f'system{system_id}'] = {
                            'predicted_yield_kwh': prediction,
                            'confidence': 'Medium',
                            'features_used': len(feature_vector)
                        }
                        
                    except Exception as e:
                        logger.warning(f"   ⚠️ Prediction failed for system {system_id}: {e}")
                        predictions[date_str]['systems'][f'system{system_id}'] = {
                            'predicted_yield_kwh': 0,
                            'confidence': 'Low',
                            'error': str(e)
                        }
            
            # Calculate accuracy against known data
            accuracy_scores = []
            
            # Compare with June 1 real data if model can predict historical
            try:
                june1_weather = self.get_real_weather_data(datetime(2025, 6, 1).date())
                
                for system_id in [1, 2]:
                    feature_vector = self.create_features_for_model(
                        datetime(2025, 6, 1).date(), system_id, june1_weather, features
                    )
                    
                    if scaler:
                        feature_vector = scaler.transform([feature_vector])[0]
                    
                    if model_info['model_type'] == 'lightgbm' and hasattr(model, 'predict'):
                        pred = model.predict([feature_vector])[0]
                    elif model_info['model_type'] == 'xgboost':
                        import xgboost as xgb
                        dmatrix = xgb.DMatrix([feature_vector])
                        pred = model.predict(dmatrix)[0]
                    else:
                        pred = model.predict([feature_vector])[0]
                    
                    pred = max(0, min(pred, 100))
                    real_value = self.real_data['2025-06-01'][f'system{system_id}']
                    
                    if real_value > 0:
                        accuracy = (1 - abs(pred - real_value) / real_value) * 100
                        accuracy_scores.append(accuracy)
                        
            except Exception as e:
                logger.warning(f"   ⚠️ Historical validation failed: {e}")
            
            avg_accuracy = np.mean(accuracy_scores) if accuracy_scores else 0
            
            result = {
                'model_name': model_name,
                'model_info': model_info,
                'predictions': predictions,
                'validation': {
                    'accuracy_scores': accuracy_scores,
                    'average_accuracy': avg_accuracy,
                    'validation_method': 'June 1 real data comparison'
                },
                'features_count': len(features) if features else 'Unknown',
                'has_scaler': scaler is not None,
                'status': 'Success'
            }
            
            logger.info(f"   ✅ {model_name}: Avg accuracy {avg_accuracy:.1f}%")
            
            return result
            
        except Exception as e:
            logger.error(f"   ❌ {model_name} evaluation failed: {e}")
            return {
                'model_name': model_name,
                'model_info': model_info,
                'status': 'Failed',
                'error': str(e)
            }
    
    def run_comprehensive_reevaluation(self):
        """Run comprehensive re-evaluation of all models"""
        logger.info("🚀 COMPREHENSIVE MODEL RE-EVALUATION")
        logger.info("=" * 80)
        logger.info("🎯 Testing all models with correct weather data")
        
        try:
            # Discover all models
            discovered = self.discover_all_models()
            
            if not discovered:
                logger.error("❌ No models discovered")
                return False
            
            # Evaluate each model
            logger.info(f"\n🔬 EVALUATING {len(discovered)} MODELS")
            logger.info("=" * 80)
            
            results = {}
            successful_evaluations = 0
            
            for model_name, model_info in discovered.items():
                result = self.evaluate_model_with_real_weather(model_name, model_info)
                
                if result and result.get('status') == 'Success':
                    results[model_name] = result
                    successful_evaluations += 1
                elif result:
                    results[model_name] = result
            
            # Find best performing models
            successful_models = {k: v for k, v in results.items() if v.get('status') == 'Success'}
            
            if successful_models:
                best_model = max(successful_models.items(), 
                               key=lambda x: x[1]['validation']['average_accuracy'])
                
                logger.info(f"\n🏆 BEST PERFORMING MODEL:")
                logger.info(f"   Model: {best_model[0]}")
                logger.info(f"   Accuracy: {best_model[1]['validation']['average_accuracy']:.1f}%")
                logger.info(f"   Type: {best_model[1]['model_info']['model_type']}")
            
            # Compile final results
            final_results = {
                'evaluation_date': datetime.now().isoformat(),
                'total_models_discovered': len(discovered),
                'successful_evaluations': successful_evaluations,
                'failed_evaluations': len(discovered) - successful_evaluations,
                'best_model': best_model[0] if successful_models else None,
                'model_results': results,
                'summary': {
                    'weather_integration': 'Real weather data used',
                    'evaluation_method': 'June 1 real data validation',
                    'prediction_target': 'Next 2 days daily yield'
                }
            }
            
            # Save results
            results_file = f"test/results/comprehensive_model_reevaluation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            os.makedirs(os.path.dirname(results_file), exist_ok=True)
            
            with open(results_file, 'w') as f:
                json.dump(final_results, f, indent=2, default=str)
            
            # Display summary
            logger.info("\n" + "=" * 80)
            logger.info("🎉 COMPREHENSIVE RE-EVALUATION COMPLETE")
            logger.info("=" * 80)
            logger.info(f"📊 Models discovered: {len(discovered)}")
            logger.info(f"✅ Successful evaluations: {successful_evaluations}")
            logger.info(f"❌ Failed evaluations: {len(discovered) - successful_evaluations}")
            
            if successful_models:
                logger.info(f"🏆 Best model: {best_model[0]} ({best_model[1]['validation']['average_accuracy']:.1f}% accuracy)")
                
                # Show top 3 models
                sorted_models = sorted(successful_models.items(), 
                                     key=lambda x: x[1]['validation']['average_accuracy'], 
                                     reverse=True)
                
                logger.info("\n🥇 TOP 3 MODELS:")
                for i, (name, data) in enumerate(sorted_models[:3]):
                    logger.info(f"   {i+1}. {name}: {data['validation']['average_accuracy']:.1f}% accuracy")
            
            logger.info(f"\n💾 Results saved: {results_file}")
            logger.info("=" * 80)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Comprehensive re-evaluation failed: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """Execute comprehensive model re-evaluation"""
    evaluator = ComprehensiveModelReEvaluator()
    success = evaluator.run_comprehensive_reevaluation()
    
    if success:
        print("\n🎯 Comprehensive model re-evaluation completed successfully!")
        print("🌐 All models tested with correct weather data")
        return True
    else:
        print("\n❌ Comprehensive model re-evaluation failed!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
