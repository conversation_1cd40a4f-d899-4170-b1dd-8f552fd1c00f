#!/usr/bin/env python3
"""
Production Deployment Script for Multi-Source System
Deploys the complete multi-source data integration and ML pipeline
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import subprocess
import json
from datetime import datetime, timed<PERSON>ta
from typing import Dict
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ProductionDeployer:
    """Production deployment manager"""
    
    def __init__(self):
        self.project_root = '/home/<USER>/solar-prediction-project'
        self.deployment_log = []
    
    def log_step(self, step: str, status: str, details: str = ""):
        """Log deployment step"""
        entry = {
            'timestamp': datetime.now().isoformat(),
            'step': step,
            'status': status,
            'details': details
        }
        self.deployment_log.append(entry)
        
        status_icon = "✅" if status == "success" else "❌" if status == "failed" else "🔄"
        print(f"   {status_icon} {step}: {status}")
        if details:
            print(f"      {details}")
    
    def check_prerequisites(self) -> bool:
        """Check system prerequisites"""
        
        print("🔍 Checking Prerequisites...")
        
        # Check Python packages
        required_packages = [
            ('pandas', 'pandas'),
            ('numpy', 'numpy'),
            ('sklearn', 'scikit-learn'),
            ('xgboost', 'xgboost'),
            ('psycopg2', 'psycopg2'),
            ('joblib', 'joblib')
        ]

        missing_packages = []
        for import_name, package_name in required_packages:
            try:
                __import__(import_name)
                self.log_step(f"Package {package_name}", "success")
            except ImportError:
                missing_packages.append(package_name)
                self.log_step(f"Package {package_name}", "failed", "Not installed")
        
        if missing_packages:
            self.log_step("Prerequisites", "failed", f"Missing packages: {missing_packages}")
            return False
        
        # Check database connection
        try:
            import psycopg2
            conn = psycopg2.connect(
                host='localhost',
                database='solar_prediction',
                user='postgres',
                password='postgres'
            )
            conn.close()
            self.log_step("Database connection", "success")
        except Exception as e:
            self.log_step("Database connection", "failed", str(e))
            return False
        
        # Check directory structure
        required_dirs = [
            'src/data_integration',
            'src/models',
            'src/monitoring',
            'scripts/data',
            'models',
            'logs'
        ]
        
        for dir_path in required_dirs:
            full_path = os.path.join(self.project_root, dir_path)
            if os.path.exists(full_path):
                self.log_step(f"Directory {dir_path}", "success")
            else:
                os.makedirs(full_path, exist_ok=True)
                self.log_step(f"Directory {dir_path}", "success", "Created")
        
        self.log_step("Prerequisites check", "success")
        return True
    
    def deploy_data_integration(self) -> bool:
        """Deploy data integration system"""
        
        print("\n🔄 Deploying Data Integration System...")
        
        try:
            # Test multi-source manager
            from src.data_integration.multi_source_manager import MultiSourceDataManager
            
            manager = MultiSourceDataManager()
            self.log_step("Multi-source manager", "success", "Initialized successfully")
            
            # Test data retrieval
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=1)
            
            data_points = manager.get_available_data('temperature', start_time, end_time)
            self.log_step("Data retrieval test", "success", f"Retrieved {len(data_points)} data points")
            
            # Test integrated data creation
            integrated_df = manager.get_integrated_data(
                variables=['temperature'],
                start_time=start_time,
                end_time=end_time,
                time_resolution=timedelta(hours=1)
            )
            self.log_step("Integrated data test", "success", f"Created {len(integrated_df)} integrated records")
            
            return True
            
        except Exception as e:
            self.log_step("Data integration deployment", "failed", str(e))
            return False
    
    def deploy_monitoring_system(self) -> bool:
        """Deploy monitoring system"""
        
        print("\n📊 Deploying Monitoring System...")
        
        try:
            from src.monitoring.system_monitor import SystemMonitor
            
            monitor = SystemMonitor()
            summary = monitor.run_full_monitoring()
            
            self.log_step("Monitoring system", "success", f"Status: {summary['system_status']}")
            self.log_step("Alert generation", "success", f"{summary['total_alerts']} alerts generated")
            
            return True
            
        except Exception as e:
            self.log_step("Monitoring deployment", "failed", str(e))
            return False
    
    def setup_cron_jobs(self) -> bool:
        """Setup automated cron jobs"""
        
        print("\n⏰ Setting up Automated Jobs...")
        
        try:
            # Define cron jobs
            cron_jobs = [
                {
                    'schedule': '*/5 * * * *',  # Every 5 minutes
                    'command': f'/usr/bin/python3 {self.project_root}/scripts/data/solax_collector.py',
                    'description': 'SolaX data collection'
                },
                {
                    'schedule': '15 * * * *',   # Every hour at :15
                    'command': f'/usr/bin/python3 {self.project_root}/scripts/data/weather_collector.py',
                    'description': 'Weather data collection'
                },
                {
                    'schedule': '0 6 * * *',    # Daily at 6:00 AM
                    'command': f'/usr/bin/python3 {self.project_root}/scripts/data/integrated_data_collector.py',
                    'description': 'Integrated data collection'
                },
                {
                    'schedule': '0 */6 * * *',  # Every 6 hours
                    'command': f'/usr/bin/python3 {self.project_root}/src/monitoring/system_monitor.py',
                    'description': 'System monitoring'
                },
                {
                    'schedule': '0 2 * * 1',    # Weekly on Monday at 2:00 AM
                    'command': f'/usr/bin/python3 {self.project_root}/src/models/multi_source_trainer.py',
                    'description': 'Model retraining'
                }
            ]
            
            # Check existing crontab
            try:
                result = subprocess.run(['crontab', '-l'], capture_output=True, text=True)
                existing_crontab = result.stdout if result.returncode == 0 else ""
            except:
                existing_crontab = ""
            
            # Add new jobs if not already present
            new_jobs = []
            for job in cron_jobs:
                job_line = f"{job['schedule']} {job['command']} # {job['description']}"
                
                if job['command'] not in existing_crontab:
                    new_jobs.append(job_line)
                    self.log_step(f"Cron job: {job['description']}", "success", "Added")
                else:
                    self.log_step(f"Cron job: {job['description']}", "success", "Already exists")
            
            if new_jobs:
                # Write new crontab
                new_crontab = existing_crontab + "\n" + "\n".join(new_jobs) + "\n"
                
                process = subprocess.Popen(['crontab', '-'], stdin=subprocess.PIPE, text=True)
                process.communicate(input=new_crontab)
                
                if process.returncode == 0:
                    self.log_step("Crontab update", "success", f"Added {len(new_jobs)} new jobs")
                else:
                    self.log_step("Crontab update", "failed", "Failed to update crontab")
                    return False
            
            return True
            
        except Exception as e:
            self.log_step("Cron jobs setup", "failed", str(e))
            return False
    
    def run_initial_data_collection(self) -> bool:
        """Run initial data collection"""
        
        print("\n📊 Running Initial Data Collection...")
        
        try:
            # Run integrated data collector
            result = subprocess.run([
                '/usr/bin/python3', 
                f'{self.project_root}/scripts/data/integrated_data_collector.py',
                '24'  # Last 24 hours
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                self.log_step("Initial data collection", "success", "24 hours of data processed")
                return True
            else:
                self.log_step("Initial data collection", "failed", result.stderr)
                return False
                
        except subprocess.TimeoutExpired:
            self.log_step("Initial data collection", "failed", "Timeout after 5 minutes")
            return False
        except Exception as e:
            self.log_step("Initial data collection", "failed", str(e))
            return False
    
    def create_deployment_summary(self) -> Dict:
        """Create deployment summary"""
        
        successful_steps = len([step for step in self.deployment_log if step['status'] == 'success'])
        total_steps = len(self.deployment_log)
        
        summary = {
            'deployment_time': datetime.now().isoformat(),
            'success_rate': (successful_steps / total_steps) * 100 if total_steps > 0 else 0,
            'total_steps': total_steps,
            'successful_steps': successful_steps,
            'failed_steps': total_steps - successful_steps,
            'status': 'success' if successful_steps == total_steps else 'partial' if successful_steps > 0 else 'failed',
            'steps': self.deployment_log
        }
        
        return summary
    
    def save_deployment_report(self, summary: Dict):
        """Save deployment report"""
        
        os.makedirs('logs/deployment', exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = f'logs/deployment/deployment_report_{timestamp}.json'
        
        with open(report_file, 'w') as f:
            json.dump(summary, f, indent=2)
        
        self.log_step("Deployment report", "success", f"Saved to {report_file}")

def main():
    """Main deployment function"""
    
    print("🚀 Multi-Source System Production Deployment")
    print("=" * 60)
    print(f"📅 Deployment Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    deployer = ProductionDeployer()
    
    # Deployment steps
    deployment_steps = [
        ("Prerequisites Check", deployer.check_prerequisites),
        ("Data Integration Deployment", deployer.deploy_data_integration),
        ("Monitoring System Deployment", deployer.deploy_monitoring_system),
        ("Cron Jobs Setup", deployer.setup_cron_jobs),
        ("Initial Data Collection", deployer.run_initial_data_collection),
    ]
    
    # Execute deployment steps
    for step_name, step_func in deployment_steps:
        try:
            success = step_func()
            if not success:
                print(f"\n❌ Deployment failed at: {step_name}")
                break
        except Exception as e:
            deployer.log_step(step_name, "failed", str(e))
            print(f"\n❌ Deployment failed at: {step_name} - {e}")
            break
    
    # Create and save summary
    summary = deployer.create_deployment_summary()
    deployer.save_deployment_report(summary)
    
    # Final results
    print("\n" + "=" * 60)
    print("🎯 DEPLOYMENT RESULTS:")
    print(f"   Status: {summary['status'].upper()}")
    print(f"   Success Rate: {summary['success_rate']:.1f}%")
    print(f"   Steps: {summary['successful_steps']}/{summary['total_steps']}")
    
    if summary['status'] == 'success':
        print("\n🎉 DEPLOYMENT SUCCESSFUL!")
        print("✅ Multi-source system is now running in production")
        print("📊 Automated data collection and monitoring active")
        print("🤖 Model training scheduled weekly")
        print("\n📋 Next Steps:")
        print("   • Monitor system health via logs/monitoring/")
        print("   • Check cron job execution: crontab -l")
        print("   • Review model performance weekly")
        return True
    else:
        print("\n❌ DEPLOYMENT INCOMPLETE")
        print("Please review the deployment log and fix issues")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
