#!/usr/bin/env python3
"""
Comprehensive Health Check for Enhanced Model v3 Production System
Complete verification of all system components
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import requests
import json
import psycopg2
import joblib
import logging
from datetime import datetime, timedelta
from pathlib import Path
import time

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ComprehensiveHealthCheck:
    """Comprehensive health check for Enhanced Model v3 system"""
    
    def __init__(self):
        self.base_url = "http://localhost:8101"
        self.project_root = Path("/home/<USER>/solar-prediction-project")
        self.results = {
            "timestamp": datetime.now().isoformat(),
            "overall_status": "unknown",
            "checks": {}
        }
        
    def check_enhanced_model_v3(self):
        """Check Enhanced Model v3 specific components"""
        logger.info("🤖 Checking Enhanced Model v3 components...")
        
        checks = {}
        
        try:
            # Check model files
            model_path = self.project_root / "models" / "enhanced_v3_production" / "enhanced_model_v3_final.joblib"
            features_path = self.project_root / "models" / "enhanced_v3_production" / "feature_columns.json"
            
            checks["model_file"] = {
                "status": "✅ OK" if model_path.exists() else "❌ MISSING",
                "path": str(model_path),
                "size_mb": round(model_path.stat().st_size / 1024 / 1024, 2) if model_path.exists() else 0
            }
            
            checks["features_file"] = {
                "status": "✅ OK" if features_path.exists() else "❌ MISSING",
                "path": str(features_path),
                "feature_count": 0
            }
            
            # Load and validate model
            if model_path.exists():
                try:
                    model = joblib.load(model_path)
                    checks["model_loading"] = {
                        "status": "✅ OK",
                        "model_type": str(type(model).__name__),
                        "estimators": len(model.estimators_) if hasattr(model, 'estimators_') else "N/A"
                    }
                except Exception as e:
                    checks["model_loading"] = {
                        "status": "❌ FAILED",
                        "error": str(e)
                    }
            
            # Load and validate features
            if features_path.exists():
                try:
                    with open(features_path, 'r') as f:
                        features = json.load(f)
                    checks["features_file"]["feature_count"] = len(features)
                    checks["features_validation"] = {
                        "status": "✅ OK",
                        "feature_count": len(features),
                        "sample_features": features[:5] if len(features) > 5 else features
                    }
                except Exception as e:
                    checks["features_validation"] = {
                        "status": "❌ FAILED",
                        "error": str(e)
                    }
            
            # Check deployment info
            deployment_path = self.project_root / "models" / "enhanced_v3_production" / "deployment_info.json"
            if deployment_path.exists():
                try:
                    with open(deployment_path, 'r') as f:
                        deployment_info = json.load(f)
                    checks["deployment_info"] = {
                        "status": "✅ OK",
                        "model_version": deployment_info.get("model_version", "unknown"),
                        "r2_score": deployment_info.get("performance", {}).get("r2_score", "unknown"),
                        "deployment_date": deployment_info.get("deployment_date", "unknown")
                    }
                except Exception as e:
                    checks["deployment_info"] = {
                        "status": "❌ FAILED",
                        "error": str(e)
                    }
            
            self.results["checks"]["enhanced_model_v3"] = checks
            
        except Exception as e:
            logger.error(f"❌ Enhanced Model v3 check failed: {e}")
            self.results["checks"]["enhanced_model_v3"] = {
                "status": "❌ FAILED",
                "error": str(e)
            }
    
    def check_api_endpoints(self):
        """Check all API endpoints"""
        logger.info("🌐 Checking API endpoints...")
        
        endpoints = {
            "health": "/health",
            "model_info": "/api/v1/model/info",
            "forecast_72h": "/api/v1/forecast/72h",
            "solax_latest": "/api/v1/data/solax/latest",
            "weather_latest": "/api/v1/data/weather/latest",
            "predict": "/api/v1/predict"
        }
        
        api_checks = {}
        
        for name, endpoint in endpoints.items():
            try:
                start_time = time.time()
                response = requests.get(f"{self.base_url}{endpoint}", timeout=10)
                response_time = round((time.time() - start_time) * 1000, 2)
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        api_checks[name] = {
                            "status": "✅ OK",
                            "status_code": response.status_code,
                            "response_time_ms": response_time,
                            "data_keys": list(data.keys()) if isinstance(data, dict) else "non-dict response"
                        }
                        
                        # Special checks for specific endpoints
                        if name == "model_info" and isinstance(data, dict):
                            api_checks[name]["model_name"] = data.get("model_name", "unknown")
                            api_checks[name]["r2_score"] = data.get("performance", {}).get("r2", "unknown")
                        
                    except json.JSONDecodeError:
                        api_checks[name] = {
                            "status": "⚠️ WARNING",
                            "status_code": response.status_code,
                            "response_time_ms": response_time,
                            "issue": "Invalid JSON response"
                        }
                else:
                    api_checks[name] = {
                        "status": "❌ FAILED",
                        "status_code": response.status_code,
                        "response_time_ms": response_time
                    }
                    
            except requests.exceptions.RequestException as e:
                api_checks[name] = {
                    "status": "❌ FAILED",
                    "error": str(e)
                }
        
        self.results["checks"]["api_endpoints"] = api_checks
    
    def check_database_health(self):
        """Check database health and recent data"""
        logger.info("🗄️ Checking database health...")
        
        try:
            conn = psycopg2.connect(
                host='localhost',
                database='solar_prediction',
                user='postgres',
                password='postgres'
            )
            
            db_checks = {}
            
            # Check connection
            db_checks["connection"] = {"status": "✅ OK"}
            
            # Check recent data
            tables = ['solax_data', 'solax_data2', 'weather_data', 'cams_radiation_data']
            
            for table in tables:
                try:
                    query = f"""
                    SELECT 
                        COUNT(*) as total_records,
                        MAX(timestamp) as latest_timestamp,
                        COUNT(CASE WHEN timestamp >= NOW() - INTERVAL '1 hour' THEN 1 END) as recent_records
                    FROM {table}
                    """
                    
                    cursor = conn.cursor()
                    cursor.execute(query)
                    result = cursor.fetchone()
                    
                    latest_timestamp = result[1]
                    minutes_ago = (datetime.now() - latest_timestamp).total_seconds() / 60 if latest_timestamp else float('inf')
                    
                    db_checks[table] = {
                        "status": "✅ OK" if minutes_ago < 60 else "⚠️ STALE" if minutes_ago < 1440 else "❌ OLD",
                        "total_records": result[0],
                        "latest_timestamp": str(latest_timestamp),
                        "minutes_since_latest": round(minutes_ago, 1),
                        "recent_records": result[2]
                    }
                    
                except Exception as e:
                    db_checks[table] = {
                        "status": "❌ FAILED",
                        "error": str(e)
                    }
            
            conn.close()
            self.results["checks"]["database"] = db_checks
            
        except Exception as e:
            logger.error(f"❌ Database check failed: {e}")
            self.results["checks"]["database"] = {
                "status": "❌ FAILED",
                "error": str(e)
            }
    
    def check_frontend_files(self):
        """Check frontend files and references"""
        logger.info("🎨 Checking frontend files...")
        
        frontend_checks = {}
        
        # Check main forecast interface
        forecast_html = self.project_root / "static" / "forecast" / "index.html"
        if forecast_html.exists():
            try:
                with open(forecast_html, 'r') as f:
                    content = f.read()
                
                # Check for Enhanced Model v3 references
                has_v3_title = "Enhanced Model v3 Optimized" in content
                has_v3_badge = 'model-badge">Enhanced v3' in content
                has_correct_accuracy = "96.7% Accuracy" in content
                
                frontend_checks["forecast_html"] = {
                    "status": "✅ OK" if all([has_v3_title, has_v3_badge, has_correct_accuracy]) else "⚠️ PARTIAL",
                    "has_v3_title": has_v3_title,
                    "has_v3_badge": has_v3_badge,
                    "has_correct_accuracy": has_correct_accuracy,
                    "file_size_kb": round(forecast_html.stat().st_size / 1024, 2)
                }
                
            except Exception as e:
                frontend_checks["forecast_html"] = {
                    "status": "❌ FAILED",
                    "error": str(e)
                }
        else:
            frontend_checks["forecast_html"] = {
                "status": "❌ MISSING",
                "path": str(forecast_html)
            }
        
        # Check JavaScript file
        forecast_js = self.project_root / "static" / "forecast" / "forecast.js"
        if forecast_js.exists():
            frontend_checks["forecast_js"] = {
                "status": "✅ OK",
                "file_size_kb": round(forecast_js.stat().st_size / 1024, 2)
            }
        else:
            frontend_checks["forecast_js"] = {
                "status": "❌ MISSING",
                "path": str(forecast_js)
            }
        
        self.results["checks"]["frontend"] = frontend_checks
    
    def check_prediction_functionality(self):
        """Test actual prediction functionality"""
        logger.info("🔮 Testing prediction functionality...")
        
        try:
            # Test prediction endpoint with sample data
            prediction_data = {
                "system_id": 1,
                "timestamp": datetime.now().isoformat(),
                "soc": 75.0,
                "bat_power": -500.0,
                "powerdc1": 2000.0,
                "powerdc2": 1800.0,
                "ghi": 600.0,
                "temperature": 25.0,
                "cloud_cover": 20.0
            }
            
            start_time = time.time()
            response = requests.post(
                f"{self.base_url}/api/v1/predict",
                json=prediction_data,
                timeout=10
            )
            prediction_time = round((time.time() - start_time) * 1000, 2)
            
            if response.status_code == 200:
                result = response.json()
                predicted_power = result.get("predicted_ac_power", 0)
                
                # Validate prediction is reasonable
                is_reasonable = 0 <= predicted_power <= 15000
                
                self.results["checks"]["prediction"] = {
                    "status": "✅ OK" if is_reasonable else "⚠️ UNREASONABLE",
                    "predicted_power": predicted_power,
                    "prediction_time_ms": prediction_time,
                    "is_reasonable": is_reasonable,
                    "response_keys": list(result.keys())
                }
            else:
                self.results["checks"]["prediction"] = {
                    "status": "❌ FAILED",
                    "status_code": response.status_code,
                    "prediction_time_ms": prediction_time
                }
                
        except Exception as e:
            logger.error(f"❌ Prediction test failed: {e}")
            self.results["checks"]["prediction"] = {
                "status": "❌ FAILED",
                "error": str(e)
            }
    
    def calculate_overall_status(self):
        """Calculate overall system status"""
        logger.info("📊 Calculating overall status...")
        
        all_checks = []
        critical_failures = []
        warnings = []
        
        for category, checks in self.results["checks"].items():
            if isinstance(checks, dict):
                for check_name, check_result in checks.items():
                    if isinstance(check_result, dict) and "status" in check_result:
                        status = check_result["status"]
                        all_checks.append(status)
                        
                        if status.startswith("❌"):
                            critical_failures.append(f"{category}.{check_name}")
                        elif status.startswith("⚠️"):
                            warnings.append(f"{category}.{check_name}")
        
        # Determine overall status
        if critical_failures:
            overall_status = "❌ CRITICAL ISSUES"
        elif warnings:
            overall_status = "⚠️ WARNINGS DETECTED"
        else:
            overall_status = "✅ HEALTHY"
        
        self.results["overall_status"] = overall_status
        self.results["summary"] = {
            "total_checks": len(all_checks),
            "critical_failures": len(critical_failures),
            "warnings": len(warnings),
            "critical_issues": critical_failures,
            "warning_issues": warnings
        }
    
    def run_comprehensive_check(self):
        """Run all health checks"""
        logger.info("🚀 STARTING COMPREHENSIVE HEALTH CHECK")
        logger.info("=" * 80)
        logger.info(f"📅 Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info("🎯 Objective: Verify Enhanced Model v3 production system")
        
        try:
            # Run all checks
            self.check_enhanced_model_v3()
            self.check_api_endpoints()
            self.check_database_health()
            self.check_frontend_files()
            self.check_prediction_functionality()
            
            # Calculate overall status
            self.calculate_overall_status()
            
            # Save results
            results_path = self.project_root / "test" / "results" / f"health_check_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            results_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(results_path, 'w') as f:
                json.dump(self.results, f, indent=2, default=str)
            
            # Display results
            logger.info("\n" + "=" * 80)
            logger.info("🏥 COMPREHENSIVE HEALTH CHECK COMPLETE")
            logger.info(f"🎯 Overall Status: {self.results['overall_status']}")
            logger.info(f"📊 Total Checks: {self.results['summary']['total_checks']}")
            logger.info(f"❌ Critical Issues: {self.results['summary']['critical_failures']}")
            logger.info(f"⚠️ Warnings: {self.results['summary']['warnings']}")
            
            if self.results['summary']['critical_issues']:
                logger.info("🔧 Critical Issues:")
                for issue in self.results['summary']['critical_issues']:
                    logger.info(f"   - {issue}")
            
            if self.results['summary']['warning_issues']:
                logger.info("⚠️ Warnings:")
                for warning in self.results['summary']['warning_issues']:
                    logger.info(f"   - {warning}")
            
            logger.info(f"💾 Results saved: {results_path}")
            logger.info("=" * 80)
            
            return self.results
            
        except Exception as e:
            logger.error(f"❌ Comprehensive health check failed: {e}")
            import traceback
            traceback.print_exc()
            return None

def main():
    """Execute comprehensive health check"""
    health_check = ComprehensiveHealthCheck()
    results = health_check.run_comprehensive_check()
    
    if results:
        # Return appropriate exit code
        if results["overall_status"].startswith("❌"):
            sys.exit(1)  # Critical issues
        elif results["overall_status"].startswith("⚠️"):
            sys.exit(2)  # Warnings
        else:
            sys.exit(0)  # Healthy
    else:
        sys.exit(3)  # Health check failed

if __name__ == "__main__":
    main()
