#!/usr/bin/env python3
"""
Comprehensive CSV Import
Import ALL data from the CSV file into the database
"""

import csv
import os
import sys
import psycopg2
from datetime import datetime
from typing import Dict, List, Tuple

def get_db_connection():
    """Get PostgreSQL database connection"""
    try:
        return psycopg2.connect(
            host=os.getenv("DB_HOST", "localhost"),
            database=os.getenv("DB_NAME", "solar_prediction"),
            user=os.getenv("DB_USER", "postgres"),
            password=os.getenv("DB_PASSWORD", "postgres"),
            port=os.getenv("DB_PORT", "5432")
        )
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return None

def parse_csv_data(file_path: str) -> List[Dict]:
    """Parse CSV data and extract all records"""
    print(f"📊 PARSING CSV FILE: {file_path}")
    print("=" * 50)
    
    records = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            
            # Read header
            header = next(reader)
            print(f"Header: {header}")
            
            # Find column indices
            time_col_idx = None
            yield_col_idx = None
            
            for i, col in enumerate(header):
                if 'Update time' in col:
                    time_col_idx = i
                elif 'Daily inverter output' in col and 'kWh' in col:
                    yield_col_idx = i
            
            print(f"Time column index: {time_col_idx}")
            print(f"Yield column index: {yield_col_idx}")
            
            if time_col_idx is None or yield_col_idx is None:
                print("❌ Required columns not found")
                return []
            
            # Read all data
            total_rows = 0
            valid_records = 0
            
            for row in reader:
                total_rows += 1
                
                if len(row) > max(time_col_idx, yield_col_idx):
                    try:
                        # Parse timestamp
                        time_str = row[time_col_idx].strip('"')
                        dt = datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
                        
                        # Parse yield
                        yield_str = row[yield_col_idx].strip('"')
                        yield_val = float(yield_str)
                        
                        # Create record
                        record = {
                            'timestamp': dt,
                            'yield_today': yield_val,
                            'inverter_sn': 'SYSTEM2_CSV_IMPORT',
                            'wifi_sn': 'SYSTEM2_CSV_IMPORT',
                            'soc': 50.0,  # Default values
                            'bat_power': 0.0,
                            'temperature': 20.0
                        }
                        
                        records.append(record)
                        valid_records += 1
                        
                        if valid_records % 1000 == 0:
                            print(f"   Processed {valid_records} valid records...")
                            
                    except (ValueError, IndexError) as e:
                        continue
            
            print(f"✅ Parsing completed:")
            print(f"   Total rows: {total_rows}")
            print(f"   Valid records: {valid_records}")
            print(f"   Date range: {records[0]['timestamp']} to {records[-1]['timestamp']}")
            
            return records
            
    except Exception as e:
        print(f"❌ Error parsing CSV: {e}")
        return []

def import_to_database(records: List[Dict]) -> bool:
    """Import records to database"""
    print(f"\n💾 IMPORTING TO DATABASE")
    print("=" * 30)
    
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        with conn.cursor() as cur:
            # Check if table exists and create if needed
            cur.execute("""
                CREATE TABLE IF NOT EXISTS solax_data2 (
                    id SERIAL PRIMARY KEY,
                    timestamp TIMESTAMP NOT NULL,
                    inverter_sn VARCHAR(50),
                    wifi_sn VARCHAR(50),
                    yield_today DECIMAL(10,2),
                    soc DECIMAL(5,2),
                    bat_power DECIMAL(10,2),
                    temperature DECIMAL(5,2),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create index on timestamp if not exists
            cur.execute("""
                CREATE INDEX IF NOT EXISTS idx_solax_data2_timestamp 
                ON solax_data2(timestamp)
            """)
            
            # Clear existing data (optional - comment out if you want to keep existing data)
            print("🗑️  Clearing existing data...")
            cur.execute("DELETE FROM solax_data2 WHERE inverter_sn = 'SYSTEM2_CSV_IMPORT'")
            
            # Insert new data in batches
            batch_size = 1000
            total_inserted = 0
            
            for i in range(0, len(records), batch_size):
                batch = records[i:i + batch_size]
                
                # Prepare batch insert
                insert_query = """
                    INSERT INTO solax_data2 (
                        timestamp, inverter_sn, wifi_sn, yield_today, 
                        soc, bat_power, temperature
                    ) VALUES %s
                """
                
                # Prepare values
                values = []
                for record in batch:
                    values.append((
                        record['timestamp'],
                        record['inverter_sn'],
                        record['wifi_sn'],
                        record['yield_today'],
                        record['soc'],
                        record['bat_power'],
                        record['temperature']
                    ))
                
                # Execute batch insert
                from psycopg2.extras import execute_values
                execute_values(cur, insert_query, values)
                
                total_inserted += len(batch)
                print(f"   Inserted {total_inserted}/{len(records)} records...")
            
            # Commit transaction
            conn.commit()
            
            print(f"✅ Import completed successfully!")
            print(f"   Total records inserted: {total_inserted}")
            
            # Verify import
            cur.execute("""
                SELECT 
                    COUNT(*) as total_records,
                    MIN(timestamp) as earliest,
                    MAX(timestamp) as latest,
                    MIN(yield_today) as min_yield,
                    MAX(yield_today) as max_yield
                FROM solax_data2 
                WHERE inverter_sn = 'SYSTEM2_CSV_IMPORT'
            """)
            
            result = cur.fetchone()
            print(f"\n📊 Import Verification:")
            print(f"   Total records: {result[0]}")
            print(f"   Date range: {result[1]} to {result[2]}")
            print(f"   Yield range: {result[3]} - {result[4]} kWh")
            
            return True
            
    except Exception as e:
        print(f"❌ Import failed: {e}")
        conn.rollback()
        return False
    
    finally:
        conn.close()

def main():
    """Main import function"""
    print("🚀 COMPREHENSIVE CSV IMPORT - ALL DATA")
    print("=" * 50)
    print(f"Import Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # CSV file path
    csv_file = "data/raw/System2/Plant Reports 2024-03-01-2024-06-28.csv"
    
    if not os.path.exists(csv_file):
        print(f"❌ CSV file not found: {csv_file}")
        return False
    
    # Parse CSV data
    records = parse_csv_data(csv_file)
    
    if not records:
        print("❌ No valid records found in CSV")
        return False
    
    # Import to database
    success = import_to_database(records)
    
    if success:
        print("\n🎉 COMPREHENSIVE IMPORT COMPLETED SUCCESSFULLY!")
        print("✅ All CSV data has been imported to solax_data2 table")
        print("🔄 Ready to proceed with April 2024 vs 2025 vs 2026 comparison")
        return True
    else:
        print("\n❌ IMPORT FAILED!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
