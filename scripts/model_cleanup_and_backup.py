#!/usr/bin/env python3
"""
Model Cleanup and Backup Script
Μεταφέρει παλιά μοντέλα στο backup και κρατάει μόνο τα 8 παραγωγικά
"""

import os
import shutil
import json
from pathlib import Path
from datetime import datetime
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ModelCleanupManager:
    """Διαχειρίζεται το cleanup και backup των μοντέλων"""
    
    def __init__(self):
        self.models_dir = Path("/home/<USER>/solar-prediction-project/models")
        self.backup_dir = Path("/home/<USER>/solar-prediction-project/models_backup")
        self.backup_dir.mkdir(exist_ok=True)
        
        # Τα 8 παραγωγικά μοντέλα που πρέπει να κρατήσουμε
        self.production_models = {
            # Existing multi-horizon models
            'multi_horizon_daily_system1': 'daily_system1',
            'multi_horizon_daily_system2': 'daily_system2', 
            'multi_horizon_monthly_system1': 'monthly_system1',
            'multi_horizon_monthly_system2': 'monthly_system2',
            
            # Models to be created
            'multi_horizon_hourly_system1': 'hourly_system1',
            'multi_horizon_hourly_system2': 'hourly_system2',
            'multi_horizon_yearly_system1': 'yearly_system1',
            'multi_horizon_yearly_system2': 'yearly_system2'
        }
    
    def identify_models_to_backup(self):
        """Αναγνωρίζει ποια μοντέλα πρέπει να μπουν σε backup"""
        
        if not self.models_dir.exists():
            logger.error(f"Models directory not found: {self.models_dir}")
            return []
        
        all_models = [d for d in self.models_dir.iterdir() if d.is_dir()]
        models_to_backup = []
        
        for model_dir in all_models:
            model_name = model_dir.name
            
            # Αν δεν είναι στα παραγωγικά μοντέλα, πάει σε backup
            if model_name not in self.production_models:
                models_to_backup.append(model_dir)
        
        return models_to_backup
    
    def backup_old_models(self):
        """Μεταφέρει παλιά μοντέλα στο backup directory"""
        
        models_to_backup = self.identify_models_to_backup()
        
        if not models_to_backup:
            logger.info("No models to backup")
            return True
        
        # Create timestamped backup directory
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_session_dir = self.backup_dir / f"backup_{timestamp}"
        backup_session_dir.mkdir(exist_ok=True)
        
        logger.info(f"Backing up {len(models_to_backup)} models to {backup_session_dir}")
        
        backup_summary = {
            'backup_date': datetime.now().isoformat(),
            'total_models_backed_up': len(models_to_backup),
            'backed_up_models': [],
            'production_models_kept': list(self.production_models.keys())
        }
        
        for model_dir in models_to_backup:
            try:
                backup_path = backup_session_dir / model_dir.name
                shutil.copytree(model_dir, backup_path)
                
                # Remove original
                shutil.rmtree(model_dir)
                
                backup_summary['backed_up_models'].append({
                    'name': model_dir.name,
                    'original_path': str(model_dir),
                    'backup_path': str(backup_path),
                    'status': 'success'
                })
                
                logger.info(f"✅ Backed up: {model_dir.name}")
                
            except Exception as e:
                logger.error(f"❌ Failed to backup {model_dir.name}: {e}")
                backup_summary['backed_up_models'].append({
                    'name': model_dir.name,
                    'original_path': str(model_dir),
                    'backup_path': None,
                    'status': 'failed',
                    'error': str(e)
                })
        
        # Save backup summary
        summary_file = backup_session_dir / "backup_summary.json"
        with open(summary_file, 'w') as f:
            json.dump(backup_summary, f, indent=2)
        
        logger.info(f"Backup completed. Summary saved to {summary_file}")
        return True
    
    def verify_production_models(self):
        """Επαληθεύει ότι τα παραγωγικά μοντέλα υπάρχουν"""
        
        existing_models = []
        missing_models = []
        
        for model_name, description in self.production_models.items():
            model_path = self.models_dir / model_name
            
            if model_path.exists():
                existing_models.append((model_name, description))
                logger.info(f"✅ Found: {model_name} ({description})")
            else:
                missing_models.append((model_name, description))
                logger.warning(f"❌ Missing: {model_name} ({description})")
        
        return existing_models, missing_models
    
    def create_production_model_registry(self):
        """Δημιουργεί registry των παραγωγικών μοντέλων"""
        
        existing_models, missing_models = self.verify_production_models()
        
        registry = {
            'created_date': datetime.now().isoformat(),
            'total_production_models': len(self.production_models),
            'existing_models': len(existing_models),
            'missing_models': len(missing_models),
            'model_architecture': {
                'horizons': ['hourly', 'daily', 'monthly', 'yearly'],
                'systems': [1, 2],
                'total_models_required': 8,
                'models_per_system': 4
            },
            'existing_models_list': [
                {'name': name, 'description': desc, 'status': 'ready'} 
                for name, desc in existing_models
            ],
            'missing_models_list': [
                {'name': name, 'description': desc, 'status': 'needs_creation'} 
                for name, desc in missing_models
            ],
            'next_steps': [
                'Create missing hourly and yearly models',
                'Implement ensemble methods',
                'Set up real-time monitoring',
                'Deploy seasonal adjustment algorithms'
            ]
        }
        
        registry_file = self.models_dir / "production_models_registry.json"
        with open(registry_file, 'w') as f:
            json.dump(registry, f, indent=2)
        
        logger.info(f"Production model registry created: {registry_file}")
        return registry
    
    def run_cleanup(self):
        """Εκτελεί το πλήρες cleanup process"""
        
        logger.info("🚀 Starting model cleanup and backup process")
        
        try:
            # Step 1: Backup old models
            logger.info("📦 Step 1: Backing up old models...")
            self.backup_old_models()
            
            # Step 2: Verify production models
            logger.info("🔍 Step 2: Verifying production models...")
            existing, missing = self.verify_production_models()
            
            # Step 3: Create registry
            logger.info("📋 Step 3: Creating production model registry...")
            registry = self.create_production_model_registry()
            
            # Summary
            logger.info("✅ Model cleanup completed successfully!")
            logger.info(f"📊 Summary:")
            logger.info(f"   - Existing models: {len(existing)}")
            logger.info(f"   - Missing models: {len(missing)}")
            logger.info(f"   - Models backed up: {len(self.identify_models_to_backup())}")
            
            if missing:
                logger.info("🔧 Next steps:")
                for name, desc in missing:
                    logger.info(f"   - Create: {name} ({desc})")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Cleanup failed: {e}")
            return False

def main():
    """Main function"""
    print("🧹 MODEL CLEANUP AND BACKUP UTILITY")
    print("=" * 60)
    
    cleanup_manager = ModelCleanupManager()
    success = cleanup_manager.run_cleanup()
    
    if success:
        print("\n🎉 CLEANUP COMPLETED SUCCESSFULLY!")
        print("✅ Old models backed up")
        print("✅ Production model registry created")
        print("🔧 Ready for next implementation steps")
    else:
        print("\n❌ CLEANUP FAILED!")
        print("Check logs for details")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
