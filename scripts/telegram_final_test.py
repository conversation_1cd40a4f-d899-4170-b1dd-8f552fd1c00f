#!/usr/bin/env python3
"""
Final Telegram Integration Test
Send success message to confirm everything works
"""

import sys
import os
import asyncio

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from monitoring.notifications.telegram_service import get_telegram_service

async def send_success_message():
    """Send final success message"""
    print("🎉 Sending final Telegram integration success message...")
    
    service = get_telegram_service()
    
    message = """🎉 TELEGRAM INTEGRATION COMPLETE!

✅ Solar Prediction System είναι τώρα πλήρως ενσωματωμένο με Telegram!

🤖 Bot: @grlvSolarAI_bot
📱 Chat ID: 1510889515 (ATHANASIOS)
🌐 API Server: http://localhost:8100
🔧 Port Configuration: FIXED στο 8100

🚀 Active Features:
• 🚨 Real-time alerts για battery SOC, system failures
• 📊 System status updates με production data
• 🌞 Daily summaries με performance metrics
• ⚡ Rich messaging με emojis και Greek system names

📋 System Status:
• Σπίτι Πάνω: Online ✅
• Σπίτι Κάτω: Online ✅
• Weather API: Active ✅
• SolaX API: Connected ✅
• Database: Operational ✅

🎯 Ready για Production!

Το σύστημα παρακολουθεί τα solar systems σου 24/7 και θα σε ενημερώνει για όλα τα σημαντικά events!

Timestamp: 2025-06-04 00:15:00"""
    
    result = await service.send_message(message, parse_mode="")
    
    if result.get('status') == 'success':
        print("✅ Success message sent to Telegram!")
        print("🎉 Telegram integration is fully operational!")
    else:
        print(f"❌ Failed to send message: {result}")

if __name__ == "__main__":
    asyncio.run(send_success_message())
