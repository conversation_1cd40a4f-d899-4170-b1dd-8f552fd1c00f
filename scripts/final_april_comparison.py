#!/usr/bin/env python3
"""
Final April Comparison Analysis
APRIL 2024 vs 2025 vs 2026 PREDICTION
Using real data from database
"""

import os
import sys
import json
import joblib
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, Any, List, <PERSON>ple
import psycopg2
from psycopg2.extras import RealDictCursor
import warnings
warnings.filterwarnings('ignore')

def get_db_connection():
    """Get PostgreSQL database connection"""
    try:
        return psycopg2.connect(
            host=os.getenv("DB_HOST", "localhost"),
            database=os.getenv("DB_NAME", "solar_prediction"),
            user=os.getenv("DB_USER", "postgres"),
            password=os.getenv("DB_PASSWORD", "postgres"),
            port=os.getenv("DB_PORT", "5432")
        )
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return None

class FinalAprilAnalyzer:
    """Final analyzer for April comparison"""
    
    def __init__(self):
        self.conn = get_db_connection()
        if not self.conn:
            raise Exception("Cannot proceed without database connection")
        
        self.models = {}
        self.scalers = {}
        self.load_models()
    
    def load_models(self):
        """Load trained models"""
        print("📊 Loading trained models...")
        
        for system_id in [1, 2]:
            model_dir = f"models/corrected_yield_system{system_id}"
            
            if os.path.exists(f"{model_dir}/model.joblib"):
                self.models[system_id] = joblib.load(f"{model_dir}/model.joblib")
                self.scalers[system_id] = joblib.load(f"{model_dir}/scaler.joblib")
                print(f"✅ System {system_id}: Model loaded")
            else:
                print(f"❌ Model not found for System {system_id}")
    
    def get_april_data(self, year: int) -> Dict[str, pd.DataFrame]:
        """Get April data for both systems"""
        print(f"\n📊 Getting April {year} data...")
        
        april_data = {}
        
        for system_id in [1, 2]:
            table_name = 'solax_data' if system_id == 1 else 'solax_data2'
            
            try:
                with self.conn.cursor(cursor_factory=RealDictCursor) as cur:
                    cur.execute(f"""
                        WITH daily_yields AS (
                            SELECT 
                                DATE(timestamp) as date,
                                MAX(yield_today) as daily_yield,
                                COUNT(*) as measurements
                            FROM {table_name}
                            WHERE EXTRACT(year FROM timestamp) = %s
                                AND EXTRACT(month FROM timestamp) = 4
                                AND yield_today >= 0
                            GROUP BY DATE(timestamp)
                            HAVING MAX(yield_today) > 0 AND MAX(yield_today) < 100
                        )
                        SELECT * FROM daily_yields ORDER BY date
                    """, (year,))
                    
                    results = cur.fetchall()
                    
                    if results:
                        df = pd.DataFrame([dict(row) for row in results])
                        april_data[f"system_{system_id}"] = df
                        
                        avg_yield = df['daily_yield'].mean()
                        total_yield = df['daily_yield'].sum()
                        days = len(df)
                        
                        print(f"✅ System {system_id}: {days} days, avg {avg_yield:.1f} kWh/day, total {total_yield:.1f} kWh")
                    else:
                        print(f"❌ No data for System {system_id} in April {year}")
                        april_data[f"system_{system_id}"] = pd.DataFrame()
                        
            except Exception as e:
                print(f"❌ Error getting data for System {system_id}: {e}")
                april_data[f"system_{system_id}"] = pd.DataFrame()
        
        return april_data
    
    def create_prediction_features(self, date: datetime, system_id: int) -> np.array:
        """Create features for prediction"""
        month = date.month
        day_of_year = date.timetuple().tm_yday
        day_of_week = date.weekday()
        
        # Temporal features
        season = (month - 1) // 3
        is_peak_season = 1 if month in [5, 6, 7] else 0
        is_low_season = 1 if month in [12, 1, 2] else 0
        is_weekend = 1 if day_of_week >= 5 else 0
        
        # Cyclical encoding
        month_sin = np.sin(2 * np.pi * month / 12)
        month_cos = np.cos(2 * np.pi * month / 12)
        day_sin = np.sin(2 * np.pi * day_of_year / 365)
        day_cos = np.cos(2 * np.pi * day_of_year / 365)
        
        # Solar position
        solar_declination = 23.45 * np.sin(np.radians(360 * (284 + day_of_year) / 365))
        day_length = 12 + 4 * np.sin(np.radians(solar_declination))
        
        # April patterns for seasonal factors
        if system_id == 1:
            seasonal_yield_factor = 59.3  # Spring average for System 1
            monthly_yield_factor = 58.7   # April average for System 1
        else:
            seasonal_yield_factor = 53.5  # Spring average for System 2 (lower)
            monthly_yield_factor = 49.1   # April average for System 2 (lower)
        
        # Calculate yield efficiency
        estimated_daily_yield = monthly_yield_factor
        yield_efficiency = estimated_daily_yield / (day_length + 1)
        
        # Feature vector
        features = [
            month, day_of_year, season, day_of_week, is_weekend,
            month_sin, month_cos, day_sin, day_cos,
            is_peak_season, is_low_season, solar_declination, day_length,
            yield_efficiency, seasonal_yield_factor, monthly_yield_factor
        ]
        
        return np.array([features])
    
    def predict_april_2026(self) -> Dict[str, Any]:
        """Predict April 2026"""
        print(f"\n🔮 PREDICTING APRIL 2026")
        print("=" * 30)
        
        predictions = {}
        april_2026_dates = pd.date_range(start='2026-04-01', end='2026-04-30', freq='D')
        
        for system_id in [1, 2]:
            if system_id not in self.models:
                continue
            
            daily_predictions = []
            
            for date in april_2026_dates:
                features = self.create_prediction_features(date, system_id)
                features_scaled = self.scalers[system_id].transform(features)
                prediction = self.models[system_id].predict(features_scaled)[0]
                
                daily_predictions.append({
                    'date': date.date(),
                    'predicted_yield': prediction
                })
            
            predictions[f"system_{system_id}"] = daily_predictions
            
            total_predicted = sum(p['predicted_yield'] for p in daily_predictions)
            avg_predicted = total_predicted / len(daily_predictions)
            
            print(f"✅ System {system_id}: avg {avg_predicted:.1f} kWh/day, total {total_predicted:.1f} kWh")
        
        return predictions
    
    def compare_april_years(self, data_2024: Dict, data_2025: Dict, predictions_2026: Dict) -> Dict[str, Any]:
        """Compare April across years"""
        print(f"\n📊 APRIL COMPARISON: 2024 vs 2025 vs 2026")
        print("=" * 50)
        
        comparison = {}
        
        for system_key in ['system_1', 'system_2']:
            system_id = int(system_key.split('_')[1])
            
            # Calculate averages
            avg_2024 = data_2024[system_key]['daily_yield'].mean() if not data_2024[system_key].empty else 0
            avg_2025 = data_2025[system_key]['daily_yield'].mean() if not data_2025[system_key].empty else 0
            avg_2026 = np.mean([p['predicted_yield'] for p in predictions_2026[system_key]]) if system_key in predictions_2026 else 0
            
            # Calculate totals
            total_2024 = data_2024[system_key]['daily_yield'].sum() if not data_2024[system_key].empty else 0
            total_2025 = data_2025[system_key]['daily_yield'].sum() if not data_2025[system_key].empty else 0
            total_2026 = sum(p['predicted_yield'] for p in predictions_2026[system_key]) if system_key in predictions_2026 else 0
            
            # Calculate changes
            change_2024_to_2025 = avg_2025 - avg_2024 if avg_2024 > 0 else 0
            change_2025_to_2026 = avg_2026 - avg_2025 if avg_2025 > 0 else 0
            
            change_2024_to_2025_pct = (change_2024_to_2025 / avg_2024 * 100) if avg_2024 > 0 else 0
            change_2025_to_2026_pct = (change_2025_to_2026 / avg_2025 * 100) if avg_2025 > 0 else 0
            
            comparison[system_key] = {
                'avg_2024': avg_2024,
                'avg_2025': avg_2025,
                'avg_2026': avg_2026,
                'total_2024': total_2024,
                'total_2025': total_2025,
                'total_2026': total_2026,
                'change_2024_to_2025_kwh': change_2024_to_2025,
                'change_2025_to_2026_kwh': change_2025_to_2026,
                'change_2024_to_2025_pct': change_2024_to_2025_pct,
                'change_2025_to_2026_pct': change_2025_to_2026_pct
            }
            
            print(f"\n📊 System {system_id} April Comparison:")
            print(f"   2024 Actual:    {avg_2024:.1f} kWh/day (total: {total_2024:.1f} kWh)")
            print(f"   2025 Actual:    {avg_2025:.1f} kWh/day (total: {total_2025:.1f} kWh)")
            print(f"   2026 Predicted: {avg_2026:.1f} kWh/day (total: {total_2026:.1f} kWh)")
            print(f"   ")
            print(f"   2024→2025: {change_2024_to_2025:+.1f} kWh/day ({change_2024_to_2025_pct:+.1f}%)")
            print(f"   2025→2026: {change_2025_to_2026:+.1f} kWh/day ({change_2025_to_2026_pct:+.1f}%)")
        
        return comparison
    
    def analyze_deviations(self, comparison: Dict) -> Dict[str, List[str]]:
        """Analyze deviations and patterns"""
        print(f"\n🔍 DEVIATION ANALYSIS")
        print("=" * 30)
        
        findings = {}
        
        for system_key, data in comparison.items():
            system_id = int(system_key.split('_')[1])
            system_findings = []
            
            # Analyze year-over-year changes
            if abs(data['change_2024_to_2025_pct']) > 5:
                if data['change_2024_to_2025_pct'] > 0:
                    system_findings.append(f"Production INCREASED {data['change_2024_to_2025_pct']:.1f}% from 2024 to 2025")
                else:
                    system_findings.append(f"Production DECREASED {abs(data['change_2024_to_2025_pct']):.1f}% from 2024 to 2025")
            
            if abs(data['change_2025_to_2026_pct']) > 5:
                if data['change_2025_to_2026_pct'] > 0:
                    system_findings.append(f"Model predicts INCREASE of {data['change_2025_to_2026_pct']:.1f}% for 2026")
                else:
                    system_findings.append(f"Model predicts DECREASE of {abs(data['change_2025_to_2026_pct']):.1f}% for 2026")
            
            # Model accuracy assessment
            if data['avg_2024'] > 0 and data['avg_2025'] > 0:
                historical_avg = (data['avg_2024'] + data['avg_2025']) / 2
                prediction_vs_historical = ((data['avg_2026'] - historical_avg) / historical_avg) * 100
                
                if abs(prediction_vs_historical) > 10:
                    system_findings.append(f"2026 prediction deviates {prediction_vs_historical:+.1f}% from historical average")
                else:
                    system_findings.append(f"2026 prediction within {abs(prediction_vs_historical):.1f}% of historical average")
            
            findings[system_key] = system_findings
            
            print(f"\n🔍 System {system_id} Findings:")
            for i, finding in enumerate(system_findings, 1):
                print(f"   {i}. {finding}")
        
        return findings

def main():
    """Main analysis"""
    print("📊 FINAL APRIL COMPARISON: 2024 vs 2025 vs 2026")
    print("=" * 60)
    print(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        analyzer = FinalAprilAnalyzer()
        
        # Get April data
        april_2024 = analyzer.get_april_data(2024)
        april_2025 = analyzer.get_april_data(2025)
        
        # Predict April 2026
        predictions_2026 = analyzer.predict_april_2026()
        
        # Compare across years
        comparison = analyzer.compare_april_years(april_2024, april_2025, predictions_2026)
        
        # Analyze deviations
        findings = analyzer.analyze_deviations(comparison)
        
        print(f"\n🎉 FINAL APRIL ANALYSIS COMPLETED!")
        print("✅ All data analyzed successfully")
        
        return {
            'april_2024': april_2024,
            'april_2025': april_2025,
            'predictions_2026': predictions_2026,
            'comparison': comparison,
            'findings': findings
        }
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()
