#!/usr/bin/env python3
"""
ML Ensemble 7-Day Solar Forecast
================================

Advanced 7-day solar production forecast using:
- Ensemble ML models (XGBoost + LightGBM + Random Forest)
- Grade A mathematical model fallback
- Hybrid prediction system with confidence scoring
- Advanced feature engineering
- Real weather data integration

Created: June 6, 2025
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import numpy as np
import pandas as pd
import logging
from datetime import datetime, timedelta
import requests
import json
from typing import Dict, List, Tuple, Any
import math
import joblib
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# ML libraries
import xgboost as xgb
import lightgbm as lgb
from sklearn.ensemble import RandomForestRegressor, VotingRegressor
from sklearn.preprocessing import StandardScaler

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AdvancedFeatureEngineer:
    """Advanced feature engineering for ML models"""
    
    def __init__(self, latitude: float = 38.141348, longitude: float = 24.007165):
        self.latitude = math.radians(latitude)
        self.longitude = math.radians(longitude)
    
    def engineer_features(self, weather_data: List[Dict]) -> pd.DataFrame:
        """Engineer comprehensive features for ML models"""
        features = []
        
        for i, data in enumerate(weather_data):
            dt = data['datetime']
            
            # Basic weather features
            base_features = {
                'ghi': data['ghi'],
                'temperature': data['temperature'],
                'cloud_cover': data['cloud_cover'],
                'sun_elevation': data['sun_elevation'],
                'sun_azimuth': data['sun_azimuth']
            }
            
            # Temporal features
            base_features.update({
                'hour': dt.hour,
                'day_of_year': dt.timetuple().tm_yday,
                'month': dt.month,
                'season': ((dt.timetuple().tm_yday - 1) // 91) % 4,
                'is_weekend': dt.weekday() >= 5
            })
            
            # Derived features
            base_features.update({
                'ghi_normalized': data['ghi'] / 1000,
                'temperature_deviation': data['temperature'] - 25,
                'cloud_factor': 1 - (data['cloud_cover'] / 100),
                'sun_elevation_sin': math.sin(math.radians(max(0, data['sun_elevation']))),
                'sun_elevation_cos': math.cos(math.radians(max(0, data['sun_elevation']))),
                'hour_sin': math.sin(2 * math.pi * dt.hour / 24),
                'hour_cos': math.cos(2 * math.pi * dt.hour / 24),
                'day_sin': math.sin(2 * math.pi * dt.timetuple().tm_yday / 365),
                'day_cos': math.cos(2 * math.pi * dt.timetuple().tm_yday / 365)
            })
            
            # Lag features (if available)
            if i > 0:
                prev_data = weather_data[i-1]
                base_features.update({
                    'ghi_lag_1h': prev_data['ghi'],
                    'temp_lag_1h': prev_data['temperature'],
                    'cloud_lag_1h': prev_data['cloud_cover']
                })
            else:
                base_features.update({
                    'ghi_lag_1h': data['ghi'],
                    'temp_lag_1h': data['temperature'],
                    'cloud_lag_1h': data['cloud_cover']
                })
            
            # Rolling features (simplified for forecast)
            window_start = max(0, i-5)
            window_data = weather_data[window_start:i+1]
            
            if len(window_data) > 1:
                base_features.update({
                    'ghi_rolling_mean_6h': np.mean([d['ghi'] for d in window_data]),
                    'temp_rolling_mean_6h': np.mean([d['temperature'] for d in window_data]),
                    'cloud_rolling_mean_6h': np.mean([d['cloud_cover'] for d in window_data])
                })
            else:
                base_features.update({
                    'ghi_rolling_mean_6h': data['ghi'],
                    'temp_rolling_mean_6h': data['temperature'],
                    'cloud_rolling_mean_6h': data['cloud_cover']
                })
            
            features.append(base_features)
        
        return pd.DataFrame(features)

class MLEnsemblePredictor:
    """ML Ensemble prediction system"""
    
    def __init__(self, system_id: str):
        self.system_id = system_id
        self.models = {}
        self.scaler = None
        self.feature_names = []
        self.is_trained = False
        
        # Create models
        self._create_models()
        
        logger.info(f"🤖 ML Ensemble predictor initialized for {system_id}")
    
    def _create_models(self):
        """Create ensemble ML models"""
        self.models = {
            'xgboost': xgb.XGBRegressor(
                n_estimators=300,
                max_depth=8,
                learning_rate=0.1,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=42,
                n_jobs=-1
            ),
            
            'lightgbm': lgb.LGBMRegressor(
                n_estimators=300,
                max_depth=8,
                learning_rate=0.1,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=42,
                n_jobs=-1,
                verbose=-1
            ),
            
            'random_forest': RandomForestRegressor(
                n_estimators=200,
                max_depth=12,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42,
                n_jobs=-1
            )
        }
        
        # Create ensemble
        estimators = [(name, model) for name, model in self.models.items()]
        self.models['ensemble'] = VotingRegressor(estimators=estimators)
    
    def train_with_synthetic_data(self):
        """Train models with synthetic solar data"""
        logger.info(f"🎓 Training ML models with synthetic data for {self.system_id}...")
        
        # Generate synthetic training data
        X_train, y_train = self._generate_training_data()
        
        # Scale features
        self.scaler = StandardScaler()
        X_train_scaled = self.scaler.fit_transform(X_train)
        
        # Store feature names
        self.feature_names = X_train.columns.tolist()
        
        # Train all models
        for model_name, model in self.models.items():
            logger.info(f"   Training {model_name}...")
            model.fit(X_train_scaled, y_train)
        
        self.is_trained = True
        logger.info(f"✅ ML models trained for {self.system_id}")
    
    def _generate_training_data(self, n_samples: int = 5000) -> Tuple[pd.DataFrame, np.ndarray]:
        """Generate synthetic training data"""
        np.random.seed(42)
        
        # Generate diverse weather conditions
        data = []
        targets = []
        
        for i in range(n_samples):
            # Random time
            base_date = datetime(2024, 1, 1) + timedelta(days=np.random.randint(0, 365))
            hour = np.random.randint(5, 20)  # Daylight hours
            dt = base_date.replace(hour=hour)
            
            # Weather features
            day_of_year = dt.timetuple().tm_yday
            
            # Seasonal patterns
            seasonal_temp = 20 + 10 * math.sin((day_of_year - 80) * 2 * math.pi / 365)
            temperature = seasonal_temp + 8 * math.sin((hour - 6) * math.pi / 12) + np.random.normal(0, 3)
            
            # Solar elevation
            sun_elevation = max(0, 60 * math.sin((hour - 6) * math.pi / 12))
            
            # GHI with seasonal and daily patterns
            base_ghi = 900 * math.sin(math.radians(sun_elevation)) * (1 + 0.2 * math.sin((day_of_year - 172) * 2 * math.pi / 365))
            cloud_cover = np.random.beta(2, 5) * 100
            ghi = max(0, base_ghi * (1 - cloud_cover / 150) + np.random.normal(0, 50))
            
            # Create weather data point
            weather_point = {
                'datetime': dt,
                'ghi': ghi,
                'temperature': temperature,
                'cloud_cover': cloud_cover,
                'sun_elevation': sun_elevation,
                'sun_azimuth': (hour - 12) * 15
            }
            
            data.append(weather_point)
            
            # Calculate target (solar yield)
            if ghi > 50 and sun_elevation > 0:
                # Physics-based calculation with noise
                temp_factor = 1 - 0.004 * (temperature - 25)
                efficiency = 0.18 * temp_factor
                panel_area = 52.5  # For 10.5kWp system
                
                hourly_yield = (ghi / 1000) * panel_area * efficiency
                hourly_yield = max(0, hourly_yield + np.random.normal(0, 0.5))
                
                # System-specific calibration
                if self.system_id == 'system_1':
                    hourly_yield *= 1.06
                else:
                    hourly_yield *= 1.03
            else:
                hourly_yield = 0
            
            targets.append(hourly_yield)
        
        # Engineer features
        feature_engineer = AdvancedFeatureEngineer()
        X = feature_engineer.engineer_features(data)
        y = np.array(targets)
        
        return X, y
    
    def predict_hourly(self, features: pd.DataFrame) -> Dict[str, float]:
        """Make hourly predictions with all models"""
        if not self.is_trained:
            self.train_with_synthetic_data()
        
        # Scale features
        features_scaled = self.scaler.transform(features)
        
        predictions = {}
        for model_name, model in self.models.items():
            pred = model.predict(features_scaled)[0]
            predictions[model_name] = max(0, pred)  # Ensure non-negative
        
        return predictions
    
    def get_ensemble_prediction(self, features: pd.DataFrame) -> Tuple[float, float]:
        """Get ensemble prediction with confidence"""
        predictions = self.predict_hourly(features)
        
        # Use ensemble model as primary
        ensemble_pred = predictions['ensemble']
        
        # Calculate confidence based on model agreement
        individual_preds = [predictions[name] for name in ['xgboost', 'lightgbm', 'random_forest']]
        std_dev = np.std(individual_preds)
        mean_pred = np.mean(individual_preds)
        
        # Confidence score (higher when models agree)
        confidence = max(0.5, 1.0 - (std_dev / (mean_pred + 0.1)))
        
        return ensemble_pred, confidence

class GradeAFallbackModel:
    """Grade A mathematical model for fallback"""
    
    def __init__(self, system_id: str):
        self.system_id = system_id
        self.calibration_factors = {'system_1': 1.06, 'system_2': 1.03}
        self.calibration_factor = self.calibration_factors.get(system_id, 1.0)
    
    def predict_hourly(self, ghi: float, temperature: float, sun_elevation: float, cloud_cover: float) -> float:
        """Grade A mathematical prediction"""
        if sun_elevation <= 0 or ghi < 50:
            return 0.0
        
        # Temperature derating
        temp_factor = 1 - 0.004 * (temperature - 25)
        temp_factor = max(0.7, min(1.2, temp_factor))
        
        # Cloud impact
        cloud_factor = 1 - (cloud_cover / 100) * 0.8
        effective_ghi = ghi * cloud_factor
        
        # Solar elevation factor
        elevation_factor = max(0, math.sin(math.radians(sun_elevation)))
        
        # Power calculation
        panel_area = 52.5  # For 10.5kWp
        efficiency = 0.20
        
        hourly_power = (effective_ghi / 1000) * panel_area * efficiency * temp_factor * elevation_factor
        hourly_power = max(0, min(hourly_power, 10.5))  # System limit
        
        return hourly_power * self.calibration_factor

class HybridForecastSystem:
    """Hybrid ML + Grade A forecast system"""
    
    def __init__(self):
        self.ml_predictors = {
            'system_1': MLEnsemblePredictor('system_1'),
            'system_2': MLEnsemblePredictor('system_2')
        }
        
        self.grade_a_models = {
            'system_1': GradeAFallbackModel('system_1'),
            'system_2': GradeAFallbackModel('system_2')
        }
        
        self.feature_engineer = AdvancedFeatureEngineer()
        
        logger.info("🔮 Hybrid ML + Grade A forecast system initialized")
    
    def get_weather_forecast(self) -> List[Dict]:
        """Get 7-day weather forecast"""
        logger.info("🌤️ Fetching weather forecast...")
        
        url = "https://api.open-meteo.com/v1/forecast"
        params = {
            'latitude': 38.141348,
            'longitude': 24.007165,
            'hourly': ['temperature_2m', 'cloud_cover', 'shortwave_radiation'],
            'forecast_days': 7,
            'timezone': 'Europe/Athens'
        }
        
        try:
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            
            # Process weather data
            weather_data = []
            for i, time_str in enumerate(data['hourly']['time']):
                dt = datetime.fromisoformat(time_str.replace('Z', '+00:00')).replace(tzinfo=None)
                
                # Calculate solar position
                sun_elevation, sun_azimuth = self._calculate_solar_position(dt)
                
                weather_data.append({
                    'datetime': dt,
                    'temperature': data['hourly']['temperature_2m'][i],
                    'cloud_cover': data['hourly']['cloud_cover'][i],
                    'ghi': data['hourly']['shortwave_radiation'][i],
                    'sun_elevation': sun_elevation,
                    'sun_azimuth': sun_azimuth
                })
            
            logger.info(f"✅ Weather data retrieved: {len(weather_data)} hours")
            return weather_data
            
        except Exception as e:
            logger.error(f"❌ Weather API error: {e}")
            return self._generate_fallback_weather()
    
    def _calculate_solar_position(self, dt: datetime) -> Tuple[float, float]:
        """Calculate solar elevation and azimuth"""
        latitude = math.radians(38.141348)
        
        day_of_year = dt.timetuple().tm_yday
        declination = math.radians(23.45) * math.sin(math.radians(360 * (284 + day_of_year) / 365))
        hour_angle = math.radians(15 * (dt.hour + dt.minute/60 - 12))
        
        elevation = math.asin(
            math.sin(declination) * math.sin(latitude) +
            math.cos(declination) * math.cos(latitude) * math.cos(hour_angle)
        )
        
        azimuth = math.atan2(
            math.sin(hour_angle),
            math.cos(hour_angle) * math.sin(latitude) - 
            math.tan(declination) * math.cos(latitude)
        )
        
        return math.degrees(elevation), math.degrees(azimuth)
    
    def _generate_fallback_weather(self) -> List[Dict]:
        """Generate fallback weather if API fails"""
        logger.info("🔄 Generating fallback weather...")
        
        weather_data = []
        base_time = datetime.now().replace(minute=0, second=0, microsecond=0)
        
        for i in range(168):  # 7 days
            dt = base_time + timedelta(hours=i)
            sun_elevation, sun_azimuth = self._calculate_solar_position(dt)
            
            # Generate realistic weather
            day_of_year = dt.timetuple().tm_yday
            base_temp = 20 + 8 * math.sin((day_of_year - 80) * 2 * math.pi / 365)
            temperature = base_temp + 8 * math.sin((dt.hour - 6) * math.pi / 12)
            
            if 6 <= dt.hour <= 18:
                ghi = max(0, 800 * math.sin(math.radians(max(0, sun_elevation))))
            else:
                ghi = 0
            
            weather_data.append({
                'datetime': dt,
                'temperature': max(15, min(35, temperature)),
                'cloud_cover': 20,  # Assume light clouds
                'ghi': ghi,
                'sun_elevation': sun_elevation,
                'sun_azimuth': sun_azimuth
            })
        
        return weather_data
    
    def generate_hybrid_forecast(self) -> Dict[str, Any]:
        """Generate hybrid ML + Grade A forecast"""
        logger.info("🔮 Generating Hybrid ML + Grade A Forecast...")
        logger.info("=" * 70)
        
        # Get weather data
        weather_data = self.get_weather_forecast()
        
        # Engineer features for ML models
        features_df = self.feature_engineer.engineer_features(weather_data)
        
        # Generate predictions
        daily_forecasts = {'system_1': [], 'system_2': []}
        hourly_details = []
        
        current_day = None
        daily_data = {'system_1': [], 'system_2': []}
        
        for i, weather_point in enumerate(weather_data):
            dt = weather_point['datetime']
            
            # Get features for this hour
            hour_features = features_df.iloc[i:i+1]
            
            predictions = {}
            
            for system_id in ['system_1', 'system_2']:
                # ML prediction
                ml_pred, confidence = self.ml_predictors[system_id].get_ensemble_prediction(hour_features)
                
                # Grade A prediction
                grade_a_pred = self.grade_a_models[system_id].predict_hourly(
                    ghi=weather_point['ghi'],
                    temperature=weather_point['temperature'],
                    sun_elevation=weather_point['sun_elevation'],
                    cloud_cover=weather_point['cloud_cover']
                )
                
                # Hybrid prediction (weighted by confidence)
                if confidence > 0.8:
                    # High confidence - use ML
                    final_pred = ml_pred
                    method_used = 'ML_Ensemble'
                elif confidence > 0.6:
                    # Medium confidence - blend
                    final_pred = 0.7 * ml_pred + 0.3 * grade_a_pred
                    method_used = 'ML_Grade_A_Blend'
                else:
                    # Low confidence - use Grade A
                    final_pred = grade_a_pred
                    method_used = 'Grade_A_Fallback'
                
                predictions[system_id] = {
                    'ml_prediction': ml_pred,
                    'grade_a_prediction': grade_a_pred,
                    'final_prediction': final_pred,
                    'confidence': confidence,
                    'method_used': method_used
                }
            
            hourly_details.append({
                'datetime': dt,
                'weather': weather_point,
                'predictions': predictions
            })
            
            # Group by day
            day_key = dt.date()
            if current_day != day_key:
                if current_day is not None:
                    # Process previous day
                    for system_id in ['system_1', 'system_2']:
                        daily_total = sum(daily_data[system_id])
                        daily_forecasts[system_id].append({
                            'date': current_day,
                            'total_yield_kwh': daily_total
                        })
                
                current_day = day_key
                daily_data = {'system_1': [], 'system_2': []}
            
            # Add to daily totals
            for system_id in ['system_1', 'system_2']:
                daily_data[system_id].append(predictions[system_id]['final_prediction'])
        
        # Process last day
        if current_day is not None:
            for system_id in ['system_1', 'system_2']:
                daily_total = sum(daily_data[system_id])
                daily_forecasts[system_id].append({
                    'date': current_day,
                    'total_yield_kwh': daily_total
                })
        
        # Calculate totals
        totals = {}
        for system_id in ['system_1', 'system_2']:
            total_7_days = sum(day['total_yield_kwh'] for day in daily_forecasts[system_id])
            totals[system_id] = total_7_days
        
        # Analyze method usage
        method_stats = {}
        for system_id in ['system_1', 'system_2']:
            methods = [h['predictions'][system_id]['method_used'] for h in hourly_details]
            method_stats[system_id] = {
                'ML_Ensemble': methods.count('ML_Ensemble'),
                'ML_Grade_A_Blend': methods.count('ML_Grade_A_Blend'),
                'Grade_A_Fallback': methods.count('Grade_A_Fallback')
            }
        
        return {
            'forecast_generated': datetime.now(),
            'model_type': 'Hybrid ML Ensemble + Grade A Mathematical',
            'systems': {
                'system_1': {
                    'name': 'Σπίτι Πάνω',
                    'daily_forecasts': daily_forecasts['system_1'],
                    'total_7_days_kwh': totals['system_1'],
                    'method_usage': method_stats['system_1']
                },
                'system_2': {
                    'name': 'Σπίτι Κάτω',
                    'daily_forecasts': daily_forecasts['system_2'],
                    'total_7_days_kwh': totals['system_2'],
                    'method_usage': method_stats['system_2']
                }
            },
            'combined_total_kwh': totals['system_1'] + totals['system_2'],
            'hourly_details': hourly_details[:24],  # First day details
            'weather_source': 'Open-Meteo API + ML Feature Engineering'
        }
    
    def display_hybrid_forecast(self, forecast: Dict[str, Any]):
        """Display hybrid forecast results"""
        logger.info("\n🔮 HYBRID ML + GRADE A FORECAST")
        logger.info("=" * 80)
        
        logger.info(f"📅 Generated: {forecast['forecast_generated'].strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"🤖 Model: {forecast['model_type']}")
        logger.info(f"🌤️ Weather: {forecast['weather_source']}")
        
        logger.info(f"\n📊 7-DAY TOTALS:")
        logger.info(f"   🏠 System 1 (Σπίτι Πάνω): {forecast['systems']['system_1']['total_7_days_kwh']:.1f} kWh")
        logger.info(f"   🏠 System 2 (Σπίτι Κάτω): {forecast['systems']['system_2']['total_7_days_kwh']:.1f} kWh")
        logger.info(f"   🔋 Combined Total: {forecast['combined_total_kwh']:.1f} kWh")
        
        logger.info(f"\n🤖 PREDICTION METHOD USAGE:")
        for system_id, system_name in [('system_1', 'System 1'), ('system_2', 'System 2')]:
            methods = forecast['systems'][system_id]['method_usage']
            total_hours = sum(methods.values())
            
            logger.info(f"   {system_name}:")
            logger.info(f"     ML Ensemble: {methods['ML_Ensemble']}/{total_hours} hours ({methods['ML_Ensemble']/total_hours*100:.1f}%)")
            logger.info(f"     ML+Grade A Blend: {methods['ML_Grade_A_Blend']}/{total_hours} hours ({methods['ML_Grade_A_Blend']/total_hours*100:.1f}%)")
            logger.info(f"     Grade A Fallback: {methods['Grade_A_Fallback']}/{total_hours} hours ({methods['Grade_A_Fallback']/total_hours*100:.1f}%)")
        
        logger.info(f"\n📅 DAILY BREAKDOWN:")
        for i in range(min(7, len(forecast['systems']['system_1']['daily_forecasts']))):
            day_s1 = forecast['systems']['system_1']['daily_forecasts'][i]
            day_s2 = forecast['systems']['system_2']['daily_forecasts'][i]
            
            date_str = day_s1['date'].strftime('%A, %Y-%m-%d')
            daily_total = day_s1['total_yield_kwh'] + day_s2['total_yield_kwh']
            
            logger.info(f"   📅 {date_str}:")
            logger.info(f"      🏠 System 1: {day_s1['total_yield_kwh']:.1f} kWh")
            logger.info(f"      🏠 System 2: {day_s2['total_yield_kwh']:.1f} kWh")
            logger.info(f"      🔋 Daily Total: {daily_total:.1f} kWh")

def main():
    """Generate hybrid ML + Grade A forecast"""
    logger.info("🔮 Starting Hybrid ML + Grade A 7-Day Forecast")
    logger.info("=" * 70)
    
    # Initialize hybrid system
    hybrid_system = HybridForecastSystem()
    
    # Generate forecast
    forecast = hybrid_system.generate_hybrid_forecast()
    
    # Display results
    hybrid_system.display_hybrid_forecast(forecast)
    
    # Save forecast
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    forecast_file = f"forecasts/hybrid_ml_forecast_{timestamp}.json"
    
    os.makedirs("forecasts", exist_ok=True)
    
    # Save (excluding hourly details for size)
    save_forecast = forecast.copy()
    save_forecast.pop('hourly_details', None)
    
    with open(forecast_file, 'w') as f:
        json.dump(save_forecast, f, indent=2, default=str, ensure_ascii=False)
    
    logger.info(f"\n💾 Hybrid forecast saved to: {forecast_file}")
    logger.info("\n✅ Hybrid ML + Grade A forecast completed!")
    
    return forecast

if __name__ == "__main__":
    forecast = main()
