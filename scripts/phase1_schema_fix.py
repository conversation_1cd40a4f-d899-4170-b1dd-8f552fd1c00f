#!/usr/bin/env python3
"""
Phase 1: Database Schema Fix
Adds missing total_yield columns and creates necessary indexes
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import psycopg2
from psycopg2.extras import RealDictCursor
import pandas as pd
from datetime import datetime
import logging
import subprocess

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseSchemaFixer:
    """Database schema fixer for Phase 1"""
    
    def __init__(self):
        self.db_config = {
            'host': 'localhost',
            'database': 'solar_prediction',
            'user': 'postgres',
            'password': 'postgres'
        }
        
        self.backup_created = False
        self.changes_made = []
    
    def connect_database(self):
        """Connect to database"""
        try:
            conn = psycopg2.connect(**self.db_config)
            logger.info("✅ Database connection successful")
            return conn
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            return None
    
    def create_backup(self):
        """Create database backup before making changes"""
        
        logger.info("💾 Creating database backup...")
        
        try:
            # Create backup directory
            os.makedirs('backups', exist_ok=True)
            
            # Generate backup filename
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_file = f"backups/solar_prediction_backup_{timestamp}.sql"
            
            # Create backup using pg_dump
            cmd = [
                'pg_dump',
                '-h', self.db_config['host'],
                '-U', self.db_config['user'],
                '-d', self.db_config['database'],
                '-f', backup_file
            ]
            
            # Set password environment variable
            env = os.environ.copy()
            env['PGPASSWORD'] = self.db_config['password']
            
            result = subprocess.run(cmd, env=env, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info(f"✅ Backup created: {backup_file}")
                self.backup_created = True
                return backup_file
            else:
                logger.error(f"❌ Backup failed: {result.stderr}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Backup creation failed: {e}")
            return None
    
    def check_column_exists(self, cur, table_name, column_name):
        """Check if column exists in table"""
        
        cur.execute(f"""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = '{table_name}' AND column_name = '{column_name}'
        """)
        
        return cur.fetchone() is not None
    
    def add_total_yield_column(self, cur, table_name):
        """Add total_yield column to table"""
        
        logger.info(f"🔧 Adding total_yield column to {table_name}...")
        
        try:
            # Check if column already exists
            if self.check_column_exists(cur, table_name, 'total_yield'):
                logger.info(f"   ✅ total_yield column already exists in {table_name}")
                return True
            
            # Add the column
            cur.execute(f"ALTER TABLE {table_name} ADD COLUMN total_yield FLOAT")
            logger.info(f"   ✅ Added total_yield column to {table_name}")
            
            self.changes_made.append(f"Added total_yield column to {table_name}")
            return True
            
        except Exception as e:
            logger.error(f"   ❌ Failed to add total_yield column to {table_name}: {e}")
            return False
    
    def populate_total_yield_data(self, cur, table_name):
        """Populate total_yield column with data from yield_today"""
        
        logger.info(f"📊 Populating total_yield data in {table_name}...")
        
        try:
            # Check if yield_today column exists
            if not self.check_column_exists(cur, table_name, 'yield_today'):
                logger.warning(f"   ⚠️ yield_today column not found in {table_name}")
                return False
            
            # Update total_yield from yield_today (convert kWh to Wh)
            cur.execute(f"""
                UPDATE {table_name} 
                SET total_yield = yield_today * 1000 
                WHERE yield_today IS NOT NULL AND total_yield IS NULL
            """)
            
            # Get count of updated records
            updated_count = cur.rowcount
            logger.info(f"   ✅ Updated {updated_count:,} records in {table_name}")
            
            self.changes_made.append(f"Populated {updated_count:,} total_yield records in {table_name}")
            return True
            
        except Exception as e:
            logger.error(f"   ❌ Failed to populate total_yield data in {table_name}: {e}")
            return False
    
    def create_indexes(self, cur):
        """Create necessary indexes for performance"""
        
        logger.info("🔍 Creating performance indexes...")
        
        indexes = [
            ("idx_solax_timestamp", "solax_data", "timestamp"),
            ("idx_solax2_timestamp", "solax_data2", "timestamp"),
            ("idx_solax_total_yield", "solax_data", "total_yield"),
            ("idx_solax2_total_yield", "solax_data2", "total_yield"),
        ]
        
        # Add weather_data indexes if table exists
        cur.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_name = 'weather_data'
        """)
        
        if cur.fetchone():
            indexes.append(("idx_weather_timestamp", "weather_data", "timestamp"))
        
        created_count = 0
        
        for index_name, table_name, column_name in indexes:
            try:
                # Check if index already exists
                cur.execute(f"""
                    SELECT indexname 
                    FROM pg_indexes 
                    WHERE indexname = '{index_name}'
                """)
                
                if cur.fetchone():
                    logger.info(f"   ✅ Index {index_name} already exists")
                    continue
                
                # Create index
                cur.execute(f"CREATE INDEX {index_name} ON {table_name}({column_name})")
                logger.info(f"   ✅ Created index: {index_name}")
                
                created_count += 1
                self.changes_made.append(f"Created index: {index_name}")
                
            except Exception as e:
                logger.warning(f"   ⚠️ Failed to create index {index_name}: {e}")
        
        logger.info(f"📊 Created {created_count} new indexes")
        return True
    
    def validate_schema_changes(self, cur):
        """Validate that schema changes were successful"""
        
        logger.info("✅ Validating schema changes...")
        
        validation_results = {}
        
        # Check total_yield columns
        for table_name in ['solax_data', 'solax_data2']:
            has_column = self.check_column_exists(cur, table_name, 'total_yield')
            validation_results[f"{table_name}_total_yield"] = has_column
            
            if has_column:
                # Check data population
                cur.execute(f"""
                    SELECT COUNT(*) as total_count,
                           COUNT(total_yield) as populated_count
                    FROM {table_name}
                """)
                
                counts = cur.fetchone()
                population_rate = (counts['populated_count'] / counts['total_count']) * 100 if counts['total_count'] > 0 else 0
                
                validation_results[f"{table_name}_population"] = {
                    'total_records': counts['total_count'],
                    'populated_records': counts['populated_count'],
                    'population_rate': population_rate
                }
                
                logger.info(f"   ✅ {table_name}: {counts['populated_count']:,}/{counts['total_count']:,} records populated ({population_rate:.1f}%)")
            else:
                logger.error(f"   ❌ {table_name}: total_yield column missing")
        
        # Check indexes
        cur.execute("SELECT indexname FROM pg_indexes WHERE schemaname = 'public'")
        indexes = [row['indexname'] for row in cur.fetchall()]
        
        required_indexes = ['idx_solax_timestamp', 'idx_solax2_timestamp']
        for index_name in required_indexes:
            has_index = index_name in indexes
            validation_results[f"index_{index_name}"] = has_index
            
            status = "✅" if has_index else "❌"
            logger.info(f"   {status} Index {index_name}: {'exists' if has_index else 'missing'}")
        
        return validation_results
    
    def run_schema_fixes(self):
        """Run all schema fixes"""
        
        logger.info("🚀 Starting schema fixes...")
        
        # Create backup first
        backup_file = self.create_backup()
        if not backup_file:
            logger.error("❌ Cannot proceed without backup")
            return False
        
        conn = self.connect_database()
        if not conn:
            return False
        
        try:
            cur = conn.cursor(cursor_factory=RealDictCursor)
            
            # Start transaction
            conn.autocommit = False
            
            success = True
            
            # Add total_yield columns
            for table_name in ['solax_data', 'solax_data2']:
                if not self.add_total_yield_column(cur, table_name):
                    success = False
                    break
                
                if not self.populate_total_yield_data(cur, table_name):
                    success = False
                    break
            
            # Create indexes
            if success:
                success = self.create_indexes(cur)
            
            if success:
                # Commit changes
                conn.commit()
                logger.info("✅ All schema changes committed successfully")
                
                # Validate changes
                validation_results = self.validate_schema_changes(cur)
                
                conn.close()
                return validation_results
            else:
                # Rollback changes
                conn.rollback()
                logger.error("❌ Schema changes rolled back due to errors")
                conn.close()
                return False
                
        except Exception as e:
            conn.rollback()
            conn.close()
            logger.error(f"❌ Schema fix failed: {e}")
            return False
    
    def generate_fix_report(self, validation_results):
        """Generate schema fix report"""
        
        report = {
            'fix_date': datetime.now().isoformat(),
            'backup_created': self.backup_created,
            'changes_made': self.changes_made,
            'validation_results': validation_results,
            'status': 'success' if validation_results else 'failed'
        }
        
        # Save report
        os.makedirs('reports/phase1', exist_ok=True)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_path = f'reports/phase1/schema_fix_report_{timestamp}.json'
        
        import json
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        logger.info(f"📋 Fix report saved to: {report_path}")
        
        return report
    
    def print_summary(self, report):
        """Print fix summary"""
        
        print("\n" + "="*80)
        print("🔧 DATABASE SCHEMA FIX SUMMARY")
        print("="*80)
        print(f"📅 Fix Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"💾 Backup Created: {'✅' if report['backup_created'] else '❌'}")
        print()
        
        # Changes made
        if report['changes_made']:
            print("🔧 CHANGES MADE:")
            for change in report['changes_made']:
                print(f"   ✅ {change}")
            print()
        
        # Validation results
        if report['validation_results']:
            print("✅ VALIDATION RESULTS:")
            validation = report['validation_results']
            
            for table_name in ['solax_data', 'solax_data2']:
                has_column = validation.get(f"{table_name}_total_yield", False)
                population = validation.get(f"{table_name}_population", {})
                
                print(f"   📊 {table_name}:")
                print(f"      total_yield column: {'✅' if has_column else '❌'}")
                
                if population:
                    print(f"      Data populated: {population['populated_records']:,}/{population['total_records']:,} ({population['population_rate']:.1f}%)")
            print()
        
        # Overall status
        if report['status'] == 'success':
            print("🎉 SCHEMA FIX COMPLETED SUCCESSFULLY!")
            print("🚀 Ready to proceed to Phase 2: Feature Engineering")
        else:
            print("❌ SCHEMA FIX FAILED!")
            print("🔧 Please review errors and try again")
        
        print("="*80)

def main():
    """Main schema fix function"""
    
    print("🔧 PHASE 1: DATABASE SCHEMA FIX")
    print("="*60)
    
    try:
        # Initialize fixer
        fixer = DatabaseSchemaFixer()
        
        # Run schema fixes
        validation_results = fixer.run_schema_fixes()
        
        if validation_results:
            # Generate report
            report = fixer.generate_fix_report(validation_results)
            
            # Print summary
            fixer.print_summary(report)
            
            print(f"\n📋 Detailed report saved")
            print("🎉 Phase 1 schema fix completed successfully!")
            
            return True
        else:
            print("❌ Schema fix failed")
            return False
            
    except Exception as e:
        print(f"❌ Schema fix failed: {e}")
        logger.exception("Schema fix failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
