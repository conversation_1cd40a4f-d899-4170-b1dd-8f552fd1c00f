#!/usr/bin/env python3
"""
Phase 1: Database Schema Analysis & Preparation
Analyzes current database schema and prepares for total_yield column addition
"""

import sys
import os
sys.path.append('/home/<USER>/solar-prediction-project')

import psycopg2
from psycopg2.extras import RealDictCursor
import pandas as pd
from datetime import datetime, timedelta
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseAnalyzer:
    """Database schema analyzer for Phase 1"""
    
    def __init__(self):
        self.db_config = {
            'host': 'localhost',
            'database': 'solar_prediction',
            'user': 'postgres',
            'password': 'postgres'
        }
        
        self.analysis_results = {}
    
    def connect_database(self):
        """Connect to database"""
        try:
            conn = psycopg2.connect(**self.db_config)
            logger.info("✅ Database connection successful")
            return conn
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            return None
    
    def analyze_existing_schema(self):
        """Analyze existing database schema"""
        
        logger.info("🔍 Analyzing current database schema...")
        
        conn = self.connect_database()
        if not conn:
            return False
        
        try:
            cur = conn.cursor(cursor_factory=RealDictCursor)
            
            # Get all tables
            cur.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public'
                ORDER BY table_name
            """)
            
            tables = [row['table_name'] for row in cur.fetchall()]
            self.analysis_results['tables'] = tables
            
            logger.info(f"📋 Found {len(tables)} tables: {', '.join(tables)}")
            
            # Analyze each solar table
            for table_name in ['solax_data', 'solax_data2']:
                if table_name in tables:
                    self.analyze_table_schema(cur, table_name)
                    self.analyze_table_data(cur, table_name)
                else:
                    logger.warning(f"⚠️ Table {table_name} not found")
            
            # Check weather data
            if 'weather_data' in tables:
                self.analyze_weather_data(cur)
            
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"❌ Schema analysis failed: {e}")
            conn.close()
            return False
    
    def analyze_table_schema(self, cur, table_name):
        """Analyze schema of a specific table"""
        
        logger.info(f"📊 Analyzing {table_name} schema...")
        
        cur.execute(f"""
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = '{table_name}'
            ORDER BY ordinal_position
        """)
        
        columns = cur.fetchall()
        
        # Check for total_yield column
        has_total_yield = any(col['column_name'] == 'total_yield' for col in columns)
        
        self.analysis_results[table_name] = {
            'columns': columns,
            'has_total_yield': has_total_yield,
            'column_count': len(columns)
        }
        
        logger.info(f"   📝 {table_name}: {len(columns)} columns, total_yield: {'✅' if has_total_yield else '❌'}")
        
        # Log important columns
        important_columns = ['timestamp', 'yield_today', 'total_yield', 'ac_power', 'battery_soc']
        for col_name in important_columns:
            exists = any(col['column_name'] == col_name for col in columns)
            status = "✅" if exists else "❌"
            logger.info(f"      {status} {col_name}")
    
    def analyze_table_data(self, cur, table_name):
        """Analyze data in a specific table"""
        
        logger.info(f"📈 Analyzing {table_name} data...")
        
        # Get record count
        cur.execute(f"SELECT COUNT(*) as count FROM {table_name}")
        record_count = cur.fetchone()['count']
        
        # Get date range
        cur.execute(f"SELECT MIN(timestamp) as earliest, MAX(timestamp) as latest FROM {table_name}")
        date_range = cur.fetchone()
        
        # Get sample data
        cur.execute(f"""
            SELECT timestamp, yield_today, ac_power, battery_soc 
            FROM {table_name} 
            ORDER BY timestamp DESC 
            LIMIT 5
        """)
        sample_data = cur.fetchall()
        
        self.analysis_results[table_name].update({
            'record_count': record_count,
            'date_range': date_range,
            'sample_data': sample_data
        })
        
        logger.info(f"   📊 {table_name}: {record_count:,} records")
        logger.info(f"   📅 Date range: {date_range['earliest']} to {date_range['latest']}")
        
        # Check for recent data (last 24 hours)
        cur.execute(f"""
            SELECT COUNT(*) as recent_count 
            FROM {table_name} 
            WHERE timestamp >= NOW() - INTERVAL '24 hours'
        """)
        recent_count = cur.fetchone()['recent_count']
        logger.info(f"   ⏰ Recent data (24h): {recent_count} records")
    
    def analyze_weather_data(self, cur):
        """Analyze weather data table"""
        
        logger.info("🌤️ Analyzing weather data...")
        
        cur.execute("SELECT COUNT(*) as count FROM weather_data")
        weather_count = cur.fetchone()['count']
        
        cur.execute("SELECT MIN(timestamp) as earliest, MAX(timestamp) as latest FROM weather_data")
        weather_range = cur.fetchone()
        
        self.analysis_results['weather_data'] = {
            'record_count': weather_count,
            'date_range': weather_range
        }
        
        logger.info(f"   📊 weather_data: {weather_count:,} records")
        logger.info(f"   📅 Date range: {weather_range['earliest']} to {weather_range['latest']}")
    
    def generate_analysis_report(self):
        """Generate comprehensive analysis report"""
        
        logger.info("📋 Generating analysis report...")
        
        report = {
            'analysis_date': datetime.now().isoformat(),
            'database_status': 'connected',
            'tables_found': len(self.analysis_results.get('tables', [])),
            'schema_issues': [],
            'recommendations': []
        }
        
        # Check schema issues
        for table_name in ['solax_data', 'solax_data2']:
            if table_name in self.analysis_results:
                table_info = self.analysis_results[table_name]
                
                if not table_info['has_total_yield']:
                    report['schema_issues'].append(f"Missing total_yield column in {table_name}")
                    report['recommendations'].append(f"Add total_yield column to {table_name}")
                
                if table_info['record_count'] == 0:
                    report['schema_issues'].append(f"No data in {table_name}")
                    report['recommendations'].append(f"Populate {table_name} with historical data")
        
        # Overall assessment
        if not report['schema_issues']:
            report['status'] = 'ready_for_phase2'
            report['recommendations'].append("Schema is ready, proceed to Phase 2")
        else:
            report['status'] = 'needs_schema_fixes'
            report['recommendations'].insert(0, "Fix schema issues before proceeding")
        
        return report
    
    def save_analysis_report(self, report):
        """Save analysis report to file"""
        
        os.makedirs('reports/phase1', exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_path = f'reports/phase1/database_analysis_{timestamp}.json'
        
        import json
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        logger.info(f"📋 Analysis report saved to: {report_path}")
        
        return report_path
    
    def print_summary(self, report):
        """Print analysis summary"""
        
        print("\n" + "="*80)
        print("📊 DATABASE SCHEMA ANALYSIS SUMMARY")
        print("="*80)
        print(f"📅 Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🗄️ Database: {self.db_config['database']}")
        print(f"📋 Tables Found: {report['tables_found']}")
        print()
        
        # Schema status
        if report['schema_issues']:
            print("❌ SCHEMA ISSUES FOUND:")
            for issue in report['schema_issues']:
                print(f"   • {issue}")
            print()
        else:
            print("✅ SCHEMA STATUS: All required columns present")
            print()
        
        # Data summary
        print("📊 DATA SUMMARY:")
        for table_name in ['solax_data', 'solax_data2']:
            if table_name in self.analysis_results:
                table_info = self.analysis_results[table_name]
                total_yield_status = "✅" if table_info['has_total_yield'] else "❌"
                print(f"   📋 {table_name}:")
                print(f"      Records: {table_info['record_count']:,}")
                print(f"      Columns: {table_info['column_count']}")
                print(f"      total_yield: {total_yield_status}")
                print(f"      Date range: {table_info['date_range']['earliest']} to {table_info['date_range']['latest']}")
        print()
        
        # Recommendations
        print("🎯 RECOMMENDATIONS:")
        for rec in report['recommendations']:
            print(f"   • {rec}")
        print()
        
        # Next steps
        print("📋 NEXT STEPS:")
        if report['status'] == 'ready_for_phase2':
            print("   ✅ Schema is ready")
            print("   🚀 Proceed to Phase 2: Feature Engineering")
        else:
            print("   🔧 Fix schema issues first")
            print("   📊 Run: python scripts/phase1_schema_fix.py")
        
        print("="*80)

def main():
    """Main analysis function"""
    
    print("🔍 PHASE 1: DATABASE SCHEMA ANALYSIS")
    print("="*60)
    
    try:
        # Initialize analyzer
        analyzer = DatabaseAnalyzer()
        
        # Run analysis
        success = analyzer.analyze_existing_schema()
        
        if success:
            # Generate report
            report = analyzer.generate_analysis_report()
            
            # Save report
            report_path = analyzer.save_analysis_report(report)
            
            # Print summary
            analyzer.print_summary(report)
            
            print(f"\n📋 Detailed report saved to: {report_path}")
            print("🎉 Phase 1 analysis completed successfully!")
            
            return report['status'] == 'ready_for_phase2'
        else:
            print("❌ Analysis failed - check database connection")
            return False
            
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        logger.exception("Analysis failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
