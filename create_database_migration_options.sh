#!/bin/bash

# Solar Prediction System - Database Migration Options
# Creates different packages based on data requirements

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🗄️ SOLAR PREDICTION DATABASE MIGRATION OPTIONS${NC}"
echo "============================================================"
echo

echo -e "${YELLOW}📊 Current Database Status:${NC}"
echo "   • Total Size: ~650MB"
echo "   • Main Tables: 98 tables"
echo "   • Core Data: solax_data (162MB) + solax_data2 (75MB)"
echo "   • ML Training Data: normalized_training_data (209MB)"
echo "   • Weather Data: weather_data (3.8MB)"
echo

echo -e "${BLUE}🎯 Available Migration Options:${NC}"
echo "1. 📦 FULL DATABASE PACKAGE (Recommended)"
echo "2. 🎯 ESSENTIAL DATA ONLY"
echo "3. 🌱 FRESH START (Empty database)"
echo

read -p "Choose option (1-3): " choice

case $choice in
    1)
        echo -e "${GREEN}📦 Creating FULL DATABASE PACKAGE...${NC}"
        create_full_database_package
        ;;
    2)
        echo -e "${YELLOW}🎯 Creating ESSENTIAL DATA PACKAGE...${NC}"
        create_essential_data_package
        ;;
    3)
        echo -e "${BLUE}🌱 Creating FRESH START PACKAGE...${NC}"
        create_fresh_start_package
        ;;
    *)
        echo -e "${RED}❌ Invalid option${NC}"
        exit 1
        ;;
esac

# Function: Full Database Package
create_full_database_package() {
    echo
    echo -e "${BLUE}📦 OPTION 1: FULL DATABASE PACKAGE${NC}"
    echo "============================================================"
    echo "✅ Includes ALL your historical data"
    echo "✅ Complete ML training datasets"
    echo "✅ All weather and system data"
    echo "✅ Ready to use immediately"
    echo "⚠️  Large package size (~100-150MB compressed)"
    echo

    PACKAGE_DIR="solar-prediction-full-data"
    rm -rf "$PACKAGE_DIR"
    mkdir -p "$PACKAGE_DIR"

    # Create database dump
    echo "🗄️ Creating complete database dump..."
    pg_dump -h localhost -p 5433 -U postgres -d solar_prediction \
        --no-owner --no-privileges --clean --if-exists \
        > "$PACKAGE_DIR/complete_database.sql"

    # Create compressed dump for faster transfer
    echo "🗜️ Compressing database dump..."
    gzip "$PACKAGE_DIR/complete_database.sql"

    # Create restore script
    cat > "$PACKAGE_DIR/restore_database.sh" << 'EOF'
#!/bin/bash
echo "🗄️ Restoring complete database..."

# Wait for PostgreSQL to be ready
echo "⏳ Waiting for PostgreSQL..."
until pg_isready -h postgres -p 5432 -U postgres; do
    sleep 2
done

# Restore database
echo "📥 Restoring data..."
gunzip -c complete_database.sql.gz | psql -h postgres -p 5432 -U postgres -d solar_prediction

echo "✅ Database restored successfully!"
EOF

    chmod +x "$PACKAGE_DIR/restore_database.sh"

    # Create Docker override for data restoration
    cat > "$PACKAGE_DIR/docker-compose.override.yml" << 'EOF'
version: '3.8'

services:
  postgres:
    volumes:
      - ./complete_database.sql.gz:/docker-entrypoint-initdb.d/complete_database.sql.gz
      - ./restore_database.sh:/docker-entrypoint-initdb.d/restore_database.sh

  solar-prediction:
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      - RESTORE_DATABASE=true
EOF

    echo -e "${GREEN}✅ Full database package created${NC}"
    echo "📦 Package: $PACKAGE_DIR"
    echo "📏 Database dump: $(du -h $PACKAGE_DIR/complete_database.sql.gz | cut -f1)"
}

# Function: Essential Data Package
create_essential_data_package() {
    echo
    echo -e "${BLUE}🎯 OPTION 2: ESSENTIAL DATA PACKAGE${NC}"
    echo "============================================================"
    echo "✅ Core solar system data (last 6 months)"
    echo "✅ Essential weather data"
    echo "✅ Latest ML models"
    echo "✅ Smaller package size (~20-30MB)"
    echo "⚠️  Limited historical data"
    echo

    PACKAGE_DIR="solar-prediction-essential-data"
    rm -rf "$PACKAGE_DIR"
    mkdir -p "$PACKAGE_DIR"

    # Create essential data dump
    echo "🎯 Creating essential data dump..."
    
    # Export recent data (last 6 months)
    psql -h localhost -p 5433 -U postgres -d solar_prediction << 'EOF' > "$PACKAGE_DIR/essential_data.sql"
-- Essential Solar Prediction Data Export
-- Last 6 months of data + essential tables

\echo 'Creating essential data export...'

-- Core tables structure
\copy (SELECT * FROM solax_data WHERE timestamp >= NOW() - INTERVAL '6 months') TO 'solax_data_recent.csv' WITH CSV HEADER;
\copy (SELECT * FROM solax_data2 WHERE timestamp >= NOW() - INTERVAL '6 months') TO 'solax_data2_recent.csv' WITH CSV HEADER;
\copy (SELECT * FROM weather_data WHERE timestamp >= NOW() - INTERVAL '6 months') TO 'weather_data_recent.csv' WITH CSV HEADER;
\copy (SELECT * FROM predictions WHERE timestamp >= NOW() - INTERVAL '3 months') TO 'predictions_recent.csv' WITH CSV HEADER;

-- Essential configuration tables
\copy (SELECT * FROM tariff_configs) TO 'tariff_configs.csv' WITH CSV HEADER;
\copy (SELECT * FROM system_configuration) TO 'system_configuration.csv' WITH CSV HEADER;
\copy (SELECT * FROM model_registry) TO 'model_registry.csv' WITH CSV HEADER;

\echo 'Essential data exported successfully!'
EOF

    # Create restoration script for essential data
    cat > "$PACKAGE_DIR/restore_essential_data.sh" << 'EOF'
#!/bin/bash
echo "🎯 Restoring essential data..."

# Wait for PostgreSQL
until pg_isready -h postgres -p 5432 -U postgres; do
    sleep 2
done

# Create tables first
psql -h postgres -p 5432 -U postgres -d solar_prediction << 'SQL'
-- Create essential tables
CREATE TABLE IF NOT EXISTS solax_data (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMP WITH TIME ZONE,
    yield_today DECIMAL(10,2),
    yield_total DECIMAL(10,2),
    ac_power DECIMAL(10,2),
    soc DECIMAL(5,2),
    bat_power DECIMAL(10,2),
    temperature DECIMAL(5,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS solax_data2 (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMP WITH TIME ZONE,
    yield_today DECIMAL(10,2),
    yield_total DECIMAL(10,2),
    ac_power DECIMAL(10,2),
    soc DECIMAL(5,2),
    bat_power DECIMAL(10,2),
    temperature DECIMAL(5,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS weather_data (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMP WITH TIME ZONE,
    temperature_2m DECIMAL(5,2),
    relative_humidity_2m DECIMAL(5,2),
    cloud_cover DECIMAL(5,2),
    global_horizontal_irradiance DECIMAL(8,2),
    is_forecast BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS predictions (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMP WITH TIME ZONE,
    system_id VARCHAR(20),
    predicted_power DECIMAL(10,2),
    confidence DECIMAL(5,4),
    model_version VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
SQL

# Import CSV data
echo "📥 Importing CSV data..."
psql -h postgres -p 5432 -U postgres -d solar_prediction << 'SQL'
\copy solax_data FROM 'solax_data_recent.csv' WITH CSV HEADER;
\copy solax_data2 FROM 'solax_data2_recent.csv' WITH CSV HEADER;
\copy weather_data FROM 'weather_data_recent.csv' WITH CSV HEADER;
\copy predictions FROM 'predictions_recent.csv' WITH CSV HEADER;
SQL

echo "✅ Essential data restored!"
EOF

    chmod +x "$PACKAGE_DIR/restore_essential_data.sh"

    echo -e "${GREEN}✅ Essential data package created${NC}"
    echo "📦 Package: $PACKAGE_DIR"
}

# Function: Fresh Start Package
create_fresh_start_package() {
    echo
    echo -e "${BLUE}🌱 OPTION 3: FRESH START PACKAGE${NC}"
    echo "============================================================"
    echo "✅ Clean installation"
    echo "✅ Will collect new data automatically"
    echo "✅ Smallest package size (~5-10MB)"
    echo "✅ Good for new installations"
    echo "⚠️  No historical data"
    echo

    PACKAGE_DIR="solar-prediction-fresh-start"
    rm -rf "$PACKAGE_DIR"
    mkdir -p "$PACKAGE_DIR"

    # Create minimal initialization
    cat > "$PACKAGE_DIR/init_fresh.sql" << 'EOF'
-- Fresh Start Database Initialization
-- Creates tables and basic configuration

-- Core tables
CREATE TABLE IF NOT EXISTS solax_data (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    yield_today DECIMAL(10,2),
    yield_total DECIMAL(10,2),
    ac_power DECIMAL(10,2),
    soc DECIMAL(5,2),
    bat_power DECIMAL(10,2),
    temperature DECIMAL(5,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS solax_data2 (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    yield_today DECIMAL(10,2),
    yield_total DECIMAL(10,2),
    ac_power DECIMAL(10,2),
    soc DECIMAL(5,2),
    bat_power DECIMAL(10,2),
    temperature DECIMAL(5,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS weather_data (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    temperature_2m DECIMAL(5,2),
    relative_humidity_2m DECIMAL(5,2),
    cloud_cover DECIMAL(5,2),
    global_horizontal_irradiance DECIMAL(8,2),
    is_forecast BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS predictions (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    system_id VARCHAR(20),
    predicted_power DECIMAL(10,2),
    confidence DECIMAL(5,4),
    model_version VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_solax_data_timestamp ON solax_data(timestamp);
CREATE INDEX IF NOT EXISTS idx_solax_data2_timestamp ON solax_data2(timestamp);
CREATE INDEX IF NOT EXISTS idx_weather_data_timestamp ON weather_data(timestamp);
CREATE INDEX IF NOT EXISTS idx_predictions_timestamp ON predictions(timestamp);

-- Insert basic configuration
INSERT INTO solax_data (yield_today, ac_power, soc) VALUES (0, 0, 50) ON CONFLICT DO NOTHING;
INSERT INTO weather_data (temperature_2m, cloud_cover) VALUES (20, 30) ON CONFLICT DO NOTHING;

COMMIT;
EOF

    echo -e "${GREEN}✅ Fresh start package created${NC}"
    echo "📦 Package: $PACKAGE_DIR"
    echo "💡 This will start collecting data immediately after deployment"
}

echo
echo -e "${GREEN}🎉 Database migration option completed!${NC}"
