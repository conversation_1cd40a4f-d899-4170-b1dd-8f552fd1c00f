#!/usr/bin/env python3
"""
Simple Telegram Bot for Solar Prediction System
Works with monolithic application
"""

import asyncio
import logging
import requests
import psycopg2
from datetime import datetime
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, CallbackQueryHandler, ContextTypes

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration
BOT_TOKEN = "8060881816:AAFBhGADTAAcUkbom_FEFSkOHoT5N6t5png"
CHAT_ID = "1510889515"
API_BASE_URL = "http://localhost:8100"

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 5433,
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

class SimpleTelegramBot:
    def __init__(self):
        self.application = Application.builder().token(BOT_TOKEN).build()
        self.setup_handlers()
    
    def setup_handlers(self):
        """Setup command handlers"""
        self.application.add_handler(CommandHandler("start", self.start_command))
        self.application.add_handler(CommandHandler("data", self.data_command))
        self.application.add_handler(CommandHandler("weather", self.weather_command))
        self.application.add_handler(CommandHandler("health", self.health_command))
        self.application.add_handler(CommandHandler("predict", self.predict_command))
        self.application.add_handler(CallbackQueryHandler(self.button_callback))
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Start command handler"""
        
        keyboard = [
            [
                InlineKeyboardButton("📊 Solar Data", callback_data="data"),
                InlineKeyboardButton("🌤️ Weather", callback_data="weather")
            ],
            [
                InlineKeyboardButton("🔧 Health", callback_data="health"),
                InlineKeyboardButton("🤖 Predict", callback_data="predict")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        welcome_message = """🌞 Solar Prediction Bot

Welcome to your solar energy assistant!

Features:
• Real-time solar data
• Weather conditions  
• System health monitoring
• ML predictions

Use the buttons below or commands:
/data - Solar system data
/weather - Weather conditions
/health - System health
/predict - ML predictions

All data is REAL from production database!"""
        
        await update.message.reply_text(welcome_message, reply_markup=reply_markup)
    
    async def data_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Get solar data"""
        try:
            response = requests.get(f"{API_BASE_URL}/api/v1/data/solax/latest", timeout=10)
            if response.status_code == 200:
                data = response.json()
                
                message = f"""📊 Solar System Data

Yield Today: {data.get('yield_today', 0)} kWh
AC Power: {data.get('ac_power', 0)} W
Battery SOC: {data.get('soc', 0)}%
Battery Power: {data.get('bat_power', 0)} W
Temperature: {data.get('temperature', 0)}°C

Timestamp: {data.get('timestamp', 'Unknown')}
Source: Production Database"""
                
                keyboard = [[InlineKeyboardButton("🔄 Refresh", callback_data="data")]]
                reply_markup = InlineKeyboardMarkup(keyboard)
                
                await update.message.reply_text(message, reply_markup=reply_markup)
            else:
                await update.message.reply_text("❌ Failed to get solar data")
        except Exception as e:
            await update.message.reply_text(f"❌ Error: {e}")
    
    async def weather_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Get weather data"""
        try:
            response = requests.get(f"{API_BASE_URL}/api/v1/data/weather/latest", timeout=10)
            if response.status_code == 200:
                data = response.json()
                
                message = f"""🌤️ Weather Data

Temperature: {data.get('temperature_2m', 'N/A')}°C
Cloud Cover: {data.get('cloud_cover', 'N/A')}%
Humidity: {data.get('humidity', 'N/A')}%

Location: Marathon, Attica, Greece
Timestamp: {data.get('timestamp', 'Unknown')}
Source: Production APIs"""
                
                keyboard = [[InlineKeyboardButton("🔄 Refresh", callback_data="weather")]]
                reply_markup = InlineKeyboardMarkup(keyboard)
                
                await update.message.reply_text(message, reply_markup=reply_markup)
            else:
                await update.message.reply_text("❌ Failed to get weather data")
        except Exception as e:
            await update.message.reply_text(f"❌ Error: {e}")
    
    async def health_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Get system health"""
        try:
            response = requests.get(f"{API_BASE_URL}/health", timeout=10)
            if response.status_code == 200:
                data = response.json()
                
                status_emoji = "🟢" if data.get('status') == 'healthy' else "🔴"
                
                message = f"""{status_emoji} System Health

Status: {data.get('status', 'Unknown')}
Database: {data.get('database', 'Unknown')}
Model: {data.get('model', 'Unknown')}
SolaX API: {data.get('solax_api', 'Unknown')}
Weather API: {data.get('weather_api', 'Unknown')}

Timestamp: {data.get('timestamp', 'Unknown')}
Version: {data.get('version', 'Unknown')}"""
                
                keyboard = [[InlineKeyboardButton("🔄 Refresh", callback_data="health")]]
                reply_markup = InlineKeyboardMarkup(keyboard)
                
                await update.message.reply_text(message, reply_markup=reply_markup)
            else:
                await update.message.reply_text("❌ Failed to get health status")
        except Exception as e:
            await update.message.reply_text(f"❌ Error: {e}")
    
    async def predict_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Make ML prediction"""
        try:
            prediction_data = {"system": "system1", "hours": 24}
            response = requests.post(f"{API_BASE_URL}/api/v1/predict", json=prediction_data, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                message = f"""🤖 ML Prediction

Predicted Energy: {data.get('prediction_kwh', 0)} kWh
Confidence: {data.get('confidence', 0):.1%}
Model: {data.get('model_used', 'Unknown')}
Accuracy: {data.get('model_accuracy', 0):.1f}%

System: {data.get('system', 'Unknown')}
Hours: {data.get('hours', 0)}
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

Based on production ML model!"""
                
                keyboard = [[InlineKeyboardButton("🔄 New Prediction", callback_data="predict")]]
                reply_markup = InlineKeyboardMarkup(keyboard)
                
                await update.message.reply_text(message, reply_markup=reply_markup)
            else:
                await update.message.reply_text("❌ Failed to make prediction")
        except Exception as e:
            await update.message.reply_text(f"❌ Error: {e}")
    
    async def button_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle button callbacks"""
        query = update.callback_query
        await query.answer()
        
        # Create a fake update for the command handlers
        fake_update = Update(
            update_id=update.update_id,
            message=query.message
        )
        
        if query.data == "data":
            await self.data_command(fake_update, context)
        elif query.data == "weather":
            await self.weather_command(fake_update, context)
        elif query.data == "health":
            await self.health_command(fake_update, context)
        elif query.data == "predict":
            await self.predict_command(fake_update, context)
    
    def run(self):
        """Run the bot"""
        logger.info("Starting Simple Telegram bot...")
        self.application.run_polling()

def main():
    """Main function"""
    print("🤖 SIMPLE TELEGRAM BOT")
    print("=" * 40)
    print("🔄 Connecting to monolithic app...")
    
    try:
        # Test API connection
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ API connection successful")
        else:
            print("❌ API connection failed")
            return False
        
        # Test database connection
        conn = psycopg2.connect(**DB_CONFIG)
        if conn:
            print("✅ Database connection successful")
            conn.close()
        else:
            print("❌ Database connection failed")
            return False
        
        print(f"📱 Bot Token: {BOT_TOKEN[:20]}...")
        print(f"💬 Chat ID: {CHAT_ID}")
        print(f"🌐 API URL: {API_BASE_URL}")
        print("🚀 Starting bot...")
        
        bot = SimpleTelegramBot()
        bot.run()
        
    except Exception as e:
        print(f"❌ Bot failed: {e}")
        return False

if __name__ == "__main__":
    main()
