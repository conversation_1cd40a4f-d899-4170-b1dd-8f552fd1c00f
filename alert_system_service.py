#!/usr/bin/env python3
"""
Alert System Service for Docker Container
Wrapper for the Alert System
"""

import os
import sys
import asyncio
import logging
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """Main entry point for Alert System service"""
    try:
        port = int(os.getenv('SERVICE_PORT', 8107))
        logger.info(f"🚨 Starting Alert System Service on port {port}...")

        # Import and run the Alert System
        from scripts.frontend_system.alert_system import main as alert_main

        # Run the alert system
        alert_main()

    except Exception as e:
        logger.error(f"❌ Failed to start Alert System: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
