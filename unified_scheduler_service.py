#!/usr/bin/env python3
"""
Unified Scheduler Service for Docker Container
Runs both Prediction Scheduler and Enhanced Scheduler Service in parallel
"""

import os
import sys
import asyncio
import logging
import threading
from pathlib import Path
from fastapi import FastAPI, HTTPException
from fastapi.responses import JSONResponse
import uvicorn

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Global variables for services
prediction_scheduler = None
enhanced_scheduler = None
ml_training_scheduler = None
app = FastAPI(title="Unified Scheduler Service", version="1.0.0")

def start_prediction_scheduler():
    """Start the Prediction Scheduler in a separate thread"""
    global prediction_scheduler
    try:
        logger.info("📅 Starting Prediction Scheduler...")
        from scripts.production.prediction_scheduler import PredictionScheduler
        
        prediction_scheduler = PredictionScheduler()
        prediction_scheduler.start()
        
        logger.info("✅ Prediction Scheduler started successfully")
        
    except Exception as e:
        logger.error(f"❌ Failed to start Prediction Scheduler: {e}")

def start_enhanced_scheduler():
    """Start the Enhanced Scheduler Service in async context"""
    global enhanced_scheduler
    try:
        logger.info("🕐 Starting Enhanced Scheduler Service...")
        from src.services.scheduler_service import EnhancedSchedulerService

        enhanced_scheduler = EnhancedSchedulerService()

        # Initialize default tasks if needed
        enhanced_scheduler.initialize_default_tasks()
        enhanced_scheduler.fix_old_next_run_times()

        # Start the scheduler loop
        asyncio.create_task(enhanced_scheduler.start_scheduler())

        logger.info("✅ Enhanced Scheduler Service started successfully")

    except Exception as e:
        logger.error(f"❌ Failed to start Enhanced Scheduler Service: {e}")

def start_ml_training_scheduler():
    """Start the ML Training Scheduler"""
    global ml_training_scheduler
    try:
        logger.info("🤖 Starting ML Training Scheduler...")
        from scripts.scheduling.ml_training_scheduler import MLTrainingScheduler

        ml_training_scheduler = MLTrainingScheduler()
        ml_training_scheduler.start()

        logger.info("✅ ML Training Scheduler started successfully")

    except Exception as e:
        logger.error(f"❌ Failed to start ML Training Scheduler: {e}")

@app.on_event("startup")
async def startup_event():
    """Initialize both schedulers on startup"""
    logger.info("🚀 Starting Unified Scheduler Service...")
    
    # Start Prediction Scheduler in a separate thread
    prediction_thread = threading.Thread(target=start_prediction_scheduler, daemon=True)
    prediction_thread.start()
    
    # Start Enhanced Scheduler Service in async context
    await asyncio.sleep(2)  # Give prediction scheduler time to start
    start_enhanced_scheduler()

    # Start ML Training Scheduler
    await asyncio.sleep(1)
    start_ml_training_scheduler()

    logger.info("✅ Unified Scheduler Service startup completed")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "unified-scheduler",
        "prediction_scheduler_running": prediction_scheduler is not None,
        "enhanced_scheduler_running": enhanced_scheduler is not None and enhanced_scheduler.running,
        "ml_training_scheduler_running": ml_training_scheduler is not None and ml_training_scheduler.running
    }

@app.get("/schedule/status")
async def get_schedule_status():
    """Get status of both schedulers"""
    status = {
        "prediction_scheduler": {
            "running": prediction_scheduler is not None,
            "next_run": None,
            "last_run": None
        },
        "enhanced_scheduler": {
            "running": enhanced_scheduler is not None and enhanced_scheduler.running,
            "active_tasks": 0
        }
    }
    
    # Get prediction scheduler status
    if prediction_scheduler:
        try:
            status["prediction_scheduler"]["next_run"] = prediction_scheduler.next_run.isoformat() if hasattr(prediction_scheduler, 'next_run') and prediction_scheduler.next_run else None
            status["prediction_scheduler"]["last_run"] = prediction_scheduler.last_run.isoformat() if hasattr(prediction_scheduler, 'last_run') and prediction_scheduler.last_run else None
        except:
            pass
    
    # Get enhanced scheduler status
    if enhanced_scheduler:
        try:
            from src.database.database import get_db_session
            from src.database.models import ScheduleTask
            
            with get_db_session() as db:
                active_tasks = db.query(ScheduleTask).filter(ScheduleTask.enabled == True).count()
                status["enhanced_scheduler"]["active_tasks"] = active_tasks
        except:
            pass
    
    return status

@app.post("/schedule/execute/{task_name}")
async def execute_task(task_name: str):
    """Execute a specific task from Enhanced Scheduler"""
    if not enhanced_scheduler:
        raise HTTPException(status_code=503, detail="Enhanced Scheduler not running")
    
    try:
        result = await enhanced_scheduler.execute_task_now(task_name)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Task execution failed: {e}")

@app.get("/schedule/tasks")
async def list_tasks():
    """List all scheduled tasks from Enhanced Scheduler"""
    if not enhanced_scheduler:
        raise HTTPException(status_code=503, detail="Enhanced Scheduler not running")
    
    try:
        from src.database.database import get_db_session
        from src.database.models import ScheduleTask
        
        with get_db_session() as db:
            tasks = db.query(ScheduleTask).all()
            
            task_list = []
            for task in tasks:
                task_list.append({
                    "task_name": task.task_name,
                    "task_type": task.task_type,
                    "enabled": task.enabled,
                    "interval_seconds": task.interval_seconds,
                    "next_run": task.next_run.isoformat() if task.next_run else None,
                    "status": task.status,
                    "description": task.description
                })
            
            return {"tasks": task_list, "total": len(task_list)}
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list tasks: {e}")

@app.get("/ml-training/status")
async def get_ml_training_status():
    """Get ML training scheduler status"""
    if not ml_training_scheduler:
        raise HTTPException(status_code=503, detail="ML Training Scheduler not running")

    return ml_training_scheduler.get_status()

@app.post("/ml-training/force")
async def force_ml_training():
    """Force immediate ML model training"""
    if not ml_training_scheduler:
        raise HTTPException(status_code=503, detail="ML Training Scheduler not running")

    try:
        ml_training_scheduler.force_training()
        return {"message": "ML training started", "status": "initiated"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to start ML training: {e}")

def main():
    """Main entry point for Unified Scheduler service"""
    try:
        port = int(os.getenv('SERVICE_PORT', 8106))
        logger.info(f"🚀 Starting Unified Scheduler Service on port {port}...")
        
        # Run the FastAPI app
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=port,
            log_level="info"
        )
        
    except Exception as e:
        logger.error(f"❌ Failed to start Unified Scheduler Service: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
