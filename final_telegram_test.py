#!/usr/bin/env python3
"""
Final Telegram Bot Test
"""

import requests

BOT_TOKEN = "**********:AAFBhGADTAAcUkbom_FEFSkOHoT5N6t5png"
CHAT_ID = "**********"

def send_final_test():
    """Send final test message"""
    url = f"https://api.telegram.org/bot{BOT_TOKEN}/sendMessage"
    
    payload = {
        "chat_id": CHAT_ID,
        "text": """🎉 **TELEGRAM BOT ΔΙΟΡΘΩΘΗΚΕ!**

✅ Όλα τα προβλήματα επιλύθηκαν:
• Health Monitor
• Daily Cost  
• ROI & Payback
• Predictions
• Tariffs

🤖 Δοκιμάστε οποιαδήποτε επιλογή από το μενού!""",
        "parse_mode": "Markdown"
    }
    
    try:
        response = requests.post(url, json=payload, timeout=10)
        if response.status_code == 200:
            print("✅ Final test message sent successfully!")
            print("📱 Check your Telegram and try any menu option")
            return True
        else:
            print(f"❌ Failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    send_final_test()
