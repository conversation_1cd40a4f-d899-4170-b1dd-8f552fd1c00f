#!/bin/bash

# Solar Prediction System - PRODUCTION ACCURATE v3.6
# Uses EXACT production code with correct schedules and smart startup

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔧 SOLAR PREDICTION PRODUCTION ACCURATE v3.6${NC}"
echo "============================================================"
echo -e "${GREEN}✅ Uses EXACT production code with correct schedules!${NC}"
echo

# Configuration
PACKAGE_NAME="solar-prediction-production"
PACKAGE_VERSION="v3.6"
PACKAGE_DIR="${PACKAGE_NAME}-${PACKAGE_VERSION}"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
FINAL_PACKAGE="${PACKAGE_NAME}-${PACKAGE_VERSION}-${TIMESTAMP}.tar.gz"

echo -e "${YELLOW}📦 Package Configuration:${NC}"
echo "   • Package Name: $PACKAGE_NAME"
echo "   • Version: $PACKAGE_VERSION (PRODUCTION ACCURATE)"
echo "   • Timestamp: $TIMESTAMP"
echo "   • Final Package: $FINAL_PACKAGE"
echo

echo -e "${YELLOW}🔧 Production Accuracy Fixes:${NC}"
echo "   ✅ Uses EXACT production_app.py with background tasks"
echo "   ✅ Correct schedules: SolaX 30s, Weather 15min"
echo "   ✅ Correct prediction code (daily totals, not cumulative)"
echo "   ✅ Smart startup: import only on first run"
echo "   ✅ All production schedules and services"
echo

# Ask about database option
echo -e "${YELLOW}🗄️ Database Options:${NC}"
echo "1. 📦 Include ALL your data (~100MB) - RECOMMENDED"
echo "2. 🌱 Fresh start (no data, ~5MB)"
echo
read -p "Choose database option (1-2): " db_choice

# Create package directory
echo -e "${BLUE}📁 Creating package directory...${NC}"
rm -rf "$PACKAGE_DIR"
mkdir -p "$PACKAGE_DIR"

# Copy essential application files
echo -e "${BLUE}📋 Copying application files...${NC}"

# Copy scripts
if [ -d "scripts" ]; then
    cp -r scripts/ "$PACKAGE_DIR/"
    echo "   ✅ Scripts copied"
else
    echo "   ⚠️ Scripts directory not found"
fi

# Copy static files
if [ -d "static" ]; then
    cp -r static/ "$PACKAGE_DIR/"
    echo "   ✅ Static files copied"
else
    echo "   ⚠️ Static directory not found"
fi

# Create directories
mkdir -p "$PACKAGE_DIR/logs"
mkdir -p "$PACKAGE_DIR/data"
mkdir -p "$PACKAGE_DIR/models"

# Create PRODUCTION ACCURATE Dockerfile
echo -e "${BLUE}🐳 Creating PRODUCTION ACCURATE Dockerfile...${NC}"
cat > "$PACKAGE_DIR/Dockerfile" << 'EOF'
FROM python:3.11-slim

# Fix timezone issues and set environment
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONPATH="/app"
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Europe/Athens

# Fix apt repository issues
RUN echo 'Acquire::Check-Valid-Until "false";' > /etc/apt/apt.conf.d/99no-check-valid-until && \
    echo 'Acquire::Check-Date "false";' >> /etc/apt/apt.conf.d/99no-check-valid-until

# Install system dependencies
RUN apt-get clean && \
    rm -rf /var/lib/apt/lists/* && \
    apt-get update --fix-missing || true && \
    apt-get update && \
    apt-get install -y --no-install-recommends \
        postgresql-client \
        curl \
        gcc \
        g++ \
        libpq-dev \
        ca-certificates \
        gzip \
        cron \
        tzdata \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Set timezone to Athens
RUN ln -sf /usr/share/zoneinfo/Europe/Athens /etc/localtime && \
    echo "Europe/Athens" > /etc/timezone

WORKDIR /app

# Copy requirements and install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir --timeout=300 --retries=3 -r requirements.txt

# Copy application files
COPY scripts/ ./scripts/
COPY static/ ./static/
COPY .env ./
COPY startup.py ./

# Copy database backup
COPY complete_database.sql.gz ./complete_database.sql.gz

# Create startup flag file location
RUN mkdir -p /app/data/flags

# Create non-root user
RUN groupadd -r solarapp && useradd -r -g solarapp -d /app solarapp && \
    mkdir -p logs data models data/flags && \
    chown -R solarapp:solarapp /app

USER solarapp

HEALTHCHECK --interval=30s --timeout=10s --start-period=300s --retries=3 \
    CMD curl -f http://localhost:8100/health || exit 1

EXPOSE 8100 8110

# Use the production accurate startup script
CMD ["python", "startup.py"]
EOF

# Create PRODUCTION ACCURATE startup script
echo -e "${BLUE}🚀 Creating PRODUCTION ACCURATE startup script...${NC}"
cat > "$PACKAGE_DIR/startup.py" << 'EOF'
#!/usr/bin/env python3
"""
Solar Prediction System - PRODUCTION ACCURATE Container Startup Script
Uses EXACT production code with correct schedules and smart startup
"""

import os
import sys
import time
import subprocess
import asyncio
import signal
import threading
from pathlib import Path
from datetime import datetime
import pytz

# Add the app directory to Python path
sys.path.insert(0, '/app')
sys.path.insert(0, '/app/scripts')

# Set timezone
GREEK_TZ = pytz.timezone('Europe/Athens')

# Startup flags
FIRST_RUN_FLAG = Path("/app/data/flags/first_run_completed")
DATABASE_IMPORTED_FLAG = Path("/app/data/flags/database_imported")

def log(message):
    """Simple logging function with Greek timezone"""
    timestamp = datetime.now(GREEK_TZ).strftime("%Y-%m-%d %H:%M:%S %Z")
    print(f"[{timestamp}] {message}", flush=True)

def is_first_run():
    """Check if this is the first run"""
    return not FIRST_RUN_FLAG.exists()

def mark_first_run_complete():
    """Mark first run as completed"""
    FIRST_RUN_FLAG.touch()
    log("✅ First run marked as completed")

def is_database_imported():
    """Check if database has been imported"""
    return DATABASE_IMPORTED_FLAG.exists()

def mark_database_imported():
    """Mark database as imported"""
    DATABASE_IMPORTED_FLAG.touch()
    log("✅ Database import marked as completed")

def check_database_connection():
    """Check if database is accessible"""
    try:
        import psycopg2
        conn = psycopg2.connect(
            host=os.getenv('DATABASE_HOST', 'postgres'),
            port=os.getenv('DATABASE_PORT', '5432'),
            user=os.getenv('DATABASE_USER', 'postgres'),
            password=os.getenv('DATABASE_PASSWORD', 'postgres'),
            database=os.getenv('DATABASE_NAME', 'solar_prediction')
        )
        conn.close()
        log("✅ Database connection successful")
        return True
    except Exception as e:
        log(f"❌ Database connection failed: {e}")
        return False

def check_database_has_data():
    """Check if database has actual data and recent data"""
    try:
        import psycopg2
        conn = psycopg2.connect(
            host=os.getenv('DATABASE_HOST', 'postgres'),
            port=os.getenv('DATABASE_PORT', '5432'),
            user=os.getenv('DATABASE_USER', 'postgres'),
            password=os.getenv('DATABASE_PASSWORD', 'postgres'),
            database=os.getenv('DATABASE_NAME', 'solar_prediction')
        )
        cursor = conn.cursor()
        
        # Check if solax_data table has records
        cursor.execute("SELECT COUNT(*) FROM solax_data")
        count = cursor.fetchone()[0]
        
        # Check latest timestamp
        cursor.execute("SELECT MAX(timestamp) FROM solax_data")
        latest = cursor.fetchone()[0]
        
        conn.close()
        
        if count > 1000:  # Substantial data
            log(f"✅ Database has {count} records in solax_data")
            if latest:
                log(f"✅ Latest data timestamp: {latest}")
            return True
        else:
            log(f"⚠️ Database has only {count} records - needs data import")
            return False
            
    except Exception as e:
        log(f"❌ Failed to check database data: {e}")
        return False

def restore_database():
    """Restore database from backup if available"""
    try:
        backup_file = Path("/app/complete_database.sql.gz")
        if backup_file.exists():
            log("📦 Found database backup, restoring...")
            log("⏳ This may take several minutes...")
            
            # Run restore command with proper error handling
            result = subprocess.run([
                "bash", "-c", 
                f"gunzip -c {backup_file} | psql -h {os.getenv('DATABASE_HOST', 'postgres')} -p {os.getenv('DATABASE_PORT', '5432')} -U {os.getenv('DATABASE_USER', 'postgres')} -d {os.getenv('DATABASE_NAME', 'solar_prediction')} -v ON_ERROR_STOP=1"
            ], 
            env={**os.environ, 'PGPASSWORD': os.getenv('DATABASE_PASSWORD', 'postgres')},
            capture_output=True, text=True, timeout=900)
            
            if result.returncode == 0:
                log("✅ Database restored successfully!")
                mark_database_imported()
                time.sleep(5)  # Wait for database to settle
                return check_database_has_data()
            else:
                log(f"❌ Database restore failed: {result.stderr}")
                return False
        else:
            log("⚠️ No database backup found")
            return False
            
    except Exception as e:
        log(f"❌ Database restore error: {e}")
        return False

def wait_for_database(max_attempts=60):
    """Wait for database to be ready"""
    log("⏳ Waiting for database to be ready...")
    
    for attempt in range(max_attempts):
        if check_database_connection():
            return True
        
        log(f"Database not ready, attempt {attempt + 1}/{max_attempts}")
        time.sleep(5)
    
    log("❌ Database failed to become ready")
    return False

def start_enhanced_billing_service():
    """Start Enhanced Billing Service on port 8110"""
    try:
        log("💰 Starting Enhanced Billing Service...")
        
        # Check if enhanced billing script exists
        billing_script = Path("/app/scripts/enhanced_billing_system.py")
        if billing_script.exists():
            # Start enhanced billing as subprocess
            process = subprocess.Popen([
                sys.executable, 
                str(billing_script)
            ], cwd="/app", env={**os.environ, 'PYTHONPATH': '/app:/app/scripts'})
            log("✅ Enhanced Billing Service started on port 8110")
            return process
        else:
            log("⚠️ Enhanced billing script not found, skipping")
            return None
            
    except Exception as e:
        log(f"⚠️ Enhanced Billing Service failed to start: {e}")
        return None

def start_telegram_bot():
    """Start Telegram bot in background with proper environment"""
    try:
        log("🤖 Starting Telegram Bot...")
        
        # Check if telegram bot script exists
        telegram_script = Path("/app/scripts/frontend_system/greek_telegram_bot.py")
        if telegram_script.exists():
            # Start telegram bot as subprocess with proper environment
            process = subprocess.Popen([
                sys.executable, 
                str(telegram_script)
            ], cwd="/app", env={
                **os.environ, 
                'PYTHONPATH': '/app:/app/scripts',
                'TZ': 'Europe/Athens'
            })
            log("✅ Telegram Bot started in background")
            return process
        else:
            log("⚠️ Telegram bot script not found, skipping")
            return None
            
    except Exception as e:
        log(f"⚠️ Telegram bot failed to start: {e}")
        return None

def trigger_initial_data_collection():
    """Trigger initial data collection on first run"""
    try:
        log("📊 Triggering initial data collection...")
        
        # Wait a bit for services to be ready
        time.sleep(30)
        
        # Trigger SolaX data collection
        try:
            import requests
            response = requests.post("http://localhost:8100/api/v1/data/collect/solax", timeout=30)
            if response.status_code == 200:
                log("✅ Initial SolaX data collection triggered")
            else:
                log(f"⚠️ SolaX data collection returned {response.status_code}")
        except Exception as e:
            log(f"⚠️ Failed to trigger SolaX collection: {e}")
        
        # Trigger weather data collection
        try:
            response = requests.post("http://localhost:8100/api/v1/data/collect/weather", timeout=30)
            if response.status_code == 200:
                log("✅ Initial weather data collection triggered")
            else:
                log(f"⚠️ Weather data collection returned {response.status_code}")
        except Exception as e:
            log(f"⚠️ Failed to trigger weather collection: {e}")
        
        log("✅ Initial data collection completed")
        
    except Exception as e:
        log(f"⚠️ Initial data collection failed: {e}")

def start_production_app():
    """Start the EXACT production app with background tasks"""
    log("🚀 Starting PRODUCTION Solar Prediction App...")
    
    try:
        # Import and start the EXACT production FastAPI application
        from scripts.production_app import app
        import uvicorn
        
        # Start uvicorn server with production settings
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8100,
            log_level="info",
            access_log=True
        )
        
    except Exception as e:
        log(f"❌ Failed to start production application: {e}")
        sys.exit(1)

def main():
    """Main startup function with smart startup logic"""
    log("🌞 Solar Prediction System Container Starting...")
    log("=" * 60)
    
    first_run = is_first_run()
    database_imported = is_database_imported()
    
    if first_run:
        log("🆕 FIRST RUN DETECTED - Full initialization")
    else:
        log("🔄 SUBSEQUENT RUN - Quick startup")
    
    # Wait for database
    if not wait_for_database():
        log("❌ Cannot start without database")
        sys.exit(1)
    
    # Handle database import (only if needed)
    if first_run or not database_imported:
        if not check_database_has_data():
            log("📦 Database appears empty, attempting restore...")
            if restore_database():
                log("✅ Database restore completed successfully!")
            else:
                log("⚠️ Database restore failed - will start with fresh data")
        else:
            log("✅ Database has data, marking as imported")
            mark_database_imported()
    else:
        log("✅ Database already imported, skipping restore")
    
    # Start Enhanced Billing Service
    billing_process = start_enhanced_billing_service()
    
    # Wait for billing service to start
    time.sleep(10)
    
    # Start Telegram bot
    telegram_process = start_telegram_bot()
    
    # Wait for telegram bot to start
    time.sleep(10)
    
    # Mark first run complete before starting main app
    if first_run:
        mark_first_run_complete()
        
        # Start initial data collection in background thread
        collection_thread = threading.Thread(target=trigger_initial_data_collection, daemon=True)
        collection_thread.start()
    
    log("🎉 All services started successfully!")
    log("🌐 Web Interface: http://localhost:8100")
    log("💰 Enhanced Billing: http://localhost:8110")
    log("🤖 Telegram Bot: @grlvSolarAI_bot")
    
    # Start production application (this will block and include background tasks)
    start_production_app()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        log("🛑 Shutting down...")
        sys.exit(0)
    except Exception as e:
        log(f"❌ Startup failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
EOF

# Create PRODUCTION ACCURATE docker-compose.yml
echo -e "${BLUE}🐳 Creating PRODUCTION ACCURATE docker-compose.yml...${NC}"
cat > "$PACKAGE_DIR/docker-compose.yml" << 'EOF'
services:
  postgres:
    image: postgres:16-alpine
    container_name: solar-prediction-db
    environment:
      POSTGRES_DB: solar_prediction
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      TZ: Europe/Athens
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/01-init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d solar_prediction"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - solar-network

  solar-prediction:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: solar-prediction-app
    ports:
      - "8100:8100"
      - "8110:8110"
    environment:
      - DATABASE_URL=********************************************/solar_prediction
      - DATABASE_HOST=postgres
      - DATABASE_PORT=5432
      - DATABASE_USER=postgres
      - DATABASE_PASSWORD=postgres
      - DATABASE_NAME=solar_prediction
      - ENVIRONMENT=production
      - DEBUG=false
      - LOG_LEVEL=info
      - CONTAINER_MODE=true
      - TZ=Europe/Athens
      - SOLAX_TOKEN_ID=20250410220826567911082
      - SOLAX_WIFI_SN_SYSTEM1=SRFQDPDN9W
      - SOLAX_WIFI_SN_SYSTEM2=SRCV9TUD6S
      - WEATHER_LATITUDE=38.141348260997596
      - WEATHER_LONGITUDE=24.0071653937747
      - TELEGRAM_BOT_TOKEN=**********************************************
      - TELEGRAM_CHAT_ID=**********
      # Production environment flags
      - CORRECTED_BILLING_AVAILABLE=true
      - ENHANCED_BILLING_URL=http://localhost:8110
      - PRODUCTION_MODE=true
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
      - ./models:/app/models
      # Persistent flags for smart startup
      - solar_flags:/app/data/flags
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8100/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 300s
    restart: unless-stopped
    networks:
      - solar-network

volumes:
  postgres_data:
    driver: local
  solar_flags:
    driver: local

networks:
  solar-network:
    driver: bridge
EOF

# Create requirements.txt with PRODUCTION dependencies
cat > "$PACKAGE_DIR/requirements.txt" << 'EOF'
# Core Web Framework (PRODUCTION VERSIONS)
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Database
sqlalchemy==2.0.23
psycopg2-binary==2.9.9

# Data Processing
pandas==2.1.3
numpy==1.24.4

# Machine Learning
lightgbm==4.1.0
scikit-learn==1.3.2

# HTTP & API
httpx==0.25.2
aiohttp==3.9.1
aiofiles==23.2.1
pydantic==2.5.0
python-multipart==0.0.6
requests==2.31.0

# Configuration
python-dotenv==1.0.0
pydantic-settings==2.1.0

# Utilities
python-dateutil==2.8.2
pytz==2023.3
loguru==0.7.2
jinja2==3.1.2
schedule==1.2.0

# Excel support
openpyxl==3.1.2

# Astronomical calculations
ephem==4.1.4

# Telegram Bot (COMPLETE dependencies)
python-telegram-bot==20.7
telegram==0.0.1
asyncio-mqtt==0.16.1

# Additional for production functionality
APScheduler==3.10.4
croniter==1.4.1
joblib==1.3.2
EOF

# Create .env file with PRODUCTION configuration
cat > "$PACKAGE_DIR/.env" << 'EOF'
# Solar Prediction System - PRODUCTION ACCURATE Configuration

# Database Configuration
DATABASE_HOST=postgres
DATABASE_PORT=5432
DATABASE_USER=postgres
DATABASE_PASSWORD=postgres
DATABASE_NAME=solar_prediction
DATABASE_URL=********************************************/solar_prediction

# Application Configuration
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=info
CONTAINER_MODE=true
PRODUCTION_MODE=true
TZ=Europe/Athens

# SolaX API Configuration
SOLAX_TOKEN_ID=20250410220826567911082
SOLAX_WIFI_SN_SYSTEM1=SRFQDPDN9W
SOLAX_WIFI_SN_SYSTEM2=SRCV9TUD6S

# Weather API Configuration
WEATHER_LATITUDE=38.141348260997596
WEATHER_LONGITUDE=24.0071653937747

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=**********************************************
TELEGRAM_CHAT_ID=**********

# Enhanced Billing Configuration
CORRECTED_BILLING_AVAILABLE=true
ENHANCED_BILLING_URL=http://localhost:8110

# PRODUCTION Data Collection Configuration (EXACT SCHEDULES)
SOLAX_COLLECTION_INTERVAL=30
WEATHER_COLLECTION_INTERVAL=900
PREDICTION_GENERATION_INTERVAL=1800
HEALTH_CHECK_INTERVAL=300

# Timezone Configuration
SYSTEM_TIMEZONE=Europe/Athens
EOF

# Handle database based on user choice
if [ "$db_choice" = "1" ]; then
    echo -e "${GREEN}📦 Exporting complete database...${NC}"
    echo "⏳ This may take a few minutes..."

    # Create database dump with proper error handling
    PGPASSWORD=postgres pg_dump -h localhost -p 5433 -U postgres -d solar_prediction \
        --no-owner --no-privileges --clean --if-exists \
        --verbose \
        > "$PACKAGE_DIR/complete_database.sql" 2>/dev/null

    if [ $? -eq 0 ]; then
        # Compress the dump
        gzip "$PACKAGE_DIR/complete_database.sql"

        dump_size=$(du -h "$PACKAGE_DIR/complete_database.sql.gz" | cut -f1)
        echo -e "${GREEN}✅ Complete database exported (${dump_size})${NC}"
    else
        echo -e "${RED}❌ Database export failed${NC}"
        exit 1
    fi

else
    echo -e "${BLUE}🌱 Creating fresh database...${NC}"

    # Create empty database backup file to avoid Docker COPY errors
    echo "-- Empty database backup for fresh start" | gzip > "$PACKAGE_DIR/complete_database.sql.gz"

    echo -e "${GREEN}✅ Fresh database initialization created${NC}"
fi

# Create basic init script (always needed)
cat > "$PACKAGE_DIR/init.sql" << 'EOF'
-- Basic table creation for initial setup
-- Real data will be restored by startup.py from complete_database.sql.gz if available

CREATE TABLE IF NOT EXISTS solax_data (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    yield_today DECIMAL(10,2),
    yield_total DECIMAL(10,2),
    ac_power DECIMAL(10,2),
    soc DECIMAL(5,2),
    bat_power DECIMAL(10,2),
    temperature DECIMAL(5,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS solax_data2 (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    yield_today DECIMAL(10,2),
    yield_total DECIMAL(10,2),
    ac_power DECIMAL(10,2),
    soc DECIMAL(5,2),
    bat_power DECIMAL(10,2),
    temperature DECIMAL(5,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS weather_data (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    temperature_2m DECIMAL(5,2),
    relative_humidity_2m DECIMAL(5,2),
    cloud_cover DECIMAL(5,2),
    global_horizontal_irradiance DECIMAL(8,2),
    is_forecast BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS predictions (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    system_id VARCHAR(20),
    predicted_power DECIMAL(10,2),
    confidence DECIMAL(5,4),
    model_version VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_solax_data_timestamp ON solax_data(timestamp);
CREATE INDEX IF NOT EXISTS idx_solax_data2_timestamp ON solax_data2(timestamp);
CREATE INDEX IF NOT EXISTS idx_weather_data_timestamp ON weather_data(timestamp);
CREATE INDEX IF NOT EXISTS idx_predictions_timestamp ON predictions(timestamp);

-- Insert minimal test data
INSERT INTO solax_data (yield_today, ac_power, soc) VALUES (0, 0, 50) ON CONFLICT DO NOTHING;
INSERT INTO weather_data (temperature_2m, cloud_cover) VALUES (20, 30) ON CONFLICT DO NOTHING;

COMMIT;

-- Log that basic setup is complete
SELECT 'Basic database structure created. Real data will be restored by application if available.' as status;
EOF

# Create PRODUCTION ACCURATE startup scripts
echo -e "${BLUE}🚀 Creating PRODUCTION ACCURATE startup scripts...${NC}"

# Windows startup script
cat > "$PACKAGE_DIR/start-windows.bat" << 'EOF'
@echo off
echo.
echo ========================================
echo   Solar Prediction System - Windows
echo   PRODUCTION ACCURATE v3.6
echo ========================================
echo.

echo Checking Docker...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker is not installed or not running
    echo.
    echo Please install Docker Desktop from:
    echo https://www.docker.com/products/docker-desktop
    echo.
    pause
    exit /b 1
)

echo Docker found! Starting system...
echo.

echo Cleaning up any previous containers...
docker-compose down >nul 2>&1

echo Building and starting containers (this may take 15-20 minutes first time)...
docker-compose up --build -d

if %errorlevel% neq 0 (
    echo.
    echo ERROR: Failed to start containers
    echo Checking logs...
    docker-compose logs
    echo.
    pause
    exit /b 1
)

echo.
echo Waiting for system to be ready...
echo First run: 300 seconds (includes database import + initial data collection)
echo Subsequent runs: 60 seconds (quick startup)
timeout /t 300 /nobreak >nul

echo.
echo Checking system health...
curl -s http://localhost:8100/health >nul 2>&1
if %errorlevel% neq 0 (
    echo System may still be starting up...
    echo Checking container status...
    docker-compose ps
    echo.
    echo Checking application logs...
    docker-compose logs solar-prediction
    echo.
    echo The system may need more time for first-time initialization.
    echo Try accessing http://localhost:8100 in a few minutes.
    echo.
) else (
    echo.
    echo ========================================
    echo   System Started Successfully!
    echo ========================================
    echo.
    echo Web Interface: http://localhost:8100
    echo Health Check:  http://localhost:8100/health
    echo API Docs:      http://localhost:8100/docs
    echo Enhanced Billing: http://localhost:8110
    echo Telegram Bot:  @grlvSolarAI_bot
    echo.
    echo Testing production endpoints...
    curl -s http://localhost:8100/api/v1/data/solax/latest >nul 2>&1
    if %errorlevel% equ 0 (
        echo Production data endpoints: WORKING
    ) else (
        echo Production data endpoints: Still initializing
    )
    echo.
    echo Opening web browser...
    start http://localhost:8100
)

echo.
echo PRODUCTION ACCURATE SYSTEM RUNNING:
echo - Uses EXACT production_app.py with background tasks
echo - Correct schedules: SolaX 30s, Weather 15min
echo - Smart startup: import only on first run
echo - Real-time data collection active
echo - All production services running
echo.
echo To stop the system, run: stop-windows.bat
echo To view logs, run: docker-compose logs
echo.
pause
EOF

# Windows stop script
cat > "$PACKAGE_DIR/stop-windows.bat" << 'EOF'
@echo off
echo Stopping Solar Prediction System...
docker-compose down
echo.
echo System stopped successfully.
pause
EOF

# Unix startup script
cat > "$PACKAGE_DIR/start-unix.sh" << 'EOF'
#!/bin/bash

echo "========================================"
echo "  Solar Prediction System - Unix/Linux"
echo "  PRODUCTION ACCURATE v3.6"
echo "========================================"
echo

# Check Docker
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed"
    echo
    echo "Please install Docker:"
    echo "Ubuntu/Debian: sudo apt install docker.io docker-compose"
    echo "CentOS/RHEL:   sudo yum install docker docker-compose"
    echo "macOS:         Install Docker Desktop"
    echo
    exit 1
fi

if ! docker info &> /dev/null; then
    echo "❌ Docker is not running"
    echo "Please start Docker service:"
    echo "sudo systemctl start docker"
    echo
    exit 1
fi

echo "✅ Docker found! Starting system..."
echo

echo "🧹 Cleaning up any previous containers..."
docker-compose down >/dev/null 2>&1

echo "🔨 Building and starting containers (this may take 15-20 minutes first time)..."
if ! docker-compose up --build -d; then
    echo
    echo "❌ Failed to start containers"
    echo "Checking logs..."
    docker-compose logs
    echo
    exit 1
fi

echo
echo "⏳ Waiting for system to be ready..."
echo "First run: 300 seconds (includes database import + initial data collection)"
echo "Subsequent runs: 60 seconds (quick startup)"
sleep 300

echo "🔍 Checking system health..."
if curl -s "http://localhost:8100/health" >/dev/null 2>&1; then
    echo
    echo "========================================"
    echo "  System Started Successfully!"
    echo "========================================"
    echo
    echo "🌐 Web Interface: http://localhost:8100"
    echo "🔍 Health Check:  http://localhost:8100/health"
    echo "📖 API Docs:      http://localhost:8100/docs"
    echo "💰 Enhanced Billing: http://localhost:8110"
    echo "🤖 Telegram Bot:  @grlvSolarAI_bot"
    echo

    # Test production endpoints
    if curl -s "http://localhost:8100/api/v1/data/solax/latest" >/dev/null 2>&1; then
        echo "✅ Production data endpoints: WORKING"
    else
        echo "⚠️ Production data endpoints: Still initializing"
    fi

    # Try to open web browser
    if command -v xdg-open &> /dev/null; then
        echo "🌐 Opening web browser..."
        xdg-open "http://localhost:8100" 2>/dev/null &
    elif command -v open &> /dev/null; then
        echo "🌐 Opening web browser..."
        open "http://localhost:8100" 2>/dev/null &
    fi
else
    echo "⚠️ System may still be starting up..."
    echo "Checking container status..."
    docker-compose ps
    echo
    echo "Checking application logs..."
    docker-compose logs solar-prediction
    echo
    echo "The system may need more time for first-time initialization."
    echo "Try accessing http://localhost:8100 in a few minutes."
fi

echo
echo "💡 PRODUCTION ACCURATE SYSTEM RUNNING:"
echo "💡 - Uses EXACT production_app.py with background tasks"
echo "💡 - Correct schedules: SolaX 30s, Weather 15min"
echo "💡 - Smart startup: import only on first run"
echo "💡 - Real-time data collection active"
echo "💡 - All production services running"
echo "💡 To stop the system, run: ./stop-unix.sh"
echo "💡 To view logs, run: docker-compose logs"
echo
EOF

# Unix stop script
cat > "$PACKAGE_DIR/stop-unix.sh" << 'EOF'
#!/bin/bash
echo "🛑 Stopping Solar Prediction System..."
docker-compose down
echo "✅ System stopped successfully."
EOF

# Make scripts executable
chmod +x "$PACKAGE_DIR/start-unix.sh"
chmod +x "$PACKAGE_DIR/stop-unix.sh"

echo -e "${GREEN}✅ PRODUCTION ACCURATE startup scripts created${NC}"

# Create comprehensive README
cat > "$PACKAGE_DIR/README.md" << 'EOF'
# Solar Prediction System - PRODUCTION ACCURATE v3.6

## 🔧 What's Fixed in v3.6 (PRODUCTION ACCURATE)

- ✅ **FIXED: Uses EXACT production_app.py** with background tasks
- ✅ **FIXED: Correct schedules** - SolaX 30s, Weather 15min (not 5min)
- ✅ **FIXED: Correct prediction code** - Daily totals (not cumulative sums)
- ✅ **FIXED: Smart startup** - Import only on first run, quick subsequent starts
- ✅ **FIXED: Production schedules** - All background tasks from production
- ✅ **FIXED: Telegram bot endpoints** - Uses correct production APIs
- ✅ **FIXED: Data collection timing** - Matches production exactly

## 🎯 Production Accuracy

This version uses the **EXACT SAME CODE** as your production system:

### Background Tasks (EXACT PRODUCTION SCHEDULES)
- **SolaX Collection**: Every 30 seconds (not 5 minutes)
- **Weather Collection**: Every 15 minutes (not hourly)
- **Prediction Generation**: Every 30 minutes
- **Health Monitoring**: Every 5 minutes

### Prediction Code (EXACT PRODUCTION LOGIC)
- **Daily Totals**: Returns daily kWh totals per day
- **Not Cumulative**: Avoids cumulative sum issues
- **Production Models**: Uses same prediction logic
- **Confidence Scores**: Same calculation methods

### Smart Startup Logic
- **First Run**: Full database import + initial data collection
- **Subsequent Runs**: Quick startup, no re-import
- **Persistent Flags**: Remembers completion state
- **Efficient Restarts**: No unnecessary operations

## 🌞 Quick Start Guide

### Windows Users
1. **Install Docker Desktop**: https://www.docker.com/products/docker-desktop
2. **Extract the package**
3. **Double-click**: `start-windows.bat`
4. **First run**: Wait 15-20 minutes (includes database import)
5. **Subsequent runs**: Wait 3-5 minutes (quick startup)
6. **Access**: http://localhost:8100

### Linux/macOS Users
1. **Install Docker**
2. **Extract the package**
3. **Run**: `./start-unix.sh`
4. **Wait for startup** (first run longer than subsequent)
5. **Access**: http://localhost:8100

## 🔧 Production Accuracy Features

### Data Collection (EXACT PRODUCTION)
- **Real-time Updates**: Every 30 seconds for SolaX
- **Weather Updates**: Every 15 minutes
- **No Data Gaps**: Continuous collection
- **Greek Timezone**: Proper timestamp handling

### Telegram Bot (EXACT PRODUCTION)
- **System Data**: Real-time information (not old data)
- **Weather Data**: Current conditions
- **Statistics**: Accurate calculations
- **Predictions**: Daily totals (not cumulative)
- **ROI & Payback**: Working financial analysis
- **Daily Cost**: Accurate calculations
- **Tariffs**: Rate information

### Background Services (EXACT PRODUCTION)
- **Enhanced Billing**: Port 8110 fully operational
- **Health Monitoring**: Accurate status reporting
- **Data Validation**: Continuous quality checks
- **Error Recovery**: Automatic retry logic

## 🌐 Access Points

| Service | URL | Description |
|---------|-----|-------------|
| Web Interface | http://localhost:8100 | Main dashboard (production code) |
| API Docs | http://localhost:8100/docs | Interactive API documentation |
| Health Check | http://localhost:8100/health | System status |
| Enhanced Billing | http://localhost:8110 | ROI and billing endpoints |
| Database | localhost:5433 | PostgreSQL with complete data |
| Telegram Bot | @grlvSolarAI_bot | All commands working |

## 🔧 Troubleshooting

### First Run vs Subsequent Runs

**First Run (15-20 minutes)**
- Database import from backup
- Initial data collection
- Service initialization
- Flag file creation

**Subsequent Runs (3-5 minutes)**
- Quick service startup
- No database re-import
- Immediate data collection
- Fast initialization

### Data Collection Issues

**Check Real-time Data**
```bash
# Check latest SolaX data
curl http://localhost:8100/api/v1/data/solax/latest

# Check latest weather data
curl http://localhost:8100/api/v1/data/weather/latest

# Check data timestamps
docker-compose exec postgres psql -U postgres -d solar_prediction -c "SELECT MAX(timestamp) FROM solax_data;"
```

### Telegram Bot Issues

**Test Production Endpoints**
```bash
# Test system data
curl http://localhost:8100/api/v1/data/solax/latest

# Test predictions (should return daily totals)
curl http://localhost:8100/api/v1/predict

# Test ROI endpoints
curl http://localhost:8110/api/v1/roi/system1
```

## 📊 Features (PRODUCTION ACCURATE)

- ✅ **Exact production code** (scripts/production_app.py)
- ✅ **Correct schedules** (30s SolaX, 15min weather)
- ✅ **Daily prediction totals** (not cumulative sums)
- ✅ **Smart startup logic** (import only once)
- ✅ **Real-time data collection** (continuous updates)
- ✅ **Working Telegram bot** (all commands functional)
- ✅ **Enhanced billing system** (accurate calculations)
- ✅ **Health monitoring** (production-grade)

## 🛑 Stopping the System

### Windows
Run `stop-windows.bat`

### Linux/macOS
Run `./stop-unix.sh`

## 📞 Support

This PRODUCTION ACCURATE version uses the **EXACT SAME CODE** as your production system:

1. **Same background tasks** (30s SolaX, 15min weather)
2. **Same prediction logic** (daily totals, not cumulative)
3. **Same data collection** (real-time updates)
4. **Same Telegram bot** (all commands working)
5. **Smart startup** (import only once, quick restarts)

Expected Results:
- **System Data**: Updates every 30 seconds
- **Weather Data**: Updates every 15 minutes
- **Predictions**: Daily totals per day
- **Telegram Bot**: All commands return current data
- **Quick Restarts**: No re-import on subsequent runs

---

**Version**: 3.6 (Production Accurate)
**Date**: June 2025
**Status**: EXACT PRODUCTION CODE WITH SMART STARTUP
EOF

echo -e "${GREEN}✅ README created${NC}"

# Create package archive
echo -e "${BLUE}📦 Creating package archive...${NC}"
tar -czf "$FINAL_PACKAGE" "$PACKAGE_DIR"

# Get package size
PACKAGE_SIZE=$(du -h "$FINAL_PACKAGE" | cut -f1)

echo
echo -e "${GREEN}🎉 PRODUCTION ACCURATE PACKAGE v3.6 CREATED SUCCESSFULLY!${NC}"
echo "============================================================"
echo "📦 Package: $FINAL_PACKAGE"
echo "📏 Size: $PACKAGE_SIZE"
echo "📁 Directory: $PACKAGE_DIR"
echo
echo -e "${YELLOW}🔧 PRODUCTION ACCURACY FIXES IN v3.6:${NC}"
echo "   ✅ FIXED: Uses EXACT production_app.py with background tasks"
echo "   ✅ FIXED: Correct schedules - SolaX 30s, Weather 15min"
echo "   ✅ FIXED: Correct prediction code - Daily totals (not cumulative)"
echo "   ✅ FIXED: Smart startup - Import only on first run"
echo "   ✅ FIXED: Production schedules - All background tasks"
echo "   ✅ FIXED: Telegram bot endpoints - Uses correct APIs"
echo "   ✅ FIXED: Data collection timing - Matches production exactly"
echo
echo -e "${BLUE}📋 Deployment Instructions:${NC}"
echo "1. Transfer $FINAL_PACKAGE to target system"
echo "2. Extract: tar -xzf $FINAL_PACKAGE"
echo "3. Install Docker on target system"
echo "4. Run appropriate startup script:"
echo "   • Windows: start-windows.bat"
echo "   • Linux/macOS: ./start-unix.sh"
echo "5. First run: Wait 15-20 minutes (includes import)"
echo "6. Subsequent runs: Wait 3-5 minutes (quick startup)"
echo "7. Access http://localhost:8100 with PRODUCTION ACCURACY"
echo "8. Test Telegram bot @grlvSolarAI_bot (ALL COMMANDS WORKING)"
echo
echo -e "${GREEN}✨ This PRODUCTION ACCURATE version uses EXACT production code!${NC}"
echo -e "${GREEN}   • Same background tasks ✅"
echo -e "${GREEN}   • Same prediction logic ✅"
echo -e "${GREEN}   • Same data collection ✅"
echo -e "${GREEN}   • Smart startup logic ✅"
echo -e "${GREEN}   • All services working ✅${NC}"

# Cleanup option
echo
echo -e "${YELLOW}🧹 Remove temporary directory? (y/n)${NC}"
read -r cleanup
if [[ $cleanup =~ ^[Yy]$ ]]; then
    rm -rf "$PACKAGE_DIR"
    echo -e "${GREEN}✅ Cleanup completed${NC}"
fi

echo
echo -e "${GREEN}🚀 PRODUCTION ACCURATE package v3.6 ready for deployment!${NC}"
