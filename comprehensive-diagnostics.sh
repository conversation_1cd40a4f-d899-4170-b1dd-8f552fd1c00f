#!/bin/bash

# Solar Prediction System - COMPREHENSIVE DIAGNOSTICS
# Collects ALL logs and performs complete system testing

echo
echo "========================================"
echo "  SOLAR PREDICTION COMPREHENSIVE DIAGNOSTICS"
echo "  EXACT WORKING IMAGE VERIFICATION"
echo "========================================"
echo

# Create diagnostics directory with timestamp
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
DIAG_DIR="solar-diagnostics-${TIMESTAMP}"
mkdir -p "$DIAG_DIR"

echo "📁 Created diagnostics directory: $DIAG_DIR"
echo

# Start logging
LOG_FILE="$DIAG_DIR/comprehensive-diagnostics.log"
echo "SOLAR PREDICTION SYSTEM - COMPREHENSIVE DIAGNOSTICS" > "$LOG_FILE"
echo "Timestamp: $TIMESTAMP" >> "$LOG_FILE"
echo "==================================================" >> "$LOG_FILE"
echo >> "$LOG_FILE"

echo "🔍 Starting comprehensive system diagnostics..."
echo "🔍 Starting comprehensive system diagnostics..." >> "$LOG_FILE"
echo

# ========================================
# 1. SYSTEM INFORMATION
# ========================================
echo "1️⃣ COLLECTING SYSTEM INFORMATION..."
echo "1. SYSTEM INFORMATION" >> "$LOG_FILE"
echo "====================" >> "$LOG_FILE"

echo "   • Operating System..."
uname -a >> "$LOG_FILE"
lsb_release -a >> "$LOG_FILE" 2>/dev/null || echo "lsb_release not available" >> "$LOG_FILE"

echo "   • Docker Version..."
docker --version >> "$LOG_FILE" 2>&1

echo "   • Docker Compose Version..."
docker-compose --version >> "$LOG_FILE" 2>&1

echo "   • Available Memory..."
free -h >> "$LOG_FILE"

echo "   • Available Disk Space..."
df -h >> "$LOG_FILE"
echo >> "$LOG_FILE"

# ========================================
# 2. DOCKER STATUS
# ========================================
echo "2️⃣ CHECKING DOCKER STATUS..."
echo "2. DOCKER STATUS" >> "$LOG_FILE"
echo "================" >> "$LOG_FILE"

echo "   • Docker Service Status..."
docker info >> "$LOG_FILE" 2>&1
echo >> "$LOG_FILE"

echo "   • Docker Images..."
docker images >> "$LOG_FILE" 2>&1
echo >> "$LOG_FILE"

echo "   • Docker Containers..."
docker ps -a >> "$LOG_FILE" 2>&1
echo >> "$LOG_FILE"

echo "   • Docker Networks..."
docker network ls >> "$LOG_FILE" 2>&1
echo >> "$LOG_FILE"

echo "   • Docker Volumes..."
docker volume ls >> "$LOG_FILE" 2>&1
echo >> "$LOG_FILE"

# ========================================
# 3. CONTAINER LOGS
# ========================================
echo "3️⃣ COLLECTING CONTAINER LOGS..."
echo "3. CONTAINER LOGS" >> "$LOG_FILE"
echo "=================" >> "$LOG_FILE"

echo "   • Docker Compose Status..."
docker-compose ps >> "$LOG_FILE" 2>&1
echo >> "$LOG_FILE"

echo "   • Solar Prediction App Logs..."
docker-compose logs solar-prediction > "$DIAG_DIR/solar-prediction-app.log" 2>&1
echo "Solar Prediction App logs saved to solar-prediction-app.log" >> "$LOG_FILE"

echo "   • PostgreSQL Database Logs..."
docker-compose logs postgres > "$DIAG_DIR/postgres-database.log" 2>&1
echo "PostgreSQL Database logs saved to postgres-database.log" >> "$LOG_FILE"

echo "   • All Services Combined Logs..."
docker-compose logs > "$DIAG_DIR/all-services-combined.log" 2>&1
echo "All services logs saved to all-services-combined.log" >> "$LOG_FILE"
echo >> "$LOG_FILE"

# ========================================
# 4. NETWORK CONNECTIVITY
# ========================================
echo "4️⃣ TESTING NETWORK CONNECTIVITY..."
echo "4. NETWORK CONNECTIVITY" >> "$LOG_FILE"
echo "=======================" >> "$LOG_FILE"

echo "   • Testing localhost connectivity..."
ping -c 4 localhost >> "$LOG_FILE" 2>&1

echo "   • Testing port 8100 (Main App)..."
netstat -an | grep :8100 >> "$LOG_FILE" 2>&1

echo "   • Testing port 8110 (Enhanced Billing)..."
netstat -an | grep :8110 >> "$LOG_FILE" 2>&1

echo "   • Testing port 5433 (Database)..."
netstat -an | grep :5433 >> "$LOG_FILE" 2>&1
echo >> "$LOG_FILE"

# ========================================
# 5. API ENDPOINTS TESTING
# ========================================
echo "5️⃣ TESTING API ENDPOINTS..."
echo "5. API ENDPOINTS TESTING" >> "$LOG_FILE"
echo "========================" >> "$LOG_FILE"

echo "   • Testing Main Application Health..."
if curl -v -m 30 http://localhost:8100/health > "$DIAG_DIR/health-endpoint.log" 2>&1; then
    echo "✅ Health Endpoint: WORKING" >> "$LOG_FILE"
    HEALTH_STATUS="WORKING"
else
    echo "❌ Health Endpoint: FAILED" >> "$LOG_FILE"
    HEALTH_STATUS="FAILED"
fi

echo "   • Testing API Documentation..."
if curl -v -m 30 http://localhost:8100/docs > "$DIAG_DIR/docs-endpoint.log" 2>&1; then
    echo "✅ API Docs: WORKING" >> "$LOG_FILE"
    DOCS_STATUS="WORKING"
else
    echo "❌ API Docs: FAILED" >> "$LOG_FILE"
    DOCS_STATUS="FAILED"
fi

echo "   • Testing SolaX Data Endpoint..."
if curl -v -m 30 http://localhost:8100/api/v1/data/solax/latest > "$DIAG_DIR/solax-data.log" 2>&1; then
    echo "✅ SolaX Data: WORKING" >> "$LOG_FILE"
    SOLAX_STATUS="WORKING"
else
    echo "❌ SolaX Data: FAILED" >> "$LOG_FILE"
    SOLAX_STATUS="FAILED"
fi

echo "   • Testing Weather Data Endpoint..."
if curl -v -m 30 http://localhost:8100/api/v1/data/weather/latest > "$DIAG_DIR/weather-data.log" 2>&1; then
    echo "✅ Weather Data: WORKING" >> "$LOG_FILE"
    WEATHER_STATUS="WORKING"
else
    echo "❌ Weather Data: FAILED" >> "$LOG_FILE"
    WEATHER_STATUS="FAILED"
fi

echo "   • Testing Enhanced Billing ROI..."
if curl -v -m 30 http://localhost:8110/api/v1/roi/system1 > "$DIAG_DIR/roi-endpoint.log" 2>&1; then
    echo "✅ Enhanced Billing ROI: WORKING" >> "$LOG_FILE"
    ROI_STATUS="WORKING"
else
    echo "❌ Enhanced Billing ROI: FAILED" >> "$LOG_FILE"
    ROI_STATUS="FAILED"
fi

echo "   • Testing Daily Cost Endpoint..."
if curl -v -m 30 http://localhost:8110/api/v1/billing/system1/daily > "$DIAG_DIR/daily-cost.log" 2>&1; then
    echo "✅ Daily Cost: WORKING" >> "$LOG_FILE"
    DAILY_STATUS="WORKING"
else
    echo "❌ Daily Cost: FAILED" >> "$LOG_FILE"
    DAILY_STATUS="FAILED"
fi

echo "   • Testing Tariffs Endpoint..."
if curl -v -m 30 http://localhost:8110/api/v1/tariffs > "$DIAG_DIR/tariffs.log" 2>&1; then
    echo "✅ Tariffs: WORKING" >> "$LOG_FILE"
    TARIFFS_STATUS="WORKING"
else
    echo "❌ Tariffs: FAILED" >> "$LOG_FILE"
    TARIFFS_STATUS="FAILED"
fi

echo "   • Testing Data Collection Endpoints..."
if curl -v -m 30 -X POST http://localhost:8100/api/v1/data/collect/solax > "$DIAG_DIR/collect-solax.log" 2>&1; then
    echo "✅ SolaX Collection: WORKING" >> "$LOG_FILE"
    COLLECT_SOLAX_STATUS="WORKING"
else
    echo "❌ SolaX Collection: FAILED" >> "$LOG_FILE"
    COLLECT_SOLAX_STATUS="FAILED"
fi

if curl -v -m 30 -X POST http://localhost:8100/api/v1/data/collect/weather > "$DIAG_DIR/collect-weather.log" 2>&1; then
    echo "✅ Weather Collection: WORKING" >> "$LOG_FILE"
    COLLECT_WEATHER_STATUS="WORKING"
else
    echo "❌ Weather Collection: FAILED" >> "$LOG_FILE"
    COLLECT_WEATHER_STATUS="FAILED"
fi
echo >> "$LOG_FILE"

# ========================================
# 6. DATABASE CONNECTIVITY
# ========================================
echo "6️⃣ TESTING DATABASE CONNECTIVITY..."
echo "6. DATABASE CONNECTIVITY" >> "$LOG_FILE"
echo "========================" >> "$LOG_FILE"

echo "   • Testing PostgreSQL Connection..."
if docker exec solar-prediction-db psql -U postgres -d solar_prediction -c "SELECT version();" > "$DIAG_DIR/database-version.log" 2>&1; then
    echo "✅ Database Connection: WORKING" >> "$LOG_FILE"
    DB_STATUS="WORKING"
else
    echo "❌ Database Connection: FAILED" >> "$LOG_FILE"
    DB_STATUS="FAILED"
fi

echo "   • Testing Database Tables..."
docker exec solar-prediction-db psql -U postgres -d solar_prediction -c "\\dt" > "$DIAG_DIR/database-tables.log" 2>&1

echo "   • Testing Data Availability..."
docker exec solar-prediction-db psql -U postgres -d solar_prediction -c "SELECT COUNT(*) FROM solax_data;" > "$DIAG_DIR/solax-data-count.log" 2>&1
docker exec solar-prediction-db psql -U postgres -d solar_prediction -c "SELECT COUNT(*) FROM weather_data;" > "$DIAG_DIR/weather-data-count.log" 2>&1
echo >> "$LOG_FILE"

# ========================================
# 7. TELEGRAM BOT TESTING
# ========================================
echo "7️⃣ TESTING TELEGRAM BOT ENDPOINTS..."
echo "7. TELEGRAM BOT ENDPOINTS" >> "$LOG_FILE"
echo "=========================" >> "$LOG_FILE"

echo "   • Testing Telegram Bot Token..."
if curl -v -m 30 "https://api.telegram.org/bot8060881816:AAFBhGADTAAcUkbom_FEFSkOHoT5N6t5png/getMe" > "$DIAG_DIR/telegram-bot-token.log" 2>&1; then
    echo "✅ Telegram Bot Token: VALID" >> "$LOG_FILE"
    TELEGRAM_TOKEN_STATUS="VALID"
else
    echo "❌ Telegram Bot Token: INVALID" >> "$LOG_FILE"
    TELEGRAM_TOKEN_STATUS="INVALID"
fi

echo "   • Testing Telegram Bot Webhook..."
curl -v -m 30 "https://api.telegram.org/bot8060881816:AAFBhGADTAAcUkbom_FEFSkOHoT5N6t5png/getWebhookInfo" > "$DIAG_DIR/telegram-webhook.log" 2>&1

echo "   • Testing Telegram Bot Updates..."
curl -v -m 30 "https://api.telegram.org/bot8060881816:AAFBhGADTAAcUkbom_FEFSkOHoT5N6t5png/getUpdates" > "$DIAG_DIR/telegram-updates.log" 2>&1

echo "   • Testing Internal Telegram Bot Service..."
if docker exec solar-prediction-app ps aux > "$DIAG_DIR/telegram-process.log" 2>&1; then
    echo "✅ Telegram Bot Process: RUNNING" >> "$LOG_FILE"
    TELEGRAM_PROCESS_STATUS="RUNNING"
else
    echo "❌ Telegram Bot Process: NOT RUNNING" >> "$LOG_FILE"
    TELEGRAM_PROCESS_STATUS="NOT_RUNNING"
fi
echo >> "$LOG_FILE"

# ========================================
# 8. FINAL SUMMARY REPORT
# ========================================
echo "📊 GENERATING FINAL SUMMARY REPORT..."
echo "8. FINAL SUMMARY REPORT" >> "$LOG_FILE"
echo "=======================" >> "$LOG_FILE"

echo
echo "========================================"
echo "  COMPREHENSIVE DIAGNOSTICS SUMMARY"
echo "========================================"
echo
echo "SYSTEM STATUS:"
echo "  • Health Endpoint: $HEALTH_STATUS"
echo "  • API Documentation: $DOCS_STATUS"
echo "  • Database: $DB_STATUS"
echo
echo "DATA ENDPOINTS:"
echo "  • SolaX Data: $SOLAX_STATUS"
echo "  • Weather Data: $WEATHER_STATUS"
echo
echo "ENHANCED BILLING:"
echo "  • ROI Endpoint: $ROI_STATUS"
echo "  • Daily Cost: $DAILY_STATUS"
echo "  • Tariffs: $TARIFFS_STATUS"
echo
echo "DATA COLLECTION:"
echo "  • SolaX Collection: $COLLECT_SOLAX_STATUS"
echo "  • Weather Collection: $COLLECT_WEATHER_STATUS"
echo
echo "TELEGRAM BOT:"
echo "  • Token Validation: $TELEGRAM_TOKEN_STATUS"
echo "  • Process Status: $TELEGRAM_PROCESS_STATUS"
echo

# Write summary to log file
{
    echo "SYSTEM STATUS:"
    echo "  Health Endpoint: $HEALTH_STATUS"
    echo "  API Documentation: $DOCS_STATUS"
    echo "  Database: $DB_STATUS"
    echo "DATA ENDPOINTS:"
    echo "  SolaX Data: $SOLAX_STATUS"
    echo "  Weather Data: $WEATHER_STATUS"
    echo "ENHANCED BILLING:"
    echo "  ROI Endpoint: $ROI_STATUS"
    echo "  Daily Cost: $DAILY_STATUS"
    echo "  Tariffs: $TARIFFS_STATUS"
    echo "DATA COLLECTION:"
    echo "  SolaX Collection: $COLLECT_SOLAX_STATUS"
    echo "  Weather Collection: $COLLECT_WEATHER_STATUS"
    echo "TELEGRAM BOT:"
    echo "  Token Validation: $TELEGRAM_TOKEN_STATUS"
    echo "  Process Status: $TELEGRAM_PROCESS_STATUS"
    echo
} >> "$LOG_FILE"

echo
echo "========================================"
echo "  DIAGNOSTICS COMPLETED"
echo "========================================"
echo
echo "📁 All diagnostic files saved in: $DIAG_DIR"
echo "📄 Main log file: $LOG_FILE"
echo
echo "📋 Key Files to Review:"
echo "  • comprehensive-diagnostics.log - Main summary"
echo "  • all-services-combined.log - All container logs"
echo "  • health-endpoint.log - Health check details"
echo "  • roi-endpoint.log - Enhanced billing details"
echo "  • telegram-bot-token.log - Telegram bot validation"
echo "  • database-version.log - Database connectivity"
echo "  • solax-data-count.log - Data availability"
echo "  • weather-data-count.log - Weather data availability"
echo
echo "🔍 If issues found, share the entire $DIAG_DIR folder"
echo "   for detailed analysis and troubleshooting."
echo

# Create a summary file for easy sharing
cat > "$DIAG_DIR/SUMMARY.txt" << EOF
SOLAR PREDICTION SYSTEM - DIAGNOSTIC SUMMARY
==============================================
Timestamp: $TIMESTAMP
Package: solar-prediction-complete-image-20250627_052453.tar.gz
Source: EXACT working Docker image export

QUICK STATUS:
Health: $HEALTH_STATUS
Database: $DB_STATUS
Enhanced Billing: $ROI_STATUS
Telegram Bot: $TELEGRAM_TOKEN_STATUS
SolaX Data: $SOLAX_STATUS
Weather Data: $WEATHER_STATUS

EXPECTED WORKING ENDPOINTS:
• Web Interface: http://localhost:8100
• Health Check: http://localhost:8100/health
• API Docs: http://localhost:8100/docs
• Enhanced Billing: http://localhost:8110
• ROI Endpoint: http://localhost:8110/api/v1/roi/system1
• Daily Cost: http://localhost:8110/api/v1/billing/system1/daily
• Tariffs: http://localhost:8110/api/v1/tariffs
• SolaX Data: http://localhost:8100/api/v1/data/solax/latest
• Weather Data: http://localhost:8100/api/v1/data/weather/latest
• Database: localhost:5433
• Telegram Bot: @grlvSolarAI_bot

For detailed analysis, review all files in this directory.
If sharing for troubleshooting, include the entire folder.
EOF

echo "📄 Created SUMMARY.txt for quick reference"
echo
echo "🎯 NEXT STEPS:"
echo "  1. Review the summary above"
echo "  2. Check SUMMARY.txt for quick reference"
echo "  3. If issues found, review detailed log files"
echo "  4. Share entire $DIAG_DIR folder if help needed"
echo
