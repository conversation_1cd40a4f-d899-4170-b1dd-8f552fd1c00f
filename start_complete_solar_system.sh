#!/bin/bash

# Complete Solar Prediction System Startup Script
# Starts PostgreSQL, Production API, and Telegram Bot

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🌞 COMPLETE SOLAR PREDICTION SYSTEM STARTUP${NC}"
echo "============================================================"
echo

# Function to check if a port is in use (fixed to use netstat)
check_port() {
    local port=$1
    if netstat -tlnp 2>/dev/null | grep -q ":$port "; then
        return 0  # Port is in use
    else
        return 1  # Port is free
    fi
}

# Function to wait for service to be ready
wait_for_service() {
    local url=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1
    
    echo -n "⏳ Waiting for $service_name to be ready"
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url" >/dev/null 2>&1; then
            echo -e " ${GREEN}✅${NC}"
            return 0
        fi
        echo -n "."
        sleep 2
        ((attempt++))
    done
    echo -e " ${RED}❌ Timeout${NC}"
    return 1
}

# Step 1: Start PostgreSQL
echo -e "${YELLOW}📋 STEP 1: PostgreSQL Database${NC}"
echo "------------------------------------------------------------"

if check_port 5433; then
    echo -e "${GREEN}✅ PostgreSQL already running on port 5433${NC}"
else
    echo "🐘 Starting PostgreSQL container..."
    docker compose up -d postgres
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ PostgreSQL started successfully${NC}"
        sleep 5
    else
        echo -e "${RED}❌ Failed to start PostgreSQL${NC}"
        exit 1
    fi
fi

echo

# Step 2: Start Production API
echo -e "${YELLOW}📋 STEP 2: Production API${NC}"
echo "------------------------------------------------------------"

if check_port 8100; then
    echo -e "${YELLOW}⚠️  Port 8100 already in use. Stopping existing service...${NC}"
    pkill -f "scripts/production_app.py" || true
    sleep 3
fi

echo "🚀 Starting Production API..."
cd /home/<USER>/solar-prediction-project
python3 scripts/production_app.py &
PRODUCTION_PID=$!

# Wait for API to be ready
if wait_for_service "http://localhost:8100/health" "Production API"; then
    echo -e "${GREEN}✅ Production API started successfully (PID: $PRODUCTION_PID)${NC}"
else
    echo -e "${RED}❌ Production API failed to start${NC}"
    kill $PRODUCTION_PID 2>/dev/null || true
    exit 1
fi

echo

# Step 3: Start Enhanced Billing System
echo -e "${YELLOW}📋 STEP 3: Enhanced Billing System${NC}"
echo "------------------------------------------------------------"

if check_port 8110; then
    echo -e "${YELLOW}⚠️  Port 8110 already in use. Stopping existing service...${NC}"
    pkill -f "enhanced_billing_system.py" || true
    sleep 3
fi

echo "💰 Starting Enhanced Billing System..."
python3 scripts/frontend_system/enhanced_billing_system.py &
BILLING_PID=$!

# Wait for Billing API to be ready
if wait_for_service "http://localhost:8110/health" "Enhanced Billing System"; then
    echo -e "${GREEN}✅ Enhanced Billing System started successfully (PID: $BILLING_PID)${NC}"
else
    echo -e "${RED}❌ Enhanced Billing System failed to start${NC}"
    kill $BILLING_PID 2>/dev/null || true
    exit 1
fi

echo

# Step 4: Start Telegram Bot
echo -e "${YELLOW}📋 STEP 4: Telegram Bot${NC}"
echo "------------------------------------------------------------"

# Check if Telegram bot is already running
if pgrep -f "greek_telegram_bot.py" >/dev/null; then
    echo -e "${YELLOW}⚠️  Telegram bot already running. Stopping existing instance...${NC}"
    pkill -f "greek_telegram_bot.py" || true
    sleep 3
fi

echo "🤖 Starting Telegram Bot..."
cd /home/<USER>/solar-prediction-project
./telegram_bot_env/bin/python scripts/frontend_system/greek_telegram_bot.py &
TELEGRAM_PID=$!

sleep 5

if ps -p $TELEGRAM_PID > /dev/null; then
    echo -e "${GREEN}✅ Telegram Bot started successfully (PID: $TELEGRAM_PID)${NC}"
else
    echo -e "${RED}❌ Telegram Bot failed to start${NC}"
fi

echo

# Step 5: System Status Summary
echo -e "${YELLOW}📋 STEP 5: System Status Summary${NC}"
echo "------------------------------------------------------------"

echo "🔍 Checking all services..."
echo

# Check PostgreSQL
if check_port 5433; then
    echo -e "🐘 PostgreSQL: ${GREEN}✅ Running (port 5433)${NC}"
else
    echo -e "🐘 PostgreSQL: ${RED}❌ Not running${NC}"
fi

# Check Production API
if check_port 8100; then
    echo -e "🚀 Production API: ${GREEN}✅ Running (port 8100)${NC}"

    # Test API health
    if curl -s "http://localhost:8100/health" >/dev/null 2>&1; then
        echo -e "   └─ Health check: ${GREEN}✅ Healthy${NC}"
    else
        echo -e "   └─ Health check: ${RED}❌ Unhealthy${NC}"
    fi
else
    echo -e "🚀 Production API: ${RED}❌ Not running${NC}"
fi

# Check Enhanced Billing System
if check_port 8110; then
    echo -e "💰 Enhanced Billing: ${GREEN}✅ Running (port 8110)${NC}"

    # Test Billing API health
    if curl -s "http://localhost:8110/health" >/dev/null 2>&1; then
        echo -e "   └─ Health check: ${GREEN}✅ Healthy${NC}"
    else
        echo -e "   └─ Health check: ${RED}❌ Unhealthy${NC}"
    fi
else
    echo -e "💰 Enhanced Billing: ${RED}❌ Not running${NC}"
fi

# Check Telegram Bot
if pgrep -f "greek_telegram_bot.py" >/dev/null; then
    echo -e "🤖 Telegram Bot: ${GREEN}✅ Running${NC}"
else
    echo -e "🤖 Telegram Bot: ${RED}❌ Not running${NC}"
fi

echo

# Step 6: Access Information
echo -e "${YELLOW}📋 STEP 6: Access Information${NC}"
echo "------------------------------------------------------------"

echo "🌐 Available Services:"
echo "   • Production API: http://localhost:8100"
echo "   • API Health: http://localhost:8100/health"
echo "   • API Docs: http://localhost:8100/docs"
echo "   • Enhanced Billing: http://localhost:8110"
echo "   • Billing Health: http://localhost:8110/health"
echo "   • Database: localhost:5433"
echo "   • Telegram Bot: @grlvSolarAI_bot"
echo

echo "📊 Real-time Data:"
echo "   • SolaX Collection: Every 30 seconds"
echo "   • Weather Collection: Every 15 minutes"
echo "   • ML Predictions: Automatic"
echo

echo "🎯 Process IDs:"
echo "   • Production API: $PRODUCTION_PID"
echo "   • Enhanced Billing: $BILLING_PID"
echo "   • Telegram Bot: $TELEGRAM_PID"
echo

echo -e "${GREEN}🎉 COMPLETE SOLAR SYSTEM STARTUP COMPLETED!${NC}"
echo "============================================================"
echo
echo "💡 To stop all services, run:"
echo "   pkill -f 'scripts/production_app.py'"
echo "   pkill -f 'enhanced_billing_system.py'"
echo "   pkill -f 'greek_telegram_bot.py'"
echo "   docker compose down postgres"
echo

# Interactive Management Menu
echo -e "${BLUE}🎯 INTERACTIVE SYSTEM MANAGEMENT${NC}"
echo "============================================================"
echo "💡 This terminal will remain open for system management"
echo

# Function to show system status
show_status() {
    echo
    echo -e "${YELLOW}🔍 Current System Status:${NC}"
    echo "------------------------------------------------------------"

    # Check PostgreSQL
    if check_port 5433; then
        echo -e "🐘 PostgreSQL: ${GREEN}✅ Running (port 5433)${NC}"
    else
        echo -e "🐘 PostgreSQL: ${RED}❌ Not running${NC}"
    fi

    # Check Production API
    if check_port 8100; then
        echo -e "🚀 Production API: ${GREEN}✅ Running (port 8100)${NC}"
        if curl -s "http://localhost:8100/health" >/dev/null 2>&1; then
            echo -e "   └─ Health check: ${GREEN}✅ Healthy${NC}"
        else
            echo -e "   └─ Health check: ${RED}❌ Unhealthy${NC}"
        fi
    else
        echo -e "🚀 Production API: ${RED}❌ Not running${NC}"
    fi

    # Check Enhanced Billing
    if check_port 8110; then
        echo -e "💰 Enhanced Billing: ${GREEN}✅ Running (port 8110)${NC}"
        if curl -s "http://localhost:8110/health" >/dev/null 2>&1; then
            echo -e "   └─ Health check: ${GREEN}✅ Healthy${NC}"
        else
            echo -e "   └─ Health check: ${RED}❌ Unhealthy${NC}"
        fi
    else
        echo -e "💰 Enhanced Billing: ${RED}❌ Not running${NC}"
    fi

    # Check Telegram Bot
    if pgrep -f "greek_telegram_bot.py" >/dev/null; then
        echo -e "🤖 Telegram Bot: ${GREEN}✅ Running${NC}"
    else
        echo -e "🤖 Telegram Bot: ${RED}❌ Not running${NC}"
    fi
    echo
}

# Function to open web interface
open_web() {
    echo "🌐 Opening web interface..."
    if command -v xdg-open >/dev/null 2>&1; then
        xdg-open "http://localhost:8100/" 2>/dev/null &
    elif command -v firefox >/dev/null 2>&1; then
        firefox "http://localhost:8100/" 2>/dev/null &
    else
        echo "Please open manually: http://localhost:8100/"
    fi
}

# Function to stop all services
stop_services() {
    echo
    echo -e "${YELLOW}🛑 Stopping all services...${NC}"
    echo "------------------------------------------------------------"

    echo "Stopping Production API..."
    pkill -f 'scripts/production_app.py' || true

    echo "Stopping Enhanced Billing..."
    pkill -f 'enhanced_billing_system.py' || true

    echo "Stopping Telegram Bot..."
    pkill -f 'greek_telegram_bot.py' || true

    echo "Stopping PostgreSQL..."
    docker compose down postgres

    echo -e "${GREEN}✅ All services stopped${NC}"
    echo
}

# Show initial status
show_status

# Interactive menu loop
while true; do
    echo -e "${BLUE}Available commands:${NC}"
    echo "  s - Show system status"
    echo "  w - Open web interface"
    echo "  r - Restart all services"
    echo "  q - Stop all services and quit"
    echo "  h - Show this help"
    echo
    echo -n "Solar> "
    read -r -n 1 input
    echo

    case "$input" in
        "s"|"S")
            show_status
            ;;
        "w"|"W")
            open_web
            ;;
        "r"|"R")
            echo -e "${YELLOW}🔄 Restarting system...${NC}"
            stop_services
            sleep 3
            exec "$0"
            ;;
        "q"|"Q")
            stop_services
            echo -e "${GREEN}👋 Goodbye!${NC}"
            exit 0
            ;;
        "h"|"H"|"?")
            echo "Available commands:"
            echo "  s - Show system status"
            echo "  w - Open web interface"
            echo "  r - Restart all services"
            echo "  q - Stop all services and quit"
            echo "  h - Show this help"
            ;;
        *)
            echo "Unknown command. Press 'h' for help."
            ;;
    esac
done
