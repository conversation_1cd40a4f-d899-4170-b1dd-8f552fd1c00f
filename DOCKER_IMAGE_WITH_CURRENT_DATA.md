# Docker Image με Τρέχοντα Δεδομένα

## Επισκόπηση

Το Docker image `solar-prediction-complete:latest` περιέχει τώρα τα τρέχοντα δεδομένα από τη βάση δεδομένων αντί για το παλιό backup.

## Χαρακτηριστικά

### ✅ Ενημερωμένα Δεδομένα
- **solax_data**: 132,215 εγγραφές (ενημερωμένο από 135,526)
- **solax_data2**: 181,154 εγγραφές (ενημερωμένο από 130,529)
- **Τελευταία εγγραφή**: 25 Ιουνίου 2025 (ενημερωμένο από 16 Ιουνίου)
- **Συνολικό μέγεθος**: ~4.9GB (περιλαμβάνει όλα τα δεδομένα)

### 🐳 Περιεχόμενα Docker Image
- **PostgreSQL 15** με προ-φορτωμένα δεδομένα
- **Μονολιθική εφαρμογή** Solar Prediction System
- **Supervisor** για διαχείριση υπηρεσιών
- **Όλα τα αρχεία του project** (scripts, models, data)
- **Timezone**: Europe/Athens

### 🚀 Υπηρεσίες
- **PostgreSQL**: Port 5432
- **Web Interface**: Port 8100
- **Telegram Bot**: Port 8109
- **Enhanced Billing**: Port 8110

## Χρήση

### Εκκίνηση Container
```bash
docker run -d \
  --name solar-prediction-system \
  -p 5432:5432 \
  -p 8100:8100 \
  -p 8109:8109 \
  -p 8110:8110 \
  solar-prediction-complete:latest
```

### Έλεγχος Κατάστασης
```bash
# Logs
docker logs solar-prediction-system

# Κατάσταση υπηρεσιών
docker exec solar-prediction-system supervisorctl status

# Έλεγχος βάσης δεδομένων
docker exec solar-prediction-system psql -U postgres -d solar_prediction -c "SELECT COUNT(*) FROM solax_data;"
```

### Πρόσβαση στις Υπηρεσίες
- **Web Interface**: http://localhost:8100
- **API Documentation**: http://localhost:8100/docs
- **PostgreSQL**: localhost:5432 (postgres/postgres)

## Ενημέρωση με Νέα Δεδομένα

Για να ενημερώσετε το Docker image με τα πιο πρόσφατα δεδομένα:

```bash
# Εκτέλεση του script ενημέρωσης
./update_docker_with_current_data.sh
```

Το script:
1. Ελέγχει αν η PostgreSQL τρέχει
2. Δημιουργεί backup από την τρέχουσα βάση
3. Ενημερώνει το Dockerfile
4. Κάνει rebuild το Docker image
5. Καθαρίζει προσωρινά αρχεία

## Τεχνικές Λεπτομέρειες

### Dockerfile
- **Base Image**: python:3.11-slim
- **PostgreSQL**: Εγκατεστημένη και προ-ρυθμισμένη
- **Dependencies**: Όλες οι απαιτούμενες βιβλιοθήκες Python
- **Supervisor**: Αυτόματη διαχείριση υπηρεσιών

### Δομή Αρχείων
```
/app/
├── scripts/           # Παραγωγικά scripts
├── models/           # ML μοντέλα (symlink)
├── data/            # Δεδομένα (symlink)
├── logs/            # Log αρχεία
├── static/          # Static αρχεία web
├── templates/       # HTML templates
└── requirements.txt # Python dependencies
```

### Environment Variables
- `DATABASE_URL`: postgresql://postgres:postgres@localhost:5432/solar_prediction
- `PYTHONPATH`: /app:/app/src
- `TZ`: Europe/Athens

## Αντιμετώπιση Προβλημάτων

### Container δεν ξεκινάει
```bash
# Έλεγχος logs
docker logs solar-prediction-system

# Έλεγχος supervisor
docker exec solar-prediction-system supervisorctl status
```

### Βάση δεδομένων δεν απαντά
```bash
# Έλεγχος PostgreSQL
docker exec solar-prediction-system pg_isready -U postgres

# Επανεκκίνηση PostgreSQL
docker exec solar-prediction-system supervisorctl restart postgresql
```

### Εφαρμογή δεν απαντά
```bash
# Έλεγχος εφαρμογής
docker exec solar-prediction-system supervisorctl restart solar-prediction-app

# Έλεγχος logs εφαρμογής
docker exec solar-prediction-system tail -f /app/logs/production_app.log
```

## Backup & Restore

### Backup Δεδομένων
```bash
# Backup από container
docker exec solar-prediction-system pg_dump -U postgres solar_prediction > backup.sql
```

### Restore Δεδομένων
```bash
# Restore σε container
docker exec -i solar-prediction-system psql -U postgres solar_prediction < backup.sql
```

## Ασφάλεια

- PostgreSQL τρέχει με default credentials (postgres/postgres)
- Για παραγωγή, αλλάξτε τα credentials
- Το container εκθέτει ports μόνο στο localhost
- Χρησιμοποιήστε reverse proxy για εξωτερική πρόσβαση

## Συντήρηση

### Καθαρισμός Παλιών Images
```bash
# Αφαίρεση παλιών images
docker image prune

# Αφαίρεση συγκεκριμένου image
docker rmi solar-prediction-complete:old-tag
```

### Ενημέρωση Dependencies
```bash
# Rebuild με νέες dependencies
docker build -f Dockerfile.complete -t solar-prediction-complete:latest .
```

## Επόμενα Βήματα

1. **Τεστ σε παραγωγικό περιβάλλον**
2. **Ρύθμιση monitoring**
3. **Αυτοματοποίηση backups**
4. **Ρύθμιση SSL/TLS**
5. **Δημιουργία CI/CD pipeline**

---

**Ημερομηνία Δημιουργίας**: 28 Ιουνίου 2025  
**Έκδοση**: 1.0 με τρέχοντα δεδομένα  
**Μέγεθος Image**: ~4.9GB  
**Κατάσταση**: Έτοιμο για χρήση
