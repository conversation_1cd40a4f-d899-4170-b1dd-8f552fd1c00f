#!/bin/bash

# Solar Prediction System - Portable Package Creator
# Creates a complete portable package for deployment on any system

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 SOLAR PREDICTION PORTABLE PACKAGE CREATOR${NC}"
echo "============================================================"
echo

# Configuration
PACKAGE_NAME="solar-prediction-portable"
PACKAGE_VERSION="v2.0"
PACKAGE_DIR="${PACKAGE_NAME}-${PACKAGE_VERSION}"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
FINAL_PACKAGE="${PACKAGE_NAME}-${PACKAGE_VERSION}-${TIMESTAMP}.tar.gz"

echo -e "${YELLOW}📦 Package Configuration:${NC}"
echo "   • Package Name: $PACKAGE_NAME"
echo "   • Version: $PACKAGE_VERSION"
echo "   • Timestamp: $TIMESTAMP"
echo "   • Final Package: $FINAL_PACKAGE"
echo

# Create package directory
echo -e "${BLUE}📁 Creating package directory...${NC}"
rm -rf "$PACKAGE_DIR"
mkdir -p "$PACKAGE_DIR"

# Copy essential files
echo -e "${BLUE}📋 Copying essential files...${NC}"

# Core application files
cp -r scripts/ "$PACKAGE_DIR/"
cp -r static/ "$PACKAGE_DIR/"
cp -r src/ "$PACKAGE_DIR/" 2>/dev/null || echo "   • src/ not found, skipping"

# Docker configuration
cp docker-compose.yml "$PACKAGE_DIR/"
cp Dockerfile "$PACKAGE_DIR/"
cp requirements.txt "$PACKAGE_DIR/"

# Environment and configuration
cp .env.example "$PACKAGE_DIR/.env" 2>/dev/null || echo "   • .env.example not found, creating default"

# Models (if they exist)
if [ -d "models" ]; then
    echo "   • Copying models directory..."
    cp -r models/ "$PACKAGE_DIR/"
else
    echo "   • Models directory not found, creating placeholder"
    mkdir -p "$PACKAGE_DIR/models"
    echo "# Models will be downloaded on first run" > "$PACKAGE_DIR/models/README.md"
fi

# Data samples (not full database)
if [ -d "data" ]; then
    echo "   • Creating data samples..."
    mkdir -p "$PACKAGE_DIR/data/samples"
    # Copy only sample data, not full database
    find data/ -name "*.csv" -size -1M | head -5 | xargs -I {} cp {} "$PACKAGE_DIR/data/samples/" 2>/dev/null || true
fi

# Documentation
mkdir -p "$PACKAGE_DIR/docs"
cp docs/*.md "$PACKAGE_DIR/docs/" 2>/dev/null || echo "   • No documentation found"

echo -e "${GREEN}✅ Files copied successfully${NC}"
echo

# Create startup scripts
echo -e "${BLUE}🚀 Creating startup scripts...${NC}"

# Windows startup script
cat > "$PACKAGE_DIR/start-windows.bat" << 'EOF'
@echo off
echo Solar Prediction System - Windows Startup
echo ==========================================

echo Checking Docker...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker is not installed or not running
    echo Please install Docker Desktop from: https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
)

echo Starting Solar Prediction System...
docker-compose up -d

echo.
echo System started successfully!
echo Web Interface: http://localhost:8100
echo.
echo Press any key to open web interface...
pause >nul
start http://localhost:8100

echo.
echo To stop the system, run: stop-windows.bat
pause
EOF

# Windows stop script
cat > "$PACKAGE_DIR/stop-windows.bat" << 'EOF'
@echo off
echo Stopping Solar Prediction System...
docker-compose down
echo System stopped.
pause
EOF

# Linux/macOS startup script
cat > "$PACKAGE_DIR/start-unix.sh" << 'EOF'
#!/bin/bash

echo "🌞 Solar Prediction System - Unix Startup"
echo "=========================================="

# Check Docker
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed"
    echo "Please install Docker from: https://docs.docker.com/get-docker/"
    exit 1
fi

if ! docker info &> /dev/null; then
    echo "❌ Docker is not running"
    echo "Please start Docker and try again"
    exit 1
fi

echo "🚀 Starting Solar Prediction System..."
docker-compose up -d

echo
echo "✅ System started successfully!"
echo "🌐 Web Interface: http://localhost:8100"
echo "📊 Health Check: http://localhost:8100/health"
echo

# Try to open web browser
if command -v xdg-open &> /dev/null; then
    xdg-open "http://localhost:8100" 2>/dev/null &
elif command -v open &> /dev/null; then
    open "http://localhost:8100" 2>/dev/null &
fi

echo "💡 To stop the system, run: ./stop-unix.sh"
EOF

# Unix stop script
cat > "$PACKAGE_DIR/stop-unix.sh" << 'EOF'
#!/bin/bash
echo "🛑 Stopping Solar Prediction System..."
docker-compose down
echo "✅ System stopped."
EOF

# Make scripts executable
chmod +x "$PACKAGE_DIR/start-unix.sh"
chmod +x "$PACKAGE_DIR/stop-unix.sh"

echo -e "${GREEN}✅ Startup scripts created${NC}"
echo

# Create README
echo -e "${BLUE}📖 Creating README...${NC}"

cat > "$PACKAGE_DIR/README.md" << 'EOF'
# Solar Prediction System - Portable Package

## 🌞 Quick Start

### Windows
1. Install Docker Desktop from: https://www.docker.com/products/docker-desktop
2. Double-click `start-windows.bat`
3. Wait for startup (first time takes 2-3 minutes)
4. Open http://localhost:8100

### Linux/macOS
1. Install Docker: https://docs.docker.com/get-docker/
2. Run: `./start-unix.sh`
3. Open http://localhost:8100

## 🔧 Configuration

Edit `.env` file to customize:
- Database settings
- API keys
- Telegram bot configuration

## 🌐 Access Points

- **Web Interface**: http://localhost:8100
- **API Documentation**: http://localhost:8100/docs
- **Health Check**: http://localhost:8100/health
- **Enhanced Billing**: http://localhost:8110

## 🛑 Stopping

### Windows
Run `stop-windows.bat`

### Linux/macOS
Run `./stop-unix.sh`

## 📊 Features

- ✅ Real-time solar data collection
- ✅ ML-powered predictions (94.31% accuracy)
- ✅ Weather integration
- ✅ Telegram bot notifications
- ✅ Financial ROI calculations
- ✅ Web dashboard

## 🔧 Troubleshooting

### Docker Issues
```bash
# Check Docker status
docker --version
docker info

# View logs
docker-compose logs

# Restart system
docker-compose restart
```

### Port Conflicts
If ports 8100 or 5433 are in use, edit `docker-compose.yml`:
```yaml
ports:
  - "8200:8100"  # Change 8100 to 8200
```

## 📞 Support

For issues or questions, check the documentation in the `docs/` folder.
EOF

echo -e "${GREEN}✅ README created${NC}"
echo

# Create .env file if it doesn't exist
if [ ! -f "$PACKAGE_DIR/.env" ]; then
    echo -e "${BLUE}⚙️ Creating default .env file...${NC}"
    cat > "$PACKAGE_DIR/.env" << 'EOF'
# Solar Prediction System Configuration

# Database Configuration
DATABASE_HOST=postgres
DATABASE_PORT=5432
DATABASE_USER=postgres
DATABASE_PASSWORD=postgres
DATABASE_NAME=solar_prediction

# SolaX API Configuration (Update with your values)
SOLAX_TOKEN_ID=your_token_here
SOLAX_WIFI_SN_SYSTEM1=your_wifi_sn_here
SOLAX_WIFI_SN_SYSTEM2=your_wifi_sn_here

# Telegram Bot Configuration (Optional)
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_CHAT_ID=your_chat_id_here

# Weather API Configuration
WEATHER_LATITUDE=38.141348260997596
WEATHER_LONGITUDE=24.0071653937747

# Application Configuration
DEBUG=false
LOG_LEVEL=info
ENVIRONMENT=production
EOF
    echo -e "${GREEN}✅ Default .env created${NC}"
fi

# Create package archive
echo -e "${BLUE}📦 Creating package archive...${NC}"
tar -czf "$FINAL_PACKAGE" "$PACKAGE_DIR"

# Get package size
PACKAGE_SIZE=$(du -h "$FINAL_PACKAGE" | cut -f1)

echo
echo -e "${GREEN}🎉 PORTABLE PACKAGE CREATED SUCCESSFULLY!${NC}"
echo "============================================================"
echo "📦 Package: $FINAL_PACKAGE"
echo "📏 Size: $PACKAGE_SIZE"
echo "📁 Directory: $PACKAGE_DIR"
echo
echo -e "${YELLOW}📋 Next Steps:${NC}"
echo "1. Transfer $FINAL_PACKAGE to target system"
echo "2. Extract: tar -xzf $FINAL_PACKAGE"
echo "3. Install Docker on target system"
echo "4. Run startup script for your OS"
echo
echo -e "${BLUE}💡 Package Contents:${NC}"
echo "   • Complete application code"
echo "   • Docker configuration"
echo "   • Startup scripts (Windows/Unix)"
echo "   • Documentation"
echo "   • Sample data"
echo "   • Configuration templates"
echo

# Cleanup
echo -e "${YELLOW}🧹 Cleanup temporary directory? (y/n)${NC}"
read -r cleanup
if [[ $cleanup =~ ^[Yy]$ ]]; then
    rm -rf "$PACKAGE_DIR"
    echo -e "${GREEN}✅ Cleanup completed${NC}"
fi

echo
echo -e "${GREEN}✨ Portable package ready for deployment!${NC}"
