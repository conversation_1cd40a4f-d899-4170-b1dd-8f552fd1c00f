#!/usr/bin/env python3
"""
Telegram Menu Simulation - Simulate each menu option exactly as the bot would respond
"""

import requests
import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime

# Configuration
API_BASE_URL = "http://localhost:8100"
BILLING_API_URL = "http://localhost:8110"

DB_CONFIG = {
    'host': 'localhost',
    'port': 5433,
    'database': 'solar_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

class TelegramMenuSimulation:
    """Simulate exact Telegram bot menu responses"""
    
    def __init__(self):
        print("📱 TELEGRAM BOT MENU SIMULATION")
        print("="*80)
        print("Προσομοίωση ακριβώς όπως θα απαντήσει το Telegram bot...")
        print()
    
    def simulate_start_command(self):
        """Simulate /start command"""
        print("🤖 /start COMMAND")
        print("-" * 50)
        
        welcome_message = """🌞 Καλώς ήρθατε στο Solar Bot!

Το πλήρες σύστημα διαχείρισης ηλιακής ενέργειας με ΠΡΑΓΜΑΤΙΚΑ ΔΕΔΟΜΕΝΑ!

🔥 Χαρακτηριστικά:
• Δεδομένα σε πραγματικό χρόνο από PostgreSQL
• Παρακολούθηση συστήματος (275,000+ εγγραφές)
• Καιρικές συνθήκες από APIs παραγωγής
• ML προβλέψεις και κατάσταση μοντέλων
• Οικονομική ανάλυση ROI & κόστους

📊 Πηγές Δεδομένων:
• PostgreSQL Database (solax_data, solax_data2, weather_data)
• Production API (localhost:8100)
• Enhanced Billing API (localhost:8110)

🏠 Συστήματα:
• Σύστημα 1: Σπίτι Πάνω (140,835 εγγραφές)
• Σύστημα 2: Σπίτι Κάτω (135,039 εγγραφές)

Χρησιμοποιήστε τα κουμπιά παρακάτω:

[📊 System Data] [🌤️ Weather]
[📈 Statistics] [🔧 Health]
[🔮 Predictions] [📈 ROI]
[💡 Daily Cost] [⚙️ Tariffs]
[ℹ️ Help]"""
        
        print(welcome_message)
        print()
    
    def simulate_system_data(self):
        """1️⃣ 📊 System Data"""
        print("1️⃣ 📊 SYSTEM DATA")
        print("-" * 50)
        
        try:
            response = requests.get(f"{API_BASE_URL}/api/v1/data/solax/both", timeout=10)
            if response.status_code == 200:
                data = response.json()
                systems = data.get('systems', {})
                
                message = "📊 Δεδομένα Ηλιακής Παραγωγής\n\n"
                
                for system_id, system_data in systems.items():
                    if 'error' not in system_data:
                        message += f"""🏠 {system_data.get('system_name', 'Άγνωστο')}:
• Παραγωγή Σήμερα: {system_data.get('yield_today', 0)} kWh
• AC Ισχύς: {system_data.get('ac_power', 0)} W
• SOC: {system_data.get('soc', 0)}%
• Ισχύς Μπαταρίας: {system_data.get('bat_power', 0)} W
• Τελευταία Ενημέρωση: {system_data.get('timestamp', 'Άγνωστο')[:19]}

"""
                    else:
                        message += f"""🏠 {system_data.get('system_name', 'Άγνωστο')}:
❌ {system_data.get('error', 'Άγνωστο σφάλμα')}

"""
                
                message += "📊 Πηγή: PostgreSQL Database"
                
                print("✅ TELEGRAM RESPONSE:")
                print(message)
            else:
                print("❌ Δεν υπάρχουν διαθέσιμα δεδομένα παραγωγής")
                
        except Exception as e:
            print(f"❌ Σφάλμα λήψης δεδομένων: {e}")
        
        print()
    
    def simulate_weather(self):
        """2️⃣ 🌤️ Weather"""
        print("2️⃣ 🌤️ WEATHER")
        print("-" * 50)
        
        try:
            response = requests.get(f"{API_BASE_URL}/api/v1/data/weather/latest", timeout=10)
            if response.status_code == 200:
                data = response.json()
                
                message = f"""🌤️ Πραγματικά Καιρικά Δεδομένα

🌡️ Θερμοκρασία: {data.get('temperature_2m', 'N/A')}°C
☁️ Νεφοκάλυψη: {data.get('cloud_cover', 'N/A')}%

📍 Τοποθεσία: Μαραθώνας, Αττική, Ελλάδα
📅 Χρόνος Δεδομένων: {data.get('timestamp', 'Άγνωστο')}

📊 Πηγή: Production Database & APIs"""
                
                print("✅ TELEGRAM RESPONSE:")
                print(message)
            else:
                print("❌ Δεν υπάρχουν διαθέσιμα καιρικά δεδομένα")
                
        except Exception as e:
            print(f"❌ Σφάλμα λήψης καιρικών δεδομένων: {e}")
        
        print()
    
    def simulate_statistics(self):
        """3️⃣ 📈 Statistics"""
        print("3️⃣ 📈 STATISTICS")
        print("-" * 50)
        
        try:
            conn = psycopg2.connect(**DB_CONFIG)
            cur = conn.cursor(cursor_factory=RealDictCursor)
            
            # Get counts
            cur.execute('SELECT COUNT(*) as count FROM solax_data')
            count1 = cur.fetchone()["count"]
            
            cur.execute('SELECT COUNT(*) as count FROM solax_data2')
            count2 = cur.fetchone()["count"]
            
            cur.execute('SELECT COUNT(*) as count FROM weather_data')
            weather_count = cur.fetchone()["count"]
            
            # Get latest data
            cur.execute('SELECT timestamp, yield_today, soc FROM solax_data ORDER BY timestamp DESC LIMIT 1')
            latest1 = cur.fetchone()
            
            cur.execute('SELECT timestamp, yield_today, soc FROM solax_data2 ORDER BY timestamp DESC LIMIT 1')
            latest2 = cur.fetchone()
            
            conn.close()
            
            message = f"""🗄️ Στατιστικά PostgreSQL Database

Σπίτι Πάνω (System 1):
• Εγγραφές: {count1:,}
• Τελευταία: {latest1['timestamp'] if latest1 else 'N/A'}
• Παραγωγή: {latest1['yield_today'] if latest1 else 0} kWh
• SOC: {latest1['soc'] if latest1 else 0}%

Σπίτι Κάτω (System 2):
• Εγγραφές: {count2:,}
• Τελευταία: {latest2['timestamp'] if latest2 else 'N/A'}
• Παραγωγή: {latest2['yield_today'] if latest2 else 0} kWh
• SOC: {latest2['soc'] if latest2 else 0}%

Καιρικά Δεδομένα:
• Εγγραφές: {weather_count:,}

📊 Πηγή: PostgreSQL Database"""
            
            print("✅ TELEGRAM RESPONSE:")
            print(message)
            
        except Exception as e:
            print(f"❌ Σφάλμα λήψης στατιστικών: {e}")
        
        print()
    
    def simulate_health(self):
        """4️⃣ 🔧 Health"""
        print("4️⃣ 🔧 HEALTH")
        print("-" * 50)
        
        try:
            response = requests.get(f"{API_BASE_URL}/health", timeout=10)
            if response.status_code == 200:
                data = response.json()
                
                message = f"""🔧 Κατάσταση Production System

🔌 API Status: {data.get('status', 'Άγνωστο')}
📅 Timestamp: {data.get('timestamp', 'Άγνωστο')}

🗄️ Κατάσταση Υπηρεσιών:
• Database: ✅ Connected
• Weather API: ✅ Working
• Background Tasks: ✅ Running

🔥 Πηγή Δεδομένων: Production PostgreSQL Database
✅ Κατάσταση: Όλα τα συστήματα λειτουργικά"""
                
                print("✅ TELEGRAM RESPONSE:")
                print(message)
            else:
                print("❌ Σφάλμα κατάστασης υγείας")
                
        except Exception as e:
            print(f"❌ Σφάλμα λήψης κατάστασης: {e}")
        
        print()
    
    def simulate_predictions(self):
        """5️⃣ 🔮 Predictions"""
        print("5️⃣ 🔮 PREDICTIONS")
        print("-" * 50)
        
        try:
            prediction_data = {"soc": 75.0}
            response = requests.post(f"{API_BASE_URL}/api/v1/predict", json=prediction_data, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                predicted_power = data.get('predicted_power', 0)
                confidence = data.get('confidence', 0)
                model_version = data.get('model_version', 'Άγνωστο')
                inputs = data.get('inputs', {})
                
                # Format power output
                if predicted_power > 1000:
                    power_str = f"{predicted_power/1000:.1f} kW"
                else:
                    power_str = f"{predicted_power:.0f} W"
                
                message = f"""🤖 ML Πρόβλεψη (Production Model)

🎯 Αποτελέσματα Πρόβλεψης:
• Προβλεπόμενη Ισχύς: {power_str}
• Εμπιστοσύνη: {confidence*100:.1f}%
• Μοντέλο: {model_version}

📊 Παράμετροι Εισόδου:
• Θερμοκρασία: {inputs.get('temperature', 'N/A')}°C
• Νεφοκάλυψη: {inputs.get('cloud_cover', 'N/A')}%
• SOC: {inputs.get('soc', 'N/A')}%
• Ώρα: {inputs.get('hour', 'N/A')}

📊 Πηγή: Production ML Model
📅 Δημιουργήθηκε: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""
                
                print("✅ TELEGRAM RESPONSE:")
                print(message)
            else:
                message = f"""🤖 Κατάσταση ML Πρόβλεψης

❌ Σφάλμα Πρόβλεψης: Status {response.status_code}

🔧 Σημείωση: Το μοντέλο μπορεί να χρειάζεται φόρτωση ή επανεκπαίδευση.
📅 Πηγή Δεδομένων: Production Database"""
                
                print("❌ TELEGRAM RESPONSE:")
                print(message)
                
        except Exception as e:
            print(f"❌ Σφάλμα δημιουργίας πρόβλεψης: {e}")
        
        print()

    def simulate_roi(self):
        """6️⃣ 📈 ROI"""
        print("6️⃣ 📈 ROI")
        print("-" * 50)

        try:
            response = requests.get(f"{BILLING_API_URL}/billing/enhanced/roi/system1", timeout=10)
            if response.status_code == 200:
                data = response.json()

                message = f"""📈 Ανάλυση ROI & Payback

🏠 Σπίτι Πάνω (System 1):
• Επένδυση: €{data.get('investment_cost', 12500):,}
• Ετήσια Παραγωγή: {data.get('annual_production', 0):,.0f} kWh
• Ετήσια Εξοικονόμηση: €{data.get('annual_savings', 0):,.2f}
• Payback: {data.get('payback_years', 0):.1f} έτη
• ROI: {data.get('annual_roi', 0):.1f}%

📊 Πηγή: Enhanced Billing API"""

                print("✅ TELEGRAM RESPONSE:")
                print(message)
            else:
                print("❌ Δεν υπάρχουν διαθέσιμα δεδομένα ROI")

        except Exception as e:
            print(f"❌ Σφάλμα λήψης δεδομένων ROI: {e}")

        print()

    def simulate_daily_cost(self):
        """7️⃣ 💡 Daily Cost"""
        print("7️⃣ 💡 DAILY COST")
        print("-" * 50)

        try:
            response = requests.get(f"{BILLING_API_URL}/billing/enhanced/cost/system1", timeout=10)
            if response.status_code == 200:
                data = response.json()

                message = f"""💡 Ημερήσιο Κόστος

🏠 Σπίτι Πάνω (System 1):
• Παραγωγή: {data.get('production_kwh', 0):.2f} kWh
• Κατανάλωση από Δίκτυο: {data.get('grid_consumption_kwh', 0):.2f} kWh
• Κόστος Δικτύου: €{data.get('grid_cost', 0):.2f}
• Εξοικονόμηση Παραγωγής: €{data.get('production_savings', 0):.2f}
• Καθαρή Εξοικονόμηση: €{data.get('net_savings', 0):.2f}

📊 Πηγή: Enhanced Billing API"""

                print("✅ TELEGRAM RESPONSE:")
                print(message)
            else:
                print("❌ Δεν υπάρχουν διαθέσιμα δεδομένα κόστους")

        except Exception as e:
            print(f"❌ Σφάλμα λήψης δεδομένων κόστους: {e}")

        print()

    def simulate_tariffs(self):
        """8️⃣ ⚙️ Tariffs"""
        print("8️⃣ ⚙️ TARIFFS")
        print("-" * 50)

        try:
            response = requests.get(f"{BILLING_API_URL}/billing/enhanced/tariffs", timeout=10)
            if response.status_code == 200:
                data = response.json()

                message = f"""⚙️ Τιμολόγια Ενέργειας

💡 Τιμές Ενέργειας:
• Ημερήσια Τιμή: €{data.get('day_rate', 0):.3f}/kWh
• Νυχτερινή Τιμή: €{data.get('night_rate', 0):.3f}/kWh

🔌 Χρεώσεις Δικτύου:
• Βαθμίδα 1 (0-1,600 kWh): €{data.get('network_tier1', 0):.4f}/kWh
• Βαθμίδα 2 (1,601-2,000 kWh): €{data.get('network_tier2', 0):.4f}/kWh
• Βαθμίδα 3 (2,001+ kWh): €{data.get('network_tier3', 0):.4f}/kWh

📊 Πρόσθετες Χρεώσεις:
• ETMEAR: €{data.get('etmear', 0):.3f}/kWh
• ΦΠΑ: {data.get('vat', 24)}%

🔄 Net Metering:
• Ποσοστό Συμψηφισμού: {data.get('surplus_rate', 90)}%

📊 Πηγή: Enhanced Billing API"""

                print("✅ TELEGRAM RESPONSE:")
                print(message)
            else:
                print("❌ Δεν υπάρχουν διαθέσιμα δεδομένα τιμολογίων")

        except Exception as e:
            print(f"❌ Σφάλμα λήψης δεδομένων τιμολογίων: {e}")

        print()

    def simulate_help(self):
        """9️⃣ ℹ️ Help"""
        print("9️⃣ ℹ️ HELP")
        print("-" * 50)

        message = """ℹ️ Βοήθεια Solar Bot

🔧 Εντολές:
• /start - Εμφάνιση κύριου μενού

📊 Λειτουργίες:
• System Data - Τρέχοντα δεδομένα ηλιακού συστήματος
• Weather - Καιρικές συνθήκες
• Statistics - Στατιστικά βάσης δεδομένων
• Health - Κατάσταση Production API
• Predictions - ML πρόβλεψη με production μοντέλα
• ROI - Ανάλυση ROI & Payback
• Daily Cost - Ημερήσιο κόστος
• Tariffs - Τιμολόγια ενέργειας

🔥 Production Features:
• 275,000+ εγγραφές συνολικά
• Πραγματική PostgreSQL ενσωμάτωση
• Χωρίς mock δεδομένα - 100% παραγωγή
• Live API monitoring

Όλα τα δεδομένα προέρχονται από πραγματική βάση δεδομένων παραγωγής!"""

        print("✅ TELEGRAM RESPONSE:")
        print(message)
        print()

    def run_all_simulations(self):
        """Run all menu simulations"""
        print("🚀 Εκτέλεση προσομοίωσης όλων των επιλογών μενού...\n")
        
        simulations = [
            ("START COMMAND", self.simulate_start_command),
            ("SYSTEM DATA", self.simulate_system_data),
            ("WEATHER", self.simulate_weather),
            ("STATISTICS", self.simulate_statistics),
            ("HEALTH", self.simulate_health),
            ("PREDICTIONS", self.simulate_predictions),
            ("ROI", self.simulate_roi),
            ("DAILY COST", self.simulate_daily_cost),
            ("TARIFFS", self.simulate_tariffs),
            ("HELP", self.simulate_help)
        ]
        
        for name, simulation in simulations:
            try:
                simulation()
            except Exception as e:
                print(f"❌ {name} simulation failed: {e}\n")
        
        print("="*80)
        print("📊 ΠΡΟΣΟΜΟΙΩΣΗ ΟΛΟΚΛΗΡΩΘΗΚΕ")
        print("="*80)
        print("🔥 Όλες οι επιλογές μενού δοκιμάστηκαν!")
        print("📱 Το Telegram bot είναι έτοιμο για χρήση!")

def main():
    """Main function"""
    simulator = TelegramMenuSimulation()
    simulator.run_all_simulations()

if __name__ == "__main__":
    main()
